#!/bin/bash

echo "Cleaning up previous installation..."
rm -rf node_modules
rm -f package-lock.json

echo "Installing dependencies..."
npm install --save @nestjs/typeorm@10.0.1 typeorm@0.3.20 sqlite3@5.1.7
npm install --save class-validator@0.14.0 class-transformer@0.5.1
npm install --save rimraf@5.0.5

echo "Installing all remaining dependencies..."
npm install

echo "Building the project..."
npm run build

echo "Installation completed!"
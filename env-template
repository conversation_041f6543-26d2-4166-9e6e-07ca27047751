# Application
PORT=4000
NODE_ENV=development

# APIs
COURT_LISTENER_BASE_URL=https://www.courtlistener.com/api/v4
COURT_LISTENER_API_KEY=your-api-key
COURT_LISTENER_API_KEY_EXPIRATION=yyyy-mm-dd
COURT_LISTENER_RATE_LIMIT=1000
COURT_LISTENER_TIMEOUT=5000

GOVINFO_BASE_URL=https://api.govinfo.gov/v1
GOVINFO_API_KEY=your-api-key
GOVINFO_API_KEY_EXPIRATION=yyyy-mm-dd
GOVINFO_RATE_LIMIT=1000
GOVINFO_TIMEOUT=5000

# Cache Configuration
CASE_CACHE_TTL=2592000000        # 30 days in milliseconds
CASE_CACHE_CHECK_PERIOD=600000   # 10 minutes in milliseconds
CASE_CACHE_MAX_KEYS=10000

STATUTE_CACHE_TTL=604800000      # 7 days in milliseconds
STATUTE_CACHE_CHECK_PERIOD=600000
STATUTE_CACHE_MAX_KEYS=10000

REGULATION_CACHE_TTL=86400000    # 24 hours in milliseconds
REGULATION_CACHE_CHECK_PERIOD=600000
REGULATION_CACHE_MAX_KEYS=10000

# API Retry Configuration
API_RETRY_MAX_ATTEMPTS=3
API_RETRY_BASE_DELAY=1000        # 1 second in milliseconds
API_RETRY_MAX_DELAY=10000        # 10 seconds in milliseconds
API_RETRY_EXPONENTIAL_BASE=2

# Database configuration
DATABASE_URI=mongodb://localhost:27017/legal-document-analyzer

# OpenAI configuration
OPENAI_API_KEY=your-api-key
OPENAI_MODEL_NAME=your-model-name
OPENAI_TEMPERATURE=0.3

# Storage configuration
UPLOAD_DIR=uploads
STORAGE_PROVIDER=cloudflare
# Cloudflare R2 Storage Configuration
CLOUDFLARE_R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
CLOUDFLARE_R2_ACCESS_KEY_ID=your-access-key-id
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your-secret-access-key
CLOUDFLARE_R2_BUCKET_NAME=your-bucket-name
CLOUDFLARE_R2_PUBLIC_URL=https://pub-xxxxxxxx.r2.dev

# Authentication configuration
JWT_SECRET=your-secret-key-change-in-production
JWT_EXPIRES_IN=1d
BCRYPT_SALT_ROUNDS=10

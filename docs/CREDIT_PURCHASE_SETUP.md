# Credit Purchase Implementation Setup

## Overview
The credit purchase functionality has been implemented and requires Stripe configuration to work properly.

## Required Environment Variables

Add the following environment variables to your `.env` file:

```bash
# Existing Stripe Configuration
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Subscription Plan Price IDs (existing)
STRIPE_PLAN_FREE=
STRIPE_PLAN_PRO=price_...
STRIPE_PLAN_ADMIN=price_...

# Credit Package Price IDs (NEW - required for credit purchases)
STRIPE_CREDIT_STUDENT=price_...
STRIPE_CREDIT_LAWYER_SMALL=price_...
STRIPE_CREDIT_LAWYER_LARGE=price_...
STRIPE_CREDIT_FIRM_STANDARD=price_...
STRIPE_CREDIT_FIRM_ENTERPRISE=price_...
```

## Stripe Setup Required

### 1. Create Products in Stripe Dashboard

Create the following products in your Stripe dashboard:

1. **Student Credit Package**
   - Name: "Student Credit Package"
   - Description: "50 credits for law students"

2. **Lawyer Small Credit Package**
   - Name: "Lawyer Starter Pack"
   - Description: "200 credits + 20 bonus for practicing attorneys"

3. **Lawyer Large Credit Package**
   - Name: "Lawyer Professional Pack"
   - Description: "500 credits + 75 bonus for busy legal practices"

4. **Firm Standard Credit Package**
   - Name: "Law Firm Standard Pack"
   - Description: "1000 credits + 200 bonus for growing legal teams"

5. **Firm Enterprise Credit Package**
   - Name: "Law Firm Enterprise Pack"
   - Description: "5000 credits + 1500 bonus for large legal organizations"

### 2. Create Prices for Each Product

For each product above, create a **one-time payment** price:

- **Student**: $4.99 (499 cents)
- **Lawyer Small**: $19.99 (1999 cents)
- **Lawyer Large**: $44.99 (4499 cents)
- **Firm Standard**: $79.99 (7999 cents)
- **Firm Enterprise**: $349.99 (34999 cents)

**Important**: Make sure to select "One time" payment type, not recurring.

### 3. Copy Price IDs

After creating the prices, copy the price IDs (they start with `price_`) and add them to your environment variables.

## API Endpoints

### Purchase Credits
```http
POST /api/credits/purchase
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "package": "lawyer_small",
  "successUrl": "https://yourdomain.com/credits/success",
  "cancelUrl": "https://yourdomain.com/credits/cancel"
}
```

**Response:**
```json
{
  "success": true,
  "sessionUrl": "https://checkout.stripe.com/pay/cs_...",
  "package": {
    "name": "lawyer_small",
    "credits": 200,
    "bonus": 20,
    "totalCredits": 220,
    "price": 19.99,
    "description": "Lawyer starter pack - Additional credits for practicing attorneys"
  },
  "organizationId": "org-123"
}
```

### Get Available Packages
```http
GET /api/credits/packages
Authorization: Bearer <jwt_token>
```

**Response:**
```json
[
  {
    "id": "student",
    "name": "Student credit package - Perfect for law students",
    "credits": 50,
    "bonus": 0,
    "totalCredits": 50,
    "price": 4.99,
    "targetTier": "law_student"
  },
  {
    "id": "lawyer_small",
    "name": "Lawyer starter pack - Additional credits for practicing attorneys",
    "credits": 200,
    "bonus": 20,
    "totalCredits": 220,
    "price": 19.99,
    "targetTier": "lawyer"
  }
  // ... more packages
]
```

## Webhook Handling

The existing webhook endpoint `/api/subscriptions/webhook` now handles both subscription and credit purchase events:

- **Subscription events**: `customer.subscription.*`
- **Credit purchase events**: `checkout.session.completed` with `mode: 'payment'`

## Testing

1. Set up the Stripe price IDs in your environment
2. Make a POST request to `/api/credits/purchase`
3. Complete the checkout flow
4. Verify credits are added to the organization's balance
5. Check the credit history for the purchase transaction

## Package Types

- `student`: 50 credits, $4.99
- `lawyer_small`: 200 + 20 bonus credits, $19.99
- `lawyer_large`: 500 + 75 bonus credits, $44.99
- `firm_standard`: 1000 + 200 bonus credits, $79.99
- `firm_enterprise`: 5000 + 1500 bonus credits, $349.99

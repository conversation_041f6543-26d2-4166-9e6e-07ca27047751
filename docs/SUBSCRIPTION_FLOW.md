# Subscription Management Flow

This document provides a visual representation of the subscription management flow in the Legal Document Analyzer application. Use this as a reference when implementing the frontend subscription management components.

## User Registration & Subscription Flow

```mermaid
graph TD
    A[User Registration] --> B{Create New Org?}
    B -->|Yes| C[Create Organization]
    B -->|No| D[Join Existing Organization]

    C --> E[Free Tier with Pro Trial]
    D --> F[Inherit Org's Subscription]

    E --> G[User Dashboard]
    F --> G

    G --> H{View Available Plans}
    H --> I[Select Plan]
    I --> J[Checkout with Stripe]
    J --> K{Payment Successful?}

    K -->|Yes| L[Update Subscription Tier]
    K -->|No| M[Show Error]

    L --> N[Access New Features]
    M --> H
```

## Feature Access Control Flow

```mermaid
graph TD
    A[User Requests Feature] --> B{Check Tenant Context}
    B -->|Valid Context| C{Check Trial Status}
    B -->|Invalid Context| D[Return 401 Unauthorized]

    C -->|In Trial| E{Check Feature in Trial Tier}
    C -->|Not in Trial| F{Check Subscription Status}

    E -->|Feature Available| G[Allow Access]
    E -->|Feature Not Available| H[Return 403 Forbidden]

    F -->|Active| I{Check Feature in Tier}
    F -->|Inactive/Canceled/Past Due| J[Free Tier Features Only]

    I -->|Feature Available| G
    I -->|Feature Not Available| H

    J --> K{Feature in Free Tier?}
    K -->|Yes| G
    K -->|No| H

    G --> L[Execute Feature]
    H --> M[Show Upgrade Prompt]
```

## Usage Tracking Flow

```mermaid
graph TD
    A[User Action] -->|Document Upload| B[Check Document Limit]
    A -->|Analysis| C[Check Analysis Limit]

    B -->|Under Limit| D[Allow Upload]
    B -->|Over Limit| E[Block Upload]

    C -->|Under Limit| F[Allow Analysis]
    C -->|Over Limit| G[Block Analysis]

    D --> H[Process Document]
    F --> I[Run Analysis]

    H -->|Success| J[Increment Usage Counter]
    I -->|Success| J

    E --> K[Show Upgrade Prompt]
    G --> K

    J --> L[Update UI with New Usage Stats]
```

## Subscription State Transitions

```mermaid
stateDiagram-v2
    [*] --> Free: Registration
    Free --> FreeTrial: Auto-assigned for 14 days

    FreeTrial --> Pro: Upgrade before trial ends
    FreeTrial --> Free: Trial expires

    Free --> Pro: Upgrade
    Pro --> Free: Downgrade

    Pro --> Canceled: Cancel Subscription

    Canceled --> Free: Auto-transition after period end

    Pro --> PastDue: Payment Failed

    PastDue --> Pro: Payment Resolved
    PastDue --> Free: Payment Not Resolved

    Free --> [*]: Account Deletion
    Pro --> [*]: Account Deletion
    Canceled --> [*]: Account Deletion
    PastDue --> [*]: Account Deletion
```

## Subscription Plans

| Feature                        | Free | Pro       |
| ------------------------------ | ---- | --------- |
| Document Limit                 | 10   | 200       |
| Analysis Per Month             | 50   | Unlimited |
| Basic Analysis                 | ✓    | ✓         |
| Document Organization          | ✓    | ✓         |
| User Feedback                  | ✓    | ✓         |
| Advanced Analysis              | ✗    | ✓         |
| Batch Processing               | ✗    | ✓         |
| Custom Models                  | ✗    | ✓         |
| Enhanced Comparison            | ✗    | ✓         |
| Advanced Document Organization | ✗    | ✓         |
| Advanced Analytics             | ✗    | ✓         |
| Document Comparison            | ✗    | ✓         |

> Note: Admin tier includes all Pro features plus administrative capabilities.

## Stripe Configuration

| Plan | Price ID                         | Description                                   |
| ---- | -------------------------------- | --------------------------------------------- |
| Pro  | price_1RIXSEE7koxutxfhnFyQ9YKI   | Professional tier with advanced features      |
| Free | N/A                              | Free tier (no Stripe subscription required)   |

> Note: The Free tier does not create a Stripe subscription, but users automatically get a 14-day trial of the Pro tier.

## Trial Period

All new Free tier users automatically receive a 14-day trial of the Pro tier. During this period:

1. Users have access to all Pro features without payment
2. Document limit is temporarily increased to 200
3. Analysis limit is temporarily unlimited
4. After 14 days, users revert to Free tier features unless they upgrade

This allows users to experience the full power of the Legal Document Analyzer before deciding on a subscription plan.

## Frontend Component Dependencies

```mermaid
graph TD
    A[App] --> B[AuthProvider]
    B --> C[OrganizationProvider]
    C --> D[SubscriptionProvider]

    D --> E[Dashboard]
    D --> F[DocumentUpload]
    D --> G[Analysis]
    D --> H[Chat]
    D --> I[SubscriptionManagement]

    F --> J[UsageTracker]
    G --> J
    H --> J

    I --> K[PlanSelector]
    I --> L[StripeCheckout]
    I --> M[BillingHistory]

    E --> N[FeatureGuard]
    F --> N
    G --> N
    H --> N
```

## Subscription Webhook Processing Flow

```mermaid
graph TD
    A[Stripe Webhook] --> B{Event Type}

    B -->|subscription.created| C[Create Subscription]
    B -->|subscription.updated| D[Update Subscription]
    B -->|subscription.deleted| E[Mark as Canceled]
    B -->|invoice.payment_succeeded| F[Confirm Payment]
    B -->|invoice.payment_failed| G[Mark as Past Due]

    C --> H[Update User Interface]
    D --> H
    E --> H
    F --> H
    G --> H[Show Payment Issue Warning]

    G --> I[Send Email Notification]
```

## Implementing This Flow

When implementing the subscription management system in the frontend:

1. **User Registration**: Enable organization creation with default Free tier
2. **Feature Access Control**: Use FeatureGuard component to protect premium features
3. **Subscription Selection**: Show available plans based on current tier
4. **Payment Processing**: Integrate Stripe Elements for secure payment collection
5. **Usage Tracking**: Display current usage vs limits with progress indicators
6. **Subscription Management**: Allow users to upgrade, downgrade, or cancel subscriptions

## API Endpoints

### Create Subscription

```http
POST /api/subscriptions
```

**Request Body:**

```json
{
  "organizationId": "org-123",
  "email": "<EMAIL>",
  "tier": "free"
}
```

**Response:**

```json
{
  "organizationId": "org-123",
  "tier": "free",
  "status": "active",
  "currentPeriodStart": "2025-04-27T18:41:42+01:00",
  "currentPeriodEnd": "2026-04-27T18:41:42+01:00",
  "cancelAtPeriodEnd": false,
  "trialTier": "pro",
  "trialEndDate": "2025-05-11T18:41:42+01:00",
  "features": ["basic_analysis", "document_organization", "user_feedback"],
  "usageStats": {
    "documentsProcessed": 0,
    "analysisCount": 0,
    "lastUpdated": "2025-04-27T18:41:42+01:00"
  }
}
```

### Create Checkout Session

```http
POST /api/subscriptions/checkout
```

**Request Body:**

```json
{
  "organizationId": "org-123",
  "tier": "pro",
  "successUrl": "https://yourdomain.com/subscription/success?session_id={CHECKOUT_SESSION_ID}",
  "cancelUrl": "https://yourdomain.com/subscription/cancel"
}
```

**Response:**

```json
{
  "sessionUrl": "https://checkout.stripe.com/c/pay/cs_test_a1b2c3d4e5f6g7h8i9j0"
}
```

> Note: The `successUrl` and `cancelUrl` parameters are optional. If not provided, default URLs will be used based on the application's configuration. The `{CHECKOUT_SESSION_ID}` placeholder in the success URL will be replaced with the actual session ID by Stripe.

### Get Subscription

```http
GET /api/subscriptions/:organizationId
```

**Response:**

```json
{
  "organizationId": "org-123",
  "tier": "free",
  "status": "active",
  "cancelAtPeriodEnd": false,
  "currentPeriodEnd": "2026-04-27T18:41:42+01:00",
  "trialTier": "pro",
  "trialEndDate": "2025-05-11T18:41:42+01:00",
  "features": ["basic_analysis", "document_organization", "user_feedback"],
  "usageStats": {
    "documentsProcessed": 5,
    "analysisCount": 12,
    "lastUpdated": "2025-04-27T18:41:42+01:00"
  }
}
```

### Cancel Subscription

```http
DELETE /api/subscriptions/:organizationId
```

**Response:**

```json
{
  "organizationId": "org-123",
  "tier": "free",
  "status": "canceled",
  "cancelAtPeriodEnd": true,
  "currentPeriodEnd": "2026-04-27T18:41:42+01:00",
  "features": ["basic_analysis", "document_organization", "user_feedback"],
  "usageStats": {
    "documentsProcessed": 5,
    "analysisCount": 12,
    "lastUpdated": "2025-04-27T18:41:42+01:00"
  }
}
```

### Stripe Webhook Handler

```http
POST /api/subscriptions/webhook
```

**Headers:**
- `stripe-signature`: Signature from Stripe to verify the webhook

**Request Body:**
- Raw Stripe event payload

**Response:**
- 204 No Content on success
- 400 Bad Request if signature is missing or invalid

### Check Usage Limits

```http
GET /api/subscriptions/:organizationId/usage
```

**Response:**

```json
{
  "allowed": true
}

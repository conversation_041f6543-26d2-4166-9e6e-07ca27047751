# Getting Started with Pro Features

This guide will help you quickly get started with the Negotiation Simulator and Compliance Auditor features.

## Prerequisites

- ✅ Valid JWT authentication token
- ✅ Access to the Legal Document Analyzer API
- ✅ Basic understanding of REST APIs
- ✅ Legal documents for testing (optional)

## Quick Setup

### 1. Authentication

First, obtain your JWT token by logging in:

```bash
curl -X POST "http://localhost:4000/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your-password"
  }'
```

Save the returned token for use in subsequent requests:

```bash
export JWT_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### 2. Test API Access

Verify your access to the pro features:

```bash
# Test Negotiation Simulator
curl -X GET "http://localhost:4000/api/negotiation-simulator/scenarios" \
  -H "Authorization: Bearer $JWT_TOKEN"

# Test Compliance Auditor
curl -X GET "http://localhost:4000/api/compliance/frameworks" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

---

## 🎯 Negotiation Simulator - Quick Start

### Step 1: Create Your First Scenario

```bash
curl -X POST "http://localhost:4000/api/negotiation-simulator/scenarios" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Basic Contract Negotiation",
    "description": "Practice negotiating a simple service contract",
    "industry": "Professional Services",
    "contractType": "Service Agreement",
    "difficulty": "beginner",
    "parties": [
      {
        "name": "Service Provider",
        "role": "vendor",
        "priorities": ["Maximize revenue", "Minimize liability"],
        "negotiationStyle": "competitive",
        "constraints": {
          "minPrice": 50000
        }
      },
      {
        "name": "Client Company",
        "role": "client",
        "priorities": ["Cost control", "Quality assurance"],
        "negotiationStyle": "analytical",
        "constraints": {
          "maxBudget": 80000
        }
      }
    ],
    "initialOffer": {
      "price": 75000,
      "currency": "USD",
      "paymentTerms": "Net 30",
      "warranties": ["Service quality guarantee"],
      "liabilities": ["Limited to contract value"]
    },
    "constraints": {
      "maxRounds": 8,
      "timeLimit": 45,
      "mustHaveTerms": ["Payment terms", "Delivery timeline"],
      "dealBreakers": ["Unlimited liability"],
      "flexibleTerms": ["Payment schedule", "Scope adjustments"]
    },
    "timeline": {
      "startDate": "2025-05-24T18:00:00.000Z",
      "expectedDuration": 30,
      "maxDuration": 60,
      "breakDuration": 5
    },
    "tags": ["beginner", "service-contract", "training"]
  }'
```

### Step 2: Start a Negotiation Session

```bash
# Use the scenarioId from the previous response
curl -X POST "http://localhost:4000/api/negotiation-simulator/sessions" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "scenarioId": "YOUR_SCENARIO_ID",
    "aiPersonality": {
      "aggressiveness": 0.5,
      "flexibility": 0.7,
      "riskTolerance": 0.6,
      "communicationStyle": "diplomatic",
      "decisionSpeed": "moderate",
      "concessionPattern": "gradual"
    }
  }'
```

### Step 3: Make Your First Move

```bash
# Use the sessionId from the previous response
curl -X POST "http://localhost:4000/api/negotiation-simulator/sessions/YOUR_SESSION_ID/moves" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "move": {
      "offer": {
        "price": 65000,
        "currency": "USD",
        "paymentTerms": "Net 45",
        "customTerms": {
          "deliveryTimeline": "8 weeks",
          "revisions": "3 included"
        }
      },
      "message": "We appreciate your proposal. We would like to discuss the pricing and payment terms to better align with our budget constraints.",
      "strategy": "collaborative",
      "reasoning": "Starting with a reasonable counter-offer while showing willingness to negotiate"
    }
  }'
```

### Step 4: View Session Progress

```bash
curl -X GET "http://localhost:4000/api/negotiation-simulator/sessions/YOUR_SESSION_ID" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

---

## 🛡️ Compliance Auditor - Quick Start

### Step 1: Check Available Frameworks

```bash
curl -X GET "http://localhost:4000/api/compliance/frameworks" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

### Step 2: Create a Compliance Profile (Optional)

```bash
curl -X POST "http://localhost:4000/api/compliance/profiles" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Basic GDPR Profile",
    "description": "Standard GDPR compliance profile for data processing agreements",
    "industry": "Technology",
    "frameworks": [
      {
        "name": "GDPR",
        "version": "2018",
        "enabled": true,
        "weight": 1.0,
        "applicableArticles": ["6", "7", "13", "14", "17"]
      }
    ],
    "riskThresholds": {
      "low": 0.3,
      "medium": 0.6,
      "high": 0.8
    },
    "customRequirements": [
      {
        "category": "data_protection",
        "requirement": "Data subject rights must be clearly stated",
        "mandatory": true
      }
    ]
  }'
```

### Step 3: Audit a Document

First, upload a document using the main documents API, then audit it:

```bash
# Audit the document
curl -X POST "http://localhost:4000/api/compliance/audit" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "documentId": "YOUR_DOCUMENT_ID",
    "frameworks": ["GDPR"],
    "options": {
      "includeRecommendations": true,
      "detailedAnalysis": true,
      "riskThreshold": "medium"
    }
  }'
```

### Step 4: View Audit Results

```bash
# Get specific audit result
curl -X GET "http://localhost:4000/api/compliance/audit-results/YOUR_AUDIT_ID" \
  -H "Authorization: Bearer $JWT_TOKEN"

# Or list all audit results
curl -X GET "http://localhost:4000/api/compliance/audit-results?page=1&limit=10" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

---

## 📊 Analytics and Monitoring

### Negotiation Analytics

```bash
# Get negotiation performance overview
curl -X GET "http://localhost:4000/api/negotiation-simulator/analytics/overview" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

### Compliance Analytics

```bash
# Get compliance overview
curl -X GET "http://localhost:4000/api/compliance/analytics/overview" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

---

## 🔧 Common Use Cases

### Training New Lawyers

1. **Create beginner scenarios** with simple contract types
2. **Start with collaborative AI** personalities
3. **Review session analytics** to identify improvement areas
4. **Progress to intermediate** scenarios as skills develop

```bash
# Example: Create a beginner employment contract scenario
curl -X POST "http://localhost:4000/api/negotiation-simulator/scenarios" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Employment Contract Basics",
    "difficulty": "beginner",
    "industry": "Human Resources",
    "contractType": "Employment Agreement"
  }'
```

### Compliance Monitoring

1. **Set up compliance profiles** for your industry
2. **Audit documents regularly** using automated workflows
3. **Monitor compliance trends** through analytics
4. **Address high-risk findings** promptly

```bash
# Example: Regular compliance check
curl -X POST "http://localhost:4000/api/compliance/audit" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "documentId": "contract-123",
    "frameworks": ["GDPR", "CCPA"],
    "options": {
      "riskThreshold": "low"
    }
  }'
```

---

## 🚨 Troubleshooting

### Common Issues

#### Authentication Errors
```bash
# Error: 401 Unauthorized
# Solution: Check your JWT token is valid and not expired
curl -X POST "http://localhost:4000/api/auth/validate-token" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

#### Validation Errors
```bash
# Error: 400 Bad Request with validation details
# Solution: Check the API documentation for required fields
# The error response will show exactly which fields are missing or invalid
```

#### Rate Limiting
```bash
# Error: 429 Too Many Requests
# Solution: Wait for the rate limit to reset or upgrade your plan
# Check the X-RateLimit-Reset header for reset time
```

### Getting Help

- 📖 **Documentation**: Check the detailed feature guides
- 💬 **Support**: Contact <EMAIL>
- 🐛 **Issues**: Report bugs on GitHub
- 💡 **Feature Requests**: Submit via the dashboard

---

## 🎯 Next Steps

1. **Explore Advanced Features**:
   - Custom AI personalities for negotiation
   - Industry-specific compliance profiles
   - Advanced analytics and reporting

2. **Integration**:
   - Set up webhooks for real-time notifications
   - Integrate with your existing document workflow
   - Build custom dashboards using the analytics APIs

3. **Best Practices**:
   - Review the detailed documentation for each feature
   - Start with simple scenarios and gradually increase complexity
   - Use analytics to track improvement and identify training needs

4. **Production Deployment**:
   - Configure proper authentication and authorization
   - Set up monitoring and alerting
   - Implement proper error handling in your applications

---

## 📚 Additional Resources

- 📖 **[Negotiation Simulator Guide](./features/negotiation-simulator.md)**
- 📖 **[Compliance Auditor Guide](./features/compliance-auditor.md)**
- 🔧 **[Complete API Reference](./api-reference.md)**
- 🎥 **Video Tutorials**: Available in the dashboard
- 💬 **Community Forum**: Join discussions with other users

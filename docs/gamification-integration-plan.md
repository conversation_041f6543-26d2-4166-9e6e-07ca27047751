# **🎮 Gamification Integration Plan for DocGic Negotiation Simulator**

## **📋 Phase 1: Core Gamification Infrastructure**

### **1.1 Create Gamification Module**
```bash
# New module structure
src/modules/gamification/
├── gamification.module.ts
├── controllers/
│   ├── gamification.controller.ts
│   ├── achievements.controller.ts
│   ├── leaderboards.controller.ts
│   └── characters.controller.ts
├── services/
│   ├── gamification.service.ts
│   ├── achievement.service.ts
│   ├── level.service.ts
│   ├── relationship.service.ts
│   └── leaderboard.service.ts
├── schemas/
│   ├── user-gamification.schema.ts
│   ├── achievement.schema.ts
│   ├── character.schema.ts
│   ├── character-relationship.schema.ts
│   └── leaderboard.schema.ts
├── dto/
│   ├── gamification.dto.ts
│   └── achievement.dto.ts
├── interfaces/
│   └── gamification.interface.ts
└── engines/
    ├── achievement.engine.ts
    └── level.engine.ts
```

### **1.2 Database Schema Extensions**

#### **Extend User Schema**
```typescript
// Add to existing User schema
@Prop({ type: Object, default: null })
gamificationProfile?: {
  level: number;
  totalXP: number;
  currentXP: number;
  xpToNext: number;
  title: string;
  joinedAt: Date;
  lastActiveAt: Date;
};
```

#### **New Gamification Collections**
- `user_gamification` - User progress, achievements, statistics
- `achievements` - Achievement definitions and rules
- `characters` - AI character personalities and unlock requirements
- `character_relationships` - User-character relationship tracking
- `leaderboards` - Competitive rankings and statistics
- `pressure_events` - Dynamic event definitions
- `challenges` - Team and individual challenges

### **1.3 Integration Points**

#### **Extend Existing Negotiation Session Schema**
```typescript
// Add to existing NegotiationSession schema
@Prop({ type: String, default: null })
characterId?: string; // Link to character

@Prop({ type: Object, default: {} })
gamificationData?: {
  xpEarned: number;
  achievementsUnlocked: string[];
  pressureEventsTriggered: string[];
  relationshipChanges: Record<string, number>;
  levelUps: number;
};

@Prop({ type: Object, default: {} })
gameState?: {
  userStress: number;
  aiMood: string;
  activePressureEvents: string[];
  timeRemaining?: number;
  currentScore: number;
  dealMomentum: string;
};
```

## **📋 Phase 2: Service Integration**

### **2.1 Enhance Negotiation Simulator Service**

#### **Modify Session Creation**
```typescript
// In existing NegotiationSimulatorService.startSession()
async startSession(dto: StartNegotiationSessionDto, userId: string, organizationId: string) {
  // ... existing code ...

  // NEW: Initialize gamification data
  const userGamification = await this.gamificationService.getUserGamification(userId);
  const character = dto.characterId ? 
    await this.characterService.getCharacter(dto.characterId) : 
    await this.characterService.getDefaultCharacter();

  // NEW: Apply relationship bonuses
  const relationship = await this.relationshipService.getRelationship(userId, character.id);
  const enhancedAIPersonality = this.applyRelationshipBonuses(aiPersonality, relationship);

  // NEW: Initialize game state
  const gameState = {
    userStress: 0.3,
    aiMood: 'neutral',
    activePressureEvents: [],
    currentScore: 5.0,
    dealMomentum: 'neutral'
  };

  const session = new this.sessionModel({
    // ... existing fields ...
    characterId: character.id,
    aiPersonality: enhancedAIPersonality,
    gameState,
    gamificationData: {
      xpEarned: 0,
      achievementsUnlocked: [],
      pressureEventsTriggered: [],
      relationshipChanges: {},
      levelUps: 0
    }
  });

  // NEW: Schedule pressure events
  await this.pressureEventService.scheduleInitialEvents(session._id);

  return session;
}
```

#### **Enhance Move Processing**
```typescript
// In existing NegotiationSimulatorService.makeMove()
async makeMove(dto: MakeNegotiationMoveDto, userId: string) {
  // ... existing code ...

  // NEW: Check for pressure events
  await this.pressureEventService.checkTriggers(session._id, session);

  // NEW: Update relationship
  await this.relationshipService.updateRelationship(
    userId, 
    session.characterId, 
    dto.move, 
    aiResponse
  );

  // NEW: Check achievements
  const newAchievements = await this.achievementService.checkAchievements(
    userId, 
    session._id, 
    session
  );

  // NEW: Award XP
  const xpEarned = this.calculateXPReward(session, dto.move, aiResponse);
  const levelUpdate = await this.gamificationService.awardExperience(
    userId, 
    xpEarned, 
    'negotiation_move'
  );

  // Update session with gamification data
  session.gamificationData.xpEarned += xpEarned;
  session.gamificationData.achievementsUnlocked.push(...newAchievements);
  if (levelUpdate.leveledUp) {
    session.gamificationData.levelUps += 1;
  }

  return { session, aiResponse, gamificationUpdate: { xpEarned, newAchievements, levelUpdate } };
}
```

### **2.2 Credit System Integration**

#### **Modify Credit Consumption**
```typescript
// In existing UseCredits decorator usage
@Post('sessions')
@UseCredits('negotiation_simulator', { 
  baseCredits: 1,
  calculateDynamic: (context) => {
    // Higher difficulty characters cost more credits
    const character = context.body.characterId;
    const difficulty = character?.difficulty || 1;
    return Math.ceil(difficulty * 0.5); // 1-3 credits based on difficulty
  }
})
async startSession(@Body() dto: StartNegotiationSessionDto) {
  // ... implementation
}
```

## **📋 Phase 3: Real-time Features**

### **3.1 WebSocket Integration**

#### **Extend Existing WebSocket Setup**
```typescript
// Create new gateway: src/modules/gamification/gateways/gamification.gateway.ts
@WebSocketGateway({
  namespace: '/gamification',
  cors: { origin: '*' }
})
export class GamificationGateway {
  @SubscribeMessage('join_session')
  async handleJoinSession(client: Socket, data: { sessionId: string }) {
    await client.join(`session:${data.sessionId}`);
    
    // Send current game state
    const gameState = await this.cacheService.getSessionGameState(data.sessionId);
    client.emit('game_state_update', gameState);
  }

  @SubscribeMessage('request_live_score')
  async handleLiveScore(client: Socket, data: { sessionId: string, moveData: any }) {
    const liveScore = await this.scoringService.calculateLiveScore(data.moveData, data.sessionId);
    client.emit('live_score_update', liveScore);
  }

  // Broadcast pressure events
  async broadcastPressureEvent(sessionId: string, event: any) {
    this.server.to(`session:${sessionId}`).emit('pressure_event', event);
  }

  // Broadcast achievements
  async broadcastAchievement(userId: string, achievement: any) {
    this.server.to(`user:${userId}`).emit('achievement_unlocked', achievement);
  }
}
```

### **3.2 Background Jobs**

#### **Leaderboard Updates**
```typescript
// Create: src/modules/gamification/jobs/leaderboard-update.job.ts
@Injectable()
export class LeaderboardUpdateJob {
  @Cron('0 */5 * * * *') // Every 5 minutes
  async updateLeaderboards() {
    await this.leaderboardService.updateGlobalLeaderboard('weekly');
    await this.leaderboardService.updateOrganizationLeaderboards('weekly');
  }
}
```

## **📋 Phase 4: API Endpoints**

### **4.1 New Gamification Endpoints**
```typescript
// GET /api/gamification/profile/:userId
// GET /api/gamification/achievements
// GET /api/gamification/leaderboards
// GET /api/gamification/characters
// POST /api/gamification/characters/:characterId/unlock
```

### **4.2 Enhanced Negotiation Endpoints**
```typescript
// Modify existing endpoints to include gamification data
// GET /api/negotiation-simulator/sessions - include XP earned, achievements
// POST /api/negotiation-simulator/sessions - accept characterId
// POST /api/negotiation-simulator/sessions/:id/moves - return gamification updates
```

## **📋 Phase 5: Migration Strategy**

### **5.1 Database Migration**
1. Create new collections for gamification
2. Add gamification fields to existing User schema
3. Migrate existing negotiation sessions to include basic gamification data
4. Seed initial characters and achievements

### **5.2 Backward Compatibility**
- All existing API endpoints continue to work
- Gamification features are opt-in
- Default character assigned if none specified
- Graceful degradation if gamification services unavailable

## **📋 Implementation Timeline**

### **Week 1-2: Core Infrastructure**
- Create gamification module structure
- Implement basic schemas and services
- Set up database collections

### **Week 3-4: Service Integration**
- Enhance negotiation simulator service
- Implement achievement and XP systems
- Add character relationship tracking

### **Week 5-6: Real-time Features**
- Implement WebSocket integration
- Add pressure events system
- Create background jobs

### **Week 7-8: API and Testing**
- Complete API endpoints
- Comprehensive testing
- Performance optimization

### **Week 9-10: Migration and Deployment**
- Database migration scripts
- Production deployment
- Monitoring and analytics setup

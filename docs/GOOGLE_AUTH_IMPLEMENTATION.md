# Google Authentication Implementation Guide

This guide explains how to implement Google Authentication in the Legal Document Analyzer application.

## Backend Implementation

The backend implementation is complete and includes the following components:

1. **Google OAuth Strategy**: Implemented using Passport.js to handle Google authentication flow
2. **User Schema Updates**: Added fields for Google authentication (googleId, isGoogleUser, picture)
3. **Auth Service Methods**: Added methods to handle Google user validation and creation
4. **Auth Controller Endpoints**: Added endpoints for Google authentication flow

## Environment Configuration

To use Google authentication, you need to set up the following environment variables:

```
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_CALLBACK_URL=http://localhost:4000/api/auth/google/callback
FRONTEND_URL=http://localhost:3000
```

## How to Obtain Google OAuth Credentials

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Navigate to "APIs & Services" > "Credentials"
4. Click "Create Credentials" > "OAuth client ID"
5. Select "Web application" as the application type
6. Add your application name
7. Add authorized JavaScript origins (e.g., http://localhost:3000)
8. Add authorized redirect URIs (e.g., http://localhost:4000/api/auth/google/callback)
9. Click "Create" to generate your client ID and client secret

## Frontend Implementation

The frontend implementation requires the following components:

### 1. Google Login Button Component

A React component is provided in `docs/examples/GoogleLoginButton.tsx` that demonstrates how to implement a Google login button.

### 2. Authentication Flow

The authentication flow works as follows:

1. User clicks the Google login button
2. The application redirects to Google for authentication
3. After successful authentication, Google redirects to the callback URL
4. The backend processes the authentication and returns a JWT token
5. The frontend stores the token and updates the user's authentication state

### 3. Integration Example

Here's how to integrate the Google login button into your authentication page:

```tsx
import React, { useState } from 'react';
import GoogleLoginButton from '../components/GoogleLoginButton';
import { useAuth } from '../contexts/AuthContext';

const LoginPage: React.FC = () => {
  const { login } = useAuth();
  const [error, setError] = useState<string | null>(null);

  const handleGoogleLoginSuccess = (token: string) => {
    login(token);
    // Redirect to dashboard or home page
  };

  return (
    <div className="login-page">
      <h1>Login to Legal Document Analyzer</h1>
      
      {/* Regular login form */}
      <form>
        {/* ... your regular login form fields ... */}
      </form>
      
      <div className="separator">OR</div>
      
      {/* Google login button */}
      <GoogleLoginButton 
        onLoginSuccess={handleGoogleLoginSuccess}
        className="google-btn"
      />
      
      {error && <div className="error-message">{error}</div>}
    </div>
  );
};

export default LoginPage;
```

## Testing the Implementation

To test the Google authentication:

1. Ensure your backend server is running with the correct environment variables
2. Navigate to your login page in the frontend application
3. Click the "Sign in with Google" button
4. Complete the Google authentication flow
5. Verify that you are redirected back to your application and authenticated

## Security Considerations

1. **HTTPS**: Always use HTTPS in production to protect authentication data
2. **Token Storage**: Store JWT tokens securely (HttpOnly cookies are recommended)
3. **Scope Limitations**: Only request the minimum required scopes from Google
4. **Token Validation**: Always validate tokens on the server side
5. **Environment Variables**: Keep your Google client secret secure

## Troubleshooting

Common issues and solutions:

1. **Redirect URI Mismatch**: Ensure the callback URL in your code matches exactly what's registered in Google Cloud Console
2. **CORS Issues**: Check CORS configuration if you encounter cross-origin problems
3. **Token Expiration**: Implement token refresh logic for long-lived sessions
4. **Missing Scopes**: Ensure you're requesting the necessary scopes (email and profile)

## Next Steps

1. Add token refresh functionality
2. Implement account linking (connecting Google accounts to existing accounts)
3. Add profile picture display from Google accounts
4. Implement social sharing features

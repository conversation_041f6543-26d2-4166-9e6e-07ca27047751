# Document Automation API Endpoints

This document provides comprehensive documentation for the Intelligent Document Automation & Generation feature endpoints.

## Overview

The Document Automation feature leverages AI to assist in drafting and generating legal documents, moving beyond static templates. It provides four main capabilities:

1. **AI-Assisted Drafting** - Generate new legal documents using AI
2. **Related Document Generation** - Generate ancillary documents (schedules, exhibits, etc.)
3. **Clause Intelligence** - Get intelligent clause suggestions and auto-population
4. **Clause Library Auto-Population** - Automatically extract and categorize clauses from document corpus

## Authentication

All endpoints require JWT authentication with the `document_automation` feature enabled.

```
Authorization: Bearer <jwt_token>
```

## Base URL

```
/api/documents/automation
```

---

## 1. AI-Assisted Document Drafting

Generate new legal documents using AI based on prompts and requirements.

### Endpoint

```
POST /api/documents/automation/ai-assisted-drafting
```

### Request Body

```json
{
  "documentType": "nda",
  "draftingPrompt": {
    "prompt": "Create a simple non-disclosure agreement for a technology consulting project",
    "keyTerms": ["confidential information", "technology", "consulting"],
    "requiredClauses": ["termination", "governing law"],
    "context": "This is for a software development project"
  },
  "organizationPreferences": "Use conservative language and include standard disclaimers",
  "useClauseLibrary": true,
  "includeDisclaimers": true,
  "jurisdiction": "California"
}
```

### Request Parameters

| Field                            | Type     | Required | Description                                                                                                                                                                                                      |
| -------------------------------- | -------- | -------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `documentType`                   | string   | Yes      | Type of document to generate. Options: `contract`, `nda`, `msa`, `sow`, `amendment`, `addendum`, `schedule`, `exhibit`, `privacy_policy`, `terms_of_service`, `employment_agreement`, `lease_agreement`, `other` |
| `draftingPrompt`                 | object   | Yes      | Main drafting instructions                                                                                                                                                                                       |
| `draftingPrompt.prompt`          | string   | Yes      | Main prompt or instruction for document generation                                                                                                                                                               |
| `draftingPrompt.keyTerms`        | string[] | No       | Key terms or concepts to include                                                                                                                                                                                 |
| `draftingPrompt.requiredClauses` | string[] | No       | Specific clauses to include                                                                                                                                                                                      |
| `draftingPrompt.context`         | string   | No       | Additional context or requirements                                                                                                                                                                               |
| `organizationPreferences`        | string   | No       | Organization-specific preferences or standards                                                                                                                                                                   |
| `useClauseLibrary`               | boolean  | No       | Use organization clause library for suggestions                                                                                                                                                                  |
| `includeDisclaimers`             | boolean  | No       | Include standard legal disclaimers                                                                                                                                                                               |
| `jurisdiction`                   | string   | No       | Target jurisdiction for legal compliance                                                                                                                                                                         |

### Response

```json
{
  "content": "Generated document content...",
  "documentType": "nda",
  "metadata": {
    "generatedAt": "2025-05-25T03:28:49.413Z",
    "generationDurationMs": 12870,
    "modelUsed": "ai-service",
    "clausesUsed": 4,
    "organizationClausesUsed": 0
  },
  "suggestedClauses": [
    {
      "type": "termination",
      "content": "AI-generated termination clause",
      "source": "ai_generated",
      "confidence": 0.8
    }
  ],
  "recommendations": [
    "Define confidential information scope clearly",
    "Specify return of confidential materials"
  ]
}
```

### Response Fields

| Field              | Type     | Description                              |
| ------------------ | -------- | ---------------------------------------- |
| `content`          | string   | Generated document content               |
| `documentType`     | string   | Type of generated document               |
| `metadata`         | object   | Generation metadata and statistics       |
| `suggestedClauses` | array    | Clauses that were included or suggested  |
| `recommendations`  | string[] | Recommendations for document improvement |

---

## 2. Generate Related Documents

Automatically generate ancillary documents based on a primary agreement.

### Endpoint

```
POST /api/documents/automation/generate-related-documents
```

### Request Body

```json
{
  "primaryDocumentId": "doc-123-456",
  "documentTypes": ["schedule", "exhibit", "addendum"],
  "additionalContent": "Include technical specifications and delivery timelines",
  "autoPopulate": true
}
```

### Request Parameters

| Field               | Type     | Required | Description                                                                                                 |
| ------------------- | -------- | -------- | ----------------------------------------------------------------------------------------------------------- |
| `primaryDocumentId` | string   | Yes      | ID of the primary document                                                                                  |
| `documentTypes`     | string[] | Yes      | Types of related documents to generate. Options: `schedule`, `exhibit`, `addendum`, `amendment`, `appendix` |
| `additionalContent` | string   | No       | Specific content or data for related documents                                                              |
| `autoPopulate`      | boolean  | No       | Auto-populate from primary document content                                                                 |

### Response

```json
{
  "documents": [
    {
      "type": "schedule",
      "title": "Schedule A - Detailed Specifications",
      "content": "Generated schedule content...",
      "metadata": {
        "generatedFrom": "primary_document",
        "autoPopulated": true,
        "hasAdditionalContent": true
      }
    }
  ],
  "metadata": {
    "primaryDocumentId": "doc-123-456",
    "generatedAt": "2025-05-25T03:30:00.000Z",
    "generationDurationMs": 8500,
    "documentsGenerated": 3
  }
}
```

---

## 3. Clause Intelligence

Get intelligent clause suggestions and auto-population recommendations for document drafting.

### Endpoint

```
POST /api/documents/automation/clause-intelligence
```

### Request Body

```json
{
  "documentType": "contract",
  "currentContent": "This is a sample contract for software development services.",
  "sectionType": "payment",
  "context": "B2B software development agreement",
  "includeOrgClauses": true
}
```

### Request Parameters

| Field               | Type    | Required | Description                               |
| ------------------- | ------- | -------- | ----------------------------------------- |
| `documentType`      | string  | Yes      | Type of document being drafted            |
| `currentContent`    | string  | Yes      | Current document content or partial draft |
| `sectionType`       | string  | No       | Specific section or clause type needed    |
| `context`           | string  | No       | Context for clause suggestions            |
| `includeOrgClauses` | boolean | No       | Include organization-specific clauses     |

### Response

```json
{
  "suggestedClauses": [
    {
      "type": "payment_terms",
      "content": "Payment shall be due within 30 days...",
      "relevanceScore": 0.9,
      "source": "organization",
      "position": "middle",
      "explanation": "This payment clause is relevant based on your document type and content."
    }
  ],
  "autoPopulationSuggestions": [
    {
      "sectionName": "Effective Date",
      "suggestedContent": "This Agreement shall be effective as of [DATE] (\"Effective Date\").",
      "confidence": 0.9
    }
  ],
  "missingClauses": [
    {
      "type": "governing_law",
      "importance": "critical",
      "description": "Missing governing law clause - this is critical for contract documents"
    }
  ]
}
```

---

## 4. Auto-Build Clause Library from Document Corpus

Automatically extract and categorize clauses from organization's existing documents to build clause library.

### Endpoint

```
POST /api/documents/automation/build-clause-library
```

### Request Body

```json
{
  "includeExistingClauses": true,
  "minimumConfidence": 0.7,
  "maxClausesPerCategory": 10,
  "documentTypes": ["contract", "nda", "msa"],
  "analysisDepth": "detailed"
}
```

### Request Parameters

| Field                    | Type     | Required | Description                                                                  |
| ------------------------ | -------- | -------- | ---------------------------------------------------------------------------- |
| `includeExistingClauses` | boolean  | No       | Include existing clauses in the analysis                                     |
| `minimumConfidence`      | number   | No       | Minimum confidence threshold for clause extraction (0.0-1.0), default: 0.7   |
| `maxClausesPerCategory`  | number   | No       | Maximum number of clauses to extract per category, default: 10               |
| `documentTypes`          | string[] | No       | Filter by specific document types                                            |
| `analysisDepth`          | string   | No       | Depth of analysis: `basic`, `detailed`, `comprehensive`. Default: `detailed` |

### Response

```json
{
  "extractedClauses": [
    {
      "content": "This Agreement shall terminate upon...",
      "category": "termination",
      "confidence": 0.85,
      "sourceDocument": "doc-123-456",
      "patternType": "termination_clause"
    }
  ],
  "totalDocumentsAnalyzed": 25,
  "totalClausesExtracted": 47,
  "categoriesFound": ["termination", "payment", "liability", "confidentiality"],
  "processingDurationMs": 15420
}
```

### Response Fields

| Field                    | Type     | Description                              |
| ------------------------ | -------- | ---------------------------------------- |
| `extractedClauses`       | array    | Array of extracted clauses with metadata |
| `totalDocumentsAnalyzed` | number   | Number of documents processed            |
| `totalClausesExtracted`  | number   | Total number of clauses extracted        |
| `categoriesFound`        | string[] | Categories of clauses found              |
| `processingDurationMs`   | number   | Time taken for processing                |

---

## Error Responses

### 400 Bad Request - Validation Error

```json
{
  "statusCode": 400,
  "message": "Validation failed",
  "error": "Bad Request",
  "details": [
    {
      "field": "documentType",
      "constraints": [
        "documentType must be one of the following values: contract, nda, msa, sow, ..."
      ]
    }
  ],
  "path": "/api/documents/automation/ai-assisted-drafting",
  "timestamp": "2025-05-25T03:37:11.724Z"
}
```

### 401 Unauthorized

```json
{
  "statusCode": 401,
  "message": "Unauthorized",
  "error": "Bad Request",
  "details": null,
  "path": "/api/documents/automation/ai-assisted-drafting",
  "timestamp": "2025-05-25T03:23:27.298Z"
}
```

### 403 Forbidden - Feature Not Available

```json
{
  "statusCode": 403,
  "message": "Feature not available in current subscription",
  "error": "Forbidden"
}
```

### 404 Not Found - Document Not Found

```json
{
  "statusCode": 400,
  "message": "Failed to generate related documents: Document with id test-doc-123 not found",
  "error": "Bad Request"
}
```

---

## Usage Examples

### Example 1: Generate a Simple NDA

```bash
curl -X POST http://localhost:4000/api/documents/automation/ai-assisted-drafting \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your-jwt-token>" \
  -d '{
    "documentType": "nda",
    "draftingPrompt": {
      "prompt": "Create a simple NDA for technology consulting"
    }
  }'
```

### Example 2: Get Clause Suggestions

```bash
curl -X POST http://localhost:4000/api/documents/automation/clause-intelligence \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your-jwt-token>" \
  -d '{
    "documentType": "contract",
    "currentContent": "This is a software development agreement..."
  }'
```

### Example 3: Generate Related Documents

```bash
curl -X POST http://localhost:4000/api/documents/automation/generate-related-documents \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your-jwt-token>" \
  -d '{
    "primaryDocumentId": "your-document-id",
    "documentTypes": ["schedule", "exhibit"]
  }'
```

### Example 4: Auto-Build Clause Library

```bash
curl -X POST http://localhost:4000/api/documents/automation/build-clause-library \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your-jwt-token>" \
  -d '{
    "analysisDepth": "detailed",
    "minimumConfidence": 0.7,
    "maxClausesPerCategory": 5,
    "documentTypes": ["contract", "nda"]
  }'
```

---

## Notes

- All endpoints use conservative AI temperature settings (0.2-0.4) for reliable legal content
- Generated content should be reviewed by qualified legal professionals
- The system integrates with existing clause library and analytics systems
- All usage is tracked for analytics and monitoring purposes

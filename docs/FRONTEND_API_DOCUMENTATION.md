# Legal Document Analyzer API Documentation

This document provides comprehensive documentation for the frontend developer to integrate with the Legal Document Analyzer backend API.

## Table of Contents

1. [Authentication](#authentication)
2. [Users & Organizations](#users--organizations)
3. [Documents](#documents)
4. [Analysis](#analysis)
5. [Chat](#chat)
6. [Subscriptions](#subscriptions)

## Authentication

The API uses JWT-based authentication. All endpoints except the authentication endpoints require a valid JWT token.

### Base URL

```
/api/auth
```

### Endpoints

#### Register User

```
POST /register
```

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "firstName": "John",
  "lastName": "Doe",
  "organizationName": "Legal Team", // Optional, defaults to "[firstName]'s Workspace"
  "createNewOrganization": true, // Default: true
  "organizationId": "existing-org-id" // Required only if createNewOrganization is false
}
```

**Response:**

```json
{
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "firstName": "<PERSON>",
    "lastName": "Doe",
    "organizations": ["org-id"]
  },
  "tokens": {
    "accessToken": "jwt-token",
    "refreshToken": "refresh-token"
  }
}
```

#### Login

```
POST /login
```

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response:**

```json
{
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "organizations": ["org-id"]
  },
  "tokens": {
    "accessToken": "jwt-token",
    "refreshToken": "refresh-token"
  }
}
```

#### Refresh Token

```
POST /refresh-token
```

**Request Body:**

```json
{
  "refreshToken": "refresh-token"
}
```

**Response:**

```json
{
  "accessToken": "new-jwt-token",
  "refreshToken": "new-refresh-token"
}
```

#### Logout

```
POST /logout
```

**Request Body:**

```json
{
  "refreshToken": "refresh-token"
}
```

**Response:**

```json
{
  "success": true
}
```

## Users & Organizations

### Base URL

```
/api/users
/api/organizations
```

### User Endpoints

#### Get Current User

```
GET /api/users/me
```

**Response:**

```json
{
  "id": "user-id",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "organizations": [
    {
      "id": "org-id",
      "name": "Legal Team",
      "role": "admin"
    }
  ]
}
```

#### Update User

```
PATCH /api/users/me
```

**Request Body:**

```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>" // Requires email verification
}
```

**Response:**

```json
{
  "id": "user-id",
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe"
}
```

### Organization Endpoints

#### Get User's Organizations

```
GET /api/organizations
```

**Response:**

```json
[
  {
    "id": "org-id",
    "name": "Legal Team",
    "role": "admin",
    "memberCount": 5,
    "subscription": {
      "tier": "pro",
      "status": "active"
    }
  }
]
```

#### Get Organization Details

```
GET /api/organizations/:organizationId
```

**Response:**

```json
{
  "id": "org-id",
  "name": "Legal Team",
  "members": [
    {
      "id": "user-id",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "admin"
    }
  ],
  "subscription": {
    "tier": "pro",
    "status": "active",
    "currentPeriodEnd": "2025-04-20T00:00:00.000Z"
  }
}
```

#### Create Organization

```
POST /api/organizations
```

**Request Body:**

```json
{
  "name": "New Legal Team"
}
```

**Response:**

```json
{
  "id": "new-org-id",
  "name": "New Legal Team",
  "role": "admin"
}
```

#### Update Organization

```
PATCH /api/organizations/:organizationId
```

**Request Body:**

```json
{
  "name": "Updated Legal Team Name"
}
```

**Response:**

```json
{
  "id": "org-id",
  "name": "Updated Legal Team Name"
}
```

#### Add Member to Organization

```
POST /api/organizations/:organizationId/members
```

**Request Body:**

```json
{
  "email": "<EMAIL>",
  "role": "member" // Options: admin, member
}
```

**Response:**

```json
{
  "success": true,
  "message": "Invitation <NAME_EMAIL>"
}
```

#### Remove Member from Organization

```
DELETE /api/organizations/:organizationId/members/:userId
```

**Response:**

```json
{
  "success": true
}
```

## Documents

### Endpoints

#### Upload Document

```
POST /api/documents/upload
```

**Request Body:**
Form data with:

- `file`: Document file (PDF, DOCX, TXT)
- `metadata`: JSON object with:
  ```json
  {
    "title": "string",
    "author": "string"
  }
  ```

**Response:**

```json
{
  "id": "doc-id",
  "filename": "contract.pdf",
  "size": 1024000,
  "uploadDate": "2025-03-20T14:30:00Z",
  "metadata": {
    "title": "Contract Agreement",
    "author": "John Doe"
  }
}
```

#### Get All Documents

```
GET /api/documents
```

**Response:**

```json
[
  {
    "id": "doc-id",
    "filename": "contract.pdf",
    "size": 1024000,
    "uploadDate": "2025-03-20T14:30:00Z",
    "metadata": {
      "title": "Contract Agreement",
      "author": "John Doe",
      "pageCount": 10
    }
  }
]
```

#### Get Document by ID

```
GET /api/documents/:id
```

**Response:**

```json
{
  "id": "doc-id",
  "filename": "contract.pdf",
  "size": 1024000,
  "uploadDate": "2025-03-20T14:30:00Z",
  "metadata": {
    "title": "Contract Agreement",
    "author": "John Doe",
    "pageCount": 10
  }
}
```

#### Get Document Content

```
GET /api/documents/:id/content
```

**Response:**

```json
{
  "content": "Full document text content..."
}
```

#### Analyze Document

```
POST /api/documents/:id/analyze
```

**Request Body:**

```json
{
  "documentType": "contract",
  "query": "What are the key risks in this agreement?" // Optional
}
```

**Response:**

```json
{
  "analysisId": "analysis-id",
  "result": {
    "summary": "This is a service agreement between...",
    "risks": [
      {
        "type": "legal",
        "description": "Ambiguous termination clause..."
      }
    ],
    "recommendations": ["Consider clarifying the termination conditions..."]
  }
}
```

#### Get Analysis Results

```
GET /api/documents/:id/analysis
```

**Query Parameters:**

- `latest`: boolean (optional) - If true, returns only the most recent analysis

**Response:**

```json
{
  "id": "analysis-id",
  "documentId": "doc-id",
  "analysisContent": {
    "summary": "...",
    "risks": [],
    "recommendations": []
  },
  "aiMetadata": {
    "provider": "openai",
    "detectedDocumentType": "contract"
  },
  "createdAt": "2025-03-20T14:35:00Z"
}
```

#### Analyze Multiple Documents

```
POST /api/documents/analyze-multiple
```

**Request Body:**

```json
{
  "primaryDocumentId": "doc-id-1",
  "relatedDocumentIds": ["doc-id-2", "doc-id-3"],
  "documentType": "contract"
}
```

**Response:**
Similar to single document analysis but includes comparative analysis.

#### Compare Document Sections

```
POST /api/documents/compare-sections
```

**Request Body:**

```json
{
  "documentSections": {
    "doc1": [
      {
        "id": "section1",
        "content": "Section content..."
      }
    ],
    "doc2": [
      {
        "id": "section1",
        "content": "Section content..."
      }
    ]
  },
  "comparisonType": "both"
}
```

#### Queue Document Processing

```
POST /api/documents/:documentId/process-async
```

**Request Body:**

```json
{
  "priority": "high",
  "extractMetadata": true,
  "generateSummary": true
}
```

**Response:**

```json
{
  "jobId": "job-id",
  "documentId": "doc-id",
  "status": "queued"
}
```

#### Get Job Status

```
GET /api/documents/jobs/:jobId
```

**Response:**

```json
{
  "id": "job-id",
  "status": "processing",
  "progress": 50,
  "documentId": "doc-id",
  "failedReason": null,
  "attempts": 1
}
```

#### Get Document Jobs

```
GET /api/documents/:documentId/jobs
```

**Response:**

```json
[
  {
    "id": "job-id",
    "status": "completed",
    "progress": 100,
    "documentId": "doc-id"
  }
]
```

#### Detect Patterns

```
POST /api/documents/:id/patterns
```

**Request Body:**

```json
{
  "query": "Find all force majeure clauses"
}
```

**Response:**

```json
[
  {
    "patternType": "force_majeure",
    "content": "In the event of any force majeure...",
    "location": {
      "startIndex": 1200,
      "endIndex": 1500
    },
    "metadata": {
      "riskLevel": "medium",
      "recommendations": [
        "Consider updating force majeure clause to include pandemic scenarios"
      ]
    }
  }
]
```

Endpoints for managing and interacting with uploaded documents.

**Note:** Document management and direct analysis endpoints (upload, list, get details, analyze, patterns) are handled by the `DocumentsController`.

### Base URL

```
/api/documents
```

### Endpoints

#### Upload Document

```
POST /upload
```

**Request Body:**
Form data with:

- `file`: Document file (PDF, DOCX, TXT)
- `metadata`: (Optional) JSON string with additional metadata

**Response:**

```json
{
  "id": "doc-id",
  "filename": "contract.pdf",
  "fileType": "application/pdf",
  "fileSize": 1024000,
  "uploadedAt": "2025-03-20T14:30:00Z",
  "status": "processing",
  "metadata": {
    "title": "Contract Agreement"
  }
}
```

#### Get All Documents

```
GET /
```

**Query Parameters:**

- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `sortBy`: Field to sort by (default: uploadedAt)
- `sortOrder`: asc or desc (default: desc)
- `query`: Search term

**Response:**

```json
{
  "items": [
    {
      "id": "doc-id",
      "filename": "contract.pdf",
      "fileType": "application/pdf",
      "fileSize": 1024000,
      "uploadedAt": "2025-03-20T14:30:00Z",
      "status": "processed",
      "metadata": {
        "title": "Contract Agreement",
        "parties": ["Company A", "Company B"],
        "documentType": "Legal Contract"
      }
    }
  ],
  "total": 45,
  "page": 1,
  "pages": 5,
  "limit": 10
}
```

#### Get Document by ID

```
GET /:documentId
```

**Response:**

```json
{
  "id": "doc-id",
  "filename": "contract.pdf",
  "fileType": "application/pdf",
  "fileSize": 1024000,
  "uploadedAt": "2025-03-20T14:30:00Z",
  "status": "processed",
  "content": "Full document text...",
  "metadata": {
    "title": "Contract Agreement",
    "parties": ["Company A", "Company B"],
    "documentType": "Legal Contract"
  },
  "clauses": [
    {
      "id": "clause-id",
      "title": "Termination Clause",
      "content": "This agreement may be terminated...",
      "metadata": {
        "type": "termination",
        "risk": "medium"
      }
    }
  ]
}
```

#### Delete Document

```
DELETE /:documentId
```

**Response:**

```json
{
  "success": true
}
```

#### Update Document Metadata

```
PATCH /:documentId/metadata
```

**Request Body:**

```json
{
  "title": "Updated Contract Title",
  "customField": "Custom Value"
}
```

**Response:**

```json
{
  "id": "doc-id",
  "metadata": {
    "title": "Updated Contract Title",
    "customField": "Custom Value",
    "parties": ["Company A", "Company B"],
    "documentType": "Legal Contract"
  }
}
```

#### Download Original Document

```
GET /:documentId/download
```

**Response:**
Binary file download

## Analysis

### Base URL

```
/api/analysis
```

### Endpoints

#### Run Basic Analysis

```
POST /
```

**Request Body:**

```json
{
  "documentId": "doc-id",
  "analysisType": "basic",
  "options": {
    "includeRiskAssessment": true,
    "includeParties": true
  }
}
```

**Response:**

```json
{
  "id": "analysis-id",
  "documentId": "doc-id",
  "status": "processing",
  "createdAt": "2025-03-20T14:35:00Z"
}
```

#### Run Advanced Analysis (Professional & Enterprise Tiers)

```
POST /advanced
```

**Request Body:**

```json
{
  "documentId": "doc-id",
  "analysisType": "advanced",
  "options": {
    "includeRiskAssessment": true,
    "includeParties": true,
    "includeComplianceCheck": true,
    "jurisdictions": ["US", "EU"]
  }
}
```

**Response:**

```json
{
  "id": "analysis-id",
  "documentId": "doc-id",
  "status": "processing",
  "createdAt": "2025-03-20T14:35:00Z"
}
```

#### Get Analysis Status

```
GET /:analysisId
```

**Response:**

```json
{
  "id": "analysis-id",
  "documentId": "doc-id",
  "status": "completed",
  "createdAt": "2025-03-20T14:35:00Z",
  "completedAt": "2025-03-20T14:36:30Z",
  "result": {
    "summary": "This contract is a standard service agreement...",
    "riskAssessment": {
      "overall": "medium",
      "details": [
        {
          "clause": "clause-id",
          "risk": "high",
          "reason": "Ambiguous termination conditions"
        }
      ]
    },
    "parties": [
      {
        "name": "Company A",
        "role": "service provider"
      },
      {
        "name": "Company B",
        "role": "client"
      }
    ],
    "keyDates": [
      {
        "name": "Effective Date",
        "date": "2025-04-01"
      },
      {
        "name": "Termination Date",
        "date": "2026-04-01"
      }
    ]
  }
}
```

#### Get All Analyses for a Document

```
GET /document/:documentId
```

**Response:**

```json
[
  {
    "id": "analysis-id",
    "documentId": "doc-id",
    "analysisType": "basic",
    "status": "completed",
    "createdAt": "2025-03-20T14:35:00Z",
    "completedAt": "2025-03-20T14:36:30Z"
  }
]
```

## Chat

Endpoints for managing chat sessions related to documents.

**Note:** Chat session management and messaging endpoints are handled by the `ChatController`.

### Base URL

```
/api/chat
```

### Endpoints

#### Create Chat Session

```
POST /sessions
```

**Request Body:**

```json
{
  "title": "Contract Analysis Discussion",
  "documentIds": ["doc-id-1", "doc-id-2"] // Optional, can start with no documents
}
```

**Response:**

```json
{
  "id": "session-id",
  "title": "Contract Analysis Discussion",
  "createdAt": "2025-03-20T14:40:00Z",
  "documents": [
    {
      "id": "doc-id-1",
      "filename": "contract1.pdf"
    },
    {
      "id": "doc-id-2",
      "filename": "contract2.pdf"
    }
  ]
}
```

#### Get All Chat Sessions

```
GET /sessions
```

**Response:**

```json
[
  {
    "id": "session-id",
    "title": "Contract Analysis Discussion",
    "createdAt": "2025-03-20T14:40:00Z",
    "updatedAt": "2025-03-20T15:05:00Z",
    "documentCount": 2,
    "messageCount": 5
  }
]
```

#### Get Chat Session by ID

```
GET /sessions/:sessionId
```

**Response:**

```json
{
  "id": "session-id",
  "title": "Contract Analysis Discussion",
  "createdAt": "2025-03-20T14:40:00Z",
  "updatedAt": "2025-03-20T15:05:00Z",
  "documents": [
    {
      "id": "doc-id-1",
      "filename": "contract1.pdf"
    },
    {
      "id": "doc-id-2",
      "filename": "contract2.pdf"
    }
  ],
  "messages": [
    {
      "id": "msg-id-1",
      "sender": "user",
      "content": "What are the key differences between these two contracts?",
      "timestamp": "2025-03-20T14:45:00Z"
    },
    {
      "id": "msg-id-2",
      "sender": "assistant",
      "content": "The main differences are in the termination clauses and payment terms...",
      "timestamp": "2025-03-20T14:45:30Z",
      "references": [
        {
          "documentId": "doc-id-1",
          "clauseId": "clause-id-1",
          "text": "Termination requires 30 days notice"
        },
        {
          "documentId": "doc-id-2",
          "clauseId": "clause-id-2",
          "text": "Termination requires 60 days notice"
        }
      ]
    }
  ]
}
```

#### Send Message

```
POST /sessions/:sessionId/messages
```

**Request Body:**

```json
{
  "content": "What are the key differences between these two contracts?"
}
```

**Response:**

```json
{
  "id": "msg-id-1",
  "sender": "user",
  "content": "What are the key differences between these two contracts?",
  "timestamp": "2025-03-20T14:45:00Z"
}
```

#### Get Chat Messages

```
GET /sessions/:sessionId/messages
```

**Query Parameters:**

- `before`: Message ID to get messages before
- `limit`: Number of messages to retrieve (default: 50)

**Response:**

```json
[
  {
    "id": "msg-id-2",
    "sender": "assistant",
    "content": "The main differences are in the termination clauses and payment terms...",
    "timestamp": "2025-03-20T14:45:30Z",
    "references": [
      {
        "documentId": "doc-id-1",
        "clauseId": "clause-id-1",
        "text": "Termination requires 30 days notice"
      },
      {
        "documentId": "doc-id-2",
        "clauseId": "clause-id-2",
        "text": "Termination requires 60 days notice"
      }
    ]
  },
  {
    "id": "msg-id-1",
    "sender": "user",
    "content": "What are the key differences between these two contracts?",
    "timestamp": "2025-03-20T14:45:00Z"
  }
]
```

#### Add Document to Chat Session

```
POST /sessions/:sessionId/documents
```

**Request Body:**

```json
{
  "documentId": "doc-id-3"
}
```

**Response:**

```json
{
  "success": true,
  "documents": [
    {
      "id": "doc-id-1",
      "filename": "contract1.pdf"
    },
    {
      "id": "doc-id-2",
      "filename": "contract2.pdf"
    },
    {
      "id": "doc-id-3",
      "filename": "contract3.pdf"
    }
  ]
}
```

#### Remove Document from Chat Session

```
DELETE /sessions/:sessionId/documents/:documentId
```

**Response:**

```json
{
  "success": true,
  "documents": [
    {
      "id": "doc-id-1",
      "filename": "contract1.pdf"
    },
    {
      "id": "doc-id-2",
      "filename": "contract2.pdf"
    }
  ]
}
```

## Subscriptions

### Base URL

```
/api/subscriptions
```

### Endpoints

#### Get Current Subscription

```
GET /
```

**Response:**

```json
{
  "organizationId": "org-id",
  "tier": "pro",
  "status": "active",
  "cancelAtPeriodEnd": true,
  "currentPeriodEnd": "2025-04-01T00:00:00Z",
  "trialTier": "pro",
  "trialEndDate": "2025-05-11T00:00:00Z"
}
```

#### Create Subscription

```
POST /
```

**Request Body:**

```json
{
  "tier": "pro",
  "email": "<EMAIL>"
}
```

**Response:**

```json
{
  "clientSecret": "stripe_client_secret",
  "subscriptionId": "sub_123456",
  "customerId": "cus_123456"
}
```

_Note: The client secret is used to complete the payment with Stripe Elements on the frontend._

#### Cancel Subscription

```
POST /cancel
```

**Request Body:**

```json
{
  "cancelAtPeriodEnd": true
}
```

**Response:**

```json
{
  "organizationId": "org-id",
  "tier": "pro",
  "status": "active",
  "cancelAtPeriodEnd": true,
  "currentPeriodEnd": "2025-04-01T00:00:00Z"
}
```

#### Change Subscription Tier

```
POST /change-tier
```

**Request Body:**

```json
{
  "newTier": "admin"
}
```

**Response:**

```json
{
  "clientSecret": "stripe_client_secret",
  "subscriptionId": "sub_123456"
}
```

_Note: If upgrading to a higher tier, the client secret is required to complete additional payment with Stripe Elements._

#### Get Available Plans

```
GET /plans
```

**Response:**

```json
[
  {
    "id": "free",
    "name": "Free",
    "price": 0,
    "currency": "USD",
    "interval": "month",
    "features": [
      {
        "name": "Document Limit",
        "value": "10 documents"
      },
      {
        "name": "Analysis",
        "value": "50 per month"
      },
      {
        "name": "Basic Analysis",
        "value": "Included"
      },
      {
        "name": "Document Organization",
        "value": "Basic"
      },
      {
        "name": "User Feedback",
        "value": "Included"
      },
      {
        "name": "Pro Features Trial",
        "value": "14 days"
      }
    ]
  },
  {
    "id": "pro",
    "name": "Pro",
    "price": 49.99,
    "currency": "USD",
    "interval": "month",
    "features": [
      {
        "name": "Document Limit",
        "value": "200 documents"
      },
      {
        "name": "Analysis",
        "value": "Unlimited"
      },
      {
        "name": "Basic Analysis",
        "value": "Included"
      },
      {
        "name": "Advanced Analysis",
        "value": "Included"
      },
      {
        "name": "Batch Processing",
        "value": "Included"
      },
      {
        "name": "Document Organization",
        "value": "Advanced"
      },
      {
        "name": "User Feedback",
        "value": "Included"
      },
      {
        "name": "Custom Models",
        "value": "Included"
      },
      {
        "name": "Enhanced Comparison",
        "value": "Included"
      },
      {
        "name": "Document Comparison",
        "value": "Included"
      },
      {
        "name": "Advanced Analytics",
        "value": "Included"
      }
    ]
  },
  {
    "id": "admin",
    "name": "Admin",
    "price": 199.99,
    "currency": "USD",
    "interval": "month",
    "features": [
      {
        "name": "Document Limit",
        "value": "Unlimited"
      },
      {
        "name": "Analysis",
        "value": "Unlimited"
      },
      {
        "name": "All Pro Features",
        "value": "Included"
      },
      {
        "name": "Admin Features",
        "value": "Included"
      },
      {
        "name": "Team Management",
        "value": "Included"
      },
      {
        "name": "Cross-Organization Access",
        "value": "Included"
      }
    ]
  }
]
```

## Error Handling

All API endpoints follow a consistent error format:

```json
{
  "statusCode": 400,
  "message": "Error message explaining what went wrong",
  "error": "Bad Request"
}
```

Common error codes:

- `400`: Bad Request - Invalid input data
- `401`: Unauthorized - Missing or invalid authentication
- `403`: Forbidden - Insufficient permissions or subscription tier
- `404`: Not Found - Resource not found
- `429`: Too Many Requests - Rate limit exceeded
- `500`: Internal Server Error - Server-side error

## Feature Availability by Subscription Tier

| Feature             | Free       | Pro        | Admin      |
| ------------------- | ---------- | ---------- | ---------- |
| Basic Analysis      | ✓          | ✓          | ✓          |
| Document Upload     | ✓ (10 max) | ✓ (200 max) | ✓ (Unlimited) |
| Chat                | ✓          | ✓          | ✓          |
| Advanced Analysis   | ✗          | ✓          | ✓          |
| Batch Processing    | ✗          | ✓          | ✓          |
| Document Organization| Basic     | Advanced   | Advanced   |
| User Feedback       | ✓          | ✓          | ✓          |
| Custom Models       | ✗          | ✓          | ✓          |
| Enhanced Comparison | ✗          | ✓          | ✓          |
| Document Comparison | ✗          | ✓          | ✓          |
| Advanced Analytics  | ✗          | ✓          | ✓          |
| Admin Features      | ✗          | ✗          | ✓          |
| Team Management     | ✗          | ✗          | ✓          |
| Cross-Organization Access| ✗       | ✗          | ✓          |

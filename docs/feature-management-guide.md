# Feature Management Guide

This guide explains how to manage pro features and update user subscriptions when new features are added to the Legal Document Analyzer.

## 🎯 **Overview**

The Feature Management System provides:
- **Automated subscription updates** when new features are added
- **Tier-based feature distribution** (FREE, PRO, ADMIN)
- **Database migration scripts** for feature rollouts
- **Feature usage analytics** and monitoring
- **Manual feature synchronization** for specific subscriptions

---

## 🔧 **Adding New Features**

### **Method 1: Using the API (Recommended)**

#### Add a Single Feature
```bash
curl -X POST http://localhost:4000/api/admin/features/quick-add \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin-jwt-token>" \
  -d '{
    "featureNames": ["new_ai_feature"],
    "targetTier": "PRO",
    "category": "ai_analysis",
    "dryRun": false
  }'
```

#### Add Multiple Features
```bash
curl -X POST http://localhost:4000/api/admin/features/update-subscriptions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin-jwt-token>" \
  -d '{
    "features": [
      {
        "name": "advanced_contract_analysis",
        "description": "Advanced contract analysis with AI",
        "category": "ai_analysis",
        "requiredTier": "PRO"
      },
      {
        "name": "bulk_document_processing",
        "description": "Process multiple documents simultaneously",
        "category": "workflow",
        "requiredTier": "PRO"
      }
    ],
    "dryRun": false,
    "notifyUsers": true
  }'
```

### **Method 2: Using Migration Scripts**

#### Preview Changes (Dry Run)
```bash
npm run migrate:features:dry
```

#### Add a New Feature
```bash
npm run migrate:features -- --feature "new_feature_name" --tier "PRO" --category "ai_analysis" --description "Feature description"
```

#### Sync All Subscriptions
```bash
npm run migrate:features:sync
```

#### Sync Single Subscription
```bash
npm run migrate:features -- --subscription-id "507f1f77bcf86cd799439011"
```

---

## 📊 **Feature Management Workflow**

### **1. Development Phase**
1. **Implement the feature** in your controller/service
2. **Add feature flag** to `FeatureAvailabilityGuard`
3. **Update feature definitions** in `FeatureManagementService`
4. **Add `@RequireFeatures()` decorator** to protected endpoints

### **2. Testing Phase**
```bash
# Test with dry run first
npm run migrate:features -- --feature "test_feature" --tier "PRO" --dry-run

# Check what would be updated
curl -X GET http://localhost:4000/api/admin/features/usage-stats \
  -H "Authorization: Bearer <admin-jwt-token>"
```

### **3. Production Deployment**
```bash
# 1. Deploy code with new feature
git push origin main

# 2. Run migration to update subscriptions
npm run migrate:features -- --feature "new_feature" --tier "PRO" --category "ai_analysis"

# 3. Verify deployment
curl -X GET http://localhost:4000/api/admin/features/usage-stats \
  -H "Authorization: Bearer <admin-jwt-token>"
```

---

## 🛠 **Common Use Cases**

### **Case 1: Adding a New Pro Feature**

```typescript
// 1. Add to controller
@Post('new-endpoint')
@RequireFeatures('new_pro_feature')
async newProFeature() {
  // Implementation
}

// 2. Update FeatureAvailabilityGuard
[SubscriptionTier.PRO]: [
  // ... existing features
  'new_pro_feature',
]

// 3. Deploy and migrate
npm run migrate:features -- --feature "new_pro_feature" --tier "PRO"
```

### **Case 2: Upgrading Free Feature to Pro**

```bash
# 1. Remove from FREE tier in code
# 2. Add to PRO tier in code
# 3. Run migration to update subscriptions
npm run migrate:features:sync
```

### **Case 3: Bulk Feature Addition**

```bash
# Use API for multiple features
curl -X POST http://localhost:4000/api/admin/features/update-subscriptions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin-jwt-token>" \
  -d '{
    "features": [
      {"name": "feature1", "requiredTier": "PRO", "category": "ai"},
      {"name": "feature2", "requiredTier": "PRO", "category": "workflow"},
      {"name": "feature3", "requiredTier": "ADMIN", "category": "admin"}
    ]
  }'
```

---

## 📈 **Monitoring and Analytics**

### **Get Feature Usage Statistics**
```bash
curl -X GET http://localhost:4000/api/admin/features/usage-stats \
  -H "Authorization: Bearer <admin-jwt-token>"
```

**Response:**
```json
{
  "success": true,
  "data": {
    "totalSubscriptions": 150,
    "featureUsage": {
      "document_automation": {
        "count": 45,
        "percentage": 30.0,
        "tier": "PRO"
      },
      "compliance_auditor": {
        "count": 23,
        "percentage": 15.3,
        "tier": "PRO"
      }
    }
  }
}
```

### **Get Features by Tier**
```bash
curl -X GET http://localhost:4000/api/admin/features/tier/PRO \
  -H "Authorization: Bearer <admin-jwt-token>"
```

---

## 🔍 **Troubleshooting**

### **Common Issues**

#### **1. Feature Not Available After Migration**
```bash
# Check subscription features
curl -X GET http://localhost:4000/api/subscription/current \
  -H "Authorization: Bearer <user-jwt-token>"

# Sync specific subscription
npm run migrate:features -- --subscription-id "SUBSCRIPTION_ID"
```

#### **2. Migration Errors**
```bash
# Run with dry-run to see what would happen
npm run migrate:features:dry

# Check logs for specific errors
tail -f logs/application.log | grep "FeatureManagement"
```

#### **3. Feature Guard Blocking Access**
```typescript
// Verify feature is in the correct tier
const tierFeatures = {
  [SubscriptionTier.PRO]: [
    'your_feature_name', // Make sure this exists
  ],
};
```

---

## 🚀 **Best Practices**

### **1. Always Use Dry Run First**
```bash
# Test before applying
npm run migrate:features -- --feature "new_feature" --tier "PRO" --dry-run
```

### **2. Feature Naming Convention**
- Use snake_case: `document_automation`
- Be descriptive: `ai_assisted_drafting` not `ai_draft`
- Include category: `compliance_auditor` not `auditor`

### **3. Gradual Rollout**
```bash
# 1. Add to ADMIN tier first
npm run migrate:features -- --feature "beta_feature" --tier "ADMIN"

# 2. Monitor usage and feedback
# 3. Later promote to PRO tier
npm run migrate:features:sync
```

### **4. Feature Categories**
- `core` - Basic functionality
- `ai_analysis` - AI-powered analysis features
- `compliance` - Compliance and risk management
- `workflow` - Workflow automation
- `litigation` - Litigation support
- `contracts` - Contract management
- `training` - AI training and simulation
- `collaboration` - Team collaboration
- `admin` - Administrative features
- `enterprise` - Enterprise-only features

---

## 📋 **Migration Script Reference**

### **Available Commands**
```bash
# Sync all subscriptions with current feature definitions
npm run migrate:features:sync

# Preview all changes without applying
npm run migrate:features:dry

# Add specific feature
npm run migrate:features -- --feature "FEATURE_NAME" --tier "TIER" [options]

# Sync single subscription
npm run migrate:features -- --subscription-id "ID"

# Show help
npm run migrate:features
```

### **Options**
- `--dry-run` - Preview changes without applying
- `--feature` - Feature name to add
- `--tier` - Required tier (FREE, PRO, ADMIN)
- `--category` - Feature category
- `--description` - Feature description
- `--subscription-id` - Specific subscription to sync
- `--sync-all` - Sync all subscriptions

---

## 🔐 **Security Considerations**

1. **Admin Access Required** - Feature management requires ADMIN role
2. **Audit Logging** - All feature changes are logged
3. **Rollback Capability** - Feature history is maintained
4. **Validation** - Feature names and tiers are validated
5. **Rate Limiting** - API endpoints are rate limited

---

## 📞 **Support**

For issues with feature management:
1. Check the application logs
2. Verify subscription status
3. Run feature sync for affected users
4. Contact development team with specific error messages

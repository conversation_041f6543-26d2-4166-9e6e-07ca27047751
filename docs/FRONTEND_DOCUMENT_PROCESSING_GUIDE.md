# Frontend Document Processing Integration Guide

## 🎯 Overview

This guide covers the frontend implementation for the new high-performance document processing system. The backend now provides 2-3 second upload responses with real-time processing status tracking.

## 📡 API Endpoints

### Upload Document
```typescript
POST /api/documents/upload
Content-Type: multipart/form-data
Authorization: Bearer {token}

// Form data
file: File
title: string
author?: string
```

**Response (2-3 seconds):**
```json
{
  "message": "Document uploaded successfully and processing started",
  "document": {
    "id": "84d4f6c0-854f-4c2c-a5f8-a1ecd9e7d134",
    "title": "Contract Document",
    "filename": "contract.pdf",
    "size": 2048576,
    "uploadDate": "2025-07-14T10:30:00Z",
    "status": "uploaded",
    "processingStatus": "queued",
    "estimatedProcessingTime": 5000
  }
}
```

### Get Processing Status
```typescript
GET /api/documents/{documentId}/processing-status
Authorization: Bearer {token}
```

**Response:**
```json
{
  "documentId": "84d4f6c0-854f-4c2c-a5f8-a1ecd9e7d134",
  "status": "processing", // "queued" | "processing" | "completed" | "failed"
  "progress": 45,         // 0-100
  "jobId": "4",          // Queue job ID for tracking
  "estimatedTimeRemaining": 15000, // milliseconds
  "error": null          // Error message if failed
}
```

## 🔄 Frontend Implementation

### 1. Upload with Immediate Feedback

```typescript
// Upload handler with immediate response
const uploadDocument = async (file: File, title: string) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('title', title);

  try {
    // Fast upload response (2-3 seconds)
    const response = await fetch('/api/documents/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: formData
    });

    const result = await response.json();
    
    // Immediately show success and start status polling
    showUploadSuccess(result.document);
    startStatusPolling(result.document.id);
    
    return result.document;
  } catch (error) {
    showUploadError(error.message);
  }
};
```

### 2. Real-time Status Polling

```typescript
// Poll processing status every 2 seconds
const startStatusPolling = (documentId: string) => {
  const pollInterval = setInterval(async () => {
    try {
      const response = await fetch(`/api/documents/${documentId}/processing-status`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      const status = await response.json();
      
      // Update UI with current status
      updateProcessingUI(status);
      
      // Stop polling when complete or failed
      if (status.status === 'completed' || status.status === 'failed') {
        clearInterval(pollInterval);
        handleProcessingComplete(status);
      }
    } catch (error) {
      console.error('Status polling error:', error);
    }
  }, 2000); // Poll every 2 seconds
  
  return pollInterval;
};
```

### 3. Progress UI Component

```typescript
interface ProcessingStatusProps {
  documentId: string;
  onComplete?: (document: any) => void;
}

const ProcessingStatus: React.FC<ProcessingStatusProps> = ({ 
  documentId, 
  onComplete 
}) => {
  const [status, setStatus] = useState<ProcessingStatus | null>(null);
  const [polling, setPolling] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const interval = startStatusPolling(documentId);
    setPolling(interval);
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [documentId]);

  const updateProcessingUI = (newStatus: ProcessingStatus) => {
    setStatus(newStatus);
    
    if (newStatus.status === 'completed' && onComplete) {
      onComplete(newStatus);
    }
  };

  return (
    <div className="processing-status">
      {status && (
        <>
          <div className="status-header">
            <span className={`status-badge ${status.status}`}>
              {status.status.toUpperCase()}
            </span>
            {status.jobId && (
              <span className="job-id">Job #{status.jobId}</span>
            )}
          </div>
          
          <div className="progress-bar">
            <div 
              className="progress-fill"
              style={{ width: `${status.progress}%` }}
            />
          </div>
          
          <div className="progress-text">
            {status.progress}% complete
            {status.estimatedTimeRemaining && (
              <span className="time-remaining">
                • {Math.ceil(status.estimatedTimeRemaining / 1000)}s remaining
              </span>
            )}
          </div>
          
          {status.error && (
            <div className="error-message">
              Error: {status.error}
            </div>
          )}
        </>
      )}
    </div>
  );
};
```

### 4. Upload Flow Component

```typescript
const DocumentUpload: React.FC = () => {
  const [uploading, setUploading] = useState(false);
  const [uploadedDocument, setUploadedDocument] = useState<any>(null);

  const handleFileUpload = async (file: File, title: string) => {
    setUploading(true);
    
    try {
      // Fast upload (2-3 seconds)
      const document = await uploadDocument(file, title);
      setUploadedDocument(document);
      
      // Show immediate success
      toast.success('Document uploaded successfully! Processing started.');
    } catch (error) {
      toast.error('Upload failed: ' + error.message);
    } finally {
      setUploading(false);
    }
  };

  const handleProcessingComplete = (status: ProcessingStatus) => {
    if (status.status === 'completed') {
      toast.success('Document processing completed!');
      // Refresh document list or navigate to document
    } else if (status.status === 'failed') {
      toast.error('Document processing failed: ' + status.error);
    }
  };

  return (
    <div className="document-upload">
      {!uploadedDocument ? (
        <UploadForm 
          onUpload={handleFileUpload}
          uploading={uploading}
        />
      ) : (
        <ProcessingStatus
          documentId={uploadedDocument.id}
          onComplete={handleProcessingComplete}
        />
      )}
    </div>
  );
};
```

## 🎨 UI/UX Guidelines

### Status States
- **Queued**: Blue badge, "Waiting in queue..."
- **Processing**: Orange badge with spinner, progress bar
- **Completed**: Green badge, "Processing complete!"
- **Failed**: Red badge, error message

### Performance Expectations
- **Upload Response**: 2-3 seconds maximum
- **Status Updates**: Every 2 seconds
- **Processing Time**: 5-30 seconds typical

### Error Handling
- Network errors: Retry with exponential backoff
- Upload failures: Clear error messages
- Processing failures: Show detailed error from API

## 🔧 TypeScript Interfaces

```typescript
interface ProcessingStatus {
  documentId: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress: number; // 0-100
  jobId?: string;
  estimatedTimeRemaining?: number; // milliseconds
  error?: string;
}

interface UploadResponse {
  message: string;
  document: {
    id: string;
    title: string;
    filename: string;
    size: number;
    uploadDate: string;
    status: string;
    processingStatus: string;
    estimatedProcessingTime: number;
  };
}
```

## 🚀 Key Benefits for Users

1. **Immediate Feedback**: 2-3 second upload responses
2. **Real-time Updates**: Live progress tracking
3. **Transparent Process**: Clear status and time estimates
4. **Professional Experience**: No more long waits or uncertainty

This implementation provides a dramatically improved user experience with fast uploads and real-time processing visibility.

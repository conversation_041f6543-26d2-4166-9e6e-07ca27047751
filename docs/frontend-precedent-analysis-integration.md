# Frontend Integration Guide: Precedent Analysis

This document outlines how to integrate the Precedent Analysis feature into the frontend application.

## 1. Service Integration

First, add the precedent analysis service functions to your API client:

```typescript
// src/services/precedent.service.ts
import apiClient from './api.client';

/**
 * Analyze precedents in a document by document ID
 * @param documentId The ID of the document to analyze
 * @param options Analysis options
 * @returns Precedent analysis results
 */
export const analyzePrecedents = async (documentId: string, options = {}) => {
  const response = await apiClient.post(
    `/documents/precedents/${documentId}/analyze`,
    options,
  );
  return response.data;
};

/**
 * Types for precedent analysis
 */
export interface PrecedentAnalysisOptions {
  includeRecommendations?: boolean;
  maxRelatedCases?: number;
  minRelevanceScore?: number;
  categorize?: boolean;
  assessImpact?: boolean;
}

export interface RelatedCase {
  caseName: string;
  citation: string;
  court: string;
  year: number;
  relevance: number;
  relationship: string;
}

export interface PrecedentAnalysisResult {
  citation: string;
  relevanceScore: number;
  impact: 'positive' | 'negative' | 'neutral' | 'unknown';
  category: string;
  keyPoints: string[];
  recommendation: string;
  relatedCases: RelatedCase[];
}
```

## 2. React Component for Precedent Analysis

Create a component to display the precedent analysis results:

```typescript
// src/components/analysis/PrecedentAnalysis.tsx
import React, { useState, useEffect } from 'react';
import {
  analyzePrecedents,
  PrecedentAnalysisResult,
} from '../../services/precedent.service';
import {
  CircularProgress,
  Alert,
  Box,
  Typography,
  Paper,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
} from '@mui/material';

interface PrecedentAnalysisProps {
  documentId: string;
}

const PrecedentAnalysis: React.FC<PrecedentAnalysisProps> = ({
  documentId,
}) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [results, setResults] = useState<PrecedentAnalysisResult[]>([]);

  useEffect(() => {
    const fetchPrecedentAnalysis = async () => {
      try {
        setLoading(true);
        setError(null);

        const analysisResults = await analyzePrecedents(documentId, {
          includeRecommendations: true,
          categorize: true,
          assessImpact: true,
          maxRelatedCases: 5,
          minRelevanceScore: 0.3,
        });

        setResults(analysisResults);
      } catch (err) {
        console.error('Error analyzing precedents:', err);
        setError('Failed to analyze precedents. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (documentId) {
      fetchPrecedentAnalysis();
    }
  }, [documentId]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" my={4}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  if (!results.length) {
    return <Alert severity="info">No precedents found in this document.</Alert>;
  }

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Precedent Analysis
      </Typography>
      <Typography variant="body2" color="textSecondary" paragraph>
        Found {results.length} precedents in this document.
      </Typography>

      {results.map((precedent, index) => (
        <Paper key={index} sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            {precedent.citation}
          </Typography>

          <Box display="flex" gap={1} mb={2}>
            <Chip
              label={`Relevance: ${Math.round(
                precedent.relevanceScore * 100,
              )}%`}
              color={
                precedent.relevanceScore > 0.7
                  ? 'success'
                  : precedent.relevanceScore > 0.4
                  ? 'primary'
                  : 'default'
              }
            />
            <Chip
              label={`Impact: ${precedent.impact}`}
              color={
                precedent.impact === 'positive'
                  ? 'success'
                  : precedent.impact === 'negative'
                  ? 'error'
                  : precedent.impact === 'neutral'
                  ? 'default'
                  : 'warning'
              }
            />
            <Chip label={`Category: ${precedent.category}`} />
          </Box>

          <Typography variant="subtitle1">Key Points</Typography>
          <List dense>
            {precedent.keyPoints.map((point, i) => (
              <ListItem key={i}>
                <ListItemText primary={point} />
              </ListItem>
            ))}
          </List>

          {precedent.recommendation && (
            <>
              <Typography variant="subtitle1" mt={2}>
                Recommendation
              </Typography>
              <Typography variant="body2" paragraph>
                {precedent.recommendation}
              </Typography>
            </>
          )}

          {precedent.relatedCases && precedent.relatedCases.length > 0 && (
            <>
              <Divider sx={{ my: 2 }} />
              <Typography variant="subtitle1">Related Cases</Typography>
              <List dense>
                {precedent.relatedCases.map((relatedCase, i) => (
                  <ListItem key={i}>
                    <ListItemText
                      primary={`${relatedCase.caseName} (${relatedCase.citation})`}
                      secondary={`${relatedCase.court}, ${
                        relatedCase.year
                      } - Relevance: ${Math.round(
                        relatedCase.relevance * 100,
                      )}%, Relationship: ${relatedCase.relationship}`}
                    />
                  </ListItem>
                ))}
              </List>
            </>
          )}
        </Paper>
      ))}
    </Box>
  );
};

export default PrecedentAnalysis;
```

## 3. Integration into Document Viewer

Add the precedent analysis component to your document viewer page:

```typescript
// src/pages/DocumentViewer.tsx
import React from 'react';
import { useParams } from 'react-router-dom';
import { Box, Tabs, Tab, Typography } from '@mui/material';
import DocumentContent from '../components/documents/DocumentContent';
import PrecedentAnalysis from '../components/analysis/PrecedentAnalysis';
import CitationAnalysis from '../components/analysis/CitationAnalysis';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`document-tabpanel-${index}`}
      aria-labelledby={`document-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const DocumentViewer: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const location = useLocation();
  const [tabValue, setTabValue] = React.useState(() => {
    // Check if there's a tab parameter in the URL
    const params = new URLSearchParams(location.search);
    const tab = params.get('tab');
    if (tab === 'precedents') return 1;
    if (tab === 'citations') return 2;
    return 0;
  });

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  if (!id) {
    return <Typography>Document not found</Typography>;
  }

  return (
    <Box sx={{ width: '100%' }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          aria-label="document tabs"
        >
          <Tab label="Document" id="document-tab-0" />
          <Tab label="Precedent Analysis" id="document-tab-1" />
          <Tab label="Citation Analysis" id="document-tab-2" />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        <DocumentContent documentId={id} />
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <PrecedentAnalysis documentId={id} />
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <CitationAnalysis documentId={id} />
      </TabPanel>
    </Box>
  );
};

export default DocumentViewer;
```

## 4. Adding a Precedent Analysis Button to Document Cards

You can also add a button to document cards in your document list to quickly access precedent analysis:

```typescript
// src/components/documents/DocumentCard.tsx
import React from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Button,
  Chip,
  Box,
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { formatDate } from '../../utils/formatters';

interface DocumentCardProps {
  document: {
    id: string;
    filename: string;
    uploadDate: string;
    fileType: string;
    size: number;
    metadata?: {
      title?: string;
      pageCount?: number;
    };
  };
}

const DocumentCard: React.FC<DocumentCardProps> = ({ document }) => {
  const navigate = useNavigate();

  const viewDocument = () => {
    navigate(`/documents/${document.id}`);
  };

  const analyzePrecedents = () => {
    navigate(`/documents/${document.id}?tab=precedents`);
  };

  return (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardContent sx={{ flexGrow: 1 }}>
        <Typography variant="h6" component="div" noWrap>
          {document.metadata?.title || document.filename}
        </Typography>

        <Typography variant="body2" color="text.secondary">
          Uploaded: {formatDate(document.uploadDate)}
        </Typography>

        <Box mt={1} display="flex" gap={1}>
          <Chip size="small" label={document.fileType.toUpperCase()} />
          {document.metadata?.pageCount && (
            <Chip size="small" label={`${document.metadata.pageCount} pages`} />
          )}
        </Box>
      </CardContent>

      <CardActions>
        <Button size="small" onClick={viewDocument}>
          View
        </Button>
        <Button size="small" onClick={analyzePrecedents} color="primary">
          Analyze Precedents
        </Button>
      </CardActions>
    </Card>
  );
};

export default DocumentCard;
```

## 5. Handling the Tab Parameter in Document Viewer

Update the document viewer to handle the tab parameter from the URL:

```typescript
// src/pages/DocumentViewer.tsx (update)
const DocumentViewer: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const location = useLocation();
  const [tabValue, setTabValue] = React.useState(() => {
    // Check if there's a tab parameter in the URL
    const params = new URLSearchParams(location.search);
    const tab = params.get('tab');
    if (tab === 'precedents') return 1;
    if (tab === 'citations') return 2;
    return 0;
  });

  // Rest of the component remains the same
};
```

## 6. Error Handling and Loading States

Make sure to handle loading states and errors appropriately in your components. The `PrecedentAnalysis` component already includes this, but you should also handle API errors at the service level:

```typescript
// src/services/precedent.service.ts (update)
export const analyzePrecedents = async (documentId: string, options = {}) => {
  try {
    const response = await apiClient.post(
      `/documents/precedents/${documentId}/analyze`,
      options,
    );
    return response.data;
  } catch (error) {
    // Check for specific error types
    if (error.response?.status === 403) {
      throw new Error(
        'You do not have access to the precedent analysis feature. Please upgrade your subscription.',
      );
    } else if (error.response?.status === 404) {
      throw new Error(
        'Document not found. Please check the document ID and try again.',
      );
    } else {
      throw new Error('Failed to analyze precedents. Please try again later.');
    }
  }
};
```

## 7. Testing

Test the integration thoroughly to ensure it works as expected:

1. Test with documents that contain precedents
2. Test with documents that don't contain precedents
3. Test error handling (e.g., invalid document ID, network errors)
4. Test with different subscription tiers to ensure feature availability is correctly enforced

## 8. Conclusion

This integration provides a complete solution for analyzing precedents in legal documents. Users can access the precedent analysis feature from the document viewer or directly from document cards in the document list.

The precedent analysis component displays:

- Citation information
- Relevance score
- Impact assessment
- Category
- Key points
- Recommendations
- Related cases

This provides users with valuable insights into the precedents cited in their legal documents, helping them understand the legal context and implications.

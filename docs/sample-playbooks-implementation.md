# Sample Playbooks Implementation Documentation

## Overview

The Sample Playbooks feature provides two complementary systems that offer expert-crafted templates and guidance for legal document processing:

1. **Sample Negotiation Playbooks** - Strategic negotiation guidance and tactics
2. **Sample Contract Playbooks** - Rule-based compliance checking and analysis

Both systems use a **code-based architecture** stored in the codebase rather than the database, ensuring consistency across environments and version control through git deployments.

## Architecture

### Code-Based Implementation Benefits
- **Version Control**: All sample data tracked in git with deployment history
- **Consistency**: Same samples across all environments (dev, staging, production)
- **Performance**: No database queries for static content
- **Maintainability**: Easy updates through code deployments
- **Reliability**: No database seeding or migration dependencies

### System Components
- **Data Services**: Manage code-based sample data with filtering and search
- **Controllers**: REST API endpoints with authentication and feature guards
- **Clone Functionality**: Copy samples to user/organization-specific collections
- **Statistics**: Analytics and usage insights

## Sample Negotiation Playbooks

### Purpose
Strategic negotiation guidance providing tactics, scenarios, and educational content to help users negotiate contracts effectively.

### Key Features
- **8 Expert-Crafted Playbooks** covering common contract types
- **Progressive Learning Path** from beginner to expert difficulty
- **Negotiation Strategies** with specific tactics and recommendations
- **Simulation Scenarios** with triggers, responses, and outcomes
- **Risk Assessment** with leverage points and deal breakers
- **No Credit Consumption** - accessible to all subscription tiers

### Sample Playbook Structure
```json
{
  "id": "template-service-agreement-beginner",
  "name": "Service Agreement Negotiation (Beginner)",
  "contractType": "SERVICE_AGREEMENT",
  "difficulty": "beginner",
  "description": "Essential negotiation tactics for service agreements",
  "strategies": [
    {
      "category": "Payment Terms",
      "recommendations": [
        {
          "priority": "high",
          "strategy": "Negotiate favorable payment schedules",
          "riskLevel": "medium",
          "alternativeLanguage": ["Net 30 payment terms", "Milestone-based payments"]
        }
      ]
    }
  ],
  "simulationScenarios": [
    {
      "scenario": "Client requests extended payment terms",
      "trigger": "Payment schedule negotiation",
      "response": "Counter with early payment discounts",
      "expectedOutcome": "Balanced payment terms"
    }
  ],
  "overallAssessment": {
    "keyLeveragePoints": ["Service quality guarantees", "Intellectual property rights"],
    "dealBreakers": ["Unlimited liability", "Unreasonable termination clauses"],
    "strategicGuidance": "Focus on balanced risk allocation"
  }
}
```

### Available Sample Playbooks
1. **NDA (Beginner)** - Confidentiality agreement essentials
2. **Service Agreement (Beginner)** - Basic service contract tactics
3. **Consulting Agreement (Intermediate)** - Professional services negotiation
4. **Employment Contract (Intermediate)** - Salary and benefits negotiation
5. **Master Service Agreement (Intermediate)** - Enterprise framework agreements
6. **Vendor Agreement (Intermediate)** - Supply chain negotiations
7. **Software License (Expert)** - Complex enterprise licensing
8. **Partnership Agreement (Expert)** - Strategic alliances and joint ventures

## Sample Contract Playbooks

### Purpose
Rule-based compliance checking providing automated contract analysis rules, acceptable language patterns, and risk assessment criteria.

### Key Features
- **5 Expert-Crafted Playbooks** covering essential contract types
- **Compliance Rules** with detailed criteria and patterns
- **Risk Classification** from low to critical severity levels
- **Acceptable Language** with preferred, acceptable, and fallback options
- **Negotiation Guidance** with strategies and business impact analysis
- **No Credit Consumption** - accessible to all subscription tiers

### Sample Playbook Structure
```json
{
  "id": "sample-nda-playbook",
  "name": "Standard NDA Compliance Playbook",
  "contractType": "NDA",
  "description": "Comprehensive compliance rules for Non-Disclosure Agreements",
  "rules": [
    {
      "id": "nda-mutual-confidentiality",
      "name": "Mutual Confidentiality Requirement",
      "category": "Confidentiality",
      "ruleType": "required_clause",
      "severity": "HIGH",
      "criteria": {
        "keywords": ["mutual", "confidentiality", "both parties"],
        "patterns": ["mutual.*confidential", "both.*parties.*confidential"],
        "semanticConcepts": ["mutual confidentiality", "reciprocal protection"],
        "contextRequirements": ["confidentiality section"]
      },
      "acceptableLanguage": {
        "preferred": ["Both parties acknowledge that confidential information may be disclosed"],
        "acceptable": ["Each party may disclose confidential information"],
        "fallbackPositions": ["Confidential information disclosed by either party"]
      },
      "unacceptableTerms": {
        "prohibited": ["one-way confidentiality", "unilateral confidentiality"],
        "requiresEscalation": ["perpetual confidentiality"],
        "autoReject": ["no confidentiality obligations"]
      },
      "negotiationGuidance": {
        "strategy": "Insist on mutual confidentiality",
        "alternatives": ["Propose separate mutual NDA"],
        "businessImpact": "One-way NDAs create unbalanced risk exposure"
      }
    }
  ]
}
```

### Available Sample Playbooks
1. **NDA Compliance** - Mutual confidentiality and term limitations
2. **Service Agreement Compliance** - Liability caps and risk management
3. **Employment Contract Compliance** - Termination rights and labor law
4. **Software License Compliance** - IP protection and data privacy
5. **Purchase Agreement Compliance** - Governing law and jurisdiction

## API Endpoints

### Sample Negotiation Playbooks API

**Base URL**: `/api/sample-playbooks`

#### Get All Sample Negotiation Playbooks
```http
GET /api/sample-playbooks
```

**Query Parameters:**
- `contractType` (optional): Filter by contract type (NDA, SERVICE_AGREEMENT, etc.)
- `industry` (optional): Filter by industry
- `difficulty` (optional): Filter by difficulty (beginner, intermediate, expert)
- `tags` (optional): Filter by tags (comma-separated)

**Response:**
```json
[
  {
    "id": "template-service-agreement-beginner",
    "name": "Service Agreement Negotiation (Beginner)",
    "contractType": "SERVICE_AGREEMENT",
    "difficulty": "beginner",
    "strategies": [...],
    "simulationScenarios": [...],
    "overallAssessment": {...}
  }
]
```

#### Get Sample Playbook Statistics
```http
GET /api/sample-playbooks/stats/overview
```

**Response:**
```json
{
  "totalSamples": 8,
  "totalUserPlaybooks": 0,
  "contractTypeDistribution": [
    {"contractType": "NDA", "count": 1},
    {"contractType": "SERVICE_AGREEMENT", "count": 1}
  ]
}
```

#### Get Specific Sample Playbook
```http
GET /api/sample-playbooks/{playbookId}
```

**Response:**
```json
{
  "id": "template-service-agreement-beginner",
  "name": "Service Agreement Negotiation (Beginner)",
  "contractType": "SERVICE_AGREEMENT",
  "strategies": [...],
  "simulationScenarios": [...],
  "overallAssessment": {...}
}
```

#### Clone Sample Playbook to Document
```http
POST /api/sample-playbooks/{playbookId}/clone/{documentId}
```

**Response:**
```json
{
  "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
  "documentId": "document-uuid",
  "organizationId": "org-uuid",
  "name": "Service Agreement Negotiation (Beginner) - Copy",
  "isTemplate": false,
  "strategies": [...],
  "createdAt": "2025-06-04T08:00:00.000Z"
}
```

### Sample Contract Playbooks API

**Base URL**: `/api/sample-contract-playbooks`

#### Get All Sample Contract Playbooks
```http
GET /api/sample-contract-playbooks
```

**Query Parameters:**
- `contractType` (optional): Filter by contract type
- `industry` (optional): Filter by industry
- `riskProfile` (optional): Filter by risk profile (Low, Medium, High, Critical)
- `tags` (optional): Filter by tags (comma-separated)

**Response:**
```json
[
  {
    "id": "sample-nda-playbook",
    "name": "Standard NDA Compliance Playbook",
    "contractType": "NDA",
    "rules": [...],
    "metadata": {...}
  }
]
```

#### Get Contract Playbook Statistics
```http
GET /api/sample-contract-playbooks/stats/overview
```

**Response:**
```json
{
  "totalSamples": 5,
  "contractTypeDistribution": [
    {"contractType": "NDA", "count": 1}
  ],
  "riskProfileDistribution": [
    {"riskProfile": "Medium", "count": 3}
  ],
  "industryDistribution": [
    {"industry": "General", "count": 3}
  ]
}
```

#### Get Specific Sample Contract Playbook
```http
GET /api/sample-contract-playbooks/{playbookId}
```

**Response:**
```json
{
  "id": "sample-nda-playbook",
  "name": "Standard NDA Compliance Playbook",
  "contractType": "NDA",
  "rules": [...],
  "metadata": {...}
}
```

#### Clone Sample Contract Playbook
```http
POST /api/sample-contract-playbooks/{playbookId}/clone
```

**Response:**
```json
{
  "_id": "60f7b3b3b3b3b3b3b3b3b3b3",
  "id": "uuid-generated",
  "organizationId": "org-uuid",
  "name": "Standard NDA Compliance Playbook (Copy)",
  "isTemplate": false,
  "rules": [...],
  "metadata": {
    "clonedFrom": "sample-nda-playbook",
    "clonedAt": "2025-06-04T08:00:00.000Z"
  }
}
```

#### Get Available Contract Types
```http
GET /api/sample-contract-playbooks/contract-types/available
```

**Response:**
```json
["NDA", "SERVICE_AGREEMENT", "EMPLOYMENT_CONTRACT", "LICENSING_AGREEMENT", "PURCHASE_AGREEMENT"]
```

## Authentication & Authorization

All endpoints require:
- **JWT Authentication**: Valid Bearer token in Authorization header
- **Feature Access**: User must have access to respective features
  - `negotiation_playbooks` for negotiation playbook endpoints
  - `contract_playbooks` for contract playbook endpoints

## Error Responses

### Common Error Codes
- `401 Unauthorized`: Invalid or missing authentication token
- `403 Forbidden`: User lacks required feature access
- `404 Not Found`: Sample playbook not found
- `500 Internal Server Error`: Server-side error

### Error Response Format
```json
{
  "statusCode": 404,
  "message": "Sample playbook with ID 'invalid-id' not found",
  "error": "Not Found"
}
```

## Integration Notes

### Frontend Integration
- Use filtering parameters to create category-based browsing
- Implement search functionality using tags and keywords
- Show difficulty progression for negotiation playbooks
- Display risk levels and severity for contract playbooks

### Workflow Integration
- Clone samples as starting points for custom playbooks
- Use contract playbooks for automated compliance checking
- Integrate negotiation playbooks with document-specific workflows
- Track usage analytics through statistics endpoints

### Performance Considerations
- Sample data is cached in memory for fast access
- No database queries required for browsing samples
- Clone operations involve database writes for user-specific copies
- Statistics are computed in real-time from sample data

## Future Enhancements

### Planned Features
- **Content Expansion**: Additional sample playbooks for specialized contract types
- **Customization**: User-specific modifications to cloned playbooks
- **Analytics**: Usage tracking and recommendation engine
- **Integration**: Direct integration with contract analysis workflows
- **Collaboration**: Sharing and commenting on cloned playbooks

### Extensibility
- **Modular Architecture**: Easy addition of new sample playbooks
- **Plugin System**: Support for industry-specific playbook extensions
- **API Versioning**: Backward compatibility for future enhancements
- **Internationalization**: Support for multiple jurisdictions and languages

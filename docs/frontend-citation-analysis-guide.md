# Frontend Integration Guide: Enhanced Citation Analysis

This document outlines how the frontend application should interact with the backend API for the Enhanced Citation Analysis feature.

## 1. Overview

The backend provides endpoints to analyze individual citations or all citations within a document. The level of detail returned (basic relationships vs. enhanced analysis including impact and precedent chains) depends on the user's subscription tier.

## 2. Authentication

All citation analysis endpoints require authentication. Requests must include a valid JWT in the `Authorization` header:

```
Authorization: Bearer <your_jwt_token>
```

Failure to provide a valid token will result in a `401 Unauthorized` response.

## 3. Tenant Context

The backend uses tenant context derived from the JWT payload (`organizationId`). Ensure the JWT includes the correct `organizationId`. No separate header is needed as the middleware handles context extraction from the token.

## 4. Available Endpoints

The primary endpoints automatically adjust the analysis level based on the user's subscription tier.

### 4.1 Analyze Single Citation

* **Path:** `/api/legal-research/citations/analyze`
* **Method:** `POST`
* **Required Feature:** `basic_citation_analysis` (automatically checked by the backend guard)
* **Request Body:** `AnalyzeCitationDto`
    ```json
    {
      "citation": "string (required) - The citation text to analyze (e.g., '410 U.S. 113')",
      "includeRelationships": "boolean (optional) - Defaults determined by tier",
      "includePrecedentChains": "boolean (optional) - Defaults determined by tier; requires enhanced feature",
      "includeImpact": "boolean (optional) - Defaults determined by tier; requires enhanced feature",
      "maxRelationshipDepth": "number (optional) - Defaults determined by tier",
      "maxPrecedentChainLength": "number (optional) - Defaults determined by tier; requires enhanced feature"
    }
    ```
* **Response:** `CitationAnalysisResponse` (or `null` if citation not found/analyzable)
    ```json
    {
      "citation": {
        "id": "string",
        "citation": "string",
        "type": "string ('case', 'statute', etc.)",
        "title": "string (optional)",
        "court": "string (optional)",
        "year": "number (optional)",
        "jurisdiction": "string (optional)",
        "courtListenerId": "string (optional)"
        // ... other metadata
      },
      "relationships": [
        {
          "id": "string",
          "sourceCitationId": "string",
          "targetCitationId": "string",
          "relationshipType": "string ('cites', 'citedBy', etc.)",
          "strength": "string ('weak', 'moderate', 'strong')",
          "metadata": {
            // For 'cites' relationships
            "citedCase": {
              "name": "string",
              "citation": "string",
              "court": "string",
              "year": "number (optional)",
              "jurisdiction": "string"
            },
            "relationshipDescription": "string (e.g., 'This case cites strongly')",
            "citedCaseUrl": "string (URL to CourtListener)"
            
            // For 'citedBy' relationships
            "citingCase": {
              "name": "string",
              "citation": "string",
              "court": "string",
              "year": "number (optional)",
              "jurisdiction": "string"
            },
            "relationshipDescription": "string (e.g., 'This case is cited moderately')",
            "citingCaseUrl": "string (URL to CourtListener)"
          }
        }
      ],
      "impact": {
        "citationId": "string",
        "totalCitations": "number",
        "recentCitations": "number",
        "influentialCitations": "number",
        "negativeReferences": "number",
        "positiveReferences": "number",
        "impactScore": "number (0-100)"
      },
      "precedentChains": [
        {
          "id": "string",
          "rootCitationId": "string",
          "chain": [
            {
              "citationId": "string",
              "relationshipToParent": "string ('follows', 'distinguishes', etc.)",
              "significance": "string ('low', 'medium', 'high')"
            }
          ]
        }
      ]
    }
    ```
* **Notes:** 
    - The backend service (`analyzeCitationBySubscription`) determines the analysis depth (basic vs. enhanced) based on the subscription tied to the `organizationId` in the JWT.
    - The `metadata` field in relationships provides user-friendly information for frontend display, eliminating the need for additional processing.
    - The `impactScore` is normalized on a scale of 0-100, making it suitable for visualization.

### 4.2 Analyze Document Citations

* **Path:** `/api/legal-research/citations/analyze-document`
* **Method:** `POST`
* **Required Feature:** `basic_citation_analysis`
* **Request Body:** `AnalyzeDocumentCitationsDto`
    ```json
    {
      "documentText": "string (required) - The full text of the document containing citations",
      // Optional analysis flags similar to AnalyzeCitationDto
      "includeRelationships": "boolean (optional)",
      "includePrecedentChains": "boolean (optional)",
      "includeImpact": "boolean (optional)",
      "maxRelationshipDepth": "number (optional)",
      "maxPrecedentChainLength": "number (optional)"
    }
    ```
* **Response:** `CitationAnalysisResponse[]` (Array of analysis results for each detected citation)
* **Notes:** Similar to the single citation endpoint, the backend determines the analysis level per citation based on the subscription.

### 4.3 Specific Analysis Endpoints (Optional)

While the auto-detect endpoints are recommended, specific endpoints exist for forcing basic or enhanced analysis if needed, guarded by respective features:

* `POST /api/legal-research/citations/analyze/basic` (Requires `basic_citation_analysis`)
* `POST /api/legal-research/citations/analyze/enhanced` (Requires `enhanced_citation_analysis`)
* `POST /api/legal-research/citations/analyze-document/basic` (Requires `basic_citation_analysis`)
* `POST /api/legal-research/citations/analyze-document/enhanced` (Requires `enhanced_citation_analysis`)

### 4.4 Specific Data Retrieval Endpoints

These endpoints retrieve specific parts of the analysis and require relevant features:

* `GET /api/legal-research/citations/relationships/:citation?depth=<number>`
    * Requires: `basic_citation_analysis`
    * Returns: `CitationAnalysisResponse` containing only the `citation` and `relationships` fields.
* `GET /api/legal-research/citations/impact/:citation`
    * Requires: `enhanced_citation_analysis`
    * Returns: `CitationAnalysisResponse` containing only the `citation` and `impact` fields.
* `GET /api/legal-research/citations/precedent/:citation?chainLength=<number>`
    * Requires: `enhanced_citation_analysis`
    * Returns: `CitationAnalysisResponse` containing only the `citation` and `precedentChains` fields.
* `GET /api/legal-research/citations/network/:citation?depth=<number>`
    * Requires: `enhanced_citation_analysis`
    * Returns: Network graph data (nodes and links) for visualization.

## 5. Example Usage (curl)

```bash
# Analyze a single citation (auto-detects tier)
curl -X POST http://localhost:4000/api/legal-research/citations/analyze \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your_jwt_token>" \
  -d '{
    "citation": "5 U.S. 137"
  }'

# Analyze citations in a document
curl -X POST http://localhost:4000/api/legal-research/citations/analyze-document \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your_jwt_token>" \
  -d '{
    "documentText": "The landmark case Marbury v. Madison, 5 U.S. 137 (1803), established..."
  }'
```

## 6. Handling Responses and Errors

* **Success (200 OK / 201 Created):** The response body will contain the `CitationAnalysisResponse` or an array thereof.
* **Bad Request (400 Bad Request):** Typically due to validation errors (e.g., missing `citation` field). The response body will contain details.
    ```json
    {
      "statusCode": 400,
      "message": "Validation failed",
      "error": "Bad Request",
      "details": [
        {
          "field": "citation",
          "value": null,
          "constraints": [ "citation should not be empty", "citation must be a string" ]
        }
      ]
      // ...
    }
    ```
* **Unauthorized (401 Unauthorized):** Invalid or missing JWT.
* **Forbidden (403 Forbidden):** The user's subscription tier does not grant access to the required feature (e.g., trying to access an enhanced feature on a FREE plan).
    ```json
    {
      "statusCode": 403,
      "message": "Feature 'enhanced_citation_analysis' is not available in your current subscription plan",
      "error": "Forbidden"
      // ...
    }
    ```
* **Server Error (5xx):** Indicates an issue on the backend.

The frontend should handle these responses appropriately, displaying informative messages to the user, especially regarding feature limitations based on their subscription.

## 7. Frontend Visualization Guidelines

### 7.1 Citation Impact Visualization

The `impact` object provides several metrics that can be visualized:

* **Impact Score**: Use a gauge or meter visualization to show the normalized score (0-100)
* **Citation Distribution**: Create a pie chart showing the distribution of total, recent, and influential citations
* **Positive/Negative References**: Display a horizontal bar chart comparing positive vs. negative references

### 7.2 Relationship Visualization

The `relationships` array contains rich metadata that can be used to create interactive visualizations:

* **Citation Network**: Create a force-directed graph where:
  * Nodes represent cases (the analyzed case and related cases)
  * Edges represent relationships (cites/citedBy)
  * Edge thickness can represent relationship strength
  * Node size can represent the case's importance
  
* **Relationship List**: Display a sortable, filterable list of relationships with:
  * Case name and citation
  * Court and jurisdiction
  * Relationship description
  * Link to the full case on CourtListener

### 7.3 Precedent Chain Visualization

The `precedentChains` array can be visualized as:

* **Tree Diagram**: Show the analyzed case as the root, with cited cases as branches
* **Timeline**: Display cases in chronological order to show the evolution of legal precedent
* **Hierarchical List**: Group cases by significance (high/medium/low) for easy navigation

## 8. Caching Recommendations

To improve performance, consider implementing frontend caching for citation analysis results:

* Cache analysis results by citation string
* Set a reasonable TTL (Time To Live) for cached data (e.g., 24 hours)
* Implement a cache invalidation strategy for when users request fresh data
* Consider using IndexedDB for persistent caching between sessions

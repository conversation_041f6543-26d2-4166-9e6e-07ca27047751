# Email Domain Blocking

This document explains the email domain blocking feature implemented in the Legal Document Analyzer to prevent abuse from temporary email and forwarding services.

## Overview

The Legal Document Analyzer now blocks email addresses from known forwarding services and temporary email providers during registration and login. This security measure helps prevent abuse, spam accounts, and ensures that users register with legitimate email addresses.

## Implementation Details

### EmailValidationService

The core of this feature is the `EmailValidationService`, which:

1. Maintains a list of known email forwarding and temporary email domains
2. Provides methods to check if an email is from a forwarding service
3. Supports custom blocked domains via configuration
4. Optionally supports an allowlist mode for restricting registration to specific domains

### Integration Points

The email validation is integrated at the following points:

1. **User Registration**: Prevents creation of accounts with blocked email domains
2. **User Login**: Prevents login attempts from blocked email domains
3. **Google OAuth Authentication**: Prevents Google authentication with blocked email domains

### Configuration Options

The following environment variables can be used to configure the email domain blocking:

| Variable | Description | Default |
|----------|-------------|---------|
| `EMAIL_BLOCKED_DOMAINS` | Comma-separated list of additional domains to block | `""` (empty) |
| `EMAIL_ALLOWED_DOMAINS` | Comma-separated list of allowed domains (when using allowlist mode) | `""` (empty) |
| `EMAIL_USE_ALLOWLIST` | Whether to use allowlist mode (only allow specific domains) | `false` |

## Domain Lists

### Built-in Blocked Domains

The system includes a comprehensive list of known email forwarding and temporary email domains in the file `data/forwarding-domains.txt`. This list is loaded at application startup and used to validate email addresses.

### Custom Blocked Domains

You can add additional domains to block by setting the `EMAIL_BLOCKED_DOMAINS` environment variable. For example:

```
EMAIL_BLOCKED_DOMAINS=example.com,suspicious-domain.com,another-domain.net
```

### Allowlist Mode

For more restrictive environments, you can enable allowlist mode, which only allows registrations from specific domains:

```
EMAIL_USE_ALLOWLIST=true
EMAIL_ALLOWED_DOMAINS=company.com,partner.org,trusted-domain.net
```

## Error Messages

When a user attempts to register or login with a blocked email domain, they will receive one of the following error messages:

- "Registration not allowed: Email forwarding services are not allowed"
- "Registration not allowed: Email domain is blocked"
- "Registration not allowed: Email domain not in the allowed domains list" (when using allowlist mode)

## Security Considerations

1. **Regular Updates**: The list of forwarding domains should be regularly updated as new services emerge.
2. **False Positives**: Be aware that some legitimate email services might be incorrectly blocked. Monitor user feedback.
3. **Circumvention**: Sophisticated users may still find ways to bypass this protection. This is a first line of defense.

## Multi-Tenant Considerations

In a multi-tenant environment like the Legal Document Analyzer:

1. The email domain blocking applies across all tenants for security reasons
2. Organization administrators cannot override these security settings
3. For organizations that need to use specific email domains, contact the system administrator to update the configuration

## Future Enhancements

Potential future enhancements to the email domain blocking feature:

1. **API for Domain Management**: Admin API to manage blocked/allowed domains
2. **Per-Organization Settings**: Allow organization-specific domain restrictions
3. **Pattern Matching**: Support for wildcard patterns in domain lists
4. **Reputation Scoring**: Implement a reputation-based system instead of binary allow/block
5. **Integration with External Services**: Connect to external email validation services

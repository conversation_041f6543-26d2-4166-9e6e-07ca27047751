# Deposition Endpoints Documentation

This document describes the available endpoints for deposition preparation and analysis, including request payloads, responses, and feature descriptions.

## 1. Create Deposition Preparation

**Endpoint**: `POST /api/depositions`
**Description**: Create a new deposition preparation.

**Request Payload** (CreateDepositionPreparationDto):

```json
{
  "title": "string",
  "description": "string (optional)",
  "targetWitnesses": ["string"],
  "caseContext": "string",
  "keyIssues": ["string"],
  "relatedDocumentIds": ["string"]
}
```

**Response** (DepositionPreparation):

```json
{
  "id": "uuid",
  "organizationId": "uuid",
  "userId": "uuid",
  "caseId": "uuid",
  "title": "string",
  "description": "string",
  "targetWitnesses": ["string"],
  "caseContext": "string",
  "keyIssues": ["string"],
  "relatedDocumentIds": ["string"],
  "questions": [],
  "status": "DRAFT",
  "createdAt": "ISODate",
  "updatedAt": "ISODate"
}
```

---

## 2. List All Deposition Preparations

**Endpoint**: `GET /api/depositions`
**Description**: Retrieve all deposition preparations for the authenticated user.

**Response**:

```json
[DepositionPreparation]
```

---

## 3. Get Specific Deposition Preparation

**Endpoint**: `GET /api/depositions/:id`
**Description**: Retrieve a single deposition preparation by its UUID.

**Response**:

```json
DepositionPreparation
```

---

## 4. Update Deposition Preparation

**Endpoint**: `PUT /api/depositions/:id`
**Description**: Update title, description, witnesses, context, issues, related documents, or status.

**Request Payload** (UpdateDepositionPreparationDto):

```json
{
  "title": "string (optional)",
  "description": "string (optional)",
  "targetWitnesses": ["string"] (optional),
  "caseContext": "string (optional)",
  "keyIssues": ["string"] (optional),
  "relatedDocumentIds": ["string"] (optional),
  "status": "DRAFT|IN_PROGRESS|COMPLETED" (optional)
}
```

**Response**:

```json
DepositionPreparation
```

---

## 5. Delete Deposition Preparation

**Endpoint**: `DELETE /api/depositions/:id`
**Description**: Delete a deposition preparation by its UUID.

**Response**: `204 No Content`

---

## 6. Add Question to Deposition

**Endpoint**: `POST /api/depositions/:id/questions`
**Description**: Add a new question to a deposition preparation.

**Request Payload** (CreateDepositionQuestionDto):

```json
{
  "text": "string",
  "category": "GENERAL|CREDIBILITY|CONSISTENCY|DOCUMENTATION",
  "purpose": "string",
  "targetWitness": "string (optional)",
  "priority": "high|medium|low",
  "notes": "string (optional)",
  "suggestedFollowUps": ["string"] (optional),
  "relatedDocuments": ["string"] (optional)
}
```

**Response** (DepositionQuestion):

```json
{
  "id": "uuid",
  "text": "string",
  "category": "string",
  "purpose": "string",
  "targetWitness": "string",
  "priority": "string",
  "notes": "string",
  "suggestedFollowUps": ["string"],
  "relatedDocuments": ["string"],
  "createdAt": "ISODate",
  "updatedAt": "ISODate"
}
```

---

## 7. Update Question

**Endpoint**: `PUT /api/depositions/:id/questions/:questionId`
**Description**: Update text, priority, or notes of an existing question.

**Request Payload** (UpdateDepositionQuestionDto):

```json
{
  "text": "string (optional)",
  "purpose": "string (optional)",
  "priority": "high|medium|low (optional)",
  "notes": "string (optional)"
}
```

**Response**:

```json
DepositionQuestion
```

---

## 8. Delete Question

**Endpoint**: `DELETE /api/depositions/:id/questions/:questionId`
**Description**: Remove a question from a deposition preparation.

**Response**: `204 No Content`

---

## 9. Generate Questions Using AI (Global)

**Endpoint**: `POST /api/depositions/generate-questions`
**Description**: AI-powered generation of deposition questions based on context and issues.

**Request Payload** (GenerateQuestionsDto):

```json
{
  "caseContext": "string",
  "keyIssues": ["string"],
  "targetWitnesses": ["string"],
  "focusAreas": ["string"] (optional),
  "questionCount": number (optional, default: 5),
  "questionCategories": ["GENERAL", "CREDIBILITY", "CONSISTENCY", "DOCUMENTATION"] (optional),
  "includeFollowUps": boolean (optional, default: true),
  "priorityLevel": "high|medium|low|all" (optional, default: "all")
}
```

**Response** (QuestionGenerationResult):

```json
{
  "questions": [DepositionQuestion],
  "metadata": {
    "generatedAt": "ISODate",
    "generationDurationMs": number,
    "modelUsed": string
  }
}
```

---

## 10. Generate Questions for Specific Deposition

### Endpoint

`POST /api/depositions/:id/generate-questions`

### Description

AI-powered generation of questions for an existing deposition preparation, combining saved context.

### Request Payload

Same as above, with optional overrides.

### Response

```json
QuestionGenerationResult
```

## 11. Get Deposition Analyses

### Endpoint

`GET /api/depositions/:id/analyses`

### Description

Retrieve all analyses generated for a deposition. Returns an empty array if no analyses exist.

#### Path Parameters

- `id`: The ID of the deposition to retrieve analyses for

#### Response

```json
[
  {
    "id": "uuid",
    "depositionId": "uuid",
    "transcript": "string",
    "caseContext": "string",
    "focusAreas": ["string"],
    "overallCredibilityScore": 0.8,
    "keyTestimonyAnalysis": [
      {
        "witness": "string",
        "testimony": "string",
        "credibilityScore": 0.8,
        "confidence": 0.9,
        "keyPoints": ["string"]
      }
    ],
    "crossExaminationSuggestions": [
      {
        "witness": "string",
        "topic": "string",
        "suggestedQuestions": ["string"],
        "suggestedFollowUps": ["string"],
        "rationale": "string",
        "estimatedImpact": "HIGH|MEDIUM|LOW"
      }
    ],
    "inconsistencies": [
      {
        "witness": "string",
        "statement1": "string",
        "statement2": "string",
        "context": "string",
        "severity": "HIGH|MEDIUM|LOW"
      }
    ],
    "keyFindings": ["string"],
    "potentialImpeachmentOpportunities": [],
    "timelineAnalysis": [],
    "metadata": {
      "modelUsed": "string",
      "confidence": "string",
      "analyzedAt": "ISODate",
      "analysisDurationMs": 0
    },
    "userId": "uuid",
    "organizationId": "uuid",
    "caseId": "uuid"
  }
]
```

#### Error Responses

- `401 Unauthorized`: If the user is not authenticated

## 12. Analyze Deposition Transcript

### Endpoint

`POST /api/depositions/analyze-transcript`

### Description

AI-powered analysis of a deposition transcript to identify key insights, including credibility assessment, inconsistencies, and cross-examination suggestions.

### Request Payload (AnalyzeDepositionDto)

```json
{
  "transcript": "Full text of the deposition transcript",
  "caseContext": "Background information about the case",
  "focusAreas": ["credibility", "inconsistencies", "expert testimony"],
  "depositionPreparationId": "optional-uuid"
}
```

#### Request Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| transcript | string | Yes | The full text content of the deposition transcript |
| caseContext | string | Yes | Background information about the case |
| focusAreas | string[] | Yes | Areas of focus for the analysis (e.g., ["credibility", "inconsistencies"]) |
| depositionPreparationId | string | No | Optional ID of the associated deposition preparation |

### Response (DepositionAnalysisResult)

```json
{
  "id": "uuid",
  "depositionId": "uuid",
  "overallCredibilityScore": 0.85,
  "keyTestimonyAnalysis": [
    {
      "speaker": "string",
      "statement": "string",
      "credibilityScore": 0.9,
      "confidence": "high",
      "reasoning": "string",
      "supportingEvidence": ["string"]
    }
  ],
  "crossExaminationSuggestions": [
    {
      "topic": "string",
      "question": "string",
      "purpose": "string",
      "legalBasis": "string"
    }
  ],
  "potentialImpeachmentOpportunities": [
    {
      "statement": "string",
      "conflictingEvidence": "string",
      "suggestedApproach": "string"
    }
  ],
  "timelineAnalysis": [
    {
      "event": "string",
      "timestamp": "string",
      "relevance": 0.9,
      "notes": "string"
    }
  ],
  "metadata": {
    "analyzedAt": "2025-05-22T05:29:15.000Z",
    "analysisDurationMs": 1500,
    "confidence": "high"
  }
}
```

### Error Responses

* `400 Bad Request`: If the transcript is empty or invalid
* `401 Unauthorized`: If the user is not authenticated
* `404 Not Found`: If the referenced deposition preparation ID doesn't exist
* `500 Internal Server Error`: If there was an error processing the analysis

## Question Category Mapping Feature

The API provides a flexible category mapping system that automatically normalizes different input formats while maintaining consistent internal categorization.

### Standard Categories

All inputs are mapped to these six standard categories:

- **GENERAL**: Background and basic witness information
- **CREDIBILITY**: Fact-finding and witness reliability assessment
- **CONSISTENCY**: Timeline and sequence verification
- **DOCUMENTATION**: Document and evidence review
- **EXPERT_QUALIFICATION**: Expert witness qualification and competency assessment
- **IMPEACHMENT**: Credibility challenges and prior inconsistency examination

### Input Format Examples

#### GENERAL Category

- Basic forms: "background", "general-info", "basic-info"
- Witness specific: "witness-background", "background-witness"
- Variations: "basic_background", "general-questions"

#### CREDIBILITY Category

- Fact related: "fact-finding", "facts-and-evidence"
- Witness related: "witness-reliability", "credibility-check"
- Evidence related: "evidence-gathering", "reliability-assessment"

#### CONSISTENCY Category

- Time related: "timeline", "chronology", "timeline-review"
- Sequence related: "sequence-of-events", "event-sequence"
- Pattern related: "consistency-check", "pattern-analysis"

#### DOCUMENTATION Category

- Document focused: "document-review", "documentation"
- Record types: "records-review", "record-analysis"
- Evidence types: "evidence-review", "exhibits"

### Input Processing Features

- Case-insensitive input handling
- Multiple separator types (spaces/hyphens/underscores)
- Multiple consecutive separator handling
- Pattern recognition for similar terms
- Smart category mapping
- Standardized output format

### Expert Qualification Category Variations

- "expert-qualification"
- "expert", "expertise"
- "qualifications"
- "expert-background"

### Impeachment Category Variations

- "impeachment"
- "credibility-challenge"
- "prior-inconsistency"
- "contradiction"
- "bias"

The system normalizes all inputs and maps them to the appropriate standard category while maintaining flexibility in input formats.

---

**Response** (DepositionAnalysisResult):

```json
{
  "id": "uuid",
  "overallCredibilityScore": number,
  "keyTestimonyAnalysis": [
    { speaker, statement, credibilityScore, confidence, reasoning, supportingEvidence }
  ],
  "inconsistencies": { [speaker]: [statements] },
  "crossExaminationSuggestions": [
    { topic, question, purpose, legalBasis, suggestedFollowUps }
  ],
  "keyFindings": ["string"],
  "metadata": { analyzedAt, analysisDurationMs, modelUsed, confidence }
}
```

---

## Sample Responses

### Create Deposition Preparation

```json
{
  "id": "2ea86b46-dd6f-4b9a-977d-618b2c40f8d1",
  "organizationId": "11111111-**************-************",
  "userId": "*************-7777-6666-************",
  "caseId": "aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee",
  "title": "Contract Signing",
  "description": "Discussion about section 5.3 of the contract.",
  "targetWitnesses": ["John Smith"],
  "caseContext": "Contract dispute over delivery timelines.",
  "keyIssues": ["Delivery timeline", "Duty to read"],
  "relatedDocumentIds": ["doc12345"],
  "questions": [],
  "status": "DRAFT",
  "createdAt": "2025-05-17T07:00:00.000Z",
  "updatedAt": "2025-05-17T07:00:00.000Z"
}
```

### List All Deposition Preparations

```json
[
  {
    "id": "2ea86b46-dd6f-4b9a-977d-618b2c40f8d1",
    "title": "Contract Signing",
    "status": "DRAFT",
    "createdAt": "2025-05-17T07:00:00.000Z",
    "updatedAt": "2025-05-17T07:00:00.000Z"
  }
]
```

### Get Specific Deposition Preparation

```json
{
  "id": "2ea86b46-dd6f-4b9a-977d-618b2c40f8d1",
  "title": "Contract Signing",
  "description": "Discussion about section 5.3 of the contract.",
  "questions": [],
  "status": "DRAFT"
}
```

### Update Deposition Preparation

```json
{
  "id": "2ea86b46-dd6f-4b9a-977d-618b2c40f8d1",
  "status": "IN_PROGRESS",
  "updatedAt": "2025-05-17T07:05:00.000Z"
}
```

### Delete Deposition Preparation

> 204 No Content

### Add Question

```json
{
  "id": "q1234567-89ab-cdef-0123-456789abcdef",
  "text": "Can you confirm your role in the project?",
  "category": "GENERAL",
  "purpose": "Establish witness identity",
  "targetWitness": "John Smith",
  "priority": "medium",
  "notes": "",
  "suggestedFollowUps": [],
  "relatedDocuments": [],
  "createdAt": "2025-05-17T07:10:00.000Z",
  "updatedAt": "2025-05-17T07:10:00.000Z"
}
```

### Generate Questions (Global)

```json
{
  "questions": [
    {
      "id": "e9d440ca-fe6c-439c-9f25-f5b7f2494be5",
      "text": "Please describe your role and responsibilities as a security guard/janitor at the building.",
      "category": "GENERAL",
      "purpose": "Establish witness background and credibility",
      "priority": "high",
      "targetWitness": "Security Guard",
      "suggestedFollowUps": [
        "What shift were you working that night?",
        "How long have you been employed in this role at this location?"
      ],
      "createdAt": "2025-05-18T17:42:48.776Z",
      "updatedAt": "2025-05-18T17:42:48.776Z",
      "relatedDocuments": [],
      "notes": "",
      "isFollowUp": false
    }
  ],
  "metadata": {
    "generatedAt": "2025-05-18T17:42:48.776Z",
    "generationDurationMs": 11043,
    "modelUsed": "gemini-2.5-flash-preview:thinking"
  }
}
```

### Analyze Deposition Transcript

```json
{
  "id": "ae123456-7890-1234-5678-abcdefabcdef",
  "overallCredibilityScore": 0.6,
  "keyTestimonyAnalysis": [
    {
      "speaker": "John Smith",
      "statement": "Yes, I did sign it.",
      "credibilityScore": 0.9,
      "confidence": "high",
      "reasoning": "Direct affirmation.",
      "supportingEvidence": []
    }
  ],
  "inconsistencies": {},
  "crossExaminationSuggestions": [
    {
      "topic": "Duty to Read",
      "question": "Did you fully read the contract before signing?",
      "purpose": "Test awareness of terms",
      "legalBasis": "Duty to read",
      "suggestedFollowUps": []
    }
  ],
  "keyFindings": ["Witness admits skimming the contract."],
  "metadata": {
    "analyzedAt": "2025-05-17T07:01:03.759Z",
    "analysisDurationMs": 7965,
    "modelUsed": "gpt-4",
    "confidence": "medium"
  }
}
```

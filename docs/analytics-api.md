# Analytics API Documentation

This document provides comprehensive information about the analytics endpoints available in the Legal Document Analyzer API. These endpoints can be used to build the analytics dashboard visualization layer as outlined in Phase 1 of the development roadmap.

## Base URL

All endpoints are relative to the base URL: `http://localhost:4000/api`

## Authentication

All analytics endpoints require authentication. Include the JWT token in the Authorization header:

```http
Authorization: Bearer <your_jwt_token>
```

## Common Query Parameters

The following query parameters are supported by most analytics endpoints:

| Parameter              | Type            | Description                                     | Default      |
| ---------------------- | --------------- | ----------------------------------------------- | ------------ |
| startDate              | ISO date string | Start date for the analytics time range         | 30 days ago  |
| endDate                | ISO date string | End date for the analytics time range           | Current date |
| granularity            | string          | Time series data granularity (day, week, month) | day          |
| includeTimeSeriesData  | boolean         | Whether to include time series data             | true         |
| includeCitationMetrics | boolean         | Whether to include citation metrics             | true         |
| includeTopicAnalysis   | boolean         | Whether to include topic analysis               | true         |

> **Note on Boolean Parameters**: When passing boolean parameters in the URL query string, use `true` or `false` as string values. The API will automatically convert these string values to actual boolean values.

## Endpoints

### 1. Organization Analytics

Get comprehensive analytics data for the entire organization.

**Endpoint:** `GET /analytics/organization`

**Query Parameters:** All common query parameters

**Response:**

```json
{
  "organizationId": "string",
  "timeRange": {
    "start": "2025-03-18T00:00:00.000Z",
    "end": "2025-04-18T00:00:00.000Z"
  },
  "documentAnalysis": {
    "totalDocuments": 42,
    "analyzedDocuments": 35,
    "comparedDocuments": 18,
    "documentsByType": [
      {
        "type": "contract",
        "count": 15
      },
      {
        "type": "brief",
        "count": 10
      }
    ],
    "averageAnalysisTime": 120,
    "classificationMetrics": {
      "automaticClassifications": 28,
      "manualClassifications": 7,
      "unclassifiedDocuments": 7,
      "classificationAccuracy": 0.92
    },
    "comparisonMetrics": {
      "totalComparisons": 18,
      "basicComparisons": 12,
      "enhancedComparisons": 6,
      "comparisonsByType": [
        {
          "type": "similarities",
          "count": 5
        },
        {
          "type": "differences",
          "count": 8
        },
        {
          "type": "both",
          "count": 5
        }
      ]
    }
  },
  "userEngagement": {
    "totalUsers": 8,
    "activeUsers": 6,
    "totalQueries": 156,
    "averageQueriesPerUser": 26,
    "averageResponseTime": 1.2
  },
  "topicAnalysis": {
    "topTopics": [
      {
        "topic": "contract terms",
        "count": 35
      },
      {
        "topic": "liability clauses",
        "count": 28
      }
    ],
    "topicTrends": [
      {
        "date": "2025-04-01T00:00:00.000Z",
        "topics": [
          {
            "topic": "contract terms",
            "count": 12
          }
        ]
      }
    ]
  },
  "citationMetrics": {
    "totalCitations": 87,
    "topCitedCases": [
      {
        "caseName": "Smith v. Jones",
        "count": 12
      }
    ],
    "citationsByJurisdiction": [
      {
        "jurisdiction": "Federal",
        "count": 45
      }
    ]
  },
  "timeSeriesData": {
    "documentUploads": [
      {
        "date": "2025-04-01T00:00:00.000Z",
        "count": 5
      }
    ],
    "documentAnalyses": [
      {
        "date": "2025-04-01T00:00:00.000Z",
        "count": 4
      }
    ],
    "documentComparisons": [
      {
        "date": "2025-04-01T00:00:00.000Z",
        "count": 2,
        "basic": 1,
        "enhanced": 1
      }
    ],
    "documentClassifications": [
      {
        "date": "2025-04-01T00:00:00.000Z",
        "count": 3,
        "automatic": 2,
        "manual": 1
      }
    ],
    "userQueries": [
      {
        "date": "2025-04-01T00:00:00.000Z",
        "count": 15
      }
    ],
    "averageResponseTimes": [
      {
        "date": "2025-04-01T00:00:00.000Z",
        "value": 1.3
      }
    ]
  },
  "lastUpdated": "2025-04-18T10:35:21.031Z"
}
```

### 2. Dashboard Analytics

Get analytics data specifically formatted for the dashboard. This is currently an alias for the organization analytics endpoint but may be customized in the future.

**Endpoint:** `GET /analytics/dashboard`

**Query Parameters:** All common query parameters

**Response:** Same as organization analytics

### 3. Document Analytics

Get analytics data focused on document metrics.

**Endpoint:** `GET /analytics/documents`

**Query Parameters:** All common query parameters

**Response:**

```json
{
  "organizationId": "string",
  "timeRange": {
    "start": "2025-03-18T00:00:00.000Z",
    "end": "2025-04-18T00:00:00.000Z"
  },
  "documentAnalysis": {
    "totalDocuments": 42,
    "analyzedDocuments": 35,
    "comparedDocuments": 18,
    "documentsByType": [
      {
        "type": "contract",
        "count": 15
      }
    ],
    "averageAnalysisTime": 120,
    "classificationMetrics": {
      "automaticClassifications": 28,
      "manualClassifications": 7,
      "unclassifiedDocuments": 7,
      "classificationAccuracy": 0.92
    },
    "comparisonMetrics": {
      "totalComparisons": 18,
      "basicComparisons": 12,
      "enhancedComparisons": 6,
      "comparisonsByType": [
        {
          "type": "similarities",
          "count": 5
        },
        {
          "type": "differences",
          "count": 8
        },
        {
          "type": "both",
          "count": 5
        }
      ]
    }
  },
  "timeSeriesData": {
    "documentUploads": [
      {
        "date": "2025-04-01T00:00:00.000Z",
        "count": 5
      }
    ],
    "documentAnalyses": [
      {
        "date": "2025-04-01T00:00:00.000Z",
        "count": 4
      }
    ],
    "documentComparisons": [
      {
        "date": "2025-04-01T00:00:00.000Z",
        "count": 2,
        "basic": 1,
        "enhanced": 1
      }
    ],
    "documentClassifications": [
      {
        "date": "2025-04-01T00:00:00.000Z",
        "count": 3,
        "automatic": 2,
        "manual": 1
      }
    ]
  },
  "lastUpdated": "2025-04-18T10:35:21.031Z"
}
```

### 4. User Analytics

Get analytics data focused on user engagement metrics.

**Endpoint:** `GET /analytics/users`

**Query Parameters:** All common query parameters

**Response:**

```json
{
  "organizationId": "string",
  "timeRange": {
    "start": "2025-03-18T00:00:00.000Z",
    "end": "2025-04-18T00:00:00.000Z"
  },
  "userEngagement": {
    "totalUsers": 8,
    "activeUsers": 6,
    "totalQueries": 156,
    "averageQueriesPerUser": 26,
    "averageResponseTime": 1.2,
    "userActivity": [
      {
        "userId": "user-123",
        "name": "John Doe",
        "queryCount": 45,
        "documentCount": 12,
        "lastActive": "2025-04-17T14:32:10.000Z"
      }
    ]
  },
  "topicAnalysis": {
    "topTopics": [
      {
        "topic": "contract terms",
        "count": 35
      }
    ],
    "topicTrends": [
      {
        "date": "2025-04-01T00:00:00.000Z",
        "topics": [
          {
            "topic": "contract terms",
            "count": 12
          }
        ]
      }
    ]
  },
  "timeSeriesData": {
    "userQueries": [
      {
        "date": "2025-04-01T00:00:00.000Z",
        "count": 15
      }
    ],
    "averageResponseTimes": [
      {
        "date": "2025-04-01T00:00:00.000Z",
        "value": 1.3
      }
    ]
  },
  "lastUpdated": "2025-04-18T10:35:21.031Z"
}
```

### 5. Citation Analytics

Get analytics data focused on citation metrics.

**Endpoint:** `GET /analytics/citations`

**Query Parameters:** All common query parameters

**Response:**

```json
{
  "organizationId": "string",
  "timeRange": {
    "start": "2025-03-18T00:00:00.000Z",
    "end": "2025-04-18T00:00:00.000Z"
  },
  "citationMetrics": {
    "totalCitations": 87,
    "topCitedCases": [
      {
        "caseName": "Smith v. Jones",
        "count": 12,
        "url": "https://courtlistener.com/opinion/12345/",
        "jurisdiction": "Federal"
      }
    ],
    "citationsByJurisdiction": [
      {
        "jurisdiction": "Federal",
        "count": 45
      }
    ],
    "citationsByYear": [
      {
        "year": 2023,
        "count": 32
      }
    ],
    "citationNetwork": {
      "nodes": [
        {
          "id": "case-123",
          "name": "Smith v. Jones",
          "year": 2023,
          "weight": 12
        }
      ],
      "links": [
        {
          "source": "case-123",
          "target": "case-456",
          "weight": 3
        }
      ]
    }
  },
  "lastUpdated": "2025-04-18T10:35:21.031Z"
}
```

## Analytics Collection Events

The following events are automatically tracked by the analytics system:

### Document Events

- **Document Upload**: Recorded when a document is uploaded to the system
- **Document Analysis**: Recorded when a document is analyzed
- **Document Comparison**: Recorded when two documents are compared
- **Document Classification**: Recorded when a document is classified

### User Engagement Events

- **User Query**: Recorded when a user makes a query about a document
- **Query Feedback**: Recorded when a user provides feedback on a query response

## Integration with Frontend

### Recommended Libraries

For visualizing the analytics data, we recommend the following libraries:

- **Charts**: [Chart.js](https://www.chartjs.org/) or [D3.js](https://d3js.org/)
- **Data Tables**: [AG Grid](https://www.ag-grid.com/) or [React Table](https://react-table.tanstack.com/)
- **Date Range Selection**: [React DatePicker](https://reactdatepicker.com/)

### Example Usage (React)

```jsx
import { useState, useEffect } from 'react';
import axios from 'axios';
import { Bar } from 'react-chartjs-2';

const AnalyticsDashboard = () => {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await axios.get(
        'http://localhost:4000/api/analytics/dashboard',
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
          params: {
            startDate: '2025-03-18T00:00:00.000Z',
            endDate: '2025-04-18T00:00:00.000Z',
            granularity: 'day',
          },
        },
      );
      setAnalytics(response.data);
      setError(null);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, []);

  if (loading) return <div>Loading analytics data...</div>;
  if (error) return <div>Error loading analytics: {error}</div>;
  if (!analytics) return null;

  // Prepare chart data for document uploads
  const documentUploadsData = {
    labels: analytics.timeSeriesData.documentUploads.map((item) =>
      new Date(item.date).toLocaleDateString(),
    ),
    datasets: [
      {
        label: 'Document Uploads',
        data: analytics.timeSeriesData.documentUploads.map(
          (item) => item.count,
        ),
        backgroundColor: 'rgba(54, 162, 235, 0.5)',
      },
    ],
  };

  return (
    <div>
      <h1>Analytics Dashboard</h1>

      <div className="stats-summary">
        <div className="stat-card">
          <h3>Total Documents</h3>
          <p>{analytics.documentAnalysis.totalDocuments}</p>
        </div>
        <div className="stat-card">
          <h3>Active Users</h3>
          <p>{analytics.userEngagement.activeUsers}</p>
        </div>
        <div className="stat-card">
          <h3>Total Queries</h3>
          <p>{analytics.userEngagement.totalQueries}</p>
        </div>
      </div>

      <div className="chart-container">
        <h2>Document Uploads Over Time</h2>
        <Bar data={documentUploadsData} />
      </div>

      {/* Add more visualizations as needed */}
    </div>
  );
};

export default AnalyticsDashboard;
```

## Error Handling

All endpoints return standard HTTP status codes:

- **200 OK**: Request succeeded
- **400 Bad Request**: Invalid parameters
- **401 Unauthorized**: Missing or invalid authentication
- **403 Forbidden**: Insufficient permissions
- **500 Internal Server Error**: Server-side error

Error responses follow this format:

```json
{
  "statusCode": 400,
  "message": "Invalid date range",
  "error": "Bad Request"
}
```

## Rate Limiting

Analytics endpoints are subject to rate limiting of 100 requests per minute per user.

## Data Freshness

Analytics data is updated in near real-time as events occur in the system. The `lastUpdated` field in the response indicates when the data was last refreshed.

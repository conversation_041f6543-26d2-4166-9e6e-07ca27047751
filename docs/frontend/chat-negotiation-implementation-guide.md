# Chat Negotiation Frontend Implementation Guide

## Overview

This guide provides step-by-step instructions for implementing the chat negotiation features in the frontend application. The implementation includes session management, real-time chat interface, performance tracking, and gamification elements.

## Required Components

### 1. Core Components Structure

```
src/
├── components/
│   ├── chat-negotiation/
│   │   ├── NegotiationSession.tsx
│   │   ├── ChatInterface.tsx
│   │   ├── SessionSetup.tsx
│   │   ├── PerformanceMetrics.tsx
│   │   ├── AIPersonalitySelector.tsx
│   │   └── SessionHistory.tsx
│   └── ui/
│       ├── MessageBubble.tsx
│       ├── TypingIndicator.tsx
│       └── ProgressBar.tsx
├── hooks/
│   ├── useNegotiationSession.ts
│   ├── useChatMessages.ts
│   └── useDataExtraction.ts
├── services/
│   └── chatNegotiationApi.ts
├── types/
│   └── negotiation.ts
└── utils/
    ├── messageParser.ts
    └── performanceCalculator.ts
```

## Implementation Steps

### Step 1: API Service Layer

Create the API service for all negotiation endpoints:

```typescript
// src/services/chatNegotiationApi.ts
import { apiClient } from './apiClient';
import type { 
  CreateSessionRequest, 
  CreateSessionResponse,
  SendMoveRequest,
  SendMoveResponse,
  ExtractDataRequest,
  ExtractDataResponse 
} from '../types/negotiation';

export class ChatNegotiationApi {
  private baseUrl = '/api/chat-negotiation';

  async createSession(data: CreateSessionRequest): Promise<CreateSessionResponse> {
    const response = await apiClient.post(`${this.baseUrl}/sessions`, data);
    return response.data;
  }

  async getSession(sessionId: string): Promise<CreateSessionResponse> {
    const response = await apiClient.get(`${this.baseUrl}/sessions/${sessionId}`);
    return response.data;
  }

  async getUserSessions(params?: {
    status?: string;
    limit?: number;
    offset?: number;
  }): Promise<{ sessions: CreateSessionResponse[]; total: number; hasMore: boolean }> {
    const response = await apiClient.get(`${this.baseUrl}/sessions`, { params });
    return response.data;
  }

  async sendMove(sessionId: string, data: SendMoveRequest): Promise<SendMoveResponse> {
    const response = await apiClient.post(`${this.baseUrl}/sessions/${sessionId}/moves`, data);
    return response.data;
  }

  async extractData(data: ExtractDataRequest): Promise<ExtractDataResponse> {
    const response = await apiClient.post(`${this.baseUrl}/extract-data`, data);
    return response.data;
  }
}

export const chatNegotiationApi = new ChatNegotiationApi();
```

### Step 2: Type Definitions

```typescript
// src/types/negotiation.ts
export interface AiPersonality {
  characterId: string;
  aggressiveness: number;
  flexibility: number;
  riskTolerance: number;
  communicationStyle: 'DIRECT' | 'DIPLOMATIC' | 'ANALYTICAL' | 'EMOTIONAL';
}

export interface RelationshipMetrics {
  trust: number;
  respect: number;
  pressure: number;
}

export interface ExtractedTerms {
  price?: number;
  currency?: string;
  terms?: string[];
}

export interface NegotiationSession {
  id: string;
  scenarioId: string;
  status: 'active' | 'completed' | 'paused';
  currentRound: number;
  extractedTerms: ExtractedTerms;
  relationshipMetrics: RelationshipMetrics;
  score: number;
  aiPersonality: AiPersonality;
  negotiationSessionId: string;
  totalMessages: number;
  aiResponseTime: number;
  createdAt: string;
  updatedAt: string;
  lastActivityAt: string;
}

export interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: string;
  extractedData?: any;
  suggestions?: string[];
  processingTime?: number;
}

export interface CreateSessionRequest {
  scenarioId: string;
  aiPersonality: AiPersonality;
  metadata?: Record<string, any>;
}

export interface SendMoveRequest {
  content: string;
  extractedData?: any;
  context?: Record<string, any>;
}
```

### Step 3: Custom Hooks

```typescript
// src/hooks/useNegotiationSession.ts
import { useState, useEffect } from 'react';
import { chatNegotiationApi } from '../services/chatNegotiationApi';
import type { NegotiationSession, CreateSessionRequest } from '../types/negotiation';

export function useNegotiationSession(sessionId?: string) {
  const [session, setSession] = useState<NegotiationSession | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createSession = async (data: CreateSessionRequest) => {
    setLoading(true);
    setError(null);
    try {
      const newSession = await chatNegotiationApi.createSession(data);
      setSession(newSession);
      return newSession;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create session');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const loadSession = async (id: string) => {
    setLoading(true);
    setError(null);
    try {
      const sessionData = await chatNegotiationApi.getSession(id);
      setSession(sessionData);
      return sessionData;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load session');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const updateSession = (updates: Partial<NegotiationSession>) => {
    setSession(prev => prev ? { ...prev, ...updates } : null);
  };

  useEffect(() => {
    if (sessionId) {
      loadSession(sessionId);
    }
  }, [sessionId]);

  return {
    session,
    loading,
    error,
    createSession,
    loadSession,
    updateSession
  };
}
```

```typescript
// src/hooks/useChatMessages.ts
import { useState, useCallback } from 'react';
import { chatNegotiationApi } from '../services/chatNegotiationApi';
import type { ChatMessage, SendMoveRequest } from '../types/negotiation';

export function useChatMessages(sessionId: string, onSessionUpdate?: (updates: any) => void) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isAiTyping, setIsAiTyping] = useState(false);
  const [loading, setLoading] = useState(false);

  const sendMessage = useCallback(async (content: string, context?: Record<string, any>) => {
    if (!content.trim() || loading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content,
      sender: 'user',
      timestamp: new Date().toISOString()
    };

    setMessages(prev => [...prev, userMessage]);
    setLoading(true);
    setIsAiTyping(true);

    try {
      const moveData: SendMoveRequest = {
        content,
        context: {
          userConfidence: 0.8,
          timeSpent: 30,
          ...context
        }
      };

      const response = await chatNegotiationApi.sendMove(sessionId, moveData);

      // Add AI response message
      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: response.aiResponse.content,
        sender: 'ai',
        timestamp: response.aiResponse.timestamp,
        suggestions: response.aiResponse.suggestions,
        processingTime: response.aiResponse.processingTime
      };

      setMessages(prev => [...prev, aiMessage]);

      // Update session state
      if (onSessionUpdate) {
        onSessionUpdate(response.sessionUpdate);
      }

      return response;
    } catch (error) {
      console.error('Failed to send message:', error);
      // Remove user message on error
      setMessages(prev => prev.filter(msg => msg.id !== userMessage.id));
      throw error;
    } finally {
      setLoading(false);
      setIsAiTyping(false);
    }
  }, [sessionId, loading, onSessionUpdate]);

  const clearMessages = () => {
    setMessages([]);
  };

  return {
    messages,
    isAiTyping,
    loading,
    sendMessage,
    clearMessages
  };
}
```

### Step 4: Main Session Component

```typescript
// src/components/chat-negotiation/NegotiationSession.tsx
import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import { useNegotiationSession } from '../../hooks/useNegotiationSession';
import { useChatMessages } from '../../hooks/useChatMessages';
import { ChatInterface } from './ChatInterface';
import { PerformanceMetrics } from './PerformanceMetrics';
import { SessionSetup } from './SessionSetup';

export function NegotiationSession() {
  const { sessionId } = useParams<{ sessionId?: string }>();
  const [showSetup, setShowSetup] = useState(!sessionId);
  
  const { 
    session, 
    loading: sessionLoading, 
    error: sessionError,
    createSession,
    updateSession 
  } = useNegotiationSession(sessionId);

  const {
    messages,
    isAiTyping,
    loading: messageLoading,
    sendMessage
  } = useChatMessages(session?.id || '', updateSession);

  const handleSessionCreated = (newSession: any) => {
    setShowSetup(false);
    // Navigate to new session URL
    window.history.pushState({}, '', `/negotiation/${newSession.id}`);
  };

  if (showSetup) {
    return (
      <SessionSetup 
        onSessionCreated={handleSessionCreated}
        loading={sessionLoading}
        error={sessionError}
      />
    );
  }

  if (sessionLoading) {
    return <div className="flex justify-center p-8">Loading session...</div>;
  }

  if (sessionError || !session) {
    return (
      <div className="flex flex-col items-center p-8">
        <p className="text-red-600 mb-4">{sessionError || 'Session not found'}</p>
        <button 
          onClick={() => setShowSetup(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          Start New Session
        </button>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        <div className="bg-white border-b p-4">
          <h1 className="text-xl font-semibold">
            Negotiation Session - Round {session.currentRound}
          </h1>
          <p className="text-gray-600">
            Status: {session.status} | Score: {session.score.toFixed(2)}
          </p>
        </div>
        
        <ChatInterface
          messages={messages}
          onSendMessage={sendMessage}
          isAiTyping={isAiTyping}
          loading={messageLoading}
          session={session}
        />
      </div>

      {/* Performance Sidebar */}
      <div className="w-80 bg-white border-l">
        <PerformanceMetrics session={session} />
      </div>
    </div>
  );
}
```

### Step 5: Chat Interface Component

```typescript
// src/components/chat-negotiation/ChatInterface.tsx
import React, { useState, useRef, useEffect } from 'react';
import { MessageBubble } from '../ui/MessageBubble';
import { TypingIndicator } from '../ui/TypingIndicator';
import type { ChatMessage, NegotiationSession } from '../../types/negotiation';

interface ChatInterfaceProps {
  messages: ChatMessage[];
  onSendMessage: (content: string, context?: Record<string, any>) => Promise<any>;
  isAiTyping: boolean;
  loading: boolean;
  session: NegotiationSession;
}

export function ChatInterface({ 
  messages, 
  onSendMessage, 
  isAiTyping, 
  loading,
  session 
}: ChatInterfaceProps) {
  const [inputValue, setInputValue] = useState('');
  const [startTime, setStartTime] = useState<number | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isAiTyping]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim() || loading) return;

    const timeSpent = startTime ? (Date.now() - startTime) / 1000 : 0;
    
    try {
      await onSendMessage(inputValue, {
        timeSpent,
        userConfidence: 0.8 // Could be dynamic based on UI
      });
      setInputValue('');
      setStartTime(null);
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value);
    if (!startTime) {
      setStartTime(Date.now());
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 && (
          <div className="text-center text-gray-500 mt-8">
            <p>Start the negotiation by sending your first message!</p>
            <p className="text-sm mt-2">
              AI Personality: {session.aiPersonality.communicationStyle} 
              (Aggressiveness: {(session.aiPersonality.aggressiveness * 100).toFixed(0)}%)
            </p>
          </div>
        )}
        
        {messages.map((message) => (
          <MessageBubble key={message.id} message={message} />
        ))}
        
        {isAiTyping && <TypingIndicator />}
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="border-t bg-white p-4">
        <form onSubmit={handleSubmit} className="flex space-x-2">
          <textarea
            ref={inputRef}
            value={inputValue}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder="Type your negotiation message..."
            className="flex-1 resize-none border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={2}
            disabled={loading}
          />
          <button
            type="submit"
            disabled={!inputValue.trim() || loading}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Sending...' : 'Send'}
          </button>
        </form>
        
        <div className="flex justify-between text-xs text-gray-500 mt-2">
          <span>Round {session.currentRound} • {messages.length} messages</span>
          <span>Press Enter to send, Shift+Enter for new line</span>
        </div>
      </div>
    </div>
  );
}
```

## Next Steps

1. **Implement remaining components** (PerformanceMetrics, SessionSetup, etc.)
2. **Add real-time data extraction** preview while typing
3. **Implement session history** and analytics
4. **Add WebSocket support** for real-time updates
5. **Create mobile-responsive** design
6. **Add accessibility features** (ARIA labels, keyboard navigation)
7. **Implement error boundaries** and retry mechanisms
8. **Add unit and integration tests**

## Performance Considerations

- **Debounce data extraction** calls while typing
- **Implement message virtualization** for long conversations
- **Cache session data** in localStorage
- **Optimize re-renders** with React.memo and useMemo
- **Implement progressive loading** for session history

## Security Notes

- **Validate all user inputs** before sending to API
- **Sanitize message content** to prevent XSS
- **Implement rate limiting** on the frontend
- **Handle authentication errors** gracefully
- **Store sensitive data securely** (avoid localStorage for tokens)

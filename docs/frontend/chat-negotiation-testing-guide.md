# Chat Negotiation Testing Guide

## Testing Strategy

### 1. Unit Tests

#### API Service Tests

```typescript
// src/services/__tests__/chatNegotiationApi.test.ts
import { chatNegotiationApi } from '../chatNegotiationApi';
import { apiClient } from '../apiClient';

jest.mock('../apiClient');
const mockedApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe('ChatNegotiationApi', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createSession', () => {
    it('should create a new negotiation session', async () => {
      const mockSession = {
        id: 'session-123',
        scenarioId: 'scenario-456',
        status: 'active',
        currentRound: 1
      };

      mockedApiClient.post.mockResolvedValue({ data: mockSession });

      const sessionData = {
        scenarioId: 'scenario-456',
        aiPersonality: {
          characterId: 'default',
          aggressiveness: 0.5,
          flexibility: 0.5,
          riskTolerance: 0.5,
          communicationStyle: 'ANALYTICAL' as const
        }
      };

      const result = await chatNegotiationApi.createSession(sessionData);

      expect(mockedApiClient.post).toHaveBeenCalledWith(
        '/api/chat-negotiation/sessions',
        sessionData
      );
      expect(result).toEqual(mockSession);
    });

    it('should handle API errors', async () => {
      mockedApiClient.post.mockRejectedValue(new Error('Network error'));

      const sessionData = {
        scenarioId: 'scenario-456',
        aiPersonality: {
          characterId: 'default',
          aggressiveness: 0.5,
          flexibility: 0.5,
          riskTolerance: 0.5,
          communicationStyle: 'ANALYTICAL' as const
        }
      };

      await expect(chatNegotiationApi.createSession(sessionData))
        .rejects.toThrow('Network error');
    });
  });

  describe('sendMove', () => {
    it('should send a chat move and return response', async () => {
      const mockResponse = {
        userMove: {
          content: 'Test message',
          extractedData: {},
          timestamp: '2023-01-01T00:00:00Z',
          processingTime: 100
        },
        aiResponse: {
          content: 'AI response',
          timestamp: '2023-01-01T00:00:01Z',
          processingTime: 50
        },
        sessionUpdate: {
          currentRound: 2,
          score: 5.5
        },
        creditsConsumed: 3
      };

      mockedApiClient.post.mockResolvedValue({ data: mockResponse });

      const moveData = {
        content: 'Test message',
        context: { userConfidence: 0.8 }
      };

      const result = await chatNegotiationApi.sendMove('session-123', moveData);

      expect(mockedApiClient.post).toHaveBeenCalledWith(
        '/api/chat-negotiation/sessions/session-123/moves',
        moveData
      );
      expect(result).toEqual(mockResponse);
    });
  });
});
```

#### Hook Tests

```typescript
// src/hooks/__tests__/useNegotiationSession.test.ts
import { renderHook, act } from '@testing-library/react';
import { useNegotiationSession } from '../useNegotiationSession';
import { chatNegotiationApi } from '../../services/chatNegotiationApi';

jest.mock('../../services/chatNegotiationApi');
const mockedApi = chatNegotiationApi as jest.Mocked<typeof chatNegotiationApi>;

describe('useNegotiationSession', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should create a new session', async () => {
    const mockSession = {
      id: 'session-123',
      scenarioId: 'scenario-456',
      status: 'active',
      currentRound: 1
    };

    mockedApi.createSession.mockResolvedValue(mockSession);

    const { result } = renderHook(() => useNegotiationSession());

    expect(result.current.session).toBeNull();
    expect(result.current.loading).toBe(false);

    await act(async () => {
      await result.current.createSession({
        scenarioId: 'scenario-456',
        aiPersonality: {
          characterId: 'default',
          aggressiveness: 0.5,
          flexibility: 0.5,
          riskTolerance: 0.5,
          communicationStyle: 'ANALYTICAL'
        }
      });
    });

    expect(result.current.session).toEqual(mockSession);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it('should handle session creation errors', async () => {
    mockedApi.createSession.mockRejectedValue(new Error('Failed to create'));

    const { result } = renderHook(() => useNegotiationSession());

    await act(async () => {
      try {
        await result.current.createSession({
          scenarioId: 'scenario-456',
          aiPersonality: {
            characterId: 'default',
            aggressiveness: 0.5,
            flexibility: 0.5,
            riskTolerance: 0.5,
            communicationStyle: 'ANALYTICAL'
          }
        });
      } catch (error) {
        // Expected to throw
      }
    });

    expect(result.current.session).toBeNull();
    expect(result.current.error).toBe('Failed to create');
  });
});
```

### 2. Component Tests

```typescript
// src/components/chat-negotiation/__tests__/ChatInterface.test.tsx
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ChatInterface } from '../ChatInterface';

const mockSession = {
  id: 'session-123',
  currentRound: 1,
  aiPersonality: {
    characterId: 'default',
    aggressiveness: 0.5,
    flexibility: 0.5,
    riskTolerance: 0.5,
    communicationStyle: 'ANALYTICAL' as const
  }
};

const mockMessages = [
  {
    id: '1',
    content: 'Hello, let\'s negotiate!',
    sender: 'user' as const,
    timestamp: '2023-01-01T00:00:00Z'
  },
  {
    id: '2',
    content: 'I\'m ready to discuss terms.',
    sender: 'ai' as const,
    timestamp: '2023-01-01T00:00:01Z'
  }
];

describe('ChatInterface', () => {
  const defaultProps = {
    messages: mockMessages,
    onSendMessage: jest.fn(),
    isAiTyping: false,
    loading: false,
    session: mockSession
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render messages correctly', () => {
    render(<ChatInterface {...defaultProps} />);

    expect(screen.getByText('Hello, let\'s negotiate!')).toBeInTheDocument();
    expect(screen.getByText('I\'m ready to discuss terms.')).toBeInTheDocument();
  });

  it('should send message when form is submitted', async () => {
    const user = userEvent.setup();
    const mockOnSendMessage = jest.fn().mockResolvedValue({});

    render(
      <ChatInterface 
        {...defaultProps} 
        onSendMessage={mockOnSendMessage}
      />
    );

    const input = screen.getByPlaceholderText('Type your negotiation message...');
    const sendButton = screen.getByText('Send');

    await user.type(input, 'My offer is $50,000');
    await user.click(sendButton);

    await waitFor(() => {
      expect(mockOnSendMessage).toHaveBeenCalledWith(
        'My offer is $50,000',
        expect.objectContaining({
          timeSpent: expect.any(Number),
          userConfidence: 0.8
        })
      );
    });
  });

  it('should handle Enter key to send message', async () => {
    const user = userEvent.setup();
    const mockOnSendMessage = jest.fn().mockResolvedValue({});

    render(
      <ChatInterface 
        {...defaultProps} 
        onSendMessage={mockOnSendMessage}
      />
    );

    const input = screen.getByPlaceholderText('Type your negotiation message...');

    await user.type(input, 'Quick message');
    await user.keyboard('{Enter}');

    await waitFor(() => {
      expect(mockOnSendMessage).toHaveBeenCalledWith(
        'Quick message',
        expect.any(Object)
      );
    });
  });

  it('should show typing indicator when AI is typing', () => {
    render(<ChatInterface {...defaultProps} isAiTyping={true} />);

    expect(screen.getByText('AI is thinking...')).toBeInTheDocument();
  });

  it('should disable input when loading', () => {
    render(<ChatInterface {...defaultProps} loading={true} />);

    const input = screen.getByPlaceholderText('Type your negotiation message...');
    const sendButton = screen.getByText('Sending...');

    expect(input).toBeDisabled();
    expect(sendButton).toBeDisabled();
  });
});
```

### 3. Integration Tests

```typescript
// src/components/chat-negotiation/__tests__/NegotiationSession.integration.test.tsx
import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { NegotiationSession } from '../NegotiationSession';
import { chatNegotiationApi } from '../../services/chatNegotiationApi';

jest.mock('../../services/chatNegotiationApi');
const mockedApi = chatNegotiationApi as jest.Mocked<typeof chatNegotiationApi>;

// Mock react-router-dom
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({ sessionId: 'session-123' })
}));

const mockSession = {
  id: 'session-123',
  scenarioId: 'scenario-456',
  status: 'active',
  currentRound: 1,
  extractedTerms: {},
  relationshipMetrics: {
    trust: 50,
    respect: 50,
    pressure: 20
  },
  score: 5.0,
  aiPersonality: {
    characterId: 'default',
    aggressiveness: 0.5,
    flexibility: 0.5,
    riskTolerance: 0.5,
    communicationStyle: 'ANALYTICAL'
  },
  negotiationSessionId: 'neg-123',
  totalMessages: 0,
  aiResponseTime: 0,
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
  lastActivityAt: '2023-01-01T00:00:00Z'
};

describe('NegotiationSession Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should load session and allow sending messages', async () => {
    const user = userEvent.setup();

    mockedApi.getSession.mockResolvedValue(mockSession);
    mockedApi.sendMove.mockResolvedValue({
      userMove: {
        content: 'Test message',
        extractedData: {},
        timestamp: '2023-01-01T00:00:00Z',
        processingTime: 100
      },
      aiResponse: {
        content: 'AI response to test',
        timestamp: '2023-01-01T00:00:01Z',
        processingTime: 50
      },
      sessionUpdate: {
        currentRound: 2,
        extractedTerms: { price: 50000, currency: 'USD' },
        relationshipMetrics: { trust: 55, respect: 52, pressure: 18 },
        score: 5.5,
        status: 'active'
      },
      creditsConsumed: 3
    });

    render(
      <BrowserRouter>
        <NegotiationSession />
      </BrowserRouter>
    );

    // Wait for session to load
    await waitFor(() => {
      expect(screen.getByText(/Negotiation Session - Round 1/)).toBeInTheDocument();
    });

    // Send a message
    const input = screen.getByPlaceholderText('Type your negotiation message...');
    await user.type(input, 'I propose $50,000 for this deal');
    await user.click(screen.getByText('Send'));

    // Wait for AI response
    await waitFor(() => {
      expect(screen.getByText('AI response to test')).toBeInTheDocument();
    });

    // Check that session was updated
    expect(screen.getByText(/Round 2/)).toBeInTheDocument();
  });

  it('should handle session loading errors', async () => {
    mockedApi.getSession.mockRejectedValue(new Error('Session not found'));

    render(
      <BrowserRouter>
        <NegotiationSession />
      </BrowserRouter>
    );

    await waitFor(() => {
      expect(screen.getByText(/Session not found/)).toBeInTheDocument();
    });

    expect(screen.getByText('Start New Session')).toBeInTheDocument();
  });
});
```

### 4. E2E Tests (Cypress)

```typescript
// cypress/e2e/chat-negotiation.cy.ts
describe('Chat Negotiation Flow', () => {
  beforeEach(() => {
    // Mock API responses
    cy.intercept('POST', '/api/chat-negotiation/sessions', {
      fixture: 'negotiation-session.json'
    }).as('createSession');

    cy.intercept('POST', '/api/chat-negotiation/sessions/*/moves', {
      fixture: 'chat-move-response.json'
    }).as('sendMove');

    cy.visit('/negotiation');
  });

  it('should complete a full negotiation flow', () => {
    // Setup session
    cy.get('[data-testid="scenario-software"]').click();
    cy.get('[data-testid="communication-style-analytical"]').click();
    cy.get('[data-testid="aggressiveness-slider"]').invoke('val', 0.6).trigger('input');
    cy.get('[data-testid="start-negotiation"]').click();

    cy.wait('@createSession');

    // Send first message
    cy.get('[data-testid="message-input"]')
      .type('I would like to discuss licensing terms for your software solution.');
    cy.get('[data-testid="send-button"]').click();

    cy.wait('@sendMove');

    // Verify AI response appears
    cy.get('[data-testid="ai-message"]').should('be.visible');

    // Check performance metrics update
    cy.get('[data-testid="trust-metric"]').should('contain', '55');
    cy.get('[data-testid="current-round"]').should('contain', '2');

    // Send follow-up message
    cy.get('[data-testid="message-input"]')
      .type('My budget is around $50,000 with Net 30 payment terms.');
    cy.get('[data-testid="send-button"]').click();

    // Verify extracted terms appear
    cy.get('[data-testid="extracted-price"]').should('contain', '$50,000');
    cy.get('[data-testid="extracted-terms"]').should('contain', 'Net 30');
  });

  it('should handle real-time data extraction', () => {
    cy.intercept('POST', '/api/chat-negotiation/extract-data', {
      fixture: 'data-extraction.json'
    }).as('extractData');

    // Start typing a message with price
    cy.get('[data-testid="message-input"]').type('I can offer $75,000');

    // Should show real-time extraction preview
    cy.get('[data-testid="extraction-preview"]').should('be.visible');
    cy.get('[data-testid="preview-price"]').should('contain', '$75,000');
  });

  it('should show error states gracefully', () => {
    // Mock API error
    cy.intercept('POST', '/api/chat-negotiation/sessions/*/moves', {
      statusCode: 500,
      body: { message: 'Internal server error' }
    }).as('sendMoveError');

    cy.get('[data-testid="message-input"]').type('Test message');
    cy.get('[data-testid="send-button"]').click();

    cy.wait('@sendMoveError');

    // Should show error message
    cy.get('[data-testid="error-message"]').should('be.visible');
    cy.get('[data-testid="retry-button"]').should('be.visible');
  });
});
```

### 5. Performance Tests

```typescript
// src/utils/__tests__/performance.test.ts
import { render } from '@testing-library/react';
import { ChatInterface } from '../components/chat-negotiation/ChatInterface';

describe('Performance Tests', () => {
  it('should render large message lists efficiently', () => {
    const largeMessageList = Array.from({ length: 1000 }, (_, i) => ({
      id: i.toString(),
      content: `Message ${i}`,
      sender: i % 2 === 0 ? 'user' : 'ai' as const,
      timestamp: new Date().toISOString()
    }));

    const mockSession = {
      id: 'session-123',
      currentRound: 500,
      aiPersonality: {
        characterId: 'default',
        aggressiveness: 0.5,
        flexibility: 0.5,
        riskTolerance: 0.5,
        communicationStyle: 'ANALYTICAL' as const
      }
    };

    const startTime = performance.now();
    
    render(
      <ChatInterface
        messages={largeMessageList}
        onSendMessage={jest.fn()}
        isAiTyping={false}
        loading={false}
        session={mockSession}
      />
    );

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    // Should render within reasonable time (adjust threshold as needed)
    expect(renderTime).toBeLessThan(1000); // 1 second
  });
});
```

## Test Data Fixtures

### Session Fixture
```json
// cypress/fixtures/negotiation-session.json
{
  "id": "session-123",
  "scenarioId": "scenario-456",
  "status": "active",
  "currentRound": 1,
  "extractedTerms": {},
  "relationshipMetrics": {
    "trust": 50,
    "respect": 50,
    "pressure": 20
  },
  "score": 5.0,
  "aiPersonality": {
    "characterId": "default_character",
    "aggressiveness": 0.5,
    "flexibility": 0.5,
    "riskTolerance": 0.5,
    "communicationStyle": "ANALYTICAL"
  },
  "negotiationSessionId": "neg-123",
  "totalMessages": 0,
  "aiResponseTime": 0,
  "createdAt": "2023-01-01T00:00:00Z",
  "updatedAt": "2023-01-01T00:00:00Z",
  "lastActivityAt": "2023-01-01T00:00:00Z"
}
```

### Chat Move Response Fixture
```json
// cypress/fixtures/chat-move-response.json
{
  "userMove": {
    "content": "I would like to discuss licensing terms",
    "extractedData": {
      "strategy": "collaborative",
      "sentiment": "neutral",
      "confidence": 0.8
    },
    "timestamp": "2023-01-01T00:00:00Z",
    "processingTime": 100
  },
  "aiResponse": {
    "content": "I appreciate your interest in our software solution. Let's explore what would work best for both parties.",
    "suggestions": [
      "Consider asking about volume discounts",
      "Inquire about implementation support"
    ],
    "timestamp": "2023-01-01T00:00:01Z",
    "processingTime": 250
  },
  "sessionUpdate": {
    "currentRound": 2,
    "extractedTerms": {},
    "relationshipMetrics": {
      "trust": 55,
      "respect": 52,
      "pressure": 18
    },
    "score": 5.2,
    "status": "active"
  },
  "creditsConsumed": 3
}
```

## Testing Best Practices

1. **Mock External Dependencies**: Always mock API calls and external services
2. **Test User Interactions**: Focus on user workflows and edge cases
3. **Performance Testing**: Test with large datasets and slow networks
4. **Accessibility Testing**: Ensure keyboard navigation and screen reader support
5. **Error Handling**: Test all error scenarios and recovery flows
6. **Real-time Features**: Test WebSocket connections and real-time updates
7. **Cross-browser Testing**: Test on different browsers and devices
8. **Load Testing**: Test with multiple concurrent users

## Running Tests

```bash
# Unit and integration tests
npm test

# E2E tests
npm run cypress:open

# Performance tests
npm run test:performance

# Coverage report
npm run test:coverage
```

# Chat Negotiation Frontend Documentation

## 📋 Overview

This documentation provides comprehensive guidance for implementing the Chat Negotiation feature in the frontend application. The feature enables users to practice negotiation skills through AI-powered simulations with real-time feedback and performance tracking.

## 🚀 Quick Start

### Prerequisites

- React 18+
- TypeScript 4.5+
- Tailwind CSS 3.0+
- React Router 6+
- Axios or similar HTTP client

### Installation

```bash
# Install required dependencies
npm install react react-dom typescript
npm install @types/react @types/react-dom
npm install tailwindcss
npm install react-router-dom
npm install axios
```

### Basic Setup

1. **Copy the provided components** to your project structure
2. **Configure API client** with your backend URL
3. **Add routes** to your React Router configuration
4. **Import and use** the main NegotiationSession component

```typescript
// App.tsx
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import { NegotiationSession } from './components/chat-negotiation/NegotiationSession';

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/negotiation" element={<NegotiationSession />} />
        <Route path="/negotiation/:sessionId" element={<NegotiationSession />} />
      </Routes>
    </BrowserRouter>
  );
}
```

## 📚 Documentation Structure

### 1. [API Documentation](./chat-negotiation-api.md)
Complete API reference including:
- All available endpoints
- Request/response schemas
- Authentication requirements
- Error handling
- Rate limiting
- Credit system

### 2. [Implementation Guide](./chat-negotiation-implementation-guide.md)
Step-by-step implementation instructions:
- Project structure
- Custom hooks
- Service layer setup
- Component architecture
- State management

### 3. [UI Components](./chat-negotiation-components.md)
Detailed component examples:
- NegotiationSession (main container)
- ChatInterface (messaging)
- PerformanceMetrics (scoring)
- SessionSetup (configuration)
- AI Personality Selector
- Message components

### 4. [Testing Guide](./chat-negotiation-testing-guide.md)
Comprehensive testing strategy:
- Unit tests
- Integration tests
- E2E tests
- Performance tests
- Test fixtures

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Application                     │
├─────────────────────────────────────────────────────────────┤
│  Components                                                 │
│  ├── NegotiationSession (Main Container)                   │
│  ├── ChatInterface (Real-time Chat)                        │
│  ├── PerformanceMetrics (Scoring & Analytics)              │
│  ├── SessionSetup (Configuration)                          │
│  └── UI Components (MessageBubble, TypingIndicator, etc.)  │
├─────────────────────────────────────────────────────────────┤
│  Hooks & State Management                                   │
│  ├── useNegotiationSession (Session lifecycle)             │
│  ├── useChatMessages (Message handling)                    │
│  └── useDataExtraction (Real-time analysis)                │
├─────────────────────────────────────────────────────────────┤
│  Services & API Layer                                       │
│  ├── chatNegotiationApi (HTTP client)                      │
│  ├── messageParser (Data extraction)                       │
│  └── performanceCalculator (Scoring)                       │
├─────────────────────────────────────────────────────────────┤
│  Types & Utilities                                          │
│  ├── negotiation.ts (TypeScript interfaces)                │
│  └── utils (Helper functions)                              │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Backend API                              │
│  /api/chat-negotiation/*                                    │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 Key Features

### Core Functionality
- ✅ **Session Management**: Create, load, and manage negotiation sessions
- ✅ **Real-time Chat**: Send messages and receive AI responses
- ✅ **Data Extraction**: Automatic parsing of prices, terms, and strategies
- ✅ **Performance Tracking**: Real-time scoring and relationship metrics
- ✅ **AI Personalities**: Configurable opponent characteristics

### Advanced Features
- 🔄 **Real-time Updates**: Live performance metrics and suggestions
- 📊 **Analytics Dashboard**: Session history and progress tracking
- 🎮 **Gamification**: Scoring system and achievement tracking
- 📱 **Responsive Design**: Mobile and desktop optimized
- ♿ **Accessibility**: Screen reader and keyboard navigation support

## 🔧 Configuration

### Environment Variables

```bash
# .env
REACT_APP_API_BASE_URL=http://localhost:4000
REACT_APP_WS_URL=ws://localhost:4000
REACT_APP_ENABLE_REAL_TIME_EXTRACTION=true
REACT_APP_MAX_MESSAGE_LENGTH=1000
```

### API Client Configuration

```typescript
// src/services/apiClient.ts
import axios from 'axios';

export const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add auth interceptor
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

## 🎨 Styling & Theming

### Tailwind CSS Classes

The components use Tailwind CSS for styling. Key design tokens:

```css
/* Primary Colors */
.bg-primary { @apply bg-blue-600; }
.text-primary { @apply text-blue-600; }

/* Status Colors */
.bg-success { @apply bg-green-500; }
.bg-warning { @apply bg-yellow-500; }
.bg-danger { @apply bg-red-500; }

/* Layout */
.container-chat { @apply max-w-4xl mx-auto p-4; }
.sidebar-metrics { @apply w-80 bg-white border-l; }
```

### Custom CSS Variables

```css
:root {
  --chat-bubble-user: #3b82f6;
  --chat-bubble-ai: #f3f4f6;
  --performance-excellent: #10b981;
  --performance-good: #f59e0b;
  --performance-poor: #ef4444;
}
```

## 📱 Responsive Design

### Breakpoints

- **Mobile**: `< 768px` - Single column layout
- **Tablet**: `768px - 1024px` - Collapsible sidebar
- **Desktop**: `> 1024px` - Full two-column layout

### Mobile Optimizations

```typescript
// Mobile-specific considerations
const isMobile = window.innerWidth < 768;

// Adjust message bubble sizes
const messageClass = isMobile 
  ? 'max-w-xs text-sm' 
  : 'max-w-md text-base';

// Collapsible performance metrics
const [showMetrics, setShowMetrics] = useState(!isMobile);
```

## 🔒 Security Considerations

### Input Validation

```typescript
// Sanitize user input
const sanitizeMessage = (content: string): string => {
  return content
    .trim()
    .slice(0, MAX_MESSAGE_LENGTH)
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
};
```

### Authentication

```typescript
// Check auth status before API calls
const checkAuth = (): boolean => {
  const token = localStorage.getItem('authToken');
  if (!token || isTokenExpired(token)) {
    redirectToLogin();
    return false;
  }
  return true;
};
```

## 🚀 Deployment

### Build Configuration

```json
{
  "scripts": {
    "build": "react-scripts build",
    "build:staging": "REACT_APP_ENV=staging npm run build",
    "build:production": "REACT_APP_ENV=production npm run build"
  }
}
```

### Performance Optimizations

```typescript
// Code splitting for negotiation features
const NegotiationSession = lazy(() => 
  import('./components/chat-negotiation/NegotiationSession')
);

// Memoize expensive components
const PerformanceMetrics = memo(({ session }) => {
  // Component implementation
});
```

## 🐛 Troubleshooting

### Common Issues

1. **API Connection Errors**
   - Check CORS configuration
   - Verify API base URL
   - Confirm authentication tokens

2. **Real-time Updates Not Working**
   - Check WebSocket connection
   - Verify event listeners
   - Test network connectivity

3. **Performance Issues**
   - Implement message virtualization
   - Add debouncing to data extraction
   - Optimize re-renders with React.memo

### Debug Mode

```typescript
// Enable debug logging
const DEBUG = process.env.NODE_ENV === 'development';

const debugLog = (message: string, data?: any) => {
  if (DEBUG) {
    console.log(`[Chat Negotiation] ${message}`, data);
  }
};
```

## 📞 Support

For implementation questions or issues:

1. **Check the documentation** in this folder
2. **Review the API responses** in browser dev tools
3. **Test with mock data** to isolate issues
4. **Check the backend logs** for API errors

## 🔄 Future Enhancements

### Planned Features

- **Voice Input**: Speech-to-text for message input
- **Video Calls**: Face-to-face negotiation simulation
- **Multi-party**: Support for 3+ participant negotiations
- **Templates**: Pre-built negotiation scenarios
- **Analytics**: Advanced performance insights
- **Integrations**: CRM and calendar connections

### Technical Improvements

- **WebSocket Integration**: Real-time bidirectional communication
- **Offline Support**: PWA capabilities with local storage
- **Performance**: Virtual scrolling for long conversations
- **Accessibility**: Enhanced screen reader support
- **Testing**: Automated visual regression testing

# Contract Playbooks API - Quick Reference

## 🚀 Endpoints Summary

| Method | Endpoint | Purpose | Response Time |
|--------|----------|---------|---------------|
| **GET** | `/contract-playbooks` | List all playbooks | < 500ms |
| **POST** | `/contract-playbooks` | Create new playbook | < 1s |
| **GET** | `/contract-playbooks/:id` | Get specific playbook | < 200ms |
| **PUT** | `/contract-playbooks/:id` | Update playbook | < 500ms |
| **DELETE** | `/contract-playbooks/:id` | Delete playbook | < 300ms |
| **POST** | `/contract-playbooks/analyze` | **Analyze contract** | **10-30s** |
| **GET** | `/contract-playbooks/analyses` | List analyses | < 500ms |
| **GET** | `/contract-playbooks/analyses/:id` | Get specific analysis | < 200ms |
| **DELETE** | `/contract-playbooks/analyses/:id` | Delete analysis | < 300ms |
| **GET** | `/contract-playbooks/:id/analytics` | Get usage analytics | < 1s |
| **POST** | `/contract-playbooks/:id/duplicate` | Duplicate playbook | < 1s |
| **GET** | `/contract-playbooks/:id/export` | Export playbook | < 500ms |
| **POST** | `/contract-playbooks/import` | Import playbook | < 1s |

## 🔑 Authentication

```typescript
const headers = {
  'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
  'Content-Type': 'application/json',
};
```

## 📊 Key Data Models

### ContractPlaybook
```typescript
interface ContractPlaybook {
  id: string;
  name: string;
  contractType: 'nda' | 'employment' | 'service_agreement' | 'lease' | 'purchase' | 'partnership' | 'licensing' | 'other';
  description?: string;
  version: string;
  rules: PlaybookRule[];
  metadata: Record<string, any>;
  isActive: boolean;
  isTemplate: boolean;
  createdAt: string;
  updatedAt: string;
}
```

### ContractAnalysis
```typescript
interface ContractAnalysis {
  id: string;
  contractId: string;
  playbookId: string;
  playbookName: string;
  overallScore: number; // 0-100
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  deviations: Deviation[];
  summary: AnalysisSummary;
  metrics: AnalysisMetrics;
  analyzedAt: string;
}
```

## 🎯 Common Use Cases

### 1. List Playbooks with Search
```typescript
const { data } = useQuery({
  queryKey: ['playbooks', { query: searchTerm, contractType: 'nda' }],
  queryFn: () => fetchPlaybooks({ query: searchTerm, contractType: 'nda' }),
});
```

### 2. Create New Playbook
```typescript
const createMutation = useMutation({
  mutationFn: (data: CreatePlaybookRequest) => 
    fetch('/api/contract-playbooks', {
      method: 'POST',
      headers,
      body: JSON.stringify(data),
    }).then(res => res.json()),
  onSuccess: () => queryClient.invalidateQueries(['playbooks']),
});
```

### 3. Analyze Contract (Long-running)
```typescript
const [isAnalyzing, setIsAnalyzing] = useState(false);

const analyzeContract = async () => {
  setIsAnalyzing(true);
  try {
    const response = await fetch('/api/contract-playbooks/analyze', {
      method: 'POST',
      headers,
      body: JSON.stringify({
        contractId: 'contract-uuid',
        playbookId: 'playbook-uuid',
        options: { aiAnalysis: true, includeRecommendations: true }
      }),
    });
    const analysis = await response.json();
    // Handle success
  } catch (error) {
    // Handle error
  } finally {
    setIsAnalyzing(false);
  }
};
```

### 4. Get Analytics Dashboard
```typescript
const { data: analytics } = useQuery({
  queryKey: ['analytics', playbookId],
  queryFn: () => fetchPlaybookAnalytics(playbookId),
  staleTime: 10 * 60 * 1000, // 10 minutes
});
```

## 🚨 Error Handling

### HTTP Status Codes
- **400**: Bad Request (validation errors)
- **401**: Unauthorized (invalid/expired token)
- **403**: Forbidden (feature not available in subscription)
- **404**: Not Found (resource doesn't exist)
- **409**: Conflict (duplicate name, etc.)
- **500**: Internal Server Error

### Error Response Format
```typescript
interface ApiError {
  statusCode: number;
  message: string;
  error: string;
  details?: any;
  path: string;
  timestamp: string;
}
```

### Centralized Error Handler
```typescript
const handleApiError = (error: any): string => {
  if (error.response?.status === 403) {
    return 'This feature requires a premium subscription';
  }
  return error.response?.data?.message || 'An error occurred';
};
```

## ⚡ Performance Tips

### 1. Caching Strategy
```typescript
// Short cache for frequently changing data
const usePlaybooks = () => useQuery({
  queryKey: ['playbooks'],
  queryFn: fetchPlaybooks,
  staleTime: 5 * 60 * 1000, // 5 minutes
});

// Longer cache for analytics
const useAnalytics = (id) => useQuery({
  queryKey: ['analytics', id],
  queryFn: () => fetchAnalytics(id),
  staleTime: 10 * 60 * 1000, // 10 minutes
});
```

### 2. Debounced Search
```typescript
const [searchTerm, setSearchTerm] = useState('');
const [debouncedTerm] = useDebounce(searchTerm, 300);

const { data } = useQuery({
  queryKey: ['playbooks', { query: debouncedTerm }],
  queryFn: () => fetchPlaybooks({ query: debouncedTerm }),
  enabled: debouncedTerm.length >= 2,
});
```

### 3. Optimistic Updates
```typescript
const updateMutation = useMutation({
  mutationFn: updatePlaybook,
  onMutate: async (variables) => {
    // Cancel queries and snapshot previous value
    await queryClient.cancelQueries(['playbook', variables.id]);
    const previous = queryClient.getQueryData(['playbook', variables.id]);
    
    // Optimistically update
    queryClient.setQueryData(['playbook', variables.id], {
      ...previous,
      ...variables.data,
    });
    
    return { previous };
  },
  onError: (err, variables, context) => {
    // Rollback on error
    queryClient.setQueryData(['playbook', variables.id], context.previous);
  },
});
```

## 🎨 UI Components

### Loading States
```typescript
// For quick operations (< 2s)
{isLoading && <Spinner />}

// For analysis (10-30s)
{isAnalyzing && (
  <div className="analysis-progress">
    <ProgressBar progress={progress} />
    <p>Analyzing contract... This may take 10-30 seconds</p>
    <div className="steps">
      <Step active={progress > 20}>📄 Processing document</Step>
      <Step active={progress > 50}>🤖 AI analysis</Step>
      <Step active={progress > 80}>⚖️ Rule evaluation</Step>
    </div>
  </div>
)}
```

### Error Boundaries
```typescript
const ErrorBoundary = ({ children }) => {
  const [hasError, setHasError] = useState(false);
  
  if (hasError) {
    return (
      <div className="error-boundary">
        <h2>Something went wrong</h2>
        <button onClick={() => setHasError(false)}>Try Again</button>
      </div>
    );
  }
  
  return children;
};
```

## 🔄 Real-time Updates

### Polling for Analysis Status
```typescript
const useAnalysisStatus = (analysisId: string) => {
  return useQuery({
    queryKey: ['analysis', analysisId],
    queryFn: () => fetchAnalysis(analysisId),
    refetchInterval: (data) => {
      // Poll every 2 seconds if still in progress
      return data?.status === 'IN_PROGRESS' ? 2000 : false;
    },
  });
};
```

## 📱 Mobile Considerations

### Responsive Design
- Use appropriate loading states for slower connections
- Consider pagination limits (10-15 items on mobile)
- Implement pull-to-refresh for lists
- Use skeleton screens for better perceived performance

### Offline Support
```typescript
const useOfflinePlaybooks = () => {
  const { data, isLoading } = useQuery({
    queryKey: ['playbooks'],
    queryFn: fetchPlaybooks,
    staleTime: Infinity, // Keep data fresh indefinitely when offline
    cacheTime: 24 * 60 * 60 * 1000, // 24 hours
  });
  
  return {
    playbooks: data?.playbooks || [],
    isOffline: !navigator.onLine,
    isLoading,
  };
};
```

## 🧪 Testing

### Mock API Responses
```typescript
// __mocks__/contractPlaybooks.ts
export const mockPlaybooks = {
  playbooks: [
    {
      id: '1',
      name: 'Standard NDA Playbook',
      contractType: 'nda',
      rules: [],
      isActive: true,
    },
  ],
  total: 1,
  page: 1,
  limit: 20,
};

export const mockAnalysis = {
  id: '1',
  overallScore: 85,
  riskLevel: 'MEDIUM',
  status: 'COMPLETED',
  deviations: [],
};
```

### Component Testing
```typescript
// PlaybookList.test.tsx
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { PlaybookList } from './PlaybookList';

test('renders playbook list', async () => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } },
  });
  
  render(
    <QueryClientProvider client={queryClient}>
      <PlaybookList />
    </QueryClientProvider>
  );
  
  expect(await screen.findByText('Standard NDA Playbook')).toBeInTheDocument();
});
```

## 📋 Checklist for Implementation

### ✅ Setup
- [ ] Install React Query and Axios
- [ ] Setup authentication interceptors
- [ ] Configure error handling
- [ ] Setup TypeScript types

### ✅ Core Features
- [ ] Playbook list with search/filter
- [ ] Create/edit playbook forms
- [ ] Contract analysis workflow
- [ ] Analysis results display
- [ ] Analytics dashboard

### ✅ Advanced Features
- [ ] Export/import functionality
- [ ] Duplicate playbook
- [ ] Real-time analysis status
- [ ] Offline support

### ✅ Testing
- [ ] Unit tests for components
- [ ] Integration tests for API calls
- [ ] E2E tests for critical workflows
- [ ] Error scenario testing

This quick reference provides everything needed to get started with the Contract Playbooks API integration! 🚀

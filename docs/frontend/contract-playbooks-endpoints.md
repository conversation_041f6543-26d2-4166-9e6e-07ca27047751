# Contract Playbooks API - Frontend Integration Guide

## 🚀 Quick Start

### Base Configuration

```typescript
const API_BASE_URL =
  process.env.REACT_APP_API_URL || 'http://localhost:4000/api';
const ENDPOINTS = {
  PLAYBOOKS: '/contract-playbooks',
  ANALYZE: '/contract-playbooks/analyze',
  ANALYSES: '/contract-playbooks/analyses',
  ANALYTICS: (id: string) => `/contract-playbooks/${id}/analytics`,
  DUPLICATE: (id: string) => `/contract-playbooks/${id}/duplicate`,
  EXPORT: (id: string) => `/contract-playbooks/${id}/export`,
  IMPORT: '/contract-playbooks/import',
};
```

### Authentication Headers

```typescript
const getAuthHeaders = () => ({
  Authorization: `Bearer ${localStorage.getItem('authToken')}`,
  'Content-Type': 'application/json',
});
```

## 📋 Core Playbook Management

### 1. List All Playbooks

**GET** `/api/contract-playbooks`

```typescript
// Request
interface ListPlaybooksParams {
  query?: string; // Search in name/description
  contractType?: string; // 'nda', 'employment', etc.
  isActive?: boolean; // Filter by active status
  isTemplate?: boolean; // Filter by template status
  page?: number; // Page number (default: 1)
  limit?: number; // Items per page (default: 20)
}

// Response
interface PlaybooksResponse {
  playbooks: ContractPlaybook[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Implementation
const fetchPlaybooks = async (
  params?: ListPlaybooksParams,
): Promise<PlaybooksResponse> => {
  const queryString = new URLSearchParams(params as any).toString();
  const response = await fetch(
    `${API_BASE_URL}/contract-playbooks?${queryString}`,
    {
      headers: getAuthHeaders(),
    },
  );

  if (!response.ok) throw new Error('Failed to fetch playbooks');
  return response.json();
};

// Usage Example
const { data: playbooks, isLoading } = useQuery({
  queryKey: ['playbooks', filters],
  queryFn: () => fetchPlaybooks(filters),
});
```

### 2. Create New Playbook

**POST** `/api/contract-playbooks`

```typescript
// Request
interface CreatePlaybookRequest {
  name: string;
  contractType:
    | 'nda'
    | 'employment'
    | 'service_agreement'
    | 'lease'
    | 'purchase'
    | 'partnership'
    | 'licensing'
    | 'other';
  description?: string;
  version: string;
  rules: PlaybookRule[];
  metadata: {
    industry?: string;
    jurisdiction?: string;
    riskProfile?: string;
    tags?: string[];
  };
  isActive?: boolean;
  isTemplate?: boolean;
}

// Implementation
const createPlaybook = async (
  data: CreatePlaybookRequest,
): Promise<ContractPlaybook> => {
  const response = await fetch(`${API_BASE_URL}/contract-playbooks`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to create playbook');
  }

  return response.json();
};

// Usage Example
const createMutation = useMutation({
  mutationFn: createPlaybook,
  onSuccess: (newPlaybook) => {
    queryClient.invalidateQueries({ queryKey: ['playbooks'] });
    toast.success(`Playbook "${newPlaybook.name}" created successfully`);
  },
  onError: (error) => {
    toast.error(error.message);
  },
});
```

### 3. Get Specific Playbook

**GET** `/api/contract-playbooks/:id`

```typescript
// Implementation
const fetchPlaybook = async (id: string): Promise<ContractPlaybook> => {
  const response = await fetch(`${API_BASE_URL}/contract-playbooks/${id}`, {
    headers: getAuthHeaders(),
  });

  if (!response.ok) {
    if (response.status === 404) throw new Error('Playbook not found');
    throw new Error('Failed to fetch playbook');
  }

  return response.json();
};

// Usage Example
const {
  data: playbook,
  isLoading,
  error,
} = useQuery({
  queryKey: ['playbook', id],
  queryFn: () => fetchPlaybook(id),
  enabled: !!id,
});
```

### 4. Update Playbook

**PUT** `/api/contract-playbooks/:id`

```typescript
// Request
interface UpdatePlaybookRequest {
  name?: string;
  description?: string;
  version?: string;
  rules?: PlaybookRule[];
  metadata?: Record<string, any>;
  isActive?: boolean;
  isTemplate?: boolean;
}

// Implementation
const updatePlaybook = async (
  id: string,
  data: UpdatePlaybookRequest,
): Promise<ContractPlaybook> => {
  const response = await fetch(`${API_BASE_URL}/contract-playbooks/${id}`, {
    method: 'PUT',
    headers: getAuthHeaders(),
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json();
    if (response.status === 409)
      throw new Error('Playbook name already exists');
    throw new Error(error.message || 'Failed to update playbook');
  }

  return response.json();
};

// Usage Example
const updateMutation = useMutation({
  mutationFn: ({ id, data }: { id: string; data: UpdatePlaybookRequest }) =>
    updatePlaybook(id, data),
  onSuccess: (updatedPlaybook) => {
    queryClient.invalidateQueries({ queryKey: ['playbooks'] });
    queryClient.invalidateQueries({
      queryKey: ['playbook', updatedPlaybook.id],
    });
    toast.success('Playbook updated successfully');
  },
});
```

### 5. Delete Playbook

**DELETE** `/api/contract-playbooks/:id`

```typescript
// Implementation
const deletePlaybook = async (id: string): Promise<void> => {
  const response = await fetch(`${API_BASE_URL}/contract-playbooks/${id}`, {
    method: 'DELETE',
    headers: getAuthHeaders(),
  });

  if (!response.ok) {
    const error = await response.json();
    if (response.status === 400) {
      throw new Error('Cannot delete playbook - it is being used in analyses');
    }
    throw new Error(error.message || 'Failed to delete playbook');
  }
};

// Usage Example
const deleteMutation = useMutation({
  mutationFn: deletePlaybook,
  onSuccess: () => {
    queryClient.invalidateQueries({ queryKey: ['playbooks'] });
    toast.success('Playbook deleted successfully');
  },
  onError: (error) => {
    toast.error(error.message);
  },
});

// Component Usage
const handleDelete = async (playbook: ContractPlaybook) => {
  const confirmed = window.confirm(
    `Are you sure you want to delete "${playbook.name}"? This action cannot be undone.`,
  );

  if (confirmed) {
    deleteMutation.mutate(playbook.id);
  }
};
```

## 🔍 Contract Analysis

### 6. Analyze Contract

**POST** `/api/contract-playbooks/analyze`

```typescript
// Request
interface AnalyzeContractRequest {
  contractId: string;
  playbookId: string;
  options?: {
    includeRecommendations?: boolean;
    riskThreshold?: number; // 1-5
    aiAnalysis?: boolean;
    detailedReport?: boolean;
  };
}

// Response
interface ContractAnalysis {
  id: string;
  contractId: string;
  playbookId: string;
  playbookName: string;
  overallScore: number; // 0-100
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  deviations: Deviation[];
  summary: AnalysisSummary;
  metrics: AnalysisMetrics;
  analyzedAt: string;
  createdAt: string;
}

// Implementation
const analyzeContract = async (
  data: AnalyzeContractRequest,
): Promise<ContractAnalysis> => {
  const response = await fetch(`${API_BASE_URL}/contract-playbooks/analyze`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Analysis failed');
  }

  return response.json();
};

// Usage Example with Loading State
const AnalysisComponent = ({ contractId, playbookId }: Props) => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysis, setAnalysis] = useState<ContractAnalysis | null>(null);

  const handleAnalyze = async () => {
    setIsAnalyzing(true);
    try {
      const result = await analyzeContract({
        contractId,
        playbookId,
        options: {
          includeRecommendations: true,
          aiAnalysis: true,
          detailedReport: true,
        },
      });
      setAnalysis(result);
      toast.success('Analysis completed successfully');
    } catch (error) {
      toast.error(error.message);
    } finally {
      setIsAnalyzing(false);
    }
  };

  return (
    <div>
      <button
        onClick={handleAnalyze}
        disabled={isAnalyzing}
        className="btn-primary"
      >
        {isAnalyzing ? 'Analyzing... (10-30s)' : 'Start Analysis'}
      </button>

      {isAnalyzing && <AnalysisProgress />}
      {analysis && <AnalysisResults analysis={analysis} />}
    </div>
  );
};
```

### 7. List Contract Analyses

**GET** `/api/contract-playbooks/analyses`

```typescript
// Request
interface ListAnalysesParams {
  contractId?: string;
  playbookId?: string;
  riskLevel?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  status?: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  startDate?: string; // ISO 8601
  endDate?: string; // ISO 8601
  page?: number;
  limit?: number;
}

// Response
interface AnalysesResponse {
  analyses: ContractAnalysis[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Implementation
const fetchAnalyses = async (
  params?: ListAnalysesParams,
): Promise<AnalysesResponse> => {
  const queryString = new URLSearchParams(params as any).toString();
  const response = await fetch(
    `${API_BASE_URL}/contract-playbooks/analyses?${queryString}`,
    {
      headers: getAuthHeaders(),
    },
  );

  if (!response.ok) throw new Error('Failed to fetch analyses');
  return response.json();
};

// Usage Example
const AnalysesList = () => {
  const [filters, setFilters] = useState<ListAnalysesParams>({
    page: 1,
    limit: 20,
  });

  const { data, isLoading } = useQuery({
    queryKey: ['analyses', filters],
    queryFn: () => fetchAnalyses(filters),
  });

  return (
    <div>
      <AnalysisFilters filters={filters} onChange={setFilters} />

      {isLoading ? (
        <LoadingSpinner />
      ) : (
        <div>
          {data?.analyses.map((analysis) => (
            <AnalysisCard key={analysis.id} analysis={analysis} />
          ))}
          <Pagination
            currentPage={data?.page || 1}
            totalPages={data?.totalPages || 1}
            onPageChange={(page) => setFilters({ ...filters, page })}
          />
        </div>
      )}
    </div>
  );
};
```

### 8. Get Specific Analysis

**GET** `/api/contract-playbooks/analyses/:id`

```typescript
// Implementation
const fetchAnalysis = async (id: string): Promise<ContractAnalysis> => {
  const response = await fetch(
    `${API_BASE_URL}/contract-playbooks/analyses/${id}`,
    {
      headers: getAuthHeaders(),
    },
  );

  if (!response.ok) {
    if (response.status === 404) throw new Error('Analysis not found');
    throw new Error('Failed to fetch analysis');
  }

  return response.json();
};

// Usage Example
const AnalysisDetails = ({ analysisId }: { analysisId: string }) => {
  const { data: analysis, isLoading } = useQuery({
    queryKey: ['analysis', analysisId],
    queryFn: () => fetchAnalysis(analysisId),
  });

  if (isLoading) return <LoadingSpinner />;
  if (!analysis) return <div>Analysis not found</div>;

  return (
    <div className="analysis-details">
      <AnalysisHeader analysis={analysis} />
      <AnalysisMetrics metrics={analysis.metrics} />
      <DeviationsList deviations={analysis.deviations} />
      <AnalysisSummary summary={analysis.summary} />
    </div>
  );
};
```

### 9. Delete Analysis

**DELETE** `/api/contract-playbooks/analyses/:id`

````typescript
// Implementation
const deleteAnalysis = async (id: string): Promise<void> => {
  const response = await fetch(`${API_BASE_URL}/contract-playbooks/analyses/${id}`, {
    method: 'DELETE',
    headers: getAuthHeaders(),
  });

  if (!response.ok) {
    throw new Error('Failed to delete analysis');
  }
};

// Usage Example
const deleteMutation = useMutation({
  mutationFn: deleteAnalysis,
  onSuccess: () => {
    queryClient.invalidateQueries({ queryKey: ['analyses'] });
    toast.success('Analysis deleted successfully');
  },
});

## 📊 Analytics & Utilities

### 10. Get Playbook Analytics
**GET** `/api/contract-playbooks/:id/analytics`

```typescript
// Response
interface PlaybookAnalytics {
  playbookId: string;
  totalAnalyses: number;
  averageScore: number;
  riskDistribution: {
    LOW: number;
    MEDIUM: number;
    HIGH: number;
    CRITICAL: number;
  };
  commonDeviations: Array<{
    ruleId: string;
    ruleName: string;
    frequency: number;
    averageSeverity: string;
  }>;
  performanceMetrics: {
    averageProcessingTime: number;
    averageConfidenceScore: number;
  };
  timeSeriesData: Array<{
    date: string;
    analysisCount: number;
    averageScore: number;
  }>;
}

// Implementation
const fetchPlaybookAnalytics = async (id: string): Promise<PlaybookAnalytics> => {
  const response = await fetch(`${API_BASE_URL}/contract-playbooks/${id}/analytics`, {
    headers: getAuthHeaders(),
  });

  if (!response.ok) throw new Error('Failed to fetch analytics');
  return response.json();
};

// Usage Example
const PlaybookAnalyticsDashboard = ({ playbookId }: { playbookId: string }) => {
  const { data: analytics, isLoading } = useQuery({
    queryKey: ['playbook-analytics', playbookId],
    queryFn: () => fetchPlaybookAnalytics(playbookId),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  if (isLoading) return <LoadingSpinner />;
  if (!analytics) return <div>No analytics available</div>;

  return (
    <div className="analytics-dashboard">
      <div className="metrics-grid">
        <MetricCard
          title="Total Analyses"
          value={analytics.totalAnalyses}
        />
        <MetricCard
          title="Average Score"
          value={`${analytics.averageScore.toFixed(1)}%`}
        />
        <MetricCard
          title="Avg Processing Time"
          value={`${(analytics.performanceMetrics.averageProcessingTime / 1000).toFixed(1)}s`}
        />
      </div>

      <RiskDistributionChart data={analytics.riskDistribution} />
      <TimeSeriesChart data={analytics.timeSeriesData} />
      <CommonDeviationsTable deviations={analytics.commonDeviations} />
    </div>
  );
};
````

### 11. Duplicate Playbook

**POST** `/api/contract-playbooks/:id/duplicate`

```typescript
// Request
interface DuplicatePlaybookRequest {
  name: string;
}

// Implementation
const duplicatePlaybook = async (
  id: string,
  name: string,
): Promise<ContractPlaybook> => {
  const response = await fetch(
    `${API_BASE_URL}/contract-playbooks/${id}/duplicate`,
    {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify({ name }),
    },
  );

  if (!response.ok) {
    const error = await response.json();
    if (response.status === 409)
      throw new Error('Playbook name already exists');
    throw new Error(error.message || 'Failed to duplicate playbook');
  }

  return response.json();
};

// Usage Example
const DuplicatePlaybookModal = ({ playbook, onClose }: Props) => {
  const [newName, setNewName] = useState(`${playbook.name} (Copy)`);

  const duplicateMutation = useMutation({
    mutationFn: (name: string) => duplicatePlaybook(playbook.id, name),
    onSuccess: (newPlaybook) => {
      queryClient.invalidateQueries({ queryKey: ['playbooks'] });
      toast.success(`Playbook duplicated as "${newPlaybook.name}"`);
      onClose();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (newName.trim()) {
      duplicateMutation.mutate(newName.trim());
    }
  };

  return (
    <Modal onClose={onClose}>
      <form onSubmit={handleSubmit}>
        <h2>Duplicate Playbook</h2>
        <div className="form-group">
          <label>New Playbook Name:</label>
          <input
            type="text"
            value={newName}
            onChange={(e) => setNewName(e.target.value)}
            required
          />
        </div>
        <div className="form-actions">
          <button type="button" onClick={onClose}>
            Cancel
          </button>
          <button
            type="submit"
            disabled={duplicateMutation.isPending}
            className="btn-primary"
          >
            {duplicateMutation.isPending ? 'Duplicating...' : 'Duplicate'}
          </button>
        </div>
      </form>
    </Modal>
  );
};
```

### 12. Export Playbook

**GET** `/api/contract-playbooks/:id/export`

```typescript
// Response
interface ExportPlaybookResponse {
  playbook: ContractPlaybook;
  exportFormat: string;
  exportedAt: string;
  version: string;
}

// Implementation
const exportPlaybook = async (id: string): Promise<ExportPlaybookResponse> => {
  const response = await fetch(
    `${API_BASE_URL}/contract-playbooks/${id}/export`,
    {
      headers: getAuthHeaders(),
    },
  );

  if (!response.ok) throw new Error('Failed to export playbook');
  return response.json();
};

// Usage Example
const ExportButton = ({ playbookId, playbookName }: Props) => {
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async () => {
    setIsExporting(true);
    try {
      const exportData = await exportPlaybook(playbookId);

      // Download as JSON file
      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json',
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${playbookName
        .replace(/[^a-z0-9]/gi, '_')
        .toLowerCase()}_export.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success('Playbook exported successfully');
    } catch (error) {
      toast.error('Failed to export playbook');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <button
      onClick={handleExport}
      disabled={isExporting}
      className="btn-secondary"
    >
      {isExporting ? 'Exporting...' : 'Export Playbook'}
    </button>
  );
};
```

### 13. Import Playbook

**POST** `/api/contract-playbooks/import`

```typescript
// Request - Send the playbook data directly
interface ImportPlaybookRequest extends CreatePlaybookRequest {
  // Same structure as CreatePlaybookRequest
}

// Implementation
const importPlaybook = async (
  playbookData: ImportPlaybookRequest,
): Promise<ContractPlaybook> => {
  const response = await fetch(`${API_BASE_URL}/contract-playbooks/import`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify(playbookData),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || 'Failed to import playbook');
  }

  return response.json();
};

// Usage Example
const ImportPlaybookModal = ({ onClose }: Props) => {
  const [file, setFile] = useState<File | null>(null);
  const [isImporting, setIsImporting] = useState(false);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile && selectedFile.type === 'application/json') {
      setFile(selectedFile);
    } else {
      toast.error('Please select a valid JSON file');
    }
  };

  const handleImport = async () => {
    if (!file) return;

    setIsImporting(true);
    try {
      const fileContent = await file.text();
      const playbookData = JSON.parse(fileContent);

      // Extract playbook data if it's wrapped in export format
      const importData = playbookData.playbook || playbookData;

      const importedPlaybook = await importPlaybook(importData);

      queryClient.invalidateQueries({ queryKey: ['playbooks'] });
      toast.success(
        `Playbook "${importedPlaybook.name}" imported successfully`,
      );
      onClose();
    } catch (error) {
      if (error instanceof SyntaxError) {
        toast.error('Invalid JSON file format');
      } else {
        toast.error(error.message || 'Failed to import playbook');
      }
    } finally {
      setIsImporting(false);
    }
  };

  return (
    <Modal onClose={onClose}>
      <div className="import-modal">
        <h2>Import Playbook</h2>

        <div className="file-upload">
          <input
            type="file"
            accept=".json"
            onChange={handleFileChange}
            id="playbook-file"
          />
          <label htmlFor="playbook-file" className="file-upload-label">
            {file ? file.name : 'Choose JSON file...'}
          </label>
        </div>

        <div className="form-actions">
          <button type="button" onClick={onClose}>
            Cancel
          </button>
          <button
            onClick={handleImport}
            disabled={!file || isImporting}
            className="btn-primary"
          >
            {isImporting ? 'Importing...' : 'Import Playbook'}
          </button>
        </div>
      </div>
    </Modal>
  );
};
```

## 🚨 Error Handling

### Common Error Patterns

```typescript
// Centralized error handler
const handleApiError = (error: any): string => {
  if (error.response?.data) {
    const apiError = error.response.data;

    switch (apiError.statusCode) {
      case 400:
        return `Invalid request: ${apiError.message}`;
      case 401:
        // Redirect to login
        window.location.href = '/login';
        return 'Please log in to continue';
      case 403:
        return 'This feature is not available in your current subscription plan';
      case 404:
        return 'The requested resource was not found';
      case 409:
        return `Conflict: ${apiError.message}`;
      case 500:
        return 'Server error. Please try again later';
      default:
        return apiError.message || 'An unexpected error occurred';
    }
  }

  return 'Network error. Please check your connection';
};

// Usage in components
const MyComponent = () => {
  const mutation = useMutation({
    mutationFn: createPlaybook,
    onError: (error) => {
      const errorMessage = handleApiError(error);
      toast.error(errorMessage);
    },
  });
};
```

### Loading States

```typescript
// Analysis with progress tracking
const AnalysisProgress = () => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress((prev) => Math.min(prev + 1, 95)); // Don't reach 100% until complete
    }, 300); // Update every 300ms

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="analysis-progress">
      <div className="progress-bar">
        <div className="progress-fill" style={{ width: `${progress}%` }} />
      </div>
      <p>Analyzing contract... This may take 10-30 seconds</p>
      <div className="progress-steps">
        <div className={progress > 20 ? 'completed' : 'active'}>
          📄 Processing document
        </div>
        <div
          className={
            progress > 50 ? 'completed' : progress > 20 ? 'active' : ''
          }
        >
          🤖 AI analysis
        </div>
        <div
          className={
            progress > 80 ? 'completed' : progress > 50 ? 'active' : ''
          }
        >
          ⚖️ Rule evaluation
        </div>
        <div
          className={
            progress > 95 ? 'completed' : progress > 80 ? 'active' : ''
          }
        >
          📊 Generating report
        </div>
      </div>
    </div>
  );
};
```

## 🎯 Best Practices

### 1. Caching Strategy

```typescript
// React Query configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes for most data
      cacheTime: 10 * 60 * 1000, // 10 minutes in cache
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false;
        }
        return failureCount < 3;
      },
    },
  },
});

// Specific cache times
const usePlaybooks = (params) =>
  useQuery({
    queryKey: ['playbooks', params],
    queryFn: () => fetchPlaybooks(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

const useAnalytics = (id) =>
  useQuery({
    queryKey: ['analytics', id],
    queryFn: () => fetchPlaybookAnalytics(id),
    staleTime: 10 * 60 * 1000, // 10 minutes (analytics change less frequently)
  });
```

### 2. Optimistic Updates

```typescript
const updatePlaybook = useMutation({
  mutationFn: ({ id, data }) => updatePlaybookApi(id, data),
  onMutate: async ({ id, data }) => {
    // Cancel outgoing refetches
    await queryClient.cancelQueries({ queryKey: ['playbook', id] });

    // Snapshot previous value
    const previousPlaybook = queryClient.getQueryData(['playbook', id]);

    // Optimistically update
    queryClient.setQueryData(['playbook', id], (old: any) => ({
      ...old,
      ...data,
      updatedAt: new Date().toISOString(),
    }));

    return { previousPlaybook };
  },
  onError: (err, variables, context) => {
    // Rollback on error
    if (context?.previousPlaybook) {
      queryClient.setQueryData(
        ['playbook', variables.id],
        context.previousPlaybook,
      );
    }
  },
  onSettled: (data, error, variables) => {
    // Always refetch after error or success
    queryClient.invalidateQueries({ queryKey: ['playbook', variables.id] });
  },
});
```

### 3. Debounced Search

```typescript
const usePlaybookSearch = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm] = useDebounce(searchTerm, 300);

  const { data, isLoading } = useQuery({
    queryKey: ['playbooks', { query: debouncedSearchTerm }],
    queryFn: () => fetchPlaybooks({ query: debouncedSearchTerm }),
    enabled: debouncedSearchTerm.length >= 2, // Only search with 2+ characters
  });

  return {
    searchTerm,
    setSearchTerm,
    results: data?.playbooks || [],
    isLoading: isLoading && debouncedSearchTerm.length >= 2,
  };
};
```

This comprehensive guide provides everything the frontend team needs to integrate all Contract Playbooks endpoints with proper error handling, loading states, and best practices! 🚀

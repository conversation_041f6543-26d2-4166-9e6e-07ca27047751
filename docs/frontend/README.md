# Contract Playbooks - Frontend Integration Documentation

## 📋 Overview

This directory contains comprehensive documentation for integrating the Contract Playbooks API into your frontend application. The Contract Playbooks feature provides AI-powered contract analysis using customizable rule-based playbooks.

## 📚 Documentation Files

### 1. [Endpoints Guide](./contract-playbooks-endpoints.md) 
**Complete implementation guide with all 13 endpoints**

- ✅ **Fully tested endpoints** with real-world examples
- ✅ **TypeScript implementations** with proper error handling
- ✅ **React Query integration** with caching strategies
- ✅ **Component examples** for common use cases
- ✅ **Loading states** and progress tracking
- ✅ **Best practices** for performance optimization

**Key Sections:**
- Core Playbook Management (CRUD operations)
- Contract Analysis (AI-powered, 10-30 second processing)
- Analytics & Utilities (export/import, duplication)
- Error Handling & Loading States
- Performance Optimization

### 2. [Quick Reference](./quick-reference.md)
**Condensed reference for rapid development**

- 🚀 **Endpoint summary** with response times
- 🔑 **Authentication patterns**
- 📊 **Key data models**
- 🎯 **Common use cases** with code snippets
- 🚨 **Error handling** strategies
- ⚡ **Performance tips**
- 📱 **Mobile considerations**
- 🧪 **Testing examples**

## 🚀 Quick Start

### 1. Install Dependencies
```bash
npm install @tanstack/react-query axios react-hot-toast
```

### 2. Setup API Client
```typescript
// api/client.ts
import axios from 'axios';

export const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:4000/api',
});

apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

### 3. Setup React Query
```typescript
// App.tsx
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: (failureCount, error: any) => {
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false; // Don't retry 4xx errors
        }
        return failureCount < 3;
      },
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <YourApp />
    </QueryClientProvider>
  );
}
```

### 4. Copy TypeScript Types
Copy the complete type definitions from [contract-playbooks-types.ts](../api/contract-playbooks-types.ts) into your project.

## 🏗️ Architecture Overview

```
Frontend Application
├── 🔐 Authentication Layer
│   ├── JWT token management
│   ├── Automatic token refresh
│   └── Route protection
├── 🌐 API Layer
│   ├── Axios client with interceptors
│   ├── Error handling middleware
│   └── Request/response transformation
├── 📊 Data Layer (React Query)
│   ├── Caching strategies
│   ├── Background refetching
│   ├── Optimistic updates
│   └── Offline support
├── 🎣 Hooks Layer
│   ├── usePlaybooks()
│   ├── useAnalyzeContract()
│   ├── useAnalytics()
│   └── Custom business logic hooks
└── 🎨 UI Components
    ├── PlaybookList
    ├── ContractAnalyzer
    ├── AnalysisResults
    └── AnalyticsDashboard
```

## 🔑 Key Features

### ✅ **Playbook Management**
- Create, read, update, delete playbooks
- Search and filter capabilities
- Template system for reusable playbooks
- Version control and metadata management
- Duplicate and export/import functionality

### ✅ **Contract Analysis**
- AI-powered analysis (10-30 second processing time)
- Rule-based compliance checking
- Risk assessment and scoring (0-100 scale)
- Detailed deviation reporting with recommendations
- Real-time progress tracking

### ✅ **Analytics & Reporting**
- Usage analytics and performance metrics
- Risk distribution analysis
- Common deviation patterns
- Time-series data visualization
- Export capabilities for reporting

## 📊 Performance Characteristics

### API Response Times
- **CRUD Operations**: < 500ms
- **Search/Filter**: < 300ms
- **Analytics**: < 1s
- **Contract Analysis**: 10-30s (AI processing)
- **Export/Import**: < 1s

### Recommended Caching
- **Playbooks**: 5 minutes (frequently updated)
- **Analyses**: 2 minutes (results don't change)
- **Analytics**: 10 minutes (aggregated data)

## 🚨 Error Handling

### Feature Gating
The Contract Playbooks feature requires the `contract_playbooks` subscription feature. Handle the 403 Forbidden response appropriately:

```typescript
if (error.response?.status === 403) {
  // Show upgrade prompt or disable feature
  showUpgradeModal('Contract Playbooks requires a premium subscription');
}
```

### Common Error Scenarios
1. **401 Unauthorized**: Redirect to login
2. **403 Forbidden**: Feature not available in subscription
3. **404 Not Found**: Resource doesn't exist
4. **409 Conflict**: Duplicate names, validation errors
5. **500 Server Error**: Show retry option

## 🎯 Implementation Priorities

### Phase 1: Core Functionality
1. ✅ **Playbook List** - Display and search playbooks
2. ✅ **Create Playbook** - Basic form with validation
3. ✅ **Contract Analysis** - Core analysis workflow
4. ✅ **Analysis Results** - Display analysis outcomes

### Phase 2: Enhanced Features
5. ✅ **Edit/Delete Playbooks** - Full CRUD operations
6. ✅ **Analytics Dashboard** - Usage metrics and insights
7. ✅ **Export/Import** - Data portability
8. ✅ **Duplicate Playbooks** - Template functionality

### Phase 3: Advanced Features
9. ✅ **Real-time Analysis** - Progress tracking and status updates
10. ✅ **Advanced Filtering** - Complex search and filter options
11. ✅ **Bulk Operations** - Multiple playbook management
12. ✅ **Mobile Optimization** - Responsive design and offline support

## 🧪 Testing Strategy

### Unit Tests
- Component rendering and interaction
- Hook behavior and state management
- API service functions
- Error handling scenarios

### Integration Tests
- API integration with mock responses
- End-to-end user workflows
- Error boundary functionality
- Loading state management

### E2E Tests
- Complete playbook creation workflow
- Contract analysis from start to finish
- Analytics dashboard functionality
- Export/import operations

## 📱 Mobile Considerations

### Responsive Design
- Optimize for touch interactions
- Use appropriate loading states for slower connections
- Implement pull-to-refresh for data lists
- Consider reduced pagination limits (10-15 items)

### Performance
- Implement skeleton screens for better perceived performance
- Use lazy loading for large lists
- Optimize images and assets
- Consider offline functionality for core features

## 🔄 Real-time Features

### Analysis Progress Tracking
```typescript
// Poll for analysis status every 2 seconds
const { data: analysis } = useQuery({
  queryKey: ['analysis', analysisId],
  queryFn: () => fetchAnalysis(analysisId),
  refetchInterval: (data) => 
    data?.status === 'IN_PROGRESS' ? 2000 : false,
});
```

### Live Updates
- Real-time analysis status updates
- Background refresh for analytics
- Optimistic updates for better UX
- Conflict resolution for concurrent edits

## 📈 Monitoring & Analytics

### Key Metrics to Track
- Feature adoption rates
- Analysis success/failure rates
- Average processing times
- User engagement patterns
- Error frequencies by type

### Performance Monitoring
```typescript
// Track analysis performance
const trackAnalysis = (analysisId: string, duration: number, success: boolean) => {
  analytics.track('contract_analysis_completed', {
    analysisId,
    duration,
    success,
    timestamp: new Date().toISOString(),
  });
};
```

## 🚀 Deployment Checklist

### Pre-deployment
- [ ] All endpoints tested and working
- [ ] Error handling implemented
- [ ] Loading states configured
- [ ] TypeScript types integrated
- [ ] Performance optimizations applied

### Post-deployment
- [ ] Monitor error rates
- [ ] Track feature usage
- [ ] Gather user feedback
- [ ] Performance monitoring setup
- [ ] Documentation updated

## 📞 Support & Resources

### Documentation
- [Complete API Reference](../api/contract-playbooks.md)
- [TypeScript Definitions](../api/contract-playbooks-types.ts)
- [Implementation Examples](../api/contract-playbooks-examples.md)

### Getting Help
1. Check the [Quick Reference](./quick-reference.md) for common patterns
2. Review the [Endpoints Guide](./contract-playbooks-endpoints.md) for detailed examples
3. Verify authentication and feature access
4. Check error responses for specific guidance

### Best Practices
- Always handle the 10-30 second analysis processing time
- Implement proper loading states and progress indicators
- Use optimistic updates for better user experience
- Cache data appropriately to reduce API calls
- Handle offline scenarios gracefully

---

**Last Updated**: January 2025  
**API Version**: 1.0.0  
**Documentation Version**: 1.0.0  
**Status**: ✅ Production Ready - All 13 endpoints tested and working

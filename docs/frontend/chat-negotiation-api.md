# Negotiation Practice API Documentation

## Overview

The Negotiation Practice API is the **primary interface** for practicing negotiation skills through interactive chat-based sessions. Users can practice against AI opponents with different personalities, use pre-built scenarios or create custom ones, and receive real-time feedback to improve their negotiation skills.

**Key Features:**
- 🗣️ **Natural Language Interface**: Practice through conversational chat
- 🎯 **Scenario Management**: Create, clone, and customize negotiation scenarios
- 🤖 **AI Opponents**: Configure AI personality and difficulty levels
- 📊 **Real-time Analytics**: Track relationship metrics and performance
- 📚 **Template Library**: Use pre-built scenarios for common situations
- 🔗 **Document Integration**: Generate scenarios from contract analysis

## Base URL
```
/api/chat-negotiation          # Primary negotiation interface
```

**Advanced Users:** For structured negotiation training with formal moves and advanced analytics, see the [Negotiation Simulator](../features/negotiation-simulator.md) documentation.

## Authentication
All endpoints require Bearer token authentication:
```
Authorization: Bearer <jwt_token>
```

## API Endpoints

### 1. Create Custom Scenario

**Endpoint:** `POST /api/chat-negotiation/scenarios`

**Description:** Creates a new custom negotiation scenario for practice sessions.

**Request Body:**
```typescript
interface CreateScenarioRequest {
  name: string;                // Scenario name
  description: string;         // Scenario description
  industry: string;            // Industry type
  contractType: string;        // Contract type
  difficulty: 'beginner' | 'intermediate' | 'expert';
  parties: {
    name: string;              // Party name
    role: string;              // Party role (buyer, seller, etc.)
    priorities: string[];      // Key priorities
    negotiationStyle: string;  // Negotiation approach
  }[];
  initialOffer?: {
    price?: number;
    currency?: string;
    terms?: string[];
  };
  constraints?: {
    maxRounds?: number;
    timeLimit?: number;        // Minutes
    mustHaveTerms?: string[];
    dealBreakers?: string[];
  };
  tags?: string[];             // Custom tags
}
```

**Response:**
```typescript
interface ScenarioResponse {
  id: string;                  // Scenario ID to use in createSession
  name: string;                // Scenario name
  description: string;         // Scenario description
  industry: string;            // Industry type
  contractType: string;        // Contract type
  difficulty: string;          // Difficulty level
  parties: object[];           // Party configurations
  tags: string[];              // Associated tags
  createdBy: string;           // Creator user ID
  createdAt: string;           // ISO date
  updatedAt: string;           // ISO date
}
```

**Example:**
```javascript
const scenario = await fetch('/api/chat-negotiation/scenarios', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: "Software License Negotiation",
    description: "Practice negotiating software licensing terms",
    industry: "Technology",
    contractType: "Software License",
    difficulty: "intermediate",
    parties: [
      {
        name: "Software Buyer",
        role: "licensee",
        priorities: ["Cost control", "Flexible terms"],
        negotiationStyle: "collaborative"
      },
      {
        name: "Software Vendor",
        role: "licensor",
        priorities: ["Revenue maximization", "Long-term contracts"],
        negotiationStyle: "competitive"
      }
    ],
    constraints: {
      maxRounds: 10,
      timeLimit: 45
    },
    tags: ["software", "licensing"]
  })
});
```

### 2. Get Available Scenarios

**Endpoint:** `GET /api/chat-negotiation/scenarios`

**Description:** Retrieves available negotiation scenarios including user-created and template scenarios.

**Query Parameters:**
- `industry` (optional): Filter by industry
- `contractType` (optional): Filter by contract type
- `difficulty` (optional): Filter by difficulty level
- `tags` (optional): Filter by tags (comma-separated)
- `includeTemplates` (optional): Include pre-built templates (default: true)

**Response:** Array of ScenarioResponse

**Example:**
```javascript
const scenarios = await fetch('/api/chat-negotiation/scenarios?industry=software&difficulty=beginner', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

### 3. Get Template Scenarios

**Endpoint:** `GET /api/chat-negotiation/scenarios/templates`

**Description:** Retrieves pre-built template scenarios available for all users.

**Response:** Array of ScenarioResponse

**Example:**
```javascript
const templates = await fetch('/api/chat-negotiation/scenarios/templates', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

### 4. Clone Scenario

**Endpoint:** `POST /api/chat-negotiation/scenarios/:scenarioId/clone`

**Description:** Creates a copy of an existing scenario for customization.

**Parameters:**
- `scenarioId` (path): Scenario ID to clone

**Request Body (Optional):**
```typescript
interface CloneScenarioRequest {
  name?: string;               // Custom name for clone
  description?: string;        // Custom description
}
```

**Response:** ScenarioResponse

**Example:**
```javascript
const clonedScenario = await fetch(`/api/chat-negotiation/scenarios/${scenarioId}/clone`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: "My Custom Software License Scenario",
    description: "Customized for my practice needs"
  })
});
```

### 5. Update Scenario

**Endpoint:** `PUT /api/chat-negotiation/scenarios/:scenarioId`

**Description:** Updates an existing scenario (only creator can update).

**Parameters:**
- `scenarioId` (path): Scenario ID

**Request Body:** Partial CreateScenarioRequest

**Response:** ScenarioResponse

**Example:**
```javascript
const updatedScenario = await fetch(`/api/chat-negotiation/scenarios/${scenarioId}`, {
  method: 'PUT',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    difficulty: "expert",
    constraints: {
      maxRounds: 15,
      timeLimit: 60
    }
  })
});
```

### 6. Delete Scenario

**Endpoint:** `DELETE /api/chat-negotiation/scenarios/:scenarioId`

**Description:** Deletes a custom scenario (only creator can delete).

**Parameters:**
- `scenarioId` (path): Scenario ID

**Response:**
```typescript
interface DeleteResponse {
  message: string;
  deletedScenarioId: string;
}
```

**Example:**
```javascript
const result = await fetch(`/api/chat-negotiation/scenarios/${scenarioId}`, {
  method: 'DELETE',
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

### 7. Create Negotiation Session

**Endpoint:** `POST /api/chat-negotiation/sessions`

**Description:** Creates a new negotiation session with specified AI personality and scenario. Use scenario IDs from the scenario management endpoints above.

**Request Body:**
```typescript
interface CreateSessionRequest {
  scenarioId: string;           // MongoDB ObjectId of the scenario
  aiPersonality: {
    characterId: string;        // AI character identifier
    aggressiveness: number;     // 0.0 - 1.0 (0 = passive, 1 = aggressive)
    flexibility: number;        // 0.0 - 1.0 (0 = rigid, 1 = flexible)
    riskTolerance: number;      // 0.0 - 1.0 (0 = risk-averse, 1 = risk-taking)
    communicationStyle: 'DIRECT' | 'DIPLOMATIC' | 'ANALYTICAL' | 'EMOTIONAL';
  };
  metadata?: {
    source?: string;           // Optional: tracking source
    version?: string;          // Optional: version info
    [key: string]: any;        // Additional metadata
  };
}
```

**Response:**
```typescript
interface CreateSessionResponse {
  id: string;                  // Session ID
  scenarioId: string;
  status: 'active' | 'completed' | 'paused';
  currentRound: number;        // Starting at 1
  extractedTerms: {
    price?: number;
    currency?: string;
    terms?: string[];
  };
  relationshipMetrics: {
    trust: number;             // 0-100
    respect: number;           // 0-100
    pressure: number;          // 0-100
  };
  score: number;               // Performance score
  aiPersonality: AiPersonality;
  negotiationSessionId: string;
  totalMessages: number;
  aiResponseTime: number;      // Average response time in ms
  createdAt: string;           // ISO date
  updatedAt: string;           // ISO date
  lastActivityAt: string;      // ISO date
}
```

**Example Request:**
```javascript
const response = await fetch('/api/chat-negotiation/sessions', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    scenarioId: "68405c9d35f27e4188e67b72",
    aiPersonality: {
      characterId: "default_character",
      aggressiveness: 0.4,
      flexibility: 0.7,
      riskTolerance: 0.6,
      communicationStyle: "ANALYTICAL"
    },
    metadata: {
      source: "web_app",
      version: "1.0"
    }
  })
});
```

### 8. Get Session Details

**Endpoint:** `GET /api/chat-negotiation/sessions/:id`

**Description:** Retrieves detailed information about a specific negotiation session.

**Parameters:**
- `id` (path): Session ID

**Response:** Same as CreateSessionResponse

**Example:**
```javascript
const session = await fetch(`/api/chat-negotiation/sessions/${sessionId}`, {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

### 9. Get User Sessions

**Endpoint:** `GET /api/chat-negotiation/sessions`

**Description:** Retrieves all negotiation sessions for the authenticated user.

**Query Parameters:**
- `status` (optional): Filter by session status ('active', 'paused', 'completed', 'abandoned')
- `limit` (optional): Number of sessions to return (default: 20)
- `offset` (optional): Number of sessions to skip (default: 0)
- `scenarioId` (optional): Filter by scenario ID

**Response:**
```typescript
interface GetSessionsResponse {
  sessions: CreateSessionResponse[];
  total: number;
  limit: number;
  offset: number;
}
```

**Example:**
```javascript
const sessions = await fetch('/api/chat-negotiation/sessions?limit=10&status=active', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

### 10. Send Chat Move

**Endpoint:** `POST /api/chat-negotiation/sessions/:id/moves`

**Description:** Sends a user message/move in the negotiation and receives AI response.

**Parameters:**
- `id` (path): Session ID

**Request Body:**
```typescript
interface SendMoveRequest {
  content: string;             // User's message content
  extractedData?: {            // Optional: pre-extracted data
    offer?: {
      price?: number;
      currency?: string;
      terms?: string[];
    };
    strategy?: string;
    sentiment?: string;
    confidence?: number;
  };
  context?: {
    userConfidence?: number;   // 0.0 - 1.0
    timeSpent?: number;        // Time spent composing message (seconds)
    [key: string]: any;
  };
}
```

**Response:**
```typescript
interface SendMoveResponse {
  userMessage: {
    content: string;
    extractedData: {
      offer?: {
        price?: number;
        currency?: string;
        terms?: string[];
      };
      strategy: string;
      sentiment: string;
      confidence: number;
    };
    timestamp: string;
  };
  aiResponse: {
    content: string;
    suggestions?: string[];    // Strategic suggestions
    extractedData?: {
      strategy: string;
      sentiment: string;
    };
    timestamp: string;
  };
  sessionUpdate: {
    id: string;
    currentRound: number;
    extractedTerms: {
      price?: number;
      currency?: string;
      terms?: string[];
    };
    relationshipMetrics: {
      trust: number;           // 0-100
      respect: number;         // 0-100
      pressure: number;        // 0-100
    };
    score: number;
  };
  processingTime: number;      // Total processing time in ms
}
```

**Example:**
```javascript
const move = await fetch(`/api/chat-negotiation/sessions/${sessionId}/moves`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    content: "I'm thinking around $50,000 for the software license with Net 30 payment terms.",
    context: {
      userConfidence: 0.8,
      timeSpent: 45
    }
  })
});
```

### 11. Extract Data from Message

**Endpoint:** `POST /api/chat-negotiation/extract-data`

**Description:** Extracts negotiation data from a message without creating a session move. Useful for real-time feedback.

**Request Body:**
```typescript
interface ExtractDataRequest {
  message: string;             // Message to analyze
  context?: {
    scenarioType?: string;     // Optional: scenario context
    currentRound?: number;     // Optional: current negotiation round
    [key: string]: any;
  };
}
```

**Response:**
```typescript
interface ExtractDataResponse {
  offer: {
    price?: number;
    currency?: string;
    terms?: string[];
  };
  strategy: string;            // e.g., "collaborative", "competitive", "accommodating", "analytical"
  sentiment: string;           // e.g., "positive", "neutral", "negative"
  confidence: number;          // 0.0 - 1.0
  extractedEntities: string[]; // List of detected entities
  processingTime: number;      // ms
}
```

**Example:**
```javascript
const extraction = await fetch('/api/chat-negotiation/extract-data', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    message: "I propose $75,000 with Net 60 payment terms and a 10% discount for early payment",
    context: {
      scenarioType: "software_licensing",
      currentRound: 2
    }
  })
});
```

### 12. Create Scenario from Contract Analysis

**Endpoint:** `POST /api/chat-negotiation/scenarios/from-analysis`

**Description:** Generate a negotiation scenario based on contract analysis results.

**Request Body:**
```typescript
interface CreateScenarioFromAnalysisRequest {
  analysisId: string;          // Contract analysis ID
}
```

**Response:**
```typescript
interface CreateScenarioResponse {
  id: string;                  // Generated scenario ID
  name: string;                // Scenario name based on contract
  description: string;         // Scenario description
  contractType: string;        // Type of contract
  keyIssues: string[];         // Issues identified from analysis
  stakeholders: object[];      // Parties involved
  initialPosition: object;     // Starting negotiation position
  createdAt: string;           // ISO date
}
```

**Example:**
```javascript
const scenario = await fetch('/api/chat-negotiation/scenarios/from-analysis', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    analysisId: "60b5d4f3c8d4a1234567890a"
  })
});
```

### 13. Create Session from Document Analysis

**Endpoint:** `POST /api/chat-negotiation/sessions/from-analysis`

**Description:** Start a negotiation practice session based on a previously completed contract analysis.

**Request Body:**
```typescript
interface CreateSessionFromAnalysisRequest {
  analysisId: string;          // Contract analysis ID
  aiPersonality?: {
    characterId: string;
    aggressiveness: number;    // 0.0 - 1.0
    flexibility: number;       // 0.0 - 1.0
    riskTolerance: number;     // 0.0 - 1.0
    communicationStyle: 'DIRECT' | 'DIPLOMATIC' | 'ANALYTICAL' | 'EMOTIONAL';
  };
}
```

**Response:** Same as CreateSessionResponse with additional document context

**Example:**
```javascript
const session = await fetch('/api/chat-negotiation/sessions/from-analysis', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    analysisId: "683d8cfdb5a9a57e1d62dd14",
    aiPersonality: {
      characterId: "contract_specialist",
      aggressiveness: 0.7,
      flexibility: 0.5,
      riskTolerance: 0.2,
      communicationStyle: "ANALYTICAL"
    }
  })
});
```

### 14. Get Conversation History

**Endpoint:** `GET /api/chat-negotiation/sessions/:id/messages`

**Description:** Retrieve the complete message history between user and AI for a specific negotiation session.

**Parameters:**
- `id` (path): Session ID

**Query Parameters:**
- `limit` (optional): Number of messages to return (default: 50)
- `offset` (optional): Number of messages to skip (default: 0)
- `includeMetadata` (optional): Include analytics metadata (default: false)

**Response:**
```typescript
interface ConversationHistoryResponse {
  sessionId: string;
  messages: {
    id: string;
    content: string;            // Message content
    sender: 'user' | 'ai';      // Message sender
    timestamp: string;          // ISO date
    extractedData: {
      strategy?: string;        // Detected strategy
      sentiment?: string;       // Detected sentiment
      topic?: string;           // Message topic
      offer?: {                 // Extracted offers
        price?: number;
        currency?: string;
        terms?: string[];
      };
      confidence?: number;      // Extraction confidence
    };
    // Additional metadata when includeMetadata=true
    relationshipImpact?: {
      trustChange?: number;
      respectChange?: number;
      pressureChange?: number;
    };
    scoreImpact?: number;       // Impact on negotiation score
    processingTimeMs?: number;  // Processing time
    confidence?: number;        // Overall confidence
    detectedStrategy?: string;  // Strategy classification
    sentiment?: string;         // Sentiment classification
    aiResponseMetadata?: {      // AI-specific metadata
      suggestions?: string[];   // AI suggestions
      responseTime?: number;    // AI response time
    };
  }[];
  pagination: {
    total: number;              // Total messages in conversation
    limit: number;              // Current limit
    offset: number;             // Current offset
    hasMore: boolean;           // More messages available
  };
  sessionInfo: {
    currentRound: number;       // Current negotiation round
    totalMessages: number;      // Total message count
    score: number;              // Current negotiation score
    status: string;             // Session status
    relationshipMetrics: {
      trust: number;            // Current trust level (0-100)
      respect: number;          // Current respect level (0-100)
      pressure: number;         // Current pressure level (0-100)
    };
  };
}
```

**Example - Basic History:**
```javascript
const history = await fetch(`/api/chat-negotiation/sessions/${sessionId}/messages`, {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

// Response includes chronological conversation
const conversation = await history.json();
conversation.messages.forEach(message => {
  console.log(`${message.sender}: ${message.content}`);
  console.log(`Strategy: ${message.extractedData.strategy}, Sentiment: ${message.extractedData.sentiment}`);
});
```

**Example - With Pagination:**
```javascript
const history = await fetch(`/api/chat-negotiation/sessions/${sessionId}/messages?limit=10&offset=0`, {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

**Example - With Analytics Metadata:**
```javascript
const history = await fetch(`/api/chat-negotiation/sessions/${sessionId}/messages?includeMetadata=true`, {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

// Response includes detailed analytics for each message
const conversation = await history.json();
conversation.messages.forEach(message => {
  if (message.sender === 'user') {
    console.log(`User message impact: +${message.scoreImpact} score, Trust: +${message.relationshipImpact.trustChange}`);
  } else {
    console.log(`AI suggestions: ${message.aiResponseMetadata.suggestions.join(', ')}`);
  }
});
```

### 15. Get Document Context for Session

**Endpoint:** `GET /api/chat-negotiation/sessions/:id/document-context`

**Description:** Retrieve contract analysis context for document-based negotiation session.

**Parameters:**
- `id` (path): Session ID

**Response:**
```typescript
interface DocumentContextResponse {
  context: string;              // Formatted analysis context
  hasDocumentContext: boolean;  // Whether session has document context
}
```

**Example:**
```javascript
const context = await fetch(`/api/chat-negotiation/sessions/${sessionId}/document-context`, {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

// Example response for contract-based session:
{
  "context": "Contract Analysis Context:\n- Contract Type: CONTRACT\n- Overall Score: 90.45/100\n- Risk Level: HIGH\n- Total Clauses: 11\n\nKey Issues to Negotiate:\n- DATA PROTECTION: Critical section due to strict global data protection regulations...\n- RELATIONSHIP OF THE PARTIES: Crucial section for defining the independent contractor relationship...\n\nNegotiation Focus:\n- Use of Deel platform for payments\n- Automatic annual renewal after initial term\n- Non-solicitation of Client personnel for 1 year post-termination",
  "hasDocumentContext": true
}
```

## Data Types

### AI Personality Configuration
```typescript
interface AiPersonality {
  characterId: string;         // Character identifier
  aggressiveness: number;      // 0.0 - 1.0
  flexibility: number;         // 0.0 - 1.0  
  riskTolerance: number;       // 0.0 - 1.0
  communicationStyle: 'DIRECT' | 'DIPLOMATIC' | 'ANALYTICAL' | 'EMOTIONAL';
}
```

### Relationship Metrics
```typescript
interface RelationshipMetrics {
  trust: number;               // 0-100, higher is better
  respect: number;             // 0-100, higher is better  
  pressure: number;            // 0-100, lower is better
}
```

### Extracted Terms
```typescript
interface ExtractedTerms {
  price?: number;              // Numerical price value
  currency?: string;           // Currency code (e.g., "USD")
  terms?: string[];            // Payment/contract terms
}
```

## Error Handling

All endpoints return standard HTTP status codes:

- `200` - Success
- `201` - Created (for POST requests)
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (session doesn't exist)
- `429` - Too Many Requests (rate limiting)
- `500` - Internal Server Error

**Error Response Format:**
```typescript
interface ErrorResponse {
  statusCode: number;
  message: string;
  error: string;
  details?: any;
  path: string;
  timestamp: string;
}
```

## Processing Notes

- **Chat moves** are processed through AI for negotiation analysis
- **Data extraction** uses pattern matching for quick feedback
- **Response times** vary based on message complexity
- **Session state** is automatically updated after each move

## Rate Limiting

- **60 requests per minute** per user
- Rate limit headers included in responses:
  - `X-RateLimit-Limit`
  - `X-RateLimit-Remaining` 
  - `X-RateLimit-Reset`

## Complete Usage Example

Here's a complete flow for creating and managing a document-based negotiation session:

```javascript
// 1. Create session from contract analysis
const session = await fetch('/api/chat-negotiation/sessions/from-analysis', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    analysisId: "683d8cfdb5a9a57e1d62dd14",
    aiPersonality: {
      characterId: "contract_specialist",
      aggressiveness: 0.7,
      flexibility: 0.5,
      riskTolerance: 0.2,
      communicationStyle: "ANALYTICAL"
    }
  })
});

const sessionData = await session.json();
const sessionId = sessionData.id;

// 2. Get document context for the session
const context = await fetch(`/api/chat-negotiation/sessions/${sessionId}/document-context`, {
  headers: { 'Authorization': `Bearer ${token}` }
});
const contextData = await context.json();

// 3. Send negotiation messages
const move1 = await fetch(`/api/chat-negotiation/sessions/${sessionId}/moves`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    content: "I have concerns about the data protection clause. Can we discuss modifying this to be more balanced?",
    extractedData: {
      strategy: "collaborative",
      sentiment: "negative",
      topic: "data_protection"
    }
  })
});

const moveResponse1 = await move1.json();
console.log('AI Response:', moveResponse1.aiResponse.content);
console.log('Updated Score:', moveResponse1.sessionUpdate.score);
console.log('Trust Level:', moveResponse1.sessionUpdate.relationshipMetrics.trust);

// 4. Continue conversation
const move2 = await fetch(`/api/chat-negotiation/sessions/${sessionId}/moves`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    content: "Yes, let's focus on the data protection clause first. I want to ensure shared responsibility.",
    extractedData: {
      strategy: "collaborative",
      sentiment: "neutral",
      topic: "data_protection"
    }
  })
});

// 5. Get complete conversation history
const history = await fetch(`/api/chat-negotiation/sessions/${sessionId}/messages?includeMetadata=true`, {
  headers: { 'Authorization': `Bearer ${token}` }
});

const conversationData = await history.json();
console.log(`Total messages: ${conversationData.pagination.total}`);
console.log(`Current score: ${conversationData.sessionInfo.score}`);
console.log(`Trust level: ${conversationData.sessionInfo.relationshipMetrics.trust}`);

// Display conversation
conversationData.messages.forEach((message, index) => {
  console.log(`\n${index + 1}. ${message.sender.toUpperCase()}: ${message.content}`);
  if (message.extractedData) {
    console.log(`   Strategy: ${message.extractedData.strategy}, Sentiment: ${message.extractedData.sentiment}`);
  }
  if (message.relationshipImpact && message.sender === 'user') {
    console.log(`   Impact: Trust +${message.relationshipImpact.trustChange}, Score +${message.scoreImpact}`);
  }
});

// 6. Get all user sessions
const allSessions = await fetch('/api/chat-negotiation/sessions?limit=10&status=active', {
  headers: { 'Authorization': `Bearer ${token}` }
});
const sessionsData = await allSessions.json();
console.log(`Active sessions: ${sessionsData.total}`);
```

## WebSocket Events (Future Enhancement)

The system is designed to support real-time updates via WebSocket:

```typescript
// Future WebSocket events
interface NegotiationEvents {
  'session:created': CreateSessionResponse;
  'move:sent': SendMoveResponse;
  'ai:typing': { sessionId: string };
  'session:updated': Partial<CreateSessionResponse>;
  'message:history': ConversationHistoryResponse;
}
```

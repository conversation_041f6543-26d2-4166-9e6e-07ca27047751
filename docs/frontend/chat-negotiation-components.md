# Chat Negotiation UI Components

## Component Examples

### 1. Performance Metrics Component

```typescript
// src/components/chat-negotiation/PerformanceMetrics.tsx
import React from 'react';
import { ProgressBar } from '../ui/ProgressBar';
import type { NegotiationSession } from '../../types/negotiation';

interface PerformanceMetricsProps {
  session: NegotiationSession;
}

export function PerformanceMetrics({ session }: PerformanceMetricsProps) {
  const { relationshipMetrics, score, extractedTerms } = session;

  const getMetricColor = (value: number, isInverse = false) => {
    const threshold = isInverse ? 30 : 70;
    if (isInverse) {
      return value < threshold ? 'bg-green-500' : value < 60 ? 'bg-yellow-500' : 'bg-red-500';
    }
    return value > threshold ? 'bg-green-500' : value > 40 ? 'bg-yellow-500' : 'bg-red-500';
  };

  return (
    <div className="p-6 space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Performance Metrics</h3>
        
        {/* Overall Score */}
        <div className="bg-gray-50 rounded-lg p-4 mb-4">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600">{score.toFixed(1)}</div>
            <div className="text-sm text-gray-600">Overall Score</div>
          </div>
        </div>

        {/* Relationship Metrics */}
        <div className="space-y-3">
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span>Trust</span>
              <span>{relationshipMetrics.trust}%</span>
            </div>
            <ProgressBar 
              value={relationshipMetrics.trust} 
              className={getMetricColor(relationshipMetrics.trust)}
            />
          </div>

          <div>
            <div className="flex justify-between text-sm mb-1">
              <span>Respect</span>
              <span>{relationshipMetrics.respect}%</span>
            </div>
            <ProgressBar 
              value={relationshipMetrics.respect} 
              className={getMetricColor(relationshipMetrics.respect)}
            />
          </div>

          <div>
            <div className="flex justify-between text-sm mb-1">
              <span>Pressure</span>
              <span>{relationshipMetrics.pressure}%</span>
            </div>
            <ProgressBar 
              value={relationshipMetrics.pressure} 
              className={getMetricColor(relationshipMetrics.pressure, true)}
            />
            <div className="text-xs text-gray-500 mt-1">Lower is better</div>
          </div>
        </div>
      </div>

      {/* Current Terms */}
      <div>
        <h4 className="font-medium mb-3">Current Terms</h4>
        <div className="space-y-2 text-sm">
          {extractedTerms.price && (
            <div className="flex justify-between">
              <span>Price:</span>
              <span className="font-medium">
                {extractedTerms.currency || '$'}{extractedTerms.price.toLocaleString()}
              </span>
            </div>
          )}
          {extractedTerms.terms && extractedTerms.terms.length > 0 && (
            <div>
              <span>Terms:</span>
              <ul className="mt-1 space-y-1">
                {extractedTerms.terms.map((term, index) => (
                  <li key={index} className="text-gray-600 ml-2">• {term}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>

      {/* Session Stats */}
      <div>
        <h4 className="font-medium mb-3">Session Stats</h4>
        <div className="grid grid-cols-2 gap-3 text-sm">
          <div className="text-center p-2 bg-gray-50 rounded">
            <div className="font-semibold">{session.currentRound}</div>
            <div className="text-gray-600">Round</div>
          </div>
          <div className="text-center p-2 bg-gray-50 rounded">
            <div className="font-semibold">{session.totalMessages}</div>
            <div className="text-gray-600">Messages</div>
          </div>
          <div className="text-center p-2 bg-gray-50 rounded">
            <div className="font-semibold">{session.aiResponseTime.toFixed(1)}ms</div>
            <div className="text-gray-600">Avg Response</div>
          </div>
          <div className="text-center p-2 bg-gray-50 rounded">
            <div className="font-semibold">{session.status}</div>
            <div className="text-gray-600">Status</div>
          </div>
        </div>
      </div>

      {/* AI Personality */}
      <div>
        <h4 className="font-medium mb-3">AI Opponent</h4>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span>Style:</span>
            <span className="font-medium">{session.aiPersonality.communicationStyle}</span>
          </div>
          <div className="flex justify-between">
            <span>Aggressiveness:</span>
            <span>{(session.aiPersonality.aggressiveness * 100).toFixed(0)}%</span>
          </div>
          <div className="flex justify-between">
            <span>Flexibility:</span>
            <span>{(session.aiPersonality.flexibility * 100).toFixed(0)}%</span>
          </div>
          <div className="flex justify-between">
            <span>Risk Tolerance:</span>
            <span>{(session.aiPersonality.riskTolerance * 100).toFixed(0)}%</span>
          </div>
        </div>
      </div>
    </div>
  );
}
```

### 2. Session Setup Component

```typescript
// src/components/chat-negotiation/SessionSetup.tsx
import React, { useState } from 'react';
import { AIPersonalitySelector } from './AIPersonalitySelector';
import { chatNegotiationApi } from '../../services/chatNegotiationApi';
import type { AiPersonality, CreateSessionRequest } from '../../types/negotiation';

interface SessionSetupProps {
  onSessionCreated: (session: any) => void;
  loading: boolean;
  error: string | null;
}

export function SessionSetup({ onSessionCreated, loading, error }: SessionSetupProps) {
  const [selectedScenario, setSelectedScenario] = useState('');
  const [aiPersonality, setAiPersonality] = useState<AiPersonality>({
    characterId: 'default_character',
    aggressiveness: 0.5,
    flexibility: 0.5,
    riskTolerance: 0.5,
    communicationStyle: 'ANALYTICAL'
  });

  // Mock scenarios - replace with actual API call
  const scenarios = [
    { id: '68405c9d35f27e4188e67b72', name: 'Software License Agreement', description: 'Negotiate terms for enterprise software licensing' },
    { id: '68405c9d35f27e4188e67b73', name: 'Service Contract', description: 'Negotiate a professional services agreement' },
    { id: '68405c9d35f27e4188e67b74', name: 'Real Estate Deal', description: 'Commercial property purchase negotiation' }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedScenario) return;

    try {
      const sessionData: CreateSessionRequest = {
        scenarioId: selectedScenario,
        aiPersonality,
        metadata: {
          source: 'web_app',
          version: '1.0'
        }
      };

      const session = await chatNegotiationApi.createSession(sessionData);
      onSessionCreated(session);
    } catch (err) {
      console.error('Failed to create session:', err);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold mb-2">Start Negotiation Practice</h1>
        <p className="text-gray-600">Choose a scenario and configure your AI opponent</p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Scenario Selection */}
        <div>
          <label className="block text-sm font-medium mb-3">Select Scenario</label>
          <div className="grid gap-3">
            {scenarios.map((scenario) => (
              <label
                key={scenario.id}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  selectedScenario === scenario.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <input
                  type="radio"
                  name="scenario"
                  value={scenario.id}
                  checked={selectedScenario === scenario.id}
                  onChange={(e) => setSelectedScenario(e.target.value)}
                  className="sr-only"
                />
                <div className="font-medium">{scenario.name}</div>
                <div className="text-sm text-gray-600 mt-1">{scenario.description}</div>
              </label>
            ))}
          </div>
        </div>

        {/* AI Personality Configuration */}
        <AIPersonalitySelector
          personality={aiPersonality}
          onChange={setAiPersonality}
        />

        {/* Error Display */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg text-red-700">
            {error}
          </div>
        )}

        {/* Submit Button */}
        <button
          type="submit"
          disabled={!selectedScenario || loading}
          className="w-full py-3 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
        >
          {loading ? 'Creating Session...' : 'Start Negotiation'}
        </button>
      </form>
    </div>
  );
}
```

### 3. AI Personality Selector

```typescript
// src/components/chat-negotiation/AIPersonalitySelector.tsx
import React from 'react';
import type { AiPersonality } from '../../types/negotiation';

interface AIPersonalitySelectorProps {
  personality: AiPersonality;
  onChange: (personality: AiPersonality) => void;
}

export function AIPersonalitySelector({ personality, onChange }: AIPersonalitySelectorProps) {
  const updatePersonality = (field: keyof AiPersonality, value: any) => {
    onChange({ ...personality, [field]: value });
  };

  const communicationStyles = [
    { value: 'DIRECT', label: 'Direct', description: 'Straightforward and to the point' },
    { value: 'DIPLOMATIC', label: 'Diplomatic', description: 'Tactful and relationship-focused' },
    { value: 'ANALYTICAL', label: 'Analytical', description: 'Data-driven and logical' },
    { value: 'EMOTIONAL', label: 'Emotional', description: 'Appeals to feelings and values' }
  ] as const;

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium">AI Opponent Configuration</h3>
      
      {/* Communication Style */}
      <div>
        <label className="block text-sm font-medium mb-3">Communication Style</label>
        <div className="grid grid-cols-2 gap-3">
          {communicationStyles.map((style) => (
            <label
              key={style.value}
              className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                personality.communicationStyle === style.value
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <input
                type="radio"
                name="communicationStyle"
                value={style.value}
                checked={personality.communicationStyle === style.value}
                onChange={(e) => updatePersonality('communicationStyle', e.target.value)}
                className="sr-only"
              />
              <div className="font-medium text-sm">{style.label}</div>
              <div className="text-xs text-gray-600 mt-1">{style.description}</div>
            </label>
          ))}
        </div>
      </div>

      {/* Personality Sliders */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">
            Aggressiveness: {(personality.aggressiveness * 100).toFixed(0)}%
          </label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={personality.aggressiveness}
            onChange={(e) => updatePersonality('aggressiveness', parseFloat(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>Passive</span>
            <span>Aggressive</span>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            Flexibility: {(personality.flexibility * 100).toFixed(0)}%
          </label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={personality.flexibility}
            onChange={(e) => updatePersonality('flexibility', parseFloat(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>Rigid</span>
            <span>Flexible</span>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">
            Risk Tolerance: {(personality.riskTolerance * 100).toFixed(0)}%
          </label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={personality.riskTolerance}
            onChange={(e) => updatePersonality('riskTolerance', parseFloat(e.target.value))}
            className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
          />
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>Risk Averse</span>
            <span>Risk Taking</span>
          </div>
        </div>
      </div>

      {/* Difficulty Preview */}
      <div className="p-3 bg-gray-50 rounded-lg">
        <div className="text-sm font-medium mb-1">Difficulty Preview</div>
        <div className="text-sm text-gray-600">
          {getDifficultyDescription(personality)}
        </div>
      </div>
    </div>
  );
}

function getDifficultyDescription(personality: AiPersonality): string {
  const avgDifficulty = (personality.aggressiveness + (1 - personality.flexibility) + personality.riskTolerance) / 3;
  
  if (avgDifficulty < 0.3) return "Easy opponent - Good for beginners";
  if (avgDifficulty < 0.6) return "Moderate opponent - Balanced challenge";
  return "Challenging opponent - For experienced negotiators";
}
```

### 4. Message Bubble Component

```typescript
// src/components/ui/MessageBubble.tsx
import React from 'react';
import type { ChatMessage } from '../../types/negotiation';

interface MessageBubbleProps {
  message: ChatMessage;
}

export function MessageBubble({ message }: MessageBubbleProps) {
  const isUser = message.sender === 'user';
  
  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'}`}>
      <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
        isUser 
          ? 'bg-blue-600 text-white' 
          : 'bg-gray-200 text-gray-900'
      }`}>
        <div className="whitespace-pre-wrap">{message.content}</div>
        
        {/* AI Suggestions */}
        {message.suggestions && message.suggestions.length > 0 && (
          <div className="mt-2 pt-2 border-t border-gray-300">
            <div className="text-xs font-medium mb-1">Suggestions:</div>
            <ul className="text-xs space-y-1">
              {message.suggestions.map((suggestion, index) => (
                <li key={index}>• {suggestion}</li>
              ))}
            </ul>
          </div>
        )}
        
        {/* Timestamp */}
        <div className={`text-xs mt-1 ${isUser ? 'text-blue-100' : 'text-gray-500'}`}>
          {new Date(message.timestamp).toLocaleTimeString()}
          {message.processingTime && (
            <span className="ml-2">({message.processingTime}ms)</span>
          )}
        </div>
      </div>
    </div>
  );
}
```

### 5. Typing Indicator Component

```typescript
// src/components/ui/TypingIndicator.tsx
import React from 'react';

export function TypingIndicator() {
  return (
    <div className="flex justify-start">
      <div className="bg-gray-200 rounded-lg px-4 py-2">
        <div className="flex space-x-1">
          <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
        <div className="text-xs text-gray-500 mt-1">AI is thinking...</div>
      </div>
    </div>
  );
}
```

### 6. Progress Bar Component

```typescript
// src/components/ui/ProgressBar.tsx
import React from 'react';

interface ProgressBarProps {
  value: number;
  max?: number;
  className?: string;
}

export function ProgressBar({ value, max = 100, className = 'bg-blue-500' }: ProgressBarProps) {
  const percentage = Math.min((value / max) * 100, 100);
  
  return (
    <div className="w-full bg-gray-200 rounded-full h-2">
      <div 
        className={`h-2 rounded-full transition-all duration-300 ${className}`}
        style={{ width: `${percentage}%` }}
      />
    </div>
  );
}
```

## Styling Notes

### Tailwind CSS Classes Used

- **Layout**: `flex`, `grid`, `space-y-*`, `space-x-*`
- **Colors**: `bg-blue-600`, `text-white`, `border-gray-200`
- **Spacing**: `p-4`, `m-2`, `px-3`, `py-2`
- **Typography**: `text-lg`, `font-semibold`, `text-center`
- **Interactive**: `hover:bg-blue-700`, `focus:ring-2`, `disabled:opacity-50`
- **Responsive**: `max-w-xs`, `lg:max-w-md`, `grid-cols-2`

### Custom Animations

```css
/* Add to your CSS file */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-8px);
  }
  70% {
    transform: translateY(-4px);
  }
  90% {
    transform: translateY(-2px);
  }
}

.animate-bounce {
  animation: bounce 1.4s infinite;
}
```

## Accessibility Features

- **ARIA labels** for screen readers
- **Keyboard navigation** support
- **Focus management** for modal dialogs
- **Color contrast** compliance
- **Screen reader** announcements for dynamic content

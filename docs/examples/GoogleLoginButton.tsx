import React, { useEffect, useState } from 'react';
import axios from 'axios';

interface GoogleLoginButtonProps {
  onLoginSuccess: (token: string) => void;
  className?: string;
}

const GoogleLoginButton: React.FC<GoogleLoginButtonProps> = ({ 
  onLoginSuccess, 
  className 
}) => {
  const [googleAuthUrl, setGoogleAuthUrl] = useState<string | null>(null);

  useEffect(() => {
    // Fetch the Google auth URL when the component mounts
    const fetchGoogleAuthUrl = async () => {
      try {
        const response = await axios.get('/api/auth/google/url');
        setGoogleAuthUrl(response.data.url);
      } catch (error) {
        console.error('Failed to fetch Google auth URL:', error);
      }
    };

    fetchGoogleAuthUrl();
  }, []);

  // Handle the callback from Google OAuth
  useEffect(() => {
    const handleGoogleCallback = () => {
      // Check if this is a callback from Google OAuth
      const urlParams = new URLSearchParams(window.location.search);
      const token = urlParams.get('token');
      
      if (token && window.location.pathname.includes('/auth/google-callback')) {
        // Clean up the URL
        window.history.replaceState({}, document.title, '/');
        
        // Store the token and notify parent component
        localStorage.setItem('auth_token', token);
        onLoginSuccess(token);
      }
    };

    handleGoogleCallback();
  }, [onLoginSuccess]);

  return (
    <button
      className={`google-login-button ${className || ''}`}
      onClick={() => googleAuthUrl && window.location.assign(googleAuthUrl)}
      disabled={!googleAuthUrl}
    >
      <img 
        src="https://developers.google.com/identity/images/btn_google_signin_dark_normal_web.png" 
        alt="Sign in with Google" 
      />
    </button>
  );
};

export default GoogleLoginButton;

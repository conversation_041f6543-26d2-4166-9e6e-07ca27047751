# Hybrid Subscription + Credit System Implementation

## Overview

This document outlines the implementation of a hybrid subscription + credit system for the legal document analyzer application. The system combines traditional subscription tiers with a flexible credit-based usage model.

**🎯 Key Principle**: Credits are only consumed for AI-powered features that provide intelligent analysis and automation. Basic CRUD operations (Create, Read, Update, Delete) are completely FREE to encourage platform usage and data management.

## Architecture

### Core Components

1. **Credit Management Service** (`src/modules/subscription/services/credit-management.service.ts`)
   - Handles all credit operations (allocation, deduction, purchase)
   - Manages feature cost calculations
   - Tracks credit history and transactions

2. **Enhanced Subscription Schema** (`src/modules/subscription/schemas/subscription.schema.ts`)
   - Extended with credit-related fields
   - Maintains backward compatibility with existing subscriptions

3. **Feature Cost Configuration** (`src/config/feature-costs.config.ts`)
   - Defines credit costs for all features
   - Categorizes features by complexity and value
   - Configures credit packages for purchase

4. **Credit Usage Middleware** (`src/modules/subscription/middleware/credit-usage.middleware.ts`)
   - Automatically deducts credits ONLY for AI-powered features
   - Uses `@UseCredits()` and `@FreeFeature()` decorators for precise control
   - Provides real-time credit validation
   - No fallback charging - only explicitly defined AI operations consume credits

5. **Credit Controller** (`src/modules/subscription/controllers/credit.controller.ts`)
   - REST API endpoints for credit management
   - Credit balance, history, and purchase operations

6. **Credit Decorators** (`src/modules/subscription/decorators/use-credits.decorator.ts`)
   - `@UseCredits(featureName)`: Marks endpoints that consume credits
   - `@FreeFeature()`: Explicitly marks endpoints as free (no credits consumed)
   - Provides clear, declarative credit consumption control

## Subscription Tiers with Credits

### FREE Tier
- **Monthly Credits**: 25
- **Features**: Basic AI analysis, document upload (FREE), chat sessions (FREE), basic comparison
- **Document Limit**: 10
- **Free Operations**: Unlimited CRUD operations, document management, chat sessions
- **AI Operations**: 25+ AI-powered features/month (depending on feature complexity)

### PRO Tier
- **Monthly Credits**: 100
- **Features**: All basic + advanced AI analysis, bulk upload (FREE), enhanced comparison
- **Document Limit**: 200
- **Free Operations**: Unlimited CRUD operations, document management, collaboration
- **AI Operations**: 100+ AI-powered features/month (depending on feature complexity)

### ADMIN Tier
- **Monthly Credits**: 300
- **Features**: All features including enterprise-level AI capabilities
- **Document Limit**: Unlimited
- **Free Operations**: Unlimited CRUD operations, full platform access
- **AI Operations**: 300+ AI-powered features/month (depending on feature complexity)

## Feature Credit Costs

### 🆓 FREE Operations (0 credits)
**All basic CRUD operations are completely FREE:**
- Document upload, view, delete, manage versions
- Contract playbook creation, editing, viewing, export/import
- Chat session creation, viewing messages, file uploads
- Collaboration session joining, leaving, viewing
- Compliance profile creation and management
- Legal research basic lookup and search
- Document organization (tags, folders, saved searches)
- User profile and organization management

### 💳 AI-Powered Features (Credits Required)

#### Basic AI Features (1 credit)
- `basic_analysis`: 1 credit (Basic document analysis)
- `chat`: 1 credit (AI chat responses)
- `basic_comparison`: 1 credit (Basic document comparison)
- `basic_citation_analysis`: 1 credit (Basic citation analysis)
- `clause_identification`: 1 credit (AI clause identification)
- `playbook_analysis`: 1 credit (Contract playbook AI analysis)

#### Advanced AI Features (2 credits)
- `advanced_analysis`: 2 credits (Advanced document analysis)
- `enhanced_comparison`: 2 credits (Enhanced document comparison)
- `enhanced_citation_analysis`: 2 credits (Enhanced citation analysis)
- `clause_intelligence`: 2 credits (AI clause suggestions)
- `template_generation`: 2 credits (AI template generation)
- `compliance_audit`: 2 credits (AI compliance auditing)
- `real_time_collaboration`: 2 credits (AI-powered collaboration sessions)

#### Premium AI Features (3 credits)
- `ai_assisted_drafting`: 3 credits (AI document drafting)
- `clause_library_automation`: 3 credits (AI clause extraction)
- `negotiation_playbook`: 3 credits (AI negotiation analysis)
- `ai_question_generation`: 3 credits (AI deposition questions)
- `deposition_analysis`: 3 credits (AI transcript analysis)

## Controller Implementation

### Credit Decorator Usage

All controllers have been updated to use explicit credit decorators:

#### Contract Playbooks Controller
```typescript
@Post() @FreeFeature() // Creating playbooks is FREE
@Get() @FreeFeature() // Viewing playbooks is FREE
@Put(':id') @FreeFeature() // Updating playbooks is FREE
@Delete(':id') @FreeFeature() // Deleting playbooks is FREE
@Post('analyze') @UseCredits('playbook_analysis') // AI analysis consumes credits
```

#### Documents Controller
```typescript
@Post('upload') @FreeFeature() // Document upload is FREE
@Get() @FreeFeature() // Viewing documents is FREE
@Post(':id/analyze') @UseCredits('advanced_analysis') // AI analysis consumes credits
```

#### Chat Controller
```typescript
@Post('sessions') @FreeFeature() // Creating chat sessions is FREE
@Get('sessions') @FreeFeature() // Viewing sessions is FREE
@Post('messages') @UseCredits('chat') // AI responses consume credits
```

#### Document Automation Controller
```typescript
@Post('ai-assisted-drafting') @UseCredits('ai_assisted_drafting')
@Post('clause-intelligence') @UseCredits('clause_intelligence')
@Post('generate-related-documents') @UseCredits('template_generation')
```

#### Legal Research Controller
```typescript
@Post('citations/analyze') @UseCredits('basic_citation_analysis')
@Post('citations/analyze/enhanced') @UseCredits('enhanced_citation_analysis')
```

### Middleware Path Matching

The credit usage middleware has been updated with specific path matching:

```typescript
// Only AI-powered operations consume credits
if (path.includes('/analyze') && path.includes('/playbooks')) {
  return 'playbook_analysis';
}
if (path.includes('/chat/messages') && method === 'POST') {
  return 'chat';
}
if (path.includes('/documents') && path.includes('/analyze')) {
  return 'advanced_analysis';
}
// No fallback charging - only explicitly defined operations
return null;
```

## Database Schema Changes

### Subscription Collection Extensions

```typescript
{
  // Existing fields...
  
  // Credit System Fields
  creditBalance: number;
  monthlyCreditsAllocation: number;
  totalCreditsEarned: number;
  totalCreditsSpent: number;
  lastCreditAllocation: Date;
  creditHistory: Array<{
    type: 'allocation' | 'purchase' | 'usage' | 'refund' | 'expiration';
    amount: number;
    balance: number;
    featureName?: string;
    transactionId?: string;
    timestamp: Date;
    description: string;
  }>;
  autoRecharge: {
    enabled: boolean;
    threshold: number;
    amount: number;
    stripePaymentMethodId?: string;
  };
}
```

## API Endpoints

### Credit Management

#### Get Credit Balance
```http
GET /api/credits/balance
```

#### Get Credit History
```http
GET /api/credits/history?limit=50&offset=0
```

#### Check Credits for Feature
```http
GET /api/credits/check/{featureName}
```

#### Use Credits for Feature
```http
POST /api/credits/use
{
  "organizationId": "org-123",
  "featureName": "advanced_analysis",
  "transactionId": "optional-id"
}
```

#### Allocate Monthly Credits
```http
POST /api/credits/allocate-monthly
```

#### Get Feature Costs
```http
GET /api/credits/features
GET /api/credits/features/category/{category}
```

#### Get Credit Packages
```http
GET /api/credits/packages
```

## Credit Purchase Packages

### Small Package
- **Credits**: 100
- **Price**: $9.99
- **Bonus**: 0
- **Total**: 100 credits

### Medium Package
- **Credits**: 500
- **Price**: $44.99
- **Bonus**: 50 (10%)
- **Total**: 550 credits

### Large Package
- **Credits**: 1000
- **Price**: $79.99
- **Bonus**: 200 (20%)
- **Total**: 1200 credits

### Enterprise Package
- **Credits**: 5000
- **Price**: $349.99
- **Bonus**: 1500 (30%)
- **Total**: 6500 credits

## Migration Strategy

### Existing Subscriptions
1. **Automatic Credit Initialization**: Existing subscriptions automatically receive credit fields with appropriate initial values
2. **Backward Compatibility**: Old usage tracking continues to work alongside credit system
3. **Gradual Rollout**: Credit system can be enabled per feature or organization

### Data Migration
- All existing subscriptions get initialized with credit balances based on their tier
- Credit history starts with an initial allocation transaction
- Monthly credit allocation is set based on subscription tier

## Usage Flow

### 1. Feature Access Check
```typescript
// Middleware automatically checks credits before feature usage
if (!hasCreditsForFeature(organizationId, featureName)) {
  throw new BadRequestException('Insufficient credits');
}
```

### 2. Credit Deduction
```typescript
// Credits are deducted upon successful feature usage
const result = await deductCreditsForFeature(organizationId, featureName);
```

### 3. Monthly Allocation
```typescript
// Credits are allocated monthly based on subscription tier
await allocateMonthlyCredits(organizationId);
```

### 4. Credit Purchase
```typescript
// Users can purchase additional credits
const checkout = await createCreditCheckoutSession(package, organizationId);
```

## Benefits

### For Users
- **Free Data Management**: All CRUD operations are completely free with NO LIMITS
- **Unlimited Document Storage**: Upload, view, and manage unlimited documents without consuming credits
- **No Analysis Limits**: Basic document viewing and management operations are completely unrestricted
- **Transparent AI Pricing**: Only pay credits for actual AI-powered value-added features
- **Flexibility**: Pay for what you use beyond base subscription
- **No Credit Anxiety**: Users can explore and manage unlimited data without worrying about credit consumption
- **Clear Value Proposition**: Credits are reserved for intelligent analysis and automation
- **Control**: Ability to purchase additional credits when needed for AI features

### For Business
- **Improved User Retention**: Free unlimited CRUD operations encourage regular platform usage
- **No Barriers to Entry**: Users can fully explore the platform without hitting limits
- **Revenue Optimization**: Higher-value AI features generate more revenue
- **Clear Value Delivery**: Credits align with actual AI processing costs
- **Usage Analytics**: Detailed insights into AI feature popularity and usage patterns
- **Scalability**: Easy to add new AI features with appropriate pricing
- **Competitive Advantage**: Fair pricing model with no limits on basic operations
- **User Growth**: Unlimited free operations drive organic platform adoption

## Testing

### Unit Tests
- Credit management service tests (`credit-management.service.spec.ts`)
- Feature cost validation tests
- Credit transaction history tests

### Integration Tests
- End-to-end credit usage flow
- Subscription tier upgrade/downgrade with credits
- Monthly credit allocation automation

### Load Tests
- Credit deduction performance under high load
- Concurrent credit operations handling

## Future Enhancements

### Phase 2 Features
1. **Auto-Recharge**: Automatic credit purchase when balance is low
2. **Credit Sharing**: Organization-level credit pools
3. **Credit Expiration**: Time-based credit expiration policies
4. **Usage Analytics**: Detailed credit usage reporting
5. **Credit Gifting**: Transfer credits between organizations

### Phase 3 Features
1. **Dynamic Pricing**: AI-based feature cost optimization
2. **Credit Marketplace**: User-to-user credit trading
3. **Bulk Discounts**: Volume-based credit pricing
4. **Loyalty Program**: Bonus credits for long-term users

## Monitoring and Analytics

### Key Metrics
- Credit utilization rates by tier
- Feature popularity by credit cost
- Credit purchase conversion rates
- Monthly credit allocation vs usage
- Credit balance distribution across users

### Alerts
- Low credit balance warnings
- Unusual credit usage patterns
- Failed credit transactions
- Monthly allocation failures

## Security Considerations

### Credit Transaction Security
- All credit operations are logged with transaction IDs
- Credit balances are validated before and after operations
- Atomic operations prevent race conditions
- Audit trail for all credit-related activities

### Fraud Prevention
- Rate limiting on credit usage
- Anomaly detection for unusual patterns
- Transaction verification and rollback capabilities
- Secure payment processing for credit purchases

## Implementation Status

### ✅ Completed Features

#### Controllers Updated
- **Contract Playbooks Controller**: ✅ CRUD operations FREE, AI analysis PAID
- **Documents Controller**: ✅ CRUD operations FREE, AI analysis PAID
- **Chat Controller**: ✅ Sessions/messages FREE, AI responses PAID
- **Document Automation Controller**: ✅ All AI operations PAID
- **Clause Library Controller**: ✅ CRUD FREE, AI identification/generation PAID
- **Document Comparison Controllers**: ✅ Comparisons PAID, exports FREE
- **Negotiation Playbook Controller**: ✅ AI analysis PAID
- **Compliance Auditor Controller**: ✅ AI auditing PAID, profiles FREE
- **Deposition Controller**: ✅ CRUD FREE, AI question generation/analysis PAID
- **Collaboration Controller**: ✅ Session creation PAID, joining/viewing FREE
- **Legal Research Controller**: ✅ AI analysis PAID, basic lookup FREE

#### Technical Implementation
- **Credit Usage Middleware**: ✅ Updated with specific AI operation path matching
- **Credit Decorators**: ✅ `@UseCredits()` and `@FreeFeature()` implemented
- **No Fallback Charging**: ✅ Only explicitly defined AI operations consume credits
- **Backward Compatibility**: ✅ Existing subscriptions continue to work

### 🔄 Current Status
The system is **LIVE** and operational with the new credit consumption logic. All basic CRUD operations are now completely free, and only AI-powered features consume credits.

## Conclusion

The hybrid subscription + credit system provides a flexible, scalable, and user-friendly approach to feature access and billing. The updated implementation ensures that:

**🎯 Key Achievements:**
- **Fair Pricing**: Users only pay for AI-powered value, not basic data management
- **No Limits**: Unlimited document storage, viewing, and management operations
- **Improved UX**: No credit anxiety or analysis limits encourages platform exploration
- **Clear Value Proposition**: Credits directly correlate with AI processing and intelligent features
- **Business Growth**: Free unlimited CRUD operations drive user retention and platform adoption
- **Competitive Edge**: No artificial limits on basic operations unlike competitors

The system maintains the simplicity of subscription tiers while offering granular control over AI feature usage. The implementation is backward compatible and provides a solid foundation for future AI feature additions and business model innovations.

**🚀 Impact**: This approach positions the platform as user-friendly and value-driven, where users pay for intelligence and automation, not for basic data management capabilities.

# Enhanced Citation UI Interaction Flow

This document outlines the interaction flow for the enhanced citation UI in the Legal Document Analyzer, including the necessary API endpoints, payloads, and responses for implementation.

## Table of Contents

1. [User Interaction Flow](#user-interaction-flow)
2. [API Endpoints](#api-endpoints)
3. [Implementation Details](#implementation-details)
4. [UI Components](#ui-components)

## User Interaction Flow

### 1. Chat Interaction with Citation Detection

When a user is chatting with the legal document analyzer:

1. **User sends a message** mentioning a legal case or statute
2. **AI responds with enriched citations**:
   - The citation appears as a clickable link with the full case name
   - A small citation badge/icon appears next to each citation

### 2. Citation Interaction Options

When a user interacts with a citation in the chat:

#### Option 1: Hover Interaction

- **User hovers over citation**: A tooltip appears showing:
  - Full case name
  - Court and jurisdiction
  - Date filed
  - Precedential status
  - Multiple citation formats

#### Option 2: Click Interaction

- **User clicks on citation**: A side panel opens with detailed information:
  - **Citation Tab**: Shows all citation formats
  - **Case Details Tab**: Shows full case information
  - **Docket Tab**: Shows procedural history with timeline
  - **Related Cases Tab**: Shows cases that cite this case

### 3. Docket Timeline View

In the Docket Tab:

1. **Visual Timeline**: Shows key events in chronological order:

   - Filing date
   - Argument date
   - Decision date
   - Cert granted/denied dates (for Supreme Court cases)

2. **Docket Entries List**: Below the timeline, shows:
   - Date of each entry
   - Description of the filing or action
   - Document links where available

### 4. Citation Search Integration

The search functionality would be integrated in two ways:

#### Option 1: Direct Search

- A search bar in the navigation allows users to directly search for cases
- Advanced search options let users filter by court, judge, date range, etc.

#### Option 2: Contextual Search

- When viewing a citation, a "Find Related Cases" button appears
- This pre-populates the search with relevant parameters from the current case

### 5. Citation Management

For document analysis:

1. **Citation Summary**: After analyzing a document, a "Citations" tab shows all detected citations
2. **Citation Validation**: System checks if citations are valid and properly formatted
3. **Citation Export**: Users can export all citations in various formats (e.g., Bluebook)

## API Endpoints

### 1. Citation Lookup Endpoint

**Endpoint:** `GET /api/court-listener/citation/:citation`

**Description:** Retrieves comprehensive information about a citation, including case details and docket information.

**Parameters:**

- `citation` (path parameter): The citation string (e.g., "410 U.S. 113")

**Response:**

```json
{
  "status": "success",
  "data": {
    "citation": "410 U.S. 113",
    "cases": [
      {
        "case_name": "Roe v. Wade",
        "case_name_short": "Roe",
        "case_name_full": "Roe et al. v. Wade, District Attorney of Dallas County",
        "date_filed": "1973-01-22",
        "court": "LRU",
        "jurisdiction": "Federal",
        "judges": "Blackmun, Burgee, Douglas, Brennan, Stewart, Marshall, Powell, Burger, White, Rehnquist",
        "precedential_status": "Published",
        "absolute_url": "/opinion/108713/roe-v-wade/",
        "citations": [
          {
            "volume": 35,
            "reporter": "L. Ed. 2d",
            "page": "147",
            "type": 1
          },
          {
            "volume": 93,
            "reporter": "S. Ct.",
            "page": "705",
            "type": 1
          },
          {
            "volume": 410,
            "reporter": "U.S.",
            "page": "113",
            "type": 1
          },
          {
            "volume": 1973,
            "reporter": "U.S. LEXIS",
            "page": "159",
            "type": 6
          }
        ],
        "docket": {
          "docket_number": "70-18",
          "court_id": "scotus",
          "case_name": "Roe v. Wade",
          "date_filed": "1970-03-03",
          "date_argued": "1971-12-13",
          "date_reargued": "1972-10-11",
          "date_cert_granted": "1970-05-18",
          "date_terminated": "1973-01-22",
          "entries": [
            {
              "date_filed": "1970-03-03",
              "description": "Petition for writ of certiorari filed",
              "document_number": "1"
            },
            {
              "date_filed": "1970-05-18",
              "description": "Petition for writ of certiorari granted",
              "document_number": "5"
            },
            {
              "date_filed": "1971-12-13",
              "description": "Argued",
              "document_number": "15"
            },
            {
              "date_filed": "1972-10-11",
              "description": "Reargued",
              "document_number": "20"
            },
            {
              "date_filed": "1973-01-22",
              "description": "Opinion issued",
              "document_number": "25"
            }
          ]
        }
      }
    ]
  }
}
```

### 2. Case Search Endpoint

**Endpoint:** `GET /api/court-listener/search`

**Description:** Searches for cases based on various criteria.

**Parameters:**

- `query` (query parameter, optional): General search query
- `citation` (query parameter, optional): Citation string
- `case_name` (query parameter, optional): Case name
- `judge` (query parameter, optional): Judge name
- `court` (query parameter, optional): Court identifier
- `jurisdiction` (query parameter, optional): Jurisdiction
- `filed_after` (query parameter, optional): Filed after date (ISO format)
- `filed_before` (query parameter, optional): Filed before date (ISO format)
- `page` (query parameter, optional): Page number
- `page_size` (query parameter, optional): Page size

**Response:**

```json
{
  "status": "success",
  "data": {
    "count": 8,
    "next": "http://localhost:4000/api/court-listener/search?citation=410%20U.S.%20113&court=scotus&page=2",
    "previous": null,
    "results": [
      {
        "id": 108713,
        "resource_uri": "/api/rest/v4/clusters/108713/",
        "absolute_url": "/opinion/108713/roe-v-wade/",
        "case_name": "Roe v. Wade",
        "date_filed": "1973-01-22",
        "court": "scotus",
        "jurisdiction": "Federal",
        "status": "Published",
        "pdf_url": "https://www.courtlistener.com/opinion/pdf/108713/roe-v-wade/"
      }
      // Additional results...
    ]
  }
}
```

### 3. Chat Message Endpoint (Enhanced with Citations)

**Endpoint:** `POST /api/chat/messages`

**Description:** Sends a message in a chat session, with enhanced citation handling.

**Payload:**

```json
{
  "sessionId": "5xr99var6q8",
  "content": "Can you tell me about the case law citations in this document, specifically Marbury v. Madison and Roe v. Wade?"
}
```

**Response:**

```json
{
  "id": "rqk0rrtcubc",
  "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
  "sessionId": "5xr99var6q8",
  "role": "assistant",
  "content": "The document includes case law citations for Marbury v. Madison and Roe v. Wade.",
  "citations": [
    {
      "rawText": "5 U.S. 137",
      "title": "Marbury v. Madison",
      "url": "https://www.courtlistener.com/opinion/84759/marbury-v-madison/",
      "confidence": 0.95,
      "metadata": {
        "type": "case",
        "source": "CourtListener",
        "normalizedCitation": "5 U.S. 137",
        "apiUrl": "https://www.courtlistener.com/api/rest/v4/clusters/84759/"
      }
    },
    {
      "rawText": "410 U.S. 113",
      "title": "Roe v. Wade",
      "url": "https://www.courtlistener.com/opinion/108713/roe-v-wade/",
      "confidence": 0.95,
      "metadata": {
        "type": "case",
        "source": "CourtListener",
        "normalizedCitation": "410 U.S. 113",
        "apiUrl": "https://www.courtlistener.com/api/rest/v4/clusters/108713/"
      }
    }
  ],
  "timestamp": "2025-04-16T13:50:16.594Z",
  "contextSources": [
    {
      "documentId": "56fa2eaa-95c1-4555-9eb9-7f49479670f0",
      "content": "Primary document context",
      "relevanceScore": 1
    }
  ],
  "citationsText": "Citations:\n- 5 U.S. 137 (Marbury v. Madison)\n  Link: https://www.courtlistener.com/opinion/84759/marbury-v-madison/\n- 410 U.S. 113 (Roe v. Wade)\n  Link: https://www.courtlistener.com/opinion/108713/roe-v-wade/"
}
```

### 4. Paginated Messages Endpoint (Enhanced with Citations)

**Endpoint:** `GET /api/chat/sessions/:id/paginated-messages`

**Description:** Retrieves paginated messages for a chat session, with enhanced citation handling.

**Parameters:**

- `id` (path parameter): The session ID
- `page` (query parameter, optional): Page number
- `limit` (query parameter, optional): Number of messages per page

**Response:**

```json
{
  "items": [
    {
      "id": "rqk0rrtcubc",
      "sessionId": "5xr99var6q8",
      "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
      "role": "assistant",
      "content": "The document includes case law citations for Marbury v. Madison and Roe v. Wade.",
      "timestamp": "2025-04-16T13:50:16.594Z",
      "attachments": [],
      "references": [],
      "contextSources": [],
      "citations": [
        {
          "rawText": "5 U.S. 137",
          "title": "Marbury v. Madison",
          "url": "https://www.courtlistener.com/opinion/84759/marbury-v-madison/",
          "confidence": 0.95,
          "metadata": {
            "type": "case",
            "source": "CourtListener",
            "normalizedCitation": "5 U.S. 137",
            "apiUrl": "https://www.courtlistener.com/api/rest/v4/clusters/84759/"
          }
        },
        {
          "rawText": "410 U.S. 113",
          "title": "Roe v. Wade",
          "url": "https://www.courtlistener.com/opinion/108713/roe-v-wade/",
          "confidence": 0.95,
          "metadata": {
            "type": "case",
            "source": "CourtListener",
            "normalizedCitation": "410 U.S. 113",
            "apiUrl": "https://www.courtlistener.com/api/rest/v4/clusters/108713/"
          }
        }
      ],
      "citationsText": "Citations:\n- 5 U.S. 137 (Marbury v. Madison)\n  Link: https://www.courtlistener.com/opinion/84759/marbury-v-madison/\n- 410 U.S. 113 (Roe v. Wade)\n  Link: https://www.courtlistener.com/opinion/108713/roe-v-wade/"
    }
    // Additional messages...
  ],
  "meta": {
    "totalItems": 10,
    "itemsPerPage": 5,
    "currentPage": 1,
    "totalPages": 2,
    "hasPreviousPage": false,
    "hasNextPage": true
  }
}
```

## Docket API Endpoints

The following endpoints provide access to docket information and procedural history for legal cases.

### 1. Get Docket Information

**Endpoint:** `GET /api/court-listener/docket/:id`

**Description:** Retrieves detailed information about a specific docket.

**Parameters:**

- `id` (path parameter): The docket ID

**Response:**

```json
{
  "status": "success",
  "data": {
    "resource_uri": "https://www.courtlistener.com/api/rest/v4/dockets/69856122/",
    "id": 69856122,
    "court": "https://www.courtlistener.com/api/rest/v4/courts/scotus/",
    "court_id": "scotus",
    "clusters": [
      "https://www.courtlistener.com/api/rest/v4/clusters/10373795/"
    ],
    "absolute_url": "/docket/69856122/trump-v-j-g-g/",
    "date_created": "2025-04-07T16:01:51.375265-07:00",
    "date_modified": "2025-04-07T16:01:56.183698-07:00",
    "date_cert_granted": null,
    "date_cert_denied": null,
    "date_argued": null,
    "date_reargued": null,
    "date_reargument_denied": null,
    "date_filed": null,
    "date_terminated": null,
    "case_name_short": "Trump",
    "case_name": "Trump v. J. G. G.",
    "case_name_full": "",
    "docket_number": "24A931"
  }
}
```

### 2. Get Docket Entries

**Endpoint:** `GET /api/court-listener/docket/:id/entries`

**Description:** Retrieves all entries for a specific docket, showing the procedural history.

**Parameters:**

- `id` (path parameter): The docket ID

**Response:**

```json
{
  "status": "success",
  "data": {
    "results": [
      {
        "id": 12345678,
        "docket": "https://www.courtlistener.com/api/rest/v4/dockets/69856122/",
        "date_created": "2025-04-07T16:01:51.375265-07:00",
        "date_modified": "2025-04-07T16:01:56.183698-07:00",
        "date_filed": "2023-01-15",
        "entry_number": "1",
        "description": "Petition for writ of certiorari filed",
        "pacer_doc_id": "123456789",
        "document_number": "1",
        "attachment_number": null,
        "recap_documents": []
      },
      {
        "id": 12345679,
        "docket": "https://www.courtlistener.com/api/rest/v4/dockets/69856122/",
        "date_created": "2025-04-07T16:02:51.375265-07:00",
        "date_modified": "2025-04-07T16:02:56.183698-07:00",
        "date_filed": "2023-02-15",
        "entry_number": "2",
        "description": "Brief in opposition filed",
        "pacer_doc_id": "123456790",
        "document_number": "2",
        "attachment_number": null,
        "recap_documents": []
      }
    ]
  }
}
```

### 3. Get Cluster Information

**Endpoint:** `GET /api/court-listener/cluster/:id`

**Description:** Retrieves information about a case cluster, which links opinions and dockets.

**Parameters:**

- `id` (path parameter): The cluster ID

**Response:**

```json
{
  "status": "success",
  "data": {
    "id": 10373795,
    "resource_uri": "https://www.courtlistener.com/api/rest/v4/clusters/10373795/",
    "absolute_url": "/opinion/10373795/trump-v-j-g-g/",
    "date_created": "2025-04-07T16:01:51.375265-07:00",
    "date_modified": "2025-04-07T16:01:56.183698-07:00",
    "case_name": "Trump v. J. G. G.",
    "case_name_short": "Trump",
    "case_name_full": "Donald J. Trump v. J. G. G.",
    "slug": "trump-v-j-g-g",
    "date_filed": "2025-04-07",
    "docket": "https://www.courtlistener.com/api/rest/v4/dockets/69856122/",
    "docket_id": 69856122,
    "sub_opinions": [
      "https://www.courtlistener.com/api/rest/v4/opinions/12345678/"
    ],
    "citations": [
      {
        "volume": 598,
        "reporter": "U.S.",
        "page": "123",
        "type": 1
      }
    ],
    "attorneys": [],
    "judges": [],
    "precedential_status": "Published",
    "source": "C",
    "procedural_history": "",
    "posture": "",
    "syllabus": "",
    "citation_count": 0,
    "scdb_id": "",
    "scdb_decision_direction": null,
    "scdb_votes_majority": null,
    "scdb_votes_minority": null,
    "citation_id": null,
    "authorities": []
  }
}
```

### 4. Integration with Citation Report

The citation report endpoint combines information from these endpoints to provide a comprehensive view of a case:

**Endpoint:** `GET /api/court-listener/citation/:citation`

This endpoint returns citation information enriched with docket data and procedural history, providing all the necessary information for the citation UI components in a single request.

## Implementation Details

### 1. Citation Extraction Service

The citation extraction service has been enhanced to:

1. Extract citations from text using regex patterns
2. Enrich citations with metadata from CourtListener
3. Add docket information to citations
4. Format citations for display

### 2. Citation Storage

Citations are stored in the database as part of the chat message schema:

```typescript
@Schema()
class Citation {
  @Prop({ required: true })
  rawText: string;

  @Prop({ required: false })
  title?: string;

  @Prop({ required: false })
  url?: string;

  @Prop({ required: false })
  court?: string;

  @Prop({ required: false })
  year?: number;

  @Prop({ required: false })
  confidence?: number;

  @Prop({ type: mongoose.Schema.Types.Mixed, required: false })
  metadata?: Record<string, any>;
}

@Schema()
class ChatMessage {
  // Other fields...

  @Prop({ type: [Citation], required: false })
  citations?: Citation[];
}
```

### 3. Frontend Components

The frontend implementation requires the following components:

1. **CitationLink**: A component that renders citations as clickable links
2. **CitationTooltip**: A tooltip component that shows citation details on hover
3. **CitationPanel**: A side panel component that shows detailed citation information
4. **DocketTimeline**: A timeline component that visualizes the docket history
5. **CitationSearch**: A search component for finding cases

## UI Components

### Citation Link Component

```jsx
const CitationLink = ({ citation }) => {
  const [tooltipVisible, setTooltipVisible] = useState(false);

  return (
    <span className="citation-link">
      <a
        href="#"
        onClick={() => openCitationPanel(citation)}
        onMouseEnter={() => setTooltipVisible(true)}
        onMouseLeave={() => setTooltipVisible(false)}
      >
        {citation.title} ({citation.rawText})
      </a>
      {tooltipVisible && <CitationTooltip citation={citation} />}
    </span>
  );
};
```

### Citation Panel Component

```jsx
const CitationPanel = ({ citation }) => {
  const [activeTab, setActiveTab] = useState('details');

  return (
    <div className="citation-panel">
      <div className="citation-panel-header">
        <h2>{citation.title}</h2>
        <div className="citation-panel-tabs">
          <button
            className={activeTab === 'details' ? 'active' : ''}
            onClick={() => setActiveTab('details')}
          >
            Case Details
          </button>
          <button
            className={activeTab === 'docket' ? 'active' : ''}
            onClick={() => setActiveTab('docket')}
          >
            Docket
          </button>
          <button
            className={activeTab === 'related' ? 'active' : ''}
            onClick={() => setActiveTab('related')}
          >
            Related Cases
          </button>
        </div>
      </div>

      <div className="citation-panel-content">
        {activeTab === 'details' && <CitationDetails citation={citation} />}
        {activeTab === 'docket' && <DocketTimeline docket={citation.docket} />}
        {activeTab === 'related' && <RelatedCases citation={citation} />}
      </div>
    </div>
  );
};
```

### Docket Timeline Component

```jsx
const DocketTimeline = ({ docket }) => {
  if (!docket || !docket.entries) {
    return <div>No docket information available</div>;
  }

  return (
    <div className="docket-timeline">
      <div className="timeline-header">
        <div className="timeline-event">
          <div className="event-date">{formatDate(docket.date_filed)}</div>
          <div className="event-label">Filed</div>
        </div>
        {docket.date_argued && (
          <div className="timeline-event">
            <div className="event-date">{formatDate(docket.date_argued)}</div>
            <div className="event-label">Argued</div>
          </div>
        )}
        {docket.date_terminated && (
          <div className="timeline-event">
            <div className="event-date">
              {formatDate(docket.date_terminated)}
            </div>
            <div className="event-label">Decided</div>
          </div>
        )}
      </div>

      <div className="docket-entries">
        <h3>Docket Entries</h3>
        <table>
          <thead>
            <tr>
              <th>Date</th>
              <th>Description</th>
              <th>Document</th>
            </tr>
          </thead>
          <tbody>
            {docket.entries.map((entry, index) => (
              <tr key={index}>
                <td>{formatDate(entry.date_filed)}</td>
                <td>{entry.description}</td>
                <td>
                  {entry.document_number && (
                    <a href="#" onClick={() => viewDocument(entry)}>
                      #{entry.document_number}
                    </a>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};
```

## Conclusion

This enhanced citation UI interaction flow leverages the improved backend citation handling to provide a comprehensive legal research experience within the Legal Document Analyzer. The integration of docket information and rich citation metadata allows users to gain deeper insights into legal citations and their context.

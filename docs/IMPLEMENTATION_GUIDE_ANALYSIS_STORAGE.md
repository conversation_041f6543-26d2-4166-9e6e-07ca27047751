# Implementation Guide: Persistent Document Analysis Storage

**Objective:** Allow users (lawyers) to efficiently retrieve previously generated AI analysis results for documents without re-running the analysis each time.

**Core Idea:** Store the structured JSON output of successful AI analyses in the database, linked to the original document, and provide a dedicated API endpoint to fetch this stored result.

---

## Step-by-Step Implementation

### Phase 1: Database & Data Layer Setup

1. **Define Analysis Result Schema/Entity:**

   - **Goal:** Create a structure to store the analysis results.
   - **Action:** Define a new schema (Mongoose/MongoDB) or entity (TypeORM) named `AnalysisResult`.
   - **Fields:**
     - `_id`/`id`: Primary key.
     - `documentId`: Foreign key/reference to the `Document` entity/schema (indexed).
     - `analysisContent`: Structured JSON analysis result (Type: `Object` or `JSONB`).
     - `aiProvider`: String indicating the provider (e.g., 'openai', 'gemini').
     - `modelUsed`: (Optional) Specific model name.
     - `analysisTimestamp`: `Date` of analysis completion.
     - `analysisParameters`: (Optional) Object with analysis parameters (query, documentType, etc.).
     - `organizationId`: Link for multi-tenancy.
   - **Location:** `src/modules/documents/schemas/analysis-result.schema.ts` or `src/modules/documents/entities/analysis-result.entity.ts`.
   - **Database:** Apply migrations or ensure schema synchronization.

2. **Create Repository/Service for Analysis Results:**
   - **Goal:** Encapsulate database interactions for `AnalysisResult`.
   - **Action:** Create `AnalysisResultRepository` and `AnalysisResultService`.
   - **Methods:**
     - `save(result: AnalysisResult): Promise<AnalysisResult>`
     - `findLatestByDocumentId(documentId: string, organizationId: string): Promise<AnalysisResult | null>`
     - (Optional) `findAllByDocumentId(...)` for history.
   - **Location:** `src/modules/documents/repositories/analysis-result.repository.ts`, `src/modules/documents/services/analysis-result.service.ts`.
   - **Module:** Add to providers/exports in `DocumentsModule` (`src/modules/documents/documents.module.ts`).

### Phase 2: Integrate Saving Logic

3. **Modify Analysis Execution Logic:**
   - **Goal:** Save the result after a successful analysis.
   - **Action (Immediate Analysis - `POST /documents/:id/analyze`):**
     - Inject `AnalysisResultService` into `DocumentsController`.
     - After successful analysis and transformation, create an `AnalysisResult` object.
     - Call `analysisResultService.save(...)`. Handle save errors gracefully.
   - **Action (Background Analysis - `/documents/process` worker):**
     - Inject `AnalysisResultService` into the job processor/worker.
     - On successful job completion, create and save the `AnalysisResult` using `analysisResultService.save(...)`.
   - **Location:** `src/modules/documents/controllers/documents.controller.ts`, relevant worker file in `src/modules/documents/workers`.

### Phase 3: API Endpoint for Retrieval

4. **Create DTO for Analysis Result Response:**

   - **Goal:** Define the API response structure.
   - **Action:** Create `GetAnalysisResultDto.ts`.
   - **Fields:** Mirror relevant fields from `AnalysisResult`. Use Swagger decorators.
   - **Location:** `src/modules/documents/dto/get-analysis-result.dto.ts`.

5. **Implement `GET /documents/:id/analysis` Endpoint:**
   - **Goal:** Provide an endpoint to fetch the latest stored analysis.
   - **Action:** In `DocumentsController`:
     - Add method: `getLatestAnalysis(@Param('id') documentId: string, @Req() req)`.
     - Decorators: `@Get(':id/analysis')`, `@ApiOperation`, `@ApiResponse(200, { type: GetAnalysisResultDto }), @ApiResponse(404)`.
     - Inject `AnalysisResultService`.
     - Extract `organizationId` from `req.user`.
     - Call `analysisResultService.findLatestByDocumentId(documentId, organizationId)`.
     - If found, map to DTO and return.
     - If not found, throw `NotFoundException`.
     - Apply necessary Guards (`AuthGuard`, `SubscriptionGuard`).
   - **Location:** `src/modules/documents/controllers/documents.controller.ts`.

### Phase 4: Enhancements & Production Readiness

6. **(Optional) Enhance `GET /documents/:id`:**

   - **Goal:** Indicate if stored analysis exists.
   - **Action:**
     - Inject `AnalysisResultService` into `DocumentsController`.
     - In `getDocument(...)`, call `analysisResultService.findLatestByDocumentId(...)`.
     - Modify the return DTO (`GetDocumentDto`) to include `hasAnalysis: boolean` and `latestAnalysisTimestamp: Date | null`.
   - **Location:** `src/modules/documents/controllers/documents.controller.ts`, relevant DTO file.

7. **Add Comprehensive Tests:**

   - **Goal:** Ensure correctness and prevent regressions.
   - **Action:** Create/update unit and E2E tests for:
     - `AnalysisResultService`
     - `AnalysisResultRepository` (integration)
     - New `GET /documents/:id/analysis` endpoint (E2E)
     - Verification that results _are_ saved (E2E)
     - Updated `GET /:id` endpoint (E2E), if enhanced.
   - **Location:** `test/` directory or `src/modules/documents/tests`.

8. **Update API Documentation (Swagger/OpenAPI):**

   - **Goal:** Keep API documentation accurate.
   - **Action:** Ensure all changes are documented using Swagger decorators. Regenerate the definition.

9. **Review Error Handling & Logging:**

   - **Goal:** Production robustness.
   - **Action:** Review error handling for database operations. Add informative logging.

10. **Deployment:**
    - **Goal:** Smooth rollout.
    - **Action:**
      - Prepare and run database migration scripts.
      - Configure production environment variables.
      - Deploy the updated application.
      - Monitor logs post-deployment.

---

This guide outlines the necessary steps to implement persistent storage and retrieval for document analysis results, enhancing the workflow for legal professionals.

## Executive Summary Improvements

The analysis system now generates a concise executive summary for legal contracts. It highlights key findings, including potential risks in specific clauses, identification of parties, effective dates, and key obligations. The summary employs refined legal language, integrating cross-referenced metadata that associates clauses with legal risks and compliance requirements. This structured presentation—featuring clear bullet lists and tables—ensures that legal practitioners gain a rapid, actionable overview of the contract's risk profile.

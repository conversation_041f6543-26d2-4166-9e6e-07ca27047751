# Two-Factor Authentication (2FA) Registration Flow

This document outlines the implementation plan for adding Two-Factor Authentication (2FA) registration to the Legal Document Analyzer application.

## Overview

Two-Factor Authentication adds an extra layer of security by requiring users to provide two different authentication factors:
1. Something they know (password)
2. Something they have (a mobile device with an authenticator app)

## Implementation Plan

### 1. Database Schema Updates

Update the User schema to include 2FA-related fields:

```typescript
// Add to User schema
@Prop({ default: false })
twoFactorEnabled: boolean;

@Prop({ type: String })
twoFactorSecret?: string;

@Prop({ default: false })
twoFactorVerified: boolean;
```

### 2. Required Dependencies

Install the necessary packages:

```bash
npm install otplib qrcode
```

- `otplib`: For generating TOTP secrets and validating TOTP codes
- `qrcode`: For generating QR codes that can be scanned by authenticator apps

### 3. Service Implementation

Create a new service for 2FA operations:

```typescript
@Injectable()
export class TwoFactorAuthService {
  constructor(
    private readonly userService: UserService,
  ) {}

  // Generate a secret for a user
  async generateSecret(userId: string): Promise<{ secret: string, otpAuthUrl: string }> {
    // Implementation details
  }

  // Verify a TOTP code against a user's secret
  async verifyCode(userId: string, code: string): Promise<boolean> {
    // Implementation details
  }

  // Enable 2FA for a user after successful verification
  async enableTwoFactor(userId: string): Promise<void> {
    // Implementation details
  }

  // Disable 2FA for a user
  async disableTwoFactor(userId: string): Promise<void> {
    // Implementation details
  }
}
```

### 4. API Endpoints

Add new endpoints to the AuthController:

```typescript
@Post('2fa/generate')
@UseGuards(JwtAuthGuard)
async generateTwoFactorSecret(@Req() req): Promise<{ secret: string, qrCode: string }> {
  // Implementation details
}

@Post('2fa/verify')
@UseGuards(JwtAuthGuard)
async verifyTwoFactorCode(@Req() req, @Body() body: { code: string }): Promise<{ success: boolean }> {
  // Implementation details
}

@Post('2fa/enable')
@UseGuards(JwtAuthGuard)
async enableTwoFactor(@Req() req): Promise<{ success: boolean }> {
  // Implementation details
}

@Post('2fa/disable')
@UseGuards(JwtAuthGuard)
async disableTwoFactor(@Req() req): Promise<{ success: boolean }> {
  // Implementation details
}
```

### 5. Registration Flow

The 2FA registration flow will consist of the following steps:

1. **Generate Secret**
   - User requests a 2FA secret
   - System generates a secret and returns it along with a QR code
   - User scans the QR code with an authenticator app (Google Authenticator, Authy, etc.)

2. **Verify Code**
   - User enters a code from their authenticator app
   - System verifies the code against the stored secret
   - If valid, the system marks the 2FA setup as verified

3. **Enable 2FA**
   - After verification, user can enable 2FA for their account
   - System updates the user record to indicate 2FA is enabled

### 6. Frontend Integration

The frontend will need to:
- Display the QR code for scanning
- Provide an input field for the verification code
- Show clear instructions for the user
- Provide options to enable/disable 2FA

### 7. Security Considerations

- Store the 2FA secret securely (consider encryption)
- Implement rate limiting on verification attempts
- Provide backup/recovery codes when enabling 2FA
- Consider session management for 2FA-enabled accounts

## Implementation Timeline

1. Database schema updates (1 day)
2. Service implementation (2 days)
3. API endpoint implementation (1 day)
4. Testing and security review (2 days)

## Future Enhancements

- Backup/recovery codes for account recovery
- Remember device functionality
- SMS-based 2FA as an alternative
- Admin controls for enforcing 2FA for specific user roles

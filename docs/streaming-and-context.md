# Streaming and Context Management

## Overview

The Legal Document Analyzer now features enhanced streaming capabilities and sophisticated context management for improved interaction with legal documents.

## Streaming Implementation

### SSE (Server-Sent Events) Response Format

```json
data: {
  "id": "cc6wvtz",
  "role": "user",
  "content": "What are the hourly rates?",
  "timestamp": "2025-03-16T13:18:33.509Z"
}

data: {
  "id": "j0h21sn",
  "sessionId": "frqrmoxm22s",
  "role": "assistant",
  "content": "According to Section 2.1...",
  "timestamp": "2025-03-16T13:18:35.540Z",
  "contextSources": [
    {
      "documentId": "791c3441-5272-4cd0-ad2a-33f6d11d024e",
      "content": "Primary document context",
      "relevanceScore": 1.0
    },
    {
      "documentId": "791c3441-5272-4cd0-ad2a-33f6d11d024e",
      "content": "Chat history context",
      "relevanceScore": 0.8
    }
  ]
}
```

### Features

- Real-time streaming responses
- Context information included in stream
- Relevance scoring for content sources
- Progressive content delivery
- Proper error handling via SSE error events

## Context Management

### Implementation Details

The context management system:

1. **Smart Context Window Building**
   - Analyzes document content
   - Selects most relevant sections based on queries
   - Uses sophisticated scoring algorithm

2. **Token Optimization**
   - Default max tokens: 8000
   - Typical utilization: 20-30%
   - Dynamic adjustment based on content relevance

3. **Relevance Scoring System**
   - Keyword matching
   - Section importance (headers, key clauses)
   - Numeric content relevance
   - Context preservation across messages

4. **Performance**
   - Real-time context updates
   - Efficient token usage
   - Seamless integration with streaming

### Usage Example

1. Client sends message:
```json
{
  "sessionId": "frqrmoxm22s",
  "content": "What are the hourly rates?",
  "relatedDocumentIds": []
}
```

2. System processes:
   - Builds context window (typically ~2000-3000 tokens)
   - Scores document sections for relevance
   - Maintains conversation context
   - Streams response with context information

3. Client receives streamed response with context sources and relevance scores.

## Integration with Gemini AI

- Context windows are optimized for Gemini's token limits
- Relevance scoring ensures most important content is included
- Streaming enables real-time AI responses
- Error handling and retry mechanisms built in
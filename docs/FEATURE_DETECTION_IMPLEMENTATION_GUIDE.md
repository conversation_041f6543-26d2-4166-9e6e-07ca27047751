# Feature Detection Implementation Guide

## Overview

This guide explains how to implement feature detection for the hybrid subscription + credit system. We provide multiple approaches to detect when features are used and automatically deduct credits.

## Quick Start

### 1. Enable Credit Interceptor Globally

```typescript
// In your main.ts or app.module.ts
import { CreditUsageInterceptor } from './modules/subscription/interceptors/credit-usage.interceptor';

// Option A: Global interceptor (recommended)
app.useGlobalInterceptors(
  new CreditUsageInterceptor(reflector, creditService, subscriptionService, tenantContext)
);

// Option B: Module-level interceptor
@Module({
  providers: [
    {
      provide: APP_INTERCEPTOR,
      useClass: CreditUsageInterceptor,
    },
  ],
})
export class AppModule {}
```

### 2. Mark Controller Methods with Decorators

```typescript
import { UseCredits, FreeFeature } from '../subscription/decorators/use-credits.decorator';

@Controller('documents')
export class DocumentsController {
  
  @Post('analyze')
  @UseCredits('advanced_analysis') // Automatically deducts 5 credits
  async analyzeDocument(@Body() dto: AnalyzeDocumentDto) {
    return this.service.analyzeDocument(dto);
  }
  
  @Get(':id')
  @FreeFeature() // Explicitly free
  async getDocument(@Param('id') id: string) {
    return this.service.getDocument(id);
  }
}
```

## Detection Methods

### 1. Decorator-Based Detection (Recommended)

**Best for:** Explicit control, clear documentation, type safety

```typescript
// Simple usage
@UseCredits('advanced_analysis')
@Post('analyze')
async analyzeDocument() { ... }

// Custom cost
@UseCredits('custom_feature', 15)
@Post('expensive-operation')
async expensiveOperation() { ... }

// Multiple features
@UseMultipleCredits([
  { feature: 'advanced_analysis', cost: 5 },
  { feature: 'precedent_analysis', cost: 10 }
])
@Post('comprehensive-analysis')
async comprehensiveAnalysis() { ... }

// Conditional usage
@UseCreditsConditional('advanced_analysis', (req) => req.body.useAdvanced)
@Post('flexible-analysis')
async flexibleAnalysis() { ... }
```

### 2. Middleware-Based Detection (Automatic)

**Best for:** Automatic detection, legacy endpoints, fallback

```typescript
// Automatically detects based on URL patterns
// /documents/analyze -> 'advanced_analysis'
// /chat/messages -> 'chat'
// /deposition/prepare -> 'deposition_preparation'

// Configure in app.module.ts
consumer
  .apply(CreditUsageMiddleware)
  .forRoutes('*'); // Apply to all routes
```

### 3. Service-Level Detection (Manual)

**Best for:** Complex business logic, dynamic feature usage

```typescript
@Injectable()
export class DocumentsService {
  async complexOperation(dto: ComplexDto) {
    const organizationId = this.tenantContext.getCurrentOrganization();
    
    // Manual credit checking and deduction
    if (dto.includeAdvancedAnalysis) {
      const result = await this.creditService.deductCreditsForFeature(
        organizationId,
        'advanced_analysis'
      );
      if (!result.success) {
        throw new BadRequestException(result.message);
      }
    }
    
    // Perform operation
    return this.performOperation(dto);
  }
}
```

### 4. Event-Based Detection (Async)

**Best for:** Analytics, audit trails, non-blocking operations

```typescript
// Emit events after successful operations
@Injectable()
export class DocumentsService {
  async analyzeDocument(dto: AnalyzeDocumentDto) {
    const result = await this.performAnalysis(dto);
    
    // Emit event for async credit tracking
    this.eventEmitter.emit('feature.used', {
      featureName: 'advanced_analysis',
      organizationId: this.tenantContext.getCurrentOrganization(),
      metadata: { documentId: dto.documentId }
    });
    
    return result;
  }
}

// Listen for events
@OnEvent('feature.used')
async handleFeatureUsage(payload: FeatureUsageEvent) {
  await this.creditService.deductCreditsForFeature(
    payload.organizationId,
    payload.featureName
  );
}
```

## Implementation Patterns

### Pattern 1: Simple Feature Mapping

```typescript
// Direct 1:1 mapping between endpoint and feature
@Controller('documents')
export class DocumentsController {
  @Post('upload')
  @UseCredits('document_upload')
  async uploadDocument() { ... }
  
  @Post('analyze')
  @UseCredits('advanced_analysis')
  async analyzeDocument() { ... }
  
  @Post('compare')
  @UseCredits('enhanced_comparison')
  async compareDocuments() { ... }
}
```

### Pattern 2: Conditional Feature Usage

```typescript
// Credits only charged under certain conditions
@Controller('analysis')
export class AnalysisController {
  @Post('flexible')
  @UseCreditsConditional(
    'advanced_analysis',
    (req) => req.body.analysisType === 'advanced'
  )
  async flexibleAnalysis(@Body() dto: FlexibleAnalysisDto) {
    if (dto.analysisType === 'advanced') {
      return this.performAdvancedAnalysis(dto);
    } else {
      return this.performBasicAnalysis(dto);
    }
  }
}
```

### Pattern 3: Tiered Feature Access

```typescript
// Different credit rules for different tiers
@Controller('features')
export class FeaturesController {
  @Post('premium-feature')
  @UseCreditsPremiumOnly('advanced_analysis') // Free for FREE tier, credits for PRO/ADMIN
  async premiumFeature() { ... }
  
  @Post('admin-feature')
  @UseCredits('admin_features') // Always requires credits (but admin gets more)
  async adminFeature() { ... }
}
```

### Pattern 4: Batch Operations

```typescript
// Optimized credit usage for batch operations
@Controller('batch')
export class BatchController {
  @Post('analyze-multiple')
  @UseCredits('bulk_upload', 10) // Fixed cost regardless of batch size
  async batchAnalyze(@Body() dto: BatchAnalyzeDto) {
    const results = [];
    for (const documentId of dto.documentIds) {
      results.push(await this.analyzeDocument(documentId));
    }
    return results;
  }
}
```

### Pattern 5: Complex Workflows

```typescript
// Manual credit handling for complex scenarios
@Controller('workflows')
export class WorkflowController {
  @Post('custom')
  @FreeFeature() // Handle credits manually
  async customWorkflow(@Body() dto: WorkflowDto) {
    return this.workflowService.executeWorkflow(dto);
  }
}

@Injectable()
export class WorkflowService {
  async executeWorkflow(dto: WorkflowDto) {
    const organizationId = this.tenantContext.getCurrentOrganization();
    let totalCredits = 0;
    
    try {
      for (const step of dto.steps) {
        const feature = this.mapStepToFeature(step);
        const result = await this.creditService.deductCreditsForFeature(
          organizationId,
          feature
        );
        
        if (!result.success) {
          throw new BadRequestException(result.message);
        }
        
        totalCredits += Math.abs(result.transaction.amount);
        await this.executeStep(step);
      }
      
      return { success: true, creditsUsed: totalCredits };
    } catch (error) {
      // Refund credits on failure
      if (totalCredits > 0) {
        await this.creditService.addCredits(
          organizationId,
          totalCredits,
          'refund',
          'Workflow execution failed'
        );
      }
      throw error;
    }
  }
}
```

## Best Practices

### 1. Use Decorators for Standard Operations
```typescript
// ✅ Good: Clear and explicit
@UseCredits('advanced_analysis')
@Post('analyze')
async analyzeDocument() { ... }

// ❌ Avoid: Hidden credit logic
@Post('analyze')
async analyzeDocument() {
  // Hidden credit deduction in service
}
```

### 2. Handle Failures Gracefully
```typescript
// ✅ Good: Automatic refund on failure
@UseCredits('expensive_operation')
async expensiveOperation() {
  // If this throws, credits are automatically refunded
  return this.performExpensiveOperation();
}
```

### 3. Use Appropriate Detection Method
```typescript
// ✅ Simple operations: Use decorators
@UseCredits('basic_feature')
async simpleOperation() { ... }

// ✅ Complex operations: Use service-level detection
async complexOperation(dto: ComplexDto) {
  if (dto.useFeatureA) await this.useCredits('feature_a');
  if (dto.useFeatureB) await this.useCredits('feature_b');
}
```

### 4. Provide Clear Error Messages
```typescript
// ✅ Good: Detailed error information
throw new BadRequestException({
  message: 'Insufficient credits for Advanced Analysis',
  creditInfo: {
    required: 5,
    available: 2,
    feature: 'advanced_analysis'
  }
});
```

### 5. Log Credit Usage
```typescript
// ✅ Good: Comprehensive logging
this.logger.log(
  `Credits deducted: ${amount} for ${feature} (Org: ${orgId}, Balance: ${newBalance})`
);
```

## Testing Feature Detection

### Unit Tests
```typescript
describe('CreditUsageInterceptor', () => {
  it('should deduct credits for decorated methods', async () => {
    // Test credit deduction
    const result = await controller.analyzeDocument(dto);
    expect(creditService.deductCreditsForFeature).toHaveBeenCalledWith(
      'org-123',
      'advanced_analysis'
    );
  });
  
  it('should refund credits on operation failure', async () => {
    // Test refund logic
    service.performAnalysis.mockRejectedValue(new Error('Analysis failed'));
    await expect(controller.analyzeDocument(dto)).rejects.toThrow();
    expect(creditService.addCredits).toHaveBeenCalledWith(
      'org-123',
      5,
      'refund',
      expect.any(String)
    );
  });
});
```

### Integration Tests
```typescript
describe('Feature Detection Integration', () => {
  it('should handle end-to-end credit flow', async () => {
    // Test complete flow from request to credit deduction
    const response = await request(app)
      .post('/documents/analyze')
      .send(dto)
      .expect(200);
      
    expect(response.headers['x-credits-used']).toBe('5');
    expect(response.headers['x-feature-used']).toBe('advanced_analysis');
  });
});
```

## Monitoring and Analytics

### Track Feature Usage
```typescript
// Log feature usage for analytics
@OnEvent('feature.used')
async trackFeatureUsage(event: FeatureUsageEvent) {
  await this.analyticsService.recordFeatureUsage({
    feature: event.featureName,
    organization: event.organizationId,
    timestamp: event.timestamp,
    creditsUsed: event.creditsUsed
  });
}
```

### Monitor Credit Patterns
```typescript
// Alert on unusual credit usage
@OnEvent('credits.deducted')
async monitorCreditUsage(event: CreditDeductionEvent) {
  if (event.amount > 100) {
    await this.alertService.sendHighUsageAlert(event);
  }
}
```

## Migration Strategy

### Phase 1: Add Decorators to New Endpoints
```typescript
// Start with new features
@UseCredits('new_feature')
@Post('new-endpoint')
async newFeature() { ... }
```

### Phase 2: Migrate Existing Endpoints
```typescript
// Gradually add decorators to existing endpoints
@UseCredits('existing_feature')
@Post('existing-endpoint')
async existingFeature() { ... }
```

### Phase 3: Remove Middleware Fallback
```typescript
// Once all endpoints are decorated, remove middleware
// Keep middleware only for legacy or third-party integrations
```

This comprehensive approach ensures accurate feature detection while providing flexibility for different use cases and complexity levels.

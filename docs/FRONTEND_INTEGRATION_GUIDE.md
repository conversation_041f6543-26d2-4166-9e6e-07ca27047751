# Legal Document Analyzer Frontend Integration Guide

This guide provides practical examples and implementation advice for building the frontend of the Legal Document Analyzer application. It focuses on integrating with the API endpoints and implementing the subscription-based feature availability system.

## Quick Start

1. Set up a React application with TypeScript
2. Install dependencies:
   - React Router for navigation
   - Axios for API requests
   - Redux Toolkit or React Query for state management
   - Stripe.js for subscription payments
   - A UI component library (MUI, Chakra UI, or similar)

## Core Implementation Areas

### 1. Authentication Flow

```typescript
// src/services/auth.service.ts
import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:4000/api';

export const register = async (userData) => {
  try {
    const response = await axios.post(`${API_URL}/auth/register`, userData);
    if (response.data.tokens) {
      localStorage.setItem('user', JSON.stringify(response.data.user));
      localStorage.setItem('accessToken', response.data.tokens.accessToken);
      localStorage.setItem('refreshToken', response.data.tokens.refreshToken);
    }
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

export const login = async (credentials) => {
  try {
    const response = await axios.post(`${API_URL}/auth/login`, credentials);
    if (response.data.tokens) {
      localStorage.setItem('user', JSON.stringify(response.data.user));
      localStorage.setItem('accessToken', response.data.tokens.accessToken);
      localStorage.setItem('refreshToken', response.data.tokens.refreshToken);
    }
    return response.data;
  } catch (error) {
    throw error.response?.data || error;
  }
};

export const logout = async () => {
  try {
    const refreshToken = localStorage.getItem('refreshToken');
    await axios.post(`${API_URL}/auth/logout`, { refreshToken });
  } finally {
    localStorage.removeItem('user');
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
  }
};
```

### 2. API Client Configuration with Interceptors

```typescript
// src/services/api.client.ts
import axios from 'axios';

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000/api';

const apiClient = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add token to requests
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Handle 401 responses with token refresh
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        const refreshToken = localStorage.getItem('refreshToken');
        const response = await axios.post(`${API_URL}/auth/refresh-token`, { refreshToken });
        
        if (response.data.accessToken) {
          localStorage.setItem('accessToken', response.data.accessToken);
          localStorage.setItem('refreshToken', response.data.refreshToken);
          
          originalRequest.headers['Authorization'] = `Bearer ${response.data.accessToken}`;
          return axios(originalRequest);
        }
      } catch (refreshError) {
        // Redirect to login if refresh fails
        localStorage.clear();
        window.location.href = '/login';
      }
    }
    
    return Promise.reject(error);
  }
);

export default apiClient;
```

### 3. Multi-Tenant Organization Context

```typescript
// src/contexts/OrganizationContext.tsx
import React, { createContext, useState, useEffect, useContext } from 'react';
import apiClient from '../services/api.client';

const OrganizationContext = createContext(null);

export const OrganizationProvider = ({ children }) => {
  const [organizations, setOrganizations] = useState([]);
  const [currentOrganization, setCurrentOrganization] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Load user's organizations on mount
    const fetchOrganizations = async () => {
      try {
        const response = await apiClient.get('/organizations');
        setOrganizations(response.data);
        
        // Set current organization from localStorage or use first one
        const savedOrgId = localStorage.getItem('currentOrganizationId');
        if (savedOrgId && response.data.find(org => org.id === savedOrgId)) {
          setCurrentOrganization(response.data.find(org => org.id === savedOrgId));
        } else if (response.data.length > 0) {
          setCurrentOrganization(response.data[0]);
          localStorage.setItem('currentOrganizationId', response.data[0].id);
        }
      } catch (error) {
        console.error('Failed to load organizations:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchOrganizations();
  }, []);

  const switchOrganization = (organizationId) => {
    const org = organizations.find(o => o.id === organizationId);
    if (org) {
      setCurrentOrganization(org);
      localStorage.setItem('currentOrganizationId', org.id);
      
      // Update the organization ID header that will be used for all API requests
      apiClient.defaults.headers.common['X-Organization-ID'] = org.id;
    }
  };

  return (
    <OrganizationContext.Provider 
      value={{ 
        organizations, 
        currentOrganization, 
        switchOrganization,
        loading
      }}
    >
      {children}
    </OrganizationContext.Provider>
  );
};

export const useOrganization = () => useContext(OrganizationContext);
```

### 4. Subscription Management Integration

```typescript
// src/services/subscription.service.ts
import apiClient from './api.client';
import { loadStripe } from '@stripe/stripe-js';

const stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLIC_KEY);

export const getSubscription = async () => {
  const response = await apiClient.get('/subscriptions');
  return response.data;
};

export const getSubscriptionPlans = async () => {
  const response = await apiClient.get('/subscriptions/plans');
  return response.data;
};

export const createSubscription = async (tierData) => {
  const response = await apiClient.post('/subscriptions', tierData);
  
  // Handle Stripe payment
  const stripe = await stripePromise;
  const { clientSecret } = response.data;
  
  return { stripe, clientSecret };
};

export const cancelSubscription = async (cancelAtPeriodEnd = true) => {
  const response = await apiClient.post('/subscriptions/cancel', { cancelAtPeriodEnd });
  return response.data;
};

export const changeTier = async (newTier) => {
  const response = await apiClient.post('/subscriptions/change-tier', { newTier });
  
  // If upgrading, this may require additional payment
  if (response.data.clientSecret) {
    const stripe = await stripePromise;
    return { stripe, clientSecret: response.data.clientSecret };
  }
  
  return response.data;
};
```

### 5. Feature Availability Components

```typescript
// src/components/FeatureGuard.tsx
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useSubscription } from '../hooks/useSubscription';

interface FeatureGuardProps {
  requiredFeature: string;
  fallbackPath?: string;
  children: React.ReactNode;
}

export const FeatureGuard: React.FC<FeatureGuardProps> = ({ 
  requiredFeature, 
  fallbackPath = '/upgrade', 
  children 
}) => {
  const { subscription, loading, error } = useSubscription();
  
  if (loading) {
    return <div>Loading...</div>;
  }
  
  if (error) {
    return <div>Error loading subscription status</div>;
  }
  
  // Check if the feature is available in the current subscription
  const hasFeature = subscription?.features?.includes(requiredFeature);
  
  if (!hasFeature) {
    return <Navigate to={fallbackPath} />;
  }
  
  return <>{children}</>;
};
```

### 6. Document Upload and Processing
### 7. Document Pattern Recognition Integration

```typescript
// src/services/document.service.ts
import apiClient from './api.client';

export const detectDocumentPatterns = async (documentId: string, query: string) => {
  try {
    const response = await apiClient.post(`/documents/${documentId}/patterns`, {
      query
    });
    return response.data;
  } catch (error) {
    if (error.response?.status === 403) {
      // Handle subscription tier restrictions
      throw new Error('Pattern detection requires a higher subscription tier');
    }
    throw error;
  }
};

// src/components/PatternDetection.tsx
import React, { useState } from 'react';
import { detectDocumentPatterns } from '../services/document.service';

interface Pattern {
  patternType: string;
  content: string;
  location: {
    startIndex: number;
    endIndex: number;
  };
  metadata: {
    riskLevel: string;
    recommendations: string[];
  };
}

export const PatternDetection: React.FC<{ documentId: string }> = ({ documentId }) => {
  const [patterns, setPatterns] = useState<Pattern[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleDetectPatterns = async (query: string) => {
    try {
      setLoading(true);
      setError(null);
      const detectedPatterns = await detectDocumentPatterns(documentId, query);
      setPatterns(detectedPatterns);
    } catch (error) {
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="pattern-detection">
      <h3>Pattern Detection</h3>
      
      <div className="search-box">
        <input
          type="text"
          placeholder="Enter pattern query (e.g., 'Find all force majeure clauses')"
          onKeyPress={(e) => {
            if (e.key === 'Enter') {
              handleDetectPatterns(e.currentTarget.value);
            }
          }}
        />
        {loading && <div className="loading">Detecting patterns...</div>}
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      <div className="patterns-list">
        {patterns.map((pattern, index) => (
          <div key={index} className="pattern-item">
            <div className="pattern-header">
              <span className="pattern-type">{pattern.patternType}</span>
              <span className={`risk-level risk-${pattern.metadata.riskLevel}`}>
                {pattern.metadata.riskLevel} risk
              </span>
            </div>
            <div className="pattern-content">{pattern.content}</div>
            {pattern.metadata.recommendations.length > 0 && (
              <div className="recommendations">
                <h4>Recommendations:</h4>
                <ul>
                  {pattern.metadata.recommendations.map((rec, i) => (
                    <li key={i}>{rec}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

// Usage in your document viewer
import { PatternDetection } from '../components/PatternDetection';

const DocumentViewer = ({ documentId }) => {
  return (
    <div>
      {/* Other document viewing components */}
      <PatternDetection documentId={documentId} />
    </div>
  );
};
```

### 8. Implementation Timeline Update

The implementation timeline from Week 3 should now include pattern recognition features:

```typescript
// Week 3: Chat, Analysis & Pattern Features
1. Implement basic chat interface
2. Set up document analysis components
3. Add pattern recognition UI components
4. Integrate subscription-based feature guards
5. Implement real-time pattern highlighting
6. Add pattern search history and favorites
7. Set up error handling and loading states
```

### 9. Best Practices for Pattern Recognition

1. **Efficient Pattern Searching:**
   - Cache pattern detection results for frequently used queries
   - Implement debouncing for pattern search input
   - Show loading states during detection

2. **Error Handling:**
   - Handle subscription-related errors gracefully
   - Provide clear feedback when pattern detection fails
   - Implement retry logic for failed requests

3. **UI/UX Considerations:**
   - Highlight detected patterns in the document viewer
   - Provide filtering options for different pattern types
   - Allow saving and sharing detected patterns

4. **Performance Optimization:**
   ```typescript
   // Example of optimized pattern detection with debouncing
   import { debounce } from 'lodash';

   const debouncedPatternDetection = debounce(async (query: string) => {
     // Implementation
   }, 500);
   ```


```typescript
// src/services/document.service.ts
import apiClient from './api.client';

export const uploadDocument = async (file, metadata = {}) => {
  const formData = new FormData();
  formData.append('file', file);
  
  if (Object.keys(metadata).length > 0) {
    formData.append('metadata', JSON.stringify(metadata));
  }
  
  const response = await apiClient.post('/documents/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  
  return response.data;
};

export const getDocuments = async (page = 1, limit = 10, sortBy = 'uploadedAt', sortOrder = 'desc', query = '') => {
  const response = await apiClient.get('/documents', {
    params: { page, limit, sortBy, sortOrder, query }
  });
  
  return response.data;
};

export const getDocument = async (documentId) => {
  const response = await apiClient.get(`/documents/${documentId}`);
  return response.data;
};

export const runAnalysis = async (documentId, analysisType = 'basic', options = {}) => {
  const response = await apiClient.post('/analysis', {
    documentId,
    analysisType,
    options
  });
  
  return response.data;
};

export const runAdvancedAnalysis = async (documentId, options = {}) => {
  const response = await apiClient.post('/analysis/advanced', {
    documentId,
    analysisType: 'advanced',
    options
  });
  
  return response.data;
};

// Note: The backend AI service might occasionally wrap its JSON response
// in markdown code fences (e.g., ```json [...] ```). The backend API is
// designed to handle this and will always return a clean JSON array to the frontend.
// The frontend does not need to implement any special handling for this.

export const chatWithDocument = async (documentId, message, sessionId = null) => {
  const response = await apiClient.post(`/documents/${documentId}/chat`, {
    message,
    sessionId
  });
  
  return response.data;
};
```

### 7. Handling Subscription Limits and Upgrades

```typescript
// src/components/SubscriptionAlert.tsx
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useSubscription } from '../hooks/useSubscription';
import { Alert, Button, Progress } from 'your-ui-library';

export const SubscriptionAlert = () => {
  const { subscription, loading } = useSubscription();
  const [usagePercentage, setUsagePercentage] = useState(0);
  const [alertType, setAlertType] = useState('info');
  
  useEffect(() => {
    if (subscription && !loading) {
      // Calculate document usage percentage
      const documentsUsed = subscription.usageStats.documentsProcessed;
      const documentsLimit = getDocumentLimit(subscription.tier);
      
      if (documentsLimit > 0) {
        const percentage = Math.min(100, Math.round((documentsUsed / documentsLimit) * 100));
        setUsagePercentage(percentage);
        
        // Set alert type based on usage
        if (percentage > 90) {
          setAlertType('error');
        } else if (percentage > 70) {
          setAlertType('warning');
        } else {
          setAlertType('info');
        }
      }
    }
  }, [subscription, loading]);
  
  // Helper function to get the document limit based on tier
  const getDocumentLimit = (tier) => {
    switch (tier) {
      case 'free': return 10;
      case 'pro': return 200;
      case 'admin': return -1; // unlimited
      default: return 10;
    }
  };
  
  if (loading || !subscription) {
    return null;
  }
  
  // Don't show for unlimited plans
  if (getDocumentLimit(subscription.tier) === -1) {
    return null;
  }
  
  return (
    <Alert type={alertType}>
      <h4>Document Usage</h4>
      <Progress value={usagePercentage} />
      <p>
        {subscription.usageStats.documentsProcessed} of {getDocumentLimit(subscription.tier)} documents used
        {usagePercentage > 70 && (
          <Button as={Link} to="/upgrade" size="small" style={{ marginLeft: '8px' }}>
            Upgrade Plan
          </Button>
        )}
      </p>
    </Alert>
  );
};
```

### 8. Implementing Stripe Payment Flow

```typescript
// src/components/SubscriptionCheckout.tsx
import React, { useState, useEffect } from 'react';
import {
  CardElement,
  useStripe,
  useElements,
  Elements
} from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import { createSubscription } from '../services/subscription.service';

const stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLIC_KEY);

const CheckoutForm = ({ tierSelected, onSuccess, onCancel }) => {
  const [succeeded, setSucceeded] = useState(false);
  const [error, setError] = useState(null);
  const [processing, setProcessing] = useState(false);
  const [clientSecret, setClientSecret] = useState('');
  const stripe = useStripe();
  const elements = useElements();
  
  useEffect(() => {
    // Create subscription and get client secret
    const getClientSecret = async () => {
      try {
        setProcessing(true);
        const { clientSecret } = await createSubscription({
          tier: tierSelected,
          email: JSON.parse(localStorage.getItem('user')).email
        });
        setClientSecret(clientSecret);
      } catch (error) {
        setError(error.message);
      } finally {
        setProcessing(false);
      }
    };
    
    getClientSecret();
  }, [tierSelected]);
  
  const handleSubmit = async (event) => {
    event.preventDefault();
    setProcessing(true);
    
    if (!stripe || !elements) {
      return;
    }
    
    const result = await stripe.confirmCardPayment(clientSecret, {
      payment_method: {
        card: elements.getElement(CardElement),
        billing_details: {
          email: JSON.parse(localStorage.getItem('user')).email,
        },
      }
    });
    
    if (result.error) {
      setError(`Payment failed: ${result.error.message}`);
      setProcessing(false);
    } else {
      setError(null);
      setSucceeded(true);
      onSuccess();
    }
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <h2>Subscribe to {tierSelected} Plan</h2>
      
      <CardElement options={{
        style: {
          base: {
            fontSize: '16px',
            color: '#424770',
            '::placeholder': {
              color: '#aab7c4',
            },
          },
          invalid: {
            color: '#9e2146',
          },
        },
      }} />
      
      {error && <div className="error">{error}</div>}
      
      <button 
        type="submit" 
        disabled={processing || !stripe || !clientSecret}
      >
        {processing ? 'Processing...' : 'Subscribe'}
      </button>
      
      <button type="button" onClick={onCancel} disabled={processing}>
        Cancel
      </button>
    </form>
  );
};

export const SubscriptionCheckout = ({ tierSelected, onSuccess, onCancel }) => {
  return (
    <Elements stripe={stripePromise}>
      <CheckoutForm 
        tierSelected={tierSelected} 
        onSuccess={onSuccess}
        onCancel={onCancel}
      />
    </Elements>
  );
};
```

## UI Component Examples

### Dashboard with Subscription Status

```tsx
// src/pages/Dashboard.tsx
import React from 'react';
import { useSubscription } from '../hooks/useSubscription';
import { useDocuments } from '../hooks/useDocuments';
import { SubscriptionAlert } from '../components/SubscriptionAlert';
import { DocumentList } from '../components/DocumentList';
import { FeatureGuard } from '../components/FeatureGuard';
import { Button, Card, Grid } from 'your-ui-library';

export const Dashboard = () => {
  const { subscription, loading: subLoading } = useSubscription();
  const { documents, loading: docsLoading } = useDocuments();
  
  return (
    <div className="dashboard">
      <h1>Dashboard</h1>
      
      {!subLoading && subscription && (
        <Card className="subscription-status">
          <h3>Subscription Status</h3>
          <p>Current Plan: <strong>{subscription.tier}</strong></p>
          <p>Status: <span className={`status-${subscription.status}`}>{subscription.status}</span></p>
          <p>Renews: {new Date(subscription.currentPeriodEnd).toLocaleDateString()}</p>
          
          <SubscriptionAlert />
          
          {subscription.tier !== 'admin' && (
            <Button to="/upgrade">Upgrade Plan</Button>
          )}
        </Card>
      )}
      
      <Grid columns={2}>
        <Card>
          <h3>Recent Documents</h3>
          {docsLoading ? (
            <p>Loading documents...</p>
          ) : (
            <DocumentList documents={documents.slice(0, 5)} />
          )}
          <Button to="/documents">View All</Button>
        </Card>
        
        <FeatureGuard requiredFeature="advanced_analysis">
          <Card>
            <h3>Advanced Analysis</h3>
            <p>You have access to advanced document analysis features.</p>
            <Button to="/documents/analysis">Run Analysis</Button>
          </Card>
        </FeatureGuard>
      </Grid>
    </div>
  );
};
```

## Recommended Project Structure

```
src/
├── assets/
│   ├── images/
│   └── styles/
├── components/
│   ├── common/
│   │   ├── Header.tsx
│   │   ├── Footer.tsx
│   │   ├── Sidebar.tsx
│   │   └── ...
│   ├── documents/
│   │   ├── DocumentCard.tsx
│   │   ├── DocumentList.tsx
│   │   ├── UploadForm.tsx
│   │   └── ...
│   ├── analysis/
│   │   ├── AnalysisResults.tsx
│   │   ├── AnalysisForm.tsx
│   │   └── ...
│   ├── chat/
│   │   ├── ChatBox.tsx
│   │   ├── MessageList.tsx
│   │   └── ...
│   └── subscription/
│       ├── PlanSelector.tsx
│       ├── SubscriptionAlert.tsx
│       ├── SubscriptionCheckout.tsx
│       └── ...
├── contexts/
│   ├── AuthContext.tsx
│   ├── OrganizationContext.tsx
│   └── ...
├── hooks/
│   ├── useAuth.ts
│   ├── useDocuments.ts
│   ├── useSubscription.ts
│   ├── useChat.ts
│   └── ...
├── pages/
│   ├── auth/
│   │   ├── Login.tsx
│   │   ├── Register.tsx
│   │   └── ...
│   ├── Dashboard.tsx
│   ├── Documents.tsx
│   ├── DocumentView.tsx
│   ├── Analysis.tsx
│   ├── Chat.tsx
│   ├── Subscription.tsx
│   └── ...
├── services/
│   ├── api.client.ts
│   ├── auth.service.ts
│   ├── document.service.ts
│   ├── analysis.service.ts
│   ├── chat.service.ts
│   ├── subscription.service.ts
│   └── ...
├── types/
│   ├── auth.types.ts
│   ├── document.types.ts
│   ├── analysis.types.ts
│   ├── chat.types.ts
│   ├── subscription.types.ts
│   └── ...
├── utils/
│   ├── formatters.ts
│   ├── validators.ts
│   └── ...
├── App.tsx
└── index.tsx
```

## Implementation Timeline Recommendation

1. **Week 1: Core Infrastructure**
   - Authentication flows
   - API client with interceptors
   - Multi-tenant organization context
   - Basic layout and navigation

2. **Week 2: Document Management**
   - Document upload
   - Document listing and viewing
   - Basic analysis integration

3. **Week 3: Chat & Analysis Features**
   - Chat interface
   - Advanced analysis UI
   - Feature availability guards

4. **Week 4: Subscription Management**
   - Stripe integration
   - Subscription tier UI
   - Usage tracking visuals
   - Account management screens

5. **Week 5: Refinement & Testing**
   - Cross-browser testing
   - Responsive design adjustments
   - Performance optimization
   - User feedback iteration

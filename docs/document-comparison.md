# Document Comparison API Documentation

## Overview

The Document Comparison feature allows users to analyze and compare multiple legal documents, identifying relationships, conflicts, and complementary provisions between them. This document outlines the API endpoints, request/response formats, and implementation guidelines for frontend integration.

## Endpoints

### Basic Document Comparison

- **URL**: `/api/documents/comparison`
- **Method**: `POST`
- **Content-Type**: `application/json`
- **Authorization**: `Bearer <your-jwt-token>`
- **Required Feature**: `basic_comparison`

#### Request Body

```json
{
  "documentA": "Content of the first document",
  "documentB": "Content of the second document",
  "documentAId": "optional-id-of-document-a",
  "documentBId": "optional-id-of-document-b"
}
```

#### Parameters

| Parameter   | Type   | Required | Description                                      |
|-------------|--------|----------|--------------------------------------------------|
| documentA   | string | Yes      | Content of the first document to compare         |
| documentB   | string | Yes      | Content of the second document to compare        |
| documentAId | string | No       | ID of the first document (for analytics tracking)|
| documentBId | string | No       | ID of the second document (for analytics tracking)|

#### Response

```json
{
  "status": "success",
  "data": {
    "diffs": [
      {
        "type": "equal",
        "text": "Text that is the same in both documents"
      },
      {
        "type": "delete",
        "text": "Text that was removed"
      },
      {
        "type": "insert",
        "text": "Text that was added"
      }
    ],
    "metadata": {
      "timestamp": "2025-05-09T11:15:30Z",
      "documentStats": {
        "documentA": {
          "length": 1000,
          "wordCount": 200,
          "charCount": 1000
        },
        "documentB": {
          "length": 1200,
          "wordCount": 240,
          "charCount": 1200
        }
      },
      "summary": {
        "addedLines": 10,
        "removedLines": 5,
        "modifiedLines": 15,
        "totalChanges": 30
      }
    },
    "visualization": {
      "htmlDiff": "<pre style=\"white-space: pre-wrap;\">...[HTML visualization of differences]...</pre>",
      "colors": {
        "addition": "#e6ffe6",
        "deletion": "#ffe6e6",
        "modification": "#fff5e6"
      }
    }
  },
  "metadata": {
    "timestamp": "2025-05-09T11:15:30Z",
    "documentStats": {
      "documentA": { "length": 1000, "id": "document-a-id" },
      "documentB": { "length": 1200, "id": "document-b-id" }
    }
  }
}
```

#### Response Structure Details

##### diffs
An array of text segments showing the differences between the documents:

| Field | Type   | Description                                                |
|-------|--------|------------------------------------------------------------|  
| type  | string | Type of difference: "equal", "insert", or "delete"        |
| text  | string | The text content for this segment                          |

##### metadata
Contains information about the comparison:

| Field        | Type   | Description                                        |
|------------- |--------|----------------------------------------------------|  
| timestamp    | string | ISO timestamp when the comparison was performed    |
| documentStats| object | Statistics about both documents                    |
| summary      | object | Summary of the changes between documents           |

##### visualization
Provides visual representation of the differences:

| Field    | Type   | Description                                        |
|----------|--------|----------------------------------------------------|  
| htmlDiff | string | HTML-formatted visualization of the differences    |
| colors   | object | Color codes used for different types of changes    |

#### Example Usage

```javascript
const response = await fetch('http://localhost:4000/documents/comparison', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-jwt-token'
  },
  body: JSON.stringify({
    documentA: "This is the original document text.",
    documentB: "This is the modified document text.",
    documentAId: "original-doc-id",
    documentBId: "modified-doc-id"
  })
});

const result = await response.json();
console.log(result.data.visualization.htmlDiff); // HTML visualization of differences
```

### Compare Multiple Documents

- **URL**: `/api/documents/analyze-multiple`
- **Method**: `POST`
- **Content-Type**: `application/json`
- **Authorization**: `Bearer <your-jwt-token>`

#### Request Body

```json
{
  "primaryDocumentId": "string (UUID)",
  "relatedDocumentIds": ["string (UUID)", "string (UUID)"],
  "documentType": "string (optional)",
  "documentTypes": ["string", "string"] (optional)
}
```

#### Parameters

| Parameter          | Type   | Required | Description                                                       |
| ------------------ | ------ | -------- | ----------------------------------------------------------------- |
| primaryDocumentId  | string | Yes      | UUID of the main document to analyze                              |
| relatedDocumentIds | array  | Yes      | Array of UUIDs for documents to compare with the primary document |
| documentType       | string | No       | Type of the primary document (e.g., "CONTRACT", "AGREEMENT")      |
| documentTypes      | array  | No       | Array of document types corresponding to each document            |

#### Response

The response follows a structured JSON format with the following components:

```json
{
  "documentSet": {
    "count": 2,
    "documentTypes": ["CONTRACT", "STATEMENT_OF_WORK"],
    "relationships": [
      {
        "primaryDocument": "CONTRACT",
        "relatedDocument": "STATEMENT_OF_WORK",
        "relationship": "supplement",
        "description": "SOW provides specific deliverables under the contract framework"
      }
    ]
  },
  "typeSpecificAnalysis": {
    "dominantDocumentType": "CONTRACT",
    "findings": [
      {
        "documentType": "CONTRACT",
        "observation": "Contains standard service terms with general obligations",
        "implication": "Establishes the contractual framework for all related documents"
      }
    ]
  },
  "keyProvisions": [
    {
      "topic": "Term and Termination",
      "provisions": [
        {
          "document": "CONTRACT",
          "content": "Either party may terminate with 30 days written notice.",
          "sectionReference": "Section 12.1",
          "conflicts": ""
        },
        {
          "document": "STATEMENT_OF_WORK",
          "content": "This SOW will terminate upon completion of deliverables or as per the MSA.",
          "sectionReference": "Section 4",
          "conflicts": "Potentially conflicts with CONTRACT Section 12.1 if deliverables are completed in less than 30 days"
        }
      ],
      "significance": "medium",
      "recommendation": "Clarify which termination provision takes precedence in case of conflict"
    }
  ],
  "gaps": [
    {
      "area": "Insurance",
      "description": "The STATEMENT_OF_WORK does not address insurance requirements",
      "recommendation": "Add insurance requirements to the SOW or reference the master agreement",
      "priority": "medium"
    }
  ],
  "summary": {
    "overallCoherence": "medium",
    "keyInsights": [
      "Documents form a coherent set with clear relationships",
      "Some conflicts exist in termination and payment provisions"
    ],
    "recommendations": [
      "Clarify precedence between documents",
      "Address identified gaps in coverage"
    ]
  }
}
```

## Response Structure Details

### documentSet

Provides an overview of the documents being compared:

| Field         | Type   | Description                                 |
| ------------- | ------ | ------------------------------------------- |
| count         | number | Total number of documents in the comparison |
| documentTypes | array  | Types of documents identified in the set    |
| relationships | array  | Relationships between documents             |

#### Relationship Types

- `amendment`: One document amends or modifies another
- `supplement`: One document supplements or adds to another
- `replacement`: One document replaces another
- `reference`: One document references another
- `independent`: Documents are independent of each other

### typeSpecificAnalysis

Analysis specific to each document type:

| Field                | Type   | Description                                             |
| -------------------- | ------ | ------------------------------------------------------- |
| dominantDocumentType | string | The primary document type that governs the relationship |
| findings             | array  | Observations and implications for each document type    |

### keyProvisions

Detailed analysis of important provisions across documents:

| Field          | Type   | Description                                                 |
| -------------- | ------ | ----------------------------------------------------------- |
| topic          | string | The subject matter of the provision (e.g., "Payment Terms") |
| provisions     | array  | Comparison of this provision across documents               |
| significance   | string | Importance of differences ("low", "medium", "high")         |
| recommendation | string | Suggested action to address any conflicts                   |

#### Provision Details

Each provision contains:

| Field            | Type   | Description                                            |
| ---------------- | ------ | ------------------------------------------------------ |
| document         | string | Document type containing this provision                |
| content          | string | Extracted text of the provision                        |
| sectionReference | string | Section number or location in the document             |
| conflicts        | string | Description of conflicts with other documents (if any) |

### gaps

Identifies missing elements across the document set:

| Field          | Type   | Description                                                |
| -------------- | ------ | ---------------------------------------------------------- |
| area           | string | Topic area with a gap                                      |
| description    | string | Description of the missing element                         |
| recommendation | string | Suggested action to address the gap                        |
| priority       | string | Importance of addressing the gap ("low", "medium", "high") |

### summary

Overall assessment of the document comparison:

| Field            | Type   | Description                                                    |
| ---------------- | ------ | -------------------------------------------------------------- |
| overallCoherence | string | Level of coherence between documents ("low", "medium", "high") |
| keyInsights      | array  | Important observations about the document set                  |
| recommendations  | array  | Suggested actions based on the analysis                        |

## Frontend Implementation Guidelines

### Displaying Document Comparison Results

1. **Document Set Overview**

   - Show document types and relationships in a visual diagram
   - Use relationship types to indicate connections between documents

2. **Key Provisions Comparison**

   - Group provisions by topic
   - For each topic, display side-by-side comparison of provisions from each document
   - Highlight conflicts and include section references
   - Use color coding based on significance (e.g., red for high, yellow for medium, green for low)

3. **Gaps Analysis**

   - Display gaps in a separate section, sorted by priority
   - Include recommendations for addressing each gap

4. **Summary and Recommendations**
   - Present overall coherence with a visual indicator
   - List key insights and recommendations prominently

### Example UI Components

#### Provision Comparison Card

```jsx
<ProvisionCard
  topic="Payment Terms"
  provisions={[
    {
      document: 'CONTRACT',
      content: 'Payment due within 30 days of invoice.',
      sectionReference: 'Section 5.2',
      conflicts: '',
    },
    {
      document: 'STATEMENT_OF_WORK',
      content:
        'Payment due within 45 days of invoice with milestone-based billing.',
      sectionReference: 'Section 3.1',
      conflicts:
        'Directly conflicts with CONTRACT Section 5.2 payment timeline',
    },
  ]}
  significance="high"
  recommendation="Harmonize payment terms or explicitly acknowledge the SOW overrides the contract for this specific project"
/>
```

#### Document Relationship Diagram

```jsx
<DocumentRelationshipDiagram
  documents={[
    { type: 'CONTRACT', id: 'doc1' },
    { type: 'STATEMENT_OF_WORK', id: 'doc2' },
  ]}
  relationships={[
    {
      primaryDocument: 'CONTRACT',
      relatedDocument: 'STATEMENT_OF_WORK',
      relationship: 'supplement',
      description:
        'SOW provides specific deliverables under the contract framework',
    },
  ]}
/>
```

## Error Handling

The API may return the following error responses:

| Status Code | Description           | Possible Cause                                        |
| ----------- | --------------------- | ----------------------------------------------------- |
| 400         | Bad Request           | Invalid document IDs or missing required parameters   |
| 404         | Not Found             | One or more documents not found                       |
| 401         | Unauthorized          | Invalid or expired authentication token               |
| 403         | Forbidden             | User does not have permission to access the documents |
| 500         | Internal Server Error | Server-side processing error                          |

## Best Practices

1. **Document Type Consistency**

   - Once a document type is identified, it is used consistently throughout the analysis
   - Frontend should maintain this consistency in the UI

2. **Section References**

   - Each provision includes a section reference to help users locate it in the original document
   - Consider implementing a feature to jump directly to the referenced section in the document viewer

3. **Conflict Highlighting**

   - Conflicts between documents are explicitly noted in the `conflicts` field
   - Use visual indicators to highlight these conflicts in the UI

4. **Significance Levels**
   - Use consistent visual indicators for significance levels:
     - High: Red or critical indicator
     - Medium: Yellow or warning indicator
     - Low: Blue or informational indicator

## Changelog

### April 17, 2025

- Added section references to each provision
- Ensured document type consistency throughout the analysis
- Integrated significance levels and recommendations for each topic
- Removed separate document sections category and integrated section references into key provisions

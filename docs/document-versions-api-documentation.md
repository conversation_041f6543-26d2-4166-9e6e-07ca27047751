# Document Versions API Documentation

## Overview
This documentation describes the API endpoint for retrieving document versions. This endpoint allows clients to fetch the version history of a document along with metadata about each version.

## API Endpoint

### Get Document Versions

\`\`\`http
GET /api/documents/:id/versions
Authorization: Bearer <token>
\`\`\`

#### Path Parameters
- \`id\` - The unique identifier of the document

#### Headers
- \`Authorization\` - Bearer token for authentication

#### Response
Status: 200 OK
Content-Type: application/json

\`\`\`typescript
interface DocumentVersion {
  id: string;              // Unique identifier for the version
  documentId: string;      // Reference to parent document
  version: number;         // Version number (incremental)
  content: string;         // Document content at this version
  metadata: {
    patternAnalysis?: "in_progress" | "completed";  // Analysis status
    patterns?: any[];      // Detected patterns in this version
    status?: string;       // Processing status
    lastUpdated?: string;  // Last update timestamp
    characterCount?: number; // Character count for this version
    wordCount?: number;    // Word count for this version
  };
  createdAt: string;       // Version creation timestamp
  updatedAt: string;       // Version last update timestamp
}

// Response type
type Response = DocumentVersion[]
\`\`\`

#### Error Responses

1. **401 Unauthorized**
   - When the authorization token is missing or invalid

2. **404 Not Found**
   - When the document with the specified ID does not exist

3. **403 Forbidden**
   - When the user does not have permission to access the document

4. **500 Internal Server Error**
   - When there is a server-side error processing the request

#### Example Response

\`\`\`json
[
  {
    "id": "v123",
    "documentId": "doc456",
    "version": 2,
    "content": "Updated document content",
    "metadata": {
      "patternAnalysis": "completed",
      "patterns": [],
      "status": "processed",
      "lastUpdated": "2025-04-19T07:10:57.836Z",
      "characterCount": 22,
      "wordCount": 3
    },
    "createdAt": "2025-04-19T07:10:57.836Z",
    "updatedAt": "2025-04-19T07:10:57.836Z"
  },
  {
    "id": "v122",
    "documentId": "doc456",
    "version": 1,
    "content": "Original document content",
    "metadata": {
      "patternAnalysis": "completed",
      "patterns": [],
      "status": "processed",
      "lastUpdated": "2025-04-19T07:00:00.000Z",
      "characterCount": 23,
      "wordCount": 3
    },
    "createdAt": "2025-04-19T07:00:00.000Z",
    "updatedAt": "2025-04-19T07:00:00.000Z"
  }
]
\`\`\`

## Usage Notes

1. Versions are returned in descending order (newest first)
2. Each version contains a complete snapshot of the document at that point
3. Metadata includes analysis status and document statistics
4. The endpoint supports pagination for documents with many versions

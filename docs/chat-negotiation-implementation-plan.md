# **💬 Chat Negotiation System - Implementation Plan**

## **📋 Overview**

Implementation plan for the **Interactive Chat Negotiation** feature requested by the frontend team. This system bridges natural language chat with the existing negotiation simulator and gamification systems.

## **🎯 Frontend Request Summary**

The frontend team has completed:
- ✅ Interactive chat interface with AI characters
- ✅ Natural language processing (client-side)
- ✅ Relationship tracking UI (trust, respect, pressure)
- ✅ Multiple negotiation scenarios
- ✅ Graceful fallback to demo mode

**They need backend implementation for:**
- ❌ Chat Negotiation Bridge Service
- ❌ AI Response Generation
- ❌ Advanced NLP for data extraction
- ❌ Real-time relationship tracking

## **🏗️ Implementation Strategy**

### **Phase 1: MVP Implementation (1 Week)**

#### **1.1 Database Schema (Day 1)**
```typescript
// MongoDB Schema for Chat Negotiation Sessions
export const ChatNegotiationSessionSchema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  negotiationSessionId: { type: Schema.Types.ObjectId, ref: 'NegotiationSession', required: true },
  chatSessionId: { type: Schema.Types.ObjectId, ref: 'ChatSession', required: true },
  scenarioId: { type: String, required: true },
  status: { 
    type: String, 
    enum: ['active', 'paused', 'completed', 'abandoned'], 
    default: 'active' 
  },
  currentRound: { type: Number, default: 1 },
  extractedTerms: { type: Schema.Types.Mixed, default: {} },
  relationshipMetrics: {
    trust: { type: Number, default: 50, min: 0, max: 100 },
    respect: { type: Number, default: 50, min: 0, max: 100 },
    pressure: { type: Number, default: 20, min: 0, max: 100 }
  },
  score: { type: Number, default: 5.0, min: 0, max: 10 },
  aiPersonality: { type: Schema.Types.Mixed, default: {} },
  organizationId: { type: Schema.Types.ObjectId, ref: 'Organization', required: true }
}, {
  timestamps: true
});
```

#### **1.2 Core Service Implementation (Days 2-3)**
```typescript
// src/modules/chat-negotiation/services/chat-negotiation.service.ts
@Injectable()
export class ChatNegotiationService {
  constructor(
    @InjectModel('ChatNegotiationSession') private chatNegotiationModel: Model<ChatNegotiationSession>,
    private negotiationSimulatorService: NegotiationSimulatorService,
    private chatService: ChatService,
    private gamificationService: GamificationService
  ) {}

  async createSession(userId: string, scenarioId: string, aiPersonality?: any) {
    // 1. Create negotiation session
    const negotiationSession = await this.negotiationSimulatorService.startSession({
      scenarioId,
      userId,
      characterId: aiPersonality?.characterId || 'default_character'
    });

    // 2. Create chat session
    const chatSession = await this.chatService.createSession({
      userId,
      title: `Negotiation: ${scenarioId}`,
      type: 'negotiation'
    });

    // 3. Create bridge session
    const chatNegotiationSession = new this.chatNegotiationModel({
      userId,
      negotiationSessionId: negotiationSession._id,
      chatSessionId: chatSession._id,
      scenarioId,
      aiPersonality: aiPersonality || this.getDefaultPersonality(scenarioId),
      organizationId: negotiationSession.organizationId
    });

    return await chatNegotiationSession.save();
  }

  async processChatMove(sessionId: string, userId: string, message: string, extractedData?: any) {
    const session = await this.getChatNegotiationSession(sessionId, userId);
    
    // 1. Extract data from message
    const enhancedData = await this.enhanceExtractedData(message, extractedData, session);
    
    // 2. Send user message to chat
    const userMessage = await this.chatService.sendMessage({
      sessionId: session.chatSessionId,
      content: message,
      userId,
      metadata: { extractedData: enhancedData }
    });

    // 3. Convert to negotiation move
    const negotiationMove = this.convertToNegotiationMove(message, enhancedData);
    
    // 4. Process through negotiation simulator
    const updatedNegotiation = await this.negotiationSimulatorService.makeMove(
      session.negotiationSessionId,
      negotiationMove,
      userId
    );

    // 5. Generate AI response
    const aiResponse = await this.generateAIResponse(session, enhancedData, updatedNegotiation);
    
    // 6. Send AI message to chat
    const aiMessage = await this.chatService.sendMessage({
      sessionId: session.chatSessionId,
      content: aiResponse.content,
      userId: 'ai',
      metadata: { 
        suggestions: aiResponse.suggestions,
        extractedData: aiResponse.extractedData 
      }
    });

    // 7. Update session metrics
    const updatedSession = await this.updateSessionMetrics(session, enhancedData, aiResponse);

    return {
      userMessage,
      aiMessage,
      sessionUpdate: updatedSession
    };
  }
}
```

#### **1.3 API Controllers (Days 4-5)**
```typescript
// src/modules/chat-negotiation/controllers/chat-negotiation.controller.ts
@Controller('chat-negotiation')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ChatNegotiationController {
  constructor(private chatNegotiationService: ChatNegotiationService) {}

  @Post('sessions')
  async createSession(@Body() createSessionDto: CreateChatNegotiationSessionDto, @Req() req) {
    return this.chatNegotiationService.createSession(
      req.user.id,
      createSessionDto.scenarioId,
      createSessionDto.aiPersonality
    );
  }

  @Get('sessions/:id')
  async getSession(@Param('id') id: string, @Req() req) {
    return this.chatNegotiationService.getChatNegotiationSession(id, req.user.id);
  }

  @Post('sessions/:id/moves')
  async sendMove(@Param('id') id: string, @Body() moveDto: ChatMoveDto, @Req() req) {
    return this.chatNegotiationService.processChatMove(
      id,
      req.user.id,
      moveDto.content,
      moveDto.extractedData
    );
  }

  @Get('sessions')
  async getUserSessions(@Req() req, @Query() query: GetSessionsQueryDto) {
    return this.chatNegotiationService.getUserSessions(req.user.id, query);
  }
}
```

### **Phase 2: Enhanced Features (Week 2)**

#### **2.1 Advanced NLP Service**
```typescript
// src/modules/chat-negotiation/services/nlp.service.ts
@Injectable()
export class NLPService {
  async extractDataFromMessage(message: string, context?: any) {
    return {
      offer: await this.extractFinancialTerms(message),
      strategy: await this.detectStrategy(message, context),
      sentiment: await this.analyzeSentiment(message),
      entities: await this.extractEntities(message),
      confidence: 0.85
    };
  }

  private async extractFinancialTerms(message: string) {
    // Price extraction patterns
    const pricePatterns = [
      /\$?([\d,]+(?:\.\d{2})?)\s*(?:k|thousand)/i,
      /\$?([\d,]+(?:\.\d{2})?)\s*(?:m|million)/i,
      /\$?([\d,]+(?:\.\d{2})?)/
    ];

    // Implementation for extracting prices, terms, conditions
    return { price: extractedPrice, currency: 'USD', terms: extractedTerms };
  }

  private async detectStrategy(message: string, context?: any) {
    const strategies = {
      collaborative: ['work together', 'mutual', 'partnership', 'win-win'],
      competitive: ['need', 'must have', 'require', 'demand', 'final offer'],
      accommodating: ['flexible', 'open to', 'willing', 'consider'],
      analytical: ['data', 'research', 'market rate', 'industry standard']
    };

    // Strategy detection logic
    return detectedStrategy;
  }
}
```

#### **2.2 AI Response Generation**
```typescript
// src/modules/chat-negotiation/services/ai-response.service.ts
@Injectable()
export class AIResponseService {
  async generateResponse(session: ChatNegotiationSession, userMove: any, context: any) {
    // 1. Analyze user move
    const moveAnalysis = await this.analyzeUserMove(userMove, session);
    
    // 2. Generate contextual response based on AI personality
    const response = await this.generateContextualResponse({
      userMove,
      aiPersonality: session.aiPersonality,
      relationshipMetrics: session.relationshipMetrics,
      scenarioContext: context,
      currentRound: session.currentRound
    });

    // 3. Generate suggestions
    const suggestions = await this.generateSuggestions(session, userMove);

    return {
      content: response.content,
      extractedData: response.extractedData,
      suggestions,
      relationshipUpdate: response.relationshipImpact,
      scoreUpdate: response.scoreImpact
    };
  }

  private async generateContextualResponse(context: any) {
    // Template-based response generation with personality adjustments
    const templates = this.getResponseTemplates(context.scenarioContext.type);
    let response = this.selectBaseResponse(templates, context.userMove, context.aiPersonality);
    
    // Adjust for relationship state
    response = this.adjustForRelationship(response, context.relationshipMetrics);
    
    return {
      content: response,
      extractedData: { strategy: 'collaborative', sentiment: 'positive' },
      relationshipImpact: this.calculateRelationshipImpact(context.userMove, context.aiPersonality),
      scoreImpact: this.calculateScoreImpact(context.userMove, context.relationshipMetrics)
    };
  }
}
```

### **Phase 3: Real-time & Advanced Features (Week 3)**

#### **3.1 WebSocket Integration**
```typescript
// src/modules/chat-negotiation/gateways/chat-negotiation.gateway.ts
@WebSocketGateway({
  namespace: '/chat-negotiation',
  cors: { origin: '*' }
})
export class ChatNegotiationGateway {
  @WebSocketServer() server: Server;

  @SubscribeMessage('join_chat_negotiation')
  handleJoinSession(client: Socket, payload: { sessionId: string, userId: string }) {
    client.join(`chat_negotiation_${payload.sessionId}`);
    client.emit('joined_session', { sessionId: payload.sessionId });
  }

  @SubscribeMessage('leave_chat_negotiation')
  handleLeaveSession(client: Socket, payload: { sessionId: string }) {
    client.leave(`chat_negotiation_${payload.sessionId}`);
  }

  emitSessionUpdate(sessionId: string, update: any) {
    this.server.to(`chat_negotiation_${sessionId}`).emit('session_updated', update);
  }

  emitAITyping(sessionId: string, isTyping: boolean) {
    this.server.to(`chat_negotiation_${sessionId}`).emit('ai_typing', { isTyping });
  }
}
```

## **🚀 Implementation Timeline**

### **Week 1: MVP (Core Functionality)**
- **Day 1**: Database schema and models
- **Day 2-3**: Core service implementation
- **Day 4-5**: API controllers and basic testing
- **Day 6-7**: Integration with existing services

### **Week 2: Enhanced Features**
- **Day 1-3**: Advanced NLP service
- **Day 4-5**: AI response generation
- **Day 6-7**: Performance optimization and testing

### **Week 3: Real-time & Polish**
- **Day 1-2**: WebSocket integration
- **Day 3-4**: Advanced analytics and metrics
- **Day 5-7**: Testing, documentation, deployment

## **📊 Success Criteria**

- ✅ **Response Time**: AI responses < 2 seconds
- ✅ **Accuracy**: Data extraction > 85% accuracy
- ✅ **Integration**: Seamless with existing negotiation simulator
- ✅ **Real-time**: WebSocket updates working
- ✅ **Frontend Ready**: All endpoints match frontend expectations

## **🎯 Next Steps**

1. **Approve Implementation Plan** - Review and approve this approach
2. **Create Feature Branch** - `git checkout -b feature/chat-negotiation`
3. **Start Phase 1** - Begin with database schema and core service
4. **Frontend Testing** - Coordinate with frontend team for integration testing
5. **Iterative Development** - Weekly demos and feedback cycles

**The frontend team is ready and waiting for this implementation! Let's make it happen! 🚀**

# Backend Implementation Request: Chat Negotiation System

## 🎯 Overview

We need backend implementation for the **Interactive Chat Negotiation** feature. The frontend is complete and ready for integration. This document specifies exactly what the backend team needs to implement.

## 📋 Current Status

### ✅ Frontend Complete
- Interactive chat interface with AI characters
- Natural language processing (client-side)
- Relationship tracking (trust, respect, pressure)
- Multiple negotiation scenarios
- Graceful fallback to demo mode

### ✅ Existing Backend Services (Already Available)
- Negotiation Simulator Service (`/api/negotiation-simulator/*`)
- Chat Service (`/api/chat/*`)
- Gamification Service (`/api/gamification/*`)
- WebSocket Service for real-time updates

### ❌ Missing Backend Components
- Chat Negotiation Bridge Service
- AI Response Generation
- Advanced NLP for data extraction
- Real-time relationship tracking

## 🛠 Required Implementation

### 1. Database Schema

#### ChatNegotiationSession Table
```sql
CREATE TABLE chat_negotiation_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    negotiation_session_id UUID NOT NULL REFERENCES negotiation_sessions(id),
    chat_session_id UUID NOT NULL REFERENCES chat_sessions(id),
    scenario_id VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- active, paused, completed, abandoned
    current_round INTEGER NOT NULL DEFAULT 1,
    extracted_terms JSONB DEFAULT '{}',
    relationship_metrics JSONB NOT NULL DEFAULT '{"trust": 50, "respect": 50, "pressure": 20}',
    score DECIMAL(3,1) NOT NULL DEFAULT 5.0,
    ai_personality JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_chat_negotiation_sessions_user_id ON chat_negotiation_sessions(user_id);
CREATE INDEX idx_chat_negotiation_sessions_status ON chat_negotiation_sessions(status);
```

#### ChatMoveData Table (Optional - for analytics)
```sql
CREATE TABLE chat_move_data (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    chat_negotiation_session_id UUID NOT NULL REFERENCES chat_negotiation_sessions(id),
    message_content TEXT NOT NULL,
    extracted_data JSONB DEFAULT '{}',
    relationship_impact JSONB DEFAULT '{}',
    score_impact DECIMAL(3,1) DEFAULT 0,
    processing_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2. API Endpoints Implementation

#### A. Session Management

```typescript
// POST /api/chat-negotiation/sessions
export async function createChatNegotiationSession(req: Request, res: Response) {
  const { scenarioId, aiPersonality } = req.body;
  const userId = req.user.id;

  try {
    // 1. Create negotiation session using existing service
    const negotiationSession = await negotiationSimulatorService.startSession({
      scenarioId,
      aiPersonality,
      userId
    });

    // 2. Create chat session using existing service
    const chatSession = await chatService.createSession({
      userId,
      title: `Negotiation: ${scenarioId}`,
      type: 'negotiation'
    });

    // 3. Create chat negotiation bridge
    const chatNegotiationSession = await db.chatNegotiationSessions.create({
      userId,
      negotiationSessionId: negotiationSession.id,
      chatSessionId: chatSession.id,
      scenarioId,
      aiPersonality: aiPersonality || getDefaultPersonality(scenarioId),
      relationshipMetrics: { trust: 50, respect: 50, pressure: 20 },
      score: 5.0
    });

    res.json(chatNegotiationSession);
  } catch (error) {
    res.status(500).json({ error: 'Failed to create chat negotiation session' });
  }
}

// GET /api/chat-negotiation/sessions/:id
export async function getChatNegotiationSession(req: Request, res: Response) {
  const { id } = req.params;
  const userId = req.user.id;

  try {
    const session = await db.chatNegotiationSessions.findOne({
      where: { id, userId }
    });

    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    res.json(session);
  } catch (error) {
    res.status(500).json({ error: 'Failed to get session' });
  }
}

// GET /api/chat-negotiation/sessions
export async function getUserChatNegotiationSessions(req: Request, res: Response) {
  const userId = req.user.id;
  const { status, limit = 20, offset = 0 } = req.query;

  try {
    const where = { userId };
    if (status) where.status = status;

    const sessions = await db.chatNegotiationSessions.findMany({
      where,
      limit: parseInt(limit),
      offset: parseInt(offset),
      orderBy: { createdAt: 'desc' }
    });

    res.json(sessions);
  } catch (error) {
    res.status(500).json({ error: 'Failed to get sessions' });
  }
}
```

#### B. Chat Move Processing

```typescript
// POST /api/chat-negotiation/sessions/:id/moves
export async function sendChatMove(req: Request, res: Response) {
  const { id } = req.params;
  const { content, extractedData } = req.body;
  const userId = req.user.id;

  try {
    // 1. Get chat negotiation session
    const session = await getChatNegotiationSessionById(id, userId);
    if (!session) {
      return res.status(404).json({ error: 'Session not found' });
    }

    // 2. Extract/enhance data from message
    const enhancedData = await enhanceExtractedData(content, extractedData, session);

    // 3. Send user message to chat service
    const userMessage = await chatService.sendMessage({
      sessionId: session.chatSessionId,
      content,
      userId,
      metadata: { extractedData: enhancedData }
    });

    // 4. Convert to negotiation move
    const negotiationMove = {
      offer: enhancedData.offer || {},
      message: content,
      strategy: enhancedData.strategy || 'collaborative',
      reasoning: `Chat move: ${enhancedData.strategy || 'collaborative'} approach`
    };

    // 5. Process through negotiation simulator
    const updatedNegotiationSession = await negotiationSimulatorService.makeMove(
      session.negotiationSessionId,
      negotiationMove,
      userId
    );

    // 6. Generate AI response
    const aiResponseData = await generateAIResponse(session, enhancedData, updatedNegotiationSession);

    // 7. Send AI message to chat service
    const aiMessage = await chatService.sendMessage({
      sessionId: session.chatSessionId,
      content: aiResponseData.content,
      userId: 'ai',
      metadata: { 
        suggestions: aiResponseData.suggestions,
        extractedData: aiResponseData.extractedData 
      }
    });

    // 8. Update chat negotiation session
    const relationshipUpdate = calculateRelationshipUpdate(
      session.relationshipMetrics,
      enhancedData,
      aiResponseData
    );

    const updatedSession = await updateChatNegotiationSession(id, {
      currentRound: session.currentRound + 1,
      extractedTerms: { ...session.extractedTerms, ...enhancedData.offer },
      relationshipMetrics: relationshipUpdate,
      score: calculateNewScore(session.score, enhancedData, relationshipUpdate),
      updatedAt: new Date()
    });

    // 9. Emit WebSocket events for real-time updates
    await emitSessionUpdate(userId, updatedSession);

    res.json({
      userMessage,
      aiResponse: aiMessage,
      sessionUpdate: updatedSession
    });

  } catch (error) {
    console.error('Chat move processing failed:', error);
    res.status(500).json({ error: 'Failed to process chat move' });
  }
}
```

#### C. AI Response Generation

```typescript
// POST /api/chat-negotiation/sessions/:id/ai-response
export async function generateAIResponse(session, userMoveData, negotiationContext) {
  try {
    // 1. Analyze user move
    const moveAnalysis = await analyzeUserMove(userMoveData, session);
    
    // 2. Get scenario-specific context
    const scenarioContext = getScenarioContext(session.scenarioId);
    
    // 3. Generate contextual response
    const response = await generateContextualResponse({
      userMove: userMoveData,
      aiPersonality: session.aiPersonality,
      relationshipMetrics: session.relationshipMetrics,
      scenarioContext,
      negotiationHistory: negotiationContext.moves || [],
      currentRound: session.currentRound
    });

    // 4. Generate suggestions
    const suggestions = await generateSuggestions({
      scenarioId: session.scenarioId,
      currentContext: response.context,
      userStrategy: userMoveData.strategy,
      relationshipState: session.relationshipMetrics
    });

    return {
      content: response.content,
      extractedData: response.extractedData,
      suggestions,
      relationshipUpdate: response.relationshipImpact,
      scoreUpdate: response.scoreImpact
    };

  } catch (error) {
    console.error('AI response generation failed:', error);
    // Fallback response
    return {
      content: "That's an interesting point. Let me think about how we can make this work for both of us.",
      suggestions: [
        "What's most important to you in this deal?",
        "Are there other terms we should discuss?",
        "Can we find a middle ground?"
      ],
      relationshipUpdate: session.relationshipMetrics,
      scoreUpdate: session.score
    };
  }
}

// Helper function for contextual response generation
async function generateContextualResponse(context) {
  const { userMove, aiPersonality, relationshipMetrics, scenarioContext, currentRound } = context;
  
  // Scenario-specific response templates
  const responseTemplates = getResponseTemplates(scenarioContext.type);
  
  // Personality-based modifications
  let baseResponse = selectBaseResponse(responseTemplates, userMove, aiPersonality);
  
  // Relationship-based adjustments
  baseResponse = adjustForRelationship(baseResponse, relationshipMetrics);
  
  // Add scenario-specific details
  if (userMove.extractedData?.offer?.price) {
    baseResponse = addPriceResponse(baseResponse, userMove.extractedData.offer, scenarioContext);
  }
  
  return {
    content: baseResponse,
    extractedData: { strategy: 'collaborative', sentiment: 'positive' },
    relationshipImpact: calculateRelationshipImpact(userMove, aiPersonality),
    scoreImpact: calculateScoreImpact(userMove, relationshipMetrics)
  };
}
```

#### D. Data Extraction Service

```typescript
// POST /api/chat-negotiation/extract-data
export async function extractDataFromMessage(req: Request, res: Response) {
  const { message, context } = req.body;

  try {
    // 1. Basic entity extraction
    const entities = await extractEntities(message);
    
    // 2. Price/currency detection
    const financialData = extractFinancialTerms(message);
    
    // 3. Strategy detection
    const strategy = detectStrategy(message, context);
    
    // 4. Sentiment analysis
    const sentiment = analyzeSentiment(message);
    
    // 5. Term extraction
    const terms = extractNegotiationTerms(message, context?.scenarioType);

    const extractedData = {
      offer: {
        ...financialData,
        terms
      },
      strategy,
      sentiment,
      confidence: calculateConfidence(entities, financialData, strategy),
      extractedEntities: entities
    };

    res.json(extractedData);
  } catch (error) {
    res.status(500).json({ error: 'Failed to extract data' });
  }
}

// Helper functions for data extraction
function extractFinancialTerms(message) {
  const pricePatterns = [
    /\$?([\d,]+(?:\.\d{2})?)\s*(?:k|thousand)/i,
    /\$?([\d,]+(?:\.\d{2})?)\s*(?:m|million)/i,
    /\$?([\d,]+(?:\.\d{2})?)/
  ];
  
  // Implementation details...
  return { price: extractedPrice, currency: 'USD' };
}

function detectStrategy(message, context) {
  const strategies = {
    collaborative: ['work together', 'mutual', 'partnership', 'win-win'],
    competitive: ['need', 'must have', 'require', 'demand', 'final offer'],
    accommodating: ['flexible', 'open to', 'willing', 'consider'],
    analytical: ['data', 'research', 'market rate', 'industry standard']
  };
  
  // Implementation details...
  return detectedStrategy;
}
```

### 3. WebSocket Integration

```typescript
// Add to existing WebSocket service
export function setupChatNegotiationWebSocket(io) {
  io.on('connection', (socket) => {
    
    socket.on('join_chat_negotiation', ({ sessionId, userId }) => {
      socket.join(`chat_negotiation_${sessionId}`);
      console.log(`User ${userId} joined chat negotiation ${sessionId}`);
    });

    socket.on('leave_chat_negotiation', ({ sessionId }) => {
      socket.leave(`chat_negotiation_${sessionId}`);
    });
  });
}

// Emit session updates
export async function emitSessionUpdate(userId, sessionUpdate) {
  io.to(`chat_negotiation_${sessionUpdate.id}`).emit('session_updated', {
    sessionId: sessionUpdate.id,
    relationshipMetrics: sessionUpdate.relationshipMetrics,
    score: sessionUpdate.score,
    currentRound: sessionUpdate.currentRound
  });
}

// Emit AI typing indicator
export async function emitAITyping(sessionId, isTyping) {
  io.to(`chat_negotiation_${sessionId}`).emit('ai_typing', { isTyping });
}
```

### 4. Configuration & Environment

```env
# Add to .env
CHAT_NEGOTIATION_AI_ENDPOINT=https://api.openai.com/v1/chat/completions
CHAT_NEGOTIATION_AI_MODEL=gpt-4
CHAT_NEGOTIATION_MAX_RESPONSE_TIME=5000
CHAT_NEGOTIATION_FALLBACK_ENABLED=true
```

## 🎯 Implementation Priority

### Phase 1 (MVP - 1 week)
1. ✅ Database schema creation
2. ✅ Basic session management endpoints
3. ✅ Simple chat move processing
4. ✅ Basic AI response generation (template-based)
5. ✅ Integration with existing services

### Phase 2 (Enhanced - 2 weeks)
1. ✅ Advanced NLP for data extraction
2. ✅ Sophisticated AI response generation
3. ✅ Real-time WebSocket updates
4. ✅ Relationship tracking algorithms
5. ✅ Performance optimization

### Phase 3 (Advanced - 3 weeks)
1. ✅ Machine learning integration
2. ✅ Multi-language support
3. ✅ Advanced analytics
4. ✅ A/B testing framework

## 🧪 Testing Requirements

### Unit Tests
- Session management functions
- Data extraction accuracy
- AI response generation
- Relationship calculation algorithms

### Integration Tests
- End-to-end chat negotiation flow
- WebSocket real-time updates
- Fallback mechanisms
- Performance under load

### API Testing
```bash
# Example test cases
curl -X POST http://localhost:4000/api/chat-negotiation/sessions \
  -H "Authorization: Bearer {token}" \
  -d '{"scenarioId": "software_licensing"}'

curl -X POST http://localhost:4000/api/chat-negotiation/sessions/{id}/moves \
  -H "Authorization: Bearer {token}" \
  -d '{"content": "I am thinking around $50k for the license"}'
```

## 📊 Success Metrics

- **Response Time**: AI responses < 2 seconds
- **Accuracy**: Data extraction > 85% accuracy
- **Uptime**: 99.9% availability
- **Concurrency**: Support 100+ simultaneous negotiations
- **User Satisfaction**: > 4.5/5 rating

## 🚀 Deployment Checklist

- [ ] Database migrations applied
- [ ] Environment variables configured
- [ ] API endpoints implemented and tested
- [ ] WebSocket integration working
- [ ] Monitoring and logging setup
- [ ] Error handling and fallbacks tested
- [ ] Performance benchmarks met
- [ ] Security review completed

## 📞 Support & Questions

For implementation questions or clarifications:
- **Frontend Integration**: Check `src/lib/services/chat-negotiation-service.ts`
- **API Contracts**: See `docs/backend-requirements-chat-negotiation.md`
- **Demo Mode**: Test at `/demo/chat-negotiation` with "Backend: OFF"
- **Live Testing**: Toggle "Backend: ON" once endpoints are ready

The frontend is **ready and waiting** for these backend implementations! 🚀

---

## 📋 Quick Implementation Checklist

### Immediate Actions (Day 1)
- [ ] Create database tables using provided schema
- [ ] Implement basic session management endpoints
- [ ] Set up routing for `/api/chat-negotiation/*`
- [ ] Test with frontend toggle "Backend: ON"

### Week 1 Goals
- [ ] All session management endpoints working
- [ ] Basic chat move processing functional
- [ ] Simple AI response generation (template-based)
- [ ] Integration with existing negotiation simulator

### Week 2 Goals
- [ ] Advanced data extraction from natural language
- [ ] Sophisticated AI response generation
- [ ] Real-time WebSocket updates
- [ ] Relationship tracking algorithms

### Ready for Production
- [ ] All endpoints implemented and tested
- [ ] Performance benchmarks met (< 2s response time)
- [ ] Error handling and fallbacks working
- [ ] Frontend integration fully functional

**The frontend team is standing by to test integration as soon as endpoints are available!** 🎯

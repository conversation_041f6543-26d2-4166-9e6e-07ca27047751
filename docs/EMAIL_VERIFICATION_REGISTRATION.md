# Email Verification Registration Flow

This document outlines the implementation of email verification in the Legal Document Analyzer application.

## Overview

Email verification ensures that users register with valid email addresses that they own, which:

- Reduces spam accounts
- Improves security
- Ensures reliable communication with users
- Complies with data protection regulations

## Implementation Details

### 1. Email Service

The `EmailService` handles sending verification emails using nodemailer:

```typescript
@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private transporter: nodemailer.Transporter;

  constructor(private readonly configService: ConfigService) {
    // Initialize the email transporter
    this.initializeTransporter();
  }

  private initializeTransporter() {
    const host = this.configService.get<string>('EMAIL_HOST');
    const port = this.configService.get<number>('EMAIL_PORT');
    const user = this.configService.get<string>('EMAIL_USER');
    const pass = this.configService.get<string>('EMAIL_PASSWORD');
    const secure = this.configService.get<boolean>('EMAIL_SECURE', true);

    // For development, if no email config is provided, use ethereal.email (fake SMTP service)
    if (!host || !port || !user || !pass) {
      this.logger.warn('Email configuration not found, using ethereal.email for testing');
      this.createTestAccount();
      return;
    }

    this.transporter = nodemailer.createTransport({
      host,
      port,
      secure,
      auth: {
        user,
        pass,
      },
    });
  }

  async sendVerificationEmail(email: string, token: string): Promise<boolean> {
    const frontendUrl = this.configService.get<string>('FRONTEND_URL', 'http://localhost:3000');
    const verificationUrl = `${frontendUrl}/verify-email?token=${token}`;
    
    // Email content with professional HTML template
    const subject = 'Verify your Legal Document Analyzer account';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <h1 style="color: #2c3e50; text-align: center;">Welcome to Legal Document Analyzer</h1>
        <p style="font-size: 16px; line-height: 1.5; color: #333;">Thank you for registering. Please verify your email address to activate your account.</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${verificationUrl}" style="background-color: #3498db; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Verify Email</a>
        </div>
        <p style="font-size: 14px; color: #7f8c8d;">This link will expire in 24 hours.</p>
      </div>
    `;
    
    // Send the email and return success status
    try {
      const info = await this.transporter.sendMail({
        from: `"Legal Document Analyzer" <${this.configService.get<string>('EMAIL_FROM', '<EMAIL>')}>`,
        to: email,
        subject,
        html,
      });
      
      this.logger.log(`Verification email sent to ${email}: ${info.messageId}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to send verification email to ${email}`, error);
      return false;
    }
  }
}
```

### 2. Verification Token Schema and Service

The `VerificationToken` schema and service manage secure token generation and validation:

```typescript
@Schema({ timestamps: true })
export class VerificationToken {
  @Prop({ required: true })
  id: string;

  @Prop({ required: true, index: true })
  userId: string;

  @Prop({ required: true })
  token: string;

  @Prop({ required: true })
  type: string; // 'email', 'password-reset', etc.

  @Prop({ required: true, expires: '24h' })
  expiresAt: Date;
}

@Injectable()
export class VerificationTokenService {
  constructor(
    @InjectModel(VerificationToken.name) private readonly tokenModel: Model<VerificationToken>,
  ) {}

  async createToken(userId: string, type: string, expiresInHours = 24): Promise<string> {
    // Generate a secure random token
    const token = crypto.randomBytes(32).toString('hex');
    
    // Calculate expiration date
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + expiresInHours);
    
    // Delete any existing tokens of the same type for this user
    await this.tokenModel.deleteMany({ userId, type });
    
    // Create a new token
    await this.tokenModel.create({
      id: uuidv4(),
      userId,
      token,
      type,
      expiresAt,
    });
    
    return token;
  }

  async validateToken(token: string, type: string): Promise<string | null> {
    // Find the token
    const verificationToken = await this.tokenModel.findOne({
      token,
      type,
      expiresAt: { $gt: new Date() }, // Not expired
    });
    
    if (!verificationToken) {
      return null;
    }
    
    // Delete the token (one-time use)
    await this.tokenModel.deleteOne({ id: verificationToken.id });
    
    return verificationToken.userId;
  }
}
```

### 3. Auth Service Integration

The `AuthService` was enhanced to support email verification:

```typescript
@Injectable()
export class AuthService {
  constructor(
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
    private readonly organizationService: OrganizationService,
    private readonly emailService: EmailService,
    private readonly verificationTokenService: VerificationTokenService,
  ) {}

  public async register(createUserDto: CreateUserDto): Promise<AuthResponseDto> {
    const organizationId = await this.handleOrganization(createUserDto);
    const user = await this.userService.create({
      ...createUserDto,
      organizationId,
    });

    // Generate verification token and send email
    const token = await this.verificationTokenService.createToken(user.id, 'email');
    await this.emailService.sendVerificationEmail(user.email, token);

    const payload = this.createJwtPayload(user);
    const jwtToken = this.jwtService.sign(payload);
    return this.createAuthResponse(user, jwtToken);
  }

  public async verifyEmail(token: string): Promise<boolean> {
    const userId = await this.verificationTokenService.validateToken(token, 'email');
    
    if (!userId) {
      return false;
    }
    
    // Find the user without organization context
    const user = await this.userService.findByIdOnly(userId);
    
    if (!user) {
      return false;
    }
    
    // Update user's email verification status
    await this.userService.verifyEmail(userId, user.organizationId);
    
    return true;
  }

  public async resendVerificationEmail(email: string): Promise<boolean> {
    const user = await this.userService.findByEmailOnly(email);
    
    if (!user) {
      throw new NotFoundException('User not found');
    }
    
    if (user.emailVerified) {
      throw new BadRequestException('Email is already verified');
    }
    
    // Generate new verification token
    const token = await this.verificationTokenService.createToken(user.id, 'email');
    
    // Send verification email
    return this.emailService.sendVerificationEmail(user.email, token);
  }
}
```

### 4. API Endpoints

Added new endpoints to the `AuthController`:

```typescript
@Public()
@Get('verify-email')
@ApiOperation({ summary: 'Verify email address' })
@ApiResponse({ status: 200, description: 'Email verified successfully' })
@ApiResponse({ status: 400, description: 'Invalid or expired token' })
async verifyEmail(@Query('token') token: string): Promise<{ success: boolean; message: string }> {
  if (!token) {
    throw new BadRequestException('Verification token is required');
  }
  
  const success = await this.authService.verifyEmail(token);
  
  if (!success) {
    throw new BadRequestException('Invalid or expired verification token');
  }
  
  return { 
    success: true,
    message: 'Email verified successfully'
  };
}

@Public()
@Post('resend-verification')
@ApiOperation({ summary: 'Resend verification email' })
@ApiResponse({ status: 200, description: 'Verification email sent' })
@ApiResponse({ status: 404, description: 'User not found' })
@ApiResponse({ status: 400, description: 'Email already verified' })
async resendVerification(@Body() body: { email: string }): Promise<{ success: boolean; message: string }> {
  if (!body.email) {
    throw new BadRequestException('Email is required');
  }
  
  try {
    const success = await this.authService.resendVerificationEmail(body.email);
    
    return { 
      success: true,
      message: 'Verification email sent successfully'
    };
  } catch (error) {
    if (error instanceof NotFoundException) {
      throw error;
    }
    if (error instanceof BadRequestException) {
      throw error;
    }
    throw new BadRequestException('Failed to send verification email');
  }
}
```

### 5. Module Registration

Updated the `AuthModule` to register the new components:

```typescript
@Module({
  imports: [
    // ... existing imports
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: Organization.name, schema: OrganizationSchema },
      { name: VerificationToken.name, schema: VerificationTokenSchema },
    ]),
  ],
  providers: [
    // ... existing providers
    EmailService,
    VerificationTokenService,
  ],
  exports: [
    // ... existing exports
    EmailService,
    VerificationTokenService,
  ],
})
export class AuthModule {}
```

## Security Considerations

- **Token Security**: Using crypto.randomBytes for secure token generation
- **Token Expiration**: Tokens expire after 24 hours
- **One-Time Use**: Tokens are deleted after validation
- **Rate Limiting**: Error handling prevents abuse of the resend endpoint
- **Tenant Isolation**: Email verification respects the multi-tenant architecture

## Frontend Integration

The frontend application should implement:

1. A page to handle the verification link (/verify-email)
2. A form to request a new verification email
3. UI elements to show verification status
4. Appropriate success/error messages

## Configuration

The email service requires the following environment variables:

```
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=password
EMAIL_SECURE=true
EMAIL_FROM=<EMAIL>
FRONTEND_URL=http://localhost:3000
```

For development, if these are not provided, the service will automatically use ethereal.email for testing.

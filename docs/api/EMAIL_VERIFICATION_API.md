# Email Verification API

This document outlines the API endpoints for email verification in the Legal Document Analyzer.

## Endpoints

### 1. Verify Email

Verifies a user's email address using a token sent to their email.

**URL**: `/auth/verify-email`

**Method**: `GET`

**Auth Required**: No

**Query Parameters**:

| Parameter | Type   | Required | Description                                  |
|-----------|--------|----------|----------------------------------------------|
| token     | string | Yes      | The verification token sent to user's email  |

**Success Response**:

- **Code**: 200 OK
- **Content**:

```json
{
  "success": true,
  "message": "<PERSON>ail verified successfully"
}
```

**Error Responses**:

- **Code**: 400 Bad Request
- **Content**:

```json
{
  "statusCode": 400,
  "message": "Verification token is required",
  "error": "Bad Request"
}
```

OR

- **Code**: 400 Bad Request
- **Content**:

```json
{
  "statusCode": 400,
  "message": "Invalid or expired verification token",
  "error": "Bad Request"
}
```

### 2. Resend Verification Email

Resends the verification email to a user.

**URL**: `/auth/resend-verification`

**Method**: `POST`

**Auth Required**: No

**Request Body**:

```json
{
  "email": "<EMAIL>"
}
```

**Success Response**:

- **Code**: 200 OK
- **Content**:

```json
{
  "success": true,
  "message": "Verification email sent successfully"
}
```

**Error Responses**:

- **Code**: 400 Bad Request
- **Content**:

```json
{
  "statusCode": 400,
  "message": "Email is required",
  "error": "Bad Request"
}
```

OR

- **Code**: 400 Bad Request
- **Content**:

```json
{
  "statusCode": 400,
  "message": "Email already verified",
  "error": "Bad Request"
}
```

OR

- **Code**: 404 Not Found
- **Content**:

```json
{
  "statusCode": 404,
  "message": "User not found",
  "error": "Not Found"
}
```

## Data Transfer Objects (DTOs)

### ResendVerificationDto

```typescript
import { IsEmail, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ResendVerificationDto {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>'
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;
}
```

### VerificationResponseDto

```typescript
import { ApiProperty } from '@nestjs/swagger';

export class VerificationResponseDto {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true
  })
  success: boolean;

  @ApiProperty({
    description: 'Message describing the result',
    example: 'Email verified successfully'
  })
  message: string;
}
```

## Implementation Example

### Controller

```typescript
@Public()
@Get('verify-email')
@ApiOperation({ summary: 'Verify email address' })
@ApiResponse({
  status: 200,
  description: 'Email verified successfully',
  type: VerificationResponseDto
})
@ApiResponse({
  status: 400,
  description: 'Invalid or expired token'
})
async verifyEmail(
  @Query('token') token: string
): Promise<VerificationResponseDto> {
  if (!token) {
    throw new BadRequestException('Verification token is required');
  }
  
  const success = await this.authService.verifyEmail(token);
  
  if (!success) {
    throw new BadRequestException('Invalid or expired verification token');
  }
  
  return {
    success: true,
    message: 'Email verified successfully'
  };
}

@Public()
@Post('resend-verification')
@ApiOperation({ summary: 'Resend verification email' })
@ApiResponse({
  status: 200,
  description: 'Verification email sent',
  type: VerificationResponseDto
})
@ApiResponse({
  status: 404,
  description: 'User not found'
})
@ApiResponse({
  status: 400,
  description: 'Email already verified'
})
async resendVerification(
  @Body() resendVerificationDto: ResendVerificationDto
): Promise<VerificationResponseDto> {
  if (!resendVerificationDto.email) {
    throw new BadRequestException('Email is required');
  }
  
  try {
    const success = await this.authService.resendVerificationEmail(
      resendVerificationDto.email
    );
    
    return {
      success: true,
      message: 'Verification email sent successfully'
    };
  } catch (error) {
    if (error instanceof NotFoundException) {
      throw error;
    }
    if (error instanceof BadRequestException) {
      throw error;
    }
    throw new BadRequestException('Failed to send verification email');
  }
}
```

## Frontend Integration

### Verification Page

The frontend should implement a page to handle the verification link with a URL like:

```
/verify-email?token=abc123
```

This page should:

1. Extract the token from the URL query parameters
2. Call the verification API endpoint
3. Display a success message or error based on the response
4. Provide a link to the login page after successful verification

### Resend Verification Form

The frontend should also implement a form to request a new verification email:

```html
<form @submit="resendVerification">
  <input type="email" v-model="email" placeholder="Enter your email" required>
  <button type="submit">Resend Verification Email</button>
</form>
```

With corresponding JavaScript:

```javascript
async function resendVerification() {
  try {
    const response = await fetch('/api/auth/resend-verification', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email: this.email })
    });
    
    const data = await response.json();
    
    if (response.ok) {
      // Show success message
      this.message = data.message;
    } else {
      // Show error message
      this.error = data.message || 'Failed to resend verification email';
    }
  } catch (error) {
    this.error = 'An error occurred. Please try again.';
  }
}
```

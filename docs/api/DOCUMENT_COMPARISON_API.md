# Document Comparison API Documentation

## Overview

The Document Comparison API provides enhanced capabilities for comparing legal documents, including visual diff highlighting, version comparison, and export functionality. This API is part of the Legal Document Analyzer's core analysis capabilities.

## Authentication

All endpoints require JWT authentication. Include the JW<PERSON> token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

Organization context is required for all requests:

```
X-Organization-Id: <organization-id>
```

## Feature Availability

The enhanced document comparison features are subject to subscription-based access control. The following features are available based on subscription tier:

- **Basic Tier**: Simple text comparison
- **Professional Tier**: Enhanced comparison with visual diff highlighting
- **Enterprise Tier**: All features including version comparison, section analysis, and export functionality

## Endpoints

### Compare Documents

Compare two documents with enhanced analysis capabilities.

**Endpoint:** `POST /api/enhanced-comparison/documents`

**Request Body:**

```json
{
  "documentA": "Content of the first document",
  "documentB": "Content of the second document",
  "type": "both", // "similarities", "differences", or "both"
  "includeVisualDiff": true,
  "includeSectionReferences": true,
  "documentAMetadata": {
    "id": "doc-a-id",
    "title": "Document A Title",
    "createdAt": "2025-04-01T12:00:00Z",
    "organizationId": "org-id",
    "userId": "user-id"
  },
  "documentBMetadata": {
    "id": "doc-b-id",
    "title": "Document B Title",
    "createdAt": "2025-04-15T12:00:00Z",
    "organizationId": "org-id",
    "userId": "user-id"
  }
}
```

**Response:**

```json
{
  "status": "success",
  "data": {
    "id": "comparison-id",
    "diffs": [
      {
        "type": "equal",
        "text": "Text that is the same in both documents"
      },
      {
        "type": "delete",
        "text": "Text that was removed"
      },
      {
        "type": "insert",
        "text": "Text that was added"
      }
    ],
    "metadata": {
      "timestamp": "2025-04-19T12:00:00Z",
      "documentStats": {
        "documentA": {
          "length": 1000
        },
        "documentB": {
          "length": 1200
        }
      },
      "summary": {
        "addedLines": 10,
        "removedLines": 5,
        "modifiedLines": 15,
        "totalChanges": 30
      }
    },
    "visualization": {
      "htmlDiff": "<div class='diff'>...</div>"
    },
    "sectionDiffs": [
      {
        "sectionA": {
          "id": "section-1",
          "title": "Introduction",
          "content": "Section content",
          "lineRange": {
            "start": 1,
            "end": 10
          }
        },
        "sectionB": {
          "id": "section-1",
          "title": "Introduction",
          "content": "Modified section content",
          "lineRange": {
            "start": 1,
            "end": 12
          }
        },
        "differences": [
          {
            "original": "Original text",
            "modified": "Modified text",
            "diffType": "modification",
            "lineNumber": 5
          }
        ]
      }
    ]
  },
  "metadata": {
    "organizationId": "org-id",
    "timestamp": "2025-04-19T12:00:00Z"
  }
}
```

### Compare Document Versions

Compare different versions of the same document.

**Endpoint:** `POST /api/enhanced-comparison/versions`

**Request Body:**

```json
{
  "documentId": "document-id",
  "versionA": 1,
  "versionB": 2,
  "options": {
    "includeVisualDiff": true,
    "includeSectionAnalysis": true
  }
}
```

**Response:**

```json
{
  "status": "success",
  "data": {
    "id": "version-comparison-id",
    "diffs": [
      {
        "type": "equal",
        "text": "Text that is the same in both versions"
      },
      {
        "type": "delete",
        "text": "Text that was removed"
      },
      {
        "type": "insert",
        "text": "Text that was added"
      }
    ],
    "metadata": {
      "timestamp": "2025-04-19T12:00:00Z",
      "documentStats": {
        "documentA": {
          "length": 1000
        },
        "documentB": {
          "length": 1200
        }
      },
      "summary": {
        "addedLines": 10,
        "removedLines": 5,
        "modifiedLines": 15,
        "totalChanges": 30
      }
    },
    "versionInfo": {
      "documentId": "document-id",
      "beforeVersion": 1,
      "afterVersion": 2,
      "versionDelta": 1,
      "beforeTimestamp": "2025-04-01T12:00:00Z",
      "afterTimestamp": "2025-04-15T12:00:00Z"
    }
  },
  "metadata": {
    "organizationId": "org-id",
    "timestamp": "2025-04-19T12:00:00Z"
  }
}
```

### Export Comparison

Export a comparison result in various formats (PDF, DOCX, HTML).

**Endpoint:** `POST /api/enhanced-comparison/export/:comparisonId`

**URL Parameters:**

- `comparisonId`: ID of the comparison to export

**Request Body:**

```json
{
  "format": "pdf", // "pdf", "docx", or "html"
  "includeMetadata": true,
  "highlightChanges": true,
  "includeSummary": true,
  "sections": {
    "include": ["section1", "section2"],
    "highlightIntensity": true
  },
  "customization": {
    "colors": {
      "addition": "#00FF00",
      "deletion": "#FF0000",
      "modification": "#0000FF"
    },
    "fonts": {
      "family": "Arial",
      "size": 12
    }
  }
}
```

**Response:**

For PDF and DOCX formats, the response will be a binary file with the appropriate Content-Type header.

For HTML format, the response will be an HTML string.

### Generate Executive Summary

Generate an executive summary of the comparison results.

**Endpoint:** `GET /api/enhanced-comparison/summary/:comparisonId`

**URL Parameters:**

- `comparisonId`: ID of the comparison to summarize

**Response:**

```json
{
  "summary": "Executive Summary:\n\nThe document has undergone moderate revisions with several changes, including some additions and some deletions.\n\nSignificant modifications include:\n1. Updated terminology in several sections\n2. Clarified legal obligations and responsibilities\n3. Added new provisions for compliance with recent regulations\n\nThese changes appear to strengthen the document's legal protections while improving clarity for all parties involved. The modifications suggest an effort to address potential loopholes or ambiguities in the previous version."
}
```

## Error Handling

The API uses standard HTTP status codes to indicate the success or failure of a request:

- `200 OK`: The request was successful
- `201 Created`: The resource was successfully created
- `400 Bad Request`: The request was malformed or invalid
- `401 Unauthorized`: Authentication is required or failed
- `403 Forbidden`: The authenticated user does not have permission to access the resource
- `404 Not Found`: The requested resource was not found
- `500 Internal Server Error`: An error occurred on the server

Error responses include a detailed message and additional information when available:

```json
{
  "statusCode": 400,
  "message": "Invalid input data",
  "error": "Bad Request",
  "details": "Document content is required",
  "path": "/api/enhanced-comparison/documents",
  "timestamp": "2025-04-19T12:00:00Z"
}
```

## Rate Limiting

API requests are subject to rate limiting based on subscription tier:

- **Basic Tier**: 100 requests per day
- **Professional Tier**: 1,000 requests per day
- **Enterprise Tier**: 10,000 requests per day

Rate limit headers are included in all responses:

```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 995
X-RateLimit-Reset: 1714521600
```

## Example Usage

### Basic Comparison

```javascript
const axios = require('axios');

const API_URL = 'http://localhost:4000/api';
const TOKEN = 'your-jwt-token';
const ORG_ID = 'your-organization-id';

async function compareDocuments() {
  try {
    const response = await axios.post(
      `${API_URL}/enhanced-comparison/documents`,
      {
        documentA: 'This is the original document.',
        documentB: 'This is the modified document with changes.',
        type: 'both',
        includeVisualDiff: true,
        includeSectionReferences: true,
      },
      {
        headers: {
          Authorization: `Bearer ${TOKEN}`,
          'Content-Type': 'application/json',
          'X-Organization-Id': ORG_ID,
        },
      },
    );

    console.log('Comparison successful!');
    console.log(`Comparison ID: ${response.data.data.id}`);
    console.log(
      `Number of differences: ${response.data.data.diffs?.length || 0}`,
    );

    return response.data.data.id;
  } catch (error) {
    console.error('Error during comparison:', error.message);
    throw error;
  }
}

async function exportComparison(comparisonId) {
  try {
    const response = await axios.post(
      `${API_URL}/enhanced-comparison/export/${comparisonId}`,
      {
        format: 'pdf',
        includeMetadata: true,
        highlightChanges: true,
        includeSummary: true,
      },
      {
        headers: {
          Authorization: `Bearer ${TOKEN}`,
          'Content-Type': 'application/json',
          'X-Organization-Id': ORG_ID,
        },
        responseType: 'arraybuffer',
      },
    );

    // Save the PDF file
    require('fs').writeFileSync('comparison_export.pdf', response.data);
    console.log('PDF export successful! Saved as comparison_export.pdf');
  } catch (error) {
    console.error('Error during export:', error.message);
    throw error;
  }
}

// Run the example
async function run() {
  const comparisonId = await compareDocuments();
  await exportComparison(comparisonId);
}

run();
```

## Implementation Notes

The Document Comparison API uses the following technologies:

- **Diff Algorithm**: Uses the `diff` library for text comparison
- **PDF Generation**: Uses `pdfkit` for PDF export
- **DOCX Generation**: Uses `docx` for DOCX export
- **Section Analysis**: Uses pattern recognition and AI-powered analysis for document section extraction
- **Executive Summary**: Uses AI-powered summarization for generating concise overviews of document changes

## Changelog

- **April 19, 2025**: Initial release of the Document Comparison Improvements feature
  - Added visual diff highlighting
  - Added version comparison capabilities
  - Added export functionality (PDF, DOCX, HTML)
  - Added executive summary generation

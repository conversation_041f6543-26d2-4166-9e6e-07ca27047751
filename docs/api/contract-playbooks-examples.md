# Contract Playbooks API - Implementation Examples

## Quick Start Guide

### 1. Basic Setup

```typescript
// api/client.ts
import axios from 'axios';

export const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:4000/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth interceptor
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

### 2. API Service Implementation

```typescript
// services/contractPlaybooksService.ts
import { apiClient } from '../api/client';
import type {
  PlaybooksResponse,
  ContractPlaybook,
  CreatePlaybookRequest,
  AnalyzeContractRequest,
  ContractAnalysis,
  AnalysesResponse,
  PlaybookAnalytics,
} from '../types/contractPlaybooks';

export class ContractPlaybooksService {
  // Playbook Management
  async getPlaybooks(params?: PlaybookSearchParams): Promise<PlaybooksResponse> {
    const response = await apiClient.get('/contract-playbooks', { params });
    return response.data;
  }

  async createPlaybook(data: CreatePlaybookRequest): Promise<ContractPlaybook> {
    const response = await apiClient.post('/contract-playbooks', data);
    return response.data;
  }

  async getPlaybook(id: string): Promise<ContractPlaybook> {
    const response = await apiClient.get(`/contract-playbooks/${id}`);
    return response.data;
  }

  async updatePlaybook(id: string, data: UpdatePlaybookRequest): Promise<ContractPlaybook> {
    const response = await apiClient.put(`/contract-playbooks/${id}`, data);
    return response.data;
  }

  async deletePlaybook(id: string): Promise<void> {
    await apiClient.delete(`/contract-playbooks/${id}`);
  }

  // Analysis
  async analyzeContract(data: AnalyzeContractRequest): Promise<ContractAnalysis> {
    const response = await apiClient.post('/contract-playbooks/analyze', data);
    return response.data;
  }

  async getAnalyses(params?: AnalysisSearchParams): Promise<AnalysesResponse> {
    const response = await apiClient.get('/contract-playbooks/analyses', { params });
    return response.data;
  }

  async getAnalysis(id: string): Promise<ContractAnalysis> {
    const response = await apiClient.get(`/contract-playbooks/analyses/${id}`);
    return response.data;
  }

  // Utilities
  async getPlaybookAnalytics(id: string): Promise<PlaybookAnalytics> {
    const response = await apiClient.get(`/contract-playbooks/${id}/analytics`);
    return response.data;
  }

  async duplicatePlaybook(id: string, name: string): Promise<ContractPlaybook> {
    const response = await apiClient.post(`/contract-playbooks/${id}/duplicate`, { name });
    return response.data;
  }
}

export const contractPlaybooksService = new ContractPlaybooksService();
```

### 3. React Hooks with React Query

```typescript
// hooks/useContractPlaybooks.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { contractPlaybooksService } from '../services/contractPlaybooksService';
import { toast } from 'react-hot-toast';

// Playbook hooks
export const usePlaybooks = (params?: PlaybookSearchParams) => {
  return useQuery({
    queryKey: ['playbooks', params],
    queryFn: () => contractPlaybooksService.getPlaybooks(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const usePlaybook = (id: string) => {
  return useQuery({
    queryKey: ['playbook', id],
    queryFn: () => contractPlaybooksService.getPlaybook(id),
    enabled: !!id,
  });
};

export const useCreatePlaybook = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: contractPlaybooksService.createPlaybook,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['playbooks'] });
      toast.success(`Playbook "${data.name}" created successfully`);
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to create playbook');
    },
  });
};

export const useUpdatePlaybook = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdatePlaybookRequest }) =>
      contractPlaybooksService.updatePlaybook(id, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['playbooks'] });
      queryClient.invalidateQueries({ queryKey: ['playbook', data.id] });
      toast.success('Playbook updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to update playbook');
    },
  });
};

export const useDeletePlaybook = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: contractPlaybooksService.deletePlaybook,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['playbooks'] });
      toast.success('Playbook deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to delete playbook');
    },
  });
};

// Analysis hooks
export const useAnalyzeContract = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: contractPlaybooksService.analyzeContract,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['analyses'] });
      toast.success('Contract analysis completed');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Analysis failed');
    },
  });
};

export const useAnalyses = (params?: AnalysisSearchParams) => {
  return useQuery({
    queryKey: ['analyses', params],
    queryFn: () => contractPlaybooksService.getAnalyses(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

export const useAnalysis = (id: string) => {
  return useQuery({
    queryKey: ['analysis', id],
    queryFn: () => contractPlaybooksService.getAnalysis(id),
    enabled: !!id,
  });
};

export const usePlaybookAnalytics = (id: string) => {
  return useQuery({
    queryKey: ['playbook-analytics', id],
    queryFn: () => contractPlaybooksService.getPlaybookAnalytics(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};
```

### 4. React Components

```typescript
// components/PlaybookList.tsx
import React, { useState } from 'react';
import { usePlaybooks, useDeletePlaybook } from '../hooks/useContractPlaybooks';
import { PlaybookCard } from './PlaybookCard';
import { SearchFilters } from './SearchFilters';
import { Pagination } from './Pagination';
import { LoadingSpinner } from './LoadingSpinner';
import { ErrorMessage } from './ErrorMessage';

export const PlaybookList: React.FC = () => {
  const [filters, setFilters] = useState<PlaybookSearchParams>({
    page: 1,
    limit: 20,
  });

  const { data, isLoading, error } = usePlaybooks(filters);
  const deletePlaybook = useDeletePlaybook();

  const handleDelete = async (id: string, name: string) => {
    if (window.confirm(`Are you sure you want to delete "${name}"?`)) {
      await deletePlaybook.mutateAsync(id);
    }
  };

  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;

  return (
    <div className="playbook-list">
      <div className="header">
        <h1>Contract Playbooks</h1>
        <button className="btn-primary">Create New Playbook</button>
      </div>

      <SearchFilters filters={filters} onChange={setFilters} />

      <div className="results-summary">
        {data?.total} playbook{data?.total !== 1 ? 's' : ''} found
      </div>

      <div className="playbook-grid">
        {data?.playbooks.map((playbook) => (
          <PlaybookCard
            key={playbook.id}
            playbook={playbook}
            onDelete={() => handleDelete(playbook.id, playbook.name)}
          />
        ))}
      </div>

      {data && (
        <Pagination
          currentPage={data.page}
          totalPages={data.totalPages}
          onPageChange={(page) => setFilters({ ...filters, page })}
        />
      )}
    </div>
  );
};
```

```typescript
// components/ContractAnalyzer.tsx
import React, { useState } from 'react';
import { useAnalyzeContract } from '../hooks/useContractPlaybooks';
import { AnalysisProgress } from './AnalysisProgress';
import { AnalysisResults } from './AnalysisResults';

interface Props {
  contractId: string;
  availablePlaybooks: ContractPlaybook[];
}

export const ContractAnalyzer: React.FC<Props> = ({ contractId, availablePlaybooks }) => {
  const [selectedPlaybook, setSelectedPlaybook] = useState<string>('');
  const [analysisOptions, setAnalysisOptions] = useState<AnalysisOptions>({
    includeRecommendations: true,
    riskThreshold: 2,
    aiAnalysis: true,
    detailedReport: true,
  });

  const analyzeContract = useAnalyzeContract();

  const handleAnalyze = async () => {
    if (!selectedPlaybook) {
      alert('Please select a playbook');
      return;
    }

    await analyzeContract.mutateAsync({
      contractId,
      playbookId: selectedPlaybook,
      options: analysisOptions,
    });
  };

  return (
    <div className="contract-analyzer">
      <h2>Analyze Contract</h2>

      <div className="form-group">
        <label>Select Playbook:</label>
        <select
          value={selectedPlaybook}
          onChange={(e) => setSelectedPlaybook(e.target.value)}
        >
          <option value="">Choose a playbook...</option>
          {availablePlaybooks.map((playbook) => (
            <option key={playbook.id} value={playbook.id}>
              {playbook.name} ({playbook.contractType})
            </option>
          ))}
        </select>
      </div>

      <div className="analysis-options">
        <h3>Analysis Options</h3>
        <label>
          <input
            type="checkbox"
            checked={analysisOptions.includeRecommendations}
            onChange={(e) =>
              setAnalysisOptions({
                ...analysisOptions,
                includeRecommendations: e.target.checked,
              })
            }
          />
          Include Recommendations
        </label>
        
        <label>
          Risk Threshold:
          <input
            type="range"
            min="1"
            max="5"
            value={analysisOptions.riskThreshold}
            onChange={(e) =>
              setAnalysisOptions({
                ...analysisOptions,
                riskThreshold: parseInt(e.target.value),
              })
            }
          />
          {analysisOptions.riskThreshold}
        </label>
      </div>

      <button
        onClick={handleAnalyze}
        disabled={!selectedPlaybook || analyzeContract.isPending}
        className="btn-primary"
      >
        {analyzeContract.isPending ? 'Analyzing...' : 'Start Analysis'}
      </button>

      {analyzeContract.isPending && <AnalysisProgress />}
      
      {analyzeContract.data && (
        <AnalysisResults analysis={analyzeContract.data} />
      )}
    </div>
  );
};
```

### 5. Error Handling

```typescript
// utils/errorHandler.ts
import { ApiError } from '../types/contractPlaybooks';

export const handleApiError = (error: any): string => {
  if (error.response?.data) {
    const apiError = error.response.data as ApiError;
    
    switch (apiError.statusCode) {
      case 400:
        return `Invalid request: ${apiError.message}`;
      case 401:
        return 'Please log in to continue';
      case 403:
        return 'This feature is not available in your current plan';
      case 404:
        return 'The requested resource was not found';
      case 409:
        return `Conflict: ${apiError.message}`;
      case 500:
        return 'Server error. Please try again later';
      default:
        return apiError.message || 'An unexpected error occurred';
    }
  }
  
  return 'Network error. Please check your connection';
};

// components/ErrorBoundary.tsx
import React from 'react';

interface Props {
  children: React.ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Contract Playbooks Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h2>Something went wrong</h2>
          <p>There was an error loading the contract playbooks feature.</p>
          <button onClick={() => this.setState({ hasError: false })}>
            Try Again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
```

### 6. Testing Examples

```typescript
// __tests__/contractPlaybooks.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { PlaybookList } from '../components/PlaybookList';
import { contractPlaybooksService } from '../services/contractPlaybooksService';

// Mock the service
jest.mock('../services/contractPlaybooksService');
const mockService = contractPlaybooksService as jest.Mocked<typeof contractPlaybooksService>;

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('PlaybookList', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders playbooks successfully', async () => {
    const mockPlaybooks = {
      playbooks: [
        {
          id: '1',
          name: 'Standard NDA Playbook',
          contractType: 'nda' as const,
          rules: [],
          isActive: true,
        },
      ],
      total: 1,
      page: 1,
      limit: 20,
      totalPages: 1,
    };

    mockService.getPlaybooks.mockResolvedValue(mockPlaybooks);

    render(<PlaybookList />, { wrapper: createWrapper() });

    expect(screen.getByText('Loading...')).toBeInTheDocument();
    
    await waitFor(() => {
      expect(screen.getByText('Standard NDA Playbook')).toBeInTheDocument();
    });
    
    expect(screen.getByText('1 playbook found')).toBeInTheDocument();
  });

  it('handles errors gracefully', async () => {
    mockService.getPlaybooks.mockRejectedValue(new Error('Network error'));

    render(<PlaybookList />, { wrapper: createWrapper() });

    await waitFor(() => {
      expect(screen.getByText(/error/i)).toBeInTheDocument();
    });
  });
});
```

This comprehensive guide provides everything the frontend team needs to implement the Contract Playbooks feature, including TypeScript types, service layer, React hooks, components, error handling, and testing examples.

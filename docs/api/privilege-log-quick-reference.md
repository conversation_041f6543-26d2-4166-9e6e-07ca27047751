# Privilege Log Automation - Quick Reference

## 🚀 Quick Start

### Prerequisites
- PRO subscription tier
- Valid JWT token
- Document uploaded to system

### Basic Workflow
1. **Analyze** → `POST /documents/:id/privilege-analysis`
2. **Review** → `PUT /documents/:id/privilege-content/:contentId/review`
3. **Redact** → `POST /documents/:id/redactions`

---

## 📋 API Endpoints Summary

| Method | Endpoint | Purpose |
|--------|----------|---------|
| `POST` | `/documents/:id/privilege-analysis` | Analyze document for privileged content |
| `GET` | `/documents/:id/privilege-log` | Get existing privilege log |
| `GET` | `/documents/privilege-logs` | List all privilege logs |
| `PUT` | `/documents/:id/privilege-content/:contentId/review` | Review privileged content |
| `POST` | `/documents/:id/redactions` | Apply single redaction |
| `POST` | `/documents/:id/bulk-redactions` | Apply bulk redactions |

---

## 🔧 Common Request Bodies

### Analyze Document
```json
{
  "includeAIAnalysis": true,
  "confidenceThreshold": 0.8,
  "privilegeTypes": ["attorney_client", "work_product"],
  "requireManualReview": true
}
```

### Review Content
```json
{
  "status": "confirmed",
  "reason": "Confirmed attorney-client privilege",
  "applyRedaction": true
}
```

### Apply Redaction
```json
{
  "contentId": "content-123",
  "reason": "Attorney-client privilege protection"
}
```

### Bulk Redaction
```json
{
  "contentIds": ["content-1", "content-2", "content-3"],
  "reason": "Bulk privilege protection"
}
```

---

## 📊 Response Data Structure

### Analysis Result
```json
{
  "success": true,
  "data": {
    "documentId": "string",
    "privilegedContent": [
      {
        "id": "string",
        "content": "string",
        "privilegeType": "attorney_client",
        "confidenceScore": 0.92,
        "status": "detected"
      }
    ],
    "summary": {
      "totalItemsFound": 5,
      "highConfidenceItems": 3,
      "requiresManualReview": 2
    }
  }
}
```

---

## 🏷️ Enums & Constants

### Privilege Types
```typescript
enum PrivilegeType {
  ATTORNEY_CLIENT = 'attorney_client',
  WORK_PRODUCT = 'work_product',
  CONFIDENTIAL_COMMUNICATION = 'confidential_communication',
  TRADE_SECRET = 'trade_secret',
  MEDICAL_PRIVILEGE = 'medical_privilege',
  SPOUSAL_PRIVILEGE = 'spousal_privilege',
  OTHER = 'other'
}
```

### Privilege Status
```typescript
enum PrivilegeStatus {
  DETECTED = 'detected',
  UNDER_REVIEW = 'under_review',
  CONFIRMED = 'confirmed',
  REJECTED = 'rejected',
  REDACTED = 'redacted'
}
```

### Detection Methods
- `pattern` - Pattern-based detection
- `ai` - AI-powered detection
- `manual` - Manually added

---

## ⚡ React Hook Examples

### usePrivilegeAnalysis Hook
```typescript
import { useState } from 'react';

interface AnalysisOptions {
  includeAIAnalysis?: boolean;
  confidenceThreshold?: number;
  privilegeTypes?: string[];
  requireManualReview?: boolean;
}

export const usePrivilegeAnalysis = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const analyzeDocument = async (documentId: string, options: AnalysisOptions) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/documents/${documentId}/privilege-analysis`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${getToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(options)
      });

      if (!response.ok) {
        throw new Error(`Analysis failed: ${response.statusText}`);
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Analysis failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { analyzeDocument, loading, error };
};
```

### usePrivilegeLog Hook
```typescript
export const usePrivilegeLog = (documentId: string) => {
  const [privilegeLog, setPrivilegeLog] = useState(null);
  const [loading, setLoading] = useState(false);

  const fetchPrivilegeLog = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/documents/${documentId}/privilege-log`, {
        headers: {
          'Authorization': `Bearer ${getToken()}`
        }
      });

      if (response.ok) {
        const result = await response.json();
        setPrivilegeLog(result.data);
      }
    } catch (error) {
      console.error('Failed to fetch privilege log:', error);
    } finally {
      setLoading(false);
    }
  };

  const reviewContent = async (contentId: string, status: string, reason?: string) => {
    const response = await fetch(`/api/documents/${documentId}/privilege-content/${contentId}/review`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ status, reason })
    });

    if (response.ok) {
      await fetchPrivilegeLog(); // Refresh data
    }
  };

  const applyRedaction = async (contentId: string, reason: string) => {
    const response = await fetch(`/api/documents/${documentId}/redactions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${getToken()}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ contentId, reason })
    });

    if (response.ok) {
      await fetchPrivilegeLog(); // Refresh data
    }
  };

  return {
    privilegeLog,
    loading,
    fetchPrivilegeLog,
    reviewContent,
    applyRedaction
  };
};
```

---

## 🎨 UI Component Examples

### Privilege Analysis Button
```tsx
import React from 'react';
import { usePrivilegeAnalysis } from './hooks/usePrivilegeAnalysis';

interface PrivilegeAnalysisButtonProps {
  documentId: string;
  onAnalysisComplete: (result: any) => void;
}

export const PrivilegeAnalysisButton: React.FC<PrivilegeAnalysisButtonProps> = ({
  documentId,
  onAnalysisComplete
}) => {
  const { analyzeDocument, loading, error } = usePrivilegeAnalysis();

  const handleAnalyze = async () => {
    try {
      const result = await analyzeDocument(documentId, {
        includeAIAnalysis: true,
        confidenceThreshold: 0.8,
        privilegeTypes: ['attorney_client', 'work_product'],
        requireManualReview: true
      });
      onAnalysisComplete(result);
    } catch (error) {
      console.error('Analysis failed:', error);
    }
  };

  return (
    <div>
      <button 
        onClick={handleAnalyze} 
        disabled={loading}
        className="bg-blue-600 text-white px-4 py-2 rounded disabled:opacity-50"
      >
        {loading ? 'Analyzing...' : 'Analyze for Privilege'}
      </button>
      {error && <p className="text-red-500 mt-2">{error}</p>}
    </div>
  );
};
```

### Privilege Content Item
```tsx
interface PrivilegeContentItemProps {
  content: PrivilegedContent;
  onReview: (contentId: string, status: string, reason?: string) => void;
  onRedact: (contentId: string, reason: string) => void;
}

export const PrivilegeContentItem: React.FC<PrivilegeContentItemProps> = ({
  content,
  onReview,
  onRedact
}) => {
  const getConfidenceColor = (score: number) => {
    if (score >= 0.9) return 'text-green-600';
    if (score >= 0.7) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="border rounded-lg p-4 mb-4">
      <div className="flex justify-between items-start mb-2">
        <span className="font-medium">{content.privilegeType.replace('_', ' ')}</span>
        <span className={`font-bold ${getConfidenceColor(content.confidenceScore)}`}>
          {(content.confidenceScore * 100).toFixed(1)}%
        </span>
      </div>
      
      <p className="text-gray-700 mb-3">"{content.content}"</p>
      
      <div className="flex gap-2">
        <button
          onClick={() => onReview(content.id, 'confirmed')}
          className="bg-green-600 text-white px-3 py-1 rounded text-sm"
        >
          Confirm
        </button>
        <button
          onClick={() => onReview(content.id, 'rejected')}
          className="bg-red-600 text-white px-3 py-1 rounded text-sm"
        >
          Reject
        </button>
        <button
          onClick={() => onRedact(content.id, 'Privilege protection')}
          className="bg-blue-600 text-white px-3 py-1 rounded text-sm"
        >
          Redact
        </button>
      </div>
    </div>
  );
};
```

---

## ⚠️ Error Handling

### Common Error Codes
- `400` - Invalid request parameters
- `403` - Feature not available (subscription)
- `404` - Document/content not found
- `500` - Server error

### Error Response Format
```json
{
  "statusCode": 403,
  "message": "Feature 'privilege_log_automation' not available",
  "error": "Forbidden"
}
```

---

## 🔒 Security Notes

- All endpoints require JWT authentication
- PRO subscription tier required
- Document access permissions enforced
- Audit trail maintained for all actions

---

## 📈 Performance Tips

- Use confidence threshold >= 0.7 for better accuracy
- Enable AI analysis for comprehensive detection
- Batch redactions using bulk endpoint
- Cache privilege logs for better UX

---

## 🧪 Testing

### Test Document Content
```text
This is a confidential attorney-client communication regarding legal advice. 
This work product contains privileged information protected by attorney-client privilege.
```

### Expected Detections
- "attorney-client communication" (confidence: ~0.9)
- "work product" (confidence: ~0.85)
- "privileged information" (confidence: ~0.8)

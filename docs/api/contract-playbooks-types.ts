// Contract Playbooks API TypeScript Definitions
// Copy these types into your frontend project

// ============================================================================
// CORE DATA MODELS
// ============================================================================

export interface ContractPlaybook {
  id: string;
  organizationId: string;
  name: string;
  contractType: ContractType;
  description?: string;
  version: string;
  rules: PlaybookRule[];
  metadata: PlaybookMetadata;
  isActive: boolean;
  isTemplate: boolean;
  createdBy: string;
  updatedBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface PlaybookRule {
  id: string;
  name: string;
  category: string;
  ruleType: RuleType;
  severity: Severity;
  criteria: RuleCriteria;
  acceptableLanguage: AcceptableLanguage;
  unacceptableTerms: UnacceptableTerms;
  description: string;
  isActive: boolean;
}

export interface ContractAnalysis {
  id: string;
  organizationId: string;
  contractId: string;
  playbookId: string;
  playbookName: string;
  overallScore: number; // 0-100
  riskLevel: RiskLevel;
  status: AnalysisStatus;
  deviations: Deviation[];
  summary: AnalysisSummary;
  metrics: AnalysisMetrics;
  metadata: AnalysisMetadata;
  analyzedBy: string;
  analyzedAt: string;
  createdAt: string;
  updatedAt: string;
}

// ============================================================================
// ENUMS AND TYPES
// ============================================================================

export type ContractType = 
  | 'nda' 
  | 'employment' 
  | 'service_agreement' 
  | 'lease' 
  | 'purchase' 
  | 'partnership' 
  | 'licensing' 
  | 'other';

export type RuleType = 
  | 'required_clause' 
  | 'prohibited_clause' 
  | 'conditional_clause' 
  | 'formatting_rule' 
  | 'calculation_rule';

export type Severity = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';

export type RiskLevel = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';

export type AnalysisStatus = 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';

// ============================================================================
// SUPPORTING INTERFACES
// ============================================================================

export interface RuleCriteria {
  keywords: string[];
  patterns: string[];
  semanticConcepts: string[];
  contextRequirements: string[];
}

export interface AcceptableLanguage {
  preferred: string[];
  acceptable: string[];
  fallbackPositions: string[];
}

export interface UnacceptableTerms {
  prohibited: string[];
  requiresEscalation: string[];
  autoReject: string[];
}

export interface PlaybookMetadata {
  industry?: string;
  jurisdiction?: string;
  riskProfile?: string;
  tags?: string[];
  customFields?: Record<string, any>;
}

export interface Deviation {
  id: string;
  ruleId: string;
  ruleName: string;
  severity: Severity;
  category: string;
  description: string;
  location: DeviationLocation;
  suggestedAction: string;
  confidence: number;
  context: string;
}

export interface DeviationLocation {
  page?: number;
  section?: string;
  paragraph?: number;
  lineNumber?: number;
  characterRange?: {
    start: number;
    end: number;
  };
}

export interface AnalysisSummary {
  totalDeviations: number;
  criticalDeviations: number;
  highRiskDeviations: number;
  mediumRiskDeviations: number;
  lowRiskDeviations: number;
  compliancePercentage: number;
  keyFindings: string[];
  executiveSummary: string;
}

export interface AnalysisMetrics {
  processingTimeMs: number;
  aiAnalysisTime: number;
  rulesEvaluated: number;
  clausesAnalyzed: number;
  confidenceScore: number;
}

export interface AnalysisMetadata {
  contractType: string;
  contractTitle: string;
  analysisVersion: string;
  aiModel?: string;
  customParameters?: Record<string, any>;
}

// ============================================================================
// REQUEST/RESPONSE TYPES
// ============================================================================

export interface PlaybookSearchParams {
  query?: string;
  contractType?: ContractType;
  isActive?: boolean;
  isTemplate?: boolean;
  page?: number;
  limit?: number;
}

export interface PlaybooksResponse {
  playbooks: ContractPlaybook[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface CreatePlaybookRequest {
  name: string;
  contractType: ContractType;
  description?: string;
  version: string;
  rules: CreatePlaybookRule[];
  metadata: PlaybookMetadata;
  isActive?: boolean;
  isTemplate?: boolean;
}

export interface CreatePlaybookRule {
  name: string;
  category: string;
  ruleType: RuleType;
  severity: Severity;
  criteria: RuleCriteria;
  acceptableLanguage: AcceptableLanguage;
  unacceptableTerms: UnacceptableTerms;
  description: string;
  isActive: boolean;
}

export interface UpdatePlaybookRequest {
  name?: string;
  description?: string;
  version?: string;
  rules?: UpdatePlaybookRule[];
  metadata?: PlaybookMetadata;
  isActive?: boolean;
  isTemplate?: boolean;
}

export interface UpdatePlaybookRule extends Partial<CreatePlaybookRule> {
  id?: string;
}

export interface AnalyzeContractRequest {
  contractId: string;
  playbookId: string;
  options?: AnalysisOptions;
}

export interface AnalysisOptions {
  includeRecommendations?: boolean;
  riskThreshold?: number; // 1-5
  aiAnalysis?: boolean;
  detailedReport?: boolean;
}

export interface AnalysisSearchParams {
  contractId?: string;
  playbookId?: string;
  riskLevel?: RiskLevel;
  status?: AnalysisStatus;
  startDate?: string; // ISO 8601
  endDate?: string; // ISO 8601
  page?: number;
  limit?: number;
}

export interface AnalysesResponse {
  analyses: ContractAnalysis[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface PlaybookAnalytics {
  playbookId: string;
  totalAnalyses: number;
  averageScore: number;
  riskDistribution: {
    LOW: number;
    MEDIUM: number;
    HIGH: number;
    CRITICAL: number;
  };
  commonDeviations: Array<{
    ruleId: string;
    ruleName: string;
    frequency: number;
    averageSeverity: Severity;
  }>;
  performanceMetrics: {
    averageProcessingTime: number;
    averageConfidenceScore: number;
  };
  timeSeriesData: Array<{
    date: string;
    analysisCount: number;
    averageScore: number;
  }>;
}

export interface DuplicatePlaybookRequest {
  name: string;
}

export interface ExportPlaybookResponse {
  playbook: ContractPlaybook;
  exportFormat: string;
  exportedAt: string;
  version: string;
}

export interface ImportPlaybookRequest {
  playbookData: any;
  options?: {
    overwriteExisting?: boolean;
    validateRules?: boolean;
  };
}

// ============================================================================
// ERROR TYPES
// ============================================================================

export interface ApiError {
  statusCode: number;
  message: string;
  error: string;
  details?: any;
  path?: string;
  timestamp?: string;
}

export interface ValidationError extends ApiError {
  statusCode: 400;
  error: 'Bad Request';
}

export interface UnauthorizedError extends ApiError {
  statusCode: 401;
  error: 'Unauthorized';
}

export interface ForbiddenError extends ApiError {
  statusCode: 403;
  error: 'Forbidden';
  message: "Feature 'contract_playbooks' is not available in your current subscription plan";
}

export interface NotFoundError extends ApiError {
  statusCode: 404;
  error: 'Not Found';
}

export interface ConflictError extends ApiError {
  statusCode: 409;
  error: 'Conflict';
}

export interface InternalServerError extends ApiError {
  statusCode: 500;
  error: 'Internal Server Error';
  message: 'Internal server error';
}

# User Feedback API Documentation

This document provides comprehensive documentation for the User Feedback feature of the Legal Document Analyzer API, which includes feedback collection, categorization, corrections, and analytics.

**Version:** 1.0.0  
**Last Updated:** April 19, 2025  
**Status:** Implemented

## Overview

The User Feedback feature enables users to provide feedback on the AI-powered analysis, suggest corrections, and helps improve the quality of the system through:

1. **Inline Feedback Mechanisms**: Contextual feedback collection with thumbs up/down and comment capabilities
2. **Suggestion/Correction Capabilities**: User-submitted corrections to AI analysis with a review workflow
3. **Feedback Dashboard**: Analytics for tracking feedback trends, patterns, and quality improvements (admin-only access)

## Authentication

All endpoints require authentication using a JWT token and organization context:

```
Authorization: Bearer <jwt_token>
X-Organization-Id: <organization_id>
```

## Feature Availability

The User Feedback feature is available in the following subscription tiers:
- Free: Basic feedback collection
- Professional: Feedback collection and corrections
- Enterprise: Feedback collection, corrections, and advanced analytics (admin-only)

## Access Controls

The API implements the following access controls:
- All users can submit feedback
- All users can submit corrections to AI analysis
- All users can view their own individual feedback items by ID
- Only admin users can view all feedback across the organization
- Only admin users can access analytics endpoints and the feedback dashboard
- Organization-based isolation ensures users can only access data within their organization

## 1. Feedback Management

### 1.1 Create Feedback

Create new user feedback.

**Endpoint:** `POST /api/user-feedback/feedback`

**Request Body:**
```json
{
  "content": "The analysis missed a key legal precedent.",
  "type": "thumbs_down",
  "source": "document_analysis",
  "contextData": {
    "documentId": "123",
    "sectionId": "456"
  },
  "sourceId": "789",
  "rating": 2,
  "isAnonymous": false
}
```

**Response:** (201 Created)
```json
{
  "id": "6804141b91e84ac0305e0665",
  "content": "The analysis missed a key legal precedent.",
  "type": "thumbs_down",
  "status": "pending",
  "source": "document_analysis",
  "userId": "d0331076-bfc2-4f8e-829e-64d2af85270e",
  "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
  "contextData": {
    "documentId": "123",
    "sectionId": "456"
  },
  "sourceId": "789",
  "rating": 2,
  "isAnonymous": false,
  "upvotes": 0,
  "downvotes": 0,
  "tags": [],
  "createdAt": "2025-04-19T21:17:26.123Z",
  "updatedAt": "2025-04-19T21:17:26.123Z"
}
```

### 1.2 Get All Feedback

Retrieve all feedback with filtering options.

**Endpoint:** `GET /api/user-feedback/feedback` (admin-only)

**Query Parameters:**
- `type`: Filter by feedback type (thumbs_up, thumbs_down, correction, suggestion, general)
- `status`: Filter by status (pending, reviewed, implemented, rejected)
- `source`: Filter by source (document_analysis, chat, document_comparison, citation_analysis)
- `categoryId`: Filter by category ID
- `sourceId`: Filter by source content ID
- `startDate`: Filter by start date
- `endDate`: Filter by end date
- `searchText`: Search in feedback content
- `page`: Page number for pagination (default: 1)
- `limit`: Items per page (default: 20)

**Response:** (200 OK)
```json
{
  "data": [
    {
      "id": "6804141b91e84ac0305e0665",
      "content": "The analysis missed a key legal precedent.",
      "type": "thumbs_down",
      "status": "pending",
      "source": "document_analysis",
      "userId": "d0331076-bfc2-4f8e-829e-64d2af85270e",
      "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
      "contextData": {
        "documentId": "123",
        "sectionId": "456"
      },
      "sourceId": "789",
      "rating": 2,
      "isAnonymous": false,
      "upvotes": 0,
      "downvotes": 0,
      "tags": [],
      "createdAt": "2025-04-19T21:17:26.123Z",
      "updatedAt": "2025-04-19T21:17:26.123Z"
    }
  ],
  "pagination": {
    "total": 1,
    "page": 1,
    "limit": 20,
    "pages": 1
  }
}
```

### 1.3 Get Feedback by ID

Retrieve a specific feedback by its ID.

**Endpoint:** `GET /api/user-feedback/feedback/:id`

**Response:** (200 OK)
```json
{
  "id": "6804141b91e84ac0305e0665",
  "content": "The analysis missed a key legal precedent.",
  "type": "thumbs_down",
  "status": "pending",
  "source": "document_analysis",
  "userId": "d0331076-bfc2-4f8e-829e-64d2af85270e",
  "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
  "contextData": {
    "documentId": "123",
    "sectionId": "456"
  },
  "sourceId": "789",
  "rating": 2,
  "isAnonymous": false,
  "upvotes": 0,
  "downvotes": 0,
  "tags": [],
  "createdAt": "2025-04-19T21:17:26.123Z",
  "updatedAt": "2025-04-19T21:17:26.123Z"
}
```

### 1.4 Update Feedback

Update an existing feedback.

**Endpoint:** `PATCH /api/user-feedback/feedback/:id`

**Request Body:**
```json
{
  "status": "reviewed",
  "adminResponse": "Thank you for your feedback. We have updated our analysis.",
  "categoryId": "abc123"
}
```

**Response:** (200 OK)
```json
{
  "id": "6804141b91e84ac0305e0665",
  "content": "The analysis missed a key legal precedent.",
  "type": "thumbs_down",
  "status": "reviewed",
  "source": "document_analysis",
  "userId": "d0331076-bfc2-4f8e-829e-64d2af85270e",
  "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
  "contextData": {
    "documentId": "123",
    "sectionId": "456"
  },
  "sourceId": "789",
  "rating": 2,
  "adminResponse": "Thank you for your feedback. We have updated our analysis.",
  "adminResponseDate": "2025-04-19T21:20:15.456Z",
  "isAnonymous": false,
  "upvotes": 0,
  "downvotes": 0,
  "tags": [],
  "createdAt": "2025-04-19T21:17:26.123Z",
  "updatedAt": "2025-04-19T21:20:15.456Z"
}
```

### 1.5 Delete Feedback

Delete a feedback by its ID.

**Endpoint:** `DELETE /api/user-feedback/feedback/:id`

**Response:** (204 No Content)

### 1.6 Vote on Feedback

Vote on a feedback (upvote or downvote).

**Endpoint:** `POST /api/user-feedback/feedback/:id/vote`

**Request Body:**
```json
{
  "voteType": "upvote"
}
```

**Response:** (200 OK)
```json
{
  "id": "6804141b91e84ac0305e0665",
  "content": "The analysis missed a key legal precedent.",
  "type": "thumbs_down",
  "status": "pending",
  "source": "document_analysis",
  "userId": "d0331076-bfc2-4f8e-829e-64d2af85270e",
  "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
  "contextData": {
    "documentId": "123",
    "sectionId": "456"
  },
  "sourceId": "789",
  "rating": 2,
  "isAnonymous": false,
  "upvotes": 1,
  "downvotes": 0,
  "tags": [],
  "createdAt": "2025-04-19T21:17:26.123Z",
  "updatedAt": "2025-04-19T21:21:45.789Z"
}
```

## 2. Feedback Categories

### 2.1 Create Category

Create a new feedback category.

**Endpoint:** `POST /api/user-feedback/categories`

**Request Body:**
```json
{
  "name": "Legal Analysis Issues",
  "description": "Issues related to legal analysis quality or accuracy",
  "color": "#4F46E5",
  "priority": 1
}
```

**Response:** (201 Created)
```json
{
  "id": "6804141b91e84ac0305e0666",
  "name": "Legal Analysis Issues",
  "description": "Issues related to legal analysis quality or accuracy",
  "isDefault": false,
  "isActive": true,
  "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
  "createdBy": "d0331076-bfc2-4f8e-829e-64d2af85270e",
  "priority": 1,
  "color": "#4F46E5",
  "tags": [],
  "createdAt": "2025-04-19T21:17:26.123Z",
  "updatedAt": "2025-04-19T21:17:26.123Z"
}
```

### 2.2 Get All Categories

Retrieve all feedback categories for the organization.

**Endpoint:** `GET /api/user-feedback/categories`

**Response:** (200 OK)
```json
[
  {
    "id": "6804141b91e84ac0305e0666",
    "name": "Legal Analysis Issues",
    "description": "Issues related to legal analysis quality or accuracy",
    "isDefault": false,
    "isActive": true,
    "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
    "createdBy": "d0331076-bfc2-4f8e-829e-64d2af85270e",
    "priority": 1,
    "color": "#4F46E5",
    "tags": [],
    "createdAt": "2025-04-19T21:17:26.123Z",
    "updatedAt": "2025-04-19T21:17:26.123Z"
  }
]
```

## 3. Feedback Corrections

### 3.1 Create Correction

Create a new feedback correction.

**Endpoint:** `POST /api/user-feedback/corrections`

**Request Body:**
```json
{
  "feedbackId": "6804141b91e84ac0305e0665",
  "originalContent": "The case Smith v. Jones established...",
  "correctedContent": "The case Smith v. Johnson established...",
  "contextData": {
    "documentId": "123",
    "sectionId": "456",
    "paragraph": 3
  },
  "sourceId": "789",
  "sourceType": "document"
}
```

**Response:** (201 Created)
```json
{
  "id": "6804141b91e84ac0305e0667",
  "feedbackId": "6804141b91e84ac0305e0665",
  "originalContent": "The case Smith v. Jones established...",
  "correctedContent": "The case Smith v. Johnson established...",
  "userId": "d0331076-bfc2-4f8e-829e-64d2af85270e",
  "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
  "status": "pending",
  "contextData": {
    "documentId": "123",
    "sectionId": "456",
    "paragraph": 3
  },
  "sourceId": "789",
  "sourceType": "document",
  "upvotes": 0,
  "downvotes": 0,
  "tags": [],
  "isIncorporatedInTraining": false,
  "createdAt": "2025-04-19T21:17:26.123Z",
  "updatedAt": "2025-04-19T21:17:26.123Z"
}
```

### 3.2 Get All Corrections

Retrieve all feedback corrections with filtering options.

**Endpoint:** `GET /api/user-feedback/corrections`

**Query Parameters:**
- `feedbackId`: Filter by feedback ID
- `status`: Filter by status (pending, approved, rejected, implemented)
- `sourceId`: Filter by source content ID
- `sourceType`: Filter by source type
- `searchText`: Search in correction content
- `page`: Page number for pagination (default: 1)
- `limit`: Items per page (default: 20)

**Response:** (200 OK)
```json
{
  "data": [
    {
      "id": "6804141b91e84ac0305e0667",
      "feedbackId": "6804141b91e84ac0305e0665",
      "originalContent": "The case Smith v. Jones established...",
      "correctedContent": "The case Smith v. Johnson established...",
      "userId": "d0331076-bfc2-4f8e-829e-64d2af85270e",
      "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
      "status": "pending",
      "contextData": {
        "documentId": "123",
        "sectionId": "456",
        "paragraph": 3
      },
      "sourceId": "789",
      "sourceType": "document",
      "upvotes": 0,
      "downvotes": 0,
      "tags": [],
      "isIncorporatedInTraining": false,
      "createdAt": "2025-04-19T21:17:26.123Z",
      "updatedAt": "2025-04-19T21:17:26.123Z"
    }
  ],
  "pagination": {
    "total": 1,
    "page": 1,
    "limit": 20,
    "pages": 1
  }
}
```

## 4. Analytics Endpoints

### 4.1 Feedback Summary

Get a summary of feedback analytics.

**Endpoint:** `GET /api/user-feedback/analytics/summary` (admin-only)

**Query Parameters:**
- `startDate`: Filter by start date
- `endDate`: Filter by end date
- `sourceId`: Filter by source content ID
- `source`: Filter by source type

**Response:** (200 OK)
```json
{
  "totalFeedback": 150,
  "typeBreakdown": [
    {
      "type": "thumbs_up",
      "count": 80,
      "percentage": 53.33
    },
    {
      "type": "thumbs_down",
      "count": 40,
      "percentage": 26.67
    },
    {
      "type": "correction",
      "count": 20,
      "percentage": 13.33
    },
    {
      "type": "suggestion",
      "count": 10,
      "percentage": 6.67
    }
  ],
  "statusBreakdown": [
    {
      "status": "pending",
      "count": 50,
      "percentage": 33.33
    },
    {
      "status": "reviewed",
      "count": 70,
      "percentage": 46.67
    },
    {
      "status": "implemented",
      "count": 30,
      "percentage": 20
    }
  ],
  "sourceBreakdown": [
    {
      "source": "document_analysis",
      "count": 80,
      "percentage": 53.33
    },
    {
      "source": "chat",
      "count": 40,
      "percentage": 26.67
    },
    {
      "source": "document_comparison",
      "count": 30,
      "percentage": 20
    }
  ],
  "categoryBreakdown": [
    {
      "id": "6804141b91e84ac0305e0666",
      "name": "Legal Analysis Issues",
      "count": 60
    }
  ],
  "timeSeriesData": [
    {
      "_id": "2025-04-01",
      "count": 15,
      "thumbsUp": 8,
      "thumbsDown": 5,
      "corrections": 2
    },
    {
      "_id": "2025-04-02",
      "count": 18,
      "thumbsUp": 10,
      "thumbsDown": 6,
      "corrections": 2
    }
  ],
  "ratings": {
    "average": 4.2,
    "count": 120
  }
}
```

## Implementation Details

The User Feedback feature is implemented using the following components:

### Schemas

1. **Feedback Schema**: Defines the structure for user feedback with types, status, and source
2. **Feedback Category Schema**: Defines categories for organizing feedback
3. **Feedback Correction Schema**: Defines the structure for user-submitted corrections

### Services

1. **User Feedback Service**: Manages feedback, categories, and corrections
2. **Feedback Analytics Service**: Provides analytics and insights on feedback data

### Controllers

1. **User Feedback Controller**: Handles API requests for feedback, categories, corrections, and analytics

### Subscription Integration

The User Feedback feature is integrated with the subscription system:
- Feature availability is controlled through the FeatureAvailabilityGuard
- Different subscription tiers have access to different levels of feedback functionality
- Advanced analytics are only available in the Enterprise tier

## Error Handling

All endpoints return appropriate HTTP status codes and error messages:

- **400 Bad Request**: Invalid input data
- **401 Unauthorized**: Missing or invalid authentication
- **403 Forbidden**: Insufficient permissions or feature not available in subscription
- **404 Not Found**: Resource not found
- **500 Internal Server Error**: Server-side error

## Future Enhancements

Planned enhancements for the User Feedback feature include:

1. **Machine Learning Integration**: Automated learning from user corrections
2. **Sentiment Analysis**: Advanced analysis of feedback sentiment
3. **Interactive Feedback Dashboard**: Visual dashboard for feedback management

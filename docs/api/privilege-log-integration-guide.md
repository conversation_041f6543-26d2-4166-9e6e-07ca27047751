# Privilege Log Automation - Frontend Integration Guide

## 🎯 Overview

This guide provides step-by-step instructions for integrating the Privilege Log Automation feature into your React frontend application.

## 📋 Prerequisites

- React 18+
- TypeScript support
- JWT authentication system
- PRO subscription tier

---

## 🚀 Step 1: Setup Types and Interfaces

Create a types file for the privilege log feature:

```typescript
// types/privilege-log.ts

export enum PrivilegeType {
  ATTORNEY_CLIENT = 'attorney_client',
  WORK_PRODUCT = 'work_product',
  CONFIDENTIAL_COMMUNICATION = 'confidential_communication',
  TRADE_SECRET = 'trade_secret',
  MEDICAL_PRIVILEGE = 'medical_privilege',
  SPOUSAL_PRIVILEGE = 'spousal_privilege',
  OTHER = 'other'
}

export enum PrivilegeStatus {
  DETECTED = 'detected',
  UNDER_REVIEW = 'under_review',
  CONFIRMED = 'confirmed',
  REJECTED = 'rejected',
  REDACTED = 'redacted'
}

export interface PrivilegedContent {
  id: string;
  documentId: string;
  startPosition: number;
  endPosition: number;
  content: string;
  privilegeType: PrivilegeType;
  confidenceScore: number;
  detectionMethod: 'pattern' | 'ai' | 'manual';
  status: PrivilegeStatus;
  reviewedBy?: string;
  reviewedAt?: Date;
  redactionApplied: boolean;
  redactionReason?: string;
}

export interface PrivilegeAnalysisResult {
  documentId: string;
  privilegedContent: PrivilegedContent[];
  redactionSuggestions: RedactionSuggestion[];
  summary: {
    totalItemsFound: number;
    highConfidenceItems: number;
    requiresManualReview: number;
    autoRedactable: number;
  };
  analysisMetadata: {
    analysisDate: Date;
    detectionMethods: string[];
    aiModelUsed?: string;
    processingTime: number;
  };
}

export interface RedactionSuggestion {
  contentId: string;
  originalText: string;
  suggestedRedaction: string;
  reason: string;
  privilegeType: PrivilegeType;
  confidenceScore: number;
  requiresReview: boolean;
}

export interface AnalysisOptions {
  includeAIAnalysis?: boolean;
  confidenceThreshold?: number;
  privilegeTypes?: PrivilegeType[];
  autoRedact?: boolean;
  requireManualReview?: boolean;
}
```

---

## 🔧 Step 2: Create API Service

Create a service for API calls:

```typescript
// services/privilege-log.service.ts

import { AnalysisOptions, PrivilegeAnalysisResult, PrivilegedContent } from '../types/privilege-log';

class PrivilegeLogService {
  private baseUrl = '/api/documents';

  private async request<T>(url: string, options: RequestInit = {}): Promise<T> {
    const token = localStorage.getItem('authToken'); // Adjust based on your auth system
    
    const response = await fetch(url, {
      ...options,
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Request failed');
    }

    const result = await response.json();
    return result.data || result;
  }

  async analyzeDocument(
    documentId: string, 
    options: AnalysisOptions
  ): Promise<PrivilegeAnalysisResult> {
    return this.request<PrivilegeAnalysisResult>(
      `${this.baseUrl}/${documentId}/privilege-analysis`,
      {
        method: 'POST',
        body: JSON.stringify(options),
      }
    );
  }

  async getPrivilegeLog(documentId: string): Promise<any> {
    return this.request(`${this.baseUrl}/${documentId}/privilege-log`);
  }

  async getPrivilegeLogs(params: {
    status?: string;
    privilegeType?: string;
    page?: number;
    limit?: number;
  } = {}): Promise<{ logs: any[]; total: number }> {
    const queryString = new URLSearchParams(
      Object.entries(params).filter(([_, value]) => value !== undefined)
    ).toString();
    
    return this.request(`${this.baseUrl}/privilege-logs?${queryString}`);
  }

  async reviewContent(
    documentId: string,
    contentId: string,
    data: {
      status: string;
      reason?: string;
      applyRedaction?: boolean;
    }
  ): Promise<any> {
    return this.request(
      `${this.baseUrl}/${documentId}/privilege-content/${contentId}/review`,
      {
        method: 'PUT',
        body: JSON.stringify(data),
      }
    );
  }

  async applyRedaction(
    documentId: string,
    data: {
      contentId: string;
      redactionText?: string;
      reason: string;
    }
  ): Promise<any> {
    return this.request(`${this.baseUrl}/${documentId}/redactions`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async applyBulkRedaction(
    documentId: string,
    data: {
      contentIds: string[];
      redactionText?: string;
      reason: string;
    }
  ): Promise<any> {
    return this.request(`${this.baseUrl}/${documentId}/bulk-redactions`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }
}

export const privilegeLogService = new PrivilegeLogService();
```

---

## 🎣 Step 3: Create React Hooks

Create custom hooks for state management:

```typescript
// hooks/usePrivilegeAnalysis.ts

import { useState, useCallback } from 'react';
import { privilegeLogService } from '../services/privilege-log.service';
import { AnalysisOptions, PrivilegeAnalysisResult } from '../types/privilege-log';

export const usePrivilegeAnalysis = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<PrivilegeAnalysisResult | null>(null);

  const analyzeDocument = useCallback(async (
    documentId: string, 
    options: AnalysisOptions
  ) => {
    setLoading(true);
    setError(null);
    
    try {
      const analysisResult = await privilegeLogService.analyzeDocument(documentId, options);
      setResult(analysisResult);
      return analysisResult;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Analysis failed';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const clearResult = useCallback(() => {
    setResult(null);
    setError(null);
  }, []);

  return {
    analyzeDocument,
    clearResult,
    loading,
    error,
    result
  };
};
```

```typescript
// hooks/usePrivilegeLog.ts

import { useState, useEffect, useCallback } from 'react';
import { privilegeLogService } from '../services/privilege-log.service';

export const usePrivilegeLog = (documentId: string) => {
  const [privilegeLog, setPrivilegeLog] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPrivilegeLog = useCallback(async () => {
    if (!documentId) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const log = await privilegeLogService.getPrivilegeLog(documentId);
      setPrivilegeLog(log);
    } catch (err) {
      if (err instanceof Error && err.message.includes('not found')) {
        setPrivilegeLog(null); // No privilege log exists yet
      } else {
        setError(err instanceof Error ? err.message : 'Failed to fetch privilege log');
      }
    } finally {
      setLoading(false);
    }
  }, [documentId]);

  const reviewContent = useCallback(async (
    contentId: string,
    status: string,
    reason?: string,
    applyRedaction?: boolean
  ) => {
    try {
      await privilegeLogService.reviewContent(documentId, contentId, {
        status,
        reason,
        applyRedaction
      });
      await fetchPrivilegeLog(); // Refresh data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Review failed');
      throw err;
    }
  }, [documentId, fetchPrivilegeLog]);

  const applyRedaction = useCallback(async (
    contentId: string,
    reason: string,
    redactionText?: string
  ) => {
    try {
      await privilegeLogService.applyRedaction(documentId, {
        contentId,
        reason,
        redactionText
      });
      await fetchPrivilegeLog(); // Refresh data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Redaction failed');
      throw err;
    }
  }, [documentId, fetchPrivilegeLog]);

  const applyBulkRedaction = useCallback(async (
    contentIds: string[],
    reason: string,
    redactionText?: string
  ) => {
    try {
      await privilegeLogService.applyBulkRedaction(documentId, {
        contentIds,
        reason,
        redactionText
      });
      await fetchPrivilegeLog(); // Refresh data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Bulk redaction failed');
      throw err;
    }
  }, [documentId, fetchPrivilegeLog]);

  useEffect(() => {
    fetchPrivilegeLog();
  }, [fetchPrivilegeLog]);

  return {
    privilegeLog,
    loading,
    error,
    fetchPrivilegeLog,
    reviewContent,
    applyRedaction,
    applyBulkRedaction
  };
};
```

---

## 🎨 Step 4: Create UI Components

### Main Privilege Analysis Component

```tsx
// components/PrivilegeAnalysis.tsx

import React, { useState } from 'react';
import { usePrivilegeAnalysis } from '../hooks/usePrivilegeAnalysis';
import { usePrivilegeLog } from '../hooks/usePrivilegeLog';
import { PrivilegeType } from '../types/privilege-log';
import { AnalysisOptionsForm } from './AnalysisOptionsForm';
import { PrivilegeContentList } from './PrivilegeContentList';
import { AnalysisSummary } from './AnalysisSummary';

interface PrivilegeAnalysisProps {
  documentId: string;
}

export const PrivilegeAnalysis: React.FC<PrivilegeAnalysisProps> = ({ documentId }) => {
  const { analyzeDocument, loading: analyzing, error: analysisError, result } = usePrivilegeAnalysis();
  const { privilegeLog, loading: logLoading, reviewContent, applyRedaction, applyBulkRedaction } = usePrivilegeLog(documentId);
  
  const [showAnalysisForm, setShowAnalysisForm] = useState(false);

  const handleAnalyze = async (options: any) => {
    try {
      await analyzeDocument(documentId, options);
      setShowAnalysisForm(false);
    } catch (error) {
      console.error('Analysis failed:', error);
    }
  };

  const displayData = result || privilegeLog;

  return (
    <div className="privilege-analysis">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Privilege Log Analysis</h2>
        <button
          onClick={() => setShowAnalysisForm(true)}
          disabled={analyzing}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
        >
          {analyzing ? 'Analyzing...' : 'Analyze Document'}
        </button>
      </div>

      {analysisError && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {analysisError}
        </div>
      )}

      {showAnalysisForm && (
        <AnalysisOptionsForm
          onAnalyze={handleAnalyze}
          onCancel={() => setShowAnalysisForm(false)}
          loading={analyzing}
        />
      )}

      {(logLoading || analyzing) && (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}

      {displayData && (
        <div className="space-y-6">
          <AnalysisSummary 
            summary={displayData.summary || {
              totalItemsFound: displayData.privilegedContent?.length || 0,
              highConfidenceItems: displayData.privilegedContent?.filter((item: any) => item.confidenceScore >= 0.8).length || 0,
              requiresManualReview: displayData.privilegedContent?.filter((item: any) => item.status === 'detected').length || 0,
              autoRedactable: 0
            }}
            metadata={displayData.analysisMetadata}
          />
          
          <PrivilegeContentList
            privilegedContent={displayData.privilegedContent || []}
            onReview={reviewContent}
            onRedact={applyRedaction}
            onBulkRedact={applyBulkRedaction}
          />
        </div>
      )}
    </div>
  );
};
```

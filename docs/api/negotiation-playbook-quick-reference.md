# Negotiation Playbook API - Quick Reference

## 🚀 Quick Start

### Generate Playbook

```bash
POST /api/documents/{documentId}/negotiation-playbook
Authorization: Bearer <token>
Content-Type: application/json

{
  "documentType": "CONTRACT",
  "focusAreas": ["termination", "liability", "payment"],
  "includeSimulations": true,
  "organizationPreferences": "Prefer shorter terms"
}
```

### Get Existing Playbook

```bash
GET /api/documents/{documentId}/negotiation-playbook
Authorization: Bearer <token>
```

## 📋 Request Parameters

### Document Types

- `CONTRACT` - Legal contracts
- `AGREEMENT` - Service agreements
- `POLICY` - Company policies
- `TERMS` - Terms of service
- `OTHER` - Other document types

### Common Focus Areas

- `termination` - Contract termination clauses
- `liability` - Liability and indemnification
- `payment` - Payment terms and conditions
- `intellectual_property` - IP rights and licensing
- `confidentiality` - Non-disclosure provisions
- `dispute_resolution` - Arbitration and legal remedies
- `force_majeure` - Force majeure clauses
- `warranties` - Warranties and representations

## 📊 Response Structure

```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "documentId": "uuid",
    "executiveSummary": "string",
    "keyNegotiationPoints": [
      {
        "title": "string",
        "description": "string",
        "currentPosition": "string",
        "recommendedPosition": "string",
        "priority": "HIGH|MEDIUM|LOW|CRITICAL",
        "rationale": "string"
      }
    ],
    "strategicRecommendations": [
      {
        "category": "TIMING|APPROACH|LEVERAGE|FALLBACK|PREPARATION",
        "recommendation": "string",
        "reasoning": "string"
      }
    ],
    "riskAssessment": {
      "overallRiskLevel": "HIGH|MEDIUM|LOW|CRITICAL",
      "riskFactors": [
        {
          "factor": "string",
          "severity": "HIGH|MEDIUM|LOW|CRITICAL",
          "mitigation": "string"
        }
      ]
    },
    "negotiationSimulations": [
      {
        "scenario": "string",
        "response": "string",
        "expectedOutcome": "string"
      }
    ],
    "createdAt": "ISO date",
    "updatedAt": "ISO date"
  }
}
```

## ⚠️ Error Codes

| Code                    | Status | Description                |
| ----------------------- | ------ | -------------------------- |
| `VALIDATION_ERROR`      | 400    | Invalid request parameters |
| `FEATURE_NOT_AVAILABLE` | 403    | PRO subscription required  |
| `DOCUMENT_NOT_FOUND`    | 404    | Document doesn't exist     |
| `PLAYBOOK_NOT_FOUND`    | 404    | No playbook for document   |
| `AI_SERVICE_ERROR`      | 500    | AI generation failed       |

## 🔧 Frontend Integration

### React Component Example

```tsx
import React, { useState } from 'react';

const NegotiationPlaybook: React.FC<{ documentId: string }> = ({
  documentId,
}) => {
  const [playbook, setPlaybook] = useState(null);
  const [loading, setLoading] = useState(false);

  const generatePlaybook = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/documents/${documentId}/negotiation-playbook`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            documentType: 'CONTRACT',
            focusAreas: ['termination', 'liability'],
            includeSimulations: true,
          }),
        },
      );

      const result = await response.json();
      if (result.success) {
        setPlaybook(result.data);
      }
    } catch (error) {
      console.error('Failed to generate playbook:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <button onClick={generatePlaybook} disabled={loading}>
        {loading ? 'Generating...' : 'Generate Playbook'}
      </button>

      {playbook && (
        <div>
          <h3>Executive Summary</h3>
          <p>{playbook.executiveSummary}</p>

          <h3>Key Negotiation Points</h3>
          {playbook.keyNegotiationPoints.map((point, index) => (
            <div
              key={index}
              className={`priority-${point.priority.toLowerCase()}`}
            >
              <h4>{point.title}</h4>
              <p>{point.description}</p>
              <p>
                <strong>Recommended:</strong> {point.recommendedPosition}
              </p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
```

### TypeScript Interfaces

```typescript
interface NegotiationPlaybook {
  id: string;
  documentId: string;
  executiveSummary: string;
  keyNegotiationPoints: NegotiationPoint[];
  strategicRecommendations: StrategicRecommendation[];
  riskAssessment: RiskAssessment;
  negotiationSimulations: NegotiationSimulation[];
  createdAt: string;
  updatedAt: string;
}

interface GeneratePlaybookRequest {
  documentType: 'CONTRACT' | 'AGREEMENT' | 'POLICY' | 'TERMS' | 'OTHER';
  focusAreas?: string[];
  includeSimulations?: boolean;
  organizationPreferences?: string;
}
```

## 🎯 Best Practices

### 1. Check for Existing Playbook First

```typescript
const loadOrGeneratePlaybook = async (documentId: string) => {
  // Try to get existing playbook first
  let playbook = await getPlaybook(documentId);

  if (!playbook) {
    // Generate new playbook if none exists
    playbook = await generatePlaybook(documentId, options);
  }

  return playbook;
};
```

### 2. Handle Loading States

```tsx
{
  loading && (
    <div>
      Generating negotiation playbook... This may take up to 30 seconds.
    </div>
  );
}
```

### 3. Error Handling

```typescript
const handleError = (error: any) => {
  if (error.code === 'FEATURE_NOT_AVAILABLE') {
    // Show upgrade prompt
    showUpgradeModal();
  } else if (error.code === 'DOCUMENT_NOT_FOUND') {
    // Redirect to documents list
    navigate('/documents');
  } else {
    // Show generic error
    showErrorToast(error.message);
  }
};
```

### 4. Priority Styling

```css
.priority-critical {
  border-left: 4px solid #dc2626;
}
.priority-high {
  border-left: 4px solid #ea580c;
}
.priority-medium {
  border-left: 4px solid #ca8a04;
}
.priority-low {
  border-left: 4px solid #65a30d;
}
```

## 📈 Performance Tips

- Cache playbooks locally to avoid unnecessary API calls
- Show loading indicators for generation (15-30 seconds)
- Implement retry logic for failed generations
- Use pagination for large negotiation point lists
- Debounce regeneration requests

## 🔒 Security Notes

- Always validate JWT tokens are present
- Handle 403 errors gracefully (subscription required)
- Don't expose sensitive document content in error logs
- Implement proper CORS headers for cross-origin requests

## 📞 Support

For integration help:

- Check the full API documentation: `/docs/api/negotiation-playbook.md`
- Review error response codes and messages
- Contact the backend team for technical issues

# Sample Negotiation Playbooks API Documentation

## Overview

The Sample Negotiation Playbooks feature provides pre-built, expert-crafted negotiation strategies for common contract types. These templates serve as educational resources and starting points for users to understand effective negotiation tactics across different industries and difficulty levels.

**Feature Availability**: All subscription tiers (accessible to all users)

## Base URL

```
https://api.docgic.com/api/sample-playbooks
```

## Authentication

All endpoints require JWT authentication via the `Authorization` header:

```
Authorization: Bearer <jwt_token>
```

---

## Endpoints

### 1. Get All Sample Playbooks

**GET** `/api/sample-playbooks`

Retrieve all available sample negotiation playbooks with optional filtering.

#### Query Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `contractType` | string | No | Filter by contract type |
| `industry` | string | No | Filter by industry |
| `difficulty` | string | No | Filter by difficulty level |
| `tags` | string | No | Filter by tags (comma-separated) |

#### Contract Types
- `service_agreement`
- `employment_contract`
- `nda`
- `software_license`
- `consulting_agreement`
- `partnership_agreement`
- `real_estate`
- `vendor_agreement`

#### Industries
- `technology`
- `healthcare`
- `finance`
- `real_estate`
- `consulting`
- `manufacturing`
- `retail`
- `general`

#### Difficulty Levels
- `beginner` - Basic negotiation strategies
- `intermediate` - Moderate complexity scenarios
- `expert` - Advanced negotiation tactics

#### Example Request

```bash
curl -X GET "https://api.docgic.com/api/sample-playbooks?contractType=service_agreement&difficulty=beginner" \
  -H "Authorization: Bearer <jwt_token>"
```

#### Example Response

```json
[
  {
    "_id": "507f1f77bcf86cd799439011",
    "templateName": "Service Agreement - Basic Negotiation",
    "templateDescription": "Essential negotiation strategies for standard service agreements",
    "contractType": "service_agreement",
    "industry": "general",
    "difficulty": "beginner",
    "tags": ["service", "basic", "beginner"],
    "usageCount": 42,
    "strategies": [
      {
        "section": "Payment Terms",
        "recommendations": [
          "Negotiate for shorter payment cycles (Net 15 instead of Net 30)",
          "Include late payment penalties (1.5% per month)",
          "Request partial upfront payment for large projects"
        ],
        "riskLevel": "low",
        "priority": 1,
        "alternativeLanguage": "Payment shall be due within fifteen (15) days of invoice date. Late payments shall incur a penalty of 1.5% per month."
      }
    ],
    "overallAssessment": "Service agreements require clear scope definition and favorable payment terms. Focus on protecting your time and ensuring prompt payment.",
    "keyLeveragePoints": [
      "Specialized expertise and skills",
      "Timeline flexibility",
      "Quality of deliverables"
    ],
    "dealBreakers": [
      "Unlimited liability exposure",
      "Work-for-hire clauses for proprietary methods",
      "Non-compete restrictions that limit future business"
    ]
  }
]
```

### 2. Get Specific Sample Playbook

**GET** `/api/sample-playbooks/:playbookId`

Retrieve detailed information about a specific sample playbook.

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `playbookId` | string | Yes | The ID of the sample playbook |

#### Example Request

```bash
curl -X GET "https://api.docgic.com/api/sample-playbooks/507f1f77bcf86cd799439011" \
  -H "Authorization: Bearer <jwt_token>"
```

### 3. Clone Sample Playbook

**POST** `/api/sample-playbooks/:playbookId/clone/:documentId`

Create a copy of a sample playbook customized for a specific document.

#### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `playbookId` | string | Yes | The ID of the sample playbook to clone |
| `documentId` | string | Yes | The ID of the document to apply the playbook to |

#### Example Request

```bash
curl -X POST "https://api.docgic.com/api/sample-playbooks/507f1f77bcf86cd799439011/clone/507f1f77bcf86cd799439012" \
  -H "Authorization: Bearer <jwt_token>"
```

#### Example Response

```json
{
  "documentId": "507f1f77bcf86cd799439012",
  "strategies": [...],
  "overallAssessment": "...",
  "keyLeveragePoints": [...],
  "dealBreakers": [...],
  "timestamp": "2025-01-23T15:30:00.000Z",
  "organizationId": "org123",
  "userId": "user456",
  "isTemplate": false,
  "contractType": "service_agreement",
  "industry": "general"
}
```

### 4. Get Playbook Statistics

**GET** `/api/sample-playbooks/stats/overview`

Retrieve statistics about sample playbooks usage and distribution.

#### Example Request

```bash
curl -X GET "https://api.docgic.com/api/sample-playbooks/stats/overview" \
  -H "Authorization: Bearer <jwt_token>"
```

#### Example Response

```json
{
  "totalSamples": 8,
  "totalUserPlaybooks": 156,
  "popularSamples": [
    {
      "templateName": "Service Agreement - Basic Negotiation",
      "usageCount": 42
    },
    {
      "templateName": "Employment Contract - Professional Level",
      "usageCount": 38
    }
  ],
  "contractTypeDistribution": [
    {
      "contractType": "service_agreement",
      "count": 2
    },
    {
      "contractType": "employment_contract",
      "count": 1
    }
  ]
}
```

### 5. Seed Sample Playbooks (Admin)

**POST** `/api/sample-playbooks/seed`

Populate the database with sample negotiation playbooks. This endpoint is typically used for initial setup.

#### Example Request

```bash
curl -X POST "https://api.docgic.com/api/sample-playbooks/seed" \
  -H "Authorization: Bearer <jwt_token>"
```

#### Example Response

```json
{
  "success": true,
  "message": "Sample negotiation playbooks seeded successfully"
}
```

---

## Available Sample Playbooks

### 1. Service Agreement - Basic Negotiation
- **Difficulty**: Beginner
- **Industry**: General
- **Focus**: Payment terms, scope of work
- **Key Strategies**: Payment cycles, change orders, liability protection

### 2. Employment Contract - Professional Level
- **Difficulty**: Intermediate
- **Industry**: General
- **Focus**: Compensation, termination, benefits
- **Key Strategies**: Total compensation negotiation, severance protection

### 3. Non-Disclosure Agreement - Essentials
- **Difficulty**: Beginner
- **Industry**: General
- **Focus**: Confidentiality terms, mutual protection
- **Key Strategies**: Balanced confidentiality, reasonable time limits

### 4. Software License - Advanced Negotiations
- **Difficulty**: Expert
- **Industry**: Technology
- **Focus**: Enterprise licensing, SLAs
- **Key Strategies**: Scalability, performance guarantees, service credits

### 5. Consulting Agreement - Professional Services
- **Difficulty**: Intermediate
- **Industry**: Consulting
- **Focus**: IP rights, rate structures
- **Key Strategies**: IP protection, rate escalation, expense reimbursement

### 6. Vendor/Supplier Agreement - Supply Chain
- **Difficulty**: Intermediate
- **Industry**: Manufacturing
- **Focus**: Pricing, quality standards
- **Key Strategies**: Volume discounts, quality metrics, warranty terms

---

## Error Responses

### 404 Not Found

```json
{
  "statusCode": 404,
  "message": "Sample playbook with ID 507f1f77bcf86cd799439011 not found",
  "error": "Not Found"
}
```

### 401 Unauthorized

```json
{
  "statusCode": 401,
  "message": "Unauthorized",
  "error": "Unauthorized"
}
```

---

## Usage Examples

### Educational Learning Path

1. **Start with Beginner**: Begin with basic service agreement or NDA playbooks
2. **Progress to Intermediate**: Move to employment contracts or consulting agreements
3. **Advance to Expert**: Tackle complex software licensing negotiations

### Practical Application

1. **Browse Available Templates**: Use the GET endpoint to explore available playbooks
2. **Select Relevant Playbook**: Choose based on your contract type and experience level
3. **Clone for Your Document**: Use the clone endpoint to apply strategies to your specific document
4. **Customize and Negotiate**: Adapt the strategies to your specific situation

### Analytics and Insights

- Use the stats endpoint to see which playbooks are most popular
- Track usage patterns to understand common negotiation scenarios
- Identify gaps in your negotiation knowledge based on available templates

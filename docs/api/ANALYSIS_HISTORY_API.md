# Analysis History API Documentation

This document provides comprehensive documentation for the Analysis History API endpoints in the Legal Document Analyzer. These endpoints allow tracking, comparing, and visualizing the evolution of document analyses over time.

## Table of Contents

1. [Overview](#overview)
2. [Authentication](#authentication)
3. [Subscription Requirements](#subscription-requirements)
4. [API Endpoints](#api-endpoints)
   - [Get All Analysis Results](#get-all-analysis-results)
   - [Get Analysis Evolution Metrics](#get-analysis-evolution-metrics)
   - [Compare Analysis Results (POST)](#compare-analysis-results-post)
   - [Compare Analysis Results (GET)](#compare-analysis-results-get)
   - [Get Analysis History Analytics](#get-analysis-history-analytics)
5. [Data Models](#data-models)
6. [Example Usage](#example-usage)
7. [<PERSON>rror Handling](#error-handling)

## Overview

The Analysis History API enables users to:

- Retrieve all analysis results for a document
- Track the evolution of analysis results over time
- Compare different analysis results to identify changes and improvements

This feature is part of the Document Timeline functionality, which provides a comprehensive view of how documents and their analyses change over time.

## Authentication

All API endpoints require authentication using JWT tokens. Include the token in the Authorization header of your requests:

```bash
Authorization: Bearer <your_jwt_token>
```

## Subscription Requirements

These endpoints require at least a `basic_analysis` subscription tier. Users with higher subscription tiers (PROFESSIONAL, ENTERPRISE) have access to additional features and higher rate limits.

## API Endpoints

### Get All Analysis Results

Retrieves all analysis results for a document or just the latest one.

**Endpoint:** `GET /api/documents/:id/analysis`

**URL Parameters:**

- `id` (required): The ID of the document

**Query Parameters:**

- `latest` (optional): If set to 'true', only returns the latest analysis result

**Response:**

- Status: 200 OK
- Body: Array of analysis results or a single result object if `latest=true`

**Example Response (latest=true):**

```json
{
  "analysisId": "60f1e5b3c2e9a83f98b4a123",
  "result": {
    "summary": "This contract outlines terms for software development services...",
    "keyTerms": ["indemnification", "liability", "termination"],
    "riskAssessment": {
      "level": "medium",
      "factors": ["ambiguous termination clause", "broad liability provisions"]
    },
    "enrichedCitations": [
      {
        "citation": "Smith v. Jones, 123 F.3d 456 (9th Cir. 2020)",
        "context": "As referenced in section 12.3 of the agreement"
      }
    ]
  }
}
```

### Get Analysis Evolution Metrics

Retrieves metrics showing how analysis results have evolved over time for a document.

**Endpoint:** `GET /api/documents/:id/analysis/evolution`

**URL Parameters:**

- `id` (required): The ID of the document

**Response:**

- Status: 200 OK
- Body: Evolution metrics object

**Example Response:**

```json
{
  "documentId": "60f1e5b3c2e9a83f98b4a456",
  "totalAnalyses": 5,
  "analysisDates": [
    "2025-01-15T10:30:00.000Z",
    "2025-02-03T14:22:15.000Z",
    "2025-03-10T09:45:33.000Z",
    "2025-03-28T16:12:45.000Z",
    "2025-04-19T11:05:22.000Z"
  ],
  "processingTimes": [2340, 2120, 3450, 1890, 2780],
  "contentLengths": [15240, 16780, 18920, 17650, 19340],
  "analysisIds": [
    "60f1e5b3c2e9a83f98b4a789",
    "61a2c4d5e6f7g8h9i0j1k2l3",
    "62b3c4d5e6f7g8h9i0j1k2l3",
    "63c4d5e6f7g8h9i0j1k2l3m4",
    "64d5e6f7g8h9i0j1k2l3m4n5"
  ]
}
```

### Compare Analysis Results (POST)

Compares multiple analysis results to identify differences and similarities.

**Endpoint:** `POST /api/documents/:id/analysis/compare`

**URL Parameters:**

- `id` (required): The ID of the document

**Request Body:**

```json
{
  "analysisIds": ["60f1e5b3c2e9a83f98b4a789", "64d5e6f7g8h9i0j1k2l3m4n5"],
  "comparisonType": "detailed"
}
```

**Request Parameters:**

- `analysisIds` (optional): Array of analysis IDs to compare. If not provided, all analyses for the document will be used.
- `comparisonType` (required): Type of comparison to perform. Options:
  - `basic`: Simple comparison of key metrics
  - `detailed`: In-depth comparison of all analysis components
  - `summary`: High-level summary of differences

**Response:**

- Status: 200 OK
- Body: Comparison results object

**Example Response:**

```json
{
  "documentId": "60f1e5b3c2e9a83f98b4a456",
  "analysisIds": ["60f1e5b3c2e9a83f98b4a789", "64d5e6f7g8h9i0j1k2l3m4n5"],
  "comparisonType": "detailed",
  "results": {
    "summary": {
      "differences": {
        "riskAssessment": {
          "from": {
            "level": "high",
            "factors": [
              "ambiguous termination clause",
              "broad liability provisions"
            ]
          },
          "to": {
            "level": "medium",
            "factors": [
              "clarified termination clause",
              "broad liability provisions"
            ]
          }
        },
        "keyTerms": {
          "added": ["arbitration", "confidentiality"],
          "removed": ["penalty"]
        }
      },
      "similarities": {
        "documentStructure": "90% similar",
        "legalConcepts": ["indemnification", "liability", "termination"]
      },
      "improvement": {
        "score": 0.35,
        "areas": ["risk assessment", "clarity", "completeness"]
      }
    },
    "details": {
      // Detailed comparison data
    }
  }
}
```

### Compare Analysis Results (GET)

Compares two analysis results using query parameters instead of a request body. This RESTful approach makes it easier to share comparison URLs and integrate with frontend applications.

**Endpoint:** `GET /api/documents/:id/analysis/compare`

**URL Parameters:**

- `id` (required): The ID of the document

**Query Parameters:**

- `firstAnalysisId` (optional): ID of the first analysis to compare. Use "latest" for the most recent analysis.
- `secondAnalysisId` (optional): ID of the second analysis to compare. Use "earliest" for the oldest analysis.
- `comparisonType` (optional): Type of comparison to perform (detailed, summary, or differences). Default: "detailed"

**Response:**

- Status: 200 OK
- Body: Comparison results object (same format as the POST endpoint)

**Example Request:**

```http
GET /api/documents/56fa2eaa-95c1-4555-9eb9-7f49479670f0/analysis/compare?firstAnalysisId=6803e5b27be70b9d50837405&secondAnalysisId=6803e5b27be70b9d50837406&comparisonType=detailed
```

**Example Response:**

```json
{
  "documentId": "56fa2eaa-95c1-4555-9eb9-7f49479670f0",
  "analysisIds": ["6803e5b27be70b9d50837405", "6803e5b27be70b9d50837406"],
  "comparisonType": "detailed",
  "results": {
    "summary": {
      "differences": {
        "riskAssessment": {
          "from": {
            "level": "high",
            "score": 0.3
          },
          "to": {
            "level": "medium-high",
            "score": 0.4
          }
        },
        "keyTerms": {
          "added": ["arbitration"],
          "removed": []
        }
      },
      "similarities": {
        "documentStructure": "95% similar",
        "legalConcepts": ["indemnification", "liability", "termination", "confidentiality"]
      },
      "improvement": {
        "score": 0.15,
        "areas": ["risk assessment", "completeness"]
      }
    },
    "details": {
      // Detailed comparison data
    }
  }
}
```

### Get Analysis History Analytics

Retrieves visualization-ready analytics data for document analysis history.

**Endpoint:** `GET /api/analytics/documents/:documentId/analysis-history`

**URL Parameters:**

- `documentId` (required): The ID of the document

**Query Parameters:**

- `startDate` (optional): Start date for the time range (ISO format)
- `endDate` (optional): End date for the time range (ISO format)
- `granularity` (optional): Time granularity for aggregation (day, week, month)
- `includeTimeSeriesData` (optional): Whether to include time series data (default: true)

**Response:**

- Status: 200 OK
- Body: Analysis history analytics data in visualization-ready format

**Example Response:**

```json
{
  "documentId": "60f1e5b3c2e9a83f98b4a456",
  "organizationId": "org123456",
  "timeRange": {
    "start": "2025-01-15T00:00:00.000Z",
    "end": "2025-04-19T00:00:00.000Z"
  },
  "analysisHistory": {
    "totalAnalyses": 5,
    "analysisDates": [
      "2025-01-15T10:30:00.000Z",
      "2025-02-03T14:22:15.000Z",
      "2025-03-10T09:45:33.000Z",
      "2025-03-28T16:12:45.000Z",
      "2025-04-19T11:05:22.000Z"
    ],
    "processingTimes": [2340, 2120, 3450, 1890, 2780],
    "contentLengths": [15240, 16780, 18920, 17650, 19340]
  },
  "evolutionMetrics": {
    "riskAssessmentTrend": [
      {
        "date": "2025-01-15T10:30:00.000Z",
        "level": "high",
        "score": 0.3
      },
      {
        "date": "2025-02-03T14:22:15.000Z",
        "level": "medium-high",
        "score": 0.4
      },
      {
        "date": "2025-03-10T09:45:33.000Z",
        "level": "medium",
        "score": 0.5
      },
      {
        "date": "2025-03-28T16:12:45.000Z",
        "level": "medium-low",
        "score": 0.6
      },
      {
        "date": "2025-04-19T11:05:22.000Z",
        "level": "low",
        "score": 0.7
      }
    ],
    "keyTermsEvolution": [
      {
        "date": "2025-01-15T10:30:00.000Z",
        "terms": [
          "indemnification",
          "liability",
          "termination",
          "confidentiality"
        ],
        "count": 4
      },
      {
        "date": "2025-04-19T11:05:22.000Z",
        "terms": [
          "indemnification",
          "liability",
          "termination",
          "confidentiality",
          "arbitration",
          "intellectual property"
        ],
        "count": 6
      }
    ],
    "citationAccuracyTrend": [
      {
        "date": "2025-01-15T10:30:00.000Z",
        "accuracy": 0.75,
        "totalCitations": 4
      },
      {
        "date": "2025-04-19T11:05:22.000Z",
        "accuracy": 0.95,
        "totalCitations": 6
      }
    ],
    "completenessScoreTrend": [
      {
        "date": "2025-01-15T10:30:00.000Z",
        "score": 0.67
      },
      {
        "date": "2025-04-19T11:05:22.000Z",
        "score": 0.98
      }
    ]
  },
  "comparisonData": {
    "firstAnalysis": {
      "date": "2025-01-15T10:30:00.000Z",
      "riskLevel": "high",
      "keyTermsCount": 4,
      "citationAccuracy": 0.75,
      "completenessScore": 0.67
    },
    "latestAnalysis": {
      "date": "2025-04-19T11:05:22.000Z",
      "riskLevel": "low",
      "keyTermsCount": 6,
      "citationAccuracy": 0.95,
      "completenessScore": 0.98
    },
    "improvement": {
      "riskLevelChange": 4,
      "keyTermsAdded": 2,
      "citationAccuracyImprovement": 0.2,
      "completenessImprovement": 0.31
    }
  },
  "lastUpdated": "2025-04-19T17:47:37.000Z"
}
```

## Data Models

### AnalysisComparisonRequestDto

```typescript
export class AnalysisComparisonRequestDto {
  @IsArray()
  @IsOptional()
  analysisIds?: string[];

  @IsString()
  @IsIn(['basic', 'detailed', 'summary'])
  comparisonType: string = 'detailed';
}
```

### AnalysisEvolutionMetricsDto

```typescript
export class AnalysisEvolutionMetricsDto {
  @IsString()
  documentId: string;

  @IsNumber()
  totalAnalyses: number;

  @IsArray()
  @IsDateString({}, { each: true })
  analysisDates: string[];

  @IsArray()
  @IsNumber({}, { each: true })
  processingTimes: number[];

  @IsArray()
  @IsNumber({}, { each: true })
  contentLengths: number[];

  @IsArray()
  @IsString({ each: true })
  analysisIds: string[];
}
```

## Example Usage

### JavaScript/TypeScript Example

```typescript
import axios from 'axios';

const API_BASE_URL = 'http://localhost:4000/api';
const JWT_TOKEN = 'your_jwt_token';

// Get all analysis results for a document
async function getAnalysisResults(documentId) {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/documents/${documentId}/analysis`,
      {
        headers: {
          Authorization: `Bearer ${JWT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching analysis results:', error);
    throw error;
  }
}

// Compare two analysis results
async function compareAnalysisResults(documentId, firstAnalysisId, secondAnalysisId) {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/documents/${documentId}/analysis/compare`,
      {
        params: {
          firstAnalysisId,
          secondAnalysisId,
          comparisonType: 'detailed'
        },
        headers: {
          Authorization: `Bearer ${JWT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  } catch (error) {
    console.error('Error comparing analysis results:', error);
    throw error;
  }
}

// Get analysis history analytics for visualization
async function visualizeAnalysisHistory(documentId) {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/analytics/documents/${documentId}/analysis-history`,
      {
        params: {
          startDate: '2025-01-01T00:00:00.000Z',
          endDate: '2025-04-20T00:00:00.000Z'
        },
        headers: {
          Authorization: `Bearer ${JWT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching analysis history analytics:', error);
    throw error;
  }
}
```

## Error Handling

The API uses standard HTTP status codes to indicate success or failure:

- **200 OK**: Request successful
- **400 Bad Request**: Invalid request parameters
- **401 Unauthorized**: Missing or invalid authentication
- **403 Forbidden**: Insufficient permissions or subscription tier
- **404 Not Found**: Document or analysis not found
- **500 Internal Server Error**: Server-side error

Error responses include a standardized format:

```json
{
  "statusCode": 400,
  "message": "At least two analysis results are required for comparison",
  "error": "Bad Request",
  "details": null,
  "path": "/api/documents/56fa2eaa-95c1-4555-9eb9-7f49479670f0/analysis/compare",
  "timestamp": "2025-04-19T17:57:37.550Z"
}

# Document Timeline - Version API Documentation

## Overview

The Document Timeline - Version API provides endpoints for managing and retrieving document versions. This feature allows users to:

- Retrieve all versions of a document
- Retrieve a specific version of a document by version number
- Create new versions of a document

This API is part of the Document Timeline feature set outlined in the 2025 Feature Roadmap (Phase 2).

## API Endpoints

### Get All Document Versions

Retrieves a list of all versions for a specific document.

**Endpoint:** `GET /api/documents/:id/versions`

**URL Parameters:**

- `id` (required): The ID of the document

**Response:**

```json
[
  {
    "id": "version-uuid-1",
    "documentId": "document-uuid",
    "version": 2,
    "content": "Document content for version 2",
    "metadata": {
      "title": "Document Title",
      "sections": [
        {
          "title": "Section 1",
          "content": "Section content"
        }
      ]
    },
    "createdAt": "2025-04-19T16:22:09.017Z",
    "createdBy": "user-uuid"
  },
  {
    "id": "version-uuid-2",
    "documentId": "document-uuid",
    "version": 1,
    "content": "Original document content",
    "metadata": {
      "title": "Original Document Title"
    },
    "createdAt": "2025-04-18T14:30:00.000Z",
    "createdBy": "user-uuid"
  }
]
```

### Get Specific Document Version

Retrieves a specific version of a document by its version number.

**Endpoint:** `GET /api/documents/:id/versions/:versionNumber`

**URL Parameters:**

- `id` (required): The ID of the document
- `versionNumber` (required): The version number to retrieve

**Response:**

```json
{
  "id": "document-uuid-v2",
  "organizationId": "org-uuid",
  "filename": "document-filename.txt",
  "originalName": "original-filename.txt",
  "content": "Document content for version 2",
  "size": 1024,
  "uploadDate": "2025-04-18T14:30:00.000Z",
  "status": "completed",
  "metadata": {
    "versionNumber": 2,
    "createdAt": "2025-04-19T16:22:09.017Z",
    "createdBy": "user-uuid",
    "title": "Document Title",
    "sections": [
      {
        "title": "Section 1",
        "content": "Section content"
      }
    ]
  }
}
```

### Create Document Version

Creates a new version of an existing document.

**Endpoint:** `POST /api/documents/:id/versions`

**URL Parameters:**

- `id` (required): The ID of the document

**Request Body:**

```json
{
  "content": "Updated document content for the new version",
  "metadata": {
    "title": "Updated Document Title",
    "sections": [
      {
        "title": "Section 1",
        "content": "Updated section content"
      },
      {
        "title": "Section 2",
        "content": "New section content"
      }
    ]
  }
}
```

**Response:**

```json
{
  "status": "success",
  "message": "Document version created successfully",
  "data": {
    "documentId": "document-uuid",
    "version": {
      "id": "document-uuid",
      "organizationId": "org-uuid",
      "filename": "document-filename.txt",
      "originalName": "original-filename.txt",
      "content": "Updated document content for the new version",
      "size": 1024,
      "uploadDate": "2025-04-18T14:30:00.000Z",
      "status": "completed",
      "metadata": {
        "versionCount": 3,
        "lastVersionCreatedAt": "2025-04-19T17:30:00.000Z",
        "lastVersionCreatedBy": "user-uuid",
        "title": "Updated Document Title",
        "sections": [
          {
            "title": "Section 1",
            "content": "Updated section content"
          },
          {
            "title": "Section 2",
            "content": "New section content"
          }
        ]
      }
    }
  }
}
```

## Error Responses

### Document Not Found

```json
{
  "statusCode": 404,
  "message": "Document with ID {id} not found",
  "error": "Not Found"
}
```

### Version Not Found

```json
{
  "statusCode": 404,
  "message": "Version {versionNumber} of document {id} not found",
  "error": "Not Found"
}
```

### Invalid Version Number

```json
{
  "statusCode": 400,
  "message": "Version number must be a valid integer",
  "error": "Bad Request"
}
```

### Server Error

```json
{
  "statusCode": 500,
  "message": "Failed to retrieve document version: {error message}",
  "error": "Internal Server Error"
}
```

## Implementation Notes

### Version Numbering

- Version numbering starts at 1 for the current document
- When a new version is created, it gets the next sequential number
- The original document is always considered version 1

### Automatic Section Extraction

When creating a new document version, if sections are not explicitly provided in the metadata, the system will attempt to automatically extract sections from the document content using pattern recognition.

### Version Storage

Document versions are stored in a separate collection (`document_versions`) with references to the original document. This allows for efficient storage and retrieval of version history without bloating the main document record.

## Frontend Integration Examples

### Fetching and Displaying Version History

```typescript
// Example using React with Axios
import axios from 'axios';
import { useState, useEffect } from 'react';

const DocumentVersionHistory = ({ documentId }) => {
  const [versions, setVersions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchVersions = async () => {
      try {
        setLoading(true);
        const response = await axios.get(
          `/api/documents/${documentId}/versions`,
          {
            headers: {
              Authorization: `Bearer ${localStorage.getItem('token')}`,
            },
          },
        );
        setVersions(response.data);
        setLoading(false);
      } catch (err) {
        setError(
          err.response?.data?.message || 'Failed to fetch document versions',
        );
        setLoading(false);
      }
    };

    fetchVersions();
  }, [documentId]);

  if (loading) return <div>Loading version history...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="version-history">
      <h2>Document Version History</h2>
      <ul>
        {versions.map((version) => (
          <li key={version.id}>
            <div>Version {version.version}</div>
            <div>Created: {new Date(version.createdAt).toLocaleString()}</div>
            <div>By: {version.createdBy}</div>
            <button onClick={() => viewVersion(documentId, version.version)}>
              View this version
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
};

const viewVersion = (documentId, versionNumber) => {
  // Navigate to version view or open in modal
  window.location.href = `/documents/${documentId}/versions/${versionNumber}`;
};
```

### Creating a New Version

```typescript
// Example using React with Axios
import axios from 'axios';
import { useState } from 'react';

const CreateDocumentVersion = ({
  documentId,
  currentContent,
  onVersionCreated,
}) => {
  const [content, setContent] = useState(currentContent);
  const [title, setTitle] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      const response = await axios.post(
        `/api/documents/${documentId}/versions`,
        {
          content,
          metadata: {
            title,
          },
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${localStorage.getItem('token')}`,
          },
        },
      );

      setLoading(false);
      if (onVersionCreated) {
        onVersionCreated(response.data);
      }
    } catch (err) {
      setError(
        err.response?.data?.message || 'Failed to create document version',
      );
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <h2>Create New Version</h2>
      {error && <div className="error">{error}</div>}

      <div>
        <label>Title</label>
        <input
          type="text"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          placeholder="Version title"
        />
      </div>

      <div>
        <label>Content</label>
        <textarea
          value={content}
          onChange={(e) => setContent(e.target.value)}
          rows={20}
        />
      </div>

      <button type="submit" disabled={loading}>
        {loading ? 'Creating...' : 'Create New Version'}
      </button>
    </form>
  );
};
```

## Changelog

- **April 19, 2025**: Initial implementation of Document Timeline - Version API
  - Added endpoint to retrieve all document versions
  - Added endpoint to retrieve specific document version
  - Added endpoint to create new document version

# Documents API Documentation

## Overview

The Documents API provides endpoints for managing legal documents within the Legal Document Analyzer system. This includes uploading, retrieving, analyzing, and comparing documents with AI-powered features.

## Authentication and Security

All endpoints require authentication via JW<PERSON> token and are protected by:

- `JwtAuthGuard`: Validates user authentication
- `DocumentAccessGuard`: Enforces tenant isolation
- `SubscriptionGuard`: Verifies subscription features

## Base URL

```
/documents
```

## Endpoints

### Document Management

#### Upload Document

Uploads a new document to the system with metadata.

**Endpoint:** `POST /documents/upload`

**Subscription Required:** None

**Request:**

```
Content-Type: multipart/form-data
```

**Payload:**

```json
{
  "file": "[Binary file data]",
  "title": "Contract Agreement",
  "author": "<PERSON>"
}
```

**Response:**

```json
{
  "id": "6453f8a12b0321e8f9a0c4d2",
  "filename": "contract-agreement.pdf",
  "size": 245678,
  "uploadDate": "2025-05-01T08:15:30Z",
  "metadata": {
    "title": "Contract Agreement",
    "author": "<PERSON>",
    "pageCount": 15,
    "organizationId": "org_12345"
  },
  "fileUrl": "https://storage.example.com/documents/contract-agreement.pdf"
}
```

**Feature Description:**
This endpoint allows users to upload legal documents in PDF, DOCX, or TXT formats. The system stores the file in Cloudflare R2 storage, extracts basic metadata, and associates the document with the user's organization for tenant isolation. The document becomes available for analysis and processing.

---

#### Get All Documents

Retrieves a paginated list of all documents accessible to the user.

**Endpoint:** `GET /documents`

**Query Parameters:**

- `page`: Page number (1-based), default: 1
- `limit`: Number of items per page (1-100), default: 10
- `sortBy`: Field to sort by (e.g., 'uploadDate', 'filename'), default: 'uploadDate'
- `sortDirection`: Sort direction ('asc' or 'desc'), default: 'desc'

**Response:**

```json
{
  "items": [
    {
      "id": "19c7edd7-46fa-4be4-9206-a6dd8aa90ea5",
      "organizationId": "org123",
      "filename": "Contract-2025.pdf",
      "size": 245789,
      "uploadDate": "2025-05-01T10:15:30Z",
      "fileType": "pdf",
      "mimeType": "application/pdf",
      "metadata": {
        "title": "Service Agreement",
        "author": "Legal Department",
        "pageCount": 15,
        "organizationId": "org123"
      }
    },
    {
      "id": "7a9b2c3d-4e5f-6g7h-8i9j-0k1l2m3n4o5p",
      "organizationId": "org123",
      "filename": "NDA-2025.pdf",
      "size": 125400,
      "uploadDate": "2025-04-28T14:22:10Z",
      "fileType": "pdf",
      "mimeType": "application/pdf",
      "metadata": {
        "title": "Non-Disclosure Agreement",
        "author": "Legal Department",
        "pageCount": 8,
        "organizationId": "org123"
      }
    }
  ],
  "total": 42,
  "page": 1,
  "limit": 10,
  "totalPages": 5,
  "hasNextPage": true,
  "hasPreviousPage": false
}
```

**Feature Description:**
This endpoint returns a paginated list of documents accessible to the current user. The response includes basic document metadata and pagination information. For production environments, strict tenant isolation is enforced, ensuring users can only access documents within their organization or documents they own.

---

#### Get Document by ID

Retrieves a specific document by ID.

**Endpoint:** `GET /documents/:id`

**Subscription Required:** None

**Parameters:**

- `id`: Document ID

**Response:**

```json
{
  "id": "6453f8a12b0321e8f9a0c4d2",
  "organizationId": "org_12345",
  "filename": "contract-agreement.pdf",
  "originalName": "contract-agreement.pdf",
  "content": "This agreement is made on the 1st day of May, 2025...",
  "size": 245678,
  "uploadDate": "2025-05-01T08:15:30Z",
  "status": "processed",
  "metadata": {
    "title": "Contract Agreement",
    "author": "John Smith",
    "pageCount": 15,
    "organizationId": "org_12345"
  }
}
```

**Feature Description:**
This endpoint retrieves complete document information including content and metadata. It enforces strict tenant isolation with organization ID checks and records document access for audit purposes.

---

#### Render Document

Renders or downloads the original document file in its native format.

**Endpoint:** `GET /documents/:id/render`

**Subscription Required:** None

**Parameters:**

- `id`: Document ID

**Response:**

Binary file stream with appropriate Content-Type and Content-Disposition headers.

- For web-viewable formats (PDF, images, text files), the file will be rendered inline in the browser.
- For other formats (DOCX, XLSX, etc.), the browser will prompt for download.

**Example Usage:**

```
// In a browser
window.location.href = 'https://api.example.com/documents/6453f8a12b0321e8f9a0c4d2/render';

// In an iframe
<iframe src="https://api.example.com/documents/6453f8a12b0321e8f9a0c4d2/render" width="100%" height="600px"></iframe>

// As a download link
<a href="https://api.example.com/documents/6453f8a12b0321e8f9a0c4d2/render" download>Download Document</a>
```

**Feature Description:**
This endpoint streams the original document file exactly as it was uploaded, preserving all formatting and content. The Content-Type header is set based on the document's MIME type, and the Content-Disposition header is set to 'inline' for web-viewable formats and 'attachment' for other formats. This allows for seamless viewing of documents directly in the browser when possible.

---

#### Get Document Content

Retrieves only the content of a specific document.

**Endpoint:** `GET /documents/:id/content`

**Subscription Required:** None

**Parameters:**

- `id`: Document ID

**Response:**

```json
{
  "content": "This agreement is made on the 1st day of May, 2025..."
}
```

**Feature Description:**
This endpoint returns only the text content of a document, which is useful for applications that need just the document text without metadata. It enforces tenant isolation through DocumentAccessGuard.

---

#### Delete Document

Permanently deletes a document and all associated data.

**Endpoint:** `DELETE /documents/:id`

**Parameters:**
- `id`: Document ID

**Response:**
```json
{
  "status": "success",
  "message": "Document with ID 322b0682-1fd9-49ee-ac3d-15ac70e3f360 has been deleted successfully"
}
```

**Feature Description:**
This endpoint permanently deletes a document and all its associated data, including analysis results and version history. This operation cannot be undone. The endpoint enforces strict tenant isolation, ensuring that users can only delete documents within their organization or documents they own.

**Security Considerations:**
- Requires authentication via JWT token
- Enforces tenant isolation based on organization ID
- Admin users can delete any document
- Regular users can only delete documents within their organization or documents they own
- All deletion actions are logged for audit purposes

---

### Document Versioning

#### Get Document Versions

Retrieves all versions of a document.

**Endpoint:** `GET /documents/:id/versions`

**Subscription Required:** None

**Parameters:**

- `id`: Document ID

**Response:**

```json
[
  {
    "versionNumber": 1,
    "content": "This agreement is made on the 1st day of May, 2025...",
    "createdAt": "2025-05-01T08:15:30Z",
    "createdBy": "user_12345",
    "metadata": {
      "title": "Contract Agreement - Initial Version",
      "status": "draft"
    }
  },
  {
    "versionNumber": 2,
    "content": "This agreement is made on the 1st day of May, 2025... [with amendments]",
    "createdAt": "2025-05-02T10:30:15Z",
    "createdBy": "user_12345",
    "metadata": {
      "title": "Contract Agreement - Revised",
      "status": "final"
    }
  }
]
```

**Feature Description:**
This endpoint provides access to the version history of a document, allowing users to track changes over time. Each version includes the full content, creation timestamp, creator, and version-specific metadata.

---

#### Get Specific Document Version

Retrieves a specific version of a document.

**Endpoint:** `GET /documents/:id/versions/:versionNumber`

**Subscription Required:** `document_comparison`

**Parameters:**

- `id`: Document ID
- `versionNumber`: Version number to retrieve

**Response:**

```json
{
  "versionNumber": 2,
  "content": "This agreement is made on the 1st day of May, 2025... [with amendments]",
  "createdAt": "2025-05-02T10:30:15Z",
  "createdBy": "user_12345",
  "metadata": {
    "title": "Contract Agreement - Revised",
    "status": "final"
  }
}
```

**Feature Description:**
This endpoint retrieves a specific version of a document by its version number. This is particularly useful for document comparison and tracking changes between specific versions. Requires the document_comparison subscription feature.

---

#### Create Document Version

Creates a new version of an existing document.

**Endpoint:** `POST /documents/:id/versions`

**Subscription Required:** None

**Parameters:**

- `id`: Document ID

**Payload:**

```json
{
  "content": "This agreement is made on the 1st day of May, 2025... [with new amendments]",
  "metadata": {
    "title": "Contract Agreement - Final Version",
    "status": "final",
    "notes": "Approved by legal team"
  }
}
```

**Response:**

```json
{
  "status": "success",
  "message": "Document version created successfully",
  "data": {
    "documentId": "6453f8a12b0321e8f9a0c4d2",
    "version": {
      "versionNumber": 3,
      "createdAt": "2025-05-03T14:25:10Z",
      "createdBy": "user_12345"
    }
  }
}
```

**Feature Description:**
This endpoint allows users to create new versions of a document while preserving previous versions. This is essential for tracking document evolution and maintaining a complete history of changes. The system automatically assigns a sequential version number.

---

### Document Analysis

#### Analyze Document

Performs AI analysis on a document.

**Endpoint:** `POST /documents/:id/analyze`

**Subscription Required:** `basic_analysis`

**Parameters:**

- `id`: Document ID

**Payload:**

```json
{
  "documentType": "CONTRACT",
  "query": "Identify key obligations and termination clauses",
  "forceNew": true
}
```

**Response:**

```json
{
  "analysisId": "6453f8a12b0321e8f9a0c4e5",
  "result": {
    "title": "Contract Analysis",
    "documentType": "CONTRACT",
    "summary": "This is a service agreement between Company A and Company B...",
    "keyProvisions": [
      {
        "title": "Payment Terms",
        "content": "Payment due within 30 days of invoice",
        "location": "Section 4.2"
      },
      {
        "title": "Termination",
        "content": "Either party may terminate with 60 days notice",
        "location": "Section 9.1"
      }
    ],
    "parties": [
      {
        "name": "Company A",
        "role": "Service Provider"
      },
      {
        "name": "Company B",
        "role": "Client"
      }
    ],
    "effectiveDate": "2025-05-01",
    "expirationDate": "2026-05-01",
    "governingLaw": "State of New York",
    "enrichedCitations": [
      {
        "citation": "Smith v. Jones, 123 F.3d 456 (2d Cir. 2020)",
        "context": "As referenced in Section 11.2",
        "summary": "Case establishing standard for material breach in service contracts",
        "url": "https://example.com/case/smith-v-jones"
      }
    ]
  }
}
```

**Feature Description:**
This endpoint uses AI to analyze legal documents and extract structured information based on document type. It can identify key provisions, parties, dates, and other relevant information. The analysis is enriched with legal research through the LegalResearchOrchestratorService, which adds context to legal citations found in the document. Results are saved for future reference and comparison.

**Parameters Explained:**

- `documentType`: The type of document being analyzed (e.g., CONTRACT, AGREEMENT, OPINION)
- `query`: Optional specific query to focus the analysis on particular aspects
- `forceNew`: Optional boolean flag that, when set to true, forces the creation of a new analysis even if one already exists for this document. This is useful for creating multiple analyses of the same document with different parameters or after document updates.

---

#### Get Analysis Results

Retrieves analysis results for a document.

**Endpoint:** `GET /documents/:id/analysis`

**Subscription Required:** `basic_analysis`

**Parameters:**

- `id`: Document ID
- `latest` (optional): Set to "true" to retrieve only the latest analysis

**Response:**

```json
{
  "analysisId": "6453f8a12b0321e8f9a0c4e5",
  "result": {
    "title": "Contract Analysis",
    "documentType": "CONTRACT",
    "summary": "This is a service agreement between Company A and Company B...",
    "keyProvisions": [
      {
        "title": "Payment Terms",
        "content": "Payment due within 30 days of invoice",
        "location": "Section 4.2"
      },
      {
        "title": "Termination",
        "content": "Either party may terminate with 60 days notice",
        "location": "Section 9.1"
      }
    ],
    "parties": [
      {
        "name": "Company A",
        "role": "Service Provider"
      },
      {
        "name": "Company B",
        "role": "Client"
      }
    ],
    "effectiveDate": "2025-05-01",
    "expirationDate": "2026-05-01",
    "governingLaw": "State of New York",
    "enrichedCitations": [
      {
        "citation": "Smith v. Jones, 123 F.3d 456 (2d Cir. 2020)",
        "context": "As referenced in Section 11.2",
        "summary": "Case establishing standard for material breach in service contracts",
        "url": "https://example.com/case/smith-v-jones"
      }
    ]
  }
}
```

**Feature Description:**
This endpoint retrieves previously performed AI analyses of a document. Users can either get all analyses or only the most recent one. Each analysis includes structured information extracted from the document and enriched legal citations. This allows users to access insights without re-analyzing the document.

---

#### Get Analysis Evolution

Retrieves metrics showing how analysis has evolved over time.

**Endpoint:** `GET /documents/:id/analysis/evolution`

**Subscription Required:** `basic_analysis`

**Parameters:**

- `id`: Document ID

**Response:**

```json
{
  "documentId": "19c7edd7-46fa-4be4-9206-a6dd8aa90ea5",
  "analysisCount": 3,
  "firstAnalysisDate": "2025-05-01T08:01:46.877Z",
  "latestAnalysisDate": "2025-05-10T14:22:45Z",
  "metrics": {
    "riskScoreEvolution": [
      { "date": "2025-05-01T08:01:46.877Z", "score": 65 },
      { "date": "2025-05-05T09:30:15Z", "score": 45 },
      { "date": "2025-05-10T14:22:45Z", "score": 30 }
    ],
    "clauseCountEvolution": {
      "restrictive": [5, 4, 3],
      "favorable": [2, 3, 5]
    },
    "keyChanges": [
      {
        "field": "governingLaw",
        "from": "State of California",
        "to": "State of New York",
        "analysisId": "68132a6a12c5d587b7ef4797",
        "date": "2025-05-05T09:30:15Z"
      }
    ]
  },
  "totalAnalyses": 3,
  "analysisDates": [
    "2025-05-01T08:01:46.877Z",
    "2025-05-05T09:30:15Z",
    "2025-05-10T14:22:45Z"
  ],
  "processingTimes": [0, 1200, 980],
  "contentLengths": [509, 612, 645],
  "analysisIds": [
    "68132a6a12c5d587b7ef4797",
    "68132a6a12c5d587b7ef4798",
    "68132a6a12c5d587b7ef4799"
  ]
}
```

**Note:** For documents with only one analysis, the response will include only the basic metrics:

```json
{
  "documentId": "19c7edd7-46fa-4be4-9206-a6dd8aa90ea5",
  "totalAnalyses": 1,
  "analysisDates": [
    "2025-05-01T08:01:46.877Z"
  ],
  "processingTimes": [0],
  "contentLengths": [509],
  "analysisIds": [
    "68132a6a12c5d587b7ef4797"
  ]
}
```

**Feature Description:**
This endpoint retrieves metrics showing how document analysis has evolved over time. For documents with multiple analyses, it provides enhanced metrics including risk scores, clause counts, and key changes. This is particularly useful for tracking how document interpretations change across versions or after modifications.

---

#### Compare Analysis Results (POST)

Compares different analysis results for the same document.

**Endpoint:** `POST /documents/:id/analysis/compare`

**Subscription Required:** `basic_analysis`

**Parameters:**

- `id`: Document ID

**Payload:**

```json
{
  "analysisIds": ["6453f8a12b0321e8f9a0c4e5", "6453f8a12b0321e8f9a0c4e6"],
  "comparisonType": "detailed"
}
```

**Response:**

```json
{
  "documentId": "6453f8a12b0321e8f9a0c4d2",
  "analysisIds": ["6453f8a12b0321e8f9a0c4e5", "6453f8a12b0321e8f9a0c4e6"],
  "comparisonType": "detailed",
  "results": {
    "summary": "The document has undergone significant changes in risk profile and governing law",
    "changes": [
      {
        "field": "riskScore",
        "from": 65,
        "to": 45,
        "significance": "high",
        "explanation": "Risk reduced due to improved termination clauses"
      },
      {
        "field": "governingLaw",
        "from": "State of California",
        "to": "State of New York",
        "significance": "high",
        "explanation": "Change in jurisdiction may impact enforcement"
      }
    ],
    "addedClauses": [
      {
        "title": "Limitation of Liability",
        "content": "Neither party shall be liable for indirect damages",
        "significance": "high"
      }
    ],
    "removedClauses": [
      {
        "title": "Automatic Renewal",
        "content": "Contract renews automatically unless terminated",
        "significance": "medium"
      }
    ],
    "modifiedClauses": [
      {
        "title": "Payment Terms",
        "from": "Payment due within 30 days of invoice",
        "to": "Payment due within 45 days of invoice",
        "significance": "low"
      }
    ]
  }
}
```

**Feature Description:**
This endpoint compares different AI analyses of the same document, highlighting changes between versions. It can identify added, removed, and modified clauses, as well as changes in key metadata like risk scores or governing law. This is valuable for understanding how document revisions have impacted its legal implications.

---

#### Compare Analysis Results (GET)

Compares analysis results using query parameters.

**Endpoint:** `GET /documents/:id/analysis/compare`

**Subscription Required:** `basic_analysis`

**Parameters:**

- `id`: Document ID
- `firstAnalysisId` (optional): ID of first analysis (or "latest")
- `secondAnalysisId` (optional): ID of second analysis (or "earliest")
- `comparisonType` (optional): Type of comparison (detailed, summary, differences)

**Response:**

```json
{
  "documentId": "6453f8a12b0321e8f9a0c4d2",
  "analysisIds": ["6453f8a12b0321e8f9a0c4e5", "6453f8a12b0321e8f9a0c4e6"],
  "comparisonType": "detailed",
  "results": {
    "summary": "The document has undergone significant changes in risk profile and governing law",
    "changes": [
      {
        "field": "riskScore",
        "from": 65,
        "to": 45,
        "significance": "high",
        "explanation": "Risk reduced due to improved termination clauses"
      },
      {
        "field": "governingLaw",
        "from": "State of California",
        "to": "State of New York",
        "significance": "high",
        "explanation": "Change in jurisdiction may impact enforcement"
      }
    ],
    "addedClauses": [
      {
        "title": "Limitation of Liability",
        "content": "Neither party shall be liable for indirect damages",
        "significance": "high"
      }
    ],
    "removedClauses": [
      {
        "title": "Automatic Renewal",
        "content": "Contract renews automatically unless terminated",
        "significance": "medium"
      }
    ],
    "modifiedClauses": [
      {
        "title": "Payment Terms",
        "from": "Payment due within 30 days of invoice",
        "to": "Payment due within 45 days of invoice",
        "significance": "low"
      }
    ]
  }
}
```

**Feature Description:**
This endpoint provides the same comparison functionality as the POST version but using query parameters, making it easier to integrate with web interfaces. It allows for convenient comparison of the latest and earliest analyses, or any specific analyses by ID.

---

#### Analyze Multiple Documents

Analyzes multiple documents together with context.

**Endpoint:** `POST /documents/analyze-multiple`

**Subscription Required:** `advanced_analysis`

**Payload:**

```json
{
  "primaryDocumentId": "6453f8a12b0321e8f9a0c4d2",
  "relatedDocumentIds": [
    "6453f8a12b0321e8f9a0c4d3",
    "6453f8a12b0321e8f9a0c4d4"
  ],
  "documentType": "CONTRACT"
}
```

**Response:**

```json
{
  "primaryDocumentId": "6453f8a12b0321e8f9a0c4d2",
  "result": {
    "title": "Multi-Document Analysis",
    "summary": "Analysis of main contract with related amendments and schedules",
    "relationships": [
      {
        "documentId": "6453f8a12b0321e8f9a0c4d3",
        "relationship": "AMENDMENT",
        "impact": "Modifies payment terms in sections 4.1-4.3"
      },
      {
        "documentId": "6453f8a12b0321e8f9a0c4d4",
        "relationship": "SCHEDULE",
        "impact": "Defines service levels referenced in section 2.5"
      }
    ],
    "consolidatedProvisions": [
      {
        "title": "Payment Terms",
        "content": "Payment due within 45 days of invoice (as amended)",
        "sourceDocuments": [
          {
            "id": "6453f8a12b0321e8f9a0c4d2",
            "section": "4.2"
          },
          {
            "id": "6453f8a12b0321e8f9a0c4d3",
            "section": "1.3"
          }
        ]
      }
    ],
    "conflicts": [
      {
        "title": "Notice Period",
        "description": "Main agreement specifies 30 days, amendment specifies 45 days",
        "recommendation": "Amendment takes precedence as the later document"
      }
    ]
  }
}
```

**Feature Description:**
This endpoint analyzes multiple related documents together, understanding their relationships and providing a consolidated view. It's particularly useful for complex legal arrangements where provisions are spread across multiple documents (e.g., main agreements, amendments, schedules). The system identifies relationships, consolidates provisions, and highlights conflicts between documents.

---

#### Compare Document Sections

Compares specific sections across multiple documents.

**Endpoint:** `POST /documents/compare-sections`

**Subscription Required:** `advanced_analysis`

**Payload:**

```json
{
  "documentSections": {
    "6453f8a12b0321e8f9a0c4d2": [
      {
        "title": "Confidentiality",
        "content": "Each party shall maintain the confidentiality of all proprietary information...",
        "sectionId": "section_5"
      }
    ],
    "6453f8a12b0321e8f9a0c4d3": [
      {
        "title": "Confidentiality",
        "content": "Both parties agree to keep confidential all business information...",
        "sectionId": "section_8"
      }
    ]
  },
  "comparisonType": "both"
}
```

**Response:**

```json
{
  "summary": "Comparison of confidentiality clauses across 2 documents",
  "differences": {
    "scope": {
      "6453f8a12b0321e8f9a0c4d2": "Covers 'proprietary information'",
      "6453f8a12b0321e8f9a0c4d3": "Covers 'business information'"
    },
    "duration": {
      "6453f8a12b0321e8f9a0c4d2": "5 years after termination",
      "6453f8a12b0321e8f9a0c4d3": "3 years after termination"
    }
  },
  "similarities": [
    "Both require written consent for disclosure",
    "Both include standard exceptions for publicly available information"
  ],
  "recommendation": "The first document provides stronger protection with broader scope and longer duration"
}
```

**Feature Description:**
This endpoint allows for targeted comparison of specific sections across different documents. Users can select particular clauses or sections from multiple documents and receive a detailed comparison of their similarities, differences, and relative strengths. This is valuable for comparing how similar provisions are handled across different agreements.

---

### Asynchronous Processing

#### Queue Document Processing

Queues a document for asynchronous processing.

**Endpoint:** `POST /documents/:documentId/process-async`

**Subscription Required:** None

**Parameters:**

- `documentId`: Document ID

**Payload:**

```json
{
  "priority": "high",
  "extractMetadata": true,
  "generateSummary": true
}
```

**Response:**

```json
{
  "jobId": "job_6453f8a12b0321e8f9a0c4f1",
  "documentId": "6453f8a12b0321e8f9a0c4d2",
  "status": "queued"
}
```

**Feature Description:**
This endpoint allows for asynchronous processing of documents, which is useful for large documents or batch operations. It places the document in a processing queue with specified options and priority, returning a job ID that can be used to check the processing status.

---

#### Get Job Status

Retrieves the status of a processing job.

**Endpoint:** `GET /documents/jobs/:jobId`

**Subscription Required:** None

**Parameters:**

- `jobId`: Job ID

**Response:**

```json
{
  "id": "job_6453f8a12b0321e8f9a0c4f1",
  "status": "processing",
  "progress": 65,
  "documentId": "6453f8a12b0321e8f9a0c4d2",
  "failedReason": null,
  "attempts": 1
}
```

**Feature Description:**
This endpoint allows users to check the status of an asynchronous document processing job. It provides information about the job's progress, any failures, and the number of processing attempts. This is essential for monitoring long-running operations.

---

#### Get Document Jobs

Retrieves all jobs for a specific document.

**Endpoint:** `GET /documents/:documentId/jobs`

**Subscription Required:** None

**Parameters:**

- `documentId`: Document ID

**Response:**

```json
[
  {
    "id": "job_6453f8a12b0321e8f9a0c4f1",
    "status": "completed",
    "progress": 100,
    "documentId": "6453f8a12b0321e8f9a0c4d2"
  },
  {
    "id": "job_6453f8a12b0321e8f9a0c4f2",
    "status": "processing",
    "progress": 30,
    "documentId": "6453f8a12b0321e8f9a0c4d2"
  }
]
```

**Feature Description:**
This endpoint returns all processing jobs associated with a specific document, allowing users to track the history of asynchronous operations performed on the document. This is useful for auditing and troubleshooting.

---

### Pattern Recognition

#### Detect Patterns

Detects specific legal patterns in a document based on a query.

**Endpoint:** `POST /documents/:id/patterns`

**Subscription Required:** Varies by pattern type

**Parameters:**

- `id`: Document ID

**Payload:**

```json
{
  "query": "Find all non-compete clauses and their durations"
}
```

**Response:**

```json
[
  {
    "patternType": "NON_COMPETE",
    "title": "Employee Non-Compete",
    "content": "Employee shall not engage in competitive business for a period of 2 years...",
    "location": "Section 8.3",
    "metadata": {
      "duration": "2 years",
      "scope": "Competitive business",
      "geography": "United States"
    },
    "riskAssessment": {
      "score": 65,
      "issues": [
        "Duration may be excessive in some jurisdictions",
        "Geographic scope is broad"
      ]
    }
  },
  {
    "patternType": "NON_COMPETE",
    "title": "Contractor Non-Compete",
    "content": "Contractor agrees not to provide similar services to competitors for 1 year...",
    "location": "Section 12.1",
    "metadata": {
      "duration": "1 year",
      "scope": "Similar services",
      "geography": "State of operation"
    },
    "riskAssessment": {
      "score": 40,
      "issues": []
    }
  }
]
```

**Feature Description:**
This endpoint uses AI to identify specific legal patterns within documents based on natural language queries. It can detect various types of clauses (non-compete, indemnification, limitation of liability, etc.) and extract structured information about them. This allows users to quickly locate and analyze specific types of provisions across their documents.

---

#### Classify Unknown Documents

Manually triggers classification of documents with unknown types.

**Endpoint:** `POST /documents/classify-unknown`

**Subscription Required:** None

**Response:**

```json
{
  "message": "Document classification job started successfully"
}
```

**Feature Description:**
This endpoint initiates a background job to classify documents that haven't been assigned a specific document type. The system will analyze the content of these documents and assign appropriate document types (e.g., contract, agreement, legal opinion) based on their structure and content. This is useful for organizing large document collections.

---

## Error Handling

The API uses standard HTTP status codes and returns detailed error messages:

- 400: Bad Request (invalid parameters)
- 401: Unauthorized (authentication issues)
- 403: Forbidden (tenant isolation violations)
- 404: Not Found (document or resource not found)
- 500: Internal Server Error (processing failures)

Example error response:

```json
{
  "statusCode": 404,
  "message": "Document with ID 6453f8a12b0321e8f9a0c4d2 not found",
  "error": "Not Found"
}
```

## Multi-tenant Isolation

All endpoints enforce strict tenant isolation through:

1. Organization ID tracking on all documents
2. Access checks based on the user's organization
3. Audit logging of document access
4. Special handling for admin users

This ensures that users can only access documents that belong to their organization.

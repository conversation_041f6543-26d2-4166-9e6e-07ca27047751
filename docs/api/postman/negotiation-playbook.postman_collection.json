{"info": {"name": "Negotiation Playbook API", "description": "API endpoints for generating and retrieving negotiation playbooks", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{jwt_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:4000/api", "type": "string"}, {"key": "document_id", "value": "43ee5001-9a05-4934-9005-d33537231cf6", "type": "string"}, {"key": "jwt_token", "value": "your_jwt_token_here", "type": "string"}], "item": [{"name": "Generate Negotiation Playbook", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"documentType\": \"CONTRACT\",\n  \"focusAreas\": [\"termination\", \"liability\", \"payment\"],\n  \"includeSimulations\": true,\n  \"organizationPreferences\": \"Prefer shorter contract terms and lower liability exposure\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/documents/{{document_id}}/negotiation-playbook", "host": ["{{base_url}}"], "path": ["documents", "{{document_id}}", "negotiation-playbook"]}, "description": "Generates a comprehensive negotiation playbook for the specified document using AI analysis."}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"documentType\": \"CONTRACT\",\n  \"focusAreas\": [\"termination\", \"liability\"],\n  \"includeSimulations\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/documents/{{document_id}}/negotiation-playbook", "host": ["{{base_url}}"], "path": ["documents", "{{document_id}}", "negotiation-playbook"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"playbook_uuid\",\n    \"documentId\": \"document_uuid\",\n    \"executiveSummary\": \"This contract presents moderate negotiation opportunities...\",\n    \"keyNegotiationPoints\": [\n      {\n        \"title\": \"Contract Termination Clause\",\n        \"description\": \"Current termination clause allows 90-day notice\",\n        \"currentPosition\": \"90-day notice period required\",\n        \"recommendedPosition\": \"Reduce to 30-day notice period\",\n        \"priority\": \"HIGH\",\n        \"rationale\": \"Shorter notice period provides more flexibility\"\n      }\n    ],\n    \"strategicRecommendations\": [\n      {\n        \"category\": \"TIMING\",\n        \"recommendation\": \"Negotiate termination clauses early\",\n        \"reasoning\": \"Sets favorable precedent\"\n      }\n    ],\n    \"riskAssessment\": {\n      \"overallRiskLevel\": \"MEDIUM\",\n      \"riskFactors\": [\n        {\n          \"factor\": \"Unlimited liability exposure\",\n          \"severity\": \"HIGH\",\n          \"mitigation\": \"Propose liability cap\"\n        }\n      ]\n    },\n    \"negotiationSimulations\": [\n      {\n        \"scenario\": \"Counterparty rejects liability cap\",\n        \"response\": \"Propose graduated liability structure\",\n        \"expectedOutcome\": \"Compromise at 2x contract value\"\n      }\n    ],\n    \"createdAt\": \"2025-01-23T13:20:59.000Z\",\n    \"updatedAt\": \"2025-01-23T13:20:59.000Z\"\n  }\n}"}]}, {"name": "Get Negotiation Playbook", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/documents/{{document_id}}/negotiation-playbook", "host": ["{{base_url}}"], "path": ["documents", "{{document_id}}", "negotiation-playbook"]}, "description": "Retrieves the existing negotiation playbook for the specified document."}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/documents/{{document_id}}/negotiation-playbook", "host": ["{{base_url}}"], "path": ["documents", "{{document_id}}", "negotiation-playbook"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"playbook_uuid\",\n    \"documentId\": \"document_uuid\",\n    \"executiveSummary\": \"This contract presents moderate negotiation opportunities...\",\n    \"keyNegotiationPoints\": [...],\n    \"strategicRecommendations\": [...],\n    \"riskAssessment\": {...},\n    \"negotiationSimulations\": [...],\n    \"createdAt\": \"2025-01-23T13:20:59.000Z\",\n    \"updatedAt\": \"2025-01-23T13:20:59.000Z\"\n  }\n}"}, {"name": "Playbook Not Found", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/documents/{{document_id}}/negotiation-playbook", "host": ["{{base_url}}"], "path": ["documents", "{{document_id}}", "negotiation-playbook"]}}, "status": "Not Found", "code": 404, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": false,\n  \"error\": {\n    \"code\": \"PLAYBOOK_NOT_FOUND\",\n    \"message\": \"No negotiation playbook found for this document\"\n  }\n}"}]}, {"name": "Generate Playbook - Contract with Simulations", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"documentType\": \"CONTRACT\",\n  \"focusAreas\": [\"intellectual_property\", \"confidentiality\", \"dispute_resolution\"],\n  \"includeSimulations\": true,\n  \"organizationPreferences\": \"Technology company focused on IP protection and rapid dispute resolution\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/documents/{{document_id}}/negotiation-playbook", "host": ["{{base_url}}"], "path": ["documents", "{{document_id}}", "negotiation-playbook"]}, "description": "Example request for a technology contract focusing on IP and confidentiality."}}, {"name": "Generate Playbook - Service Agreement", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"documentType\": \"AGREEMENT\",\n  \"focusAreas\": [\"payment\", \"termination\", \"warranties\"],\n  \"includeSimulations\": false,\n  \"organizationPreferences\": \"Service provider seeking favorable payment terms and limited warranties\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/documents/{{document_id}}/negotiation-playbook", "host": ["{{base_url}}"], "path": ["documents", "{{document_id}}", "negotiation-playbook"]}, "description": "Example request for a service agreement without simulations."}}, {"name": "Generate Playbook - Minimal Request", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"documentType\": \"OTHER\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/documents/{{document_id}}/negotiation-playbook", "host": ["{{base_url}}"], "path": ["documents", "{{document_id}}", "negotiation-playbook"]}, "description": "Minimal request with only required fields."}}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set up environment variables if not already set", "if (!pm.environment.get('base_url')) {", "    pm.environment.set('base_url', 'http://localhost:4000/api');", "}", "", "if (!pm.environment.get('document_id')) {", "    pm.environment.set('document_id', '43ee5001-9a05-4934-9005-d33537231cf6');", "}", "", "// Check if JWT token is set", "if (!pm.environment.get('jwt_token') || pm.environment.get('jwt_token') === 'your_jwt_token_here') {", "    console.warn('Please set your JWT token in the jwt_token environment variable');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Basic response validation", "pm.test('Response has success field', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "});", "", "// Check for successful responses", "if (pm.response.code === 200 || pm.response.code === 201) {", "    pm.test('Success response has data', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData.success).to.be.true;", "        pm.expect(jsonData).to.have.property('data');", "    });", "", "    if (pm.request.method === 'POST') {", "        pm.test('Generated playbook has required fields', function () {", "            const jsonData = pm.response.json();", "            const playbook = jsonData.data;", "            pm.expect(playbook).to.have.property('id');", "            pm.expect(playbook).to.have.property('documentId');", "            pm.expect(playbook).to.have.property('executiveSummary');", "            pm.expect(playbook).to.have.property('keyNegotiationPoints');", "            pm.expect(playbook).to.have.property('strategicRecommendations');", "            pm.expect(playbook).to.have.property('riskAssessment');", "        });", "    }", "}", "", "// Check for error responses", "if (pm.response.code >= 400) {", "    pm.test('Error response has error details', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData.success).to.be.false;", "        pm.expect(jsonData).to.have.property('error');", "        pm.expect(jsonData.error).to.have.property('code');", "        pm.expect(jsonData.error).to.have.property('message');", "    });", "}"]}}]}
{"info": {"name": "Privilege Log Automation API", "description": "API collection for testing Privilege Log Automation features", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.0.0"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{authToken}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "http://localhost:4000/api", "type": "string"}, {"key": "documentId", "value": "675a1b2c3d4e5f6789abcdef", "type": "string"}, {"key": "contentId", "value": "content-123", "type": "string"}], "item": [{"name": "1. Analyze Document for Privileged Content", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"includeAIAnalysis\": true,\n  \"confidenceThreshold\": 0.8,\n  \"privilegeTypes\": [\"attorney_client\", \"work_product\", \"confidential_communication\"],\n  \"autoRedact\": false,\n  \"requireManualReview\": true\n}"}, "url": {"raw": "{{baseUrl}}/documents/{{documentId}}/privilege-analysis", "host": ["{{baseUrl}}"], "path": ["documents", "{{documentId}}", "privilege-analysis"]}, "description": "Analyzes a document for privileged content using both pattern matching and AI analysis."}, "response": [{"name": "Successful Analysis", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"includeAIAnalysis\": true,\n  \"confidenceThreshold\": 0.8,\n  \"privilegeTypes\": [\"attorney_client\", \"work_product\"],\n  \"requireManualReview\": true\n}"}, "url": {"raw": "{{baseUrl}}/documents/{{documentId}}/privilege-analysis", "host": ["{{baseUrl}}"], "path": ["documents", "{{documentId}}", "privilege-analysis"]}}, "status": "Created", "code": 201, "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\n  \"success\": true,\n  \"data\": {\n    \"documentId\": \"675a1b2c3d4e5f6789abcdef\",\n    \"privilegedContent\": [\n      {\n        \"id\": \"content-123\",\n        \"documentId\": \"675a1b2c3d4e5f6789abcdef\",\n        \"startPosition\": 45,\n        \"endPosition\": 78,\n        \"content\": \"attorney-client communication\",\n        \"privilegeType\": \"attorney_client\",\n        \"confidenceScore\": 0.92,\n        \"detectionMethod\": \"pattern\",\n        \"status\": \"detected\",\n        \"redactionApplied\": false\n      }\n    ],\n    \"summary\": {\n      \"totalItemsFound\": 5,\n      \"highConfidenceItems\": 3,\n      \"requiresManualReview\": 2,\n      \"autoRedactable\": 3\n    }\n  }\n}"}]}, {"name": "2. Get Privilege Log for Document", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/documents/{{documentId}}/privilege-log", "host": ["{{baseUrl}}"], "path": ["documents", "{{documentId}}", "privilege-log"]}, "description": "Retrieves the existing privilege log for a specific document."}, "response": [{"name": "Privilege Log Found", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/documents/{{documentId}}/privilege-log", "host": ["{{baseUrl}}"], "path": ["documents", "{{documentId}}", "privilege-log"]}}, "status": "OK", "code": 200, "body": "{\n  \"success\": true,\n  \"data\": {\n    \"id\": \"log-123\",\n    \"documentId\": \"675a1b2c3d4e5f6789abcdef\",\n    \"documentTitle\": \"Employment Contract\",\n    \"privilegedContent\": [...],\n    \"totalPrivilegedItems\": 5,\n    \"totalRedactions\": 2,\n    \"analysisDate\": \"2025-01-20T10:30:00Z\",\n    \"status\": \"completed\"\n  }\n}"}]}, {"name": "3. List All Privilege Logs", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/documents/privilege-logs?page=1&limit=20&status=completed", "host": ["{{baseUrl}}"], "path": ["documents", "privilege-logs"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}, {"key": "status", "value": "completed"}]}, "description": "Retrieves all privilege logs for the organization with filtering and pagination."}}, {"name": "4. Review Privileged Content Item", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"confirmed\",\n  \"reason\": \"Confirmed attorney-client privilege\",\n  \"applyRedaction\": true\n}"}, "url": {"raw": "{{baseUrl}}/documents/{{documentId}}/privilege-content/{{contentId}}/review", "host": ["{{baseUrl}}"], "path": ["documents", "{{documentId}}", "privilege-content", "{{contentId}}", "review"]}, "description": "Updates the status of a privileged content item after manual review."}}, {"name": "5. Apply Single Redaction", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"contentId\": \"{{contentId}}\",\n  \"redactionText\": \"[REDACTED]\",\n  \"reason\": \"Attorney-client privilege protection\"\n}"}, "url": {"raw": "{{baseUrl}}/documents/{{documentId}}/redactions", "host": ["{{baseUrl}}"], "path": ["documents", "{{documentId}}", "redactions"]}, "description": "Applies redaction to a specific privileged content item."}}, {"name": "6. Apply Bulk Redactions", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"contentIds\": [\"content-123\", \"content-456\", \"content-789\"],\n  \"redactionText\": \"[REDACTED]\",\n  \"reason\": \"Bulk privilege protection\"\n}"}, "url": {"raw": "{{baseUrl}}/documents/{{documentId}}/bulk-redactions", "host": ["{{baseUrl}}"], "path": ["documents", "{{documentId}}", "bulk-redactions"]}, "description": "Applies redactions to multiple privileged content items at once."}}, {"name": "Test - Pattern Detection Only", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"includeAIAnalysis\": false,\n  \"confidenceThreshold\": 0.7,\n  \"privilegeTypes\": [\"attorney_client\", \"work_product\"],\n  \"autoRedact\": false,\n  \"requireManualReview\": true\n}"}, "url": {"raw": "{{baseUrl}}/documents/{{documentId}}/privilege-analysis", "host": ["{{baseUrl}}"], "path": ["documents", "{{documentId}}", "privilege-analysis"]}, "description": "Test analysis with pattern detection only (no AI)."}}, {"name": "Test - High Confidence Threshold", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"includeAIAnalysis\": true,\n  \"confidenceThreshold\": 0.9,\n  \"privilegeTypes\": [\"attorney_client\"],\n  \"autoRedact\": false,\n  \"requireManualReview\": false\n}"}, "url": {"raw": "{{baseUrl}}/documents/{{documentId}}/privilege-analysis", "host": ["{{baseUrl}}"], "path": ["documents", "{{documentId}}", "privilege-analysis"]}, "description": "Test analysis with high confidence threshold (0.9)."}}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set auth token if not already set", "if (!pm.environment.get('authToken')) {", "    console.log('Please set the authToken environment variable');", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Basic response validation", "pm.test('Status code is successful', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test('Response has success field', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData.success).to.be.true;", "});", "", "// Store content ID from analysis response for subsequent requests", "if (pm.request.url.path.includes('privilege-analysis')) {", "    const jsonData = pm.response.json();", "    if (jsonData.data && jsonData.data.privilegedContent && jsonData.data.privilegedContent.length > 0) {", "        pm.environment.set('contentId', jsonData.data.privilegedContent[0].id);", "    }", "}"]}}]}
# Activity Logging API

The Activity Logging API allows tracking and retrieving user interactions with documents and other entities within the Legal Document Analyzer. This provides a comprehensive timeline of activities and enables analytics on user behavior.

## Authentication

All endpoints require a valid JWT token in the Authorization header:

```http
Authorization: Bearer <jwt_token>
```

## Endpoints

### Create Activity Log

Creates a new activity log entry. This endpoint is primarily used by the system, but can also be called directly for custom activity tracking.

- **URL**: `/api/activity-logs`
- **Method**: `POST`
- **Auth Required**: Yes
- **Permissions Required**: `activity_logging` feature

**Request Body:**

```json
{
  "userId": "user-123",
  "action": "view",
  "entityType": "document",
  "entityId": "doc-456",
  "entityName": "Contract Agreement",
  "metadata": {
    "section": "legal terms",
    "timeSpent": 120
  }
}
```

**Parameters:**

| Name | Type | Required | Description |
|------|------|----------|-------------|
| userId | string | Yes | ID of the user who performed the action |
| action | string | Yes | Action performed (e.g., view, edit, share, delete) |
| entityType | string | Yes | Type of entity (document, analysis, comparison, user, system) |
| entityId | string | Yes | ID of the entity |
| entityName | string | No | Name of the entity |
| metadata | object | No | Additional metadata about the activity |
| ipAddress | string | No | IP address of the user (auto-filled if not provided) |
| userAgent | string | No | User agent of the client (auto-filled if not provided) |
| isSystem | boolean | No | Whether this is a system-generated activity |

**Response:**

- Status: 201 Created
- Body: Created activity log object

```json
{
  "id": "activity-789",
  "organizationId": "org-123",
  "userId": "user-123",
  "action": "view",
  "entityType": "document",
  "entityId": "doc-456",
  "entityName": "Contract Agreement",
  "metadata": {
    "section": "legal terms",
    "timeSpent": 120
  },
  "ipAddress": "***********",
  "userAgent": "Mozilla/5.0...",
  "isSystem": false,
  "createdAt": "2025-04-19T18:30:00.000Z",
  "updatedAt": "2025-04-19T18:30:00.000Z"
}
```

### Get Activity Logs

Retrieves activity logs with filtering and pagination.

- **URL**: `/api/activity-logs`
- **Method**: `GET`
- **Auth Required**: Yes
- **Permissions Required**: `activity_logging` feature

**Query Parameters:**

| Name | Type | Required | Description |
|------|------|----------|-------------|
| userId | string | No | Filter by user ID |
| entityType | string | No | Filter by entity type |
| entityId | string | No | Filter by entity ID |
| action | string | No | Filter by action |
| startDate | string | No | Start date for filtering (ISO format) |
| endDate | string | No | End date for filtering (ISO format) |
| page | number | No | Page number (default: 1) |
| limit | number | No | Items per page (default: 20) |
| sortBy | string | No | Field to sort by (default: createdAt) |
| sortOrder | string | No | Sort order: 'asc' or 'desc' (default: desc) |

**Response:**

- Status: 200 OK
- Body: Activity logs with pagination metadata

```json
{
  "logs": [
    {
      "id": "activity-789",
      "organizationId": "org-123",
      "userId": "user-123",
      "action": "view",
      "entityType": "document",
      "entityId": "doc-456",
      "entityName": "Contract Agreement",
      "metadata": {
        "section": "legal terms",
        "timeSpent": 120
      },
      "ipAddress": "***********",
      "userAgent": "Mozilla/5.0...",
      "isSystem": false,
      "createdAt": "2025-04-19T18:30:00.000Z",
      "updatedAt": "2025-04-19T18:30:00.000Z"
    }
    // More activity logs...
  ],
  "total": 150,
  "pages": 8
}
```

### Get Entity Timeline

Retrieves the activity timeline for a specific entity.

- **URL**: `/api/activity-logs/timeline/:entityType/:entityId`
- **Method**: `GET`
- **Auth Required**: Yes
- **Permissions Required**: `activity_logging` feature

**URL Parameters:**

| Name | Type | Required | Description |
|------|------|----------|-------------|
| entityType | string | Yes | Type of entity (document, analysis, comparison, user, system) |
| entityId | string | Yes | ID of the entity |

**Query Parameters:**

| Name | Type | Required | Description |
|------|------|----------|-------------|
| limit | number | No | Maximum number of records to return (default: 50) |

**Response:**

- Status: 200 OK
- Body: Array of activity logs for the entity

```json
[
  {
    "id": "activity-789",
    "organizationId": "org-123",
    "userId": "user-123",
    "action": "view",
    "entityType": "document",
    "entityId": "doc-456",
    "entityName": "Contract Agreement",
    "metadata": {
      "section": "legal terms",
      "timeSpent": 120
    },
    "ipAddress": "***********",
    "userAgent": "Mozilla/5.0...",
    "isSystem": false,
    "createdAt": "2025-04-19T18:30:00.000Z",
    "updatedAt": "2025-04-19T18:30:00.000Z"
  },
  {
    "id": "activity-790",
    "organizationId": "org-123",
    "userId": "user-123",
    "action": "edit",
    "entityType": "document",
    "entityId": "doc-456",
    "entityName": "Contract Agreement",
    "metadata": {
      "changedSections": ["introduction", "terms"]
    },
    "ipAddress": "***********",
    "userAgent": "Mozilla/5.0...",
    "isSystem": false,
    "createdAt": "2025-04-19T18:45:00.000Z",
    "updatedAt": "2025-04-19T18:45:00.000Z"
  }
  // More activity logs...
]
```

### Get User Activity History

Retrieves the activity history for a specific user.

- **URL**: `/api/activity-logs/user/:userId`
- **Method**: `GET`
- **Auth Required**: Yes
- **Permissions Required**: `activity_logging` feature

**URL Parameters:**

| Name | Type | Required | Description |
|------|------|----------|-------------|
| userId | string | Yes | ID of the user |

**Query Parameters:**

| Name | Type | Required | Description |
|------|------|----------|-------------|
| limit | number | No | Maximum number of records to return (default: 50) |

**Response:**

- Status: 200 OK
- Body: Array of activity logs for the user

```json
[
  {
    "id": "activity-789",
    "organizationId": "org-123",
    "userId": "user-123",
    "action": "view",
    "entityType": "document",
    "entityId": "doc-456",
    "entityName": "Contract Agreement",
    "metadata": {
      "section": "legal terms",
      "timeSpent": 120
    },
    "ipAddress": "***********",
    "userAgent": "Mozilla/5.0...",
    "isSystem": false,
    "createdAt": "2025-04-19T18:30:00.000Z",
    "updatedAt": "2025-04-19T18:30:00.000Z"
  }
  // More activity logs...
]
```

### Get Activity Statistics

Retrieves activity statistics for the organization.

- **URL**: `/api/activity-logs/statistics`
- **Method**: `GET`
- **Auth Required**: Yes
- **Permissions Required**: `activity_logging` feature

**Query Parameters:**

| Name | Type | Required | Description |
|------|------|----------|-------------|
| startDate | string | No | Start date for filtering (ISO format) |
| endDate | string | No | End date for filtering (ISO format) |

**Response:**

- Status: 200 OK
- Body: Activity statistics

```json
{
  "activityByType": [
    {
      "_id": {
        "entityType": "document",
        "action": "view"
      },
      "count": 250
    },
    {
      "_id": {
        "entityType": "document",
        "action": "edit"
      },
      "count": 120
    }
    // More activity types...
  ],
  "activityByUser": [
    {
      "_id": "user-123",
      "count": 180
    },
    {
      "_id": "user-456",
      "count": 150
    }
    // More users...
  ],
  "activityByDay": [
    {
      "_id": {
        "year": 2025,
        "month": 4,
        "day": 18
      },
      "count": 75
    },
    {
      "_id": {
        "year": 2025,
        "month": 4,
        "day": 19
      },
      "count": 120
    }
    // More days...
  ],
  "totalActivities": 500
}
```

## Automatic Activity Tracking

The system automatically tracks the following activities:

| Path | Method | Action | Entity Type |
|------|--------|--------|------------|
| `/documents/:id` | GET | view | document |
| `/documents/:id` | PUT | update | document |
| `/documents/:id` | DELETE | delete | document |
| `/documents/:id/analysis` | GET | view_analysis | analysis |
| `/documents/:id/analysis/compare` | GET | compare_analysis | analysis |
| `/documents/:id/analysis/compare` | POST | compare_analysis | analysis |
| `/enhanced-comparison/documents` | POST | enhanced_compare | comparison |
| `/enhanced-comparison/versions/:id` | POST | compare_versions | comparison |

Additional activities can be tracked by calling the Create Activity Log endpoint directly.

# Negotiation Playbook API Documentation

## Overview

The Negotiation Playbook feature provides AI-powered strategic recommendations for legal document negotiations. It analyzes uploaded documents and generates comprehensive negotiation strategies, risk assessments, and tactical recommendations. The system also integrates with the Negotiation Simulator to create practice scenarios based on playbook analysis.

**Feature Tier**: PRO (Premium feature)
**Credit Consumption**: 3 credits per playbook generation

## Base URL

```
https://api.docgic.com/api/documents/:documentId/negotiation-playbook
```

## Authentication

All endpoints require JWT authentication via the `Authorization` header:

```
Authorization: Bearer <jwt_token>
```

---

## Endpoints

### 1. Generate Negotiation Playbook

**POST** `/api/documents/:documentId/negotiation-playbook`

Generates a comprehensive negotiation playbook for the specified document using AI analysis.

#### Path Parameters

| Parameter    | Type   | Required | Description                     |
| ------------ | ------ | -------- | ------------------------------- |
| `documentId` | string | Yes      | UUID of the document to analyze |

#### Request Body

```json
{
  "documentType": "SERVICE_AGREEMENT",
  "focusAreas": ["termination", "liability", "payment"],
  "includeSimulations": true,
  "organizationPreferences": "Prefer shorter contract terms and lower liability exposure"
}
```

#### Request Body Schema

| Field                     | Type     | Required | Description                                                                              |
| ------------------------- | -------- | -------- | ---------------------------------------------------------------------------------------- |
| `documentType`            | string   | Yes      | Type of document. See DocumentType enum values below                                    |
| `focusAreas`              | string[] | No       | Specific areas to focus on during analysis (e.g., "termination", "liability", "payment") |
| `includeSimulations`      | boolean  | No       | Whether to include negotiation simulations (default: true)                              |
| `organizationPreferences` | string   | No       | Organization-specific negotiation preferences and guidelines                             |

#### DocumentType Enum Values
- `CONTRACT`
- `SERVICE_AGREEMENT`
- `EMPLOYMENT_CONTRACT`
- `NDA`
- `LICENSING_AGREEMENT`
- `PURCHASE_AGREEMENT`
- `PARTNERSHIP_AGREEMENT`
- `CONSULTING_AGREEMENT`
- `VENDOR_AGREEMENT`
- `MASTER_SERVICE_AGREEMENT`
- `SOFTWARE_LICENSE`
- `REAL_ESTATE_CONTRACT`
- `LEASE_AGREEMENT`
- `LOAN_AGREEMENT`
- `INSURANCE_POLICY`
- `TERMS_OF_SERVICE`
- `PRIVACY_POLICY`
- `COURT_FILING`
- `LEGAL_OPINION`
- `POLICY_DOCUMENT`
- `LEGISLATION`
- `OTHER`

#### Response (201 Created)

```json
{
  "documentId": "document_uuid",
  "strategies": [
    {
      "section": "Liability Limitations",
      "recommendations": [
        "Cap total liability at contract value",
        "Exclude consequential damages from liability"
      ],
      "riskLevel": "high",
      "priority": 1,
      "alternativeLanguage": "Total liability shall not exceed the fees paid under this Agreement",
      "simulationScenarios": [
        {
          "type": "dealbreaker",
          "trigger": "Counterparty demands unlimited liability",
          "responseStrategy": "Firmly decline and offer alternative risk-sharing mechanisms",
          "expectedOutcome": "Maintain liability protection while showing flexibility"
        }
      ]
    },
    {
      "section": "Payment Terms",
      "recommendations": [
        "Negotiate Net 15 instead of Net 30",
        "Include early payment discount incentives"
      ],
      "riskLevel": "medium",
      "priority": 2,
      "alternativeLanguage": "Payment due within fifteen (15) days of invoice receipt",
      "simulationScenarios": [
        {
          "type": "concession",
          "trigger": "Client requests extended payment terms",
          "responseStrategy": "Offer early payment discount as alternative",
          "expectedOutcome": "Maintain cash flow while providing value"
        }
      ]
    }
  ],
  "overallAssessment": "Contract requires significant improvements in liability and payment terms to achieve balanced risk allocation",
  "keyLeveragePoints": [
    "Service quality guarantees",
    "Intellectual property ownership",
    "Termination rights"
  ],
  "dealBreakers": [
    "Unlimited liability exposure",
    "Immediate termination without cause"
  ],
  "timestamp": "2025-01-23T13:20:59.000Z"
}
```

#### Error Responses

**400 Bad Request**

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid document type provided",
    "details": {
      "field": "documentType",
      "allowedValues": ["CONTRACT", "AGREEMENT", "POLICY", "TERMS", "OTHER"]
    }
  }
}
```

**403 Forbidden**

```json
{
  "success": false,
  "error": {
    "code": "FEATURE_NOT_AVAILABLE",
    "message": "Negotiation playbook feature requires PRO subscription",
    "upgradeUrl": "/subscription/upgrade"
  }
}
```

**404 Not Found**

```json
{
  "success": false,
  "error": {
    "code": "DOCUMENT_NOT_FOUND",
    "message": "Document not found or access denied"
  }
}
```

**500 Internal Server Error**

```json
{
  "success": false,
  "error": {
    "code": "AI_SERVICE_ERROR",
    "message": "Failed to generate negotiation playbook",
    "details": "AI service temporarily unavailable"
  }
}
```

---

### 2. Retrieve Negotiation Playbook

**GET** `/api/documents/:documentId/negotiation-playbook`

Retrieves the existing negotiation playbook for the specified document.

#### Path Parameters

| Parameter    | Type   | Required | Description          |
| ------------ | ------ | -------- | -------------------- |
| `documentId` | string | Yes      | UUID of the document |

#### Response (200 OK)

```json
{
  "documentId": "document_uuid",
  "strategies": [
    {
      "section": "Liability Limitations",
      "recommendations": [
        "Cap total liability at contract value",
        "Exclude consequential damages from liability"
      ],
      "riskLevel": "high",
      "priority": 1,
      "alternativeLanguage": "Total liability shall not exceed the fees paid under this Agreement",
      "simulationScenarios": [
        {
          "type": "dealbreaker",
          "trigger": "Counterparty demands unlimited liability",
          "responseStrategy": "Firmly decline and offer alternative risk-sharing mechanisms",
          "expectedOutcome": "Maintain liability protection while showing flexibility"
        }
      ]
    }
  ],
  "overallAssessment": "Contract requires significant improvements in liability and payment terms",
  "keyLeveragePoints": [
    "Service quality guarantees",
    "Intellectual property ownership"
  ],
  "dealBreakers": [
    "Unlimited liability exposure",
    "Immediate termination without cause"
  ],
  "timestamp": "2025-01-23T13:20:59.000Z"
}
```

#### Error Responses

**404 Not Found**

```json
{
  "success": false,
  "error": {
    "code": "PLAYBOOK_NOT_FOUND",
    "message": "No negotiation playbook found for this document"
  }
}
```

---

### 3. Create Simulation Scenario from Playbook

**POST** `/api/documents/:documentId/negotiation-playbook/create-scenario`

Creates a negotiation simulation scenario based on the existing playbook analysis. This endpoint bridges the gap between strategic analysis and practical training.

**Credit Consumption**: 0 credits (data transformation only)

#### Path Parameters

| Parameter    | Type   | Required | Description                     |
| ------------ | ------ | -------- | ------------------------------- |
| `documentId` | string | Yes      | UUID of the document with playbook |

#### Request Body

```json
{
  "difficulty": "intermediate",
  "focusAreas": ["liability", "payment_terms"],
  "aiPersonality": {
    "aggressiveness": 0.7,
    "flexibility": 0.6,
    "communicationStyle": "formal"
  },
  "customizations": {
    "maxRounds": 8,
    "timeLimit": 45,
    "specificTerms": ["liability_cap", "payment_schedule"]
  }
}
```

#### Request Body Schema

| Field                     | Type     | Required | Description                                                                              |
| ------------------------- | -------- | -------- | ---------------------------------------------------------------------------------------- |
| `difficulty`              | string   | No       | Simulation difficulty: "beginner", "intermediate", "expert" (auto-detected from playbook) |
| `focusAreas`              | string[] | No       | Specific negotiation areas to emphasize in simulation                                    |
| `aiPersonality`           | object   | No       | AI counterparty personality configuration                                                |
| `customizations`          | object   | No       | Session-specific customizations                                                          |

#### Response (201 Created)

```json
{
  "id": "scenario-uuid",
  "name": "Practice: Service Agreement Analysis",
  "description": "Negotiation practice scenario based on document analysis",
  "difficulty": "intermediate",
  "createdAt": "2025-01-23T13:20:59.000Z"
}
```

#### Error Responses

**404 Not Found**

```json
{
  "statusCode": 404,
  "message": "No negotiation playbook found for document doc-123. Please generate a playbook first.",
  "suggestedAction": "POST /api/documents/doc-123/negotiation-playbook"
}
```

---

## Data Models

### NegotiationPlaybook

```typescript
interface NegotiationPlaybook {
  documentId: string;
  strategies: NegotiationStrategy[];
  overallAssessment: string;
  keyLeveragePoints: string[];
  dealBreakers: string[];
  timestamp: Date;
}
```

### NegotiationStrategy

```typescript
interface NegotiationStrategy {
  section: string;
  recommendations: string[];
  riskLevel: 'low' | 'medium' | 'high';
  priority: number;
  alternativeLanguage: string;
  simulationScenarios: SimulationScenario[];
}
```

### SimulationScenario

```typescript
interface SimulationScenario {
  type: 'concession' | 'dealbreaker' | 'leverage' | 'compromise';
  trigger: string;
  responseStrategy: string;
  expectedOutcome: string;
}
```

### CreateScenarioRequest

```typescript
interface CreateScenarioRequest {
  difficulty?: 'beginner' | 'intermediate' | 'expert';
  focusAreas?: string[];
  aiPersonality?: {
    aggressiveness: number; // 0.0-1.0
    flexibility: number; // 0.0-1.0
    communicationStyle: 'formal' | 'diplomatic' | 'casual' | 'aggressive';
  };
  customizations?: {
    maxRounds?: number;
    timeLimit?: number;
    specificTerms?: string[];
  };
}
```

---

## Usage Examples

### JavaScript/TypeScript Example

```typescript
// Generate negotiation playbook
const generatePlaybook = async (documentId: string) => {
  try {
    const response = await fetch(
      `/api/documents/${documentId}/negotiation-playbook`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${authToken}`,
        },
        body: JSON.stringify({
          documentType: 'SERVICE_AGREEMENT',
          focusAreas: ['termination', 'liability', 'payment'],
          includeSimulations: true,
          organizationPreferences:
            'Prefer shorter contract terms and lower liability exposure',
        }),
      },
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result; // Direct response, no .data wrapper
  } catch (error) {
    console.error('Failed to generate negotiation playbook:', error);
    throw error;
  }
};

// Retrieve existing playbook
const getPlaybook = async (documentId: string) => {
  try {
    const response = await fetch(
      `/api/documents/${documentId}/negotiation-playbook`,
      {
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
      },
    );

    if (response.status === 404) {
      return null; // No playbook exists
    }

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result; // Direct response, no .data wrapper
  } catch (error) {
    console.error('Failed to retrieve negotiation playbook:', error);
    throw error;
  }
};

// Create simulation scenario from playbook
const createScenario = async (documentId: string, options?: CreateScenarioRequest) => {
  try {
    const response = await fetch(
      `/api/documents/${documentId}/negotiation-playbook/create-scenario`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${authToken}`,
        },
        body: JSON.stringify(options || {}),
      },
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Failed to create simulation scenario:', error);
    throw error;
  }
};
```

### React Hook Example

```typescript
import { useState, useEffect } from 'react';

interface UseNegotiationPlaybookResult {
  playbook: NegotiationPlaybook | null;
  loading: boolean;
  error: string | null;
  generatePlaybook: (options: GeneratePlaybookOptions) => Promise<void>;
  refreshPlaybook: () => Promise<void>;
}

export const useNegotiationPlaybook = (
  documentId: string,
): UseNegotiationPlaybookResult => {
  const [playbook, setPlaybook] = useState<NegotiationPlaybook | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generatePlaybook = async (options: GeneratePlaybookOptions) => {
    setLoading(true);
    setError(null);

    try {
      const newPlaybook = await generatePlaybook(documentId, options);
      setPlaybook(newPlaybook);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : 'Failed to generate playbook',
      );
    } finally {
      setLoading(false);
    }
  };

  const refreshPlaybook = async () => {
    setLoading(true);
    setError(null);

    try {
      const existingPlaybook = await getPlaybook(documentId);
      setPlaybook(existingPlaybook);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load playbook');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    refreshPlaybook();
  }, [documentId]);

  return {
    playbook,
    loading,
    error,
    generatePlaybook,
    refreshPlaybook,
  };
};
```

---

## Rate Limits

- **Generation**: 10 requests per hour per organization
- **Retrieval**: 100 requests per hour per organization

## Notes

- Playbook generation typically takes 15-30 seconds depending on document complexity
- Generated playbooks are cached and don't need to be regenerated unless the document changes
- The AI analysis considers document type, content, and organization preferences
- Simulations are only included if `includeSimulations` is set to `true` in the request

## Support

For technical support or questions about the Negotiation Playbook API, contact the development team or refer to the main API documentation.

# Profile API

This document outlines the API endpoints for retrieving and updating user profile information in the Legal Document Analyzer.

## Endpoints

### Get User Profile

Retrieves comprehensive user profile information including organization and subscription details.

**URL**: `/api/auth/profile`

**Method**: `GET`

**Auth Required**: Yes (JWT)

**Headers**:

| Header          | Value            | Description                                  |
|-----------------|------------------|----------------------------------------------|
| Authorization   | Bearer [token]   | JWT token obtained during login/registration |

**Success Response**:

- **Code**: 200 OK
- **Content**:

```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "email": "<EMAIL>",
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "role": "USER",
  "organizationId": "123e4567-e89b-12d3-a456-426614174000",
  "organizationName": "John's Workspace",
  "emailVerified": true,
  "isGoogleUser": false,
  "picture": "https://example.com/profile.jpg",
  "lastLoginAt": "2025-05-05T13:00:00Z",
  "createdAt": "2025-01-01T10:00:00Z",
  "subscriptionTier": "PRO",
  "subscriptionStatus": "active",
  "twoFactorEnabled": false,
  "twoFactorVerified": false
}
```

**Error Responses**:

- **Code**: 401 Unauthorized
- **Content**:

```json
{
  "statusCode": 401,
  "message": "Unauthorized",
  "error": "Unauthorized"
}
```

OR

- **Code**: 404 Not Found
- **Content**:

```json
{
  "statusCode": 404,
  "message": "User not found",
  "error": "Not Found"
}
```

### Update User Profile

Updates the current user's profile information.

**URL**: `/api/auth/profile`

**Method**: `PUT`

**Auth Required**: Yes (JWT)

**Headers**:

| Header          | Value            | Description                                  |
|-----------------|------------------|----------------------------------------------|
| Authorization   | Bearer [token]   | JWT token obtained during login/registration |
| Content-Type    | application/json | JSON payload                                 |

**Request Body**:

```json
{
  "firstName": "John",
  "lastName": "Doe",
  "picture": "https://example.com/new-profile.jpg"
}
```

All fields in the request body are optional. Only the fields that need to be updated should be included.

**Success Response**:

- **Code**: 200 OK
- **Content**: Returns the complete updated profile in the same format as the GET endpoint

**Error Responses**:

- **Code**: 400 Bad Request
- **Content**:

```json
{
  "statusCode": 400,
  "message": "Google users cannot update their name directly. Please update your Google profile.",
  "error": "Bad Request"
}
```

OR

- **Code**: 401 Unauthorized
- **Content**:

```json
{
  "statusCode": 401,
  "message": "Unauthorized",
  "error": "Unauthorized"
}
```

OR

- **Code**: 404 Not Found
- **Content**:

```json
{
  "statusCode": 404,
  "message": "User not found",
  "error": "Not Found"
}
```

## Data Models

### ProfileResponseDto

```typescript
export class ProfileResponseDto {
  id: string;                  // User ID
  email: string;               // User email address
  firstName: string;           // User first name
  lastName: string;            // User last name
  role: string;                // User role (USER, ADMIN, etc.)
  organizationId: string;      // Organization ID the user belongs to
  organizationName: string;    // Organization name
  emailVerified: boolean;      // Whether the user's email has been verified
  isGoogleUser?: boolean;      // Whether the user authenticated with Google
  picture?: string;            // User profile picture URL
  lastLoginAt?: Date;          // User last login date
  createdAt: Date;             // User account creation date
  subscriptionTier: string;    // Subscription tier of the user's organization
  subscriptionStatus: string;  // Subscription status
  twoFactorEnabled?: boolean;  // Whether two-factor authentication is enabled
  twoFactorVerified?: boolean; // Whether two-factor authentication has been verified
}
```

### UpdateProfileDto

```typescript
export class UpdateProfileDto {
  firstName?: string;          // User first name (optional)
  lastName?: string;           // User last name (optional)
  picture?: string;            // User profile picture URL (optional)
}
```

## Multi-Tenant Considerations

The profile endpoints respect the multi-tenant architecture of the Legal Document Analyzer:

1. User information is retrieved based on the user ID from the JWT token
2. Organization information is retrieved based on the organization ID from the JWT token
3. Subscription information is specific to the user's organization

This ensures proper tenant isolation, as users can only access their own profile information and the organization/subscription details relevant to them.

## Two-Factor Authentication

The profile endpoint includes information about the user's two-factor authentication status:

- `twoFactorEnabled`: Indicates whether the user has enabled two-factor authentication
- `twoFactorVerified`: Indicates whether the user has completed the verification process for two-factor authentication

These fields can be used by the frontend to determine whether to prompt the user to set up two-factor authentication or to require a two-factor code during login.

## Special Considerations

### Google Users

Users who authenticated with Google (indicated by `isGoogleUser: true`) cannot update their first and last names directly through the API. These users must update their profile information through Google, which will then be synchronized with the Legal Document Analyzer on their next login.

## Frontend Integration Example

```javascript
// Example: Get user profile
async function getUserProfile() {
  try {
    const response = await fetch('/api/auth/profile', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch profile');
    }
    
    const profileData = await response.json();
    
    // Update UI with profile data
    updateProfileUI(profileData);
    
    // Check subscription status
    if (profileData.subscriptionStatus !== 'active') {
      showSubscriptionAlert(profileData.subscriptionStatus);
    }
    
    // Check two-factor authentication status
    if (!profileData.twoFactorEnabled) {
      showTwoFactorSetupPrompt();
    }
    
    return profileData;
  } catch (error) {
    console.error('Error fetching profile:', error);
    showErrorMessage('Failed to load profile information');
  }
}

// Example: Update user profile
async function updateUserProfile(profileData) {
  try {
    const response = await fetch('/api/auth/profile', {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(profileData)
    });
    
    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to update profile');
    }
    
    const updatedProfile = await response.json();
    
    // Update UI with the updated profile data
    updateProfileUI(updatedProfile);
    
    return updatedProfile;
  } catch (error) {
    console.error('Error updating profile:', error);
    showErrorMessage(error.message || 'Failed to update profile');
  }
}

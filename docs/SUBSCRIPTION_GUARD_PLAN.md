# Subscription Guard and Decorator: Implementation Plan

This document details the implementation plan to decouple subscription checks from the business logic by using a dedicated guard and decorators.

---

## 1. Overview

The goal is to remove inline subscription validation logic from the DocumentProcessingService by enforcing it via a guard.  
Endpoints will be annotated with a custom decorator (e.g. `@SubscriptionCheck`) that describes the requirement (e.g. `"document_upload"`).  
A dedicated guard (e.g. `SubscriptionGuard`) will intercept incoming requests, check for the required metadata, and validate against the subscription service.  
A complementary decorator (`@SkipSubscriptionCheck`) remains available for testing or exceptional scenarios.

---

## 2. Components

### 2.1 SubscriptionCheck Decorator

- **Purpose:**  
  Annotate controller endpoints that require subscription validation.

- **Usage Example:**
  ```typescript
  import { SetMetadata } from '@nestjs/common';

  export const SUBSCRIPTION_CHECK_KEY = 'subscription_check';

  export const SubscriptionCheck = (operation: string) =>
    SetMetadata(SUBSCRIPTION_CHECK_KEY, operation);
  ```
- **Note:**  
  This decorator attaches the required operation (e.g. `"document_upload"`) to the route’s metadata.

---

### 2.2 SkipSubscriptionCheck Decorator

- **Purpose:**  
  Allow specific routes to bypass subscription checks.

- **Example:**
  ```typescript
  import { SetMetadata } from '@nestjs/common';

  export const SKIP_SUBSCRIPTION_CHECK = 'skip_subscription_check';

  export const SkipSubscriptionCheck = () => SetMetadata(SKIP_SUBSCRIPTION_CHECK, true);
  ```

---

### 2.3 SubscriptionGuard

- **Purpose:**  
  Implement a NestJS guard that enforces subscription checks before request handling.

- **Responsibilities:**
  - Use the NestJS `Reflector` to read metadata from the request handler.
  - If the route is decorated with `@SkipSubscriptionCheck`, bypass the check.
  - Otherwise, retrieve the required operation from the `@SubscriptionCheck` metadata.
  - Retrieve the current organization's ID (e.g., from `request.user` or a TenantContextService).
  - Check if the user is in a trial period and has access to the requested feature.
  - If not in trial or feature not in trial tier, invoke `SubscriptionService.canPerformOperation(organizationId, operation)` to determine if the operation is allowed.
  - Allow the request if allowed; if not, throw a `ForbiddenException`.

- **Example Skeleton Code:**
  ```typescript
  import { CanActivate, ExecutionContext, Injectable, ForbiddenException } from '@nestjs/common';
  import { Reflector } from '@nestjs/core';
  import { SubscriptionService } from '../services/subscription.service';
  import { SKIP_SUBSCRIPTION_CHECK } from '../decorators/skip-subscription-check.decorator';
  import { SUBSCRIPTION_CHECK_KEY } from '../decorators/subscription-check.decorator';

  @Injectable()
  export class SubscriptionGuard implements CanActivate {
    constructor(
      private readonly reflector: Reflector,
      private readonly subscriptionService: SubscriptionService,
      // Inject TenantContextService or similar to get organization ID if needed
    ) {}

    async canActivate(context: ExecutionContext): Promise<boolean> {
      const skipCheck = this.reflector.get<boolean>(SKIP_SUBSCRIPTION_CHECK, context.getHandler());
      if (skipCheck) {
        return true;
      }

      // Retrieve required operation from the route's metadata
      const operation = this.reflector.get<string>(SUBSCRIPTION_CHECK_KEY, context.getHandler());
      if (!operation) {
        // If not defined, allow by default or handle as per policy
        return true;
      }

      // Retrieve organizationId (e.g., from request.user)
      const request = context.switchToHttp().getRequest();
      const organizationId = request.user?.organizationId;
      if (!organizationId) {
        throw new ForbiddenException('Organization ID not found in request');
      }

      // Check if the operation is allowed for this subscription
      const isAllowed = await this.subscriptionService.canPerformOperation(organizationId, operation);
      if (!isAllowed) {
        throw new ForbiddenException(`Operation '${operation}' not allowed for your subscription tier`);
      }

      return true;
    }
  }
  ```

---

## 3. Implementation Steps

1. **Create Decorators:**
   - Implement `@SubscriptionCheck` and `@SkipSubscriptionCheck` decorators.

2. **Implement SubscriptionGuard:**
   - Create the guard with dependency injection for `SubscriptionService`.
   - Implement the `canActivate` method to check subscription permissions.

3. **Enhance SubscriptionService:**
   - Add a `canPerformOperation` method that checks:
     - If the subscription is active
     - If the user is in a trial period
     - If the requested feature is available in the user's tier or trial tier
     - If usage limits are not exceeded

4. **Update Controllers:**
   - Apply `@UseGuards(SubscriptionGuard)` to controllers or specific routes.
   - Annotate routes with `@SubscriptionCheck('feature_name')`.

5. **Configure Feature Availability:**
   - Define available features per tier in the subscription configuration.
   - Current tiers: Free, Pro, Admin

---

## 4. Feature Mapping

| Feature | Free | Pro | Admin |
|---------|------|-----|-------|
| document_upload | ✓ | ✓ | ✓ |
| basic_analysis | ✓ | ✓ | ✓ |
| document_organization | ✓ | ✓ | ✓ |
| user_feedback | ✓ | ✓ | ✓ |
| advanced_analysis | ✗ | ✓ | ✓ |
| batch_processing | ✗ | ✓ | ✓ |
| custom_models | ✗ | ✓ | ✓ |
| enhanced_comparison | ✗ | ✓ | ✓ |
| document_comparison | ✗ | ✓ | ✓ |
| advanced_document_organization | ✗ | ✓ | ✓ |
| advanced_analytics | ✗ | ✓ | ✓ |
| admin_features | ✗ | ✗ | ✓ |

---

## 5. Trial Period Implementation

New users on the Free tier automatically receive a 14-day trial of Pro features:

1. **Subscription Creation:**
   - When creating a Free tier subscription, set `trialTier: SubscriptionTier.PRO` and `trialEndDate: now + 14 days`

2. **Feature Availability Check:**
   - First check if user is in trial period (`now < trialEndDate`)
   - If in trial, check if feature is available in trial tier
   - If trial expired or feature not in trial tier, fall back to regular subscription check

3. **Trial Expiration:**
   - No explicit action needed; the system automatically falls back to regular subscription tier when checking feature availability after trial end date

---

## 6. Example Usage

- **Before:**
  ```typescript
  async uploadDocument(@UploadedFile() file) {
    // Manual subscription check
    if (!await this.subscriptionService.hasAccess(organizationId, 'document_upload')) {
      throw new ForbiddenException('Feature not available in your subscription');
    }
    // Process upload
  }
  ```

- **After:**
  ```typescript
  @Public()
  @UseGuards(SubscriptionGuard)
  @SubscriptionCheck('document_upload')
  async uploadDocument(@UploadedFile() file) {
    // No manual check needed, guard handles it
    // Process upload
  }
  ```

---

## 7. Testing Considerations

- Use `@SkipSubscriptionCheck()` in test environments to bypass subscription checks.
- Create test fixtures for different subscription tiers and trial periods.
- Test both positive (allowed features) and negative (forbidden features) scenarios.
- Verify trial period logic by manipulating the trial end date.

---

## 8. Migration Strategy

1. Implement the guard and decorators.
2. Update one endpoint at a time, replacing inline checks with decorators.
3. Test thoroughly after each endpoint migration.
4. Once all endpoints are migrated, remove any redundant code from services.

---

## 3. Modifications to Controllers

### DocumentsController

- **Changes:**
  - Remove inline subscription-check code.
  - Apply the guard using `@UseGuards(SubscriptionGuard)` on endpoints requiring subscription validation.
  - Annotate such endpoints with `@SubscriptionCheck('document_upload')`.

- **Before:**
  ```typescript
  @Public()
  @SkipSubscriptionCheck()
  @Post('upload')
  @UseInterceptors( ... )
  async uploadDocument(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadDocumentDto: UploadDocumentDto
  ) {
    const document = await this.documentProcessingService.processUploadedFile(file, context);
    // ...
  }
  ```

- **After:**
  ```typescript
  @Public()
  @UseGuards(SubscriptionGuard)
  @SubscriptionCheck('document_upload')
  @Post('upload')
  @UseInterceptors( ... )
  async uploadDocument(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadDocumentDto: UploadDocumentDto
  ) {
    // Inline subscription check removed (handled by guard)
    const document = await this.documentProcessingService.processUploadedFile(file);
    // ...
  }
  ```

---

## 4. Service Refactoring

- In `DocumentProcessingService.processUploadedFile`, remove the inline subscription check logic, as the guard ensures this validation is performed beforehand.
- The service now solely processes and stores the document, assuming that all subscription validations have already passed.

---

## 5. Summary and Recommendations

- **Benefits:**
  - Clear separation of concerns between business logic and policy enforcement.
  - Easier maintenance and centralized subscription logic.
  - Improved clarity in controller endpoints via declarative annotations.
  
- **Trade-Offs:**
  - Additional abstraction layers may complicate initial debugging.
  - Requires thorough testing of both the guard and the underlying subscription service.
  - Increased reliance on NestJS metadata and guard execution order.

---

## 6. Next Steps

- Review and finalize the implementation plan.
- Implement the changes in the corresponding files:
  - Create `subscription-check.decorator.ts` under `src/modules/subscription/decorators/`.
  - Create or update `SubscriptionGuard` in `src/modules/subscription/guards/`.
  - Update the `DocumentsController` to use the guard and decorators.
  - Refactor the `DocumentProcessingService` to remove inline subscription checks.
- Write unit tests to ensure that the subscription guard and the service behave as expected.

Please review this implementation plan and let me know if any changes are required.

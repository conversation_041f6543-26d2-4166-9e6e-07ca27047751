# PostHog Integration Guide

## Overview

This guide explains how to use the PostHog analytics integration in the application. PostHog provides powerful analytics capabilities including event tracking, user identification, feature flags, and session recording.

## Backend Implementation

### Service Injection

To use PostHog analytics in your service or controller, inject the `PostHogService`:

```typescript
import { PostHogService } from '../../posthog/services/posthog.service';

@Injectable()
export class YourService {
  constructor(private readonly postHogService: PostHogService) {}
}
```

### Event Tracking

#### Basic Event Tracking

```typescript
// Track a simple event
this.postHogService.trackEvent(userId, 'user_action', {
  action_type: 'click',
  element: 'button',
  page: '/dashboard'
});
```

#### Document-Specific Events

```typescript
// Document upload
this.postHogService.trackDocumentUpload(userId, documentId, documentType, {
  organization_id: organizationId,
  file_size: fileSize,
  file_name: fileName
});

// Document analysis
this.postHogService.trackDocumentAnalysis(userId, documentId, analysisType, duration, {
  organization_id: organizationId,
  success: true
});

// Document comparison
this.postHogService.trackDocumentComparison(userId, [doc1Id, doc2Id], comparisonType, {
  organization_id: organizationId
});
```

#### Feature Usage Tracking

```typescript
this.postHogService.trackFeatureUsage(userId, 'legal_research', true, 2500, {
  organization_id: organizationId,
  query_type: 'case_law'
});
```

#### Error Tracking

```typescript
try {
  // Some operation
} catch (error) {
  this.postHogService.trackError(userId, error, 'document_processing', {
    organization_id: organizationId,
    document_id: documentId
  });
  throw error;
}
```

### User Identification

```typescript
// Identify user with properties
this.postHogService.identifyUser(userId, {
  email: user.email,
  name: user.name,
  organization: user.organizationId,
  subscription_tier: user.subscriptionTier,
  role: user.role
});

// Update user properties
this.postHogService.setUserProperties(userId, {
  last_login: new Date().toISOString(),
  documents_analyzed: count
});
```

### Feature Flags

```typescript
// Check if feature is enabled
const isFeatureEnabled = await this.postHogService.isFeatureEnabled(
  'new_ui_design',
  userId,
  false // default value
);

if (isFeatureEnabled) {
  // Use new feature
}

// Get feature flag with payload
const featureConfig = await this.postHogService.getFeatureFlag(
  'analysis_config',
  userId,
  { maxDocuments: 10 }
);
```

### Performance Tracking with Decorators

```typescript
import { TrackPerformance, TrackMethodCall } from '../../posthog/decorators/track-performance.decorator';

@Injectable()
export class DocumentService {
  constructor(private readonly postHogService: PostHogService) {}

  @TrackPerformance('document_analysis_performance')
  async analyzeDocument(documentId: string): Promise<any> {
    // Method implementation
  }

  @TrackMethodCall('document_upload_attempt')
  async uploadDocument(file: Express.Multer.File): Promise<any> {
    // Method implementation
  }

  // Helper method for decorators to get user ID
  getUserId(): string {
    // Return current user ID from context
    return this.tenantContext.getCurrentUserId();
  }
}
```

## Automatic Tracking

### Middleware Integration

The application automatically tracks:
- API requests and responses
- Response times and status codes
- Error rates

This is handled by the `AnalyticsMiddleware` which is globally applied.

### Enhanced Analytics Collection

The existing `AnalyticsCollectionService` has been enhanced to send events to both MongoDB (for internal analytics) and PostHog (for external analytics):

```typescript
// The service automatically tracks to both systems
await this.analyticsCollectionService.recordDocumentUpload(
  documentId,
  organizationId,
  userId,
  documentType,
  { fileSize: 1024, fileName: 'contract.pdf' }
);
```

## Configuration

### Environment Variables

```env
# PostHog Configuration
POSTHOG_API_KEY=your_project_api_key
POSTHOG_HOST=https://app.posthog.com
POSTHOG_ENABLED=true
POSTHOG_FLUSH_AT=20
POSTHOG_FLUSH_INTERVAL=10000
```

### Feature Flag Configuration

Feature flags are configured in the PostHog dashboard. Common flags for this application:

- `advanced_analysis`: Enable advanced document analysis features
- `new_ui_components`: Enable new UI components
- `beta_features`: Enable beta features for testing
- `increased_limits`: Enable increased usage limits for specific users

## Best Practices

### Event Naming

Use consistent naming conventions:
- Use snake_case for event names
- Be descriptive but concise
- Group related events with common prefixes

```typescript
// Good
'document_uploaded'
'document_analyzed'
'document_deleted'

// Avoid
'DocumentUploaded'
'doc_upload'
'upload'
```

### Property Consistency

Always include these properties when relevant:
- `organization_id`: For tenant isolation
- `user_role`: For role-based analytics
- `timestamp`: When not automatically added

### Error Handling

Always wrap PostHog calls in try-catch blocks for non-critical tracking:

```typescript
try {
  this.postHogService.trackEvent(userId, 'feature_used', properties);
} catch (error) {
  this.logger.warn('Failed to track analytics event', error);
  // Don't let analytics failures break the main functionality
}
```

### Performance Considerations

- PostHog operations are fire-and-forget by default
- Events are batched and sent asynchronously
- Consider the frequency of high-volume events
- Use appropriate flush intervals for your use case

## Testing

### Disabling in Tests

```typescript
// In test configuration
process.env.POSTHOG_ENABLED = 'false';
```

### Mocking PostHog Service

```typescript
const mockPostHogService = {
  trackEvent: jest.fn(),
  identifyUser: jest.fn(),
  isFeatureEnabled: jest.fn().mockResolvedValue(false),
};

// In test module
{
  provide: PostHogService,
  useValue: mockPostHogService,
}
```

## Debugging

### Logging

The PostHog service includes debug logging. Set log level to debug to see PostHog operations:

```typescript
// The service automatically logs events in debug mode
this.logger.debug(`Tracked event: ${event} for user: ${userId}`);
```

### Verification

You can verify events in the PostHog dashboard under:
1. Events & Actions
2. Live Events (for real-time monitoring)
3. Insights (for analytics)

## Frontend Integration

See the [Frontend PostHog Integration](./posthog-frontend-integration.md) guide for client-side implementation.

## Security Considerations

- Never track sensitive data (passwords, API keys, personal information)
- Use appropriate data retention policies
- Implement proper user consent mechanisms
- Ensure GDPR compliance for EU users
- Mask sensitive form inputs in session recordings

## Support and Troubleshooting

### Common Issues

1. **Events not appearing**: Check API key and network connectivity
2. **Feature flags not working**: Verify flag configuration in PostHog dashboard
3. **Performance issues**: Adjust flush intervals and batch sizes

### Monitoring

Monitor PostHog integration health by tracking:
- Event delivery success rates
- API response times
- Error rates in PostHog calls
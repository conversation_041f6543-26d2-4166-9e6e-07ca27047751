# PostHog Backend Integration Plan

## Overview
This document outlines the plan for integrating PostHog analytics into the backend of our application. The integration will support user behavior tracking, event funnels, error tracking, and feature flags while maintaining our existing analytics capabilities.

```mermaid
flowchart TD
    A[Create PostHog Module] --> B[Configure PostHog Client]
    B --> C[Create Analytics Service Layer]
    C --> D[Implement Event Tracking]
    D --> E[Implement User Identification]
    E --> F[Add Error Tracking]
    F --> G[Set Up Feature Flags]
    G --> H[Configure Session Recording]
```

## 1. Initial Setup and Configuration

### Create PostHog Module
```typescript
// src/modules/posthog/posthog.module.ts
@Module({
  imports: [ConfigModule],
  providers: [PostHogService],
  exports: [PostHogService]
})
export class PostHogModule {}
```

### Configuration Changes
1. Add PostHog configuration to `.env`:
```env
POSTHOG_API_KEY=your_project_api_key
POSTHOG_HOST=https://app.posthog.com
POSTHOG_ENABLED=true
```

2. Create config file:
```typescript
// src/config/posthog.config.ts
export const posthogConfig = () => ({
  posthog: {
    apiKey: process.env.POSTHOG_API_KEY,
    host: process.env.POSTHOG_HOST,
    enabled: process.env.POSTHOG_ENABLED === 'true'
  }
});
```

## 2. Core Implementation

### PostHog Service
```typescript
// src/modules/posthog/services/posthog.service.ts
@Injectable()
export class PostHogService {
  private client: PostHog;
  
  constructor(private configService: ConfigService) {
    this.client = new PostHog(
      this.configService.get('posthog.apiKey'),
      { host: this.configService.get('posthog.host') }
    );
  }

  // Event tracking
  trackEvent(userId: string, event: string, properties: any = {}) {
    if (!this.configService.get('posthog.enabled')) return;
    
    this.client.capture({
      distinctId: userId,
      event,
      properties
    });
  }

  // User identification
  identifyUser(userId: string, properties: any = {}) {
    if (!this.configService.get('posthog.enabled')) return;
    
    this.client.identify({
      distinctId: userId,
      properties
    });
  }
}
```

## 3. Event Implementation Plan

### User Engagement Events
1. Document Events:
```typescript
// Upload events
trackEvent(userId, 'document_uploaded', {
  documentId,
  documentType,
  fileSize,
  fileName
});

// Analysis events
trackEvent(userId, 'document_analyzed', {
  documentId,
  analysisType,
  processingTime
});

// Comparison events
trackEvent(userId, 'documents_compared', {
  documentIds: [doc1Id, doc2Id],
  comparisonType
});
```

2. Feature Usage Events:
```typescript
trackEvent(userId, 'feature_used', {
  featureId,
  featureName,
  result,
  duration
});
```

### User Journey Events
```typescript
// Signup funnel
trackEvent(userId, 'signup_started');
trackEvent(userId, 'signup_completed');
trackEvent(userId, 'onboarding_started');
trackEvent(userId, 'onboarding_completed');
```

### Error Tracking
```typescript
trackEvent(userId, 'error_occurred', {
  errorType,
  errorMessage,
  stackTrace,
  component
});
```

## 4. Feature Flags Implementation
```typescript
// src/modules/posthog/services/posthog.service.ts
async isFeatureEnabled(
  featureKey: string,
  userId: string,
  defaultValue = false
): Promise<boolean> {
  if (!this.configService.get('posthog.enabled')) return defaultValue;
  
  return await this.client.isFeatureEnabled(featureKey, userId);
}
```

## 5. Integration Points

1. Create a middleware for automatic tracking:
```typescript
// src/modules/posthog/middleware/analytics.middleware.ts
@Injectable()
export class AnalyticsMiddleware implements NestMiddleware {
  constructor(private posthogService: PostHogService) {}

  use(req: Request, res: Response, next: Function) {
    const userId = req.user?.id;
    if (userId) {
      this.posthogService.trackEvent(userId, 'page_view', {
        path: req.path,
        method: req.method
      });
    }
    next();
  }
}
```

2. Add to existing activity tracking:
```typescript
// src/modules/activity-logging/middleware/activity-tracking.middleware.ts
// Add PostHog tracking alongside existing MongoDB tracking
```

## 6. Performance Monitoring

Create monitoring decorators:
```typescript
// src/modules/posthog/decorators/track-performance.decorator.ts
export function TrackPerformance() {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;
    descriptor.value = async function (...args: any[]) {
      const start = performance.now();
      const result = await originalMethod.apply(this, args);
      const duration = performance.now() - start;
      
      this.posthogService.trackEvent('performance', {
        operation: propertyKey,
        duration,
        success: true
      });
      
      return result;
    };
    return descriptor;
  };
}
```

## Migration Strategy

1. Phase 1: Parallel Tracking
- Keep existing analytics
- Add PostHog tracking
- Compare data between systems

2. Phase 2: PostHog Primary
- Make PostHog primary analytics system
- Keep MongoDB analytics as backup
- Migrate historical data if needed

3. Phase 3: Full Migration
- Remove duplicate tracking
- Use PostHog as single source of truth
- Archive old analytics data

## Implementation Steps

1. Install dependencies
2. Create PostHog module and configuration
3. Implement PostHog service
4. Add middleware for automatic tracking
5. Integrate with existing activity logging
6. Add performance monitoring
7. Test in development environment
8. Deploy and monitor in production
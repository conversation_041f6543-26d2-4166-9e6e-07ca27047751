# Precedent Analysis API

## Feature Description

The Precedent Analysis feature provides comprehensive analysis of legal precedents cited in documents. It identifies citations, calculates their relevance to the document, categorizes them by legal domain, assesses their impact (positive, negative, or neutral), and generates recommendations. This can be performed using rule-based methods or advanced AI-driven analysis, with rules serving as a fallback. The feature also identifies related cases for each precedent, providing a deeper understanding of the legal context.

The system includes robust factual accuracy safeguards that automatically detect when minimal or incomplete data is available from legal citation sources. When data completeness is limited, the analysis will include appropriate warnings to ensure that users are aware of potential limitations in the factual accuracy of the analysis.

## API Endpoint

### Analyze Precedents

- **Path:** `/api/documents/precedents/:documentId/analyze`
- **Method:** `POST`
- **Required Feature:** `precedent_analysis`
- **Authentication:** JWT token required in Authorization header

#### Request Payload

```json
{
  "includeRecommendations": true,
  "maxRelatedCases": 5,
  "minRelevanceScore": 0.3,
  "categorize": true,
  "assessImpact": true,
  "useAIAnalysis": false,
  "aiFocus": {
    "prioritizeImpact": false,
    "detailLevel": "standard"
  }
}
```

All fields are optional:

| Field                    | Type    | Description                                                                                                |
| ------------------------ | ------- | ---------------------------------------------------------------------------------------------------------- |
| `includeRecommendations` | boolean | Whether to include recommendations (primarily for rule-based analysis)                                   |
| `maxRelatedCases`        | number  | Maximum number of related cases to return for each precedent (1-20)                                        |
| `minRelevanceScore`      | number  | Minimum rule-based relevance score for including a precedent (0.0-1.0). Not applied if AI provides a score.|
| `categorize`             | boolean | Whether to perform rule-based categorization if AI analysis is not used or fails.                          |
| `assessImpact`           | boolean | Whether to perform rule-based impact assessment if AI analysis is not used or fails.                       |
| `useAIAnalysis`          | boolean | Master switch to enable AI-driven analysis for relevance, impact, and category. Defaults to `false`.       |
| `aiFocus`                | object  | Fine-tune AI analysis behavior (only applicable if `useAIAnalysis` is `true`).                           |
| `aiFocus.prioritizeImpact`| boolean | Instructs AI to place special emphasis on impact assessment.                                               |
| `aiFocus.detailLevel`    | string  | Desired level of detail for AI reasoning ('concise', 'standard', 'detailed'). Defaults to 'standard'.    |

#### Response

```json
[
  {
    "citation": "347 U.S. 483",
    "relevanceScore": 0.95,
    "impact": "positive",
    "category": "Constitutional Law",
    "keyPoints": [
      { "text": "Case name: Brown v. Board of Education", "type": "fact" },
      { "text": "Court: Supreme Court of the United States", "type": "fact" },
      { "text": "Held that state-sponsored segregation in public schools was unconstitutional.", "type": "holding" }
    ],
    "recommendation": "AI Analysis: This case is highly relevant and directly supports arguments against segregation based on the Equal Protection Clause.",
    "relatedCases": [
      {
        "caseName": "Plessy v. Ferguson",
        "citation": "163 U.S. 537",
        "court": "Supreme Court of the United States",
        "year": 1896,
        "relevance": 0.78,
        "relationship": "overruled",
        "summary": "Established the 'separate but equal' doctrine..."
      }
    ],
    "aiAnalysisDetails": {
      "reasoning": "The AI assessed the document context discussing equal rights and found a strong thematic overlap with the core principles of Brown v. Board, particularly its stance on overturning 'separate but equal'.",
      "source": "AI"
    },
    "factualAccuracy": {
      "level": "complete",
      "warning": null
    }
  },
  {
    "citation": "410 U.S. 113",
    "relevanceScore": 0.42,
    "impact": "neutral",
    "category": "Constitutional Law",
    "keyPoints": [
      { "text": "Case name: Roe v. Wade" },
      { "text": "Court: Supreme Court of the United States" }
    ],
    "recommendation": "This case has limited relevance to your current legal matter.",
    "relatedCases": [],
    "aiAnalysisDetails": {
        "source": "RuleBasedHybrid" 
    }
  },
  {
    "citation": "528 U.S. 495",
    "relevanceScore": 0.35,
    "impact": "neutral",
    "category": "Constitutional Law",
    "keyPoints": [
      { "text": "Citation: 528 U.S. 495" },
      { "text": "CAUTION: Limited data available for this case" }
    ],
    "recommendation": "This case appears to have some relevance to your matter, but due to limited data availability, specific claims about this precedent should be independently verified.",
    "relatedCases": [],
    "aiAnalysisDetails": {
        "source": "AI",
        "reasoning": "Based on the limited available information, this case appears to have some relevance to the constitutional questions discussed in your document."
    },
    "factualAccuracy": {
      "level": "minimal",
      "warning": "MINIMAL DATA AVAILABLE - Facts should be independently verified. The AI should not make specific factual claims about this case beyond what is explicitly provided."
    }
  }
]
```

The response is an array of precedent analysis results, each containing:

| Field               | Type     | Description                                                                                                |
| ------------------- | -------- | ---------------------------------------------------------------------------------------------------------- |
| `citation`          | string   | The citation text identified in the document                                                               |
| `relevanceScore`    | number   | Score indicating the relevance (0.0-1.0). Can be AI-driven or rule-based.                                |
| `impact`            | string   | Assessment of impact: "positive", "negative", "neutral", or "unknown". Can be AI-driven or rule-based. |
| `category`          | string   | Legal domain category. Can be AI-driven or rule-based.                                                     |
| `keyPoints`         | object[] | Array of key points (objects with `text` and optional `type`) about the precedent.                         |
| `recommendation`    | string   | Generated recommendation. Can include AI reasoning if AI analysis was used.                                |
| `relatedCases`      | object[] | Array of related cases for this precedent.                                                                 |
| `aiAnalysisDetails` | object   | Optional. Provides details if AI analysis was used. Contains `reasoning` (string) and `source` ('AI' or 'RuleBasedHybrid'). |
| `factualAccuracy`   | object   | Optional. Present when data completeness issues are detected. Contains `level` (string: 'minimal', 'limited', or 'complete') and `warning` (string) fields. |

Each related case contains:

| Field          | Type   | Description                                                                                                           |
| -------------- | ------ | --------------------------------------------------------------------------------------------------------------------- |
| `caseName`     | string | Name of the related case                                                                                              |
| `citation`     | string | Citation of the related case                                                                                          |
| `court`        | string | Court that decided the related case                                                                                   |
| `year`         | number | Year the related case was decided                                                                                     |
| `relevance`    | number | Relevance score of the related case to the original precedent (0.0-1.0)                                               |
| `relationship` | string | Relationship between the related case and the original precedent (e.g., "cites", "citedBy", "overruled", "companion") |
| `summary`      | string | Brief summary of the related case                                                                                     |

## Factual Accuracy Warnings

The system automatically detects when citation data from legal sources is incomplete and provides appropriate warnings. These warnings are included in the analysis to ensure that users are aware of potential limitations in the factual accuracy of the analysis.

There are three levels of factual accuracy warnings:

1. **Minimal Data Available**: When very little data is available about a case, the system will include a warning that facts should be independently verified and that the AI should not make specific factual claims beyond what is explicitly provided.

2. **Limited Data Available**: When some data is available but still incomplete, the system will include a note that facts should be treated with caution.

3. **Complete Data**: When comprehensive data is available, no warnings are included.

These warnings help ensure that the AI-driven analysis is appropriately cautious when working with incomplete data, providing more reliable and trustworthy legal analysis.

## Error Responses

- **401 Unauthorized:** Invalid or missing JWT token
- **403 Forbidden:** User doesn't have access to the precedent analysis feature
- **404 Not Found:** Document with the specified ID not found
- **400 Bad Request:** Invalid request parameters

## Example Usage (PowerShell)

```powershell
$body = @{
  includeRecommendations = $true
  categorize = $true
  assessImpact = $true
  maxRelatedCases = 3
  minRelevanceScore = 0.2
  useAIAnalysis = $true
  aiFocus = @{
    detailLevel = "concise"
  }
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:4000/api/documents/precedents/18ef0b61-460f-4761-b4ae-12aebda2520a/analyze" `
  -Headers @{
    "Authorization"="Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...";
    "Content-Type"="application/json"
  } `
  -Method POST `
  -Body $body

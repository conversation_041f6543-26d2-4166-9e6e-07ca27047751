# Clause Library Frontend Implementation Guide

## Overview

The Clause Library feature allows users to create, manage, and identify standard legal clauses in documents. This guide provides implementation details for the frontend components.

## API Endpoints

### 1. Create Clause Template

```http
POST /api/documents/clause-library/templates
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Template Name",
  "content": "Clause content...",
  "category": "Category Name",
  "tags": ["tag1", "tag2"],
  "isPublic": boolean
}
```

Response:

```json
{
  "id": "template-id",
  "name": "Template Name",
  "content": "Clause content...",
  "category": "Category Name",
  "tags": ["tag1", "tag2"],
  "isPublic": true,
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```

### 2. Identify Clauses in Document

```http
POST /api/documents/clause-library/identify
Authorization: Bearer {token}
Content-Type: application/json

{
  "documentId": "doc-id",      // ID of the document to analyze
  "categories": ["category1", "category2"],  // Optional categories to filter by (supports partial matching)
  "similarityThreshold": 0.5,  // Optional similarity threshold (0.0-1.0)
  "matchingPreferences": {     // Optional advanced matching preferences
    "ignoreNumbering": true,   // Whether to ignore section numbering in document content
    "useKeywordMatching": true, // Whether to use keyword-based matching as an alternative approach
    "handleSpecialFormats": true, // Whether to handle special formats like JSON or markdown
    "boostSimilarContent": true  // Whether to boost similarity scores for semantically similar content
  }
}
```

Note: The document content will be retrieved automatically using the provided documentId.

Response:

```json
[
  {
    "name": "Clause Name",
    "content": "Matched content...",
    "category": "Category",
    "similarity": 0.75,
    "templateId": "template-id",
    "startIndex": 120,
    "endIndex": 350
  }
]
```

#### Enhanced Category Matching

The API now supports improved category matching:

- **Partial Matching**: Categories are matched using substring matching. For example, a request with category "IP" will match templates with category "Intellectual Property" and vice versa.
- **Case-Insensitive**: Category matching is case-insensitive.

#### Advanced Similarity Calculation

The similarity calculation has been enhanced with the following features:

- **Section Numbering Removal**: Document content is preprocessed to remove section numbering (e.g., "5.1", "5.2") for better matching with templates that don't include numbering.
- **Keyword-Based Matching**: In addition to diff-based similarity, the system uses keyword-based matching to identify similar clauses, which is particularly effective for structured content.
- **Special Format Handling**: Templates with JSON or markdown formatting are handled specially to extract and match their plain text content.
- **Boosted Matching**: The system applies boost factors to improve matching likelihood for semantically similar content with different formatting.

### 3. Generate Template from Document

```http
POST /api/documents/clause-library/generate-template
Authorization: Bearer {token}
Content-Type: application/json

{
  "documentContent": "Clause text...",
  "name": "New Template Name",
  "category": "Category",
  "tags": ["tag1", "tag2"]
}
```

### 4. List Templates

```http
GET /api/documents/clause-library/templates
Authorization: Bearer {token}
```

## Frontend Components

### 1. Clause Template Management

```typescript
// types.ts
interface ClauseTemplate {
  id: string;
  name: string;
  content: string;
  category: string;
  tags: string[];
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  usageCount: number;
  effectivenessScore: number;
}

// ClauseTemplateList.tsx
const ClauseTemplateList = () => {
  const [templates, setTemplates] = useState<ClauseTemplate[]>([]);
  
  useEffect(() => {
    const fetchTemplates = async () => {
      const response = await fetch('/api/documents/clause-library/templates', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      const data = await response.json();
      setTemplates(data);
    };
    
    fetchTemplates();
  }, []);

  return (
    <div>
      {templates.map(template => (
        <TemplateCard key={template.id} template={template} />
      ))}
    </div>
  );
};
```

### 2. Clause Identification Component

```typescript
interface ClauseMatch {
  name: string;
  content: string;
  category: string;
  similarity: number;
  templateId: string;
  startIndex: number;
  endIndex: number;
}

const ClauseIdentifier = ({ documentContent }: { documentContent: string }) => {
  const [matches, setMatches] = useState<ClauseMatch[]>([]);

  const identifyClauses = async (documentId: string) => {
    const response = await fetch('/api/documents/clause-library/identify', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        documentId,
        categories: ['Confidentiality', 'Term', 'Termination'],
        similarityThreshold: 0.5
      })
    });

    const matches = await response.json();
    setMatches(matches);
  };

  return (
    <div>
      <button onClick={identifyClauses}>Identify Clauses</button>
      <MatchedClausesList matches={matches} />
    </div>
  );
};
```

### 3. Template Generation Form

```typescript
const TemplateGenerationForm = () => {
  const [formData, setFormData] = useState({
    name: '',
    content: '',
    category: '',
    tags: [] as string[]
  });

  const generateTemplate = async () => {
    const response = await fetch('/api/documents/clause-library/generate-template', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    });

    const template = await response.json();
    // Handle the newly generated template
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="text"
        placeholder="Template Name"
        value={formData.name}
        onChange={e => setFormData({...formData, name: e.target.value})}
      />
      <textarea
        placeholder="Clause Content"
        value={formData.content}
        onChange={e => setFormData({...formData, content: e.target.value})}
      />
      {/* Add other form fields */}
    </form>
  );
};
```

## UI/UX Recommendations

1. Template List View
   - Display templates in a grid or list format
   - Show key information: name, category, usage count
   - Add filters for categories and tags
   - Include search functionality

2. Clause Identification View
   - Highlight matched clauses in the document text
   - Show similarity scores and match details
   - Allow quick template creation from matched text
   - Provide confidence indicators for matches

3. Template Generation
   - Step-by-step form for template creation
   - Preview generated template
   - Category and tag suggestions
   - Validation for required fields

4. Template Management
   - Enable template editing
   - Show usage statistics
   - Allow public/private toggle
   - Implement batch operations

## Error Handling

```typescript
const handleApiError = (error: any) => {
  if (error.status === 403) {
    // Handle subscription/feature availability errors
    return "This feature is not available in your current plan";
  }
  if (error.status === 401) {
    // Handle authentication errors
    return "Please log in to access this feature";
  }
  // Handle other errors
  return "An error occurred. Please try again.";
};
```

## State Management

Consider using a state management solution (e.g., Redux, Zustand) to handle:

- Template cache
- Current document state
- Identified clauses
- User preferences

Example Redux slice:
```typescript
const clauseLibrarySlice = createSlice({
  name: 'clauseLibrary',
  initialState: {
    templates: [],
    currentMatches: [],
    loading: false,
    error: null
  },
  reducers: {
    setTemplates: (state, action) => {
      state.templates = action.payload;
    },
    setMatches: (state, action) => {
      state.currentMatches = action.payload;
    },
    // Add other reducers
  }
});
```

## Performance Considerations

### 1. Template List

- Implement pagination
- Cache templates locally
- Debounce search/filter operations

### 2. Clause Identification

- Debounce API calls
- Show loading states for long operations
- Cache recent results

### 3. Document Rendering

- Virtual scrolling for large documents
- Efficient highlighting of matched clauses
- Progressive loading of content

## Testing

Example test cases:
```typescript
describe('ClauseTemplateList', () => {
  it('should fetch and display templates', async () => {
    // Test implementation
  });

  it('should handle template filtering', () => {
    // Test implementation
  });
});

describe('ClauseIdentifier', () => {
  it('should identify clauses in document', async () => {
    // Test implementation
  });

  it('should display matched clauses correctly', () => {
    // Test implementation
  });
});
```
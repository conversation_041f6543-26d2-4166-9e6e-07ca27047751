# DocGic API Deployment Guide - Render.com

This guide walks you through deploying the DocGic API to Render.com using the included `render.yaml` configuration file.

## 🚀 Quick Start

1. **Push your code** to GitHub (including the `render.yaml` file)
2. **Connect to Render** and link your repository
3. **Set environment variables** (see below)
4. **Deploy** and your API will be live!

## 📋 Prerequisites

- GitHub repository with your code
- Render.com account (free tier available)
- Stripe account for payment processing
- Google OAuth credentials (optional)
- OpenAI/Anthropic API keys

## 🔧 Environment Variables Setup

### Required Variables (Set in Render Dashboard)

Navigate to your service in Render Dashboard → Environment tab and add:

#### **Authentication & Security**
```bash
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
```

#### **AI Services**
```bash
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
```

#### **Stripe Configuration**
```bash
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Stripe Product Price IDs (from running setup:stripe)
STRIPE_PLAN_PRO=price_your_pro_plan_id
STRIPE_PLAN_ADMIN=price_your_admin_plan_id
STRIPE_CREDIT_STUDENT=price_your_student_credit_id
STRIPE_CREDIT_LAWYER_SMALL=price_your_lawyer_small_credit_id
STRIPE_CREDIT_LAWYER_LARGE=price_your_lawyer_large_credit_id
STRIPE_CREDIT_FIRM_STANDARD=price_your_firm_standard_credit_id
STRIPE_CREDIT_FIRM_ENTERPRISE=price_your_firm_enterprise_credit_id
```

#### **Analytics**
```bash
POSTHOG_API_KEY=your_posthog_api_key
```

#### **Email (Optional)**
```bash
EMAIL_HOST=smtp.gmail.com
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
EMAIL_FROM=<EMAIL>
```

#### **Redis (Optional - for caching)**
```bash
REDIS_URL=redis://your-redis-url:6379
```

#### **Frontend Configuration**
```bash
CORS_ORIGIN=https://your-frontend-domain.com
FRONTEND_URL=https://your-frontend-domain.com
```

### Auto-Generated Variables

These are automatically generated by Render:
- `JWT_SECRET` - JWT signing secret
- `SESSION_SECRET` - Session encryption secret
- `WEBHOOK_SECRET` - General webhook secret
- `ENCRYPTION_KEY` - Data encryption key

## 🗄️ Database Setup

The `render.yaml` automatically provisions:

1. **MongoDB** - Primary database
2. **Redis** - Caching and sessions (optional)

MongoDB connection string is automatically injected as `MONGODB_URI`.

## 📦 Deployment Steps

### Step 1: Repository Setup
1. Ensure your code is pushed to GitHub
2. Verify `render.yaml` is in the root directory
3. Check that `package.json` has correct scripts

### Step 2: Create Render Service
1. Go to [Render Dashboard](https://dashboard.render.com)
2. Click "New +" → "Blueprint"
3. Connect your GitHub repository
4. Render will automatically detect the `render.yaml` file

### Step 3: Configure Environment Variables
1. In Render Dashboard, go to your service
2. Click "Environment" tab
3. Add all required environment variables listed above
4. Click "Save Changes"

### Step 4: Deploy
1. Render will automatically start the deployment
2. Monitor the build logs for any errors
3. Once deployed, test your API endpoints

## 🔧 Stripe Setup for Production

Run these commands locally to set up Stripe products:

```bash
# Set your live Stripe secret key
export STRIPE_SECRET_KEY=sk_live_your_key_here

# Create all Stripe products and prices
npm run setup:stripe

# Validate the setup
npm run validate:stripe
```

Copy the generated price IDs to your Render environment variables.

## 🔗 Webhook Configuration

### Stripe Webhooks
1. Go to [Stripe Dashboard](https://dashboard.stripe.com) → Webhooks
2. Add endpoint: `https://your-render-app.onrender.com/api/subscriptions/webhook`
3. Select events:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
4. Copy the webhook secret to `STRIPE_WEBHOOK_SECRET`

### Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Navigate to APIs & Services → Credentials
3. Update OAuth 2.0 Client:
   - Authorized JavaScript origins: `https://your-frontend-domain.com`
   - Authorized redirect URIs: `https://your-render-app.onrender.com/api/auth/google/callback`

## 📊 Monitoring & Health Checks

### Health Check Endpoints
- **General**: `https://your-app.onrender.com/health`
- **API**: `https://your-app.onrender.com/api/health`
- **Database**: `https://your-app.onrender.com/api/health/mongodb`

### API Documentation
Once deployed, access Swagger docs at:
`https://your-app.onrender.com/api/docs`

## 🛠️ Troubleshooting

### Common Issues

#### Build Failures
```bash
# Check your package.json scripts
npm run build  # Should work locally
npm run start:prod  # Should work after build
```

#### Environment Variables
- Verify all required variables are set
- Check for typos in variable names
- Ensure Stripe price IDs are correct

#### Database Connection
- MongoDB URI is auto-generated
- Check database module configuration
- Verify connection in health check endpoint

#### File Uploads
- Render uses ephemeral storage
- Files are stored in `/tmp/uploads`
- Consider using cloud storage for persistence

### Logs and Debugging
1. Go to Render Dashboard → Your Service → Logs
2. Monitor real-time logs during deployment
3. Check health check endpoints after deployment

## 📈 Scaling Configuration

The `render.yaml` is configured for:
- **Minimum instances**: 1
- **Maximum instances**: 3
- **Auto-scaling**: Based on CPU (80%) and Memory (80%)
- **Disk space**: 5GB for temporary files

## 🔐 Security Considerations

1. **Environment Variables**: All secrets are encrypted at rest
2. **HTTPS**: Automatically enabled on Render
3. **CORS**: Configured for your frontend domain
4. **Rate Limiting**: Built into the application
5. **JWT Tokens**: Secure secret auto-generated

## 📱 Frontend Integration

Update your frontend environment variables:
```bash
# Frontend .env
REACT_APP_BACKEND_URL=https://your-render-app.onrender.com
REACT_APP_STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_key
```

## 🎯 Post-Deployment Checklist

- [ ] API health check responds correctly
- [ ] Database connection is established
- [ ] Stripe webhooks are configured
- [ ] Google OAuth is working
- [ ] Frontend can connect to API
- [ ] File uploads are working
- [ ] Email notifications are sent
- [ ] Analytics are being tracked

## 🚨 Important Notes

1. **Cold Starts**: Free tier may have cold starts after 15 minutes of inactivity
2. **File Storage**: Files in `/tmp` are ephemeral and cleared on restart
3. **Database Backups**: MongoDB backups are handled automatically
4. **SSL**: HTTPS is automatically configured
5. **Custom Domains**: Available on paid plans

## 🔄 Continuous Deployment

Render automatically deploys when you push to your main branch. To deploy:

```bash
git add .
git commit -m "Deploy to production"
git push origin main
```

Your app will automatically redeploy on Render.

## 📞 Support

If you encounter issues:
1. Check the Render Dashboard logs
2. Test health check endpoints
3. Verify environment variables
4. Check Stripe webhook configuration
5. Review Google OAuth settings

Your DocGic API should now be live at `https://your-app-name.onrender.com`! 🎉 
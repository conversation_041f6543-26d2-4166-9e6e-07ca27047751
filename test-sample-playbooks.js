const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/legal-document-analyzer', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

// Define the schema
const negotiationStrategySchema = new mongoose.Schema({
  section: { type: String, required: true },
  recommendations: { type: [String], required: true },
  riskLevel: { type: String, enum: ['low', 'medium', 'high'], required: true },
  priority: { type: Number, required: true },
  alternativeLanguage: { type: String },
  simulationScenarios: [{
    type: { type: String, enum: ['concession', 'leverage', 'dealbreaker', 'compromise'] },
    trigger: { type: String },
    responseStrategy: { type: String },
    expectedOutcome: { type: String }
  }]
});

const negotiationPlaybookSchema = new mongoose.Schema({
  documentId: { type: String, required: true },
  strategies: { type: [negotiationStrategySchema], required: true },
  overallAssessment: { type: String, required: true },
  keyLeveragePoints: { type: [String], required: true },
  dealBreakers: { type: [String] },
  timestamp: { type: Date, required: true },
  organizationId: { type: String },
  userId: { type: String },
  isTemplate: { type: Boolean, default: false },
  templateName: { type: String },
  templateDescription: { type: String },
  contractType: { type: String },
  industry: { type: String },
  difficulty: { type: String },
  tags: { type: [String] },
  usageCount: { type: Number, default: 0 }
}, { timestamps: true });

const NegotiationPlaybook = mongoose.model('NegotiationPlaybook', negotiationPlaybookSchema);

// Sample data
const samplePlaybooks = [
  {
    documentId: 'template-service-agreement-beginner',
    templateName: 'Service Agreement - Basic Negotiation',
    templateDescription: 'Essential negotiation strategies for standard service agreements',
    contractType: 'service_agreement',
    industry: 'general',
    difficulty: 'beginner',
    isTemplate: true,
    tags: ['service', 'basic', 'beginner'],
    strategies: [
      {
        section: 'Payment Terms',
        recommendations: [
          'Negotiate for shorter payment cycles (Net 15 instead of Net 30)',
          'Include late payment penalties (1.5% per month)',
          'Request partial upfront payment for large projects'
        ],
        riskLevel: 'low',
        priority: 1,
        alternativeLanguage: 'Payment shall be due within fifteen (15) days of invoice date. Late payments shall incur a penalty of 1.5% per month.',
        simulationScenarios: [
          {
            type: 'concession',
            trigger: 'Client requests Net 45 payment terms',
            responseStrategy: 'Offer Net 30 with early payment discount of 2%',
            expectedOutcome: 'Agreement on Net 30 with early payment incentive'
          }
        ]
      },
      {
        section: 'Scope of Work',
        recommendations: [
          'Define deliverables with specific, measurable outcomes',
          'Include change order process for scope modifications',
          'Set clear boundaries on what is included vs. additional services'
        ],
        riskLevel: 'medium',
        priority: 2,
        alternativeLanguage: 'Services shall be limited to those specifically described in Exhibit A. Any additional services require written approval and separate compensation.',
        simulationScenarios: [
          {
            type: 'dealbreaker',
            trigger: 'Client wants unlimited revisions included',
            responseStrategy: 'Propose specific number of revisions (e.g., 3) with additional revisions at hourly rate',
            expectedOutcome: 'Agreement on limited revisions with clear additional cost structure'
          }
        ]
      }
    ],
    overallAssessment: 'Service agreements require clear scope definition and favorable payment terms. Focus on protecting your time and ensuring prompt payment.',
    keyLeveragePoints: [
      'Specialized expertise and skills',
      'Timeline flexibility',
      'Quality of deliverables'
    ],
    dealBreakers: [
      'Unlimited liability exposure',
      'Work-for-hire clauses for proprietary methods',
      'Non-compete restrictions that limit future business'
    ],
    timestamp: new Date(),
    usageCount: 0
  },
  {
    documentId: 'template-employment-contract-intermediate',
    templateName: 'Employment Contract - Professional Level',
    templateDescription: 'Advanced negotiation tactics for employment agreements',
    contractType: 'employment_contract',
    industry: 'general',
    difficulty: 'intermediate',
    isTemplate: true,
    tags: ['employment', 'salary', 'benefits'],
    strategies: [
      {
        section: 'Compensation Package',
        recommendations: [
          'Negotiate total compensation including base, bonus, and equity',
          'Request performance-based salary reviews (annually)',
          'Include cost-of-living adjustments for multi-year contracts'
        ],
        riskLevel: 'medium',
        priority: 1,
        alternativeLanguage: 'Base salary shall be reviewed annually with adjustments based on performance and market conditions, with minimum 3% cost-of-living increase.',
        simulationScenarios: [
          {
            type: 'leverage',
            trigger: 'Employer offers below market rate',
            responseStrategy: 'Present market research data and emphasize unique value proposition',
            expectedOutcome: 'Salary increase to market rate plus performance bonus structure'
          }
        ]
      }
    ],
    overallAssessment: 'Employment contracts require balancing job security with performance expectations. Focus on total compensation and protection against unfair termination.',
    keyLeveragePoints: [
      'Specialized skills and experience',
      'Market demand for your expertise',
      'Competing job offers'
    ],
    dealBreakers: [
      'Excessive non-compete restrictions',
      'No severance protection',
      'Below-market compensation with no growth path'
    ],
    timestamp: new Date(),
    usageCount: 0
  },
  {
    documentId: 'template-nda-beginner',
    templateName: 'Non-Disclosure Agreement - Essentials',
    templateDescription: 'Key negotiation points for standard NDAs',
    contractType: 'nda',
    industry: 'general',
    difficulty: 'beginner',
    isTemplate: true,
    tags: ['nda', 'confidentiality', 'basic'],
    strategies: [
      {
        section: 'Definition of Confidential Information',
        recommendations: [
          'Ensure mutual confidentiality (both parties protected)',
          'Exclude publicly available information from definition',
          'Include specific carve-outs for independently developed information'
        ],
        riskLevel: 'medium',
        priority: 1,
        alternativeLanguage: 'Confidential Information shall not include information that: (a) is publicly available, (b) was known prior to disclosure, or (c) is independently developed.',
        simulationScenarios: [
          {
            type: 'concession',
            trigger: 'Other party wants one-way NDA only',
            responseStrategy: 'Explain mutual benefit and propose reciprocal terms',
            expectedOutcome: 'Agreement on mutual NDA with balanced protections'
          }
        ]
      }
    ],
    overallAssessment: 'NDAs should provide balanced protection while allowing normal business operations. Focus on mutual terms and reasonable time limits.',
    keyLeveragePoints: [
      'Mutual need for confidentiality protection',
      'Standard industry practices',
      'Reciprocal business relationship'
    ],
    dealBreakers: [
      'Perpetual confidentiality obligations',
      'Overly broad definition of confidential information',
      'One-way protection favoring other party'
    ],
    timestamp: new Date(),
    usageCount: 0
  }
];

// Seed function
async function seedSamplePlaybooks() {
  try {
    console.log('Checking for existing sample playbooks...');
    const existingCount = await NegotiationPlaybook.countDocuments({ isTemplate: true });
    
    if (existingCount > 0) {
      console.log(`Found ${existingCount} existing sample playbooks. Skipping seeding.`);
      return;
    }

    console.log('Seeding sample playbooks...');
    
    for (const playbook of samplePlaybooks) {
      await NegotiationPlaybook.create(playbook);
      console.log(`Created sample playbook: ${playbook.templateName}`);
    }

    console.log(`Successfully seeded ${samplePlaybooks.length} sample negotiation playbooks`);
  } catch (error) {
    console.error('Error seeding sample playbooks:', error);
  } finally {
    mongoose.connection.close();
  }
}

// Run the seeding
seedSamplePlaybooks();

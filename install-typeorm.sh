#!/bin/bash

# Stop any running processes
echo "Stopping running processes..."
killall node 2>/dev/null

# Install dependencies
echo "Installing TypeORM and related packages..."
npm install --save @nestjs/typeorm typeorm sqlite3 @nestjs/config class-validator class-transformer

# Remove conflicting package
echo "Removing conflicting package..."
npm uninstall better-sqlite3

# Clear TypeScript compilation cache
echo "Clearing compilation cache..."
rm -rf dist/

# Rebuild the project
echo "Rebuilding project..."
npm run build

echo "Installation complete! Please restart your development server."
#!/bin/bash

# Test script for Chat Negotiation API
# Tests the conversation quality with multiple questions

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
BASE_URL="http://localhost:4000"
TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************.w84c2lUccYNBsvBw0Tv2caIOdCghSkJo-BdLY61IrXQ"
SESSION_ID="685403af6e777f7b2b14b20a"

echo -e "${BLUE}=== Chat Negotiation API Test Script ===${NC}"
echo -e "${YELLOW}Testing Session: $SESSION_ID${NC}"
echo ""

# Function to send a negotiation message and extract AI response
send_message() {
    local message="$1"
    local strategy="$2"
    local sentiment="$3"
    local question_num="$4"
    
    echo -e "${BLUE}Question $question_num:${NC} $message"
    echo -e "${YELLOW}Strategy: $strategy | Sentiment: $sentiment${NC}"
    
    response=$(curl -s -X POST "$BASE_URL/api/chat-negotiation/sessions/$SESSION_ID/moves" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        --data-raw "{\"content\":\"$message\",\"extractedData\":{\"strategy\":\"$strategy\",\"sentiment\":\"$sentiment\"}}")
    
    # Check if request was successful
    if echo "$response" | grep -q "statusCode"; then
        echo -e "${RED}❌ Error: $(echo "$response" | jq -r '.message // .error // "Unknown error"')${NC}"
        return 1
    fi
    
    # Extract AI response
    ai_response=$(echo "$response" | jq -r '.aiResponse.content // "No response"')
    round=$(echo "$response" | jq -r '.sessionUpdate.currentRound // "Unknown"')
    score=$(echo "$response" | jq -r '.sessionUpdate.score // "Unknown"')
    
    echo -e "${GREEN}✅ AI Response:${NC} $ai_response"
    echo -e "${YELLOW}Round: $round | Score: $score${NC}"
    echo ""
    echo "---"
    echo ""
    
    return 0
}

# Test Questions
echo -e "${BLUE}Starting Negotiation Test Sequence...${NC}"
echo ""

# Question 1: Opening concern about automatic renewal
send_message "I have concerns about the automatic renewal clause in this Turing contract. It seems risky for my business." "collaborative" "negative" "1"

sleep 2

# Question 2: Ask for specific offer
send_message "What's your final offer on the automatic renewal terms?" "competitive" "neutral" "2"

sleep 2

# Question 3: Ask about Deel platform
send_message "What can you offer me regarding the Deel platform payment requirements?" "collaborative" "positive" "3"

sleep 2

# Question 4: Challenge the non-solicitation clause
send_message "The 1-year non-solicitation clause is too restrictive. What's your best offer?" "competitive" "negative" "4"

sleep 2

# Question 5: Ask for final deal
send_message "Give me your absolute final offer for this entire contract." "competitive" "neutral" "5"

sleep 2

# Question 6: Try to close the deal
send_message "I'm willing to make a deal today if we can address the high-risk areas." "collaborative" "positive" "6"

echo -e "${GREEN}=== Test Sequence Complete ===${NC}"
echo ""

echo -e "${BLUE}=== Analysis Complete ===${NC}"

const { SamplePlaybooksDataService } = require('./src/modules/documents/services/negotiation-playbook-seeder.service');

// Test the updated sample playbooks
console.log('Testing updated sample playbooks with common DocumentType enum...');

try {
  const dataService = new SamplePlaybooksDataService();

  // Get all sample playbooks
  const allSamples = dataService.getSamplePlaybooks();
  console.log(`\nFound ${allSamples.length} sample playbooks:`);

  allSamples.forEach((sample, index) => {
    console.log(`${index + 1}. ${sample.templateName}`);
    console.log(`   Contract Type: ${sample.contractType}`);
    console.log(`   Industry: ${sample.industry}`);
    console.log(`   Difficulty: ${sample.difficulty}`);
    console.log(`   Tags: ${sample.tags?.join(', ')}`);
    console.log('');
  });

  // Test filtering by contract type using enum values
  console.log('\n--- Testing Filters ---');

  const serviceAgreements = dataService.getFilteredSamplePlaybooks({
    contractType: 'SERVICE_AGREEMENT'
  });
  console.log(`Service Agreements: ${serviceAgreements.length}`);

  const ndas = dataService.getFilteredSamplePlaybooks({
    contractType: 'NDA'
  });
  console.log(`NDAs: ${ndas.length}`);

  const employmentContracts = dataService.getFilteredSamplePlaybooks({
    contractType: 'EMPLOYMENT_CONTRACT'
  });
  console.log(`Employment Contracts: ${employmentContracts.length}`);

  const consultingAgreements = dataService.getFilteredSamplePlaybooks({
    contractType: 'CONSULTING_AGREEMENT'
  });
  console.log(`Consulting Agreements: ${consultingAgreements.length}`);

  const partnershipAgreements = dataService.getFilteredSamplePlaybooks({
    contractType: 'PARTNERSHIP_AGREEMENT'
  });
  console.log(`Partnership Agreements: ${partnershipAgreements.length}`);

  const beginnerLevel = dataService.getFilteredSamplePlaybooks({
    difficulty: 'beginner'
  });
  console.log(`Beginner level: ${beginnerLevel.length}`);

  // Test getting specific playbook
  const specificPlaybook = dataService.getSamplePlaybookById('template-service-agreement-beginner');
  console.log(`\nSpecific playbook found: ${specificPlaybook ? 'Yes' : 'No'}`);
  if (specificPlaybook) {
    console.log(`Name: ${specificPlaybook.templateName}`);
    console.log(`Strategies: ${specificPlaybook.strategies?.length || 0}`);
  }

  console.log('\n--- Contract Type Distribution ---');
  const contractTypes = new Map();
  allSamples.forEach(sample => {
    const type = sample.contractType;
    contractTypes.set(type, (contractTypes.get(type) || 0) + 1);
  });

  contractTypes.forEach((count, type) => {
    console.log(`${type}: ${count}`);
  });

} catch (error) {
  console.error('Error testing sample playbooks:', error.message);
  console.error(error.stack);
}

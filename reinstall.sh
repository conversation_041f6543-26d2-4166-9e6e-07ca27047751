#!/bin/bash

echo "Stopping any running processes..."
pkill -f "node"

echo "Cleaning project..."
rm -rf node_modules
rm -f package-lock.json
rm -rf dist

echo "Installing dependencies..."
npm install --save @nestjs/typeorm@10.0.1
npm install --save typeorm@0.3.20
npm install --save sqlite3@5.1.7
npm install --save class-validator@0.14.0
npm install --save class-transformer@0.5.1
npm install --save @nestjs/config@3.1.1
npm install --save reflect-metadata@0.1.13

echo "Installing all remaining dependencies..."
npm install

echo "Rebuilding the project..."
npm run build

echo "Starting the development server..."
npm run start:dev
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { Connection } from 'mongoose';
import { getConnectionToken } from '@nestjs/mongoose';

describe('Authentication (e2e)', () => {
  let app: INestApplication;
  let connection: Connection;
  let accessToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    
    connection = moduleFixture.get<Connection>(getConnectionToken());
    
    await app.init();
  });

  afterAll(async () => {
    await connection.dropDatabase();
    await connection.close();
    await app.close();
  });

  it('should register a new user', () => {
    return request(app.getHttpServer())
      .post('/auth/register')
      .send({
        username: 'testuser',
        password: 'Password123',
        email: '<EMAIL>',
      })
      .expect(201)
      .expect((res) => {
        expect(res.body).toHaveProperty('id');
        expect(res.body.username).toBe('testuser');
        expect(res.body.email).toBe('<EMAIL>');
        expect(res.body.role).toBe('user');
        expect(res.body).not.toHaveProperty('password');
      });
  });

  it('should not register a user with duplicate username', () => {
    return request(app.getHttpServer())
      .post('/auth/register')
      .send({
        username: 'testuser',
        password: 'Password123',
        email: '<EMAIL>',
      })
      .expect(400);
  });

  it('should login with registered user', () => {
    return request(app.getHttpServer())
      .post('/auth/login')
      .send({
        username: 'testuser',
        password: 'Password123',
      })
      .expect(200)
      .expect((res) => {
        expect(res.body).toHaveProperty('access_token');
        expect(res.body.user).toHaveProperty('id');
        expect(res.body.user.username).toBe('testuser');
        expect(res.body.user.role).toBe('user');
        accessToken = res.body.access_token;
      });
  });

  it('should not login with invalid credentials', () => {
    return request(app.getHttpServer())
      .post('/auth/login')
      .send({
        username: 'testuser',
        password: 'WrongPassword',
      })
      .expect(401);
  });

  it('should access protected profile route with JWT token', () => {
    return request(app.getHttpServer())
      .get('/profile')
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200)
      .expect((res) => {
        expect(res.body).toHaveProperty('userId');
        expect(res.body).toHaveProperty('username');
        expect(res.body).toHaveProperty('role');
        expect(res.body.username).toBe('testuser');
        expect(res.body.role).toBe('user');
      });
  });

  it('should not access protected route without JWT token', () => {
    return request(app.getHttpServer())
      .get('/profile')
      .expect(401);
  });
});

import * as request from 'supertest';
import { Test } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { AppModule } from '../src/app.module';
import { ConfigService } from '@nestjs/config';

describe('Context Management Feature Tests', () => {
  let app: INestApplication;
  let testDocumentId: string;
  let testSessionId: string;
  let relatedDocumentId: string;

  beforeAll(async () => {
    // Create the Nest.js test application
    const moduleRef = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(ConfigService)
      .useValue({
        get: jest.fn((key) => {
          // Mock configuration to enable context management
          if (key === 'contextManagement.enabled') return true;
          if (key === 'contextManagement.maxDocumentExcerpts') return 5;
          if (key === 'contextManagement.maxTokens') return 4000;
          if (key === 'contextManagement.useRateLimit') return true;
          return process.env[key];
        }),
      })
      .compile();

    app = moduleRef.createNestApplication();
    await app.init();

    // Upload a test document first to get documentId
    const createDocumentResponse = await request(app.getHttpServer())
      .post('/documents')
      .attach('file', __dirname + '/fixtures/sample-contract.pdf')
      .expect(201);

    testDocumentId = createDocumentResponse.body.id;
    console.log(`Test document created with ID: ${testDocumentId}`);

    // Upload a related document
    const relatedDocumentResponse = await request(app.getHttpServer())
      .post('/documents')
      .attach('file', __dirname + '/fixtures/sample-amendment.pdf')
      .expect(201);

    relatedDocumentId = relatedDocumentResponse.body.id;
    console.log(`Related document created with ID: ${relatedDocumentId}`);

    // Create a chat session for the document
    const createSessionResponse = await request(app.getHttpServer())
      .post('/chat/sessions')
      .send({ documentId: testDocumentId })
      .expect(201);

    testSessionId = createSessionResponse.body.id;
    console.log(`Test session created with ID: ${testSessionId}`);
  });

  afterAll(async () => {
    await app.close();
  });

  it('should send a message using standard context handling', async () => {
    // Send a message without related document IDs
    const messageResponse = await request(app.getHttpServer())
      .post('/chat/messages')
      .send({
        sessionId: testSessionId,
        content: 'What are the main terms of this contract?',
      })
      .expect(201);

    expect(messageResponse.body).toBeDefined();
    expect(messageResponse.body.id).toBeDefined();
    expect(messageResponse.body.role).toBe('assistant');
    expect(messageResponse.body.content).toBeTruthy();
    
    // Log the response for manual verification
    console.log('Standard context response:', messageResponse.body.content.substring(0, 100) + '...');
    
    // Check if the contextSources is defined but minimal
    expect(messageResponse.body.contextSources).toBeDefined();
    expect(messageResponse.body.contextSources.length).toBe(1);
  }, 30000);

  it('should send a message using enhanced context handling with related documents', async () => {
    // Send a message with related document IDs
    const enhancedMessageResponse = await request(app.getHttpServer())
      .post('/chat/messages')
      .send({
        sessionId: testSessionId,
        content: 'How does the amendment change the terms in the main contract?',
        relatedDocumentIds: [relatedDocumentId],
      })
      .expect(201);

    expect(enhancedMessageResponse.body).toBeDefined();
    expect(enhancedMessageResponse.body.id).toBeDefined();
    expect(enhancedMessageResponse.body.role).toBe('assistant');
    expect(enhancedMessageResponse.body.content).toBeTruthy();
    
    // Log the response for manual verification
    console.log('Enhanced context response:', enhancedMessageResponse.body.content.substring(0, 100) + '...');
    
    // Check if the contextSources has multiple sources (includes related documents)
    expect(enhancedMessageResponse.body.contextSources).toBeDefined();
    expect(enhancedMessageResponse.body.contextSources.length).toBeGreaterThan(1);
    
    // At least one context source should reference the related document
    const hasRelatedDocumentContext = enhancedMessageResponse.body.contextSources.some(
      source => source.documentId === relatedDocumentId
    );
    expect(hasRelatedDocumentContext).toBe(true);
  }, 30000);

  it('should check rate limiter utilization', async () => {
    // Get rate limiter stats
    const rateResponse = await request(app.getHttpServer())
      .get('/gemini/rate-limit')
      .expect(200);

    expect(rateResponse.body).toBeDefined();
    expect(rateResponse.body.utilizationPercentage).toBeDefined();
    expect(rateResponse.body.remainingTokens).toBeDefined();
    
    console.log('Rate limiter utilization:', rateResponse.body);
  });
});

import { ChatAnalyticsController } from '../src/modules/chat/controllers/chat-analytics.controller';

// Test session ID to use throughout tests
const TEST_SESSION_ID = 'test-session-id';

describe('ChatAnalyticsController', () => {
  let controller: ChatAnalyticsController;

  // Mock analytics response
  const mockAnalyticsResponse = {
    sessionId: TEST_SESSION_ID,
    analytics: {
      messages: {
        total: 2,
        userMessages: 1,
        aiMessages: 1,
        averageResponseTime: '100ms',
        averageMessageLength: 20,
      },
      threads: {
        total: 1,
        active: 1,
        averageMessages: 2,
        averageDuration: '300ms',
      },
      feedback: {
        helpful: 0,
        notHelpful: 0,
        rating: 0,
      },
      topTopics: [],
      mostAccessedDocuments: [],
    },
    lastUpdated: new Date().toISOString(),
  };

  // Create mock service with all required methods
  const mockAnalyticsService = {
    getAnalytics: jest.fn().mockImplementation((sessionId) => {
      if (sessionId === 'non-existent-id') {
        throw new Error(`No analytics found for session ${sessionId}`);
      }
      return Promise.resolve(mockAnalyticsResponse);
    }),
    updateUserFeedback: jest
      .fn()
      .mockImplementation(() => Promise.resolve(undefined)),
    updateCommonTopics: jest
      .fn()
      .mockImplementation(() => Promise.resolve(undefined)),
  };

  beforeEach(() => {
    controller = new ChatAnalyticsController(mockAnalyticsService as any);
    jest.clearAllMocks();
  });

  describe('getSessionAnalytics', () => {
    it('should return analytics for a session', async () => {
      const result = await controller.getSessionAnalytics(TEST_SESSION_ID);

      expect(result).toBe(mockAnalyticsResponse);
      expect(mockAnalyticsService.getAnalytics).toHaveBeenCalledWith(
        TEST_SESSION_ID,
      );
    });

    it('should throw error for non-existent session', async () => {
      await expect(
        controller.getSessionAnalytics('non-existent-id'),
      ).rejects.toThrow();
      expect(mockAnalyticsService.getAnalytics).toHaveBeenCalledWith(
        'non-existent-id',
      );
    });
  });

  describe('submitFeedback', () => {
    it('should update user feedback', async () => {
      const feedbackData = {
        isHelpful: true,
        rating: 5,
      };

      await controller.submitFeedback(TEST_SESSION_ID, feedbackData);

      expect(mockAnalyticsService.updateUserFeedback).toHaveBeenCalledWith(
        TEST_SESSION_ID,
        feedbackData.isHelpful,
        feedbackData.rating,
      );
    });
  });

  describe('updateTopics', () => {
    it('should update common topics', async () => {
      const topicsData = {
        topics: ['legal', 'contract', 'agreement'],
      };

      await controller.updateTopics(TEST_SESSION_ID, topicsData);

      expect(mockAnalyticsService.updateCommonTopics).toHaveBeenCalledWith(
        TEST_SESSION_ID,
        topicsData.topics,
      );
    });
  });
});

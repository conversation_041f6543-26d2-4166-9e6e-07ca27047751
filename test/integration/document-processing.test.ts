import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule, getConnectionToken } from '@nestjs/mongoose';
import * as path from 'path';
import * as fs from 'fs/promises';
import { Connection } from 'mongoose';
import { DocumentProcessingManagerService } from '../../src/modules/documents/services/document-processing-manager.service';
import { DocumentProcessingService } from '../../src/modules/documents/services/document-processing.service';
import { DocumentStorageService } from '../../src/modules/documents/services/document-storage.service';
import { LegalPatternRecognitionService } from '../../src/modules/documents/services/legal-pattern-recognition.service';
import { AIService } from '../../src/modules/ai/services/ai.service';
import { QueueModule } from '../../src/modules/queue/queue.module';
import { 
  Document as DocumentModel,
  DocumentSchema,
  LegalDocumentSchema,
  DOCUMENT_MODEL,
  LEGAL_DOCUMENT_MODEL 
} from '../../src/modules/documents/schemas/document.schema';

describe('Document Processing Integration', () => {
  let module: TestingModule;
  let processingManager: DocumentProcessingManagerService;
  let processingService: DocumentProcessingService;
  let connection: Connection;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot(),
        QueueModule,
        MongooseModule.forRoot(process.env.MONGODB_URI || 'mongodb://localhost/test-db'),
        MongooseModule.forFeature([
          { 
            name: DOCUMENT_MODEL, 
            schema: DocumentSchema 
          },
          { 
            name: LEGAL_DOCUMENT_MODEL, 
            schema: LegalDocumentSchema 
          }
        ]),
      ],
      providers: [
        DocumentProcessingManagerService,
        DocumentProcessingService,
        DocumentStorageService,
        {
          provide: AIService,
          useValue: {
            generateResponse: jest.fn().mockResolvedValue('Generated response'),
          },
        },
        {
          provide: LegalPatternRecognitionService,
          useValue: {
            analyzeDocument: jest.fn().mockResolvedValue({
              patterns: ['pattern1', 'pattern2'],
            }),
          },
        },
      ],
    }).compile();

    processingManager = module.get<DocumentProcessingManagerService>(DocumentProcessingManagerService);
    processingService = module.get<DocumentProcessingService>(DocumentProcessingService);
    connection = module.get(getConnectionToken());
  });

  afterAll(async () => {
    // Clean up test database
    await connection.dropDatabase();
    await connection.close();
    await module.close();
  });

  beforeEach(async () => {
    // Clean collections before each test
    if (!connection.db) {
      throw new Error('Database connection not initialized');
    }
    const collections = await connection.db.collections();
    await Promise.all(
      collections.map(collection => collection.deleteMany({}))
    );
  });

  it('should process a small document through the entire pipeline', async () => {
    // Create a test file
    const testContent = 'Test document content\nwith multiple lines\nand some patterns';
    const testFilePath = path.join(__dirname, 'test-document.txt');
    await fs.writeFile(testFilePath, testContent);

    try {
      // Test file chunking and processing
      const result = await processingManager.processFile(
        testFilePath,
        'text/plain',
        { chunkSize: 1024 }, // Small chunk size for testing
      );

      expect(result.text).toBe(testContent);
      expect(result.metadata).toBeDefined();
      expect(result.error).toBeUndefined();

      // Create mock Multer file
      const mockMulterFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test-document.txt',
        encoding: '7bit',
        mimetype: 'text/plain',
        size: testContent.length,
        destination: '/tmp',
        filename: 'test-document.txt',
        path: testFilePath,
        buffer: Buffer.from(testContent),
        stream: null as any,
      };

      // Save document and process it
      const savedDoc = await processingService.processUploadedFile(mockMulterFile);

      expect(savedDoc.id).toBeDefined();
      expect(savedDoc.content).toBe(testContent);
      expect(savedDoc.metadata.patterns).toEqual(['pattern1', 'pattern2']);

      // Test document queueing
      const processingResult = await processingService.processDocument(
        savedDoc.id,
        'test-user',
        {
          extractMetadata: true,
          generateSummary: true,
        },
      );

      expect(processingResult.jobId).toBeDefined();
      expect(processingResult.status).toBe('queued');
      expect(processingResult.documentId).toBe(savedDoc.id);

      // Cleanup
      await fs.unlink(testFilePath);
    } catch (error) {
      // Ensure cleanup even if test fails
      try {
        await fs.unlink(testFilePath);
      } catch {}
      throw error;
    }
  }, 30000); // 30s timeout for integration test

  it('should handle large files by processing in chunks', async () => {
    // Generate a moderate size test file (10MB) - enough to trigger chunking but not too large for tests
    const chunkSize = 1024 * 1024; // 1MB chunks
    const numberOfChunks = 10;
    const testFilePath = path.join(__dirname, 'large-test-document.txt');
    
    try {
      // Create file stream to avoid memory issues
      const fileHandle = await fs.open(testFilePath, 'w');
      
      // Write chunks of data
      for (let i = 0; i < numberOfChunks; i++) {
        // Generate chunk with repeating pattern and chunk number
        const chunkContent = `Chunk ${i}: ${'x'.repeat(chunkSize - 20)}\n`;
        await fileHandle.writeFile(chunkContent, { encoding: 'utf8' });
      }
      
      await fileHandle.close();

      // Process the large file with progress tracking
      let progress = 0;
      const progressCallback = (current: number, total: number) => {
        progress = (current / total) * 100;
      };

      const startTime = Date.now();
      const result = await processingManager.processFile(
        testFilePath,
        'text/plain',
        { 
          chunkSize: 5 * 1024 * 1024, // 5MB chunks for processing
          maxWorkers: 2, // Limit workers for test
          onProgress: progressCallback
        }
      );
      const processingTime = Date.now() - startTime;

      // Verify results
      expect(result.error).toBeUndefined();
      expect(result.metadata).toBeDefined();
      if (result.metadata) {
        expect(result.metadata.totalChunks).toBeGreaterThan(1);
        expect(result.metadata.mergedContentLength).toBe(numberOfChunks * chunkSize);
        expect(result.metadata.processingTimeMs).toBe(processingTime);
        expect(result.metadata.chunksProcessed).toBe(result.metadata.totalChunks);
      }

      // Verify all chunks were processed
      expect(progress).toBe(100);
      
      // Test error handling by trying to process a non-existent file
      try {
        await processingManager.processFile(
          'non-existent-file.txt',
          'text/plain',
          { chunkSize: 5 * 1024 * 1024 }
        );
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.message).toContain('ENOENT');
      }
      
      // Verify memory usage stayed reasonable
      const memUsage = process.memoryUsage();
      expect(memUsage.heapUsed).toBeLessThan(500 * 1024 * 1024); // Should use less than 500MB heap

      // Clean up
      await fs.unlink(testFilePath);
    } catch (error) {
      // Ensure cleanup even if test fails
      try {
        await fs.unlink(testFilePath);
      } catch {}
      throw error;
    }
  }, 60000); // 1 minute timeout for large file test

  afterEach(async () => {
    // Remove temp directory contents
    try {
      const tempDir = path.join(process.cwd(), 'uploads', 'temp');
      const files = await fs.readdir(tempDir);
      await Promise.all(
        files.map(file => fs.unlink(path.join(tempDir, file)))
      );
    } catch (error) {
      // Ignore errors during cleanup
    }
  });

  afterAll(async () => {
    // Cleanup workers
    for (const worker of processingManager['workers']) {
      await worker.terminate();
    }
  });
});

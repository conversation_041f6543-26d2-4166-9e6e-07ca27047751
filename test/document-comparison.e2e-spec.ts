import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('Document Comparison (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('POST /documents/comparison/sections', () => {
    it('should compare document sections successfully', () => {
      const payload = {
        documentSections: {
          'doc1': [
            {
              sectionTitle: 'Terms and Conditions',
              content: 'Document 1 terms and conditions content...',
            },
          ],
          'doc2': [
            {
              sectionTitle: 'Terms and Conditions',
              content: 'Document 2 terms and conditions content...',
            },
          ],
        },
        comparisonType: 'both',
      };

      return request(app.getHttpServer())
        .post('/documents/comparison/sections')
        .send(payload)
        .expect(201)
        .expect((res) => {
          expect(res.body).toBeDefined();
          expect(res.body.comparison).toBeInstanceOf(Array);
          expect(res.body.summary).toBeDefined();
        });
    });

    it('should validate input payload', () => {
      const invalidPayload = {
        documentSections: {}, // Empty sections
        comparisonType: 'invalid', // Invalid comparison type
      };

      return request(app.getHttpServer())
        .post('/documents/comparison/sections')
        .send(invalidPayload)
        .expect(400)
        .expect((res) => {
          expect(res.body.message).toContain('validation failed');
        });
    });

    it('should handle large document sections', () => {
      const largeContent = 'A'.repeat(50000); // 50KB content
      const payload = {
        documentSections: {
          'doc1': [
            {
              sectionTitle: 'Large Section',
              content: largeContent,
            },
          ],
          'doc2': [
            {
              sectionTitle: 'Large Section',
              content: largeContent,
            },
          ],
        },
        comparisonType: 'both',
      };

      return request(app.getHttpServer())
        .post('/documents/comparison/sections')
        .send(payload)
        .expect(201)
        .expect((res) => {
          expect(res.body).toBeDefined();
          expect(res.body.comparison).toBeInstanceOf(Array);
          expect(res.body.summary).toBeDefined();
        });
    });
  });
});
#!/usr/bin/env node

/**
 * SerpApi Integration Test
 * 
 * This script tests the SerpApi integration for the Legal Research Assistant
 * to ensure the API key is working and returns valid results.
 */

const GoogleSearchResults = require('google-search-results-nodejs');
const search = new GoogleSearchResults.GoogleSearch();
require('dotenv').config();

const SERPAPI_API_KEY = process.env.SERPAPI_API_KEY;

async function testSerpApiIntegration() {
  console.log('🔍 Testing SerpApi Integration for Legal Research Assistant...\n');

  if (!SERPAPI_API_KEY) {
    console.error('❌ SERPAPI_API_KEY not found in environment variables');
    console.log('Please add SERPAPI_API_KEY to your .env file');
    process.exit(1);
  }

  console.log('✅ SerpApi API key found');
  console.log(`🔑 Key: ${SERPAPI_API_KEY.substring(0, 8)}...${SERPAPI_API_KEY.substring(SERPAPI_API_KEY.length - 8)}\n`);

  try {
    // Test 1: Basic legal search
    console.log('📋 Test 1: Basic Legal Search');
    console.log('Query: "data privacy law California"');
    
    const basicSearch = await performSerpApiSearch({
      api_key: SERPAPI_API_KEY,
      engine: 'google',
      q: 'data privacy law California',
      num: 5,
      hl: 'en',
      gl: 'us'
    });

    if (basicSearch.organic_results && basicSearch.organic_results.length > 0) {
      console.log(`✅ Found ${basicSearch.organic_results.length} organic results`);
      console.log(`📊 Sample result: "${basicSearch.organic_results[0].title}"`);
      console.log(`🔗 URL: ${basicSearch.organic_results[0].link}\n`);
    } else {
      console.log('⚠️  No organic results found\n');
    }

    // Test 2: Legal news search
    console.log('📋 Test 2: Legal News Search');
    console.log('Query: "Supreme Court ruling 2024"');
    
    const newsSearch = await performSerpApiSearch({
      api_key: SERPAPI_API_KEY,
      engine: 'google',
      q: 'Supreme Court ruling 2024',
      num: 5,
      hl: 'en',
      gl: 'us',
      tbm: 'nws' // News search
    });

    if (newsSearch.news_results && newsSearch.news_results.length > 0) {
      console.log(`✅ Found ${newsSearch.news_results.length} news results`);
      console.log(`📰 Sample news: "${newsSearch.news_results[0].title}"`);
      console.log(`🔗 URL: ${newsSearch.news_results[0].link}\n`);
    } else if (newsSearch.organic_results && newsSearch.organic_results.length > 0) {
      console.log(`✅ Found ${newsSearch.organic_results.length} organic results (news mode)`);
      console.log(`📰 Sample result: "${newsSearch.organic_results[0].title}"`);
      console.log(`🔗 URL: ${newsSearch.organic_results[0].link}\n`);
    } else {
      console.log('⚠️  No news results found\n');
    }

    // Test 3: Legal site-specific search
    console.log('📋 Test 3: Legal Site-Specific Search');
    console.log('Query: "contract law site:law.cornell.edu"');
    
    const siteSearch = await performSerpApiSearch({
      api_key: SERPAPI_API_KEY,
      engine: 'google',
      q: 'contract law site:law.cornell.edu',
      num: 3,
      hl: 'en',
      gl: 'us'
    });

    if (siteSearch.organic_results && siteSearch.organic_results.length > 0) {
      console.log(`✅ Found ${siteSearch.organic_results.length} site-specific results`);
      console.log(`📚 Sample result: "${siteSearch.organic_results[0].title}"`);
      console.log(`🔗 URL: ${siteSearch.organic_results[0].link}\n`);
    } else {
      console.log('⚠️  No site-specific results found\n');
    }

    // Test 4: Check API usage
    console.log('📋 Test 4: API Usage Information');
    if (basicSearch.search_metadata) {
      console.log(`⏱️  Total time taken: ${basicSearch.search_metadata.total_time_taken}s`);
      console.log(`📅 Created at: ${basicSearch.search_metadata.created_at}`);
      console.log(`✅ Status: ${basicSearch.search_metadata.status}\n`);
    }

    console.log('🎉 SerpApi Integration Test Completed Successfully!');
    console.log('✅ The Legal Research Assistant is ready to use SerpApi for enhanced web search');
    console.log('\n📋 Next Steps:');
    console.log('1. Start your NestJS application');
    console.log('2. Test the Legal Research Assistant API endpoints');
    console.log('3. Monitor SerpApi usage in your dashboard: https://serpapi.com/dashboard');

  } catch (error) {
    console.error('❌ SerpApi Integration Test Failed:');
    console.error(`Error: ${error.message}`);
    
    if (error.message.includes('Invalid API key')) {
      console.log('\n🔧 Troubleshooting:');
      console.log('1. Verify your SerpApi API key is correct');
      console.log('2. Check if your SerpApi account is active');
      console.log('3. Ensure you have remaining API credits');
    } else if (error.message.includes('Rate limit')) {
      console.log('\n🔧 Troubleshooting:');
      console.log('1. You have exceeded your SerpApi rate limit');
      console.log('2. Wait for the rate limit to reset');
      console.log('3. Consider upgrading your SerpApi plan');
    }
    
    process.exit(1);
  }
}

function performSerpApiSearch(params) {
  return new Promise((resolve, reject) => {
    search.json(params, (json) => {
      if (json.error) {
        reject(new Error(json.error));
      } else {
        resolve(json);
      }
    });
  });
}

// Run the test
testSerpApiIntegration().catch(console.error);

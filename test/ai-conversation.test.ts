import { Test, TestingModule } from '@nestjs/testing';
import { AiConversationService } from '../src/modules/chat-negotiation/services/ai-conversation.service';
import { DocumentNegotiationService } from '../src/modules/chat-negotiation/services/document-negotiation.service';

describe('AiConversationService', () => {
  let service: AiConversationService;
  let mockDocumentNegotiationService: jest.Mocked<DocumentNegotiationService>;

  const mockDocumentContext = `Contract Type: NDA
Risk Level: HIGH

Key Issues:
1. Confidentiality Clause (HIGH RISK)
   - Scope is too broad
   - Definition of Confidential Information is too vague
   - No clear exclusions for publicly available information

2. Term & Termination (MEDIUM RISK)
   - 5-year term is too long
   - No clear termination for convenience
   - Post-termination obligations are unclear
`;

  beforeEach(async () => {
    mockDocumentNegotiationService = {
      getNegotiationContext: jest.fn().mockResolvedValue(mockDocumentContext),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AiConversationService,
        {
          provide: DocumentNegotiationService,
          useValue: mockDocumentNegotiationService,
        },
      ],
    }).compile();

    service = module.get<AiConversationService>(AiConversationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('conversation flow', () => {
    const session = {
      id: 'test-session-123',
      participants: ['user-1', 'ai-1'],
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const negotiationContext = {
      previousMoves: [] as Array<{ role: string; content: string; timestamp: Date }>,
      currentPhase: 'discussion',
    };

    it('should handle greeting', async () => {
      const result = await service.generateAIResponse(
        session,
        { message: 'Hello, I need help with an NDA' },
        negotiationContext
      );
      
      expect(result.content).toContain('Hello');
      expect(result.content).toContain('NDA');
      expect(result.suggestions.length).toBeGreaterThan(0);
    });

    it('should handle questions about confidentiality', async () => {
      const result = await service.generateAIResponse(
        session,
        { message: 'The confidentiality terms seem too broad' },
        negotiationContext
      );
      
      expect(result.content.toLowerCase()).toContain('confidentiality');
      expect(result.content).toContain('high-risk');
      expect(result.suggestions.length).toBeGreaterThan(0);
    });

    it('should handle questions about terms', async () => {
      const result = await service.generateAIResponse(
        session,
        { message: 'The 5-year term is too long' },
        negotiationContext
      );
      
      expect(result.content.toLowerCase()).toContain('term');
      expect(result.content).toContain('5-year');
      expect(result.suggestions.length).toBeGreaterThan(0);
    });

    it('should handle finalization', async () => {
      const result = await service.generateAIResponse(
        session,
        { message: 'Let\'s finalize the agreement' },
        negotiationContext
      );
      
      expect(result.content.toLowerCase()).toContain('finalize');
      expect(result.content.toLowerCase()).toContain('summarize');
      expect(result.suggestions.length).toBeGreaterThan(0);
    });
  });
});

import { Test, TestingModule } from '@nestjs/testing';
import { TenantContextService } from '../../src/modules/auth/services/tenant-context.service';

describe('TenantContextService', () => {
  let service: TenantContextService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [TenantContextService],
    }).compile();

    service = module.get<TenantContextService>(TenantContextService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('tenant context management', () => {
    const testContext = {
      organizationId: 'test-org-1',
      userId: 'user-1',
    };

    it('should run callback with tenant context', async () => {
      await service.runWithContext(testContext, async () => {
        const currentTenant = service.getCurrentTenant();
        expect(currentTenant).toEqual(testContext);
      });
    });

    it('should provide organization ID from context', async () => {
      await service.runWithContext(testContext, async () => {
        const orgId = service.getCurrentOrganization();
        expect(orgId).toBe(testContext.organizationId);
      });
    });

    it('should provide user ID from context', async () => {
      await service.runWithContext(testContext, async () => {
        const userId = service.getCurrentUserId();
        expect(userId).toBe(testContext.userId);
      });
    });

    it('should verify correct tenant', async () => {
      await service.runWithContext(testContext, async () => {
        const isValid = service.verifyTenant(testContext.organizationId);
        expect(isValid).toBe(true);
      });
    });

    it('should reject incorrect tenant', async () => {
      await service.runWithContext(testContext, async () => {
        const isValid = service.verifyTenant('wrong-org');
        expect(isValid).toBe(false);
      });
    });

    it('should throw error on unauthorized tenant access', async () => {
      await service.runWithContext(testContext, async () => {
        expect(() => {
          service.ensureTenantAccess('wrong-org');
        }).toThrow('Unauthorized tenant access attempt');
      });
    });

    it('should maintain separate contexts for nested calls', async () => {
      const context1 = { organizationId: 'org-1', userId: 'user-1' };
      const context2 = { organizationId: 'org-2', userId: 'user-2' };

      await service.runWithContext(context1, async () => {
        expect(service.getCurrentOrganization()).toBe('org-1');
        
        await service.runWithContext(context2, async () => {
          expect(service.getCurrentOrganization()).toBe('org-2');
        });

        expect(service.getCurrentOrganization()).toBe('org-1');
      });
    });
  });
});

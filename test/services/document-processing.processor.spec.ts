import { Test, TestingModule } from '@nestjs/testing';
import { DocumentProcessingProcessor } from '../../src/modules/documents/services/document-processing.processor';
import { DocumentProcessingJobType } from '../../src/modules/documents/interfaces/document-processing.types';
import { QUEUES } from '../../src/modules/queue/constants';
import { DocumentsService } from '../../src/modules/documents/services/documents.service';
import { LegalPatternRecognitionService } from '../../src/modules/documents/services/legal-pattern-recognition.service';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { LegalPattern } from '../../src/modules/documents/interfaces/legal-pattern.interface';

describe('DocumentProcessingProcessor', () => {
  let processor: DocumentProcessingProcessor;
  let documentsService: DocumentsService;
  let patternRecognitionService: LegalPatternRecognitionService;

  const mockDocumentsService = {
    updateDocumentStatus: jest.fn(),
    processDocument: jest.fn(),
    getDocumentContent: jest.fn(),
    updateMetadata: jest.fn(),
    extractMetadata: jest.fn(),
    generateSummary: jest.fn()
  };

  const mockPatternRecognitionService = {
    analyzeDocument: jest.fn()
  };

  const createMockJob = (data: any) => ({
    data,
    id: 'job-123',
    queue: {
      add: jest.fn().mockResolvedValue({ id: 'child-job-123' })
    }
  } as unknown as Job);

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DocumentProcessingProcessor,
        { provide: DocumentsService, useValue: mockDocumentsService },
        { provide: LegalPatternRecognitionService, useValue: mockPatternRecognitionService },
        { provide: Logger, useValue: { log: jest.fn(), error: jest.fn() } }
      ],
    }).compile();

    processor = module.get<DocumentProcessingProcessor>(DocumentProcessingProcessor);
    documentsService = module.get<DocumentsService>(DocumentsService);
    patternRecognitionService = module.get<LegalPatternRecognitionService>(LegalPatternRecognitionService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('processDocument', () => {
    it('should process document and add child jobs as requested', async () => {
      const job = createMockJob({
        documentId: 'doc-123',
        options: {
          extractMetadata: true,
          generateSummary: true,
          analyzePatterns: true
        }
      });

      await processor.processDocument(job);

      expect(mockDocumentsService.updateDocumentStatus).toHaveBeenCalledWith('doc-123', 'processing');
      expect(mockDocumentsService.processDocument).toHaveBeenCalledWith('doc-123');
      
      // Verify child jobs were added
      expect(job.queue.add).toHaveBeenCalledWith(
        DocumentProcessingJobType.EXTRACT_METADATA,
        { documentId: 'doc-123', parentJobId: 'job-123' }
      );
      expect(job.queue.add).toHaveBeenCalledWith(
        DocumentProcessingJobType.GENERATE_SUMMARY,
        { documentId: 'doc-123', parentJobId: 'job-123' }
      );
      expect(job.queue.add).toHaveBeenCalledWith(
        DocumentProcessingJobType.ANALYZE_PATTERNS,
        { documentId: 'doc-123', parentJobId: 'job-123' }
      );
      
      expect(mockDocumentsService.updateDocumentStatus).toHaveBeenCalledWith('doc-123', 'completed');
    });
  });

  describe('analyzePatterns', () => {
    it('should analyze patterns in document and update metadata', async () => {
      const job = createMockJob({
        documentId: 'doc-123'
      });

      const documentContent = 'This is a sample legal document';
      const patterns: LegalPattern[] = [
        { 
          type: 'definition', 
          content: 'Sample definition', 
          startIndex: 0, 
          endIndex: 10,
          metadata: { title: 'Sample Definition' }
        }
      ];

      const recognitionResult = {
        patterns,
        documentId: 'doc-123',
        timestamp: new Date()
      };

      mockDocumentsService.getDocumentContent.mockResolvedValue(documentContent);
      mockPatternRecognitionService.analyzeDocument.mockResolvedValue(recognitionResult);

      const result = await processor.analyzePatterns(job);

      expect(mockDocumentsService.getDocumentContent).toHaveBeenCalledWith('doc-123');
      expect(mockPatternRecognitionService.analyzeDocument).toHaveBeenCalledWith(documentContent, 'doc-123');
      expect(mockDocumentsService.updateMetadata).toHaveBeenCalledWith('doc-123', {
        patterns: recognitionResult.patterns,
        patternAnalysisDate: expect.any(Date)
      });
      
      expect(result).toEqual({
        success: true,
        documentId: 'doc-123',
        patternCount: 1
      });
    });

    it('should handle errors during pattern analysis', async () => {
      const job = createMockJob({
        documentId: 'doc-123'
      });

      const error = new Error('Analysis failed');
      mockDocumentsService.getDocumentContent.mockRejectedValue(error);

      await expect(processor.analyzePatterns(job)).rejects.toThrow('Analysis failed');
    });
  });
});

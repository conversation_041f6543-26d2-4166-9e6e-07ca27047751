import { Test, TestingModule } from '@nestjs/testing';
import { LegalPatternRecognitionService } from '../../src/modules/documents/services/legal-pattern-recognition.service';
import { AIService } from '../../src/modules/ai/services/ai.service';
import { LegalPattern } from '../../src/modules/documents/interfaces/legal-pattern.interface';

describe('LegalPatternRecognitionService', () => {
  let service: LegalPatternRecognitionService;
  let aiService: AIService;

  const mockAIService = {
    generateResponse: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LegalPatternRecognitionService,
        {
          provide: AIService,
          useValue: mockAIService,
        },
      ],
    }).compile();

    service = module.get<LegalPatternRecognitionService>(LegalPatternRecognitionService);
    aiService = module.get<AIService>(AIService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('analyzeDocument', () => {
    it('should recognize document structure patterns', async () => {
      const documentContent = `
        Section 1: Introduction
        This is an introduction to the document.

        § 2.1: Subsection
        This is a subsection using the § symbol.

        Article 3: Rights and Obligations
        Listing the rights and obligations.

        Exhibit A: Supporting Documents
        List of required documents.
      `;
      const documentId = 'doc-123';

      const result = await service.analyzeDocument(documentContent, documentId);

      expect(result.documentId).toBe(documentId);
      expect(result.patterns.length).toBeGreaterThan(3);
      
      // Verify section detection
      const section = result.patterns.find(p => p.type === 'section');
      expect(section).toBeDefined();
      expect(section?.metadata?.number).toBe('1');
      
      // Verify article detection
      const article = result.patterns.find(p => p.type === 'article');
      expect(article).toBeDefined();
      expect(article?.metadata?.number).toBe('3');
      
      // Verify exhibit detection
      const exhibit = result.patterns.find(p => p.type === 'exhibit');
      expect(exhibit).toBeDefined();
      expect(exhibit?.metadata?.number).toBe('A');

      expect(result.timestamp).toBeInstanceOf(Date);
    });

    it('should recognize various definition patterns', async () => {
      const documentContent = `
        "Agreement" means this legal contract.
        The term "Party" shall have the meaning of a contract signatory.
        For purposes of this Agreement, "Confidential Information" means any proprietary data.
        As used in this Agreement, "Intellectual Property" refers to all patents and copyrights.
      `;
      const documentId = 'doc-123';

      const result = await service.analyzeDocument(documentContent, documentId);

      expect(result.documentId).toBe(documentId);
      expect(result.patterns.length).toBeGreaterThan(3);
      
      // Verify standard definition
      const agreement = result.patterns.find(p => p.metadata?.term === 'Agreement');
      expect(agreement?.type).toBe('definition');
      
      // Verify term definition
      const party = result.patterns.find(p => p.metadata?.term === 'Party');
      expect(party?.type).toBe('defined_term');
      
      // Verify purpose-based definition
      const confidential = result.patterns.find(p => p.metadata?.term === 'Confidential Information');
      expect(confidential?.type).toBe('interpretation');

      expect(result.timestamp).toBeInstanceOf(Date);
    });

    it('should recognize contract formation patterns', async () => {
      const documentContent = `
        THIS AGREEMENT is made and entered into as of January 1, 2025
        
        WHEREAS, the parties wish to establish a business relationship;
        
        By and between ABC Corp., a Delaware corporation, and XYZ Inc., a California corporation.
        
        Effective Date: March 15, 2025
        
        EXECUTED AND DELIVERED by:
        By: _____________
        Name: John Smith
        Title: CEO
      `;
      const documentId = 'doc-123';

      const result = await service.analyzeDocument(documentContent, documentId);

      expect(result.patterns.length).toBeGreaterThan(3);
      
      // Verify preamble
      const preamble = result.patterns.find(p => p.type === 'preamble');
      expect(preamble).toBeDefined();
      
      // Verify recital
      const recital = result.patterns.find(p => p.type === 'recital');
      expect(recital).toBeDefined();
      
      // Verify parties
      const parties = result.patterns.find(p => p.type === 'parties');
      expect(parties).toBeDefined();
      
      // Verify effective date
      const effectiveDate = result.patterns.find(p => p.type === 'effective_date');
      expect(effectiveDate).toBeDefined();
      
      // Verify signatory
      const signatory = result.patterns.find(p => p.type === 'authorized_representative');
      expect(signatory).toBeDefined();
    });

    it('should recognize rights, obligations, and warranties', async () => {
      const documentContent = `
        Seller represents and warrants that the goods are merchantable.
        Buyer shall indemnify Seller against all claims.
        The Seller must deliver the goods within 30 days.
        Buyer has the right to inspect the goods upon delivery.
        Seller covenants and agrees that it will maintain insurance.
      `;
      const documentId = 'doc-123';

      const result = await service.analyzeDocument(documentContent, documentId);

      expect(result.patterns.length).toBeGreaterThan(4);
      
      // Verify warranty
      const warranty = result.patterns.find(p => p.type === 'warranty');
      expect(warranty).toBeDefined();
      
      // Verify indemnity
      const indemnity = result.patterns.find(p => p.type === 'indemnity');
      expect(indemnity).toBeDefined();
      
      // Verify obligation
      const obligation = result.patterns.find(p => p.type === 'obligation');
      expect(obligation).toBeDefined();
      
      // Verify right
      const right = result.patterns.find(p => p.type === 'right');
      expect(right).toBeDefined();
      
      // Verify covenant
      const covenant = result.patterns.find(p => p.type === 'covenant');
      expect(covenant).toBeDefined();
    });

    it('should recognize dispute resolution and compliance patterns', async () => {
      const documentContent = `
        This Agreement shall be governed by the laws of California.
        Any disputes shall be resolved by arbitration in Los Angeles.
        The parties agree to mediation before arbitration.
        If any provision becomes invalid, the remaining provisions shall remain in effect.
        The parties shall comply with all applicable regulations.
        All required permits and licenses must be maintained.
      `;
      const documentId = 'doc-123';

      const result = await service.analyzeDocument(documentContent, documentId);

      expect(result.patterns.length).toBeGreaterThan(5);
      
      // Verify governing law
      const law = result.patterns.find(p => p.type === 'governing_law');
      expect(law).toBeDefined();
      
      // Verify dispute resolution
      const dispute = result.patterns.find(p => p.type === 'dispute_resolution');
      expect(dispute).toBeDefined();
      
      // Verify mediation
      const mediation = result.patterns.find(p => p.type === 'mediation');
      expect(mediation).toBeDefined();
      
      // Verify severability
      const severability = result.patterns.find(p => p.type === 'severability');
      const severabilityPattern = result.patterns.find(p => p.content.includes('invalid') || p.content.includes('unenforceable'));
      expect(severabilityPattern).toBeDefined();
      
      // Verify compliance
      const compliance = result.patterns.find(p => p.type === 'compliance');
      expect(compliance).toBeDefined();
      
      // Verify permits/licenses
      const permits = result.patterns.find(p => p.type === 'permits_licenses');
      expect(permits).toBeDefined();
    });

    it('should handle documents with no recognizable patterns', async () => {
      const documentContent = 'This is a simple text without any legal patterns.';
      const documentId = 'doc-123';

      const result = await service.analyzeDocument(documentContent, documentId);

      expect(result.documentId).toBe(documentId);
      expect(result.patterns).toHaveLength(0);
      expect(result.timestamp).toBeInstanceOf(Date);
    });

    it('should handle errors during AI pattern recognition but continue with regex patterns', async () => {
      const documentContent = `A lengthy document with some patterns.
        Section 1: Introduction
        This triggers AI analysis but regex should still work.`.repeat(10); // Make it long enough to trigger AI

      const documentId = 'doc-123';

      mockAIService.generateResponse.mockRejectedValue(new Error('AI analysis failed'));

      const result = await service.analyzeDocument(documentContent, documentId);

      expect(result.documentId).toBe(documentId);
      expect(result.patterns).toBeDefined();
      expect(result.patterns.length).toBeGreaterThan(0);
      expect(result.patterns[0]?.type).toBe('section');
      expect(result.timestamp).toBeInstanceOf(Date);
    });

    it('should detect and merge AI-generated patterns without duplicates', async () => {
      const documentContent = 'A'.repeat(1000); // Long enough to trigger AI
      const documentId = 'doc-123';

      // Mock AI response
      mockAIService.generateResponse.mockResolvedValue(JSON.stringify([{
        type: 'custom_pattern',
        content: 'AI detected pattern',
        startIndex: 0,
        endIndex: 20,
        metadata: { custom: 'metadata' }
      }]));

      const result = await service.analyzeDocument(documentContent, documentId);

      expect(result.patterns).toBeDefined();
      const customPattern = result.patterns.find(p => p.type === 'custom_pattern');
      expect(customPattern).toBeDefined();
      expect(customPattern?.metadata?.custom).toBe('metadata');
    });
  });

  describe('validatePattern', () => {
    it('should validate valid section patterns', async () => {
      const pattern: LegalPattern = {
        type: 'section',
        content: 'Section content',
        startIndex: 0,
        endIndex: 20,
        metadata: {
          number: '1',
          title: 'Test Section'
        }
      };

      expect(await service.validatePattern(pattern)).toBeTruthy();
    });

    it('should validate valid definition patterns', async () => {
      const pattern: LegalPattern = {
        type: 'definition',
        content: '"Term" means definition',
        startIndex: 0,
        endIndex: 20,
        metadata: {
          term: 'Term',
          meaning: 'definition' // Changed from 'definition' to 'meaning' to match service expectations
        }
      };

      expect(await service.validatePattern(pattern)).toBeTruthy();
    });

    it('should reject patterns with missing required fields', async () => {
      const invalidPattern = {
        type: 'definition',
        content: 'Invalid content'
        // Missing required metadata fields
      } as LegalPattern;

      expect(await service.validatePattern(invalidPattern)).toBeFalsy();
    });

    it('should reject patterns with invalid metadata', async () => {
      const invalidPattern: LegalPattern = {
        type: 'definition',
        content: '"Term" means definition',
        startIndex: 0,
        endIndex: 20,
        metadata: {
          // Missing required metadata fields
        }
      };

      expect(await service.validatePattern(invalidPattern)).toBeFalsy();
    });
  });
});

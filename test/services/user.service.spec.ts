import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User } from '../../src/modules/auth/schemas/user.schema';
import { UserService } from '../../src/modules/auth/services/user.service';
import { UserRole } from '../../src/modules/auth/enums/roles.enum';
import { CreateUserDto } from '../../src/modules/auth/dto/user.dto';

describe('UserService', () => {
  let service: UserService;
  let model: Model<User>;

  const mockUser = {
    id: 'test-id',
    organizationId: 'org-1',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    role: UserRole.USER,
    emailVerified: false,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockUserModel = {
    create: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    findById: jest.fn(),
    findByIdAndUpdate: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: getModelToken(User.name),
          useValue: mockUserModel,
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    model = module.get<Model<User>>(getModelToken(User.name));
  });

  describe('create', () => {
    it('should create a user within an organization', async () => {
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Test',
        lastName: 'User',
        organizationId: 'org-1',
      };

      mockUserModel.create.mockResolvedValue(mockUser);

      const result = await service.create(createUserDto);

      expect(result.organizationId).toBe('org-1');
      expect(mockUserModel.create).toHaveBeenCalledWith({
        ...createUserDto,
        role: UserRole.USER,
        emailVerified: false,
        isActive: true,
      });
    });
  });

  describe('findByOrganization', () => {
    it('should find users within an organization', async () => {
      const organizationId = 'org-1';
      mockUserModel.find.mockResolvedValue([mockUser]);

      const result = await service.findByOrganization(organizationId);

      expect(result).toHaveLength(1);
      expect(mockUserModel.find).toHaveBeenCalledWith({ organizationId });
    });
  });

  describe('findByEmail', () => {
    it('should find a user by email within an organization', async () => {
      const email = '<EMAIL>';
      const organizationId = 'org-1';
      mockUserModel.findOne.mockResolvedValue(mockUser);

      const result = await service.findByEmail(email, organizationId);

      expect(result).toBeDefined();
      expect(mockUserModel.findOne).toHaveBeenCalledWith({
        email,
        organizationId,
      });
    });
  });
});

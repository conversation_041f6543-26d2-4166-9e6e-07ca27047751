import { Test, TestingModule } from '@nestjs/testing';
import { Model, Document } from 'mongoose';
import { TenantContextService } from '../../src/modules/auth/services/tenant-context.service';
import { TenantAwareRepository } from '../../src/modules/common/repositories/base.repository';
import { UnauthorizedException } from '@nestjs/common';

// Mock document type for testing
interface TestDocument extends Document {
  name: string;
  organizationId: string;
}

// Concrete implementation of TenantAwareRepository for testing
class TestRepository extends TenantAwareRepository<TestDocument> {
  constructor(model: Model<TestDocument>, tenantContext: TenantContextService) {
    super(model, tenantContext);
  }
}

describe('TenantAwareRepository', () => {
  let repository: TestRepository;
  let mockModel: Partial<Model<TestDocument>>;
  let tenantContext: TenantContextService;

  const testOrganizationId = 'test-org-1';
  const testData = { name: 'test', organizationId: testOrganizationId };

  beforeEach(async () => {
    // Create a fully typed mock query builder
    const mockQueryBuilder = {
      exec: jest.fn(),
      sort: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
    };

    mockModel = {
      create: jest.fn().mockImplementation((data) => Promise.resolve(data)),
      find: jest.fn().mockImplementation(() => mockQueryBuilder),
      findOne: jest.fn().mockImplementation(() => mockQueryBuilder),
      findOneAndUpdate: jest.fn().mockImplementation(() => mockQueryBuilder),
      deleteOne: jest.fn().mockImplementation(() => ({ exec: jest.fn().mockResolvedValue({ deletedCount: 1 }) })),
      countDocuments: jest.fn().mockImplementation(() => ({ exec: jest.fn().mockResolvedValue(25) })),
    } as unknown as Model<TestDocument>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: 'TestModel',
          useValue: mockModel,
        },
        {
          provide: TenantContextService,
          useValue: {
            getCurrentOrganization: jest.fn().mockReturnValue(testOrganizationId),
          },
        },
      ],
    }).compile();

    tenantContext = module.get<TenantContextService>(TenantContextService);
    repository = new TestRepository(mockModel as Model<TestDocument>, tenantContext);
  });

  describe('tenant context validation', () => {
    it('should throw UnauthorizedException when no tenant context', async () => {
      jest.spyOn(tenantContext, 'getCurrentOrganization').mockReturnValue(undefined);
      await expect(repository.find()).rejects.toThrow(UnauthorizedException);
    });

    it('should add organizationId to create operation', async () => {
      await repository.create({ name: 'test' });
      expect(mockModel.create).toHaveBeenCalledWith({
        name: 'test',
        organizationId: testOrganizationId,
      });
    });

    it('should add organizationId to find operations', async () => {
      await repository.find({ name: 'test' });
      expect(mockModel.find).toHaveBeenCalledWith({
        name: 'test',
        organizationId: testOrganizationId,
      });
    });

    it('should add organizationId to findOne operations', async () => {
      await repository.findOne({ name: 'test' });
      expect(mockModel.findOne).toHaveBeenCalledWith({
        name: 'test',
        organizationId: testOrganizationId,
      });
    });

    it('should add organizationId to update operations', async () => {
      const id = 'test-id';
      const updateData = { name: 'updated' };
      await repository.update(id, updateData);
      expect(mockModel.findOneAndUpdate).toHaveBeenCalledWith(
        { _id: id, organizationId: testOrganizationId },
        updateData,
        { new: true },
      );
    });

    it('should add organizationId to delete operations', async () => {
      const id = 'test-id';
      await repository.delete(id);
      expect(mockModel.deleteOne).toHaveBeenCalledWith({
        _id: id,
        organizationId: testOrganizationId,
      });
    });
  });

  describe('pagination', () => {
    it('should handle pagination with tenant context', async () => {
      const page = 2;
      const limit = 10;
      const filter = { name: 'test' };

      const mockPaginatedQuery = {
        sort: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
        exec: jest.fn().mockResolvedValue([testData]),
      };

      jest.spyOn(mockModel, 'find').mockReturnValueOnce(mockPaginatedQuery as any);

      const result = await repository.findWithPagination(filter, page, limit);

      expect(mockModel.find).toHaveBeenCalledWith({
        ...filter,
        organizationId: testOrganizationId,
      });
      expect(result).toEqual({
        items: [testData],
        total: 25,
        pages: 3,
      });
    });
  });
});

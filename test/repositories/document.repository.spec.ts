import { Test, TestingModule } from '@nestjs/testing';
import { Model } from 'mongoose';
import { getModelToken } from '@nestjs/mongoose';
import { DocumentRepository } from '../../src/modules/documents/repositories/document.repository';
import { TenantContextService } from '../../src/modules/auth/services/tenant-context.service';
import { Document } from '../../src/modules/documents/schemas/document.schema';

describe('DocumentRepository', () => {
  let repository: DocumentRepository;
  let mockModel: Partial<Model<Document>>;
  let tenantContext: TenantContextService;

  const testOrganizationId = 'test-org-1';
  const testDocument = {
    id: 'test-doc-1',
    organizationId: testOrganizationId,
    documentType: 'contract',
    status: 'uploaded',
    uploadDate: new Date(),
  };

  beforeEach(async () => {
    const mockQueryBuilder = {
      exec: jest.fn().mockResolvedValue([testDocument]),
      sort: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
    };

    mockModel = {
      create: jest.fn().mockResolvedValue(testDocument),
      find: jest.fn().mockImplementation(() => mockQueryBuilder),
      findOne: jest.fn().mockImplementation(() => mockQueryBuilder),
      findOneAndUpdate: jest.fn().mockImplementation(() => mockQueryBuilder),
    } as unknown as Model<Document>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DocumentRepository,
        {
          provide: getModelToken(Document.name),
          useValue: mockModel,
        },
        {
          provide: TenantContextService,
          useValue: {
            getCurrentOrganization: jest.fn().mockReturnValue(testOrganizationId),
          },
        },
      ],
    }).compile();

    repository = module.get<DocumentRepository>(DocumentRepository);
    tenantContext = module.get<TenantContextService>(TenantContextService);
  });

  describe('findByDocumentType', () => {
    it('should include tenant context in document type query', async () => {
      const documentType = 'contract';
      await repository.findByDocumentType(documentType);

      expect(mockModel.find).toHaveBeenCalledWith({
        documentType,
        organizationId: testOrganizationId,
      });
    });
  });

  describe('findByStatus', () => {
    it('should include tenant context in status query', async () => {
      const status = 'uploaded';
      await repository.findByStatus(status);

      expect(mockModel.find).toHaveBeenCalledWith({
        status,
        organizationId: testOrganizationId,
      });
    });
  });

  describe('findByDateRange', () => {
    it('should include tenant context in date range query', async () => {
      const startDate = new Date('2025-01-01');
      const endDate = new Date('2025-12-31');
      await repository.findByDateRange(startDate, endDate);

      expect(mockModel.find).toHaveBeenCalledWith({
        uploadDate: {
          $gte: startDate,
          $lte: endDate,
        },
        organizationId: testOrganizationId,
      });
    });
  });

  describe('updateStatus', () => {
    it('should include tenant context in status update', async () => {
      const id = 'doc-1';
      const status = 'processing';
      await repository.updateStatus(id, status);

      expect(mockModel.findOneAndUpdate).toHaveBeenCalledWith(
        { _id: id, organizationId: testOrganizationId },
        { status },
        { new: true },
      );
    });
  });

  describe('addMetadata', () => {
    it('should include tenant context in metadata update', async () => {
      const id = 'doc-1';
      const metadata = { key: 'value' };
      await repository.addMetadata(id, metadata);

      expect(mockModel.findOneAndUpdate).toHaveBeenCalledWith(
        { _id: id, organizationId: testOrganizationId },
        { $set: { metadata } },
        { new: true },
      );
    });
  });

  describe('updateProcessingError', () => {
    it('should include tenant context in error update', async () => {
      const id = 'doc-1';
      const error = { message: 'test error' };
      await repository.updateProcessingError(id, error);

      expect(mockModel.findOneAndUpdate).toHaveBeenCalledWith(
        { _id: id, organizationId: testOrganizationId },
        {
          $set: {
            processingError: {
              ...error,
              timestamp: expect.any(Date),
            },
          },
          status: 'failed',
        },
        { new: true },
      );
    });
  });
});

import { AiConversationService } from '../src/modules/chat-negotiation/services/ai-conversation.service';
import { DocumentNegotiationService } from '../src/modules/chat-negotiation/services/document-negotiation.service';

class MockDocumentNegotiationService {
  async getNegotiationContext() {
    return '';
  }
}

async function testConversation() {
  // Create service with mock dependencies
  const mockDocService = new MockDocumentNegotiationService();
  const service = new AiConversationService(mockDocService as any);
  
  // Mock document context with more details
  const mockDocumentContext = `Contract Type: NDA
Risk Level: HIGH

Key Issues:
1. Confidentiality Clause (HIGH RISK)
   - Scope is too broad
   - Definition of Confidential Information is too vague
   - No clear exclusions for publicly available information

2. Term & Termination (MEDIUM RISK)
   - 5-year term is too long
   - No clear termination for convenience
   - Post-termination obligations are unclear

3. Liability (HIGH RISK)
   - Unlimited liability for breach of confidentiality
   - No cap on damages
   - No exclusion of indirect/consequential damages

4. Governing Law (LOW RISK)
   - New York law is acceptable
   - Jurisdiction is reasonable
`;

  // Mock session and negotiation context
  const session = {
    id: 'test-session-123',
    participants: ['user-1', 'ai-1'],
    status: 'active',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const negotiationContext = {
    previousMoves: [] as Array<{ role: string; content: string; timestamp: Date }>,
    currentPhase: 'discussion',
    documentContext: mockDocumentContext,
  };

  // Test conversation flow with more natural progression
  const testMessages = [
    { role: 'user', content: 'Hello, I have some concerns about the NDA we\'re discussing' },
    { role: 'user', content: 'The confidentiality terms seem overly broad' },
    { role: 'user', content: 'Can we limit the scope to only what\'s necessary?' },
    { role: 'user', content: 'Also, the 5-year term is too long for our needs' },
    { role: 'user', content: 'Can we reduce it to 2 years?' },
    { role: 'user', content: 'And what about the liability terms?' },
    { role: 'user', content: 'They seem quite one-sided' },
    { role: 'user', content: 'Let\'s finalize these changes' },
  ];

  console.log('=== Starting Conversation Test ===\n');
  
  for (const message of testMessages) {
    console.log(`User: ${message.content}`);
    
    const response = await service.generateAIResponse(
      session as any,
      { 
        message: message.content,
        strategy: 'collaborative',
        sentiment: 'neutral',
        role: message.role,
      },
      negotiationContext as any
    );
    
    console.log('AI Response:', response.content);
    console.log('Suggestions:', response.suggestions);
    console.log('---\n');
    
    // Add AI response to context
    negotiationContext.previousMoves.push({
      role: 'assistant',
      content: response.content,
      timestamp: new Date(),
    });
  }
  
  console.log('=== Conversation Test Complete ===');
}

// Run the test
testConversation().catch(console.error);

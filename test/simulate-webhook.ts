import Stripe from 'stripe';
import axios from 'axios';

const stripe = new Stripe('sk_test_51N8wq1JK9ZkHwqq2X6Y8v4L7pM3nR2tU9', {
  apiVersion: '2025-02-24.acacia',
});

const WEBHOOK_URL = 'http://localhost:3000/subscriptions/webhook';

async function simulateWebhookEvent(type: string, data: any) {
  // Create a test webhook event
  const event = {
    id: `evt_${Math.random().toString(36).substr(2, 9)}`,
    object: 'event',
    api_version: '2025-02-24.acacia',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: data,
    },
    livemode: false,
    pending_webhooks: 1,
    type,
  };

  try {
    // Send webhook to your local endpoint
    const response = await axios.post(WEBHOOK_URL, event, {
      headers: {
        'stripe-signature': 'test_signature', // Your webhook endpoint should accept this in test mode
      },
    });

    console.log(`Simulated ${type} webhook event:`, response.status);
    console.log('Response:', response.data);
  } catch (error) {
    console.error('Error sending webhook:', error.response?.data || error.message);
  }
}

async function main() {
  // Simulate successful payment
  await simulateWebhookEvent('invoice.payment_succeeded', {
    subscription: 'sub_test123',
    customer: 'cus_test123',
    metadata: {
      organizationId: 'test-org-123',
    },
  });

  // Wait a bit
  await new Promise(resolve => setTimeout(resolve, 1000));

  // Simulate failed payment
  await simulateWebhookEvent('invoice.payment_failed', {
    subscription: 'sub_test123',
    customer: 'cus_test123',
    metadata: {
      organizationId: 'test-org-123',
    },
  });
}

main().catch(console.error);
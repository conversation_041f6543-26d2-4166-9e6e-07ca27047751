#!/bin/bash

# Test script for tenant-aware features
BASE_URL="http://localhost:3000"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

echo "Testing tenant-aware features..."

# 1. Create first organization and user
echo -e "\n${GREEN}1. Creating first organization and user${NC}"
ORG1_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Org1",
    "lastName": "Admin",
    "organizationName": "Test Org 1"
  }')
ORG1_TOKEN=$(echo $RESPONSE | jq -r '.accessToken')

# 2. Create second organization and user
echo -e "\n${GREEN}2. Creating second organization and user${NC}"
ORG2_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Org2",
    "lastName": "Admin",
    "organizationName": "Test Org 2"
  }')
ORG2_TOKEN=$(echo $RESPONSE | jq -r '.accessToken')

# 3. Upload document for first organization
echo -e "\n${GREEN}3. Uploading document for Org 1${NC}"
ORG1_DOC_RESPONSE=$(curl -s -X POST "$BASE_URL/documents/upload" \
  -H "Authorization: Bearer $ORG1_TOKEN" \
  -F "file=@./test-contract.txt")

# 4. Upload document for second organization
echo -e "\n${GREEN}4. Uploading document for Org 2${NC}"
ORG2_DOC_RESPONSE=$(curl -s -X POST "$BASE_URL/documents/upload" \
  -H "Authorization: Bearer $ORG2_TOKEN" \
  -F "file=@./test-contract.txt")

# 5. List documents for first organization
echo -e "\n${GREEN}5. Listing documents for Org 1${NC}"
curl -s -X GET "$BASE_URL/documents" \
  -H "Authorization: Bearer $ORG1_TOKEN"

# 6. List documents for second organization
echo -e "\n${GREEN}6. Listing documents for Org 2${NC}"
curl -s -X GET "$BASE_URL/documents" \
  -H "Authorization: Bearer $ORG2_TOKEN"

# 7. Create chat session for first organization
echo -e "\n${GREEN}7. Creating chat session for Org 1${NC}"
ORG1_CHAT_RESPONSE=$(curl -s -X POST "$BASE_URL/chat/sessions" \
  -H "Authorization: Bearer $ORG1_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Chat Org 1"
  }')

# 8. Create chat session for second organization
echo -e "\n${GREEN}8. Creating chat session for Org 2${NC}"
ORG2_CHAT_RESPONSE=$(curl -s -X POST "$BASE_URL/chat/sessions" \
  -H "Authorization: Bearer $ORG2_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Chat Org 2"
  }')

# 9. List chat sessions for first organization
echo -e "\n${GREEN}9. Listing chat sessions for Org 1${NC}"
curl -s -X GET "$BASE_URL/chat/sessions" \
  -H "Authorization: Bearer $ORG1_TOKEN"

# 10. List chat sessions for second organization
echo -e "\n${GREEN}10. Listing chat sessions for Org 2${NC}"
curl -s -X GET "$BASE_URL/chat/sessions" \
  -H "Authorization: Bearer $ORG2_TOKEN"

echo -e "\n${GREEN}Test script completed${NC}"

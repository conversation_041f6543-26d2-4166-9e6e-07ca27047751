#!/usr/bin/env node

/**
 * Simple SerpApi Test using Axios
 * 
 * This script tests the SerpApi integration using direct HTTP requests
 * to verify the API key and basic functionality.
 */

const axios = require('axios');
require('dotenv').config();

const SERPAPI_API_KEY = process.env.SERPAPI_API_KEY;
const SERPAPI_BASE_URL = 'https://serpapi.com/search';

async function testSerpApiWithAxios() {
  console.log('🔍 Testing SerpApi Integration with Axios...\n');

  if (!SERPAPI_API_KEY) {
    console.error('❌ SERPAPI_API_KEY not found in environment variables');
    console.log('Please add SERPAPI_API_KEY to your .env file');
    process.exit(1);
  }

  console.log('✅ SerpApi API key found');
  console.log(`🔑 Key: ${SERPAPI_API_KEY.substring(0, 8)}...${SERPAPI_API_KEY.substring(SERPAPI_API_KEY.length - 8)}\n`);

  try {
    // Test 1: Basic legal search
    console.log('📋 Test 1: Basic Legal Search');
    console.log('Query: "data privacy law California"');
    
    const response = await axios.get(SERPAPI_BASE_URL, {
      params: {
        api_key: SERPAPI_API_KEY,
        engine: 'google',
        q: 'data privacy law California',
        num: 5,
        hl: 'en',
        gl: 'us'
      },
      timeout: 15000
    });

    const data = response.data;

    if (data.error) {
      throw new Error(data.error);
    }

    console.log('✅ SerpApi request successful!');
    
    if (data.organic_results && data.organic_results.length > 0) {
      console.log(`📊 Found ${data.organic_results.length} organic results`);
      console.log(`🔍 Sample result: "${data.organic_results[0].title}"`);
      console.log(`🔗 URL: ${data.organic_results[0].link}`);
      console.log(`📝 Snippet: ${data.organic_results[0].snippet.substring(0, 100)}...`);
    } else {
      console.log('⚠️  No organic results found');
    }

    if (data.search_metadata) {
      console.log(`\n📊 Search Metadata:`);
      console.log(`⏱️  Total time: ${data.search_metadata.total_time_taken}s`);
      console.log(`📅 Created: ${data.search_metadata.created_at}`);
      console.log(`✅ Status: ${data.search_metadata.status}`);
    }

    if (data.search_information) {
      console.log(`\n🔍 Search Information:`);
      console.log(`📊 Total results: ${data.search_information.total_results?.toLocaleString() || 'N/A'}`);
      console.log(`⏱️  Time taken: ${data.search_information.time_taken_displayed || 'N/A'}`);
    }

    console.log('\n🎉 SerpApi Integration Test Completed Successfully!');
    console.log('✅ The Legal Research Assistant is ready to use SerpApi');
    
    // Test account info
    console.log('\n📋 Testing Account Information...');
    const accountResponse = await axios.get('https://serpapi.com/account', {
      params: {
        api_key: SERPAPI_API_KEY
      },
      timeout: 10000
    });

    if (accountResponse.data) {
      console.log('✅ Account information retrieved');
      if (accountResponse.data.account_email) {
        console.log(`📧 Account: ${accountResponse.data.account_email}`);
      }
      if (accountResponse.data.plan) {
        console.log(`📦 Plan: ${accountResponse.data.plan}`);
      }
      if (accountResponse.data.searches_left !== undefined) {
        console.log(`🔍 Searches remaining: ${accountResponse.data.searches_left}`);
      }
      if (accountResponse.data.searches_used !== undefined) {
        console.log(`📊 Searches used: ${accountResponse.data.searches_used}`);
      }
    }

    console.log('\n📋 Next Steps:');
    console.log('1. Start your NestJS application');
    console.log('2. Test the Legal Research Assistant API endpoints');
    console.log('3. Monitor SerpApi usage in your dashboard: https://serpapi.com/dashboard');

  } catch (error) {
    console.error('\n❌ SerpApi Integration Test Failed:');
    
    if (error.response) {
      console.error(`HTTP Status: ${error.response.status}`);
      console.error(`Response: ${JSON.stringify(error.response.data, null, 2)}`);
    } else if (error.request) {
      console.error('No response received from SerpApi');
      console.error('This might be a network connectivity issue');
    } else {
      console.error(`Error: ${error.message}`);
    }
    
    if (error.message.includes('Invalid API key') || 
        (error.response && error.response.data && error.response.data.error && 
         error.response.data.error.includes('Invalid API key'))) {
      console.log('\n🔧 API Key Troubleshooting:');
      console.log('1. Verify your SerpApi API key is correct');
      console.log('2. Check if your SerpApi account is active');
      console.log('3. Ensure you have remaining API credits');
      console.log('4. Visit https://serpapi.com/manage-api-key to verify your key');
    } else if (error.message.includes('Rate limit') || 
               (error.response && error.response.status === 429)) {
      console.log('\n🔧 Rate Limit Troubleshooting:');
      console.log('1. You have exceeded your SerpApi rate limit');
      console.log('2. Wait for the rate limit to reset');
      console.log('3. Consider upgrading your SerpApi plan');
    }
    
    process.exit(1);
  }
}

// Run the test
testSerpApiWithAxios().catch(console.error);

import { Test, TestingModule } from '@nestjs/testing';
import { DocumentPatternController } from '../../src/modules/documents/controllers/document-pattern.controller';
import { DocumentsService } from '../../src/modules/documents/services/documents.service';
import { LegalPatternRecognitionService } from '../../src/modules/documents/services/legal-pattern-recognition.service';
import { DOCUMENT_PROCESSING_QUEUE } from '../../src/modules/documents/services/document-processing.processor';
import { getQueueToken } from '@nestjs/bull';
import { NotFoundException } from '@nestjs/common';
import { LegalPattern, PatternRecognitionResult } from '../../src/modules/documents/interfaces/legal-pattern.interface';

describe('DocumentPatternController', () => {
  let controller: DocumentPatternController;
  let documentsService: DocumentsService;
  let patternRecognitionService: LegalPatternRecognitionService;
  let documentProcessingQueue: any;

  const mockDocumentsService = {
    findById: jest.fn(),
    getMetadata: jest.fn()
  };

  const mockPatternRecognitionService = {
    getPatternStatistics: jest.fn(),
    getPatternsByType: jest.fn()
  };

  const mockQueue = {
    add: jest.fn().mockResolvedValue({ id: 'job-123' })
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DocumentPatternController],
      providers: [
        { provide: DocumentsService, useValue: mockDocumentsService },
        { provide: LegalPatternRecognitionService, useValue: mockPatternRecognitionService },
        { provide: getQueueToken(DOCUMENT_PROCESSING_QUEUE), useValue: mockQueue }
      ],
    }).compile();

    controller = module.get<DocumentPatternController>(DocumentPatternController);
    documentsService = module.get<DocumentsService>(DocumentsService);
    patternRecognitionService = module.get<LegalPatternRecognitionService>(LegalPatternRecognitionService);
    documentProcessingQueue = module.get(getQueueToken(DOCUMENT_PROCESSING_QUEUE));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('analyzeDocumentPatterns', () => {
    it('should add a pattern analysis job to the queue for a valid document', async () => {
      // Mock document exists
      mockDocumentsService.findById.mockResolvedValue({
        id: 'doc-123',
        filename: 'test.pdf'
      });

      const result = await controller.analyzeDocumentPatterns('doc-123');

      expect(mockDocumentsService.findById).toHaveBeenCalledWith('doc-123');
      expect(documentProcessingQueue.add).toHaveBeenCalledWith(
        'analyze-patterns',
        { documentId: 'doc-123' },
        expect.any(Object)
      );
      expect(result).toEqual({
        message: 'Pattern analysis job has been queued',
        jobId: 'job-123',
        documentId: 'doc-123',
        status: 'queued',
      });
    });

    it('should throw NotFoundException when document does not exist', async () => {
      // Mock document does not exist
      mockDocumentsService.findById.mockResolvedValue(null);

      await expect(controller.analyzeDocumentPatterns('non-existent')).rejects.toThrow(NotFoundException);
    });
  });

  describe('getDocumentPatterns', () => {
    it('should return patterns for a valid document', async () => {
      // Mock document exists
      mockDocumentsService.findById.mockResolvedValue({
        id: 'doc-123',
        filename: 'test.pdf'
      });

      // Mock metadata with patterns
      const patterns: LegalPattern[] = [
        { 
          type: 'definition', 
          content: 'def1', 
          startIndex: 0, 
          endIndex: 10, 
          metadata: { title: 'Definition 1' } 
        },
        { 
          type: 'obligation', 
          content: 'obl1', 
          startIndex: 20, 
          endIndex: 30,
          metadata: { title: 'Obligation 1' }
        }
      ];
      
      mockDocumentsService.getMetadata.mockResolvedValue({
        patterns,
        patternAnalysisDate: new Date('2025-01-01')
      });

      mockPatternRecognitionService.getPatternStatistics.mockReturnValue({
        totalPatterns: 2,
        typeDistribution: { definition: 1, obligation: 1 }
      });

      const result = await controller.getDocumentPatterns('doc-123');

      expect(mockDocumentsService.findById).toHaveBeenCalledWith('doc-123');
      expect(mockDocumentsService.getMetadata).toHaveBeenCalledWith('doc-123');
      expect(mockPatternRecognitionService.getPatternStatistics).toHaveBeenCalled();
      expect(result).toEqual({
        documentId: 'doc-123',
        patternCount: 2,
        patterns,
        statistics: {
          totalPatterns: 2,
          typeDistribution: { definition: 1, obligation: 1 }
        },
        analyzedAt: expect.any(Date)
      });
    });

    it('should return empty patterns when document has no patterns', async () => {
      // Mock document exists but has no patterns
      mockDocumentsService.findById.mockResolvedValue({
        id: 'doc-123',
        filename: 'test.pdf'
      });
      
      mockDocumentsService.getMetadata.mockResolvedValue({});

      const result = await controller.getDocumentPatterns('doc-123');

      expect(result).toEqual({
        message: 'No patterns found for this document',
        documentId: 'doc-123',
        patterns: [],
      });
    });

    it('should filter patterns by type when type parameter is provided', async () => {
      // Mock document exists
      mockDocumentsService.findById.mockResolvedValue({
        id: 'doc-123',
        filename: 'test.pdf'
      });

      // Mock metadata with patterns
      const patterns: LegalPattern[] = [
        { 
          type: 'definition', 
          content: 'def1', 
          startIndex: 0, 
          endIndex: 10,
          metadata: { title: 'Definition 1' }
        },
        { 
          type: 'obligation', 
          content: 'obl1', 
          startIndex: 20, 
          endIndex: 30,
          metadata: { title: 'Obligation 1' }
        }
      ];
      
      mockDocumentsService.getMetadata.mockResolvedValue({
        patterns,
        patternAnalysisDate: new Date('2025-01-01')
      });

      mockPatternRecognitionService.getPatternStatistics.mockReturnValue({
        totalPatterns: 1,
        typeDistribution: { definition: 1 }
      });

      const result = await controller.getDocumentPatterns('doc-123', 'definition');

      expect(result.patterns.length).toBe(1);
      expect(result.patterns[0].type).toBe('definition');
    });
  });

  describe('getDocumentPatternsByType', () => {
    it('should return patterns of a specific type', async () => {
      // Mock document exists
      mockDocumentsService.findById.mockResolvedValue({
        id: 'doc-123',
        filename: 'test.pdf'
      });

      // Mock metadata with patterns
      const patterns: LegalPattern[] = [
        { 
          type: 'definition', 
          content: 'def1', 
          startIndex: 0, 
          endIndex: 10,
          metadata: { title: 'Definition 1' }
        },
        { 
          type: 'obligation', 
          content: 'obl1', 
          startIndex: 20, 
          endIndex: 30,
          metadata: { title: 'Obligation 1' }
        }
      ];
      
      mockDocumentsService.getMetadata.mockResolvedValue({
        patterns,
        patternAnalysisDate: new Date('2025-01-01')
      });

      const definitionPatterns = [
        { 
          type: 'definition', 
          content: 'def1', 
          startIndex: 0, 
          endIndex: 10,
          metadata: { title: 'Definition 1' }
        }
      ];
      
      mockPatternRecognitionService.getPatternsByType.mockReturnValue(definitionPatterns);

      const result = await controller.getDocumentPatternsByType('doc-123', 'definition');

      expect(mockDocumentsService.findById).toHaveBeenCalledWith('doc-123');
      expect(mockDocumentsService.getMetadata).toHaveBeenCalledWith('doc-123');
      expect(mockPatternRecognitionService.getPatternsByType).toHaveBeenCalled();
      expect(result).toEqual({
        documentId: 'doc-123',
        type: 'definition',
        count: 1,
        patterns: definitionPatterns,
        analyzedAt: expect.any(Date)
      });
    });
  });
});

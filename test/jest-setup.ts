const mongoose = require('mongoose');

// Mock the document processor worker
jest.mock('../src/modules/documents/workers/document-processor.worker', () => ({
  DocumentProcessorWorker: jest.fn().mockImplementation(() => ({
    processDocument: jest.fn().mockResolvedValue({
      content: 'Processed document content',
      metadata: {
        sections: [],
        clauses: []
      }
    }),
    handleMessage: jest.fn(),
    terminate: jest.fn()
  }))
}));

// Mock PromptTemplateService
jest.mock('../src/modules/prompt-templates/prompt-template.service', () => ({
  PromptTemplateService: jest.fn().mockImplementation(() => ({
    templates: [
      {
        type: 'contract',
        template: 'Mock template for contract'
      }
    ],
    generateChatPrompt: jest.fn().mockReturnValue('Mock prompt'),
    loadTemplates: jest.fn()
  }))
}));

// Mock GeminiService
jest.mock('../src/modules/gemini/gemini.service', () => ({
  GeminiService: jest.fn().mockImplementation(() => ({
    generateEnhancedResponse: jest.fn().mockResolvedValue('Mock AI response'),
    generateResponse: jest.fn().mockResolvedValue('Mock AI response'),
    getRateLimiterUtilization: jest.fn().mockReturnValue({ utilizationPercentage: 0.5 })
  }))
}));

// Mock Mongoose models and their methods
jest.mock('../src/modules/chat/schemas/chat-analytics.schema', () => ({
  ChatAnalytics: jest.fn().mockImplementation(() => ({
    findOne: jest.fn().mockResolvedValue({}),
    findOneAndUpdate: jest.fn().mockResolvedValue({}),
  })),
  ChatAnalyticsSchema: {}
}));

jest.mock('../src/modules/documents/schemas/document.schema', () => {
  return {
    LegalDocument: jest.fn().mockImplementation(() => ({
      findById: jest.fn().mockResolvedValue({})
    })),
    LegalDocumentSchema: {
      index: jest.fn()
    }
  };
});

jest.mock('../src/modules/chat/schemas/chat-session.schema', () => {
  return {
    ChatSession: jest.fn().mockImplementation(() => ({
      findById: jest.fn().mockResolvedValue({})
    })),
    ChatSessionSchema: {
      index: jest.fn()
    }
  };
});

jest.mock('../src/modules/documents/schemas/analysis-result.schema', () => {
  return {
    AnalysisResult: jest.fn().mockImplementation(() => ({})),
    AnalysisResultSchema: {
      index: jest.fn()
    }
  };
});

// Mock NestJS common decorators
jest.mock('@nestjs/common', () => {
  const actual = jest.requireActual('@nestjs/common');
  return {
    ...actual,
    Injectable: () => () => {},
    Module: () => () => {},
    Controller: () => () => {},
    Get: () => () => {},
    Post: () => () => {},
    Put: () => () => {},
    Delete: () => () => {},
    Body: () => () => {},
    Param: () => () => {},
    Query: () => () => {},
  };
});

// Mock Mongoose and its decorators
jest.mock('@nestjs/mongoose', () => {
  const actual = jest.requireActual('@nestjs/mongoose');
  return {
    ...actual,
    Prop: () => () => {},
    Schema: () => () => {},
    SchemaFactory: {
      createForClass: jest.fn((entity) => ({
        ...entity,
        index: jest.fn(),
      })),
    },
    InjectModel: () => () => {},
    MongooseModule: {
      forRoot: jest.fn(),
      forRootAsync: jest.fn(() => ({
        module: class DynamicModule {},
        imports: [],
        providers: [],
        exports: []
      })),
      forFeature: jest.fn(() => ({})),
    },
  };
});

// Mock our Auth services
jest.mock('../src/modules/auth/services/auth.service', () => ({
  AuthService: jest.fn().mockImplementation(() => ({
    validateUser: jest.fn().mockResolvedValue({
      _id: 'mock-user-id',
      username: 'testuser',
      role: 'user'
    }),
    login: jest.fn().mockResolvedValue({
      access_token: 'mock-jwt-token',
      user: {
        id: 'mock-user-id',
        username: 'testuser',
        role: 'user'
      }
    }),
    createUser: jest.fn().mockResolvedValue({
      _id: 'mock-user-id',
      username: 'testuser',
      email: '<EMAIL>',
      role: 'user',
      isActive: true
    })
  }))
}));

jest.mock('../src/modules/auth/services/user.service', () => ({
  UserService: jest.fn().mockImplementation(() => ({
    findByUsername: jest.fn().mockResolvedValue({
      _id: 'mock-user-id',
      username: 'testuser',
      password: 'hashedPassword',
      role: 'user',
      isActive: true
    }),
    findById: jest.fn().mockResolvedValue({
      _id: 'mock-user-id',
      username: 'testuser',
      role: 'user',
      isActive: true
    }),
    create: jest.fn().mockResolvedValue({
      _id: 'mock-user-id',
      username: 'testuser',
      email: '<EMAIL>',
      role: 'user',
      isActive: true
    }),
    validateUser: jest.fn().mockResolvedValue({
      _id: 'mock-user-id',
      username: 'testuser',
      role: 'user'
    })
  }))
}));

// Mock JwtService
jest.mock('@nestjs/jwt', () => ({
  JwtService: jest.fn().mockImplementation(() => ({
    sign: jest.fn().mockReturnValue('mock-jwt-token'),
    verify: jest.fn().mockReturnValue({
      sub: 'mock-user-id',
      username: 'testuser',
      role: 'user'
    })
  }))
}));

// Mock PassportModule
jest.mock('@nestjs/passport', () => ({
  PassportStrategy: jest.fn().mockImplementation(Strategy => class extends Strategy {
    constructor() {
      super();
    }
    validate() {
      return {
        userId: 'mock-user-id',
        username: 'testuser',
        role: 'user'
      };
    }
  }),
  AuthGuard: jest.fn().mockImplementation(() => ({
    canActivate: jest.fn().mockReturnValue(true)
  }))
}));

// Set up test environment variables
process.env.MONGODB_URI = 'mongodb://127.0.0.1:27017/test-db';
process.env.STORAGE_UPLOAD_DIR = 'uploads-test';
process.env.GEMINI_API_KEY = 'mock-api-key';
process.env.DATABASE_TYPE = 'mongodb';

// Create directories needed for tests
const fs = require('fs');
const path = require('path');

const testDirs = ['uploads-test', 'uploads-test/chat-sessions', 'uploads-test/chat-attachments'];
testDirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Cleanup function to run after all tests
afterAll(async () => {
  // Clean up test directories
  testDirs.forEach(dir => {
    if (fs.existsSync(dir)) {
      fs.rmSync(dir, { recursive: true, force: true });
    }
  });
});
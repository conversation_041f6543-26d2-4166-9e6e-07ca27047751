import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { UnauthorizedException } from '@nestjs/common';
import { Request, Response } from 'express';
import { TenantContextMiddleware } from '../../src/modules/auth/middleware/tenant-context.middleware';
import { TenantContextService } from '../../src/modules/auth/services/tenant-context.service';

describe('TenantContextMiddleware', () => {
  let middleware: TenantContextMiddleware;
  let jwtService: JwtService;
  let tenantContextService: TenantContextService;

  const mockJwtService = {
    verifyAsync: jest.fn(),
  };

  const mockTenantContextService = {
    runWithContext: jest.fn((context, callback) => callback()),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TenantContextMiddleware,
        {
          provide: JwtService,
          useValue: mockJwtService,
        },
        {
          provide: TenantContextService,
          useValue: mockTenantContextService,
        },
      ],
    }).compile();

    middleware = module.get<TenantContextMiddleware>(TenantContextMiddleware);
    jwtService = module.get<JwtService>(JwtService);
    tenantContextService = module.get<TenantContextService>(TenantContextService);
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const mockRequest = () => {
    const req = {
      headers: {} as Record<string, string>,
      get: jest.fn(),
      header: jest.fn(),
      accepts: jest.fn(),
      acceptsCharsets: jest.fn(),
      acceptsEncodings: jest.fn(),
      acceptsLanguages: jest.fn(),
      param: jest.fn(),
      is: jest.fn(),
      range: jest.fn(),
      accepted: [],
      ips: [],
      subdomains: [],
      app: {},
      res: {},
      next: jest.fn(),
      body: {},
      cookies: {},
      fresh: false,
      hostname: 'localhost',
      method: 'GET',
      params: {},
      query: {},
      route: {},
      secure: false,
      signedCookies: {},
      stale: false,
      xhr: false,
      protocol: 'http',
      originalUrl: '/api/test',
      baseUrl: '',
      url: '/api/test',
      ip: '',
      socket: {} as any,
    };

    return Object.assign(req, {
      accepts: () => [],
      acceptsEncodings: () => [],
      acceptsCharsets: () => [],
      acceptsLanguages: () => [],
      range: () => undefined,
      get: (header: string) => req.headers[header],
    }) as unknown as Request;
  };

  const mockResponse = () => ({} as Response);
  const mockNext = jest.fn();

  it('should be defined', () => {
    expect(middleware).toBeDefined();
  });

  describe('token validation', () => {
    it('should throw UnauthorizedException when no token is provided', async () => {
      const req = mockRequest();
      await expect(middleware.use(req, mockResponse(), mockNext)).rejects
        .toThrow(UnauthorizedException);
    });

    it('should throw UnauthorizedException for invalid token format', async () => {
      const req = mockRequest();
      req.headers.authorization = 'Invalid token';
      await expect(middleware.use(req, mockResponse(), mockNext)).rejects
        .toThrow(UnauthorizedException);
    });

    it('should allow public routes without token', async () => {
      const req = mockRequest();
      req.url = '/auth/login';
      await middleware.use(req, mockResponse(), mockNext);
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('tenant context handling', () => {
    const validToken = 'valid.jwt.token';
    const payload = {
      sub: 'user-1',
      organizationId: 'org-1',
      role: 'user',
    };

    beforeEach(() => {
      mockJwtService.verifyAsync.mockResolvedValue(payload);
    });

    it('should set tenant context for valid token', async () => {
      const req = mockRequest();
      req.headers.authorization = `Bearer ${validToken}`;

      await middleware.use(req, mockResponse(), mockNext);

      expect(mockJwtService.verifyAsync).toHaveBeenCalledWith(validToken);
      expect(mockTenantContextService.runWithContext).toHaveBeenCalledWith(
        {
          organizationId: payload.organizationId,
          userId: payload.sub,
        },
        expect.any(Function),
      );
      expect(mockNext).toHaveBeenCalled();
    });

    it('should throw UnauthorizedException when token lacks organizationId', async () => {
      const req = mockRequest();
      req.headers.authorization = `Bearer ${validToken}`;
      mockJwtService.verifyAsync.mockResolvedValue({ sub: 'user-1' });

      await expect(middleware.use(req, mockResponse(), mockNext)).rejects
        .toThrow(UnauthorizedException);
    });

    it('should handle JWT verification errors', async () => {
      const req = mockRequest();
      req.headers.authorization = `Bearer ${validToken}`;
      mockJwtService.verifyAsync.mockRejectedValue(new Error('Invalid token'));

      await expect(middleware.use(req, mockResponse(), mockNext)).rejects
        .toThrow(UnauthorizedException);
    });
  });
});

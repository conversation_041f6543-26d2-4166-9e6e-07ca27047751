const fs = require('fs');
const PDFDocument = require('pdfkit');

/**
 * Creates a PDF from a text file
 * @param {string} inputPath - Path to the text file
 * @param {string} outputPath - Path to save the PDF
 */
function createPdfFromText(inputPath, outputPath) {
  try {
    // Read the text content
    const text = fs.readFileSync(inputPath, 'utf8');
    
    // Create a PDF document
    const doc = new PDFDocument();
    const stream = fs.createWriteStream(outputPath);
    doc.pipe(stream);
    
    // Add the text to the PDF
    doc.fontSize(12);
    doc.text(text, {
      paragraphGap: 10,
      align: 'left'
    });
    
    // Finalize the PDF
    doc.end();
    
    console.log(`Created PDF: ${outputPath}`);
    
    return new Promise((resolve) => {
      stream.on('finish', resolve);
    });
  } catch (error) {
    console.error(`Error creating PDF: ${error.message}`);
    throw error;
  }
}

async function main() {
  // Create PDFs from the text files
  await createPdfFromText(
    './test/fixtures/sample-contract.txt',
    './test/fixtures/sample-contract.pdf'
  );
  
  await createPdfFromText(
    './test/fixtures/sample-amendment.txt',
    './test/fixtures/sample-amendment.pdf'
  );
  
  console.log('All PDFs created successfully');
}

main().catch(err => {
  console.error('Error:', err);
  process.exit(1);
});

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { GamificationService } from '../src/modules/gamification/services/gamification.service';
import { AchievementService } from '../src/modules/gamification/services/achievement.service';
import { CharacterService } from '../src/modules/gamification/services/character.service';
import { NegotiationSimulatorService } from '../src/modules/documents/services/negotiation-simulator.service';

describe('Gamification Integration (e2e)', () => {
  let app: INestApplication;
  let gamificationService: GamificationService;
  let achievementService: AchievementService;
  let characterService: CharacterService;
  let negotiationService: NegotiationSimulatorService;

  const testUser = {
    id: 'test-user-123',
    organizationId: 'test-org-123',
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    gamificationService = moduleFixture.get<GamificationService>(GamificationService);
    achievementService = moduleFixture.get<AchievementService>(AchievementService);
    characterService = moduleFixture.get<CharacterService>(CharacterService);
    negotiationService = moduleFixture.get<NegotiationSimulatorService>(NegotiationSimulatorService);

    // Seed test data
    await characterService.seedCharacters();
    await achievementService.seedAchievements();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Gamification Service', () => {
    it('should create user gamification profile', async () => {
      const profile = await gamificationService.getUserGamification(
        testUser.id,
        testUser.organizationId
      );

      expect(profile).toBeDefined();
      expect(profile.userId).toBe(testUser.id);
      expect(profile.level.current).toBe(1);
      expect(profile.level.title).toBe('Rookie Negotiator');
      expect(profile.statistics.totalSessions).toBe(0);
    });

    it('should award experience points', async () => {
      const levelUpdate = await gamificationService.awardExperience(
        testUser.id,
        100,
        'test_award'
      );

      expect(levelUpdate).toBeDefined();
      expect(levelUpdate.xpGained).toBe(100);
      expect(levelUpdate.totalXP).toBe(100);
    });

    it('should handle level up', async () => {
      const levelUpdate = await gamificationService.awardExperience(
        testUser.id,
        500, // Should trigger level up
        'level_up_test'
      );

      expect(levelUpdate.leveledUp).toBe(true);
      expect(levelUpdate.newLevel).toBe(2);
      expect(levelUpdate.title).toBe('Junior Professional');
    });
  });

  describe('Character Service', () => {
    it('should get default character', async () => {
      const character = await characterService.getDefaultCharacter();

      expect(character).toBeDefined();
      expect(character.isDefault).toBe(true);
      expect(character.difficulty).toBe(1);
    });

    it('should get all characters', async () => {
      const characters = await characterService.getAllCharacters();

      expect(characters).toBeDefined();
      expect(characters.length).toBeGreaterThan(0);
      expect(characters[0]).toHaveProperty('name');
      expect(characters[0]).toHaveProperty('difficulty');
    });

    it('should check unlock requirements', async () => {
      const characters = await characterService.getAllCharacters();
      const lockedCharacter = characters.find(c => !c.isDefault);

      if (lockedCharacter) {
        const canUnlock = await characterService.checkUnlockRequirements(
          lockedCharacter.id,
          1, // level 1
          [], // no achievements
          0 // no sessions
        );

        expect(typeof canUnlock).toBe('boolean');
      }
    });
  });

  describe('Achievement Service', () => {
    it('should get active achievements', async () => {
      const achievements = await achievementService.getActiveAchievements();

      expect(achievements).toBeDefined();
      expect(achievements.length).toBeGreaterThan(0);
      expect(achievements[0]).toHaveProperty('title');
      expect(achievements[0]).toHaveProperty('requirements');
    });

    it('should check session achievements', async () => {
      const sessionData = {
        metrics: {
          totalRounds: 2,
          agreementReached: true,
          overallScore: 8.5,
        },
      };

      const newAchievements = await achievementService.checkSessionAchievements(
        testUser.id,
        'test-session-123',
        sessionData
      );

      expect(Array.isArray(newAchievements)).toBe(true);
    });
  });

  describe('Enhanced Negotiation Simulator', () => {
    it('should start session with character', async () => {
      const defaultCharacter = await characterService.getDefaultCharacter();
      
      const sessionDto = {
        scenarioId: 'test-scenario',
        characterId: defaultCharacter.id,
      };

      // Note: This would require a mock scenario to exist
      // In a real test, you'd create a test scenario first
      try {
        const session = await negotiationService.startSession(
          sessionDto,
          testUser.id,
          testUser.organizationId
        );

        expect(session).toBeDefined();
        expect(session.characterId).toBe(defaultCharacter.id);
        expect(session.gamificationData).toBeDefined();
        expect(session.gameState).toBeDefined();
      } catch (error) {
        // Expected if no test scenario exists
        expect(error.message).toContain('not found');
      }
    });
  });

  describe('API Endpoints', () => {
    let authToken: string;

    beforeAll(async () => {
      // Mock authentication token
      authToken = 'mock-jwt-token';
    });

    it('/api/gamification/profile (GET)', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/gamification/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('profile');
      expect(response.body.profile).toHaveProperty('level');
      expect(response.body.profile).toHaveProperty('statistics');
    });

    it('/api/gamification/characters (GET)', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/gamification/characters')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('characters');
      expect(Array.isArray(response.body.characters)).toBe(true);
    });

    it('/api/gamification/achievements (GET)', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/gamification/achievements')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('achievements');
      expect(response.body).toHaveProperty('summary');
    });

    it('/api/gamification/leaderboards (GET)', async () => {
      const response = await request(app.getHttpServer())
        .get('/api/gamification/leaderboards')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('rankings');
      expect(response.body).toHaveProperty('totalParticipants');
    });
  });

  describe('Integration Flow', () => {
    it('should complete full gamification flow', async () => {
      // 1. Create user profile
      const profile = await gamificationService.getUserGamification(
        'flow-test-user',
        testUser.organizationId
      );
      expect(profile.level.current).toBe(1);

      // 2. Award XP and level up
      const levelUpdate = await gamificationService.awardExperience(
        'flow-test-user',
        600,
        'integration_test'
      );
      expect(levelUpdate.leveledUp).toBe(true);

      // 3. Check for achievements
      const achievements = await achievementService.checkLevelAchievements(
        'flow-test-user',
        levelUpdate.newLevel
      );
      expect(Array.isArray(achievements)).toBe(true);

      // 4. Verify profile update
      const updatedProfile = await gamificationService.getUserGamification(
        'flow-test-user',
        testUser.organizationId
      );
      expect(updatedProfile.level.current).toBe(2);
    });
  });
});

// Helper function to create mock session data
function createMockSessionData(overrides = {}) {
  return {
    currentRound: 3,
    status: 'active',
    metrics: {
      totalRounds: 3,
      agreementReached: false,
      overallScore: 7.0,
      userSatisfaction: 0.8,
      aiSatisfaction: 0.7,
    },
    gameState: {
      userStress: 0.4,
      aiMood: 'neutral',
      currentScore: 7.0,
      timeRemaining: 900,
    },
    ...overrides,
  };
}

services:
  - type: web
    name: docgic-api
    env: node
    region: oregon
    buildCommand: npm ci && npm run build
    startCommand: npm run start:prod
    healthCheckPath: /api/health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 4000
      - key: MONGODB_URI
        fromDatabase:
          name: docgic-mongodb
          property: connectionString
      - key: JWT_SECRET
        generateValue: true
      - key: JWT_EXPIRES_IN
        value: 7d
      - key: SESSION_SECRET
        generateValue: true
      - key: GOOGLE_CLIENT_ID
        sync: false
      - key: GOOGLE_CLIENT_SECRET
        sync: false
      - key: STRIPE_SECRET_KEY
        sync: false
      - key: STRIPE_PUBLISHABLE_KEY
        sync: false
      - key: STRIPE_WEBHOOK_SECRET
        sync: false
      - key: OPENAI_API_KEY
        sync: false
      - key: ANTHROPIC_API_KEY
        sync: false
      - key: POSTHOG_API_KEY
        sync: false
      - key: POSTHOG_HOST
        value: https://app.posthog.com
      - key: CORS_ORIGIN
        value: https://your-frontend-domain.com
      - key: BACKEND_URL
        value: https://docgic-api.onrender.com
      - key: FRONTEND_URL
        value: https://your-frontend-domain.com
      - key: REDIS_URL
        sync: false
      - key: EMAIL_HOST
        sync: false
      - key: EMAIL_PORT
        value: 587
      - key: EMAIL_USER
        sync: false
      - key: EMAIL_PASS
        sync: false
      - key: EMAIL_FROM
        sync: false
      - key: WEBHOOK_SECRET
        generateValue: true
      - key: ENCRYPTION_KEY
        generateValue: true
      - key: RATE_LIMIT_WINDOW
        value: 900000
      - key: RATE_LIMIT_MAX
        value: 100
      - key: FILE_UPLOAD_LIMIT
        value: 10485760
      - key: MAX_FILE_SIZE
        value: 10485760
      - key: UPLOAD_DIR
        value: /tmp/uploads
      - key: LOG_LEVEL
        value: info
      - key: ENABLE_SWAGGER
        value: true
      - key: SWAGGER_PATH
        value: api/docs
      - key: API_VERSION
        value: v1
      - key: GAMIFICATION_ENABLED
        value: true
      - key: CREDIT_SYSTEM_ENABLED
        value: true
      - key: WORKFLOW_ENABLED
        value: true
      - key: COLLABORATION_ENABLED
        value: true
      - key: ANALYTICS_ENABLED
        value: true
      - key: AUDIT_ENABLED
        value: true
      - key: LEGAL_RESEARCH_ENABLED
        value: true
      - key: CHAT_NEGOTIATION_ENABLED
        value: true
      - key: EMAIL_AUTOMATION_ENABLED
        value: true
      - key: DOCUMENT_CLASSIFICATION_ENABLED
        value: true
      - key: SUBSCRIPTION_ENABLED
        value: true
      - key: STRIPE_PLAN_PRO
        sync: false
      - key: STRIPE_PLAN_ADMIN
        sync: false
      - key: STRIPE_CREDIT_STUDENT
        sync: false
      - key: STRIPE_CREDIT_LAWYER_SMALL
        sync: false
      - key: STRIPE_CREDIT_LAWYER_LARGE
        sync: false
      - key: STRIPE_CREDIT_FIRM_STANDARD
        sync: false
      - key: STRIPE_CREDIT_FIRM_ENTERPRISE
        sync: false
    disk:
      name: docgic-disk
      mountPath: /tmp
      sizeGB: 5
    scaling:
      minInstances: 1
      maxInstances: 3
      targetMemoryPercent: 80
      targetCPUPercent: 80

databases:
  - name: docgic-mongodb
    databaseName: docgic_production
    user: docgic_user
    region: oregon
    plan: starter
    
  - name: docgic-redis
    databaseName: docgic_cache
    region: oregon
    plan: starter 
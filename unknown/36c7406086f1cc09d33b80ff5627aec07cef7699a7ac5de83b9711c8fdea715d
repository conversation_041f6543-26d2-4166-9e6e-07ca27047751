import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
  HttpStatus,
  HttpCode,
  UnauthorizedException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { ComplianceAuditorService } from '../services/compliance-auditor.service';
import { UseCredits, FreeFeature } from '../../subscription/decorators/use-credits.decorator';
import {
  AuditDocumentDto,
  CreateComplianceProfileDto,
  ComplianceQueryDto,
  GenerateComplianceReportDto,
  UpdateComplianceProfileDto,
} from '../dto/compliance-auditor.dto';
import {
  ComplianceAuditResult,
  ComplianceProfile,
} from '../interfaces/compliance-auditor.interface';
import { PostHogService } from '../../posthog/services/posthog.service';

@ApiTags('Compliance Auditor')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('compliance')
export class ComplianceAuditorController {
  private readonly logger = new Logger(ComplianceAuditorController.name);

  constructor(
    private readonly complianceAuditorService: ComplianceAuditorService,
    private readonly tenantContext: TenantContextService,
    private readonly postHogService: PostHogService,
  ) {}

  /**
   * Get current user context with proper error handling
   */
  private getCurrentUserContext(): { userId: string; organizationId: string } {
    const userId = this.tenantContext.getCurrentUserId();
    const organizationId = this.tenantContext.getCurrentOrganization();

    if (!userId || !organizationId) {
      throw new UnauthorizedException('User context not found');
    }

    return { userId, organizationId };
  }

  @Post('audit')
  @UseCredits('compliance_audit') // AI compliance auditing consumes credits
  @ApiOperation({ summary: 'Audit a document for compliance' })
  @ApiResponse({
    status: 201,
    description: 'Document audited successfully',
    type: Object,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid audit request',
  })
  @ApiResponse({
    status: 404,
    description: 'Document not found',
  })
  async auditDocument(
    @Body() dto: AuditDocumentDto,
  ): Promise<ComplianceAuditResult> {
    const { userId, organizationId } = this.getCurrentUserContext();
    const startTime = Date.now();

    this.logger.log(
      `Starting compliance audit for document: ${dto.documentId} by user: ${userId}`,
    );

    try {
      const result = await this.complianceAuditorService.auditDocument(
        dto,
        userId,
        organizationId,
      );

      const duration = Date.now() - startTime;

      // Track successful compliance audit
      this.postHogService.trackEvent(userId, 'compliance_audit_completed', {
        document_id: dto.documentId,
        organization_id: organizationId,
        duration_ms: duration,
        overall_score: result.overallScore,
        risk_level: result.riskLevel,
        findings_count: result.findings?.length || 0
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;

      // Track failed compliance audit
      this.postHogService.trackError(userId, error, 'compliance_audit', {
        document_id: dto.documentId,
        organization_id: organizationId,
        duration_ms: duration
      });

      throw error;
    }
  }

  @Get('audit-results')
  @FreeFeature() // Viewing audit results is free
  @ApiOperation({ summary: 'Get compliance audit results' })
  @ApiResponse({
    status: 200,
    description: 'Audit results retrieved successfully',
    type: Object,
  })
  @ApiQuery({
    name: 'regulations',
    required: false,
    description: 'Filter by regulations (comma-separated)',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter by compliance status',
  })
  @ApiQuery({
    name: 'riskLevel',
    required: false,
    description: 'Filter by risk level',
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    description: 'Filter by start date (ISO string)',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    description: 'Filter by end date (ISO string)',
  })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  @ApiQuery({ name: 'sortBy', required: false, description: 'Sort field' })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    description: 'Sort order (asc/desc)',
  })
  async getAuditResults(@Query() query: ComplianceQueryDto): Promise<{
    results: ComplianceAuditResult[];
    total: number;
    page: number;
    limit: number;
    pages: number;
  }> {
    const { organizationId } = this.getCurrentUserContext();

    // Parse regulations if provided
    const parsedQuery = { ...query };
    if (query.regulations && typeof query.regulations === 'string') {
      (parsedQuery as any).regulations = (query.regulations as string)
        .split(',')
        .map((r) => r.trim());
    }

    const { results, total } =
      await this.complianceAuditorService.getAuditResults(
        organizationId,
        parsedQuery,
      );

    const page = query.page || 1;
    const limit = query.limit || 20;
    const pages = Math.ceil(total / limit);

    return {
      results,
      total,
      page,
      limit,
      pages,
    };
  }

  @Get('audit-results/:resultId')
  @FreeFeature() // Viewing specific audit results is free
  @ApiOperation({ summary: 'Get a specific audit result' })
  @ApiResponse({
    status: 200,
    description: 'Audit result retrieved successfully',
    type: Object,
  })
  @ApiResponse({
    status: 404,
    description: 'Audit result not found',
  })
  @ApiParam({ name: 'resultId', description: 'Audit result ID' })
  async getAuditResult(
    @Param('resultId') resultId: string,
  ): Promise<ComplianceAuditResult> {
    const { organizationId } = this.getCurrentUserContext();
    return this.complianceAuditorService.getAuditResult(
      resultId,
      organizationId,
    );
  }

  @Post('profiles')
  @FreeFeature() // Creating compliance profiles is free
  @ApiOperation({ summary: 'Create a compliance profile' })
  @ApiResponse({
    status: 201,
    description: 'Profile created successfully',
    type: Object,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid profile data',
  })
  async createProfile(
    @Body() dto: CreateComplianceProfileDto,
  ): Promise<ComplianceProfile> {
    const { userId, organizationId } = this.getCurrentUserContext();

    this.logger.log(
      `Creating compliance profile: ${dto.name} for user: ${userId}`,
    );

    return this.complianceAuditorService.createProfile(
      dto,
      userId,
      organizationId,
    );
  }

  @Get('profiles')
  @FreeFeature() // Viewing compliance profiles is free
  @ApiOperation({ summary: 'Get compliance profiles' })
  @ApiResponse({
    status: 200,
    description: 'Profiles retrieved successfully',
    type: [Object],
  })
  async getProfiles(): Promise<ComplianceProfile[]> {
    const { organizationId } = this.getCurrentUserContext();
    return this.complianceAuditorService.getProfiles(organizationId);
  }

  @Get('profiles/:profileId')
  @ApiOperation({ summary: 'Get a specific compliance profile' })
  @ApiResponse({
    status: 200,
    description: 'Profile retrieved successfully',
    type: Object,
  })
  @ApiResponse({
    status: 404,
    description: 'Profile not found',
  })
  @ApiParam({ name: 'profileId', description: 'Profile ID' })
  async getProfile(
    @Param('profileId') profileId: string,
  ): Promise<ComplianceProfile> {
    const { organizationId } = this.getCurrentUserContext();
    return this.complianceAuditorService.getProfile(profileId, organizationId);
  }

  @Get('frameworks')
  @ApiOperation({ summary: 'Get available regulatory frameworks' })
  @ApiResponse({
    status: 200,
    description: 'Frameworks retrieved successfully',
    type: [Object],
  })
  async getFrameworks(): Promise<any[]> {
    // Return built-in frameworks for now
    return [
      {
        id: 'gdpr',
        name: 'General Data Protection Regulation',
        version: '2018',
        jurisdiction: 'EU',
        industry: ['all'],
        description: 'EU regulation on data protection and privacy',
        applicableDocumentTypes: [
          'privacy-policy',
          'data-processing-agreement',
          'contract',
        ],
      },
      {
        id: 'sox',
        name: 'Sarbanes-Oxley Act',
        version: '2002',
        jurisdiction: 'US',
        industry: ['finance', 'public-companies'],
        description:
          'US federal law for financial reporting and corporate governance',
        applicableDocumentTypes: [
          'financial-report',
          'audit-report',
          'internal-controls',
        ],
      },
      {
        id: 'hipaa',
        name: 'Health Insurance Portability and Accountability Act',
        version: '1996',
        jurisdiction: 'US',
        industry: ['healthcare'],
        description: 'US law for healthcare information privacy and security',
        applicableDocumentTypes: [
          'medical-record',
          'patient-agreement',
          'healthcare-contract',
        ],
      },
      {
        id: 'pci-dss',
        name: 'Payment Card Industry Data Security Standard',
        version: '4.0',
        jurisdiction: 'Global',
        industry: ['finance', 'retail', 'e-commerce'],
        description:
          'Security standard for organizations that handle credit card information',
        applicableDocumentTypes: [
          'payment-policy',
          'security-policy',
          'vendor-agreement',
        ],
      },
    ];
  }

  @Get('analytics/overview')
  @ApiOperation({ summary: 'Get compliance analytics overview' })
  @ApiResponse({
    status: 200,
    description: 'Analytics retrieved successfully',
    type: Object,
  })
  async getAnalyticsOverview(): Promise<any> {
    const { organizationId } = this.getCurrentUserContext();

    // Get recent audit results for analytics
    const { results } = await this.complianceAuditorService.getAuditResults(
      organizationId,
      {
        page: 1,
        limit: 100,
        sortBy: 'auditDate',
        sortOrder: 'desc',
      },
    );

    const totalAudits = results.length;
    const compliantCount = results.filter(
      (r) => r.status === 'compliant',
    ).length;
    const nonCompliantCount = results.filter(
      (r) => r.status === 'non-compliant',
    ).length;
    const partialCount = results.filter((r) => r.status === 'partial').length;
    const needsReviewCount = results.filter(
      (r) => r.status === 'needs-review',
    ).length;

    const averageScore =
      totalAudits > 0
        ? results.reduce((sum, r) => sum + r.overallScore, 0) / totalAudits
        : 0;

    const riskDistribution = {
      low: results.filter((r) => r.riskLevel === 'low').length,
      medium: results.filter((r) => r.riskLevel === 'medium').length,
      high: results.filter((r) => r.riskLevel === 'high').length,
      critical: results.filter((r) => r.riskLevel === 'critical').length,
    };

    // Get top issues
    const allFindings = results.flatMap((r) => r.findings);
    const failedFindings = allFindings.filter((f) => f.status === 'fail');
    const issueFrequency = failedFindings.reduce((acc, finding) => {
      const key = finding.category;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const topIssues = Object.entries(issueFrequency)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([issue, frequency]) => ({
        issue,
        frequency,
        averageSeverity: this.calculateAverageSeverity(
          failedFindings.filter((f) => f.category === issue),
        ),
      }));

    // Calculate trend (simplified - comparing last 30 days vs previous 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const sixtyDaysAgo = new Date();
    sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);

    const recentResults = results.filter((r) => r.auditDate >= thirtyDaysAgo);
    const previousResults = results.filter(
      (r) => r.auditDate >= sixtyDaysAgo && r.auditDate < thirtyDaysAgo,
    );

    const recentAvgScore =
      recentResults.length > 0
        ? recentResults.reduce((sum, r) => sum + r.overallScore, 0) /
          recentResults.length
        : 0;
    const previousAvgScore =
      previousResults.length > 0
        ? previousResults.reduce((sum, r) => sum + r.overallScore, 0) /
          previousResults.length
        : 0;

    const improvementRate =
      previousAvgScore > 0
        ? ((recentAvgScore - previousAvgScore) / previousAvgScore) * 100
        : 0;

    return {
      totalAudits,
      averageScore,
      complianceDistribution: {
        compliant: compliantCount,
        nonCompliant: nonCompliantCount,
        partial: partialCount,
        needsReview: needsReviewCount,
      },
      riskDistribution,
      topIssues,
      improvementRate,
      recentAudits: results.slice(0, 5), // Last 5 audits
      trendData: this.generateTrendData(results),
    };
  }

  private calculateAverageSeverity(findings: any[]): string {
    if (findings.length === 0) return 'info';

    const severityWeights = { info: 1, warning: 2, error: 3, critical: 4 };
    const totalWeight = findings.reduce(
      (sum, f) => sum + (severityWeights[f.severity] || 1),
      0,
    );
    const avgWeight = totalWeight / findings.length;

    if (avgWeight >= 3.5) return 'critical';
    if (avgWeight >= 2.5) return 'error';
    if (avgWeight >= 1.5) return 'warning';
    return 'info';
  }

  private generateTrendData(results: ComplianceAuditResult[]): any[] {
    // Group results by week for the last 12 weeks
    const weeks = 12;
    const trendData = [];
    const now = new Date();

    for (let i = weeks - 1; i >= 0; i--) {
      const weekStart = new Date(now);
      weekStart.setDate(weekStart.getDate() - i * 7);
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekEnd.getDate() + 6);

      const weekResults = results.filter(
        (r) => r.auditDate >= weekStart && r.auditDate <= weekEnd,
      );

      const avgScore =
        weekResults.length > 0
          ? weekResults.reduce((sum, r) => sum + r.overallScore, 0) /
            weekResults.length
          : 0;

      const criticalIssues = weekResults.reduce(
        (sum, r) =>
          sum +
          r.findings.filter(
            (f) => f.severity === 'critical' && f.status === 'fail',
          ).length,
        0,
      );

      trendData.push({
        date: weekStart.toISOString().split('T')[0],
        score: Math.round(avgScore * 100) / 100,
        criticalIssues,
        totalAudits: weekResults.length,
      });
    }

    return trendData;
  }

  @Get('regulations/:regulationId/rules')
  @ApiOperation({ summary: 'Get rules for a specific regulation' })
  @ApiResponse({
    status: 200,
    description: 'Rules retrieved successfully',
    type: [Object],
  })
  @ApiParam({ name: 'regulationId', description: 'Regulation ID' })
  async getRegulationRules(
    @Param('regulationId') regulationId: string,
  ): Promise<any[]> {
    // Return simplified rule information for the regulation
    const ruleMap: Record<string, any[]> = {
      gdpr: [
        {
          id: 'gdpr-1',
          ruleNumber: 'Article 6',
          title: 'Lawful basis for processing',
          description: 'Processing must have a lawful basis',
          category: 'Data Processing',
          severity: 'critical',
        },
        {
          id: 'gdpr-2',
          ruleNumber: 'Article 7',
          title: 'Conditions for consent',
          description:
            'Consent must be freely given, specific, informed and unambiguous',
          category: 'Consent',
          severity: 'critical',
        },
        {
          id: 'gdpr-3',
          ruleNumber: 'Article 13',
          title: 'Information to be provided',
          description:
            'Information must be provided when personal data is collected',
          category: 'Transparency',
          severity: 'error',
        },
      ],
      sox: [
        {
          id: 'sox-1',
          ruleNumber: 'Section 302',
          title: 'Corporate responsibility for financial reports',
          description: 'CEO and CFO must certify financial reports',
          category: 'Financial Reporting',
          severity: 'critical',
        },
        {
          id: 'sox-2',
          ruleNumber: 'Section 404',
          title: 'Management assessment of internal controls',
          description:
            'Annual assessment of internal control over financial reporting',
          category: 'Internal Controls',
          severity: 'critical',
        },
      ],
      hipaa: [
        {
          id: 'hipaa-1',
          ruleNumber: '164.502',
          title: 'Uses and disclosures of protected health information',
          description:
            'PHI must be properly protected and disclosed only when authorized',
          category: 'Data Protection',
          severity: 'critical',
        },
        {
          id: 'hipaa-2',
          ruleNumber: '164.506',
          title: 'Consent for uses and disclosures',
          description: 'Consent requirements for PHI use and disclosure',
          category: 'Consent',
          severity: 'error',
        },
      ],
    };

    return ruleMap[regulationId] || [];
  }
}

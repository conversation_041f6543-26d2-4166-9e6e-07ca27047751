import { Controller, Post, Body, UseGuards, Logger } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { FeatureAvailabilityGuard } from '../../subscription/guards/feature-availability.guard';
import { RequireFeatures } from '../../subscription/decorators/require-features.decorator';
import { SkipSubscriptionCheck } from '../../subscription/decorators/skip-subscription-check.decorator';
import { DocumentAutomationService } from '../services/document-automation.service';
import { ClauseLibraryAutomationService } from '../services/clause-library-automation.service';
import { UseCredits } from '../../subscription/decorators/use-credits.decorator';
import {
  AIAssistedDraftingDto,
  GenerateRelatedDocumentsDto,
  ClauseIntelligenceDto,
  ClauseLibraryBuildOptionsDto,
  DocumentGenerationResult,
  RelatedDocumentGenerationResult,
  ClauseIntelligenceResult,
} from '../dto/document-automation.dto';
import { User } from '../../auth/decorators/user.decorator';
import { Organization } from '../../auth/decorators/organization.decorator';
import { PostHogService } from '../../posthog/services/posthog.service';

@ApiTags('Document Automation')
@Controller('documents/automation')
@UseGuards(JwtAuthGuard, FeatureAvailabilityGuard)
@RequireFeatures('document_automation')
@SkipSubscriptionCheck()
@ApiBearerAuth()
export class DocumentAutomationController {
  private readonly logger = new Logger(DocumentAutomationController.name);

  constructor(
    private readonly documentAutomationService: DocumentAutomationService,
    private readonly clauseLibraryAutomationService: ClauseLibraryAutomationService,
    private readonly postHogService: PostHogService,
  ) {}

  @Post('ai-assisted-drafting')
  @UseCredits('ai_assisted_drafting') // AI document generation consumes credits
  @ApiOperation({
    summary: 'AI-Assisted Document Drafting',
    description:
      'Generate new legal documents using AI based on prompts and key terms',
  })
  @ApiResponse({
    status: 201,
    description: 'Document generated successfully',
    type: DocumentGenerationResult,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters',
  })
  @ApiResponse({
    status: 403,
    description: 'Feature not available in current subscription',
  })
  async generateDocument(
    @Body() dto: AIAssistedDraftingDto,
    @Organization() organizationId: string,
    @User('id') userId: string,
  ): Promise<DocumentGenerationResult> {
    this.logger.log(
      `AI-assisted drafting request for document type: ${dto.documentType} by user: ${userId}`,
    );

    const startTime = Date.now();
    
    try {
      const result = await this.documentAutomationService.generateDocument(
        dto,
        organizationId,
        userId,
      );

      const duration = Date.now() - startTime;

      // Track successful AI document generation
      this.postHogService.trackEvent(userId, 'ai_document_generated', {
        document_type: dto.documentType,
        organization_id: organizationId,
        duration_ms: duration,
        success: true
      });

      this.logger.log(
        `Document generation completed successfully for user: ${userId}, type: ${dto.documentType}`,
      );
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;

      // Track failed AI document generation
      this.postHogService.trackError(userId, error, 'ai_document_generation', {
        document_type: dto.documentType,
        organization_id: organizationId,
        duration_ms: duration
      });

      this.logger.error(
        `Failed to generate document for user: ${userId}, type: ${dto.documentType}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Post('generate-related-documents')
  @UseCredits('template_generation') // AI related document generation consumes credits
  @ApiOperation({
    summary: 'Generate Related Documents',
    description:
      'Automatically generate ancillary documents (schedules, exhibits, addendums) based on a primary agreement',
  })
  @ApiResponse({
    status: 201,
    description: 'Related documents generated successfully',
    type: RelatedDocumentGenerationResult,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters',
  })
  @ApiResponse({
    status: 404,
    description: 'Primary document not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Feature not available in current subscription',
  })
  async generateRelatedDocuments(
    @Body() dto: GenerateRelatedDocumentsDto,
    @Organization() organizationId: string,
    @User('id') userId: string,
  ): Promise<RelatedDocumentGenerationResult> {
    this.logger.log(
      `Related documents generation request for primary document: ${dto.primaryDocumentId} by user: ${userId}`,
    );

    try {
      const result =
        await this.documentAutomationService.generateRelatedDocuments(
          dto,
          organizationId,
          userId,
        );

      this.logger.log(
        `Related documents generation completed successfully for user: ${userId}, primary document: ${dto.primaryDocumentId}`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to generate related documents for user: ${userId}, primary document: ${dto.primaryDocumentId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Post('clause-intelligence')
  @UseCredits('clause_intelligence') // AI clause suggestions consume credits
  @ApiOperation({
    summary: 'Clause Intelligence',
    description:
      'Get intelligent clause suggestions and auto-population recommendations for document drafting',
  })
  @ApiResponse({
    status: 201,
    description: 'Clause intelligence provided successfully',
    type: ClauseIntelligenceResult,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters',
  })
  @ApiResponse({
    status: 403,
    description: 'Feature not available in current subscription',
  })
  async getClauseIntelligence(
    @Body() dto: ClauseIntelligenceDto,
    @Organization() organizationId: string,
    @User('id') userId: string,
  ): Promise<ClauseIntelligenceResult> {
    this.logger.log(
      `Clause intelligence request for document type: ${dto.documentType} by user: ${userId}`,
    );

    try {
      const result = await this.documentAutomationService.getClauseIntelligence(
        dto,
        organizationId,
        userId,
      );

      this.logger.log(
        `Clause intelligence completed successfully for user: ${userId}, document type: ${dto.documentType}`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Failed to provide clause intelligence for user: ${userId}, document type: ${dto.documentType}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Post('build-clause-library')
  @UseCredits('clause_library_automation') // AI clause extraction consumes credits
  @ApiOperation({
    summary: 'Auto-Build Clause Library from Document Corpus',
    description:
      'Automatically extract and categorize clauses from organization documents to build clause library',
  })
  @ApiResponse({
    status: 201,
    description: 'Clause library building completed successfully',
    schema: {
      type: 'object',
      properties: {
        extractedClauses: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              content: { type: 'string' },
              category: { type: 'string' },
              confidence: { type: 'number' },
              sourceDocument: { type: 'string' },
              patternType: { type: 'string' },
            },
          },
        },
        totalDocumentsAnalyzed: { type: 'number' },
        totalClausesExtracted: { type: 'number' },
        categoriesFound: { type: 'array', items: { type: 'string' } },
        processingDurationMs: { type: 'number' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request parameters',
  })
  @ApiResponse({
    status: 403,
    description: 'Feature not available in current subscription',
  })
  async buildClauseLibrary(
    @Body() options: ClauseLibraryBuildOptionsDto,
    @Organization() organizationId: string,
    @User('id') userId: string,
  ) {
    this.logger.log(
      `Clause library auto-build request for organization: ${organizationId} by user: ${userId}`,
    );

    const startTime = Date.now();
    
    try {
      const result =
        await this.clauseLibraryAutomationService.buildClauseLibraryFromCorpus(
          organizationId,
          userId,
          options,
        );

      const duration = Date.now() - startTime;

      // Track successful clause library automation
      this.postHogService.trackEvent(userId, 'clause_library_automated', {
        organization_id: organizationId,
        documents_analyzed: result.totalDocumentsAnalyzed,
        clauses_extracted: result.totalClausesExtracted,
        categories_found: result.categoriesFound?.length || 0,
        duration_ms: duration,
        success: true
      });

      this.logger.log(
        `Clause library building completed successfully for organization: ${organizationId}, extracted ${result.totalClausesExtracted} clauses from ${result.totalDocumentsAnalyzed} documents`,
      );
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;

      // Track failed clause library automation
      this.postHogService.trackError(userId, error, 'clause_library_automation', {
        organization_id: organizationId,
        duration_ms: duration
      });

      this.logger.error(
        `Failed to build clause library for organization: ${organizationId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}

# Use Node.js 20 LTS
FROM node:20-alpine AS builder

# Set working directory
WORKDIR /usr/src/app

# Copy package files and configs first for better caching
COPY package*.json ./
COPY tsconfig*.json ./

# Install all dependencies including devDependencies for building
RUN npm ci

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production image
FROM node:20-alpine

# Set working directory
WORKDIR /usr/src/app

# Copy package files
COPY package*.json ./

# Install only production dependencies
RUN npm ci --only=production

# Copy built files from builder
COPY --from=builder /usr/src/app/dist ./dist

# Expose the port the app runs on
EXPOSE 3000

# Start the application
CMD ["node", "dist/main"]

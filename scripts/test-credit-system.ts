#!/usr/bin/env ts-node

/**
 * Test script to verify the credit system is working properly
 * Run with: npx ts-node scripts/test-credit-system.ts
 */

import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { CreditManagementService } from '../src/modules/subscription/services/credit-management.service';
import { SubscriptionService } from '../src/modules/subscription/services/subscription.service';
import { SubscriptionTier } from '../src/modules/subscription/enums/subscription-tier.enum';

async function testCreditSystem() {
  console.log('🚀 Starting Credit System Test...\n');

  const app = await NestFactory.createApplicationContext(AppModule);
  const creditService = app.get(CreditManagementService);
  const subscriptionService = app.get(SubscriptionService);

  try {
    // Test organization ID
    const testOrgId = 'test-org-' + Date.now();
    
    console.log('1. Creating test subscription...');
    const subscription = await subscriptionService.createSubscription(
      testOrgId,
      '<EMAIL>',
      SubscriptionTier.FREE
    );
    console.log(`✅ Created subscription with ${subscription.creditBalance} credits\n`);

    console.log('2. Testing credit balance check...');
    const balance = await creditService.getCreditBalance(testOrgId);
    console.log(`✅ Current balance: ${balance.currentBalance} credits\n`);

    console.log('3. Testing feature cost lookup...');
    const basicAnalysisCost = creditService.getFeatureCost('basic_analysis');
    const advancedAnalysisCost = creditService.getFeatureCost('advanced_analysis');
    console.log(`✅ Basic analysis costs: ${basicAnalysisCost} credits`);
    console.log(`✅ Advanced analysis costs: ${advancedAnalysisCost} credits\n`);

    console.log('4. Testing credit deduction...');
    const deductionResult = await creditService.deductCreditsForFeature(
      testOrgId,
      'basic_analysis',
      'test-transaction-1'
    );
    console.log(`✅ Deduction result:`, {
      success: deductionResult.success,
      remainingBalance: deductionResult.remainingBalance,
      creditsDeducted: deductionResult.creditsDeducted
    });

    console.log('\n5. Testing insufficient credits scenario...');
    // Try to deduct more credits than available
    for (let i = 0; i < 60; i++) {
      const result = await creditService.deductCreditsForFeature(
        testOrgId,
        'basic_analysis',
        `test-transaction-${i + 2}`
      );
      if (!result.success) {
        console.log(`✅ Correctly blocked transaction after ${i + 1} attempts`);
        console.log(`   Reason: ${result.error}\n`);
        break;
      }
    }

    console.log('6. Testing credit history...');
    const finalBalance = await creditService.getCreditBalance(testOrgId);
    console.log(`✅ Final balance: ${finalBalance.currentBalance} credits`);
    console.log(`✅ Total transactions: ${finalBalance.creditHistory.length}`);
    
    // Show last few transactions
    console.log('\n📊 Recent transactions:');
    finalBalance.creditHistory.slice(-3).forEach((transaction, index) => {
      console.log(`   ${index + 1}. ${transaction.type}: ${transaction.amount} credits (Balance: ${transaction.balance})`);
    });

    console.log('\n🎉 Credit System Test Completed Successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    await app.close();
  }
}

// Run the test
testCreditSystem().catch(console.error);

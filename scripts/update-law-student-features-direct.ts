#!/usr/bin/env node

/**
 * Direct Database Migration Script for Law Student Legal Research Features
 * 
 * This script directly updates the MongoDB database to add legal research features
 * to law student subscriptions, bypassing NestJS dependency injection issues.
 */

import { MongoClient } from 'mongodb';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

interface UpdateResult {
  totalFound: number;
  totalUpdated: number;
  errors: string[];
}

async function updateLawStudentFeatures(): Promise<UpdateResult> {
  const result: UpdateResult = {
    totalFound: 0,
    totalUpdated: 0,
    errors: []
  };

  // Get MongoDB connection string from environment
  const mongoUri = process.env.DATABASE_URI || process.env.MONGODB_URI || process.env.DATABASE_URL;
  
  if (!mongoUri) {
    throw new Error('MongoDB connection string not found in environment variables (DATABASE_URI, MONGODB_URI, or DATABASE_URL)');
  }

  console.log('🔗 Connecting to MongoDB...');
  const client = new MongoClient(mongoUri);

  try {
    await client.connect();
    console.log('✅ Connected to MongoDB');

    const db = client.db();
    const subscriptionsCollection = db.collection('subscriptions');

    // New features to add to law students
    const newFeatures = ['legal_research_synthesis', 'legal_research_followup'];

    console.log('🔍 Finding law student subscriptions...');

    // Find all active law student subscriptions
    const lawStudentSubscriptions = await subscriptionsCollection.find({
      tier: 'law_student',
      status: { $in: ['active', 'trialing'] }
    }).toArray();

    result.totalFound = lawStudentSubscriptions.length;
    console.log(`📊 Found ${result.totalFound} law student subscriptions`);

    if (result.totalFound === 0) {
      console.log('ℹ️  No law student subscriptions found to update');
      return result;
    }

    console.log('🔄 Updating subscriptions...');

    // Update each subscription
    for (const subscription of lawStudentSubscriptions) {
      try {
        const currentFeatures = subscription.features || [];
        const featuresToAdd = newFeatures.filter(feature => !currentFeatures.includes(feature));

        if (featuresToAdd.length === 0) {
          console.log(`⏭️  Subscription ${subscription._id} already has all features`);
          continue;
        }

        // Update the subscription
        const updateResult = await subscriptionsCollection.updateOne(
          { _id: subscription._id },
          {
            $addToSet: {
              features: { $each: featuresToAdd }
            },
            $set: {
              lastFeatureUpdate: new Date()
            }
          }
        );

        if (updateResult.modifiedCount > 0) {
          console.log(`✅ Updated subscription ${subscription._id}: added ${featuresToAdd.join(', ')}`);
          result.totalUpdated++;
        } else {
          console.log(`⚠️  Subscription ${subscription._id} was not modified`);
        }

      } catch (error) {
        const errorMsg = `Failed to update subscription ${subscription._id}: ${error.message}`;
        console.error(`❌ ${errorMsg}`);
        result.errors.push(errorMsg);
      }
    }

    console.log('\n📈 Migration Summary:');
    console.log(`   Total Found: ${result.totalFound}`);
    console.log(`   Total Updated: ${result.totalUpdated}`);
    console.log(`   Errors: ${result.errors.length}`);

    if (result.errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      result.errors.forEach(error => console.log(`   - ${error}`));
    }

    // Verify the updates
    console.log('\n🔍 Verifying updates...');
    const verificationResult = await subscriptionsCollection.find({
      tier: 'law_student',
      status: { $in: ['active', 'trialing'] },
      features: { $all: newFeatures }
    }).toArray();

    console.log(`✅ Verification: ${verificationResult.length} law student subscriptions now have all legal research features`);

  } catch (error) {
    console.error('💥 Migration failed:', error);
    throw error;
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }

  return result;
}

// Dry run function
async function dryRun(): Promise<void> {
  const mongoUri = process.env.DATABASE_URI || process.env.MONGODB_URI || process.env.DATABASE_URL;
  
  if (!mongoUri) {
    throw new Error('MongoDB connection string not found in environment variables (DATABASE_URI, MONGODB_URI, or DATABASE_URL)');
  }

  console.log('🧪 DRY RUN - No changes will be made');
  console.log('🔗 Connecting to MongoDB...');
  
  const client = new MongoClient(mongoUri);

  try {
    await client.connect();
    const db = client.db();
    const subscriptionsCollection = db.collection('subscriptions');

    const lawStudentSubscriptions = await subscriptionsCollection.find({
      tier: 'law_student',
      status: { $in: ['active', 'trialing'] }
    }).toArray();

    console.log(`📊 Found ${lawStudentSubscriptions.length} law student subscriptions`);

    const newFeatures = ['legal_research_synthesis', 'legal_research_followup'];
    let wouldUpdate = 0;

    for (const subscription of lawStudentSubscriptions) {
      const currentFeatures = subscription.features || [];
      const featuresToAdd = newFeatures.filter(feature => !currentFeatures.includes(feature));

      if (featuresToAdd.length > 0) {
        console.log(`📝 Would update ${subscription._id}: add ${featuresToAdd.join(', ')}`);
        wouldUpdate++;
      } else {
        console.log(`⏭️  ${subscription._id} already has all features`);
      }
    }

    console.log(`\n📈 Dry Run Summary: Would update ${wouldUpdate} of ${lawStudentSubscriptions.length} subscriptions`);

  } finally {
    await client.close();
  }
}

// Main execution
async function main() {
  const isDryRun = process.argv.includes('--dry-run');

  try {
    if (isDryRun) {
      await dryRun();
    } else {
      const result = await updateLawStudentFeatures();
      
      if (result.errors.length > 0) {
        process.exit(1);
      }
      
      console.log('🎉 Migration completed successfully!');
    }
  } catch (error) {
    console.error('💥 Script failed:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

export { updateLawStudentFeatures, dryRun }; 
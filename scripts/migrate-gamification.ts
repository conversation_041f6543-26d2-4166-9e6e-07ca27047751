import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { GamificationService } from '../src/modules/gamification/services/gamification.service';
import { AchievementService } from '../src/modules/gamification/services/achievement.service';
import { CharacterService } from '../src/modules/gamification/services/character.service';
import { PressureEventService } from '../src/modules/gamification/services/pressure-event.service';
import { Model } from 'mongoose';
import { getModelToken } from '@nestjs/mongoose';
import { USER_MODEL } from '../src/modules/auth/schemas/user.schema';
import { NEGOTIATION_SESSION_MODEL } from '../src/modules/documents/schemas/negotiation-simulator.schema';

async function migrateGamification() {
  console.log('🎮 Starting Gamification Migration...');
  
  const app = await NestFactory.createApplicationContext(AppModule);
  
  try {
    // Get services
    const gamificationService = app.get(GamificationService);
    const achievementService = app.get(AchievementService);
    const characterService = app.get(CharacterService);
    const pressureEventService = app.get(PressureEventService);
    
    // Get models
    const userModel = app.get(getModelToken(USER_MODEL));
    const sessionModel = app.get(getModelToken(NEGOTIATION_SESSION_MODEL));

    console.log('📊 Step 1: Seeding initial data...');
    
    // Seed characters
    console.log('👥 Seeding characters...');
    await characterService.seedCharacters();
    
    // Seed achievements
    console.log('🏆 Seeding achievements...');
    await achievementService.seedAchievements();
    
    // Seed pressure events
    console.log('⚡ Seeding pressure events...');
    await pressureEventService.seedPressureEvents();

    console.log('👤 Step 2: Creating gamification profiles for existing users...');
    
    // Get all existing users
    const users = await userModel.find({}).exec();
    console.log(`Found ${users.length} existing users`);
    
    for (const user of users) {
      try {
        // Create gamification profile for each user
        await gamificationService.getUserGamification(user._id.toString(), user.organizationId);
        console.log(`✅ Created gamification profile for user: ${user._id}`);
      } catch (error) {
        console.error(`❌ Error creating profile for user ${user._id}: ${error.message}`);
      }
    }

    console.log('🎯 Step 3: Migrating existing negotiation sessions...');
    
    // Get all existing negotiation sessions
    const sessions = await sessionModel.find({}).exec();
    console.log(`Found ${sessions.length} existing sessions`);
    
    let migratedSessions = 0;
    for (const session of sessions) {
      try {
        // Add gamification fields if they don't exist
        let needsUpdate = false;
        
        if (!session.characterId) {
          session.characterId = 'default_character';
          needsUpdate = true;
        }
        
        if (!session.gamificationData) {
          session.gamificationData = {
            xpEarned: 0,
            achievementsUnlocked: [],
            pressureEventsTriggered: [],
            relationshipChanges: {},
            levelUps: 0,
          };
          needsUpdate = true;
        }
        
        if (!session.gameState) {
          session.gameState = {
            userStress: 0.3,
            aiMood: 'neutral',
            activePressureEvents: [],
            currentScore: session.metrics?.overallScore || 5.0,
            dealMomentum: 'neutral',
          };
          needsUpdate = true;
        }
        
        if (needsUpdate) {
          await session.save();
          migratedSessions++;
        }
      } catch (error) {
        console.error(`❌ Error migrating session ${session._id}: ${error.message}`);
      }
    }
    
    console.log(`✅ Migrated ${migratedSessions} sessions`);

    console.log('📈 Step 4: Calculating initial XP and achievements...');
    
    // Award retroactive XP for completed sessions
    for (const user of users) {
      try {
        const userSessions = await sessionModel.find({ 
          userId: user._id.toString(),
          status: 'completed'
        }).exec();
        
        if (userSessions.length > 0) {
          // Award XP for completed sessions (retroactive)
          const retroactiveXP = userSessions.length * 50; // 50 XP per completed session
          
          await gamificationService.awardExperience(
            user._id.toString(),
            retroactiveXP,
            'migration_retroactive',
            { completedSessions: userSessions.length }
          );
          
          console.log(`✅ Awarded ${retroactiveXP} XP to user ${user._id} for ${userSessions.length} completed sessions`);
        }
      } catch (error) {
        console.error(`❌ Error calculating XP for user ${user._id}: ${error.message}`);
      }
    }

    console.log('🔍 Step 5: Creating database indexes...');
    
    // Create indexes for performance
    try {
      await userModel.collection.createIndex({ 'gamificationProfile.level.current': -1 });
      await sessionModel.collection.createIndex({ characterId: 1 });
      await sessionModel.collection.createIndex({ 'gamificationData.xpEarned': -1 });
      console.log('✅ Created database indexes');
    } catch (error) {
      console.error(`❌ Error creating indexes: ${error.message}`);
    }

    console.log('🎉 Gamification migration completed successfully!');
    console.log('\n📊 Migration Summary:');
    console.log(`- Users migrated: ${users.length}`);
    console.log(`- Sessions migrated: ${migratedSessions}`);
    console.log(`- Characters seeded: Available via API`);
    console.log(`- Achievements seeded: Available via API`);
    console.log(`- Pressure events seeded: Available via API`);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await app.close();
  }
}

// Run migration if called directly
if (require.main === module) {
  migrateGamification()
    .then(() => {
      console.log('✅ Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Migration failed:', error);
      process.exit(1);
    });
}

export { migrateGamification };

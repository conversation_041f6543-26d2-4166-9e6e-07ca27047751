/**
 * Simple demonstration of context management features
 * This script shows how the enhanced context system combines information 
 * from documents and related documents based on relevance
 */

import { ContextSource } from '../src/common/interfaces/context-management.interface';
import { ChatRole } from '../src/common/interfaces/chat.interface';
import { RateLimiterConfig, RateLimiter } from '../src/common/utils/rate-limiter.util';

// Sample document content
const mainDocumentContent = `SERVICE AGREEMENT CONTRACT

This SERVICE AGREEMENT CONTRACT (the "Agreement") is made and entered into as of March 15, 2025 (the "Effective Date") by and between ACME CORPORATION and LEGAL DOCUMENT SERVICES INC.

1. SERVICES
   1.1 Provider shall provide to <PERSON>lient the services (the "Services") set forth in Exhibit A attached hereto and incorporated herein by reference.
   1.2 Provider shall perform the Services in a professional manner consistent with industry standards.
   
2. TERM AND TERMINATION
   2.1 This Agreement shall commence on the Effective Date and continue for a period of twelve (12) months, unless earlier terminated pursuant to this Section 2 (the "Initial Term").`;

const relatedDocumentContent = `AMENDMENT TO SERVICE AGREEMENT CONTRACT

This AMENDMENT TO SERVICE AGREEMENT CONTRACT (the "Amendment") is made and entered into as of September 15, 2025 (the "Amendment Effective Date") by and between ACME CORPORATION and LEGAL DOCUMENT SERVICES INC.

1. AMENDMENTS TO ORIGINAL AGREEMENT
   1.1 Section 2.1 of the Original Agreement is hereby amended and restated in its entirety as follows:
       "This Agreement shall commence on the Effective Date and continue for a period of twenty-four (24) months, unless earlier terminated pursuant to this Section 2 (the "Initial Term")."`;

// Mock context window building function
const buildContextWindow = (query: string, includeRelatedDocument: boolean) => {
  console.log(`Building context window for query: "${query}"`);
  console.log(`Including related documents: ${includeRelatedDocument ? "Yes" : "No"}`);
  
  // Extract relevant parts from the main document based on the query
  const mainDocumentExcerpts = extractRelevantContent(mainDocumentContent, query);
  
  // Extract relevant parts from related documents if enabled
  const relatedDocumentExcerpts = includeRelatedDocument 
    ? extractRelevantContent(relatedDocumentContent, query)
    : [];
  
  // Create context window
  return {
    documentExcerpts: mainDocumentExcerpts,
    relatedDocumentExcerpts: relatedDocumentExcerpts,
    chatHistory: "User: What is this document about?\nAssistant: This is a service agreement contract between ACME Corporation and Legal Document Services Inc.",
    usedSources: includeRelatedDocument 
      ? [ContextSource.DOCUMENT, ContextSource.RELATED_DOCUMENT, ContextSource.CHAT_HISTORY]
      : [ContextSource.DOCUMENT, ContextSource.CHAT_HISTORY]
  };
};

// Simple relevance-based content extraction
const extractRelevantContent = (content: string, query: string): string[] => {
  const lines = content.split('\n');
  const excerpts: string[] = [];
  
  // Simple approach: split by sections and return sections containing query terms
  const queryTerms = query.toLowerCase().split(' ');
  
  let currentSection = '';
  for (const line of lines) {
    // If line indicates a new section
    if (line.match(/^\d+\.\s/) || line.match(/^\s+\d+\.\d+\s/)) {
      if (currentSection.length > 0) {
        // Check if current section matches query terms
        if (queryTerms.some(term => 
          currentSection.toLowerCase().includes(term) && term.length > 3)) {
          excerpts.push(currentSection.trim());
        }
        currentSection = '';
      }
    }
    
    currentSection += line + '\n';
  }
  
  // Don't forget the last section
  if (currentSection.length > 0) {
    if (queryTerms.some(term => 
      currentSection.toLowerCase().includes(term) && term.length > 3)) {
      excerpts.push(currentSection.trim());
    }
  }
  
  // If no excerpts match the query terms, just return some default sections
  if (excerpts.length === 0) {
    // Extract first couple of sections as default
    let section = '';
    let sectionCount = 0;
    
    for (const line of lines) {
      if (line.match(/^\d+\.\s/) && sectionCount > 0) {
        excerpts.push(section.trim());
        section = '';
        sectionCount++;
        if (sectionCount >= 2) break;
      }
      
      section += line + '\n';
      if (line.match(/^\d+\.\s/)) {
        sectionCount++;
      }
    }
    
    if (section.length > 0 && excerpts.length < 2) {
      excerpts.push(section.trim());
    }
  }
  
  return excerpts;
};

// Simulate generating a response based on the context
const generateResponse = async (query: string, context: any, rateLimiter: RateLimiter): Promise<string> => {
  // Acquire a token from the rate limiter
  await rateLimiter.acquire('generateResponse');
  
  // Combine context excerpts into a prompt
  const prompt = `
CONTEXT:
${context.documentExcerpts.join('\n\n')}

${context.relatedDocumentExcerpts.length > 0 ? `RELATED DOCUMENTS:\n${context.relatedDocumentExcerpts.join('\n\n')}` : ''}

CONVERSATION HISTORY:
${context.chatHistory}

USER QUERY:
${query}
`;

  console.log("Generated prompt:", prompt.substring(0, 100) + "...");
  
  // Simulate response generation based on the query
  if (query.toLowerCase().includes('term') && context.relatedDocumentExcerpts.length > 0) {
    return "Based on the document, the initial term of the agreement is twelve (12) months as stated in Section 2.1. However, according to the Amendment, this has been changed to twenty-four (24) months.";
  } else if (query.toLowerCase().includes('term')) {
    return "Based on the document, the initial term of the agreement is twelve (12) months from the Effective Date, unless terminated earlier as outlined in Section 2.";
  }
  
  return "Here is a response based on the provided context.";
};

// Simulate rate limiter
const createRateLimiter = (): RateLimiter => {
  const config: RateLimiterConfig = {
    maxRequests: 60,
    windowMs: 60000, // 1 minute
    throwOnLimit: false
  };
  
  return new RateLimiter(config, 'demo-limiter');
};

/**
 * Run the demonstration
 */
async function runDemo() {
  console.log("-------------- ENHANCED CONTEXT MANAGEMENT DEMO --------------");
  
  // Initialize rate limiter
  const rateLimiter = createRateLimiter();
  
  // Test 1: Query with standard context
  console.log("\n*** TEST 1: Standard Context ***");
  const query1 = "What is the term of the agreement?";
  const context1 = buildContextWindow(query1, false);
  
  console.log(`Main document excerpts: ${context1.documentExcerpts.length}`);
  console.log("First excerpt:", context1.documentExcerpts[0].substring(0, 100) + "...");
  
  const response1 = await generateResponse(query1, context1, rateLimiter);
  console.log("Response:", response1);
  
  // Test 2: Query with enhanced context (related documents)
  console.log("\n*** TEST 2: Enhanced Context with Related Documents ***");
  const query2 = "How does the amendment change the term of the agreement?";
  const context2 = buildContextWindow(query2, true);
  
  console.log(`Main document excerpts: ${context2.documentExcerpts.length}`);
  console.log(`Related document excerpts: ${context2.relatedDocumentExcerpts.length}`);
  console.log("Main document excerpt:", context2.documentExcerpts[0].substring(0, 100) + "...");
  console.log("Related document excerpt:", context2.relatedDocumentExcerpts[0].substring(0, 100) + "...");
  
  const response2 = await generateResponse(query2, context2, rateLimiter);
  console.log("Response:", response2);
  
  // Check rate limiter utilization
  console.log("\n*** RATE LIMITER STATUS ***");
  const utilization = rateLimiter.getUtilization();
  console.log(`Utilization: ${(utilization * 100).toFixed(2)}%`);
  console.log(`Requests used: ${Math.round(utilization * 60)} of 60`);
  console.log(`Remaining requests: ${60 - Math.round(utilization * 60)}`);
  
  console.log("\n-------------- DEMO COMPLETE --------------");
}

// Run the demonstration
runDemo().catch(err => console.error("Error running demo:", err));

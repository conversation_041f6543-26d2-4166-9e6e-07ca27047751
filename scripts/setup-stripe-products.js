#!/usr/bin/env node

/**
 * Stripe Product and Price Setup Script
 * 
 * This script creates all necessary Stripe products and prices for:
 * 1. Subscription plans (PRO/ADMIN tiers)
 * 2. Credit packages (one-time purchases)
 * 
 * Usage:
 * node scripts/setup-stripe-products.js
 * 
 * Requirements:
 * - STRIPE_SECRET_KEY environment variable set
 * - stripe npm package installed
 */

require('dotenv').config();
const Stripe = require('stripe');

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

if (!process.env.STRIPE_SECRET_KEY) {
  console.error('❌ STRIPE_SECRET_KEY environment variable is required');
  process.exit(1);
}

console.log('🚀 Setting up Stripe products and prices...\n');

// Subscription Plans Configuration
const subscriptionPlans = [
  {
    name: 'DocGic Pro Plan',
    description: 'Professional plan for individual lawyers with advanced features',
    priceMonthly: 2999, // $29.99
    priceYearly: 29999, // $299.99 (save ~17%)
    envKey: 'STRIPE_PLAN_PRO',
    features: [
      'Unlimited document analysis',
      'Advanced AI insights',
      'Priority support',
      '500 monthly credits',
      'Contract playbooks',
      'Legal research tools'
    ]
  },
  {
    name: 'DocGic Admin Plan',
    description: 'Enterprise plan for law firms with team collaboration features',
    priceMonthly: 9999, // $99.99
    priceYearly: 99999, // $999.99 (save ~17%)
    envKey: 'STRIPE_PLAN_ADMIN',
    features: [
      'Everything in Pro',
      'Team collaboration',
      'Advanced workflow management',
      'Unlimited users',
      '2000 monthly credits',
      'Custom integrations',
      'Dedicated support'
    ]
  }
];

// Credit Packages Configuration
const creditPackages = [
  {
    name: 'Student Credit Package',
    description: 'Perfect for law students - 50 credits',
    price: 499, // $4.99
    credits: 50,
    bonus: 0,
    envKey: 'STRIPE_CREDIT_STUDENT',
    targetTier: 'law_student'
  },
  {
    name: 'Lawyer Starter Pack',
    description: 'Additional credits for practicing attorneys - 200 credits + 20 bonus',
    price: 1999, // $19.99
    credits: 200,
    bonus: 20,
    envKey: 'STRIPE_CREDIT_LAWYER_SMALL',
    targetTier: 'lawyer'
  },
  {
    name: 'Lawyer Professional Pack',
    description: 'For busy legal practices - 500 credits + 75 bonus',
    price: 4499, // $44.99
    credits: 500,
    bonus: 75,
    envKey: 'STRIPE_CREDIT_LAWYER_LARGE',
    targetTier: 'lawyer'
  },
  {
    name: 'Law Firm Standard Pack',
    description: 'For growing legal teams - 1000 credits + 200 bonus',
    price: 7999, // $79.99
    credits: 1000,
    bonus: 200,
    envKey: 'STRIPE_CREDIT_FIRM_STANDARD',
    targetTier: 'law_firm'
  },
  {
    name: 'Law Firm Enterprise Pack',
    description: 'For large legal organizations - 5000 credits + 1500 bonus',
    price: 34999, // $349.99
    credits: 5000,
    bonus: 1500,
    envKey: 'STRIPE_CREDIT_FIRM_ENTERPRISE',
    targetTier: 'law_firm'
  }
];

async function createSubscriptionPlans() {
  console.log('📋 Creating subscription plans...\n');
  
  const results = [];
  
  for (const plan of subscriptionPlans) {
    try {
      console.log(`Creating product: ${plan.name}`);
      
      // Create product
      const product = await stripe.products.create({
        name: plan.name,
        description: plan.description,
        metadata: {
          type: 'subscription',
          features: plan.features.join(', ')
        }
      });
      
      console.log(`✅ Product created: ${product.id}`);
      
      // Create monthly price
      const monthlyPrice = await stripe.prices.create({
        product: product.id,
        unit_amount: plan.priceMonthly,
        currency: 'usd',
        recurring: {
          interval: 'month'
        },
        nickname: `${plan.name} - Monthly`
      });
      
      console.log(`✅ Monthly price created: ${monthlyPrice.id}`);
      
      // Create yearly price
      const yearlyPrice = await stripe.prices.create({
        product: product.id,
        unit_amount: plan.priceYearly,
        currency: 'usd',
        recurring: {
          interval: 'year'
        },
        nickname: `${plan.name} - Yearly`
      });
      
      console.log(`✅ Yearly price created: ${yearlyPrice.id}`);
      
      results.push({
        envKey: plan.envKey,
        productId: product.id,
        monthlyPriceId: monthlyPrice.id,
        yearlyPriceId: yearlyPrice.id,
        name: plan.name
      });
      
      console.log('');
      
    } catch (error) {
      console.error(`❌ Error creating ${plan.name}:`, error.message);
    }
  }
  
  return results;
}

async function createCreditPackages() {
  console.log('💳 Creating credit packages...\n');
  
  const results = [];
  
  for (const pkg of creditPackages) {
    try {
      console.log(`Creating product: ${pkg.name}`);
      
      // Create product
      const product = await stripe.products.create({
        name: pkg.name,
        description: pkg.description,
        metadata: {
          type: 'credit_package',
          credits: pkg.credits.toString(),
          bonus: pkg.bonus.toString(),
          totalCredits: (pkg.credits + pkg.bonus).toString(),
          targetTier: pkg.targetTier
        }
      });
      
      console.log(`✅ Product created: ${product.id}`);
      
      // Create one-time price
      const price = await stripe.prices.create({
        product: product.id,
        unit_amount: pkg.price,
        currency: 'usd',
        nickname: pkg.name
      });
      
      console.log(`✅ Price created: ${price.id}`);
      
      results.push({
        envKey: pkg.envKey,
        productId: product.id,
        priceId: price.id,
        name: pkg.name,
        credits: pkg.credits,
        bonus: pkg.bonus,
        totalCredits: pkg.credits + pkg.bonus,
        price: pkg.price / 100
      });
      
      console.log('');
      
    } catch (error) {
      console.error(`❌ Error creating ${pkg.name}:`, error.message);
    }
  }
  
  return results;
}

function generateEnvOutput(subscriptionResults, creditResults) {
  console.log('📝 Environment Variables to Add:\n');
  console.log('# Add these to your .env file:\n');
  
  // Subscription plans
  console.log('# Subscription Plan Price IDs');
  subscriptionResults.forEach(result => {
    console.log(`${result.envKey}=${result.monthlyPriceId} # Monthly`);
    console.log(`${result.envKey}_YEARLY=${result.yearlyPriceId} # Yearly`);
  });
  
  console.log('\n# Credit Package Price IDs');
  creditResults.forEach(result => {
    console.log(`${result.envKey}=${result.priceId}`);
  });
  
  console.log('\n');
}

function generateSummary(subscriptionResults, creditResults) {
  console.log('📊 Setup Summary:\n');
  
  console.log('Subscription Plans Created:');
  subscriptionResults.forEach(result => {
    console.log(`  ✅ ${result.name}`);
    console.log(`     Product ID: ${result.productId}`);
    console.log(`     Monthly Price ID: ${result.monthlyPriceId}`);
    console.log(`     Yearly Price ID: ${result.yearlyPriceId}`);
    console.log('');
  });
  
  console.log('Credit Packages Created:');
  creditResults.forEach(result => {
    console.log(`  ✅ ${result.name}`);
    console.log(`     Product ID: ${result.productId}`);
    console.log(`     Price ID: ${result.priceId}`);
    console.log(`     Credits: ${result.credits} + ${result.bonus} bonus = ${result.totalCredits} total`);
    console.log(`     Price: $${result.price}`);
    console.log('');
  });
}

async function main() {
  try {
    const subscriptionResults = await createSubscriptionPlans();
    const creditResults = await createCreditPackages();
    
    generateEnvOutput(subscriptionResults, creditResults);
    generateSummary(subscriptionResults, creditResults);
    
    console.log('🎉 Stripe setup completed successfully!');
    console.log('\n💡 Next steps:');
    console.log('1. Copy the environment variables above to your .env file');
    console.log('2. Restart your application');
    console.log('3. Test the credit purchase functionality');
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
    process.exit(1);
  }
}

// Run the script
main();

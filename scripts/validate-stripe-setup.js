#!/usr/bin/env node

/**
 * Stripe Setup Validation Script
 * 
 * This script validates that all required Stripe price IDs are configured
 * and accessible via the Stripe API.
 * 
 * Usage:
 * node scripts/validate-stripe-setup.js
 * 
 * Requirements:
 * - All Stripe environment variables set
 * - stripe npm package installed
 */

require('dotenv').config();
const Stripe = require('stripe');

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

if (!process.env.STRIPE_SECRET_KEY) {
  console.error('❌ STRIPE_SECRET_KEY environment variable is required');
  process.exit(1);
}

console.log('🔍 Validating Stripe setup...\n');

// Required environment variables
const requiredEnvVars = [
  'STRIPE_PLAN_PRO',
  'STRIPE_PLAN_ADMIN',
  'STRIPE_CREDIT_STUDENT',
  'STRIPE_CREDIT_LAWYER_SMALL',
  'STRIPE_CREDIT_LAWYER_LARGE',
  'STRIPE_CREDIT_FIRM_STANDARD',
  'STRIPE_CREDIT_FIRM_ENTERPRISE'
];

async function validateEnvironmentVariables() {
  console.log('📋 Checking environment variables...\n');
  
  const missing = [];
  const present = [];
  
  for (const envVar of requiredEnvVars) {
    if (process.env[envVar]) {
      present.push(envVar);
      console.log(`✅ ${envVar}: ${process.env[envVar]}`);
    } else {
      missing.push(envVar);
      console.log(`❌ ${envVar}: Not set`);
    }
  }
  
  console.log('');
  
  if (missing.length > 0) {
    console.log(`❌ Missing ${missing.length} required environment variables:`);
    missing.forEach(envVar => console.log(`   - ${envVar}`));
    console.log('\n💡 Run "npm run setup:stripe" to create missing Stripe products\n');
    return false;
  }
  
  console.log(`✅ All ${present.length} environment variables are set\n`);
  return true;
}

async function validateStripePrices() {
  console.log('💳 Validating Stripe prices...\n');
  
  const results = [];
  
  for (const envVar of requiredEnvVars) {
    const priceId = process.env[envVar];
    if (!priceId) continue;
    
    try {
      console.log(`Checking ${envVar}: ${priceId}`);
      
      const price = await stripe.prices.retrieve(priceId);
      const product = await stripe.products.retrieve(price.product);
      
      console.log(`✅ Valid - ${product.name} ($${(price.unit_amount / 100).toFixed(2)})`);
      
      results.push({
        envVar,
        priceId,
        productName: product.name,
        amount: price.unit_amount / 100,
        currency: price.currency,
        type: price.recurring ? 'subscription' : 'one-time',
        interval: price.recurring?.interval || 'N/A',
        status: 'valid'
      });
      
    } catch (error) {
      console.log(`❌ Invalid - ${error.message}`);
      
      results.push({
        envVar,
        priceId,
        error: error.message,
        status: 'invalid'
      });
    }
    
    console.log('');
  }
  
  return results;
}

function generateReport(results) {
  console.log('📊 Validation Report:\n');
  
  const valid = results.filter(r => r.status === 'valid');
  const invalid = results.filter(r => r.status === 'invalid');
  
  if (valid.length > 0) {
    console.log('✅ Valid Prices:');
    valid.forEach(result => {
      console.log(`   ${result.envVar}`);
      console.log(`   └─ ${result.productName} - $${result.amount} (${result.type})`);
      if (result.interval !== 'N/A') {
        console.log(`      Billing: ${result.interval}ly`);
      }
      console.log('');
    });
  }
  
  if (invalid.length > 0) {
    console.log('❌ Invalid Prices:');
    invalid.forEach(result => {
      console.log(`   ${result.envVar}: ${result.priceId}`);
      console.log(`   └─ Error: ${result.error}`);
      console.log('');
    });
  }
  
  console.log(`Summary: ${valid.length} valid, ${invalid.length} invalid\n`);
  
  return invalid.length === 0;
}

async function testWebhookEndpoint() {
  console.log('🔗 Testing webhook configuration...\n');
  
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
  
  if (!webhookSecret) {
    console.log('⚠️  STRIPE_WEBHOOK_SECRET not set');
    console.log('   This is required for processing payments');
    console.log('   Set up a webhook endpoint in your Stripe dashboard\n');
    return false;
  }
  
  console.log(`✅ Webhook secret configured: ${webhookSecret.substring(0, 10)}...\n`);
  return true;
}

function generateNextSteps(allValid, webhookValid) {
  console.log('🎯 Next Steps:\n');
  
  if (!allValid) {
    console.log('1. ❌ Fix invalid Stripe price IDs');
    console.log('   - Run "npm run setup:stripe" to create missing products');
    console.log('   - Update your .env file with correct price IDs');
    console.log('   - Re-run this validation script');
  } else {
    console.log('1. ✅ All Stripe prices are valid');
  }
  
  if (!webhookValid) {
    console.log('2. ❌ Set up webhook endpoint');
    console.log('   - Go to Stripe Dashboard > Webhooks');
    console.log('   - Add endpoint: https://yourdomain.com/api/subscriptions/webhook');
    console.log('   - Select events: checkout.session.completed, customer.subscription.*');
    console.log('   - Copy webhook secret to STRIPE_WEBHOOK_SECRET');
  } else {
    console.log('2. ✅ Webhook configuration looks good');
  }
  
  if (allValid && webhookValid) {
    console.log('3. ✅ Test the credit purchase flow');
    console.log('   - Start your application');
    console.log('   - Make a POST request to /api/credits/purchase');
    console.log('   - Complete the Stripe checkout');
    console.log('   - Verify credits are added to your account');
  } else {
    console.log('3. ⏳ Complete steps 1-2 before testing');
  }
  
  console.log('');
}

async function main() {
  try {
    // Step 1: Check environment variables
    const envValid = await validateEnvironmentVariables();
    
    if (!envValid) {
      process.exit(1);
    }
    
    // Step 2: Validate Stripe prices
    const results = await validateStripePrices();
    const allValid = generateReport(results);
    
    // Step 3: Check webhook configuration
    const webhookValid = await testWebhookEndpoint();
    
    // Step 4: Generate next steps
    generateNextSteps(allValid, webhookValid);
    
    if (allValid && webhookValid) {
      console.log('🎉 Stripe setup validation completed successfully!');
      console.log('Your application is ready to process credit purchases.\n');
      process.exit(0);
    } else {
      console.log('⚠️  Stripe setup validation found issues.');
      console.log('Please address the issues above before proceeding.\n');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Validation failed:', error.message);
    process.exit(1);
  }
}

// Run the script
main();

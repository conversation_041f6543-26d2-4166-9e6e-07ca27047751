#!/bin/bash

# Gamification Endpoint Testing Script
# Usage: ./scripts/test-endpoints.sh [BASE_URL] [AUTH_TOKEN]

BASE_URL=${1:-"http://localhost:3000"}
AUTH_TOKEN=${2:-"mock-jwt-token"}

echo "🎮 Testing Gamification Endpoints"
echo "=================================="
echo "Base URL: $BASE_URL"
echo "Auth Token: ${AUTH_TOKEN:0:20}..."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to test an endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=${4:-200}
    
    echo -n "Testing $method $endpoint ... "
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $AUTH_TOKEN" \
                   -H "Content-Type: application/json" \
                   "$BASE_URL$endpoint")
    elif [ "$method" = "POST" ]; then
        response=$(curl -s -w "%{http_code}" -X POST \
                   -H "Authorization: Bearer $AUTH_TOKEN" \
                   -H "Content-Type: application/json" \
                   -d "$data" \
                   "$BASE_URL$endpoint")
    fi
    
    # Extract status code (last 3 characters)
    status_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} ($status_code)"
    else
        echo -e "${RED}❌ FAIL${NC} ($status_code, expected $expected_status)"
        if [ ${#response_body} -lt 200 ]; then
            echo "   Response: $response_body"
        fi
    fi
}

echo -e "${BLUE}📊 Testing Gamification Profile Endpoints${NC}"
echo "----------------------------------------"

# Gamification Profile Endpoints
test_endpoint "GET" "/api/gamification/profile"
test_endpoint "GET" "/api/gamification/profile/test-user-123"
test_endpoint "POST" "/api/gamification/experience" '{"amount":100,"source":"test_award","metadata":{"test":true}}'

echo ""
echo -e "${BLUE}👥 Testing Character Endpoints${NC}"
echo "-------------------------------"

# Character Endpoints
test_endpoint "GET" "/api/gamification/characters"
test_endpoint "GET" "/api/gamification/characters?difficulty=2"
test_endpoint "GET" "/api/gamification/characters?unlocked=true"
test_endpoint "GET" "/api/gamification/characters?specialty=friendly"
test_endpoint "GET" "/api/gamification/characters/unlocked"
test_endpoint "GET" "/api/gamification/characters/default"
test_endpoint "GET" "/api/gamification/characters/difficulty/1"
test_endpoint "GET" "/api/gamification/characters/default_character"
test_endpoint "POST" "/api/gamification/characters/sarah_chen/unlock" '{}'
test_endpoint "POST" "/api/gamification/characters/seed" '{}'

echo ""
echo -e "${BLUE}🏆 Testing Achievement Endpoints${NC}"
echo "--------------------------------"

# Achievement Endpoints
test_endpoint "GET" "/api/gamification/achievements"
test_endpoint "GET" "/api/gamification/achievements?category=efficiency"
test_endpoint "GET" "/api/gamification/achievements?rarity=rare"
test_endpoint "GET" "/api/gamification/achievements?unlocked=false"
test_endpoint "GET" "/api/gamification/achievements/categories"
test_endpoint "GET" "/api/gamification/achievements/rare"
test_endpoint "GET" "/api/gamification/achievements/speed_demon"
test_endpoint "GET" "/api/gamification/achievements/leaderboard/speed_demon"
test_endpoint "POST" "/api/gamification/achievements/seed" '{}'

echo ""
echo -e "${BLUE}🏅 Testing Leaderboard Endpoints${NC}"
echo "--------------------------------"

# Leaderboard Endpoints
test_endpoint "GET" "/api/gamification/leaderboards"
test_endpoint "GET" "/api/gamification/leaderboards?type=weekly&scope=global"
test_endpoint "GET" "/api/gamification/leaderboards?type=monthly&scope=organization"
test_endpoint "GET" "/api/gamification/leaderboards?type=all_time&limit=100"
test_endpoint "GET" "/api/gamification/leaderboards/around-user?range=5"
test_endpoint "GET" "/api/gamification/leaderboards/user-rank"
test_endpoint "GET" "/api/gamification/leaderboards/global"
test_endpoint "GET" "/api/gamification/leaderboards/organization"
test_endpoint "GET" "/api/gamification/leaderboards/summary"

echo ""
echo -e "${BLUE}🤝 Testing Enhanced Negotiation Endpoints${NC}"
echo "--------------------------------------------"

# Enhanced Negotiation Endpoints
test_endpoint "POST" "/api/negotiation-simulator/sessions" '{"scenarioId":"test-scenario","characterId":"default_character","aiPersonality":{"aggressiveness":0.5,"flexibility":0.7,"riskTolerance":0.6,"communicationStyle":"DIPLOMATIC"}}' 201
test_endpoint "GET" "/api/negotiation-simulator/sessions"

echo ""
echo -e "${YELLOW}📋 Testing Summary${NC}"
echo "=================="
echo "All endpoint tests completed!"
echo ""
echo "Note: Some endpoints may fail if:"
echo "- Server is not running on $BASE_URL"
echo "- Authentication token is invalid"
echo "- Database is not seeded with initial data"
echo "- Required test scenarios don't exist"
echo ""
echo "To run the full test suite:"
echo "1. Start your server: npm run start:dev"
echo "2. Run migration: npm run migrate:gamification"
echo "3. Run this script: ./scripts/test-endpoints.sh"
echo ""
echo "🎮 Gamification endpoint testing complete!"

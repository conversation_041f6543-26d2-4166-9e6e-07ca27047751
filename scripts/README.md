# Stripe Setup Scripts

This directory contains scripts to automatically set up Stripe products and prices for the DocGic application.

## Setup Stripe Products Script

### Overview
The `setup-stripe-products.js` script automatically creates all necessary Stripe products and prices for:

1. **Subscription Plans**:
   - DocGic Pro Plan ($29.99/month, $299.99/year)
   - DocGic Admin Plan ($99.99/month, $999.99/year)

2. **Credit Packages**:
   - Student Credit Package (50 credits, $4.99)
   - Lawyer Starter Pack (200 + 20 bonus credits, $19.99)
   - Lawyer Professional Pack (500 + 75 bonus credits, $44.99)
   - Law Firm Standard Pack (1000 + 200 bonus credits, $79.99)
   - Law Firm Enterprise Pack (5000 + 1500 bonus credits, $349.99)

### Prerequisites

1. **Stripe Account**: You need a Stripe account (test mode is fine for development)
2. **Stripe Secret Key**: Get your secret key from the Stripe dashboard
3. **Environment Variable**: Set `STRIPE_SECRET_KEY` in your `.env` file

### Usage

1. **Set up your environment**:
   ```bash
   # Add to your .env file
   STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
   ```

2. **Run the script**:
   ```bash
   # Using npm script (recommended)
   npm run setup:stripe
   
   # Or directly
   node scripts/setup-stripe-products.js
   ```

3. **Copy the output**: The script will generate environment variables that you need to add to your `.env` file.

### Example Output

```bash
🚀 Setting up Stripe products and prices...

📋 Creating subscription plans...

Creating product: DocGic Pro Plan
✅ Product created: prod_ABC123
✅ Monthly price created: price_DEF456
✅ Yearly price created: price_GHI789

Creating product: DocGic Admin Plan
✅ Product created: prod_JKL012
✅ Monthly price created: price_MNO345
✅ Yearly price created: price_PQR678

💳 Creating credit packages...

Creating product: Student Credit Package
✅ Product created: prod_STU901
✅ Price created: price_VWX234

📝 Environment Variables to Add:

# Add these to your .env file:

# Subscription Plan Price IDs
STRIPE_PLAN_PRO=price_DEF456 # Monthly
STRIPE_PLAN_PRO_YEARLY=price_GHI789 # Yearly
STRIPE_PLAN_ADMIN=price_MNO345 # Monthly
STRIPE_PLAN_ADMIN_YEARLY=price_PQR678 # Yearly

# Credit Package Price IDs
STRIPE_CREDIT_STUDENT=price_VWX234
STRIPE_CREDIT_LAWYER_SMALL=price_YZA567
STRIPE_CREDIT_LAWYER_LARGE=price_BCD890
STRIPE_CREDIT_FIRM_STANDARD=price_EFG123
STRIPE_CREDIT_FIRM_ENTERPRISE=price_HIJ456

🎉 Stripe setup completed successfully!

💡 Next steps:
1. Copy the environment variables above to your .env file
2. Restart your application
3. Test the credit purchase functionality
```

### What the Script Creates

#### Subscription Plans
- **Products** with detailed descriptions and metadata
- **Monthly prices** for recurring billing
- **Yearly prices** with discount (save ~17%)

#### Credit Packages
- **Products** with credit information in metadata
- **One-time prices** for credit purchases
- **Bonus credit calculations** included

### Error Handling

The script includes comprehensive error handling:
- Validates Stripe secret key
- Handles individual product/price creation failures
- Continues processing even if some items fail
- Provides clear error messages

### Testing

After running the script:

1. **Check Stripe Dashboard**: Verify products and prices were created
2. **Test Subscription Flow**: Try creating a subscription checkout
3. **Test Credit Purchase**: Try purchasing credits
4. **Verify Webhooks**: Ensure webhook events are processed correctly

### Troubleshooting

**Common Issues**:

1. **Missing Stripe Key**: Ensure `STRIPE_SECRET_KEY` is set in your `.env` file
2. **Network Issues**: Check your internet connection
3. **Stripe API Errors**: Check Stripe dashboard for any account issues
4. **Duplicate Products**: The script will fail if products with the same name already exist

**Solutions**:
- Delete existing products in Stripe dashboard if you need to re-run
- Use test mode for development
- Check Stripe logs for detailed error information

### Security Notes

- Never commit your Stripe secret keys to version control
- Use test keys for development
- Use live keys only in production
- Rotate keys regularly for security

### Support

If you encounter issues:
1. Check the Stripe dashboard for errors
2. Review the script output for specific error messages
3. Ensure all prerequisites are met
4. Contact support if needed

import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { FeatureManagementService } from '../src/modules/subscription/services/feature-management.service';
import { SubscriptionRepository } from '../src/modules/subscription/repositories/subscription.repository';
import { SubscriptionTier } from '../src/modules/subscription/enums/subscription-tier.enum';

async function updateLawStudentFeatures() {
  console.log('🚀 Starting law student feature update...');
  
  const app = await NestFactory.createApplicationContext(AppModule);
  const featureManagementService = app.get(FeatureManagementService);
  const subscriptionRepository = app.get(SubscriptionRepository);

  try {
    // Get all law student subscriptions
    const lawStudentSubscriptions = await subscriptionRepository.find({
      tier: SubscriptionTier.LAW_STUDENT,
      status: { $in: ['active', 'trialing'] }
    });

    console.log(`📊 Found ${lawStudentSubscriptions.length} law student subscriptions`);

    let updatedCount = 0;
    const newFeatures = ['legal_research_synthesis', 'legal_research_followup'];

    for (const subscription of lawStudentSubscriptions) {
      try {
        console.log(`🔄 Updating subscription ${subscription.id}...`);
        
        // Sync features for this subscription
        const result = await featureManagementService.syncSubscriptionFeatures(
          subscription.id
        );

        if (result.added.length > 0) {
          console.log(`✅ Added features to ${subscription.id}: ${result.added.join(', ')}`);
          updatedCount++;
        } else {
          console.log(`⏭️  No new features needed for ${subscription.id}`);
        }
      } catch (error) {
        console.error(`❌ Error updating subscription ${subscription.id}:`, error.message);
      }
    }

    console.log(`\n🎉 Update complete! Updated ${updatedCount} of ${lawStudentSubscriptions.length} subscriptions`);
    
  } catch (error) {
    console.error('❌ Error during feature update:', error);
  } finally {
    await app.close();
  }
}

// Run the update
updateLawStudentFeatures().catch(console.error); 
/**
 * Direct service test for context management and rate limiting
 * This script runs a direct test of the ContextManagerService and GeminiService 
 * without requiring the API server to be running
 */
import { ContextManagerService } from '../src/modules/context-management/services/context-manager.service';
import { GeminiService } from '../src/modules/gemini/gemini.service';
import { ConfigService } from '@nestjs/config';
import { ChatRole } from '../src/common/interfaces/chat.interface';
import { RateLimiter } from '../src/common/utils/rate-limiter.util';
import { ContextSource } from '../src/common/interfaces/context-management.interface';

// Sample document content
const mainDocumentContent = `SERVICE AGREEMENT CONTRACT

This SERVICE AGREEMENT CONTRACT (the "Agreement") is made and entered into as of March 15, 2025 (the "Effective Date") by and between ACME CORPORATION and LEGAL DOCUMENT SERVICES INC.

1. SERVICES
   1.1 Provider shall provide to <PERSON><PERSON> the services (the "Services") set forth in Exhibit A attached hereto and incorporated herein by reference.
   1.2 Provider shall perform the Services in a professional manner consistent with industry standards.
   
2. TERM AND TERMINATION
   2.1 This Agreement shall commence on the Effective Date and continue for a period of twelve (12) months, unless earlier terminated pursuant to this Section 2 (the "Initial Term").`;

const relatedDocumentContent = `AMENDMENT TO SERVICE AGREEMENT CONTRACT

This AMENDMENT TO SERVICE AGREEMENT CONTRACT (the "Amendment") is made and entered into as of September 15, 2025 (the "Amendment Effective Date") by and between ACME CORPORATION and LEGAL DOCUMENT SERVICES INC.

1. AMENDMENTS TO ORIGINAL AGREEMENT
   1.1 Section 2.1 of the Original Agreement is hereby amended and restated in its entirety as follows:
       "This Agreement shall commence on the Effective Date and continue for a period of twenty-four (24) months, unless earlier terminated pursuant to this Section 2 (the "Initial Term")."`;

// Mock config service
class MockConfigService extends ConfigService {
  private readonly configs: Record<string, any> = {
    'contextManagement.enabled': true,
    'contextManagement.maxDocumentExcerpts': 5,
    'contextManagement.maxRelatedDocuments': 3,
    'contextManagement.prioritizeRecent': true,
    'gemini.maxRequests': 60,
    'gemini.windowMs': 60000,
    'gemini.throwOnLimit': false,
    'gemini.apiKey': 'mock-api-key'
  };

  get(key: string): any {
    return this.configs[key];
  }
}

/**
 * Simple mock for the document service 
 */
class MockDocumentService {
  async getDocumentById(id: string): Promise<any> {
    if (id === 'main-doc') {
      return {
        id: 'main-doc',
        content: mainDocumentContent,
        title: 'Service Agreement',
        fileType: 'text/plain',
        createdAt: new Date(),
        sections: [
          { id: 'section-1', title: 'Services', content: mainDocumentContent.split('1. SERVICES')[1].split('2. TERM')[0] },
          { id: 'section-2', title: 'Term and Termination', content: mainDocumentContent.split('2. TERM')[1] }
        ]
      };
    } else if (id === 'related-doc') {
      return {
        id: 'related-doc',
        content: relatedDocumentContent,
        title: 'Amendment to Service Agreement',
        fileType: 'text/plain',
        createdAt: new Date(),
        sections: [
          { id: 'section-1', title: 'Amendments', content: relatedDocumentContent.split('1. AMENDMENTS')[1] }
        ]
      };
    }
    return null;
  }
}

/**
 * Mock chat service 
 */
class MockChatService {
  getSessionById(id: string): any {
    return {
      id: 'session-123',
      documentId: 'main-doc',
      messages: [
        {
          id: 'msg-1',
          role: ChatRole.USER,
          content: 'What is the term of this agreement?',
          timestamp: new Date()
        },
        {
          id: 'msg-2',
          role: ChatRole.ASSISTANT,
          content: 'The initial term of the agreement is twelve (12) months from the Effective Date, unless terminated earlier as outlined in Section 2.',
          timestamp: new Date()
        }
      ]
    };
  }
}

// Utility function for a simple rate limiter simulation
const simulateRateLimiter = (): RateLimiter => {
  return new RateLimiter({
    maxRequests: 60,
    windowMs: 60000,
    throwOnLimit: false
  }, 'test-limiter');
};

/**
 * Simple mock for Gemini API responses
 */
const mockGenerateContent = async (prompt: string): Promise<string> => {
  if (prompt.includes('term')) {
    return 'Based on the document, the initial term of the agreement is twelve (12) months as stated in Section 2.1. However, according to the Amendment, this has been changed to twenty-four (24) months.';
  }
  return 'Here is a response based on the provided context.';
};

// Mock implementation of the GeminiService
class MockGeminiService extends GeminiService {
  constructor() {
    super(new MockConfigService());
    // Initialize with our mock rate limiter
    this.rateLimiter = simulateRateLimiter();
  }

  // Override methods to provide mock implementations
  async generateEnhancedResponse(
    messages: { role: ChatRole; content: string }[],
    documentId: string,
    sessionId: string,
    relatedDocumentIds?: string[],
  ): Promise<string> {
    console.log(`Generating enhanced response with ${relatedDocumentIds ? relatedDocumentIds.length : 0} related documents`);
    
    // Simulate rate limiting
    return this.rateLimiter.executeWithRateLimit(async () => {
      // Just return a mock response for testing
      const lastMessage = messages[messages.length - 1];
      return mockGenerateContent(lastMessage.content);
    }, 'generateEnhancedResponse');
  }
}

/**
 * Run test demonstration of context management and rate limiting
 */
async function runContextManagementDemo() {
  console.log('------------------- CONTEXT MANAGEMENT DEMO -------------------');
  
  // Create service instances
  const configService = new MockConfigService();
  const mockDocService = new MockDocumentService();
  const mockChatService = new MockChatService();
  
  // Initialize context manager service with our mocks
  const contextManager = new ContextManagerService(
    configService,
    mockDocService as any,
    mockChatService as any
  );
  
  // Initialize Gemini service with our mocks
  const geminiService = new MockGeminiService();
  
  console.log('\n1. Building context window without related documents:');
  const basicContext = await contextManager.buildContextWindow({
    documentId: 'main-doc',
    sessionId: 'session-123',
    query: 'What is the term of the agreement?',
  });
  
  console.log(`- Generated ${basicContext.documentExcerpts.length} document excerpts`);
  console.log(`- Chat history length: ${basicContext.chatHistory.length} characters`);
  console.log(`- Used sources: ${basicContext.usedSources.join(', ')}`);
  
  console.log('\n2. Building enhanced context window with related documents:');
  const enhancedContext = await contextManager.buildContextWindow({
    documentId: 'main-doc',
    sessionId: 'session-123',
    query: 'How does the amendment change the term of the agreement?',
    relatedDocumentIds: ['related-doc']
  });
  
  console.log(`- Generated ${enhancedContext.documentExcerpts.length} document excerpts`);
  console.log(`- Generated ${enhancedContext.relatedDocumentExcerpts?.length || 0} related document excerpts`);
  console.log(`- Used sources: ${enhancedContext.usedSources.join(', ')}`);
  
  // Test the response generation with & without related documents
  console.log('\n3. Generating response without related documents:');
  const basicResponse = await geminiService.generateEnhancedResponse(
    [{ role: ChatRole.USER, content: 'What is the term of the agreement?' }],
    'main-doc',
    'session-123'
  );
  console.log(`- Response: ${basicResponse}`);
  
  console.log('\n4. Generating response with related documents:');
  const enhancedResponse = await geminiService.generateEnhancedResponse(
    [{ role: ChatRole.USER, content: 'How does the amendment change the term of the agreement?' }],
    'main-doc',
    'session-123',
    ['related-doc']
  );
  console.log(`- Response: ${enhancedResponse}`);
  
  // Check rate limiter status
  console.log('\n5. Rate limiter status:');
  const rateLimiterInfo = geminiService.getRateLimiterUtilization();
  console.log(`- Utilization: ${rateLimiterInfo.utilizationPercentage * 100}%`);
  console.log(`- Remaining tokens: ${rateLimiterInfo.remainingTokens}`);
  console.log(`- Max tokens: ${rateLimiterInfo.maxTokens}`);
  
  console.log('\n------------------- DEMO COMPLETE -------------------');
}

// Run the demo
runContextManagementDemo().catch(e => console.error('Error running demo:', e));

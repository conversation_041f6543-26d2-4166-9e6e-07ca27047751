import { AiConversationService } from '../src/modules/chat-negotiation/services/ai-conversation.service';
import { AIService } from '../src/modules/ai/services/ai.service';
import { DocumentNegotiationService } from '../src/modules/chat-negotiation/services/document-negotiation.service';

// Mock dependencies
class MockAIService {
  async generateChatResponse(messages: any[]) {
    // Simulate AI response based on the last user message
    const lastMessage = messages[messages.length - 1].content.toLowerCase();
    
    if (lastMessage.includes('confidentiality')) {
      return {
        content: 'The confidentiality period is 5 years, which is longer than the standard 2-3 years typically seen in NDAs. You might want to negotiate this down to 2-3 years unless there are specific reasons for the extended period.',
        suggestions: [
          'Suggest reducing to 3 years',
          'Ask about the reason for 5 years',
          'Accept as is'
        ]
      };
    }
    
    return {
      content: 'I understand your question. Based on the document, I can provide more specific information if you ask about particular terms or sections.',
      suggestions: []
    };
  }
  
  async generateResponse(prompt: string) {
    return 'Sample AI response';
  }
}

class MockDocumentNegotiationService {
  async getNegotiationContext() {
    return {
      contractType: 'Non-Disclosure Agreement',
      riskLevel: 'HIGH',
      summary: 'This NDA includes standard confidentiality clauses with some above-market terms.',
      keyIssues: [
        {
          title: 'Confidentiality Period',
          description: 'The agreement specifies a 5-year confidentiality period which is longer than standard 2-3 year terms.',
          riskLevel: 'HIGH',
          keywords: ['confidentiality', 'nda period', 'duration']
        },
        {
          title: 'Governing Law',
          description: 'The agreement is governed by the laws of the State of California.',
          riskLevel: 'LOW',
          keywords: ['law', 'jurisdiction', 'governing law']
        }
      ]
    };
  }
}

// Test conversation
async function testConversation() {
  const aiService = new MockAIService() as unknown as AIService;
  const docService = new MockDocumentNegotiationService() as unknown as DocumentNegotiationService;
  const service = new AiConversationService(aiService, docService);
  
  // Test cases
  const testCases = [
    'Hello',
    'What is the confidentiality period?',
    'What about the governing law?',
    'Can we change the jurisdiction?',
    'Thank you!'
  ];
  
  // Run the conversation
  console.log('=== Starting Conversation Test ===\n');
  
  for (const message of testCases) {
    console.log(`You: ${message}`);
    
    const response = await service.generateDocumentAwareResponse(
      { id: 'test-session' },
      { message },
      await docService.getNegotiationContext('test-session')
    );
    
    console.log(`AI: ${response}\n`);
  }
  
  console.log('=== Conversation Test Complete ===');
}

// Run the test
testConversation().catch(console.error);

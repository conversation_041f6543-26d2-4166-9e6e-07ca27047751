import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { NegotiationPlaybookSeederService } from '../src/modules/documents/services/negotiation-playbook-seeder.service';
import { Logger } from '@nestjs/common';

async function seedSamplePlaybooks() {
  const logger = new Logger('SeedSamplePlaybooks');
  
  try {
    logger.log('Starting sample playbooks seeding process...');
    
    // Create the NestJS application
    const app = await NestFactory.createApplicationContext(AppModule);
    
    // Get the seeder service
    const seederService = app.get(NegotiationPlaybookSeederService);
    
    // Seed the sample playbooks
    await seederService.seedSamplePlaybooks();
    
    logger.log('Sample playbooks seeding completed successfully!');
    
    // Close the application
    await app.close();
    
  } catch (error) {
    logger.error('Failed to seed sample playbooks:', error);
    process.exit(1);
  }
}

// Run the seeding script
seedSamplePlaybooks();

import { MongoClient } from 'mongodb';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.development' });

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/legal-document-analyzer';
const ORGANIZATION_ID = '2b7b40e6-307a-4c72-9b45-021f78a99a12'; // Your organization ID

async function updateSubscription() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db();
    const subscriptionsCollection = db.collection('subscriptions');
    
    // Find the current subscription
    const currentSubscription = await subscriptionsCollection.findOne({ organizationId: ORGANIZATION_ID });
    
    if (!currentSubscription) {
      console.log('No subscription found for this organization. Creating a new one...');
      
      // Create a new subscription with ENTERPRISE tier
      const result = await subscriptionsCollection.insertOne({
        organizationId: ORGANIZATION_ID,
        tier: 'enterprise',
        status: 'active',
        stripeCustomerId: 'manual_update',
        stripeSubscriptionId: 'manual_update',
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        cancelAtPeriodEnd: false,
        features: [
          'basic_analysis',
          'enhanced_analysis',
          'batch_processing',
          'custom_models',
          'enhanced_comparison',
          'document_upload',
          'chat',
          'advanced_analysis',
          'bulk_upload',
          'priority_processing',
          'custom_training',
          'api_access',
          'team_collaboration'
        ],
        usageStats: {
          documentsProcessed: 0,
          analysisCount: 0,
          lastUpdated: new Date(),
        },
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      console.log('New subscription created:', result.insertedId);
    } else {
      console.log('Existing subscription found. Updating to ENTERPRISE tier...');
      
      // Update the existing subscription to ENTERPRISE tier
      const result = await subscriptionsCollection.updateOne(
        { organizationId: ORGANIZATION_ID },
        { 
          $set: {
            tier: 'enterprise',
            status: 'active',
            currentPeriodEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
            features: [
              'basic_analysis',
              'enhanced_analysis',
              'batch_processing',
              'custom_models',
              'enhanced_comparison',
              'document_upload',
              'chat',
              'advanced_analysis',
              'bulk_upload',
              'priority_processing',
              'custom_training',
              'api_access',
              'team_collaboration'
            ],
            updatedAt: new Date()
          }
        }
      );
      
      console.log('Subscription updated:', result.modifiedCount > 0 ? 'Success' : 'No changes made');
    }
    
  } catch (error) {
    console.error('Error updating subscription:', error);
  } finally {
    await client.close();
    console.log('MongoDB connection closed');
  }
}

updateSubscription().catch(console.error);

/**
 * Manual test script for context management feature
 * This script tests the enhanced context handling and rate limiting features
 */
const axios = require('axios');
const fs = require('fs');
const FormData = require('form-data');
const path = require('path');

// API base URL
const API_URL = 'http://localhost:3000';

// Test document paths
const CONTRACT_PATH = path.join(__dirname, '../test/fixtures/sample-contract.pdf');
const AMENDMENT_PATH = path.join(__dirname, '../test/fixtures/sample-amendment.pdf');

// Store IDs from API responses
let documentId = null;
let relatedDocumentId = null;
let sessionId = null;

/**
 * Upload a document
 * @param {string} filePath - Path to document file
 * @returns {Promise<string>} - Document ID
 */
async function uploadDocument(filePath) {
  console.log(`Uploading document: ${filePath}`);
  
  const formData = new FormData();
  formData.append('file', fs.createReadStream(filePath));
  
  try {
    const response = await axios.post(`${API_URL}/documents`, formData, {
      headers: formData.getHeaders()
    });
    
    console.log(`Document uploaded successfully. ID: ${response.data.id}`);
    return response.data.id;
  } catch (error) {
    console.error('Error uploading document:', error.message);
    throw error;
  }
}

/**
 * Create a chat session
 * @param {string} documentId - Document ID
 * @returns {Promise<string>} - Session ID
 */
async function createChatSession(documentId) {
  console.log(`Creating chat session for document: ${documentId}`);
  
  try {
    const response = await axios.post(`${API_URL}/chat/sessions`, {
      documentId
    });
    
    console.log(`Chat session created successfully. ID: ${response.data.id}`);
    return response.data.id;
  } catch (error) {
    console.error('Error creating chat session:', error.message);
    throw error;
  }
}

/**
 * Send a message with standard context
 * @param {string} sessionId - Session ID
 * @param {string} content - Message content
 * @returns {Promise<Object>} - Response message
 */
async function sendStandardMessage(sessionId, content) {
  console.log(`Sending standard message: "${content}"`);
  
  try {
    const response = await axios.post(`${API_URL}/chat/messages`, {
      sessionId,
      content
    });
    
    console.log('Response:', response.data.content.substring(0, 150) + '...');
    return response.data;
  } catch (error) {
    console.error('Error sending message:', error.message);
    throw error;
  }
}

/**
 * Send a message with enhanced context
 * @param {string} sessionId - Session ID
 * @param {string} content - Message content
 * @param {string[]} relatedDocumentIds - Related document IDs
 * @returns {Promise<Object>} - Response message
 */
async function sendEnhancedMessage(sessionId, content, relatedDocumentIds) {
  console.log(`Sending enhanced message with related documents: "${content}"`);
  
  try {
    const response = await axios.post(`${API_URL}/chat/messages`, {
      sessionId,
      content,
      relatedDocumentIds
    });
    
    console.log('Response:', response.data.content.substring(0, 150) + '...');
    return response.data;
  } catch (error) {
    console.error('Error sending enhanced message:', error.message);
    throw error;
  }
}

/**
 * Check rate limiter status
 * @returns {Promise<Object>} - Rate limiter info
 */
async function checkRateLimiter() {
  console.log('Checking rate limiter status');
  
  try {
    const response = await axios.get(`${API_URL}/gemini/rate-limit`);
    console.log('Rate limiter status:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error checking rate limiter:', error.message);
    throw error;
  }
}

/**
 * Run the full test suite
 */
async function runTests() {
  try {
    // 1. Upload documents
    documentId = await uploadDocument(CONTRACT_PATH);
    relatedDocumentId = await uploadDocument(AMENDMENT_PATH);
    
    // 2. Create a chat session
    sessionId = await createChatSession(documentId);
    
    // 3. Check rate limiter before messages
    await checkRateLimiter();
    
    // 4. Send a standard message
    await sendStandardMessage(sessionId, 'What are the main terms of this contract?');
    
    // 5. Send an enhanced message with related document
    await sendEnhancedMessage(
      sessionId, 
      'How does the amendment change the terms in the main contract?',
      [relatedDocumentId]
    );
    
    // 6. Check rate limiter after messages
    await checkRateLimiter();
    
    console.log('\nAll tests completed successfully!');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the tests
console.log('Starting context management tests...');
runTests();

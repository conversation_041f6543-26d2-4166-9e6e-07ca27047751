#!/bin/bash

# Configuration
SESSION_ID="685193186b3ee0bcf9ef5502"
AUTH_TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************.w84c2lUccYNBsvBw0Tv2caIOdCghSkJo-BdLY61IrXQ"
API_URL="http://localhost:4000/api/chat-negotiation/sessions/$SESSION_ID/moves"

# Function to send a message and get response
send_message() {
    local message="$1"
    local strategy="${2:-collaborative}"
    local sentiment="${3:-positive}"
    
    echo "\nYou: $message"
    
    local response=$(curl -s -X POST "$API_URL" \
        -H "Authorization: Bearer $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{\"content\":\"$message\",\"extractedData\":{\"strategy\":\"$strategy\",\"sentiment\":\"$sentiment\"}}")
    
    # Extract AI response
    local ai_response=$(echo $response | jq -r '.aiResponse.content')
    echo "AI: $ai_response"
    
    # Return the full response for further processing if needed
    echo $response
}

# Start the conversation
echo "Starting chat negotiation session $SESSION_ID..."

# Conversation flow
send_message "Hi, I'm interested in negotiating a software licensing agreement." "collaborative" "positive"
send_message "I need the ability to scale to 1000+ users and have concerns about the liability terms." "collaborative" "neutral"
send_message "Your standard liability cap seems too low for our needs. Can we increase it to $500,000?" "competitive" "neutral"
send_message "I understand your position. How about we meet in the middle at $400,000 with a mutual indemnification clause?" "collaborative" "positive"
send_message "That works for us. Let's proceed with those terms." "collaborative" "positive"

echo "\nConversation completed."

import axios, { AxiosInstance } from 'axios';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

interface TestResult {
  endpoint: string;
  method: string;
  status: 'PASS' | 'FAIL' | 'SKIP';
  statusCode?: number;
  responseTime?: number;
  error?: string;
  data?: any;
}

class GamificationEndpointTester {
  private api: AxiosInstance;
  private baseUrl: string;
  private authToken: string;
  private results: TestResult[] = [];

  constructor() {
    this.baseUrl = process.env.API_BASE_URL || 'http://localhost:3000';
    this.authToken = process.env.TEST_AUTH_TOKEN || 'mock-jwt-token';
    
    this.api = axios.create({
      baseURL: this.baseUrl,
      timeout: 10000,
      headers: {
        'Authorization': `Bearer ${this.authToken}`,
        'Content-Type': 'application/json',
      },
    });
  }

  async runAllTests(): Promise<void> {
    console.log('🎮 Starting Gamification Endpoint Tests...\n');
    console.log(`Base URL: ${this.baseUrl}`);
    console.log(`Auth Token: ${this.authToken.substring(0, 20)}...\n`);

    // Test all endpoint categories
    await this.testGamificationEndpoints();
    await this.testCharacterEndpoints();
    await this.testAchievementEndpoints();
    await this.testLeaderboardEndpoints();
    await this.testEnhancedNegotiationEndpoints();

    // Print summary
    this.printSummary();
  }

  private async testEndpoint(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    endpoint: string,
    data?: any,
    expectedStatus: number = 200
  ): Promise<TestResult> {
    const startTime = Date.now();
    const result: TestResult = {
      endpoint,
      method,
      status: 'FAIL',
    };

    try {
      let response;
      switch (method) {
        case 'GET':
          response = await this.api.get(endpoint);
          break;
        case 'POST':
          response = await this.api.post(endpoint, data);
          break;
        case 'PUT':
          response = await this.api.put(endpoint, data);
          break;
        case 'DELETE':
          response = await this.api.delete(endpoint);
          break;
      }

      result.statusCode = response.status;
      result.responseTime = Date.now() - startTime;
      result.data = response.data;
      result.status = response.status === expectedStatus ? 'PASS' : 'FAIL';

      console.log(`✅ ${method} ${endpoint} - ${response.status} (${result.responseTime}ms)`);
    } catch (error: any) {
      result.statusCode = error.response?.status;
      result.responseTime = Date.now() - startTime;
      result.error = error.message;
      result.status = 'FAIL';

      console.log(`❌ ${method} ${endpoint} - ${error.response?.status || 'ERROR'} (${result.responseTime}ms)`);
      console.log(`   Error: ${error.message}`);
    }

    this.results.push(result);
    return result;
  }

  private async testGamificationEndpoints(): Promise<void> {
    console.log('📊 Testing Gamification Profile Endpoints...');

    // Get user profile
    await this.testEndpoint('GET', '/api/gamification/profile');

    // Get public profile (would need a real user ID)
    await this.testEndpoint('GET', '/api/gamification/profile/test-user-123');

    // Award experience (admin endpoint)
    await this.testEndpoint('POST', '/api/gamification/experience', {
      amount: 100,
      source: 'test_award',
      metadata: { test: true }
    });

    console.log('');
  }

  private async testCharacterEndpoints(): Promise<void> {
    console.log('👥 Testing Character Endpoints...');

    // Get all characters
    await this.testEndpoint('GET', '/api/gamification/characters');

    // Get characters with filters
    await this.testEndpoint('GET', '/api/gamification/characters?difficulty=2');
    await this.testEndpoint('GET', '/api/gamification/characters?unlocked=true');
    await this.testEndpoint('GET', '/api/gamification/characters?specialty=friendly');

    // Get unlocked characters
    await this.testEndpoint('GET', '/api/gamification/characters/unlocked');

    // Get default character
    await this.testEndpoint('GET', '/api/gamification/characters/default');

    // Get characters by difficulty
    await this.testEndpoint('GET', '/api/gamification/characters/difficulty/1');

    // Get specific character details
    await this.testEndpoint('GET', '/api/gamification/characters/default_character');

    // Try to unlock a character
    await this.testEndpoint('POST', '/api/gamification/characters/sarah_chen/unlock');

    // Seed characters (admin endpoint)
    await this.testEndpoint('POST', '/api/gamification/characters/seed');

    console.log('');
  }

  private async testAchievementEndpoints(): Promise<void> {
    console.log('🏆 Testing Achievement Endpoints...');

    // Get all achievements
    await this.testEndpoint('GET', '/api/gamification/achievements');

    // Get achievements with filters
    await this.testEndpoint('GET', '/api/gamification/achievements?category=efficiency');
    await this.testEndpoint('GET', '/api/gamification/achievements?rarity=rare');
    await this.testEndpoint('GET', '/api/gamification/achievements?unlocked=false');

    // Get achievement categories
    await this.testEndpoint('GET', '/api/gamification/achievements/categories');

    // Get rare achievements
    await this.testEndpoint('GET', '/api/gamification/achievements/rare');

    // Get specific achievement
    await this.testEndpoint('GET', '/api/gamification/achievements/speed_demon');

    // Get achievement leaderboard
    await this.testEndpoint('GET', '/api/gamification/achievements/leaderboard/speed_demon');

    // Seed achievements (admin endpoint)
    await this.testEndpoint('POST', '/api/gamification/achievements/seed');

    console.log('');
  }

  private async testLeaderboardEndpoints(): Promise<void> {
    console.log('🏅 Testing Leaderboard Endpoints...');

    // Get default leaderboard
    await this.testEndpoint('GET', '/api/gamification/leaderboards');

    // Get leaderboards with different parameters
    await this.testEndpoint('GET', '/api/gamification/leaderboards?type=weekly&scope=global');
    await this.testEndpoint('GET', '/api/gamification/leaderboards?type=monthly&scope=organization');
    await this.testEndpoint('GET', '/api/gamification/leaderboards?type=all_time&limit=100');

    // Get leaderboard around user
    await this.testEndpoint('GET', '/api/gamification/leaderboards/around-user?range=5');

    // Get user rank
    await this.testEndpoint('GET', '/api/gamification/leaderboards/user-rank');

    // Get global leaderboard
    await this.testEndpoint('GET', '/api/gamification/leaderboards/global');

    // Get organization leaderboard
    await this.testEndpoint('GET', '/api/gamification/leaderboards/organization');

    // Get leaderboard summary
    await this.testEndpoint('GET', '/api/gamification/leaderboards/summary');

    console.log('');
  }

  private async testEnhancedNegotiationEndpoints(): Promise<void> {
    console.log('🤝 Testing Enhanced Negotiation Simulator Endpoints...');

    // Test enhanced session creation (might fail if no scenarios exist)
    await this.testEndpoint('POST', '/api/negotiation-simulator/sessions', {
      scenarioId: 'test-scenario',
      characterId: 'default_character',
      aiPersonality: {
        aggressiveness: 0.5,
        flexibility: 0.7,
        riskTolerance: 0.6,
        communicationStyle: 'DIPLOMATIC'
      }
    }, 201);

    // Get sessions (should include gamification data)
    await this.testEndpoint('GET', '/api/negotiation-simulator/sessions');

    console.log('');
  }

  private printSummary(): void {
    console.log('📋 Test Summary');
    console.log('================');

    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const total = this.results.length;

    console.log(`Total Tests: ${total}`);
    console.log(`Passed: ${passed} ✅`);
    console.log(`Failed: ${failed} ❌`);
    console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);

    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results
        .filter(r => r.status === 'FAIL')
        .forEach(r => {
          console.log(`   ${r.method} ${r.endpoint} - ${r.statusCode || 'ERROR'}`);
          if (r.error) {
            console.log(`      Error: ${r.error}`);
          }
        });
    }

    console.log('\n📊 Response Time Statistics:');
    const responseTimes = this.results
      .filter(r => r.responseTime)
      .map(r => r.responseTime!);
    
    if (responseTimes.length > 0) {
      const avgTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      const maxTime = Math.max(...responseTimes);
      const minTime = Math.min(...responseTimes);
      
      console.log(`   Average: ${avgTime.toFixed(0)}ms`);
      console.log(`   Min: ${minTime}ms`);
      console.log(`   Max: ${maxTime}ms`);
    }

    console.log('\n🎮 Gamification Integration Test Complete!');
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new GamificationEndpointTester();
  tester.runAllTests()
    .then(() => {
      console.log('\n✅ All tests completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test runner failed:', error);
      process.exit(1);
    });
}

export { GamificationEndpointTester };

#!/bin/bash

# Chat Negotiation Endpoint Testing Script
# Usage: ./scripts/test-chat-negotiation.sh [BASE_URL] [AUTH_TOKEN]

BASE_URL=${1:-"http://localhost:4000"}
AUTH_TOKEN=${2:-"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************.Hvu_V7wLi6qPEPMAMhuAgPH8z9yVQS1tIm0fjjyR2h8"}

echo "💬 Testing Chat Negotiation Endpoints"
echo "====================================="
echo "Base URL: $BASE_URL"
echo "Auth Token: ${AUTH_TOKEN:0:20}..."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to test an endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=${4:-200}
    
    echo -n "Testing $method $endpoint ... "
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "%{http_code}" -H "Authorization: Bearer $AUTH_TOKEN" \
                   -H "Content-Type: application/json" \
                   "$BASE_URL$endpoint")
    elif [ "$method" = "POST" ]; then
        response=$(curl -s -w "%{http_code}" -X POST \
                   -H "Authorization: Bearer $AUTH_TOKEN" \
                   -H "Content-Type: application/json" \
                   -d "$data" \
                   "$BASE_URL$endpoint")
    fi
    
    # Extract status code (last 3 characters)
    status_code="${response: -3}"
    response_body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} ($status_code)"
        # Extract session ID if this is a session creation
        if [[ "$endpoint" == *"/sessions" && "$method" == "POST" ]]; then
            SESSION_ID=$(echo "$response_body" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)
            echo "   Session ID: $SESSION_ID"
        fi
    else
        echo -e "${RED}❌ FAIL${NC} ($status_code, expected $expected_status)"
        if [ ${#response_body} -lt 500 ]; then
            echo "   Response: $response_body"
        fi
    fi
}

echo -e "${BLUE}🚀 Testing Chat Negotiation System${NC}"
echo "--------------------------------"

# Test 1: Create Chat Negotiation Session
echo -e "\n${YELLOW}📝 Test 1: Create Chat Negotiation Session${NC}"
test_endpoint "POST" "/api/chat-negotiation/sessions" '{
  "scenarioId": "68405c9d35f27e4188e67b72",
  "aiPersonality": {
    "characterId": "default_character",
    "aggressiveness": 0.4,
    "flexibility": 0.7,
    "riskTolerance": 0.6,
    "communicationStyle": "ANALYTICAL"
  },
  "metadata": {
    "source": "test_script",
    "version": "1.0"
  }
}' 201

# Wait a moment for session creation
sleep 1

# Test 2: Get User Sessions
echo -e "\n${YELLOW}📋 Test 2: Get User Sessions${NC}"
test_endpoint "GET" "/api/chat-negotiation/sessions"

# Test 3: Get Session Details (if we have a session ID)
if [ ! -z "$SESSION_ID" ]; then
    echo -e "\n${YELLOW}🔍 Test 3: Get Session Details${NC}"
    test_endpoint "GET" "/api/chat-negotiation/sessions/$SESSION_ID"
    
    # Test 4: Send Chat Move
    echo -e "\n${YELLOW}💬 Test 4: Send Chat Move${NC}"
    test_endpoint "POST" "/api/chat-negotiation/sessions/$SESSION_ID/moves" '{
      "content": "I am thinking around $50,000 for the software license with Net 30 payment terms. This seems like a fair starting point for both parties.",
      "extractedData": {
        "offer": {
          "price": 50000,
          "currency": "USD",
          "terms": ["Net 30"]
        },
        "strategy": "collaborative",
        "sentiment": "positive",
        "confidence": 0.8
      },
      "context": {
        "userConfidence": 0.8,
        "timeSpent": 45
      }
    }'
    
    # Test 5: Send Another Chat Move
    echo -e "\n${YELLOW}💬 Test 5: Send Follow-up Chat Move${NC}"
    test_endpoint "POST" "/api/chat-negotiation/sessions/$SESSION_ID/moves" '{
      "content": "What flexibility do we have on the implementation timeline? I am open to discussing a phased approach.",
      "extractedData": {
        "strategy": "accommodating",
        "sentiment": "positive",
        "confidence": 0.7
      }
    }'
    
    # Test 6: Get Updated Session Details
    echo -e "\n${YELLOW}🔄 Test 6: Get Updated Session Details${NC}"
    test_endpoint "GET" "/api/chat-negotiation/sessions/$SESSION_ID"
else
    echo -e "\n${RED}⚠️ Skipping session-specific tests (no session ID)${NC}"
fi

# Test 7: Data Extraction
echo -e "\n${YELLOW}🧠 Test 7: Data Extraction${NC}"
test_endpoint "POST" "/api/chat-negotiation/extract-data" '{
  "message": "I propose $75,000 with Net 60 payment terms and a 10% discount for early payment",
  "context": {
    "scenarioType": "software_licensing",
    "currentRound": 2
  }
}'

# Test 8: Get Sessions with Filters
echo -e "\n${YELLOW}🔍 Test 8: Get Sessions with Filters${NC}"
test_endpoint "GET" "/api/chat-negotiation/sessions?status=active&limit=10"

# Test 9: Complex Data Extraction
echo -e "\n${YELLOW}🧠 Test 9: Complex Data Extraction${NC}"
test_endpoint "POST" "/api/chat-negotiation/extract-data" '{
  "message": "This is a challenging negotiation. I need to see more data before I can commit to anything above $40k. The market research shows similar deals at 35-45k range.",
  "context": {
    "scenarioType": "contract_negotiation",
    "previousOffers": [{"price": 60000}],
    "currentRound": 4
  }
}'

echo ""
echo -e "${YELLOW}📋 Testing Summary${NC}"
echo "=================="
echo "All chat negotiation endpoint tests completed!"
echo ""
echo "Note: Some tests may fail if:"
echo "- Server is not running on $BASE_URL"
echo "- Authentication token is invalid or expired"
echo "- Database connection issues"
echo "- Required negotiation scenarios don't exist"
echo ""
echo "To run the full test suite:"
echo "1. Start your server: npm run start:dev"
echo "2. Ensure gamification is working: npm run test:endpoints"
echo "3. Run this script: ./scripts/test-chat-negotiation.sh"
echo ""
echo "💬 Chat negotiation endpoint testing complete!"

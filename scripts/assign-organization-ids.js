/**
 * Migration Script: Assign Organization IDs to Documents
 * 
 * This script assigns organization IDs to existing documents in the database
 * to ensure proper tenant isolation. It's designed to be run as a one-time
 * migration to support the enhanced security measures implemented in the
 * document access guard.
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/legal-document-analyzer';

// Default organization ID for documents without an owner
const DEFAULT_ORG_ID = process.env.DEFAULT_ORG_ID || uuidv4();

// Document schema (simplified version of what's in the application)
const documentSchema = new mongoose.Schema({
  _id: String,
  originalName: String,
  status: String,
  uploadDate: Date,
  metadata: mongoose.Schema.Types.Mixed
}, { collection: 'documents' });

// User schema (simplified version)
const userSchema = new mongoose.Schema({
  _id: String,
  email: String,
  organizationId: String
}, { collection: 'users' });

async function main() {
  console.log('Starting organization ID migration...');
  
  try {
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB');
    
    // Define models
    const Document = mongoose.model('Document', documentSchema);
    const User = mongoose.model('User', userSchema);
    
    // Get all documents
    const documents = await Document.find({});
    console.log(`Found ${documents.length} documents`);
    
    // Get all users with their organization IDs
    const users = await User.find({});
    console.log(`Found ${users.length} users`);
    
    // Create a map of user IDs to organization IDs
    const userOrgMap = {};
    users.forEach(user => {
      if (user._id && user.organizationId) {
        userOrgMap[user._id] = user.organizationId;
      }
    });
    
    // Track migration statistics
    const stats = {
      total: documents.length,
      updated: 0,
      skipped: 0,
      errors: 0
    };
    
    // Process each document
    for (const doc of documents) {
      try {
        // Skip documents that already have an organization ID
        if (doc.metadata && doc.metadata.organizationId) {
          console.log(`Document ${doc._id} already has organization ID: ${doc.metadata.organizationId}`);
          stats.skipped++;
          continue;
        }
        
        // Initialize metadata if it doesn't exist
        if (!doc.metadata) {
          doc.metadata = {};
        }
        
        let organizationId = DEFAULT_ORG_ID;
        
        // Try to find the owner's organization ID
        if (doc.metadata.ownerId && userOrgMap[doc.metadata.ownerId]) {
          organizationId = userOrgMap[doc.metadata.ownerId];
          console.log(`Assigning organization ID ${organizationId} from owner ${doc.metadata.ownerId} to document ${doc._id}`);
        } else {
          console.log(`No owner found for document ${doc._id}, using default organization ID: ${DEFAULT_ORG_ID}`);
        }
        
        // Assign the organization ID
        doc.metadata.organizationId = organizationId;
        
        // Save the document
        await Document.updateOne({ _id: doc._id }, { metadata: doc.metadata });
        stats.updated++;
        
        console.log(`Updated document ${doc._id} with organization ID: ${organizationId}`);
      } catch (error) {
        console.error(`Error updating document ${doc._id}: ${error.message}`);
        stats.errors++;
      }
    }
    
    // Print migration summary
    console.log('\nMigration Summary:');
    console.log(`Total documents: ${stats.total}`);
    console.log(`Updated: ${stats.updated}`);
    console.log(`Skipped (already had organization ID): ${stats.skipped}`);
    console.log(`Errors: ${stats.errors}`);
    
    console.log('\nMigration completed successfully!');
  } catch (error) {
    console.error(`Migration failed: ${error.message}`);
  } finally {
    // Close MongoDB connection
    await mongoose.connection.close();
    console.log('Disconnected from MongoDB');
  }
}

// Run the migration
main().catch(console.error);

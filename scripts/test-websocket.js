const io = require('socket.io-client');

// Your JWT token
const JWT_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************.Hvu_V7wLi6qPEPMAMhuAgPH8z9yVQS1tIm0fjjyR2h8';

console.log('🔌 Testing WebSocket Real-time Features...\n');

// Connect to the gamification WebSocket namespace
const socket = io('http://localhost:4000/gamification', {
  auth: {
    token: JWT_TOKEN
  },
  transports: ['websocket']
});

let testResults = [];

function logTest(test, status, message) {
  const result = `${status === 'PASS' ? '✅' : '❌'} ${test}: ${message}`;
  console.log(result);
  testResults.push({ test, status, message });
}

// Connection events
socket.on('connect', () => {
  logTest('WebSocket Connection', 'PASS', 'Connected to gamification namespace');
  
  // Test 1: Join a session
  console.log('\n📊 Testing Session Events...');
  socket.emit('join_session', { sessionId: 'test-session-123' });
});

socket.on('disconnect', () => {
  logTest('WebSocket Disconnection', 'PASS', 'Disconnected from server');
});

socket.on('connect_error', (error) => {
  logTest('WebSocket Connection', 'FAIL', `Connection error: ${error.message}`);
});

// Test initial data
socket.on('initial_data', (data) => {
  logTest('Initial Data', 'PASS', `Received profile data: Level ${data.profile?.level?.current || 'unknown'}`);
  console.log('Initial Data:', JSON.stringify(data, null, 2));
});

// Test game state updates
socket.on('game_state_update', (data) => {
  logTest('Game State Update', 'PASS', `Received game state for session: ${data.sessionId}`);
  console.log('Game State:', JSON.stringify(data, null, 2));
});

// Test live score updates
socket.on('live_score_update', (data) => {
  logTest('Live Score Update', 'PASS', `Received live score: ${data.score}`);
  console.log('Live Score:', JSON.stringify(data, null, 2));
});

// Test achievement notifications
socket.on('achievement_unlocked', (data) => {
  logTest('Achievement Unlocked', 'PASS', `Achievement: ${data.achievement?.title || 'unknown'}`);
  console.log('Achievement:', JSON.stringify(data, null, 2));
});

// Test level up notifications
socket.on('level_up', (data) => {
  logTest('Level Up', 'PASS', `Level up to: ${data.newLevel || 'unknown'}`);
  console.log('Level Up:', JSON.stringify(data, null, 2));
});

// Test pressure events
socket.on('pressure_event', (data) => {
  logTest('Pressure Event', 'PASS', `Pressure event: ${data.event?.title || 'unknown'}`);
  console.log('Pressure Event:', JSON.stringify(data, null, 2));
});

// Test relationship updates
socket.on('relationship_update', (data) => {
  logTest('Relationship Update', 'PASS', `Relationship updated`);
  console.log('Relationship Update:', JSON.stringify(data, null, 2));
});

// Test hints
socket.on('hints_update', (data) => {
  logTest('Hints Update', 'PASS', `Received ${data.hints?.length || 0} hints`);
  console.log('Hints:', JSON.stringify(data, null, 2));
});

// Test error handling
socket.on('error', (data) => {
  logTest('Error Handling', 'PASS', `Received error: ${data.message}`);
});

// Run tests after connection
setTimeout(() => {
  if (socket.connected) {
    console.log('\n🎮 Running WebSocket Tests...');
    
    // Test 2: Request live score
    console.log('\n📈 Testing Live Score...');
    socket.emit('request_live_score', {
      sessionId: 'test-session-123',
      moveData: {
        strategy: 'collaborative',
        message: 'Let me propose a win-win solution...',
        offer: { price: 50000, terms: 'flexible' }
      }
    });
    
    // Test 3: Request hints
    setTimeout(() => {
      console.log('\n💡 Testing Hints...');
      socket.emit('request_hints', {
        sessionId: 'test-session-123',
        context: {
          currentRound: 3,
          userStress: 0.4,
          aiMood: 'neutral',
          dealGap: 0.3
        }
      });
    }, 1000);
    
    // Test 4: Leave session
    setTimeout(() => {
      console.log('\n🚪 Testing Leave Session...');
      socket.emit('leave_session', { sessionId: 'test-session-123' });
    }, 2000);
    
    // Print summary and disconnect
    setTimeout(() => {
      console.log('\n📋 WebSocket Test Summary');
      console.log('==========================');
      
      const passed = testResults.filter(r => r.status === 'PASS').length;
      const failed = testResults.filter(r => r.status === 'FAIL').length;
      const total = testResults.length;
      
      console.log(`Total Tests: ${total}`);
      console.log(`Passed: ${passed} ✅`);
      console.log(`Failed: ${failed} ❌`);
      console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
      
      if (failed > 0) {
        console.log('\n❌ Failed Tests:');
        testResults.filter(r => r.status === 'FAIL').forEach(r => {
          console.log(`   ${r.test}: ${r.message}`);
        });
      }
      
      console.log('\n🎮 WebSocket testing complete!');
      socket.disconnect();
      process.exit(0);
    }, 4000);
  } else {
    logTest('WebSocket Connection', 'FAIL', 'Failed to connect within timeout');
    process.exit(1);
  }
}, 2000);

// Timeout safety
setTimeout(() => {
  console.log('\n⏰ Test timeout reached');
  socket.disconnect();
  process.exit(1);
}, 10000);

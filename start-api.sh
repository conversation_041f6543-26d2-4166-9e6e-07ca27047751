#!/bin/bash

# Set environment variables
export PORT=3000
export MONGODB_URI="mongodb://localhost:27017/legal-doc-analyzer"
export AI_PROVIDER="openai"  # or "gemini" based on your configuration
export OPENAI_API_KEY="your-api-key"  # Replace with your actual API key

# Ensure MongoDB is running
echo "Checking MongoDB connection..."
mongosh --eval "db.serverStatus()" > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo "Error: MongoDB is not running. Please start MongoDB first."
    exit 1
fi

# Make test scripts executable
chmod +x test/quick-document-test.sh
chmod +x test/make-tests-executable.sh

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
fi

# Build the application
echo "Building the application..."
npm run build

# Start the application
echo "Starting the API server..."
npm run start:dev

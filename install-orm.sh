#!/bin/bash

echo "Cleaning up..."
rm -rf node_modules
rm -f package-lock.json
rm -rf dist

echo "Installing TypeORM dependencies..."
npm install --save @nestjs/typeorm@10.0.1
npm install --save typeorm@0.3.20
npm install --save sqlite3@5.1.7
npm install --save @nestjs/config@3.1.1
npm install --save reflect-metadata@0.1.13
npm install --save class-transformer@0.5.1
npm install --save class-validator@0.14.0

echo "Installing remaining dependencies..."
npm install

echo "Building project..."
npm run build

echo "Installation complete!"
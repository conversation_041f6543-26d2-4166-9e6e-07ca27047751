# Feature Detection Approaches

## 1. Middleware-Based Detection (Current Implementation)

### How it works:
- Intercepts all HTTP requests
- Maps URL patterns to features
- Automatically deducts credits

### Example:
```typescript
// In CreditUsageMiddleware
if (path.includes('/documents/analyze')) {
  return 'advanced_analysis'; // 5 credits
}
if (path.includes('/chat/messages')) {
  return 'chat'; // 1 credit
}
```

### Pros:
- Automatic detection
- No code changes needed in controllers
- Centralized feature mapping

### Cons:
- URL-based detection can be imprecise
- Hard to detect complex feature combinations
- May not capture business logic nuances

## 2. Decorator-Based Detection (Recommended)

### Implementation:
```typescript
// Create a decorator for feature usage
export const UseCredits = (featureName: string, customCost?: number) => {
  return (target: any, propertyName: string, descriptor: PropertyDescriptor) => {
    const method = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const organizationId = this.tenantContext.getCurrentOrganization();
      
      // Check and deduct credits before executing
      const result = await this.creditService.deductCreditsForFeature(
        organizationId, 
        featureName
      );
      
      if (!result.success) {
        throw new BadRequestException(result.message);
      }
      
      try {
        // Execute the original method
        return await method.apply(this, args);
      } catch (error) {
        // Refund credits if operation fails
        await this.creditService.addCredits(
          organizationId,
          Math.abs(result.transaction.amount),
          'refund',
          `Refund for failed ${featureName} operation`
        );
        throw error;
      }
    };
  };
};
```

### Usage in Controllers:
```typescript
@Controller('documents')
export class DocumentsController {
  
  @Post('analyze')
  @UseCredits('advanced_analysis')
  async analyzeDocument(@Body() dto: AnalyzeDocumentDto) {
    // This method automatically deducts 5 credits
    return this.documentsService.analyzeDocument(dto);
  }
  
  @Post('compare')
  @UseCredits('enhanced_comparison')
  async compareDocuments(@Body() dto: CompareDocumentsDto) {
    // This method automatically deducts 5 credits
    return this.documentsService.compareDocuments(dto);
  }
}
```

## 3. Service-Level Detection

### Implementation:
```typescript
@Injectable()
export class DocumentsService {
  constructor(
    private creditService: CreditManagementService,
    private tenantContext: TenantContextService
  ) {}
  
  async analyzeDocument(dto: AnalyzeDocumentDto) {
    const organizationId = this.tenantContext.getCurrentOrganization();
    
    // Manually check and deduct credits
    const creditResult = await this.creditService.deductCreditsForFeature(
      organizationId,
      'advanced_analysis'
    );
    
    if (!creditResult.success) {
      throw new BadRequestException(creditResult.message);
    }
    
    try {
      // Perform the actual analysis
      const result = await this.performAnalysis(dto);
      return result;
    } catch (error) {
      // Refund credits on failure
      await this.creditService.addCredits(
        organizationId,
        5, // advanced_analysis cost
        'refund',
        'Refund for failed analysis'
      );
      throw error;
    }
  }
}
```

## 4. Event-Based Detection

### Implementation:
```typescript
// Event emitter approach
@Injectable()
export class FeatureUsageService {
  constructor(private eventEmitter: EventEmitter2) {}
  
  async trackFeatureUsage(featureName: string, organizationId: string, metadata?: any) {
    this.eventEmitter.emit('feature.used', {
      featureName,
      organizationId,
      timestamp: new Date(),
      metadata
    });
  }
}

// Event listener
@Injectable()
export class CreditEventListener {
  @OnEvent('feature.used')
  async handleFeatureUsage(payload: FeatureUsageEvent) {
    await this.creditService.deductCreditsForFeature(
      payload.organizationId,
      payload.featureName
    );
  }
}

// Usage in services
async analyzeDocument(dto: AnalyzeDocumentDto) {
  const result = await this.performAnalysis(dto);
  
  // Track usage after successful operation
  await this.featureUsageService.trackFeatureUsage(
    'advanced_analysis',
    this.tenantContext.getCurrentOrganization(),
    { documentId: dto.documentId, analysisType: dto.type }
  );
  
  return result;
}
```

## 5. Guard-Based Detection

### Implementation:
```typescript
@Injectable()
export class CreditGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private creditService: CreditManagementService,
    private tenantContext: TenantContextService
  ) {}
  
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const featureName = this.reflector.get<string>('feature', context.getHandler());
    
    if (!featureName) {
      return true; // No feature specified
    }
    
    const organizationId = this.tenantContext.getCurrentOrganization();
    const creditCheck = await this.creditService.hasCreditsForFeature(
      organizationId,
      featureName
    );
    
    if (!creditCheck.hasCredits) {
      throw new ForbiddenException(
        `Insufficient credits for ${featureName}. Required: ${creditCheck.requiredCredits}, Available: ${creditCheck.currentBalance}`
      );
    }
    
    return true;
  }
}

// Usage
@Controller('documents')
@UseGuards(CreditGuard)
export class DocumentsController {
  
  @Post('analyze')
  @SetMetadata('feature', 'advanced_analysis')
  async analyzeDocument(@Body() dto: AnalyzeDocumentDto) {
    // Credits are checked by guard, deducted by decorator or service
    return this.documentsService.analyzeDocument(dto);
  }
}
```

## 6. Interceptor-Based Detection

### Implementation:
```typescript
@Injectable()
export class CreditInterceptor implements NestInterceptor {
  constructor(
    private reflector: Reflector,
    private creditService: CreditManagementService,
    private tenantContext: TenantContextService
  ) {}
  
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const featureName = this.reflector.get<string>('feature', context.getHandler());
    
    if (!featureName) {
      return next.handle();
    }
    
    const organizationId = this.tenantContext.getCurrentOrganization();
    
    return from(this.creditService.deductCreditsForFeature(organizationId, featureName))
      .pipe(
        switchMap(creditResult => {
          if (!creditResult.success) {
            throw new BadRequestException(creditResult.message);
          }
          
          return next.handle().pipe(
            catchError(error => {
              // Refund credits on error
              this.creditService.addCredits(
                organizationId,
                Math.abs(creditResult.transaction.amount),
                'refund',
                `Refund for failed ${featureName}`
              );
              throw error;
            })
          );
        })
      );
  }
}
```

## 7. Hybrid Approach (Recommended)

### Combine multiple approaches for maximum flexibility:

```typescript
// 1. Use decorators for explicit feature marking
@UseCredits('advanced_analysis')
@Post('analyze')
async analyzeDocument() { ... }

// 2. Use middleware for automatic detection of unmarked endpoints
// 3. Use service-level calls for complex business logic
// 4. Use events for async feature tracking

@Injectable()
export class DocumentsService {
  async complexAnalysis(dto: ComplexAnalysisDto) {
    let totalCreditsUsed = 0;
    
    // Multiple feature usage in one operation
    if (dto.includeAdvancedAnalysis) {
      await this.useFeature('advanced_analysis');
      totalCreditsUsed += 5;
    }
    
    if (dto.includePrecedentAnalysis) {
      await this.useFeature('precedent_analysis');
      totalCreditsUsed += 10;
    }
    
    if (dto.includeRiskScoring) {
      await this.useFeature('contract_risk_scoring');
      totalCreditsUsed += 30;
    }
    
    try {
      return await this.performComplexAnalysis(dto);
    } catch (error) {
      // Refund all credits used in this operation
      await this.creditService.addCredits(
        this.tenantContext.getCurrentOrganization(),
        totalCreditsUsed,
        'refund',
        'Refund for failed complex analysis'
      );
      throw error;
    }
  }
  
  private async useFeature(featureName: string) {
    const result = await this.creditService.deductCreditsForFeature(
      this.tenantContext.getCurrentOrganization(),
      featureName
    );
    
    if (!result.success) {
      throw new BadRequestException(result.message);
    }
  }
}
```

## Recommendation

For your application, I recommend using a **hybrid approach**:

1. **Decorators** for simple, single-feature endpoints
2. **Service-level detection** for complex operations involving multiple features
3. **Middleware** as a fallback for unmarked endpoints
4. **Events** for analytics and audit trails

This provides the best balance of:
- Automatic detection
- Explicit control
- Flexibility for complex scenarios
- Comprehensive tracking

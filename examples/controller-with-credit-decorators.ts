import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { TenantGuard } from '../auth/guards/tenant.guard';
import { CreditUsageInterceptor } from '../subscription/interceptors/credit-usage.interceptor';
import {
  UseCredits,
  FreeFeature,
  UseMultipleCredits,
  UseCreditsPremiumOnly,
  UseCreditsConditional,
} from '../subscription/decorators/use-credits.decorator';

@ApiTags('Documents')
@Controller('documents')
@UseGuards(JwtAuthGuard, TenantGuard)
@UseInterceptors(CreditUsageInterceptor) // Apply credit interceptor to all routes
export class DocumentsController {
  constructor(private readonly documentsService: DocumentsService) {}

  // Example 1: Simple credit usage
  @Post('analyze')
  @UseCredits('advanced_analysis') // Automatically deducts 5 credits
  @ApiOperation({ summary: 'Analyze document with advanced AI' })
  async analyzeDocument(@Body() dto: AnalyzeDocumentDto) {
    return this.documentsService.analyzeDocument(dto);
  }

  // Example 2: Custom credit cost
  @Post('super-analysis')
  @UseCredits('advanced_analysis', 15) // Custom cost of 15 credits instead of default 5
  @ApiOperation({ summary: 'Super advanced document analysis' })
  async superAnalyzeDocument(@Body() dto: AnalyzeDocumentDto) {
    return this.documentsService.superAnalyzeDocument(dto);
  }

  // Example 3: Free feature (no credits required)
  @Get(':id')
  @FreeFeature() // Explicitly mark as free
  @ApiOperation({ summary: 'Get document details' })
  async getDocument(@Param('id') id: string) {
    return this.documentsService.getDocument(id);
  }

  // Example 4: Multiple features in one operation
  @Post('comprehensive-analysis')
  @UseMultipleCredits([
    { feature: 'advanced_analysis', cost: 5 },
    { feature: 'precedent_analysis', cost: 10 },
    { feature: 'contract_risk_scoring', cost: 30 },
  ])
  @ApiOperation({ summary: 'Comprehensive document analysis using multiple AI features' })
  async comprehensiveAnalysis(@Body() dto: ComprehensiveAnalysisDto) {
    return this.documentsService.comprehensiveAnalysis(dto);
  }

  // Example 5: Premium-only credits (free tier gets unlimited access)
  @Post('basic-analysis')
  @UseCreditsPremiumOnly('basic_analysis') // Free tier: unlimited, Pro/Admin: consumes credits
  @ApiOperation({ summary: 'Basic document analysis' })
  async basicAnalysis(@Body() dto: BasicAnalysisDto) {
    return this.documentsService.basicAnalysis(dto);
  }

  // Example 6: Conditional credit usage
  @Post('flexible-analysis')
  @UseCreditsConditional(
    'advanced_analysis',
    (req) => req.body.useAdvancedFeatures === true // Only charge if advanced features requested
  )
  @ApiOperation({ summary: 'Flexible analysis with optional advanced features' })
  async flexibleAnalysis(@Body() dto: FlexibleAnalysisDto) {
    return this.documentsService.flexibleAnalysis(dto);
  }

  // Example 7: Complex business logic with manual credit handling
  @Post('custom-workflow')
  @FreeFeature() // Mark as free since we handle credits manually
  @ApiOperation({ summary: 'Custom workflow with dynamic credit usage' })
  async customWorkflow(@Body() dto: CustomWorkflowDto) {
    // This method handles credits manually in the service layer
    return this.documentsService.customWorkflow(dto);
  }

  // Example 8: Batch operations with credit optimization
  @Post('batch-analyze')
  @UseCredits('bulk_upload', 10) // Fixed cost for batch regardless of number of documents
  @ApiOperation({ summary: 'Batch analyze multiple documents' })
  async batchAnalyze(@Body() dto: BatchAnalyzeDto) {
    return this.documentsService.batchAnalyze(dto);
  }
}

// Example DTOs
export class AnalyzeDocumentDto {
  documentId: string;
  analysisType: 'basic' | 'advanced' | 'comprehensive';
}

export class ComprehensiveAnalysisDto {
  documentId: string;
  includeRiskScoring: boolean;
  includePrecedentAnalysis: boolean;
  includeAdvancedAnalysis: boolean;
}

export class FlexibleAnalysisDto {
  documentId: string;
  useAdvancedFeatures: boolean; // This determines if credits are charged
  analysisDepth: 'shallow' | 'deep';
}

export class CustomWorkflowDto {
  documentIds: string[];
  steps: Array<{
    type: 'analysis' | 'comparison' | 'risk_scoring';
    parameters: any;
  }>;
}

export class BatchAnalyzeDto {
  documentIds: string[];
  analysisType: 'basic' | 'advanced';
}

// Example service with manual credit handling
@Injectable()
export class DocumentsService {
  constructor(
    private readonly creditService: CreditManagementService,
    private readonly tenantContext: TenantContextService,
  ) {}

  // Simple method - credits handled by decorator
  async analyzeDocument(dto: AnalyzeDocumentDto) {
    // Credits already deducted by interceptor
    return this.performAnalysis(dto.documentId, dto.analysisType);
  }

  // Complex method with manual credit handling
  async customWorkflow(dto: CustomWorkflowDto) {
    const organizationId = this.tenantContext.getCurrentOrganization();
    let totalCreditsUsed = 0;
    const results = [];

    try {
      for (const step of dto.steps) {
        let featureName: string;
        let creditCost: number;

        // Determine feature and cost based on step type
        switch (step.type) {
          case 'analysis':
            featureName = 'advanced_analysis';
            creditCost = 5;
            break;
          case 'comparison':
            featureName = 'enhanced_comparison';
            creditCost = 5;
            break;
          case 'risk_scoring':
            featureName = 'contract_risk_scoring';
            creditCost = 30;
            break;
          default:
            throw new Error(`Unknown step type: ${step.type}`);
        }

        // Check and deduct credits
        const creditResult = await this.creditService.deductCreditsForFeature(
          organizationId,
          featureName,
        );

        if (!creditResult.success) {
          throw new BadRequestException(creditResult.message);
        }

        totalCreditsUsed += Math.abs(creditResult.transaction.amount);

        // Perform the actual operation
        const stepResult = await this.performWorkflowStep(step, dto.documentIds);
        results.push(stepResult);
      }

      return {
        results,
        creditsUsed: totalCreditsUsed,
        success: true,
      };
    } catch (error) {
      // Refund credits if workflow fails
      if (totalCreditsUsed > 0) {
        await this.creditService.addCredits(
          organizationId,
          totalCreditsUsed,
          'refund',
          'Refund for failed custom workflow',
        );
      }
      throw error;
    }
  }

  // Method with conditional logic
  async flexibleAnalysis(dto: FlexibleAnalysisDto) {
    // Credits already handled by conditional decorator
    if (dto.useAdvancedFeatures) {
      return this.performAdvancedAnalysis(dto.documentId, dto.analysisDepth);
    } else {
      return this.performBasicAnalysis(dto.documentId);
    }
  }

  // Batch operation with optimized credit usage
  async batchAnalyze(dto: BatchAnalyzeDto) {
    // Fixed credit cost already deducted by decorator
    const results = [];
    
    for (const documentId of dto.documentIds) {
      const result = await this.performAnalysis(documentId, dto.analysisType);
      results.push(result);
    }

    return {
      results,
      totalDocuments: dto.documentIds.length,
      analysisType: dto.analysisType,
    };
  }

  // Helper methods
  private async performAnalysis(documentId: string, type: string) {
    // Actual analysis logic here
    return { documentId, type, result: 'analysis complete' };
  }

  private async performAdvancedAnalysis(documentId: string, depth: string) {
    // Advanced analysis logic here
    return { documentId, depth, result: 'advanced analysis complete' };
  }

  private async performBasicAnalysis(documentId: string) {
    // Basic analysis logic here
    return { documentId, result: 'basic analysis complete' };
  }

  private async performWorkflowStep(step: any, documentIds: string[]) {
    // Workflow step logic here
    return { step: step.type, documentIds, result: 'step complete' };
  }
}

// Global interceptor setup in app.module.ts or main.ts
// app.useGlobalInterceptors(new CreditUsageInterceptor(reflector, creditService, subscriptionService, tenantContext));

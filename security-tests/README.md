# Security Testing for Legal Document Analyzer API

This directory contains curl commands for testing the security implementations of the Legal Document Analyzer API.

## How to Use

1. Start the API server:
   ```
   npm run start:dev
   ```

2. Run the curl commands in sequence:
   ```
   chmod +x security-tests.sh
   ./security-tests.sh
   ```

3. Alternatively, copy and paste individual commands to test specific security features.

## Test Coverage

1. **JWT Authentication**
   - User registration and login
   - Protected endpoint access with valid token
   - Rejection of invalid tokens

2. **Tenant Isolation**
   - Cross-organization access prevention
   - Document isolation between organizations

3. **Rate Limiting**
   - Detection of rapid request patterns
   - Proper rate limit response headers

4. **Document Access Controls**
   - Owner-based document access
   - Document sharing functionality
   - Access control within same organization

## Important Notes

- Replace placeholder values (TOKEN, DOCUMENT_ID, etc.) with actual values from previous responses
- Examine response files to verify security behavior
- For rate limiting tests, run the command multiple times in quick succession

## Security Features Tested

- JWT token validation and expiration
- Tenant context isolation
- Subscription-based rate limiting
- Document-level access controls

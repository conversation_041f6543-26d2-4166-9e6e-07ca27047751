const axios = require('axios');

const BASE_URL = 'http://localhost:4000/api';

// Test user credentials
const testUser = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

// Use an existing document ID for testing
const EXISTING_DOCUMENT_ID = '2444bb2c-93e0-4046-acb7-adced11e87d2';

async function testAdvancedFeatures() {
  console.log('🧪 TESTING ADVANCED AI-POWERED FEATURES FOR CREDIT CONSUMPTION\n');

  try {
    // 1. Login to get JWT token
    console.log('1️⃣ Logging in...');
    let authToken;
    try {
      const loginResponse = await axios.post(`${BASE_URL}/auth/login`, testUser);
      authToken = loginResponse.data.token;
      console.log('✅ Login successful\n');
    } catch (error) {
      console.log('❌ Login failed:', error.response?.data || error.message);
      return;
    }

    const headers = {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json'
    };

    // 2. Check initial credit balance
    console.log('2️⃣ Checking initial credit balance...');
    let initialBalance;
    try {
      const balanceResponse = await axios.get(`${BASE_URL}/credits/balance`, { headers });
      initialBalance = balanceResponse.data.balance;
      console.log('💰 Initial credit balance:', initialBalance);
      console.log('');
    } catch (error) {
      console.log('❌ Failed to get credit balance:', error.response?.data || error.message);
      return;
    }

    // 3. Test Privilege Log Analysis (should consume credits)
    console.log('3️⃣ Testing Privilege Log Analysis (should consume credits)...');
    await testFeature('Privilege Log Analysis', async () => {
      return await axios.post(`${BASE_URL}/documents/${EXISTING_DOCUMENT_ID}/privilege-analysis`, {
        analysisType: 'comprehensive',
        privilegeTypes: ['attorney-client', 'work-product'],
        confidenceThreshold: 0.8
      }, { headers });
    });

    // 4. Test Compliance Audit (should consume credits)
    console.log('4️⃣ Testing Compliance Audit (should consume credits)...');
    await testFeature('Compliance Audit', async () => {
      return await axios.post(`${BASE_URL}/compliance/audit`, {
        documentId: EXISTING_DOCUMENT_ID,
        regulations: ['gdpr', 'sox'],
        auditScope: 'full',
        includeRecommendations: true
      }, { headers });
    });

    // 5. Test Deposition Question Generation (should consume credits)
    console.log('5️⃣ Testing Deposition Question Generation (should consume credits)...');
    await testFeature('Deposition Question Generation', async () => {
      return await axios.post(`${BASE_URL}/depositions/generate-questions`, {
        caseContext: 'Contract dispute involving breach of payment terms',
        keyIssues: ['payment delays', 'contract interpretation'],
        targetWitnesses: ['Contract Manager', 'Finance Director'],
        questionCount: 10,
        includeFollowUps: true,
        questionCategories: ['factual', 'legal']
      }, { headers });
    });

    // 6. Test Deposition Transcript Analysis (should consume credits)
    console.log('6️⃣ Testing Deposition Transcript Analysis (should consume credits)...');
    await testFeature('Deposition Transcript Analysis', async () => {
      return await axios.post(`${BASE_URL}/depositions/analyze-transcript`, {
        transcript: 'Q: Can you tell us about the contract terms? A: The payment was due within 30 days. Q: Was this communicated clearly? A: Yes, it was in writing.',
        analysisType: 'comprehensive',
        focusAreas: ['credibility', 'inconsistencies', 'key_admissions'],
        includeTimeline: true
      }, { headers });
    });

    // 7. Test Negotiation Simulator Session (should consume credits)
    console.log('7️⃣ Testing Negotiation Simulator Session (should consume credits)...');
    await testFeature('Negotiation Simulator Session', async () => {
      // First create a scenario (should be FREE)
      const scenarioResponse = await axios.post(`${BASE_URL}/negotiation-simulator/scenarios`, {
        name: 'Credit Test Scenario',
        description: 'Test scenario for credit consumption',
        industry: 'technology',
        contractType: 'service-agreement',
        difficulty: 'intermediate',
        objectives: ['cost reduction', 'timeline optimization'],
        constraints: ['budget limit', 'regulatory compliance'],
        stakeholders: [
          { role: 'buyer', priorities: ['cost', 'quality'] },
          { role: 'seller', priorities: ['profit', 'timeline'] }
        ]
      }, { headers });
      
      const scenarioId = scenarioResponse.data.id;
      console.log('   📝 Scenario created (FREE):', scenarioId);
      
      // Start a simulation session (should consume credits)
      return await axios.post(`${BASE_URL}/negotiation-simulator/sessions`, {
        scenarioId: scenarioId,
        userRole: 'buyer',
        difficultyLevel: 'intermediate',
        sessionName: 'Credit Test Session'
      }, { headers });
    });

    // 8. Check final credit balance
    console.log('8️⃣ Checking final credit balance...');
    try {
      const finalBalanceResponse = await axios.get(`${BASE_URL}/credits/balance`, { headers });
      const finalBalance = finalBalanceResponse.data.balance;
      const totalSpent = finalBalanceResponse.data.totalSpent;
      
      console.log('💰 Final credit balance:', finalBalance);
      console.log('💸 Total spent:', totalSpent);
      console.log('📊 Credits consumed in this test:', initialBalance - finalBalance);
      console.log('');
      
      if (totalSpent > 0) {
        console.log('✅ ADVANCED FEATURES CREDIT SYSTEM IS WORKING! Credits were consumed.');
      } else {
        console.log('❌ ADVANCED FEATURES CREDIT SYSTEM NOT WORKING! No credits were consumed.');
      }
      
    } catch (error) {
      console.log('❌ Failed to get final balance:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

async function testFeature(featureName, testFunction) {
  try {
    const response = await testFunction();
    console.log(`   ✅ ${featureName} completed`);
    console.log(`   💳 Credits used: ${response.headers['x-credits-used'] || 'None'}`);
    console.log(`   💰 Credits remaining: ${response.headers['x-credits-remaining'] || 'Unknown'}`);
    console.log(`   🎯 Feature used: ${response.headers['x-feature-used'] || 'Unknown'}`);
    console.log('');
  } catch (error) {
    console.log(`   ❌ ${featureName} failed:`, error.response?.status, error.response?.statusText);
    if (error.response?.data) {
      console.log(`   📝 Error details:`, error.response.data.message || error.response.data);
    }
    console.log('');
  }
}

// Run the test
testAdvancedFeatures().catch(console.error);

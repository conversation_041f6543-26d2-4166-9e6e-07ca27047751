# Stripe Automation Guide

This guide explains how to use the automated Stripe setup scripts to quickly configure your DocGic application for credit purchases and subscriptions.

## 🚀 Quick Start

### Step 1: Get Your Stripe Secret Key

1. Go to [Stripe Dashboard](https://dashboard.stripe.com/)
2. Navigate to **Developers** > **API Keys**
3. Copy your **Secret key** (starts with `sk_test_` for test mode)

### Step 2: Set Environment Variable

Add your Stripe secret key to your `.env` file:

```bash
# Add this to your .env file
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
```

### Step 3: Run the Setup Script

```bash
# This will create all Stripe products and prices
npm run setup:stripe
```

### Step 4: Copy Environment Variables

The script will output environment variables like this:

```bash
# Add these to your .env file:

# Subscription Plan Price IDs
STRIPE_PLAN_PRO=price_1ABC123 # Monthly
STRIPE_PLAN_PRO_YEARLY=price_1DEF456 # Yearly
STRIPE_PLAN_ADMIN=price_1GHI789 # Monthly
STRIPE_PLAN_ADMIN_YEARLY=price_1JKL012 # Yearly

# Credit Package Price IDs
STRIPE_CREDIT_STUDENT=price_1MNO345
STRIPE_CREDIT_LAWYER_SMALL=price_1PQR678
STRIPE_CREDIT_LAWYER_LARGE=price_1STU901
STRIPE_CREDIT_FIRM_STANDARD=price_1VWX234
STRIPE_CREDIT_FIRM_ENTERPRISE=price_1YZA567
```

**Copy all these variables to your `.env` file.**

### Step 5: Validate Setup

```bash
# This will verify everything is configured correctly
npm run validate:stripe
```

### Step 6: Restart Your Application

```bash
npm run start:dev
```

## 📋 What Gets Created

### Subscription Plans

| Plan | Monthly Price | Yearly Price | Features |
|------|---------------|--------------|----------|
| **Pro** | $29.99 | $299.99 | Individual lawyers, 500 monthly credits |
| **Admin** | $99.99 | $999.99 | Law firms, 2000 monthly credits, team features |

### Credit Packages

| Package | Credits | Bonus | Total | Price | Target |
|---------|---------|-------|-------|-------|--------|
| **Student** | 50 | 0 | 50 | $4.99 | Law students |
| **Lawyer Small** | 200 | 20 | 220 | $19.99 | Individual lawyers |
| **Lawyer Large** | 500 | 75 | 575 | $44.99 | Busy practices |
| **Firm Standard** | 1000 | 200 | 1200 | $79.99 | Growing teams |
| **Firm Enterprise** | 5000 | 1500 | 6500 | $349.99 | Large organizations |

## 🛠️ Available Scripts

### `npm run setup:stripe`
- Creates all Stripe products and prices
- Outputs environment variables to copy
- Safe to run multiple times (will show errors for duplicates)

### `npm run validate:stripe`
- Checks all environment variables are set
- Validates each price ID with Stripe API
- Provides detailed error messages
- Suggests next steps

## 🔧 Manual Setup (Alternative)

If you prefer to set up Stripe manually:

1. **Create Products** in Stripe Dashboard
2. **Create Prices** for each product
3. **Copy Price IDs** to environment variables
4. **Run validation** with `npm run validate:stripe`

## 🧪 Testing

### Test Credit Purchase Flow

1. **Start your application**:
   ```bash
   npm run start:dev
   ```

2. **Make a purchase request**:
   ```bash
   curl -X POST http://localhost:3000/api/credits/purchase \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"package": "lawyer_small"}'
   ```

3. **Complete checkout** in the returned Stripe URL

4. **Verify credits** were added to your account

### Test Subscription Flow

1. **Create subscription checkout**:
   ```bash
   curl -X POST http://localhost:3000/api/subscriptions/checkout \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"organizationId": "your-org-id", "tier": "lawyer"}'
   ```

2. **Complete subscription** in Stripe

3. **Verify subscription** is active

## 🔍 Troubleshooting

### Common Issues

**❌ "STRIPE_SECRET_KEY not found"**
- Solution: Add `STRIPE_SECRET_KEY=sk_test_...` to your `.env` file

**❌ "Product with name already exists"**
- Solution: Delete existing products in Stripe Dashboard or use different names

**❌ "Invalid price ID"**
- Solution: Run `npm run validate:stripe` to check all price IDs

**❌ "Webhook secret missing"**
- Solution: Set up webhook endpoint and add `STRIPE_WEBHOOK_SECRET` to `.env`

### Getting Help

1. **Check script output** for specific error messages
2. **Run validation script** to identify issues
3. **Check Stripe Dashboard** for product/price status
4. **Review logs** in your application

## 🔐 Security Best Practices

- ✅ Use **test keys** for development
- ✅ Use **live keys** only in production
- ✅ Never commit keys to version control
- ✅ Rotate keys regularly
- ✅ Set up proper webhook endpoints
- ✅ Validate webhook signatures

## 📚 Additional Resources

- [Stripe Documentation](https://stripe.com/docs)
- [Stripe Test Cards](https://stripe.com/docs/testing#cards)
- [Webhook Testing](https://stripe.com/docs/webhooks/test)
- [API Reference](https://stripe.com/docs/api)

## 🎯 Next Steps

After successful setup:

1. **Configure webhook endpoint** in production
2. **Test payment flows** thoroughly
3. **Set up monitoring** for failed payments
4. **Implement error handling** for edge cases
5. **Add analytics** for purchase tracking

---

**Need help?** Check the troubleshooting section or review the detailed logs from the validation script.

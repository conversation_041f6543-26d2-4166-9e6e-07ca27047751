/**
 * Enum for document types supported by the analyzer
 * Comprehensive list of legal document types commonly used in legal practice
 */
export enum DocumentType {
  // General Categories
  CONTRACT = 'CONTRACT',
  AGREEMENT = 'AGREEMENT',
  LEGAL_MEMO = 'LEGAL_MEMO',
  POLICY = 'POLICY',
  TERMS_OF_SERVICE = 'TERMS_OF_SERVICE',
  PRIVACY_POLICY = 'PRIVACY_POLICY',
  NDA = 'NDA',
  GENERAL = 'GENERAL',
  LEGAL_OPINION = 'LEGAL_OPINION',
  LEGISLATION = 'LEGISLATION',
  COURT_FILING = 'COURT_FILING',
  
  // Regulatory Documents
  REGULATION = 'REGULATION',
  ADMINISTRATIVE_DECISION = 'ADMINISTRATIVE_DECISION',
  REGULATORY_FILING = 'REGULATORY_FILING',
  COMPLIANCE_DOCUMENT = 'COMPLIANCE_DOCUMENT',
  
  // Transactional Documents
  PURCHASE_AGREEMENT = 'PURCHASE_AGREEMENT',
  LEASE_AGREEMENT = 'LEASE_AGREEMENT',
  EMPLOYMENT_CONTRACT = 'EMPLOYMENT_CONTRACT',
  LICENSING_AGREEMENT = 'LICENSING_AGREEMENT',
  SERVICE_AGREEMENT = 'SERVICE_AGREEMENT',
  DISTRIBUTION_AGREEMENT = 'DISTRIBUTION_AGREEMENT',
  SETTLEMENT_AGREEMENT = 'SETTLEMENT_AGREEMENT',
  CONSULTING_AGREEMENT = 'CONSULTING_AGREEMENT',
  VENDOR_AGREEMENT = 'VENDOR_AGREEMENT',
  PARTNERSHIP_AGREEMENT = 'PARTNERSHIP_AGREEMENT',
  MASTER_SERVICE_AGREEMENT = 'MASTER_SERVICE_AGREEMENT',
  STATEMENT_OF_WORK = 'STATEMENT_OF_WORK',
  SUBSCRIPTION_AGREEMENT = 'SUBSCRIPTION_AGREEMENT',
  RESELLER_AGREEMENT = 'RESELLER_AGREEMENT',
  
  // Litigation Documents
  PLEADING = 'PLEADING',
  BRIEF = 'BRIEF',
  AFFIDAVIT = 'AFFIDAVIT',
  DEPOSITION = 'DEPOSITION',
  DISCOVERY_DOCUMENT = 'DISCOVERY_DOCUMENT',
  JUDGMENT = 'JUDGMENT',
  COMPLAINT = 'COMPLAINT',
  ANSWER = 'ANSWER',
  MOTION = 'MOTION',
  
  // Corporate Documents
  BYLAWS = 'BYLAWS',
  ARTICLES_OF_INCORPORATION = 'ARTICLES_OF_INCORPORATION',
  BOARD_RESOLUTION = 'BOARD_RESOLUTION',
  SHAREHOLDER_AGREEMENT = 'SHAREHOLDER_AGREEMENT',
  OPERATING_AGREEMENT = 'OPERATING_AGREEMENT',
  ANNUAL_REPORT = 'ANNUAL_REPORT',
  
  // Estate Planning Documents
  WILL = 'WILL',
  TRUST = 'TRUST',
  POWER_OF_ATTORNEY = 'POWER_OF_ATTORNEY',
  LIVING_WILL = 'LIVING_WILL',
  ESTATE_PLAN = 'ESTATE_PLAN',
  
  // Intellectual Property Documents
  PATENT = 'PATENT',
  TRADEMARK_REGISTRATION = 'TRADEMARK_REGISTRATION',
  COPYRIGHT_REGISTRATION = 'COPYRIGHT_REGISTRATION',
  IP_ASSIGNMENT = 'IP_ASSIGNMENT',
  
  // Real Estate Documents
  DEED = 'DEED',
  MORTGAGE = 'MORTGAGE',
  TITLE_DOCUMENT = 'TITLE_DOCUMENT',
  PROPERTY_DISCLOSURE = 'PROPERTY_DISCLOSURE',
  
  // International Documents
  TREATY = 'TREATY',
  INTERNATIONAL_AGREEMENT = 'INTERNATIONAL_AGREEMENT',
  
  // Financial Documents
  SECURITIES_FILING = 'SECURITIES_FILING',
  PROSPECTUS = 'PROSPECTUS',
  LOAN_AGREEMENT = 'LOAN_AGREEMENT',
  
  // Government Documents
  STATUTE = 'STATUTE',
  EXECUTIVE_ORDER = 'EXECUTIVE_ORDER',
  AGENCY_RULE = 'AGENCY_RULE'
}

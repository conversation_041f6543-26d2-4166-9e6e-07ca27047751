import { ArgumentsHost, Catch, ExceptionFilter, HttpException, HttpStatus, Logger, BadRequestException } from '@nestjs/common';
import { Request, Response } from 'express';
import { MongoError } from 'mongodb';
import { Error as MongooseError } from 'mongoose';
import { ValidationError } from 'class-validator';

/**
 * Global exception filter to handle all unhandled exceptions
 * Provides standardized error responses for any type of error
 */
@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    let error = 'Internal Server Error';
    let details = null;

    // Handle HTTP exceptions
    if (exception instanceof HttpException) {
      status = exception.getStatus();
      const errorResponse = exception.getResponse() as any;
      
      // Special handling for validation errors
      if (exception instanceof BadRequestException && errorResponse.message === 'Validation failed') {
        status = HttpStatus.BAD_REQUEST;
        message = 'Validation failed';
        error = 'Bad Request';
        
        // Handle both array and object format of validation errors
        if (Array.isArray(errorResponse.details)) {
          details = { validationErrors: errorResponse.details };
        } else {
          details = errorResponse.details || null;
        }
      } else {
        message = errorResponse.message || exception.message;
        error = errorResponse.error || HttpStatus[status];
        details = errorResponse.details || null;
      }
    } 
    // Handle MongoDB errors
    else if (exception instanceof MongoError) {
      message = this.handleMongoError(exception);
      details = { code: exception.code, errmsg: exception.errmsg };
    }
    // Handle Mongoose validation errors
    else if (exception instanceof MongooseError.ValidationError) {
      status = HttpStatus.BAD_REQUEST;
      message = 'Validation error';
      error = 'Bad Request';
      details = this.formatMongooseValidationError(exception);
    }
    // Handle class-validator ValidationError arrays directly
    else if (Array.isArray(exception) && exception.length > 0 && exception[0] instanceof ValidationError) {
      status = HttpStatus.BAD_REQUEST;
      message = 'Validation error';
      error = 'Bad Request';
      details = this.formatValidationErrors(exception as ValidationError[]);
    }
    // Handle other errors
    else if (exception instanceof Error) {
      message = exception.message;
      details = { stack: exception.stack };
    }

    const formattedResponse = {
      statusCode: status,
      message,
      error,
      path: request.url,
      timestamp: new Date().toISOString(),
      ...(details && { details }),
    };

    // Log error with appropriate severity
    if (status >= 500) {
      this.logger.error(
        `${request.method} ${request.url} ${status}`,
        exception instanceof Error ? exception.stack : null,
        'AllExceptionsFilter',
      );
    } else {
      this.logger.warn(
        `${request.method} ${request.url} ${status}: ${JSON.stringify(formattedResponse)}`,
        'AllExceptionsFilter',
      );
    }

    response.status(status).json(formattedResponse);
  }

  /**
   * Format class-validator ValidationError[] into a user-friendly structure
   */
  private formatValidationErrors(errors: ValidationError[]): Record<string, string[]> {
    const formattedErrors: Record<string, string[]> = {};
    
    this.flattenValidationErrors(errors, '', formattedErrors);
    
    return formattedErrors;
  }
  
  /**
   * Recursively flatten nested validation errors
   */
  private flattenValidationErrors(
    errors: ValidationError[], 
    parentProperty: string, 
    result: Record<string, string[]>
  ): void {
    errors.forEach(error => {
      const property = parentProperty 
        ? `${parentProperty}.${error.property}` 
        : error.property;
      
      if (error.constraints) {
        if (!result[property]) {
          result[property] = [];
        }
        
        result[property].push(...Object.values(error.constraints));
      }
      
      if (error.children && error.children.length) {
        this.flattenValidationErrors(error.children, property, result);
      }
    });
  }

  /**
   * Formats Mongoose validation errors into a more frontend-friendly structure
   */
  private formatMongooseValidationError(error: MongooseError.ValidationError): Record<string, string[]> {
    const formattedErrors: Record<string, string[]> = {};
    
    for (const field in error.errors) {
      const validationError = error.errors[field];
      if (!formattedErrors[field]) {
        formattedErrors[field] = [];
      }
      formattedErrors[field].push(validationError.message);
    }
    
    return formattedErrors;
  }

  /**
   * Handles specific MongoDB error codes and provides user-friendly messages
   */
  private handleMongoError(error: MongoError): string {
    switch (error.code) {
      case 11000: // Duplicate key error
        return 'A duplicate entry was found. Please check your inputs and try again.';
      case 121: // Document validation error
        return 'The document failed validation. Please check your data and try again.';
      default:
        return `Database error: ${error.message}`;
    }
  }
}

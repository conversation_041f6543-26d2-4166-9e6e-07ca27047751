import { ExceptionFilter, Catch, ArgumentsHost, HttpException, Logger } from '@nestjs/common';
import { Request, Response } from 'express';

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(HttpExceptionFilter.name);

  catch(exception: HttpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const status = exception.getStatus();
    const exceptionResponse = exception.getResponse() as any;

    const errorResponse = {
      statusCode: status,
      message: exceptionResponse.message || exception.message,
      error: exceptionResponse.error || 'Bad Request',
      details: exceptionResponse.details || null,
      path: request.url,
      timestamp: new Date().toISOString()
    };

    this.logger.warn(`${request.method} ${request.url} ${status}: ${JSON.stringify(errorResponse)}`);
    this.logger.warn('HttpExceptionFilter');

    response
      .status(status)
      .json(errorResponse);
  }
}

import { ClassConstructor, plainToInstance } from 'class-transformer';
import { validateSync, ValidationError } from 'class-validator';
import { BadRequestException } from '@nestjs/common';

/**
 * Base DTO class with common validation functionality
 * All DTOs should extend this class to benefit from its validation methods
 */
export abstract class BaseDto {
  /**
   * Validate this DTO instance
   * @throws BadRequestException if validation fails
   */
  validate(): void {
    const errors = validateSync(this, {
      whitelist: true,
      forbidNonWhitelisted: true,
    });

    if (errors.length > 0) {
      throw new BadRequestException({
        message: this.formatValidationErrors(errors),
        error: 'Validation Error',
      });
    }
  }

  /**
   * Parse and validate incoming data to create a DTO instance
   * @param data Data to parse
   * @param dtoClass DTO class to instantiate
   * @returns Instantiated and validated DTO
   * @throws BadRequestException if validation fails
   */
  static parse<T extends BaseDto>(
    data: Record<string, any>,
    dtoClass: ClassConstructor<T>,
  ): T {
    const instance = plainToInstance(dtoClass, data);
    instance.validate();
    return instance;
  }

  /**
   * Format validation errors into a user-friendly array of messages
   */
  private formatValidationErrors(errors: ValidationError[]): string[] {
    const result: string[] = [];
    errors.forEach(error => {
      if (error.constraints) {
        Object.values(error.constraints).forEach(constraint => {
          result.push(constraint);
        });
      }
      
      if (error.children && error.children.length > 0) {
        result.push(...this.formatValidationErrors(error.children));
      }
    });
    return result;
  }
}

import { IsInt, IsOptional, <PERSON>, <PERSON>, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { BaseDto } from './base.dto';

/**
 * Supported sort directions
 */
export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc',
}

/**
 * Base pagination DTO for standardized pagination parameters
 */
export class PaginationDto extends BaseDto {
  /**
   * Page number (1-based indexing)
   * @example 1
   */
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Page must be an integer' })
  @Min(1, { message: 'Page must be at least 1' })
  page: number = 1;

  /**
   * Number of items per page
   * @example 10
   */
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Limit must be an integer' })
  @Min(1, { message: 'Limit must be at least 1' })
  @Max(100, { message: 'Limit cannot exceed 100' })
  limit: number = 10;

  /**
   * Sort direction (asc or desc)
   * @example "desc"
   */
  @IsOptional()
  @IsEnum(SortDirection, { message: 'Sort direction must be either "asc" or "desc"' })
  sort: SortDirection = SortDirection.DESC;
}

/**
 * Standard format for paginated response data
 */
export class PaginatedResponseDto<T> {
  /**
   * Array of items for the current page
   */
  items: T[];
  
  /**
   * Pagination metadata
   */
  meta: {
    /**
     * Total number of items across all pages
     */
    totalItems: number;
    
    /**
     * Number of items per page
     */
    itemsPerPage: number;
    
    /**
     * Current page number
     */
    currentPage: number;
    
    /**
     * Total number of pages
     */
    totalPages: number;
    
    /**
     * Whether there is a previous page
     */
    hasPreviousPage: boolean;
    
    /**
     * Whether there is a next page
     */
    hasNextPage: boolean;
  };
}

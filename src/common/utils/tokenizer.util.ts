/**
 * Utility functions for tokenizing text and estimating token counts
 * for context management and optimization
 */

/**
 * Estimates the number of tokens in a text string
 * 
 * This is an approximation based on the average ratio of tokens to characters
 * for English text. For more accurate token counting, you should use the
 * actual tokenizer from your LLM provider.
 * 
 * GPT models typically use ~4 characters per token on average for English text.
 * 
 * @param text The text to tokenize
 * @returns Estimated token count
 */
export function tokenizeText(text: string): number {
  if (!text) {
    return 0;
  }
  
  // Character to token ratio (approximation)
  const CHARS_PER_TOKEN = 4;
  
  // Calculate token count based on character count
  return Math.ceil(text.length / CHARS_PER_TOKEN);
}

/**
 * Truncates text to fit within a token limit
 * 
 * @param text Text to truncate
 * @param maxTokens Maximum allowed tokens
 * @param addEllipsis Whether to add "..." at truncation point
 * @returns Truncated text
 */
export function truncateToTokenLimit(
  text: string,
  maxTokens: number,
  addEllipsis: boolean = true
): string {
  if (!text) {
    return '';
  }
  
  const currentTokens = tokenizeText(text);
  
  if (currentTokens <= maxTokens) {
    return text;
  }
  
  // Calculate approximate character limit
  const CHARS_PER_TOKEN = 4;
  const ellipsisTokens = addEllipsis ? 1 : 0;
  const maxChars = (maxTokens - ellipsisTokens) * CHARS_PER_TOKEN;
  
  // Truncate text
  const truncated = text.substring(0, maxChars);
  
  return addEllipsis ? truncated + '...' : truncated;
}

/**
 * Compresses text to reduce token usage while preserving meaning
 * 
 * @param text Text to compress
 * @returns Compressed text
 */
export function compressText(text: string): string {
  if (!text) {
    return '';
  }
  
  // Basic compression techniques:
  
  // 1. Remove extra whitespace
  let compressed = text.replace(/\s+/g, ' ').trim();
  
  // 2. Remove redundant punctuation
  compressed = compressed.replace(/[.]{2,}/g, '...');
  compressed = compressed.replace(/[!]{2,}/g, '!');
  compressed = compressed.replace(/[?]{2,}/g, '?');
  
  // 3. Remove some common filler phrases
  const fillerPhrases = [
    'in order to',
    'due to the fact that',
    'for the purpose of',
    'in the event that',
    'in light of the fact that',
    'with reference to',
    'with regard to',
    'with respect to',
    'despite the fact that',
    'in relation to',
    'in the process of',
    'it should be noted that',
    'it is important to note that',
    'it is worth noting that',
    'as a matter of fact',
  ];
  
  const replacements = [
    'to',
    'because',
    'for',
    'if',
    'because',
    'regarding',
    'regarding',
    'regarding',
    'although',
    'about',
    'during',
    '',
    'note:',
    'note:',
    'in fact',
  ];
  
  for (let i = 0; i < fillerPhrases.length; i++) {
    compressed = compressed.replace(
      new RegExp(fillerPhrases[i], 'gi'),
      replacements[i]
    );
  }
  
  return compressed;
}

/**
 * Counts the tokens in each element of a messages array and returns
 * the total for context window management
 * 
 * @param messages Array of message objects with content
 * @returns Total token count
 */
export function countMessagesTokens(messages: Array<{ content: string }>): number {
  if (!messages || messages.length === 0) {
    return 0;
  }
  
  let totalTokens = 0;
  
  for (const message of messages) {
    totalTokens += tokenizeText(message.content);
    
    // Add tokens for message overhead (4 tokens per message)
    totalTokens += 4;
  }
  
  return totalTokens;
}

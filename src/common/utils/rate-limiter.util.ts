import { Injectable, Logger } from '@nestjs/common';

export class RateLimitExceededError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'RateLimitExceededError';
  }
}

export interface RateLimiterConfig {
  /**
   * Maximum number of requests allowed in the time window
   */
  maxRequests: number;

  /**
   * Time window in milliseconds
   */
  windowMs: number;

  /**
   * Whether to throw an error when rate limit is reached
   * If false, the request will be delayed until a token is available
   */
  throwOnLimit: boolean;

  /**
   * Maximum number of requests that can be queued when rate limit is reached
   * Only applicable when throwOnLimit is false
   */
  maxQueueSize?: number;
}

export interface RateLimiterInfo {
  utilizationPercentage: number;
  remainingTokens: number;
  maxTokens: number;
}

/**
 * A token bucket based rate limiter utility
 * This implements a sliding window rate limiting strategy
 */
@Injectable()
export class RateLimiter {
  private readonly logger = new Logger(RateLimiter.name);
  private readonly buckets: Map<string, number[]> = new Map();
  private isRefilling = false;
  private readonly requestQueue: Array<() => boolean> = [];
  private maxRequests: number;
  private windowMs: number;
  private throwOnLimit: boolean;
  private maxQueueSize: number;
  private readonly name: string;

  constructor(config: RateLimiterConfig, name: string = 'default') {
    this.maxRequests = config.maxRequests;
    this.windowMs = config.windowMs;
    this.throwOnLimit = config.throwOnLimit;
    this.maxQueueSize = config.maxQueueSize || 100;
    this.name = name;
    
    this.logger.log(
      `Created rate limiter "${name}" with ${config.maxRequests} requests per ${
        config.windowMs / 1000
      }s`
    );
  }

  /**
   * Update the rate limiter configuration
   */
  updateConfig(config: Partial<RateLimiterConfig>): void {
    if (config.maxRequests !== undefined) {
      this.maxRequests = config.maxRequests;
    }
    if (config.windowMs !== undefined) {
      this.windowMs = config.windowMs;
    }
    if (config.throwOnLimit !== undefined) {
      this.throwOnLimit = config.throwOnLimit;
    }
    if (config.maxQueueSize !== undefined) {
      this.maxQueueSize = config.maxQueueSize;
    }
  }

  /**
   * Try to acquire a token for the given key
   * @param key Unique identifier for the client
   * @returns true if a token was acquired, false otherwise
   */
  acquire(key: string): boolean {
    this.cleanupExpiredTokens(key);
    
    if (!this.buckets.has(key)) {
      this.buckets.set(key, []);
    }
    
    const timestamps = this.buckets.get(key);
    
    // If we haven't reached the limit, add a new timestamp
    if (timestamps.length < this.maxRequests) {
      timestamps.push(Date.now());
      return true;
    }
    
    // We've reached the limit
    if (this.throwOnLimit) {
      return false;
    }
    
    // Queue the request if we're not throwing
    return this.queueRequest(key);
  }

  /**
   * Executes an operation with rate limiting
   * @param operation The operation to execute
   * @param operationName Name of the operation for logging purposes (optional)
   * @returns The result of the operation
   */
  async executeWithRateLimit<T>(
    operation: () => Promise<T>,
    operationName: string = 'api_call'
  ): Promise<T> {
    const key = `${this.name}:${operationName}`;
    this.cleanupExpiredTokens(key);
    
    if (!this.buckets.has(key)) {
      this.buckets.set(key, []);
    }
    
    const timestamps = this.buckets.get(key);
    
    // If we haven't reached the limit, add a new timestamp and execute
    if (timestamps.length < this.maxRequests) {
      timestamps.push(Date.now());
      return operation();
    }
    
    // We're at the limit
    if (this.throwOnLimit) {
      const error = new RateLimitExceededError(
        `Rate limit reached for "${this.name}". Operation: ${operationName}. ` +
        `Maximum ${this.maxRequests} requests per ${this.windowMs / 1000}s.`
      );
      this.logger.warn(error.message);
      throw error;
    }
    
    // Wait for next available slot
    const oldestTimestamp = timestamps[0];
    const waitTimeMs = oldestTimestamp + this.windowMs - Date.now() + 10; // Add 10ms buffer
    
    this.logger.warn(
      `Rate limited for "${this.name}". Operation: ${operationName}. ` +
      `Waiting ${waitTimeMs}ms for next available slot.`
    );
    
    await this.sleep(waitTimeMs);
    timestamps.shift(); // Remove oldest
    timestamps.push(Date.now()); // Add new
    
    return operation();
  }

  /**
   * Get utilization information for a specific key
   */
  getUtilization(key: string): RateLimiterInfo {
    this.cleanupExpiredTokens(key);
    
    const timestamps = this.buckets.get(key) || [];
    const utilizationPercentage = (timestamps.length / this.maxRequests) * 100;
    const remainingTokens = Math.max(0, this.maxRequests - timestamps.length);
    
    return {
      utilizationPercentage,
      remainingTokens,
      maxTokens: this.maxRequests
    };
  }

  /**
   * Gets the current rate limiter status information
   * @returns A RateLimiterInfo object
   */
  getInfo(): RateLimiterInfo {
    const key = `${this.name}:info`;
    return this.getUtilization(key);
  }

  /**
   * Clean up expired tokens for a key
   */
  private cleanupExpiredTokens(key: string): void {
    if (!this.buckets.has(key)) {
      return;
    }
    
    const now = Date.now();
    const timestamps = this.buckets.get(key);
    const validTimestamps = timestamps.filter(ts => now - ts < this.windowMs);
    
    this.buckets.set(key, validTimestamps);
  }

  /**
   * Queue a request to be processed when a token becomes available
   */
  private queueRequest(key: string): boolean {
    if (this.requestQueue.length >= this.maxQueueSize) {
      return false;
    }
    
    // For synchronous API, we can't actually queue in this implementation
    // Just return false to indicate rate limit exceeded
    return false;
  }

  /**
   * Process the request queue
   */
  private processQueue(): void {
    if (this.isRefilling || this.requestQueue.length === 0) {
      return;
    }
    
    this.isRefilling = true;
    
    const processNextItem = () => {
      if (this.requestQueue.length === 0) {
        this.isRefilling = false;
        return;
      }
      
      const queueItem = this.requestQueue[0];
      const result = queueItem();
      
      if (result === true) {
        this.requestQueue.shift();
      }
      
      setTimeout(processNextItem, 100);
    };
    
    processNextItem();
  }

  /**
   * Utility method to sleep for a specified time
   * @param ms Milliseconds to sleep
   * @returns A promise that resolves after the specified time
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, Math.max(0, ms)));
  }
}

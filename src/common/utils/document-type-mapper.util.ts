import { DocumentType } from '../enums/document-type.enum';

/**
 * Maps legacy contract types to standard DocumentType enum values
 */
export class DocumentTypeMapper {
  private static legacyContractTypeMap = {
    'msa': DocumentType.SERVICE_AGREEMENT,
    'nda': DocumentType.NDA,
    'sow': DocumentType.SERVICE_AGREEMENT,
    'employment': DocumentType.EMPLOYMENT_CONTRACT,
    'vendor': DocumentType.SERVICE_AGREEMENT,
    'licensing': DocumentType.LICENSING_AGREEMENT,
    'partnership': DocumentType.AGREEMENT,
    'service_agreement': DocumentType.SERVICE_AGREEMENT,
    'custom': DocumentType.CONTRACT
  };

  /**
   * Convert a legacy contract type to the standard DocumentType
   */
  static toDocumentType(legacyType: string): DocumentType {
    const mapped = this.legacyContractTypeMap[legacyType?.toLowerCase()];
    return mapped || DocumentType.CONTRACT;
  }

  /**
   * Get all valid legacy contract type values
   */
  static getLegacyContractTypes(): string[] {
    return Object.keys(this.legacyContractTypeMap);
  }

  /**
   * Check if a given contract type is valid
   */
  static isValidContractType(type: string): boolean {
    return type?.toLowerCase() in this.legacyContractTypeMap;
  }
}
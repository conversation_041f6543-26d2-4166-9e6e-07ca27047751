import * as fs from 'fs';
import * as path from 'path';
import { Logger } from '@nestjs/common';

const logger = new Logger('FileUtils');

/**
 * Ensures that the specified directory exists, creating it if it doesn't
 * @param directoryPath The directory path to ensure exists
 */
export function ensureDirectoryExists(directoryPath: string): void {
  try {
    if (!fs.existsSync(directoryPath)) {
      fs.mkdirSync(directoryPath, { recursive: true });
      logger.log(`Created directory: ${directoryPath}`);
    }
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorStack = error instanceof Error ? error.stack : undefined;
    
    logger.error(
      `Failed to create directory ${directoryPath}: ${errorMessage}`,
      errorStack,
    );
    throw new Error(`Failed to create directory: ${errorMessage}`);
  }
}

/**
 * Safely reads a file, returning null if the file doesn't exist
 * @param filePath The path to the file to read
 * @returns The file contents as a string, or null if the file doesn't exist
 */
export function safeReadFileSync(filePath: string): string | null {
  try {
    return fs.readFileSync(filePath, 'utf-8');
  } catch (error: unknown) {
    // Check if error is a NodeJS.ErrnoException which has a code property
    if (error instanceof Error && 'code' in error && error.code === 'ENOENT') {
      return null;
    }
    
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new Error(`Failed to read file: ${errorMessage}`);
  }
}

/**
 * Creates a unique filename based on the original filename
 * @param originalFilename The original filename
 * @returns A unique filename
 */
export function createUniqueFilename(originalFilename: string): string {
  const ext = path.extname(originalFilename);
  const baseName = path.basename(originalFilename, ext);
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 8);
  
  return `${baseName}-${timestamp}-${randomString}${ext}`;
}

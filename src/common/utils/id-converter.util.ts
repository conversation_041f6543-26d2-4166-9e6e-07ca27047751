import { Types } from 'mongoose';
import * as crypto from 'crypto';

/**
 * Converts a UUID string to a MongoDB ObjectId.
 * This ensures consistent ObjectIds are generated for the same UUID.
 * @param uuid The UUID to convert
 * @returns A MongoDB ObjectId
 */
export function uuidToObjectId(uuid: string): Types.ObjectId {
    // Remove hyphens and take first 24 characters of UUID
    const hex = uuid.replace(/-/g, '').slice(0, 24);
    return new Types.ObjectId(hex);
}
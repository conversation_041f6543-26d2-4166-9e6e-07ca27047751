import { Logger } from '@nestjs/common';

export interface RetryConfig {
  maxRetries: number;
  initialDelayMs: number;
  maxDelayMs: number;
  backoffFactor: number;
}

export class RetryableError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'RetryableError';
  }
}

export class NonRetryableError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'NonRetryableError';
  }
}

export class RetryUtil {
  private readonly logger = new Logger(RetryUtil.name);

  constructor(private readonly config: RetryConfig) {}

  async withRetry<T>(
    operation: () => Promise<T>,
    operationName: string,
  ): Promise<T> {
    let lastError: Error = new Error('Operation not yet executed');
    let delay = this.config.initialDelayMs;

    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error: unknown) {
        lastError = this.normalizeError(error);

        if (this.isNonRetryableError(lastError)) {
          this.logger.error(
            `Non-retryable error encountered for ${operationName}: ${lastError.message}`,
          );
          throw lastError;
        }

        if (attempt === this.config.maxRetries) {
          const finalError = new Error(
            `${operationName} failed after ${this.config.maxRetries} attempts: ${lastError.message}`,
          );
          this.logger.error(finalError.message);
          throw finalError;
        }

        this.logger.warn(
          `${operationName} attempt ${attempt} failed: ${lastError.message}. Retrying in ${delay}ms...`,
        );

        await this.sleep(delay);
        delay = Math.min(
          delay * this.config.backoffFactor,
          this.config.maxDelayMs,
        );
      }
    }

    throw lastError;
  }

  private isNonRetryableError(error: Error): boolean {
    const nonRetryableMessages = [
      'API key',
      'unauthorized',
      'invalid request',
      'permission denied',
    ];

    return nonRetryableMessages.some((msg) =>
      error.message.toLowerCase().includes(msg.toLowerCase()),
    );
  }

  private normalizeError(error: unknown): Error {
    if (error instanceof Error) {
      return error;
    }
    return new Error(
      typeof error === 'string' ? error : 'Unknown error occurred',
    );
  }

  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}

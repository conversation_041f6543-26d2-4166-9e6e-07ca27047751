import { Injectable, NestInterceptor, ExecutionContext, CallHandler, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { Observable } from 'rxjs';
import { ConfigService } from '@nestjs/config';
import { RateLimiter, RateLimiterConfig } from '../utils/rate-limiter.util';
import { TenantContextService } from '../../modules/auth/services/tenant-context.service';
import { SubscriptionService } from '../../modules/subscription/services/subscription.service';
import { SubscriptionTier } from '../../modules/subscription/enums/subscription-tier.enum';

@Injectable()
export class RateLimitInterceptor implements NestInterceptor {
  private readonly logger = new Logger(RateLimitInterceptor.name);
  private readonly rateLimiters: Map<string, RateLimiter> = new Map();
  private readonly enabled: boolean;
  private readonly ipWhitelist: string[];

  constructor(
    private configService: ConfigService,
    private tenantContext: TenantContextService,
    private subscriptionService: SubscriptionService,
  ) {
    this.enabled = this.configService.get<boolean>('rateLimit.enabled', false);
    this.ipWhitelist = this.configService.get<string[]>('rateLimit.ipWhitelist', []);
    
    this.logger.log(`Rate limiting ${this.enabled ? 'enabled' : 'disabled'}`);
    if (this.ipWhitelist.length > 0) {
      this.logger.log(`IP whitelist configured with ${this.ipWhitelist.length} entries`);
    }
  }

  private getRateLimiter(key: string, endpoint: string): RateLimiter {
    if (!this.rateLimiters.has(key)) {
      // Default configuration
      let config: RateLimiterConfig = {
        maxRequests: this.configService.get<number>('rateLimit.defaultLimits.api.FREE.maxRequests', 30),
        windowMs: this.configService.get<number>('rateLimit.defaultLimits.api.FREE.windowMs', 60000),
        throwOnLimit: this.configService.get<boolean>('rateLimit.throwOnLimit', true),
        maxQueueSize: this.configService.get<number>('rateLimit.maxQueueSize', 100),
      };

      this.rateLimiters.set(key, new RateLimiter(config));
    }

    return this.rateLimiters.get(key);
  }

  async intercept(context: ExecutionContext, next: CallHandler): Promise<Observable<any>> {
    if (!this.enabled) {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest();
    const ip = request.ip || request.connection.remoteAddress;
    
    // Skip rate limiting for whitelisted IPs
    if (this.ipWhitelist.includes(ip)) {
      return next.handle();
    }
    
    const endpoint = request.route?.path || 'unknown';
    const method = request.method;
    const organizationId = this.tenantContext.getCurrentOrganization();
    
    // Determine which rate limit category to use
    let category = 'api';
    if (endpoint.includes('/auth')) {
      category = 'auth';
    } else if (endpoint.includes('/ai') || endpoint.includes('/chat')) {
      category = 'ai';
    } else if (endpoint.includes('/upload') || endpoint.includes('/documents')) {
      category = 'upload';
    }
    
    // Get subscription tier for organization if available
    let tier = SubscriptionTier.LAW_STUDENT;
    if (organizationId) {
      try {
        const subscription = await this.subscriptionService.getSubscriptionByOrganizationId(organizationId);
        if (subscription) {
          tier = subscription.tier;
        }
      } catch (error) {
        this.logger.warn(`Failed to get subscription tier: ${error.message}`);
      }
    }
    
    // Get appropriate rate limit config based on endpoint and tier
    const configPath = organizationId 
      ? `rateLimit.defaultLimits.${category}.${tier}`
      : `rateLimit.defaultLimits.auth.${endpoint.includes('login') ? 'login' : 'register'}`;
    
    const maxRequests = this.configService.get<number>(`${configPath}.maxRequests`, 30);
    const windowMs = this.configService.get<number>(`${configPath}.windowMs`, 60000);
    
    // Create a unique key for this client
    const key = organizationId 
      ? `${organizationId}:${category}:${tier}`
      : `${ip}:${category}`;
    
    const rateLimiter = this.getRateLimiter(key, endpoint);
    
    // Update rate limiter config if needed
    rateLimiter.updateConfig({
      maxRequests,
      windowMs,
      throwOnLimit: this.configService.get<boolean>('rateLimit.throwOnLimit', true),
    });
    
    if (!rateLimiter.acquire(key)) {
      const utilization = rateLimiter.getUtilization(key);
      this.logger.warn(
        `Rate limit exceeded: ${key}, endpoint: ${method} ${endpoint}, ` +
        `utilization: ${utilization.utilizationPercentage.toFixed(2)}%, ` +
        `remaining: ${utilization.remainingTokens}/${utilization.maxTokens}`
      );
      
      // Add rate limit headers to response
      const response = context.switchToHttp().getResponse();
      response.header('X-RateLimit-Limit', maxRequests);
      response.header('X-RateLimit-Remaining', utilization.remainingTokens);
      response.header('X-RateLimit-Reset', Math.floor(Date.now() / 1000) + Math.floor(windowMs / 1000));
      
      throw new HttpException({
        statusCode: HttpStatus.TOO_MANY_REQUESTS,
        message: 'Rate limit exceeded',
        error: 'Too Many Requests',
        retryAfter: Math.ceil(windowMs / 1000),
      }, HttpStatus.TOO_MANY_REQUESTS);
    }
    
    return next.handle();
  }
}

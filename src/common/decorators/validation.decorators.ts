import { 
  registerDecorator, 
  ValidationOptions, 
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface 
} from 'class-validator';

/**
 * Validates whether a string is a valid session ID format
 */
@ValidatorConstraint({ name: 'isSessionIdFormat', async: false })
export class IsSessionIdFormatConstraint implements ValidatorConstraintInterface {
  validate(text: string, args: ValidationArguments) {
    return typeof text === 'string' && /^[a-zA-Z0-9-_]{4,64}$/.test(text);
  }

  defaultMessage(args: ValidationArguments) {
    return 'Session ID must be between 4 and 64 alphanumeric characters, dashes, or underscores';
  }
}

/**
 * Validates that a string is a valid MongoDB ObjectId format
 */
@ValidatorConstraint({ name: 'isObjectId', async: false })
export class IsObjectIdConstraint implements ValidatorConstraintInterface {
  validate(text: string, args: ValidationArguments) {
    return typeof text === 'string' && /^[0-9a-fA-F]{24}$/.test(text);
  }

  defaultMessage(args: ValidationArguments) {
    return 'Must be a valid ObjectId (24-character hex string)';
  }
}

/**
 * Custom decorator to validate session ID format
 */
export function IsSessionIdFormat(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isSessionIdFormat',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsSessionIdFormatConstraint,
    });
  };
}

/**
 * Custom decorator to validate MongoDB ObjectId format
 */
export function IsObjectId(validationOptions?: ValidationOptions) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'isObjectId',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsObjectIdConstraint,
    });
  };
}

/**
 * Validates that a value is at least the specified minimum
 */
@ValidatorConstraint({ name: 'min', async: false })
export class MinConstraint implements ValidatorConstraintInterface {
  validate(value: number, args: ValidationArguments) {
    const [min] = args.constraints;
    return typeof value === 'number' && value >= min;
  }

  defaultMessage(args: ValidationArguments) {
    const [min] = args.constraints;
    return `Value must be at least ${min}`;
  }
}

/**
 * Validates that a value is within a specified range
 */
@ValidatorConstraint({ name: 'range', async: false })
export class RangeConstraint implements ValidatorConstraintInterface {
  validate(value: number, args: ValidationArguments) {
    const [min, max] = args.constraints;
    return typeof value === 'number' && value >= min && value <= max;
  }

  defaultMessage(args: ValidationArguments) {
    const [min, max] = args.constraints;
    return `Value must be between ${min} and ${max}`;
  }
}

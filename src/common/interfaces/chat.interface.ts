import { Citation } from './legal-research.interface';

export interface ChatSession {
  id: string;
  organizationId: string;
  documentId: string;
  createdAt: Date;
  updatedAt: Date;
  userId?: string;
  title?: string;
  messages: ChatMessage[];
  threads?: string[]; // Array of thread IDs associated with this session
  metadata?: Record<string, any>;
}

export interface MessageAttachment {
  id: string;
  fileName: string;
  fileType: string;
  filePath: string;
}

export interface MessageReference {
  documentId: string;
  content: string;
  relevanceScore: number;
}

export interface ContextSource {
  documentId: string;
  content: string;
  relevanceScore: number;
}

export enum AttachmentMimeType {
  PDF = 'application/pdf',
  DOC = 'application/msword',
  DOCX = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  TXT = 'text/plain'
}

export enum ChatRole {
  USER = 'user',
  ASSISTANT = 'assistant',
  SYSTEM = 'system',
}

export interface ChatMessage {
  id: string;
  organizationId: string;
  sessionId: string;
  role: ChatRole;
  content: string;
  timestamp: Date;
  contextSources?: ContextSource[];
  threadId?: string; // Optional thread this message belongs to
  replyToMessageId?: string; // For message replies within threads
  attachments?: MessageAttachment[]; // Optional message attachments
  references?: MessageReference[]; // Optional document references
  citations?: Citation[];
  metadata?: Record<string, any>;
}

export interface DocumentAnalysis {
  sections: {
    title: string;
    content: string;
    summary: string;
  }[];
  clauses: {
    title: string;
    content: string;
    analysis: string;
  }[];
  summary: string;
}

export interface RateLimiterInfo {
  currentRequests: number;
  maxRequests: number;
  windowMs: number;
  utilizationPercentage: number;
}

export interface OpenAIAnalysisOptions {
  maxTokens?: number;
  temperature?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
}

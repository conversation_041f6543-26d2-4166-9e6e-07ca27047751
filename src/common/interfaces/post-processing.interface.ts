import { DocumentType } from './prompt-template.interface';
import { DocumentAnalysisR<PERSON>ult, ErrorResponse } from '../interfaces/document-analysis.interface';

/**
 * Interface for defining post-processing operations on AI analysis results
 */
export interface PostProcessingRule {
  /** Unique identifier for the rule */
  id: string;
  
  /** Name of the rule */
  name: string;
  
  /** Description of what the rule does */
  description: string;
  
  /** Document types this rule applies to (empty means all types) */
  applicableDocumentTypes: DocumentType[];
  
  /** Rule severity - used for logging and debugging */
  severity: 'low' | 'medium' | 'high';
  
  /** Function to process the document analysis result */
  process: (result: DocumentAnalysisResult) => DocumentAnalysisResult;
}

/**
 * A post-processing result containing the processed data and metadata
 */
export interface PostProcessingResult<T> {
  /** The processed data */
  data: T;
  
  /** Original data before processing */
  originalData: T;
  
  /** Metadata about the processing */
  meta: {
    /** Rules that were applied */
    appliedRules: string[];
    
    /** Whether processing was successful */
    success: boolean;
    
    /** Warnings or issues encountered during processing */
    warnings?: string[];
    
    /** Time taken to process in ms */
    processingTimeMs: number;
  };
}

/**
 * Configuration options for post-processing
 */
export interface PostProcessingOptions {
  /** Document type for type-specific processing */
  documentType?: DocumentType;
  
  /** Whether to apply all rules or just document-specific ones */
  applyAllRules?: boolean;
  
  /** Skip specific rules by ID */
  skipRules?: string[];
  
  /** Only apply specific rules (overrides applyAllRules) */
  onlyRules?: string[];
}

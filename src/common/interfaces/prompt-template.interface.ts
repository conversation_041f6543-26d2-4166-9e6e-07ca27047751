/**
 * Defines the document types supported by the prompt template system
 */
export enum DocumentType {
  CONTRACT = 'contract',
  AGREEMENT = 'agreement',
  LEGAL_OPINION = 'legal_opinion',
  POLICY = 'policy',
  LEGISLATION = 'legislation',
  COURT_FILING = 'court_filing',
  GENERAL = 'general',
}

/**
 * Defines the structure of a prompt template
 */
export interface PromptTemplate {
  /**
   * Unique identifier for the template
   */
  id: string;

  /**
   * User-friendly name of the template
   */
  name: string;

  /**
   * Document type this template is designed for
   */
  documentType: DocumentType;

  /**
   * Description of the template's purpose
   */
  description: string;

  /**
   * The template text with placeholders for dynamic content
   * Placeholders should be in the format: {{placeholder_name}}
   */
  template: string;

  /**
   * Optional specific instructions for the AI model
   */
  instructions?: string;
}

/**
 * Defines the allowed placeholders in templates
 */
export interface TemplatePlaceholders {
  /**
   * Document content to analyze
   */
  documentContent: string;

  /**
   * Previous chat messages for context
   */
  chatHistory?: string;

  /**
   * User's specific question or instruction
   */
  query?: string;

  /**
   * Any additional context to include
   */
  context?: string;

  /**
   * Custom properties that may be needed for specific templates
   */
  [key: string]: any;
}

export interface ChatThread {
  id: string;
  sessionId: string;
  title: string;
  createdAt: Date;
  updatedAt: Date;
  firstMessageId: string; // Reference to the message that started the thread
  parentThreadId?: string; // For nested threads
  metadata?: {
    documentSection?: string;
    documentClause?: string;
    topic?: string;
    tags?: string[];
  };
}

export interface ThreadSummary {
  id: string;
  title: string;
  messageCount: number;
  lastMessageTimestamp: Date;
  previewContent: string;
}

export enum ThreadStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
  RESOLVED = 'resolved'
}
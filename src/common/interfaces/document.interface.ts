export interface Document {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  path: string;
  content?: string;
  hash: string;
  uploadDate: Date;
  metadata: DocumentMetadata;
  organizationId?: string; // Add organizationId at the top level for tenant isolation
}

export interface DocumentMetadata {
  sections?: DocumentSection[];
  clauses?: DocumentClause[];
  summary?: string;
  title?: string;
  author?: string;
  subject?: string;
  keywords?: string;
  creator?: string;
  pageCount?: number;
  processingTimeMs?: number;
  warnings?: string[];
  version?: string;
  encoding?: string;
  info?: Record<string, unknown>;
  cachedResult?: boolean;
  originalCacheId?: string;
  [key: string]: unknown;
}

export interface DocumentSection {
  title: string;
  content: string;
  purpose?: string;
}

export interface DocumentClause {
  title: string;
  content: string;
  type?: string;
  importance?: 'high' | 'medium' | 'low';
  riskLevel?: 'high' | 'medium' | 'low';
}

export enum ClauseType {
  OBLIGATION = 'obligation',
  PROHIBITION = 'prohibition',
  PERMISSION = 'permission',
  DEFINITION = 'definition',
  TERMINATION = 'termination',
  LIABILITY = 'liability',
  CONFIDENTIALITY = 'confidentiality',
  OTHER = 'other',
}

export enum RiskLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
}

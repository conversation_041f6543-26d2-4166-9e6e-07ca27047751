export interface Citation {
  rawText: string;
  title?: string;
  url?: string;
  court?: string;
  year?: number;
  confidence?: number;
  metadata?: Record<string, any>;
}

export interface DocumentReference {
  documentId: string;
  content: string;
  relevanceScore: number;
}

export interface AnalysisResult {
  content: string;
  metadata: {
    legalCitations?: Citation[];
    [key: string]: any;
  };
  analysisContent: Record<string, any>;
}

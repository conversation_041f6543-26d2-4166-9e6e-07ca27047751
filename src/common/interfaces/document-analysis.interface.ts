/**
 * Interfaces related to document analysis results
 */

import { DocumentType } from '../enums/document-type.enum';

export interface DocumentSection {
  title: string;
  purpose: string;
  content: string;
  startIndex: number;
  endIndex: number;
}

export interface DocumentParty {
  name: string;
  role: string;
  address?: string;
  email?: string;
  registrationNumber?: string;
}

export interface ClauseMetadata {
  obligations?: string[];
  rights?: string[];
  restrictions?: string[];
  definitions?: Record<string, string>;
  section?: string[];  // Section identifier(s) for the clause
}

export interface DocumentClause {
  title?: string;
  content: string;
  type?: string;
  risk?: string;
  riskLevel?: 'high' | 'medium' | 'low' | 'none';
  riskDescription?: string;
  recommendations?: string;
  importance?: 'high' | 'medium' | 'low';
  metadata?: ClauseMetadata;
}

/**
 * Basic document analysis result structure
 */
export interface DocumentAnalysisResult {
  documentType?: string;
  title?: string; // Added for GENERAL type
  sections?: DocumentSection[];
  clauses?: DocumentClause[];
  parties?: DocumentParty[];
  keyPoints?: string[]; // Added for GENERAL type
  summary?: string;
  error?: string;
  content?: string;
  metadata?: Record<string, any>;
  effectiveDate?: string;
  terminationDate?: string;
  governingLaw?: string;
}

/**
 * Error response structure
 */
export interface ErrorResponse {
  error: string;
  status: number;
  message: string;
  rawResponse?: string;
}

/**
 * Represents the processing status of a document
 */
export interface DocumentProcessingStatus {
  documentId: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  operation: 'analyze' | 'extract' | 'multiDocumentAnalysis' | string;
  startTime?: Date;
  endTime?: Date;
  progress?: number;
  documentType?: DocumentType;
  relatedDocumentIds?: string[];
  error?: string;
  metadata?: Record<string, any>;
}

/**
 * Multi-document analysis result
 */
export interface MultiDocumentAnalysisResult {
  summary: string;
  comparisons?: Array<{
    aspect: string;
    description: string;
    documents: Record<string, string>;
  }>;
  recommendations?: string[];
}

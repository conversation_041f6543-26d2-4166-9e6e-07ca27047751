/**
 * Represents a relationship between documents
 */
export interface DocumentRelationship {
  /**
   * The source document ID
   */
  sourceDocumentId: string;
  
  /**
   * The target document ID
   */
  targetDocumentId: string;
  
  /**
   * Type of relationship between documents
   */
  relationshipType: DocumentRelationshipType;
  
  /**
   * Optional description of the relationship
   */
  description?: string;
  
  /**
   * Creation timestamp of the relationship
   */
  createdAt: Date;
}

/**
 * Types of relationships that can exist between documents
 */
export enum DocumentRelationshipType {
  RELATED = 'related',           // Generic relation between documents
  AMENDS = 'amends',             // Document amends another document
  SUPPLEMENTS = 'supplements',   // Document supplements another document
  REFERENCES = 'references',     // Document references another document
  SUPERSEDES = 'supersedes',     // Document supersedes another document
  PART_OF = 'part_of'            // Document is part of a larger document set
}

/**
 * Represents a group of related documents
 */
export interface DocumentGroup {
  /**
   * The unique ID of the document group
   */
  id: string;
  
  /**
   * Name of the document group
   */
  name: string;
  
  /**
   * Description of the document group
   */
  description?: string;
  
  /**
   * IDs of documents in this group
   */
  documentIds: string[];
  
  /**
   * Creation timestamp
   */
  createdAt: Date;
  
  /**
   * Last update timestamp
   */
  updatedAt: Date;
}

/**
 * Document context for multi-document analysis
 */
export interface DocumentContext {
  /**
   * Main document ID for this context
   */
  primaryDocumentId: string;
  
  /**
   * Related document IDs
   */
  relatedDocumentIds: string[];
  
  /**
   * Optional group ID if this context is part of a document group
   */
  groupId?: string;
  
  /**
   * Relationships between documents in this context
   */
  relationships?: DocumentRelationship[];
  
  /**
   * Creation timestamp
   */
  createdAt: Date;
  
  /**
   * Last update timestamp
   */
  updatedAt: Date;
}

/**
 * Query options for retrieving document context
 */
export interface DocumentContextQueryOptions {
  /**
   * Include document content in response
   */
  includeContent?: boolean;
  
  /**
   * Maximum depth of related documents to include
   */
  maxDepth?: number;
  
  /**
   * Only include specific relationship types
   */
  relationshipTypes?: DocumentRelationshipType[];
}

/**
 * Result of a multi-document analysis
 */
export interface MultiDocumentAnalysisResult {
  /**
   * Results for individual documents
   */
  documentResults: {
    [documentId: string]: {
      documentId: string;
      excerpts: string[];
      relevance: number;
    }
  };
  
  /**
   * Cross-document findings
   */
  crossDocumentFindings: Array<{
    title: string;
    description: string;
    documentIds: string[];
    severity: 'info' | 'warning' | 'critical';
  }>;
  
  /**
   * Summary of multi-document analysis
   */
  summary: string;
}

export interface ContextWindow {
  content: string;
  totalTokens: number;
  tokenUtilization: number;
  relevantSections: any[];
  sections?: {
    content: string;
    relevanceScore: number;
  }[];
  documentExcerpts: string[];
  documentMetadata?: string;
  relatedDocumentExcerpts?: string[];
  chatHistory?: string;
}

export interface ContextRetrievalOptions {
  query: string;
  documentId: string;
  sessionId: string;
  relatedDocumentIds?: string[];
}

export enum ContextSource {
  DOCUMENT = 'document',
  CHAT_HISTORY = 'chat_history',
  METADATA = 'metadata'
}

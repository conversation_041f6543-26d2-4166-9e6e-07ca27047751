/**
 * MongoDB Integration Test Script
 *
 * This script tests the MongoDB integration and database monitoring functionality.
 */

import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { DatabaseMonitorService } from '../modules/database/services/database-monitor.service';
import { ChatMigrationService } from '../modules/chat/services/chat-migration.service';
import { Logger } from '@nestjs/common';

const logger = new Logger('MongoDB-Test');

async function testMongoDbIntegration() {
  try {
    // Start NestJS app
    logger.log('Starting test application...');
    const app = await NestFactory.create(AppModule);
    
    // Get service instances
    const dbMonitorService = app.get(DatabaseMonitorService);
    const chatMigrationService = app.get(ChatMigrationService);
    
    // Test database connection
    logger.log('Testing database connection...');
    const connected = dbMonitorService.isConnected();
    logger.log(`Database connected: ${connected}`);
    
    if (!connected) {
      logger.error('Database connection failed. Please check configuration.');
      await app.close();
      return;
    }
    
    // Get database stats
    logger.log('Fetching database statistics...');
    const stats = await dbMonitorService.getDatabaseStats();
    logger.log('Database stats:', stats);
    
    // Check ping time
    logger.log('Measuring database ping...');
    const pingTime = await dbMonitorService.measurePing();
    logger.log(`Database ping: ${pingTime}ms`);
    
    logger.log('All database tests completed successfully');
    await app.close();
    
  } catch (error) {
    logger.error('Test failed:', error.message);
  }
}

// Run the tests
testMongoDbIntegration()
  .then(() => {
    logger.log('Test script execution complete');
    process.exit(0);
  })
  .catch(error => {
    logger.error('Fatal error during test execution:', error);
    process.exit(1);
  });

import { Test } from '@nestjs/testing';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import { Document, DocumentModel, DocumentSchema } from '../modules/documents/schemas/document.schema';
import { DocumentStorageService } from '../modules/documents/services/document-storage.service';

describe('MongoDB Integration', () => {
  let docStorageService: DocumentStorageService;

  beforeAll(async () => {
    const moduleRef = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot(),
        MongooseModule.forRoot(process.env.MONGODB_URI || 'mongodb://localhost/test'),
        MongooseModule.forFeature([
          { name: DocumentModel.name, schema: DocumentSchema }
        ])
      ],
      providers: [DocumentStorageService]
    }).compile();

    docStorageService = moduleRef.get<DocumentStorageService>(DocumentStorageService);
  });

  it('should save and retrieve a document', async () => {
    const testDoc = {
      filename: 'test.pdf',
      originalName: 'test-original.pdf',
      size: 1024,
      uploadDate: new Date(),
      status: 'uploaded',
      metadata: {
        sections: [
          {
            title: 'Introduction',
            content: 'Test content',
            purpose: 'Overview'
          }
        ],
        clauses: [
          {
            title: 'Test Clause',
            content: 'Test clause content',
            type: 'general',
            riskLevel: 'low'
          }
        ]
      }
    };

    const storedDoc = await docStorageService.saveDocument(testDoc);
    expect(storedDoc).toBeDefined();
    expect(storedDoc.id).toBeDefined();
    expect(storedDoc.filename).toBe(testDoc.filename);

    const retrievedDoc = await docStorageService.getDocumentById(storedDoc.id);
    expect(retrievedDoc).toBeDefined();
    expect(retrievedDoc.metadata.sections).toHaveLength(1);
    expect(retrievedDoc.metadata.clauses).toHaveLength(1);
  });

  it('should update document metadata', async () => {
    const testDoc = {
      filename: 'updateTest.pdf',
      originalName: 'updateTest.pdf',
      size: 2048,
      uploadDate: new Date(),
      status: 'uploaded',
      metadata: {
        version: '1.0'
      }
    };

    const storedDoc = await docStorageService.saveDocument(testDoc);
    const newMetadata = {
      version: '2.0',
      updatedAt: new Date()
    };

    await docStorageService.updateDocumentMetadata(storedDoc.id, newMetadata);
    const updatedDoc = await docStorageService.getDocumentById(storedDoc.id);

    expect(updatedDoc.metadata.version).toBe('2.0');
    expect(updatedDoc.metadata.updatedAt).toBeDefined();
  });

  afterAll(async () => {
    // Cleanup test data if needed
  });
});

import { Test } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { DocumentContextService } from '../modules/document-context/services/document-context.service';
import { DocumentRelationshipType } from '../common/interfaces/document-context.interface';

describe('DocumentContextService', () => {
  let documentContextService: DocumentContextService;
  
  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
      providers: [
        DocumentContextService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              // Mock any config values needed for tests
              return null;
            }),
          },
        },
      ],
    }).compile();

    documentContextService = moduleRef.get<DocumentContextService>(DocumentContextService);
  });

  describe('createContext', () => {
    it('should create a document context with related documents', async () => {
      const primaryDocId = 'doc1';
      const relatedDocIds = ['doc2', 'doc3'];
      
      const result = await documentContextService.createContext(primaryDocId, relatedDocIds);
      
      expect(result).toBeDefined();
      expect(result.primaryDocumentId).toBe(primaryDocId);
      expect(result.relatedDocumentIds).toEqual(relatedDocIds);
      expect(result.createdAt).toBeInstanceOf(Date);
      expect(result.updatedAt).toBeInstanceOf(Date);
    });
  });
  
  describe('getContext', () => {
    it('should retrieve a document context', async () => {
      // Create a context first
      const primaryDocId = 'doc1';
      const relatedDocIds = ['doc2', 'doc3'];
      
      await documentContextService.createContext(primaryDocId, relatedDocIds);
      
      // Then retrieve it
      const result = await documentContextService.getContext(primaryDocId);
      
      expect(result).toBeDefined();
      expect(result.primaryDocumentId).toBe(primaryDocId);
      expect(result.relatedDocumentIds).toHaveLength(2);
      expect(result.relatedDocumentIds).toContain('doc2');
      expect(result.relatedDocumentIds).toContain('doc3');
    });
    
    it('should return null for non-existent context', async () => {
      const result = await documentContextService.getContext('nonexistent');
      expect(result).toBeNull();
    });
  });
  
  describe('addRelationship', () => {
    it('should add a relationship between documents', async () => {
      const sourceDocId = 'doc1';
      const targetDocId = 'doc2';
      const relationshipType = DocumentRelationshipType.AMENDS;
      
      // Create context first
      await documentContextService.createContext(sourceDocId);
      
      // Add relationship
      const result = await documentContextService.addRelationship(
        sourceDocId,
        targetDocId,
        relationshipType,
        'Test amendment relationship'
      );
      
      expect(result).toBeDefined();
      expect(result.sourceDocumentId).toBe(sourceDocId);
      expect(result.targetDocumentId).toBe(targetDocId);
      expect(result.relationshipType).toBe(relationshipType);
      expect(result.description).toBe('Test amendment relationship');
      
      // Verify context was updated
      const context = await documentContextService.getContext(sourceDocId);
      expect(context.relatedDocumentIds).toContain(targetDocId);
    });
  });
  
  describe('createGroup', () => {
    it('should create a document group with documents', async () => {
      const groupName = 'Test Group';
      const docIds = ['doc1', 'doc2', 'doc3'];
      
      const result = await documentContextService.createGroup(groupName, docIds);
      
      expect(result).toBeDefined();
      expect(result.name).toBe(groupName);
      expect(result.documentIds).toEqual(docIds);
      expect(result.id).toBeDefined();
    });
  });
  
  describe('getRelatedDocuments', () => {
    it('should retrieve related documents', async () => {
      // Setup
      const primaryDocId = 'doc1';
      const relatedDocIds = ['doc2', 'doc3', 'doc4'];
      
      await documentContextService.createContext(primaryDocId, relatedDocIds);
      
      // Test
      const result = await documentContextService.getRelatedDocuments(primaryDocId);
      
      expect(result).toHaveLength(3);
      expect(result).toContain('doc2');
      expect(result).toContain('doc3');
      expect(result).toContain('doc4');
    });
    
    it('should filter related documents by relationship type', async () => {
      // Setup
      const primaryDocId = 'doc1';
      
      await documentContextService.createContext(primaryDocId);
      await documentContextService.addRelationship(
        primaryDocId, 'doc2', DocumentRelationshipType.AMENDS
      );
      await documentContextService.addRelationship(
        primaryDocId, 'doc3', DocumentRelationshipType.REFERENCES
      );
      await documentContextService.addRelationship(
        primaryDocId, 'doc4', DocumentRelationshipType.AMENDS
      );
      
      // Test
      const result = await documentContextService.getRelatedDocuments(
        primaryDocId, [DocumentRelationshipType.AMENDS]
      );
      
      expect(result).toHaveLength(2);
      expect(result).toContain('doc2');
      expect(result).toContain('doc4');
      expect(result).not.toContain('doc3');
    });
  });
});

import { Test } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { ResultPostProcessingService } from '../modules/post-processing/services/result-post-processing.service';
import { DocumentAnalysisResult, ErrorResponse, DocumentClause } from '../common/interfaces/document-analysis.interface';
import { DocumentType } from '../common/interfaces/prompt-template.interface';

describe('ResultPostProcessingService', () => {
  let postProcessingService: ResultPostProcessingService;
  
  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
      providers: [
        ResultPostProcessingService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string) => {
              // Mock any config values needed for tests
              return null;
            }),
          },
        },
      ],
    }).compile();

    postProcessingService = moduleRef.get<ResultPostProcessingService>(ResultPostProcessingService);
  });

  describe('processAnalysisResult', () => {
    it('should process document analysis and apply rules', async () => {
      // Create a sample document analysis result
      const sampleAnalysis: DocumentAnalysisResult = {
        sections: [
          {
            title: 'Introduction section',
            purpose: 'Sets out the background of the agreement',
            content: 'This is the introduction content',
            startIndex: 0,
            endIndex: 500,
          },
          {
            title: 'DEFINITIONS',
            purpose: 'Defines key terms used in the document',
            content: 'This is the definitions content',
            startIndex: 501,
            endIndex: 1000,
          },
        ],
        clauses: [
          {
            title: 'Indemnification Clause',
            type: 'liability',
            content: 'The party shall indemnify, defend and hold harmless the other party.',
            riskLevel: 'medium',
            riskDescription: '',
          },
          {
            title: 'Payment Terms',
            type: 'obligations',
            content: 'Payment shall be made within 30 days of invoice date.',
            riskLevel: 'low',
            riskDescription: '',
          },
        ],
        summary: 'A short summary',
      };

      // Process the analysis
      const result = await postProcessingService.processAnalysisResult(sampleAnalysis, {
        documentType: DocumentType.CONTRACT,
        applyAllRules: true,
      });

      // Assertions for post-processing results
      expect(result.data).toBeDefined();
      expect(result.originalData).toBeDefined();
      expect(result.meta.appliedRules.length).toBeGreaterThan(0);
      expect(result.meta.success).toBe(true);
      
      // Ensure we're dealing with a DocumentAnalysisResult
      if ('sections' in result.data) {
        // 1. Section title formatting
        expect(result.data.sections[0].title).toBe('Introduction Section');
        expect(result.data.sections[1].title).toBe('Definitions');
        
        // 2. Risk level and description fixes
        const paymentClause = result.data.clauses.find(c => c.title === 'Payment Terms');
        expect(paymentClause?.riskLevel).toBe('low'); // Default risk level applied
        
        // 3. Clause type standardization
        expect(paymentClause?.type).toBe('obligation'); // Standardized from 'obligations'
        
        // 4. High-risk keyword detection in contract
        const indemnificationClause = result.data.clauses.find(c => c.title === 'Indemnification Clause');
        expect(indemnificationClause?.riskLevel).toBe('high'); // Upgraded due to keyword
        expect(indemnificationClause?.riskDescription).toContain('indemnify');
        
        // 5. Summary quality check
        expect(result.data.summary.length).toBeGreaterThan(10);
      } else {
        // If we somehow get an ErrorResponse, fail the test
        fail('Expected DocumentAnalysisResult but got ErrorResponse');
      }
    });

    it('should handle error responses without modification', async () => {
      // Test with an error response
      const errorResponse: ErrorResponse = {
        error: 'Failed to parse structured data from response',
        message: 'API Error',
        status: 500, // Changed from string to number
        rawResponse: 'Invalid response from API',
      };

      const result = await postProcessingService.processAnalysisResult(errorResponse);
      
      // Verify error response is passed through
      expect(result.data).toEqual(errorResponse);
      expect(result.meta.appliedRules.length).toBe(0);
      expect(result.meta.success).toBe(false);
      expect(result.meta.warnings).toContain('Skipped post-processing due to error response');
    });
  });
});

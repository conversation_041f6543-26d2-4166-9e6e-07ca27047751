import { registerAs } from '@nestjs/config';

export const documentProcessingConfig = registerAs('documentProcessing', () => ({
  // Worker configuration
  workers: {
    maxWorkers: parseInt(process.env.DOC_PROCESSING_MAX_WORKERS || '4', 10),
    timeoutMs: parseInt(process.env.DOC_PROCESSING_TIMEOUT_MS || '120000', 10), // 2 minutes
    chunkSize: parseInt(process.env.DOC_PROCESSING_CHUNK_SIZE || '52428800', 10), // 50MB
  },

  // Queue configuration
  queue: {
    concurrency: parseInt(process.env.DOC_PROCESSING_CONCURRENCY || '3', 10),
    maxRetries: parseInt(process.env.DOC_PROCESSING_MAX_RETRIES || '3', 10),
    retryDelay: parseInt(process.env.DOC_PROCESSING_RETRY_DELAY || '1000', 10),
    removeOnComplete: parseInt(process.env.DOC_PROCESSING_REMOVE_ON_COMPLETE || '10', 10),
    removeOnFail: parseInt(process.env.DOC_PROCESSING_REMOVE_ON_FAIL || '50', 10),
  },

  // Processing optimization
  optimization: {
    enableParallelProcessing: process.env.DOC_PROCESSING_PARALLEL === 'true',
    enableCaching: process.env.DOC_PROCESSING_CACHE === 'true',
    cacheExpirationMs: parseInt(process.env.DOC_PROCESSING_CACHE_EXPIRATION || '3600000', 10), // 1 hour
    enableProgressTracking: process.env.DOC_PROCESSING_PROGRESS_TRACKING !== 'false',
    enableContentDeduplication: process.env.DOC_PROCESSING_DEDUPLICATION !== 'false',
  },

  // File size limits
  limits: {
    maxFileSize: parseInt(process.env.DOC_PROCESSING_MAX_FILE_SIZE || '52428800', 10), // 50MB
    maxFilesPerBatch: parseInt(process.env.DOC_PROCESSING_MAX_FILES_PER_BATCH || '10', 10),
    maxConcurrentUploads: parseInt(process.env.DOC_PROCESSING_MAX_CONCURRENT_UPLOADS || '5', 10),
  },

  // Performance monitoring
  monitoring: {
    enableMetrics: process.env.DOC_PROCESSING_METRICS !== 'false',
    metricsInterval: parseInt(process.env.DOC_PROCESSING_METRICS_INTERVAL || '60000', 10), // 1 minute
    alertThresholds: {
      processingTimeMs: parseInt(process.env.DOC_PROCESSING_ALERT_TIME || '300000', 10), // 5 minutes
      queueLength: parseInt(process.env.DOC_PROCESSING_ALERT_QUEUE_LENGTH || '100', 10),
      errorRate: parseFloat(process.env.DOC_PROCESSING_ALERT_ERROR_RATE || '0.1'), // 10%
    },
  },

  // Content extraction
  extraction: {
    enableOCR: process.env.DOC_PROCESSING_OCR === 'true',
    ocrLanguages: (process.env.DOC_PROCESSING_OCR_LANGUAGES || 'eng').split(','),
    enableMetadataExtraction: process.env.DOC_PROCESSING_METADATA !== 'false',
    enableContentAnalysis: process.env.DOC_PROCESSING_CONTENT_ANALYSIS !== 'false',
  },

  // Storage optimization
  storage: {
    enableCompression: process.env.DOC_PROCESSING_COMPRESSION !== 'false',
    compressionLevel: parseInt(process.env.DOC_PROCESSING_COMPRESSION_LEVEL || '6', 10),
    enableThumbnails: process.env.DOC_PROCESSING_THUMBNAILS === 'true',
    thumbnailSizes: (process.env.DOC_PROCESSING_THUMBNAIL_SIZES || '150,300').split(',').map(s => parseInt(s, 10)),
  },
}));

export type DocumentProcessingConfig = ReturnType<typeof documentProcessingConfig>;

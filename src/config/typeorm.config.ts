import { DataSourceOptions } from 'typeorm';
import * as path from 'path';

export const typeOrmConfig: DataSourceOptions = {
  type: 'sqlite',
  database: path.join(process.cwd(), 'uploads', 'cache', 'document-cache.db'),
  entities: [path.join(__dirname, '..', '**', '*.entity{.ts,.js}')],
  synchronize: process.env.NODE_ENV !== 'production',
  logging: process.env.NODE_ENV !== 'production',
};

export default typeOrmConfig;

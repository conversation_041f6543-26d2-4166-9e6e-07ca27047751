import { registerAs } from '@nestjs/config';

export const subscriptionConfig = registerAs('subscription', () => ({
  bypassSubscriptionLimit: false, // Set to false for testing subscription limits
  tiers: {
    free: {
      documentLimit: 10,
      analysisPerMonth: 50,
      features: ['basic_analysis', 'document_organization', 'user_feedback'],
    },
    pro: {
      documentLimit: 200,
      analysisPerMonth: -1, // unlimited
      features: [
        'basic_analysis', 
        'advanced_analysis', 
        'batch_processing', 
        'custom_models', 
        'enhanced_comparison',
        'document_organization',
        'advanced_document_organization',
        'user_feedback',
        'advanced_analytics',
        'document_comparison'
      ],
    },
    admin: {
      documentLimit: -1, // unlimited
      analysisPerMonth: -1, // unlimited
      features: [
        'basic_analysis', 
        'advanced_analysis', 
        'batch_processing', 
        'custom_models', 
        'enhanced_comparison',
        'document_organization',
        'advanced_document_organization',
        'user_feedback',
        'advanced_analytics',
        'admin_features',
        'document_comparison'
      ],
    },
  },
}));

export type SubscriptionConfig = ReturnType<typeof subscriptionConfig>;
import { registerAs } from '@nestjs/config';

export const appConfig = registerAs('app', () => ({
  port: parseInt(process.env.PORT || '3000', 10),
  isDevelopment: process.env.NODE_ENV !== 'production',
  uploadDir: process.env.UPLOAD_DIR || 'uploads',
  storage: {
    uploadDir: process.env.UPLOAD_DIR || 'uploads',
    provider: process.env.STORAGE_PROVIDER || 'filesystem',
    cloudflare: {
      endpoint: process.env.CLOUDFLARE_R2_ENDPOINT,
      accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID,
      secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
      bucketName: process.env.CLOUDFLARE_R2_BUCKET_NAME,
      publicUrl: process.env.CLOUDFLARE_R2_PUBLIC_URL,
    },
  },
}));

export type AppConfig = ReturnType<typeof appConfig>;

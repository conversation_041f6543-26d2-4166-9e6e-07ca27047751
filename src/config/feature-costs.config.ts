import { registerAs } from '@nestjs/config';

/**
 * Feature cost configuration for the credit system
 * Each feature has a credit cost that will be deducted when used
 */
export interface FeatureCost {
  name: string;
  creditCost: number;
  description: string;
  category: 'basic' | 'advanced' | 'premium' | 'ai' | 'analysis' | 'automation';
}

export const FEATURE_COSTS: Record<string, FeatureCost> = {
  // Basic AI Features (1 credit) - CRUD operations are FREE (0 credits)
  'basic_analysis': {
    name: 'Basic Analysis',
    creditCost: 1,
    description: 'Basic document analysis and insights',
    category: 'basic',
  },
  'document_upload': {
    name: 'Document Upload',
    creditCost: 0, // FREE - Basic CRUD operation
    description: 'Upload and process documents',
    category: 'basic',
  },
  'chat': {
    name: 'Chat',
    creditCost: 1,
    description: 'AI chat responses about documents',
    category: 'basic',
  },
  'basic_comparison': {
    name: 'Basic Comparison',
    creditCost: 1,
    description: 'Basic AI document comparison',
    category: 'basic',
  },
  'basic_citation_analysis': {
    name: 'Basic Citation Analysis',
    creditCost: 1,
    description: 'Basic citation analysis with simple relationship mapping',
    category: 'basic',
  },
  'clause_identification': {
    name: 'Clause Identification',
    creditCost: 1,
    description: 'AI clause identification in documents',
    category: 'basic',
  },
  'playbook_analysis': {
    name: 'Playbook Analysis',
    creditCost: 1,
    description: 'Contract playbook AI analysis',
    category: 'basic',
  },
  'document_organization': {
    name: 'Document Organization',
    creditCost: 0, // FREE - Basic CRUD operation
    description: 'Organize and categorize documents',
    category: 'basic',
  },
  'user_feedback': {
    name: 'User Feedback',
    creditCost: 0,
    description: 'Provide feedback on the system',
    category: 'basic',
  },

  // Advanced AI Features (2 credits)
  'advanced_analysis': {
    name: 'Advanced Analysis',
    creditCost: 2,
    description: 'Advanced document analysis with detailed insights',
    category: 'advanced',
  },
  'bulk_upload': {
    name: 'Bulk Upload',
    creditCost: 0, // FREE - Basic CRUD operation
    description: 'Upload multiple documents at once',
    category: 'basic',
  },
  'priority_processing': {
    name: 'Priority Processing',
    creditCost: 1,
    description: 'Priority document processing',
    category: 'advanced',
  },
  'enhanced_comparison': {
    name: 'Enhanced Comparison',
    creditCost: 2,
    description: 'Enhanced document comparison with visual diff',
    category: 'advanced',
  },
  'enhanced_citation_analysis': {
    name: 'Enhanced Citation Analysis',
    creditCost: 2,
    description: 'Enhanced citation analysis with relationship mapping',
    category: 'advanced',
  },
  'advanced_document_organization': {
    name: 'Advanced Document Organization',
    creditCost: 0, // FREE - Basic CRUD operation
    description: 'Advanced document organization with AI categorization',
    category: 'basic',
  },
  'advanced_analytics': {
    name: 'Advanced Analytics',
    creditCost: 2,
    description: 'Advanced analytics and reporting',
    category: 'advanced',
  },
  'document_comparison': {
    name: 'Document Comparison',
    creditCost: 2,
    description: 'Detailed document comparison analysis',
    category: 'advanced',
  },

  // Premium AI Features (3 credits)
  'precedent_analysis': {
    name: 'Precedent Analysis',
    creditCost: 2,
    description: 'AI-powered precedent analysis',
    category: 'advanced',
  },
  'clause_library': {
    name: 'Clause Library',
    creditCost: 0, // FREE - Basic CRUD operation
    description: 'Access to clause library and suggestions',
    category: 'basic',
  },
  'template_generation': {
    name: 'Template Generation',
    creditCost: 2,
    description: 'AI-powered template generation',
    category: 'advanced',
  },
  'document_automation': {
    name: 'Document Automation',
    creditCost: 3,
    description: 'Automated document processing and generation',
    category: 'premium',
  },
  'ai_assisted_drafting': {
    name: 'AI Assisted Drafting',
    creditCost: 3,
    description: 'AI assistance for document drafting',
    category: 'premium',
  },
  'clause_intelligence': {
    name: 'Clause Intelligence',
    creditCost: 2,
    description: 'Intelligent clause analysis and suggestions',
    category: 'advanced',
  },
  'clause_library_automation': {
    name: 'Clause Library Automation',
    creditCost: 3,
    description: 'AI clause extraction and automation',
    category: 'premium',
  },
  'related_document_generation': {
    name: 'Related Document Generation',
    creditCost: 3,
    description: 'Generate related documents based on existing ones',
    category: 'premium',
  },

  // Additional Premium AI Features (3 credits max)
  'privilege_log_automation': {
    name: 'Privilege Log Automation',
    creditCost: 3,
    description: 'Automated privilege log generation',
    category: 'premium',
  },
  'privilege_detection': {
    name: 'Privilege Detection',
    creditCost: 2,
    description: 'Automated privilege detection in documents',
    category: 'advanced',
  },
  'redaction_automation': {
    name: 'Redaction Automation',
    creditCost: 3,
    description: 'Automated document redaction',
    category: 'premium',
  },
  'bulk_redactions': {
    name: 'Bulk Redactions',
    creditCost: 3,
    description: 'Bulk redaction processing',
    category: 'premium',
  },
  'deposition_preparation': {
    name: 'Deposition Preparation',
    creditCost: 0, // FREE - Basic CRUD operation
    description: 'Deposition preparation and management',
    category: 'basic',
  },
  'deposition_analysis': {
    name: 'Deposition Analysis',
    creditCost: 3,
    description: 'AI-powered deposition analysis',
    category: 'premium',
  },
  'ai_question_generation': {
    name: 'AI Question Generation',
    creditCost: 3,
    description: 'AI-generated questions for depositions',
    category: 'premium',
  },
  'deposition_insights': {
    name: 'Deposition Insights',
    creditCost: 2,
    description: 'Advanced insights from deposition analysis',
    category: 'advanced',
  },
  'litigation_support': {
    name: 'Litigation Support',
    creditCost: 3,
    description: 'Comprehensive litigation support tools',
    category: 'premium',
  },

  // Contract & Negotiation Features (Updated costs)
  'contract_playbooks': {
    name: 'Contract Playbooks',
    creditCost: 0, // FREE - Basic CRUD operation
    description: 'Contract playbook creation and management',
    category: 'basic',
  },
  'deviation_detection': {
    name: 'Deviation Detection',
    creditCost: 2,
    description: 'AI-powered deviation detection from standard terms',
    category: 'advanced',
  },
  'contract_risk_scoring': {
    name: 'Contract Risk Scoring',
    creditCost: 3,
    description: 'AI-powered contract risk assessment',
    category: 'premium',
  },
  'negotiation_playbook': {
    name: 'Negotiation Playbook',
    creditCost: 3,
    description: 'AI-powered negotiation strategy playbooks',
    category: 'premium',
  },
  'negotiation_simulator': {
    name: 'Negotiation Simulator',
    creditCost: 3,
    description: 'AI-powered negotiation simulation',
    category: 'premium',
  },
  'negotiation_training': {
    name: 'Negotiation Training',
    creditCost: 2,
    description: 'Interactive negotiation training modules',
    category: 'advanced',
  },
  'scenario_management': {
    name: 'Scenario Management',
    creditCost: 0, // FREE - Basic CRUD operation
    description: 'Manage negotiation scenarios',
    category: 'basic',
  },
  'performance_analytics': {
    name: 'Performance Analytics',
    creditCost: 2,
    description: 'Performance analytics and insights',
    category: 'advanced',
  },
  'compliance_audit': {
    name: 'Compliance Audit',
    creditCost: 2,
    description: 'AI-powered compliance auditing',
    category: 'advanced',
  },

  // Collaboration & Enterprise Features (Medium Cost)
  'real_time_collaboration': {
    name: 'Real-time Collaboration',
    creditCost: 2,
    description: 'Real-time collaboration on documents',
    category: 'advanced',
  },
  'workflow_management': {
    name: 'Workflow Management',
    creditCost: 1,
    description: 'Workflow management and automation',
    category: 'automation',
  },
  'threaded_discussions': {
    name: 'Threaded Discussions',
    creditCost: 0, // FREE - Basic collaboration feature
    description: 'Create threaded discussions and comments on documents',
    category: 'basic',
  },
  'team_analytics': {
    name: 'Team Analytics',
    creditCost: 2,
    description: 'AI-powered team performance analytics',
    category: 'advanced',
  },
  'advanced_sharing': {
    name: 'Advanced Sharing',
    creditCost: 1,
    description: 'Advanced document sharing capabilities',
    category: 'advanced',
  },

  // Admin Features (Variable Cost)
  'api_access': {
    name: 'API Access',
    creditCost: 0, // FREE - Basic access
    description: 'API access for integrations',
    category: 'basic',
  },
  'team_collaboration': {
    name: 'Team Collaboration',
    creditCost: 0, // FREE - Basic collaboration
    description: 'Team collaboration features',
    category: 'basic',
  },
  'admin_features': {
    name: 'Admin Features',
    creditCost: 0,
    description: 'Administrative features',
    category: 'basic',
  },
  'user_management': {
    name: 'User Management',
    creditCost: 0,
    description: 'User management capabilities',
    category: 'basic',
  },
  'organization_management': {
    name: 'Organization Management',
    creditCost: 0,
    description: 'Organization management features',
    category: 'basic',
  },
  'billing_management': {
    name: 'Billing Management',
    creditCost: 0,
    description: 'Billing and subscription management',
    category: 'basic',
  },
  'system_analytics': {
    name: 'System Analytics',
    creditCost: 2,
    description: 'AI-powered system-wide analytics and reporting',
    category: 'advanced',
  },
  'audit_logs': {
    name: 'Audit Logs',
    creditCost: 0, // FREE - Basic logging
    description: 'Comprehensive audit logging',
    category: 'basic',
  },
  'data_export': {
    name: 'Data Export',
    creditCost: 0, // FREE - Basic export
    description: 'Data export capabilities',
    category: 'basic',
  },
  'custom_integrations': {
    name: 'Custom Integrations',
    creditCost: 0, // FREE - Basic configuration
    description: 'Custom integration development',
    category: 'basic',
  },
  'predictive_analytics': {
    name: 'Predictive Analytics',
    creditCost: 3,
    description: 'AI-powered predictive analytics and forecasting',
    category: 'premium',
  },
  'risk_forecasting': {
    name: 'Risk Forecasting',
    creditCost: 3,
    description: 'AI-powered risk forecasting and prediction',
    category: 'premium',
  },
  'trend_analysis': {
    name: 'Trend Analysis',
    creditCost: 2,
    description: 'AI-powered trend analysis and insights',
    category: 'advanced',
  },
  'custom_ai_models': {
    name: 'Custom AI Models',
    creditCost: 3,
    description: 'Custom AI model training and deployment',
    category: 'premium',
  },
  'white_label_options': {
    name: 'White Label Options',
    creditCost: 0,
    description: 'White label customization options',
    category: 'basic',
  },
  'dedicated_support': {
    name: 'Dedicated Support',
    creditCost: 0,
    description: 'Dedicated customer support',
    category: 'basic',
  },
  'sla_guarantees': {
    name: 'SLA Guarantees',
    creditCost: 0,
    description: 'Service level agreement guarantees',
    category: 'basic',
  },

  // Legal Research Assistant Features
  'legal_research_assistant': {
    name: 'Legal Research Assistant',
    creditCost: 1,
    description: 'Basic legal research across multiple sources',
    category: 'basic',
  },
  'legal_research_synthesis': {
    name: 'Legal Research AI Synthesis',
    creditCost: 3,
    description: 'AI-powered analysis and synthesis of legal research results',
    category: 'premium',
  },
  'legal_research_followup': {
    name: 'Legal Research Follow-up',
    creditCost: 1,
    description: 'Follow-up questions in legal research sessions',
    category: 'basic',
  },
};

export const featureCostsConfig = registerAs('featureCosts', () => ({
  features: FEATURE_COSTS,
  // Credit packages for purchase (tailored for legal professionals)
  creditPackages: {
    student: {
      credits: 50,
      price: 499, // $4.99 in cents
      bonus: 0,
      description: 'Student credit package - Perfect for law students',
      targetTier: 'law_student',
    },
    lawyer_small: {
      credits: 200,
      price: 1999, // $19.99 in cents
      bonus: 20, // 10% bonus
      description: 'Lawyer starter pack - Additional credits for practicing attorneys',
      targetTier: 'lawyer',
    },
    lawyer_large: {
      credits: 500,
      price: 4499, // $44.99 in cents
      bonus: 75, // 15% bonus
      description: 'Lawyer professional pack - For busy legal practices',
      targetTier: 'lawyer',
    },
    firm_standard: {
      credits: 1000,
      price: 7999, // $79.99 in cents
      bonus: 200, // 20% bonus
      description: 'Law firm standard pack - For growing legal teams',
      targetTier: 'law_firm',
    },
    firm_enterprise: {
      credits: 5000,
      price: 34999, // $349.99 in cents
      bonus: 1500, // 30% bonus
      description: 'Law firm enterprise pack - For large legal organizations',
      targetTier: 'law_firm',
    },
  },
}));

export type FeatureCostsConfig = ReturnType<typeof featureCostsConfig>;

import { registerAs } from '@nestjs/config';
import { DocumentType, PromptTemplate } from '../common/interfaces/prompt-template.interface';

/**
 * Default templates for various document types
 */
const defaultTemplates: PromptTemplate[] = [
  {
    id: 'contract-analysis',
    name: 'Contract Analysis',
    documentType: DocumentType.CONTRACT,
    description: 'Template for analyzing legal contracts and identifying key clauses',
    template: `
    You are a legal AI assistant specializing in contract analysis. Please analyze the following contract:

    {{documentContent}}

    Analyze the contract and provide the following information:
    1. Contract Parties: Identify all parties involved in the contract.
    2. Key Terms: List the most important terms and conditions.
    3. Obligations: Summarize the main obligations for each party.
    4. Termination Clauses: Identify and explain the termination conditions.
    5. Risk Factors: Highlight potential risks or ambiguities.
    6. Governing Law: Identify the jurisdiction governing the contract.
    
    {{#if query}}
    User's specific question: {{query}}
    Please address this question specifically in your analysis.
    {{/if}}
    
    Format your response in clear sections with bullet points where appropriate.
    `,
  },
  {
    id: 'agreement-analysis',
    name: 'Agreement Analysis',
    documentType: DocumentType.AGREEMENT,
    description: 'Template for analyzing legal agreements like NDAs, employment agreements, etc.',
    template: `
    You are a legal AI assistant specializing in agreement analysis. Please analyze the following agreement:

    {{documentContent}}

    Analyze the agreement and provide the following information:
    1. Agreement Type: Identify the specific type of agreement.
    2. Parties Involved: List all parties to the agreement.
    3. Key Provisions: Summarize the most important provisions.
    4. Duration and Renewal: Explain the term and any renewal provisions.
    5. Confidentiality Requirements: Identify confidentiality obligations.
    6. Restrictive Covenants: Note any non-compete or non-solicitation clauses.
    7. Breach Consequences: Explain what happens if the agreement is breached.
    
    {{#if query}}
    User's specific question: {{query}}
    Please address this question specifically in your analysis.
    {{/if}}
    
    Format your response in clear sections with bullet points where appropriate.
    `,
  },
  {
    id: 'legal-opinion-analysis',
    name: 'Legal Opinion Analysis',
    documentType: DocumentType.LEGAL_OPINION,
    description: 'Template for analyzing legal opinions and memoranda',
    template: `
    You are a legal AI assistant specializing in analyzing legal opinions. Please analyze the following legal opinion:

    {{documentContent}}

    Analyze the legal opinion and provide the following information:
    1. Issue Presented: Identify the central legal question(s) addressed.
    2. Legal Framework: Outline the relevant laws, cases, and regulations cited.
    3. Analysis: Summarize the reasoning and legal analysis provided.
    4. Conclusion: State the conclusion(s) reached in the opinion.
    5. Potential Weaknesses: Identify any limitations or potential counterarguments.
    6. Practical Implications: Explain the practical impact of this opinion.
    
    {{#if query}}
    User's specific question: {{query}}
    Please address this question specifically in your analysis.
    {{/if}}
    
    Format your response with clear headings and concise explanations.
    `,
  },
  {
    id: 'policy-analysis',
    name: 'Policy Analysis',
    documentType: DocumentType.POLICY,
    description: 'Template for analyzing policy documents and corporate policies',
    template: `
    You are a legal AI assistant specializing in policy analysis. Please analyze the following policy document:

    {{documentContent}}

    Analyze the policy and provide the following information:
    1. Policy Purpose: Identify the main purpose and scope of the policy.
    2. Key Requirements: Outline the main requirements and guidelines set forth.
    3. Compliance Mechanisms: Explain how compliance with the policy is monitored and enforced.
    4. Reporting Procedures: Identify any reporting procedures for violations or concerns.
    5. Updates and Revisions: Note when the policy was last updated and any revision protocols.
    
    {{#if query}}
    User's specific question: {{query}}
    Please address this question specifically in your analysis.
    {{/if}}
    
    Format your response in clear sections with bullet points where appropriate.
    `,
  },
  {
    id: 'legislation-analysis',
    name: 'Legislation Analysis',
    documentType: DocumentType.LEGISLATION,
    description: 'Template for analyzing laws, statutes, and regulations',
    template: `
    You are a legal AI assistant specializing in legislative analysis. Please analyze the following legislation:

    {{documentContent}}

    Analyze the legislation and provide the following information:
    1. Purpose and Scope: Identify the purpose and scope of the legislation.
    2. Key Provisions: Summarize the most important provisions and requirements.
    3. Definitions: Note important defined terms in the legislation.
    4. Obligations Created: Identify obligations created for different parties.
    5. Penalties for Non-compliance: Explain penalties or consequences for violations.
    6. Effective Date and Transition Provisions: Note when the law takes effect.
    
    {{#if query}}
    User's specific question: {{query}}
    Please address this question specifically in your analysis.
    {{/if}}
    
    Format your response in a structured manner with section headings.
    `,
  },
  {
    id: 'court-filing-analysis',
    name: 'Court Filing Analysis',
    documentType: DocumentType.COURT_FILING,
    description: 'Template for analyzing court filings, briefs, and pleadings',
    template: `
    You are a legal AI assistant specializing in analyzing court filings. Please analyze the following court document:

    {{documentContent}}

    Analyze the court filing and provide the following information:
    1. Document Type: Identify the type of filing (complaint, motion, brief, etc.).
    2. Parties: Identify the parties involved in the case.
    3. Procedural Posture: Explain where this filing fits in the case's procedural history.
    4. Legal Claims/Arguments: Summarize the main legal claims or arguments.
    5. Relief Sought: Explain what the filing party is asking the court to do.
    6. Key Legal Authorities: Identify important cases, statutes, or rules cited.
    
    {{#if query}}
    User's specific question: {{query}}
    Please address this question specifically in your analysis.
    {{/if}}
    
    Format your response with clear headings and a structured analysis.
    `,
  },
  {
    id: 'general-legal-document',
    name: 'General Legal Document Analysis',
    documentType: DocumentType.GENERAL,
    description: 'Default template for any legal document not fitting other categories',
    template: `
    You are a legal AI assistant. Please analyze the following legal document:

    {{documentContent}}

    Provide a comprehensive analysis including:
    1. Document Type: Identify what kind of legal document this appears to be.
    2. Key Parties: Identify any parties mentioned and their roles.
    3. Primary Purpose: Explain the main purpose of this document.
    4. Key Provisions: Highlight the most important sections or clauses.
    5. Important Dates: Note any significant dates mentioned (effective dates, deadlines, etc.).
    6. Potential Issues: Identify any ambiguities, conflicts, or areas of concern.
    
    {{#if query}}
    User's specific question: {{query}}
    Please address this question specifically in your analysis.
    {{/if}}
    
    Format your response in a clear, organized manner that would be helpful to a legal professional.
    `,
  },
];

export const promptTemplatesConfig = registerAs('promptTemplates', () => {
  return {
    // Return templates from environment variables if provided, otherwise use defaults
    templates: process.env.CUSTOM_PROMPT_TEMPLATES
      ? JSON.parse(process.env.CUSTOM_PROMPT_TEMPLATES)
      : defaultTemplates,
      
    // Default document type to use if the document type cannot be determined
    defaultDocumentType: DocumentType.GENERAL,
  };
});

import { registerAs } from '@nestjs/config';

export const databaseConfig = registerAs('database', () => ({
  mongodb: {
    uri:
      process.env.MONGODB_URI ||
      'mongodb://localhost:27017/legal-document-analyzer',
    options: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    },
  },
  // Keep existing SQLite config for document cache during transition
  sqlite: {
    database: 'uploads/cache/document-cache.db',
  },
}));

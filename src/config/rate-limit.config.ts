import { registerAs } from '@nestjs/config';
import { SubscriptionTier } from '../modules/subscription/enums/subscription-tier.enum';

/**
 * Rate limit configuration for different API endpoints and subscription tiers
 */
export default registerAs('rateLimit', () => {
  const isProd = process.env.NODE_ENV === 'production';
  
  // Default rate limits - more restrictive in production
  const defaultLimits = {
    // Authentication endpoints
    auth: {
      login: {
        windowMs: 15 * 60 * 1000, // 15 minutes
        maxRequests: isProd ? 10 : 100, // 10 attempts in production, 100 in dev
      },
      register: {
        windowMs: 60 * 60 * 1000, // 1 hour
        maxRequests: isProd ? 5 : 100, // 5 attempts in production, 100 in dev
      },
    },
    
    // Subscription tier-based limits
    api: {
      // General API limits by subscription tier
      [SubscriptionTier.LAW_STUDENT]: {
        windowMs: 60 * 1000, // 1 minute
        maxRequests: isProd ? 30 : 1000,
      },
      [SubscriptionTier.LAWYER]: {
        windowMs: 60 * 1000, // 1 minute
        maxRequests: isProd ? 100 : 5000,
      },
      [SubscriptionTier.LAW_FIRM]: {
        windowMs: 60 * 1000, // 1 minute
        maxRequests: isProd ? 500 : 100000,
      },
    },
    
    // AI-specific endpoints (more resource intensive)
    ai: {
      [SubscriptionTier.LAW_STUDENT]: {
        windowMs: 60 * 1000, // 1 minute
        maxRequests: isProd ? 5 : 100,
      },
      [SubscriptionTier.LAWYER]: {
        windowMs: 60 * 1000, // 1 minute
        maxRequests: isProd ? 20 : 500,
      },
      [SubscriptionTier.LAW_FIRM]: {
        windowMs: 60 * 1000, // 1 minute
        maxRequests: isProd ? 100 : 10000,
      },
    },
    
    // Document upload limits
    upload: {
      [SubscriptionTier.LAW_STUDENT]: {
        windowMs: 60 * 60 * 1000, // 1 hour
        maxRequests: isProd ? 10 : 100,
      },
      [SubscriptionTier.LAWYER]: {
        windowMs: 60 * 60 * 1000, // 1 hour
        maxRequests: isProd ? 50 : 500,
      },
      [SubscriptionTier.LAW_FIRM]: {
        windowMs: 60 * 60 * 1000, // 1 hour
        maxRequests: isProd ? 500 : 10000,
      },
    },
  };

  return {
    enabled: isProd, // Only enable in production by default
    defaultLimits,
    ipWhitelist: process.env.RATE_LIMIT_IP_WHITELIST?.split(',') || [],
    throwOnLimit: true,
    maxQueueSize: 100,
  };
});

import { registerAs } from '@nestjs/config';

export const geminiConfig = registerAs('gemini', () => ({
  // Assuming you'll use GEMINI_API_KEY from your .env
  apiKey: process.env.GEMINI_API_KEY, 
  // Set default model to gemini-2.0-flash-001
  modelName: process.env.GEMINI_MODEL_NAME || 'gemini-2.0-flash-001', 
  temperature: parseFloat(process.env.GEMINI_TEMPERATURE || '0.7'),
  // Gemini often uses maxOutputTokens - check Gemini API docs for exact naming
  maxOutputTokens: parseInt(process.env.GEMINI_MAX_OUTPUT_TOKENS || '8192', 10), 
  retry: {
    maxRetries: parseInt(process.env.GEMINI_RETRY_MAX_ATTEMPTS || '3', 10),
    initialDelayMs: parseInt(
      process.env.GEMINI_RETRY_INITIAL_DELAY_MS || '1000',
      10,
    ),
    maxDelayMs: parseInt(process.env.GEMINI_RETRY_MAX_DELAY_MS || '10000', 10),
    backoffFactor: parseFloat(process.env.GEMINI_RETRY_BACKOFF_FACTOR || '2'),
  },
  // Check Gemini documentation for official rate limits and adjust defaults
  rateLimit: { 
    maxRequests: parseInt(
      process.env.GEMINI_RATE_LIMIT_MAX_REQUESTS || '60', // Example: 60 requests per minute
      10,
    ),
    windowMs: parseInt(process.env.GEMINI_RATE_LIMIT_WINDOW_MS || '60000', 10), // 1 minute window
    throwOnLimit: process.env.GEMINI_RATE_LIMIT_THROW_ERROR === 'true',
  },
}));

export type GeminiConfig = ReturnType<typeof geminiConfig>;

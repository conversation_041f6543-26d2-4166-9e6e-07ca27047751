import { registerAs } from '@nestjs/config';

export const auditConfig = registerAs('audit', () => ({
  // Number of days to retain audit logs before purging
  retentionDays: parseInt(process.env.AUDIT_LOG_RETENTION_DAYS || '90', 10),
  
  // Whether to log all document access events or just failures
  logAllDocumentAccess: process.env.AUDIT_LOG_ALL_DOCUMENT_ACCESS === 'true',
  
  // Whether to include document content in audit logs (not recommended for production)
  includeDocumentContent: process.env.AUDIT_INCLUDE_DOCUMENT_CONTENT === 'true',
  
  // Security event severity thresholds for notifications
  securityNotificationThreshold: process.env.SECURITY_NOTIFICATION_THRESHOLD || 'medium',
  
  // Whether to enable real-time security alerts
  enableRealTimeAlerts: process.env.ENABLE_REAL_TIME_SECURITY_ALERTS === 'true',
  
  // Email to send security alerts to
  securityAlertEmail: process.env.SECURITY_ALERT_EMAIL,
}));

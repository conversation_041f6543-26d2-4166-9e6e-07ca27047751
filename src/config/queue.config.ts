import { registerAs } from '@nestjs/config';

export const queueConfig = registerAs('queue', () => {
  // Use REDIS_URL if available, otherwise fall back to individual settings
  const redisConfig = process.env.REDIS_URL
    ? process.env.REDIS_URL
    : {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT, 10) || 6379,
        password: process.env.REDIS_PASSWORD || undefined,
      };

  return {
    redis: redisConfig,
    // Global queue configuration options
    defaultJobOptions: {
      attempts: 3,              // Number of retry attempts for failed jobs
      backoff: {
        type: 'exponential',    // Exponential backoff for retries
        delay: 1000,            // Starting delay in ms
      },
      removeOnComplete: true,   // Remove jobs from queue once completed
      removeOnFail: false,      // Keep failed jobs for inspection
    },
  };
});

import { registerAs } from '@nestjs/config';

export const queueConfig = registerAs('queue', () => {
  // Parse Redis URL if available, otherwise use individual settings
  let redisConfig;

  if (process.env.REDIS_URL) {
    // Parse Redis URL for Bull
    const url = new URL(process.env.REDIS_URL);
    redisConfig = {
      host: url.hostname,
      port: parseInt(url.port) || 6379,
      password: url.password || undefined,
      username: url.username || undefined,
      // Enable TLS for rediss:// URLs
      tls: url.protocol === 'rediss:' ? {} : undefined,
    };
  } else {
    redisConfig = {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT, 10) || 6379,
      password: process.env.REDIS_PASSWORD || undefined,
    };
  }

  return {
    redis: redisConfig,
    // Global queue configuration options
    defaultJobOptions: {
      attempts: 3,              // Number of retry attempts for failed jobs
      backoff: {
        type: 'exponential',    // Exponential backoff for retries
        delay: 1000,            // Starting delay in ms
      },
      removeOnComplete: true,   // Remove jobs from queue once completed
      removeOnFail: false,      // Keep failed jobs for inspection
    },
  };
});

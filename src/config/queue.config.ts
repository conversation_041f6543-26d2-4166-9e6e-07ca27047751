import { registerAs } from '@nestjs/config';

export const queueConfig = registerAs('queue', () => ({
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT, 10) || 6379,
    password: process.env.REDIS_PASSWORD || undefined,
  },
  // Global queue configuration options
  defaultJobOptions: {
    attempts: 3,              // Number of retry attempts for failed jobs
    backoff: {
      type: 'exponential',    // Exponential backoff for retries
      delay: 1000,            // Starting delay in ms
    },
    removeOnComplete: true,   // Remove jobs from queue once completed
    removeOnFail: false,      // Keep failed jobs for inspection
  },
}));

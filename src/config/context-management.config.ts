/**
 * Configuration for the context management system
 */
import { registerAs } from '@nestjs/config';

export default registerAs('contextManagement', () => ({
  // Maximum tokens to use for context in prompts
  maxContextTokens: parseInt(process.env.MAX_CONTEXT_TOKENS || '8000', 10),
  
  // Token budget allocation (percentages)
  tokenBudget: {
    document: parseInt(process.env.CONTEXT_BUDGET_DOCUMENT || '50', 10),
    chatHistory: parseInt(process.env.CONTEXT_BUDGET_CHAT_HISTORY || '30', 10),
    documentMetadata: parseInt(process.env.CONTEXT_BUDGET_DOCUMENT_METADATA || '10', 10),
    relatedDocuments: parseInt(process.env.CONTEXT_BUDGET_RELATED_DOCUMENTS || '10', 10),
  },
  
  // Maximum messages to include from chat history
  maxChatHistoryMessages: parseInt(process.env.MAX_CHAT_HISTORY_MESSAGES || '10', 10),
  
  // Whether to use context compression techniques
  useContextCompression: process.env.USE_CONTEXT_COMPRESSION === 'true',
  
  // Whether to include AI-generated follow-up suggestions
  includeFollowUpSuggestions: process.env.INCLUDE_FOLLOW_UP_SUGGESTIONS === 'true',
}))

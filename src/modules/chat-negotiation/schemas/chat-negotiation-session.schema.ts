import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type ChatNegotiationSessionDocument = ChatNegotiationSession & Document;

@Schema({ timestamps: true })
export class ChatNegotiationSession {
  @Prop({ required: true })
  userId: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'NegotiationSession', required: true })
  negotiationSessionId: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'ChatSession', required: true })
  chatSessionId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  scenarioId: string;

  // Document Integration Fields
  @Prop()
  sourceDocumentId?: string;

  @Prop()
  sourceAnalysisId?: string;

  @Prop({ type: Object })
  documentContext?: {
    contractType?: string;
    contractTitle?: string;
    analysisScore?: number;
    riskLevel?: string;
    totalDeviations?: number;
    keyFindings?: string[];
  };

  @Prop({ 
    type: String, 
    enum: ['active', 'paused', 'completed', 'abandoned'], 
    default: 'active' 
  })
  status: string;

  @Prop({ type: Number, default: 1 })
  currentRound: number;

  @Prop({ type: MongooseSchema.Types.Mixed, default: {} })
  extractedTerms: Record<string, any>;

  @Prop({
    type: {
      trust: { type: Number, default: 50, min: 0, max: 100 },
      respect: { type: Number, default: 50, min: 0, max: 100 },
      pressure: { type: Number, default: 20, min: 0, max: 100 }
    },
    default: { trust: 50, respect: 50, pressure: 20 }
  })
  relationshipMetrics: {
    trust: number;
    respect: number;
    pressure: number;
  };

  @Prop({ type: Number, default: 5.0, min: 0, max: 10 })
  score: number;

  @Prop({ type: MongooseSchema.Types.Mixed, default: {} })
  aiPersonality: Record<string, any>;

  @Prop({ required: true })
  organizationId: string;

  @Prop({ type: MongooseSchema.Types.Mixed, default: {} })
  metadata: Record<string, any>;

  @Prop({ type: Date, default: Date.now })
  lastActivityAt: Date;

  @Prop({ type: Number, default: 0 })
  totalMessages: number;

  @Prop({ type: Number, default: 0 })
  aiResponseTime: number; // Average AI response time in ms
}

export const ChatNegotiationSessionSchema = SchemaFactory.createForClass(ChatNegotiationSession);

// Indexes for performance
ChatNegotiationSessionSchema.index({ userId: 1, status: 1 });
ChatNegotiationSessionSchema.index({ organizationId: 1 });
ChatNegotiationSessionSchema.index({ scenarioId: 1 });
ChatNegotiationSessionSchema.index({ createdAt: -1 });
ChatNegotiationSessionSchema.index({ lastActivityAt: -1 });

// Virtual for session duration
ChatNegotiationSessionSchema.virtual('duration').get(function() {
  return (this as any).updatedAt.getTime() - (this as any).createdAt.getTime();
});

// Pre-save middleware to update lastActivityAt
ChatNegotiationSessionSchema.pre('save', function(next) {
  this.lastActivityAt = new Date();
  next();
});

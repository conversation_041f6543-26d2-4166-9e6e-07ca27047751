import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type ChatMoveDataDocument = ChatMoveData & Document;

@Schema({ timestamps: true })
export class ChatMoveData {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'ChatNegotiationSession', required: true })
  chatNegotiationSessionId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  messageContent: string;

  @Prop({ type: String, enum: ['user', 'ai'], required: true })
  sender: string;

  @Prop({ type: MongooseSchema.Types.Mixed, default: {} })
  extractedData: Record<string, any>;

  @Prop({ type: MongooseSchema.Types.Mixed, default: {} })
  relationshipImpact: {
    trustChange?: number;
    respectChange?: number;
    pressureChange?: number;
  };

  @Prop({ type: Number, default: 0 })
  scoreImpact: number;

  @Prop({ type: Number })
  processingTimeMs: number;

  @Prop({ type: Number, min: 0, max: 1, default: 0.5 })
  confidence: number;

  @Prop({ type: String, enum: ['collaborative', 'competitive', 'accommodating', 'analytical'] })
  detectedStrategy: string;

  @Prop({ type: String, enum: ['positive', 'neutral', 'negative'] })
  sentiment: string;

  @Prop({ type: [String], default: [] })
  extractedEntities: string[];

  @Prop({ type: MongooseSchema.Types.Mixed, default: {} })
  aiResponseMetadata: Record<string, any>;

  @Prop({ required: true })
  organizationId: string;
}

export const ChatMoveDataSchema = SchemaFactory.createForClass(ChatMoveData);

// Indexes for analytics and performance
ChatMoveDataSchema.index({ chatNegotiationSessionId: 1, createdAt: 1 });
ChatMoveDataSchema.index({ userId: 1, sender: 1 });
ChatMoveDataSchema.index({ organizationId: 1 });
ChatMoveDataSchema.index({ detectedStrategy: 1 });
ChatMoveDataSchema.index({ sentiment: 1 });
ChatMoveDataSchema.index({ confidence: 1 });

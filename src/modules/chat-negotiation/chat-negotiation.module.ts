import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ChatNegotiationController } from './controllers/chat-negotiation.controller';
import { ChatNegotiationService } from './services/chat-negotiation.service';
import { DocumentNegotiationService } from './services/document-negotiation.service';
import { 
  ChatNegotiationSession, 
  ChatNegotiationSessionSchema 
} from './schemas/chat-negotiation-session.schema';
import {
  ChatMoveData,
  ChatMoveDataSchema
} from './schemas/chat-move-data.schema';

import { DocumentsModule } from '../documents/documents.module';
import { GamificationModule } from '../gamification/gamification.module';
import { AuthModule } from '../auth/auth.module';
import { AiConversationService } from './services/ai-conversation.service';
import { AIModule } from '../ai/ai.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ChatNegotiationSession.name, schema: ChatNegotiationSessionSchema },
      { name: ChatMoveData.name, schema: ChatMoveDataSchema },
    ]),
    forwardRef(() => DocumentsModule), // For NegotiationSimulatorService
    forwardRef(() => GamificationModule), // For GamificationService
    AIModule, // For AIService
    AuthModule // For TenantContextService
  ],
  controllers: [ChatNegotiationController],
  providers: [ChatNegotiationService, DocumentNegotiationService, AiConversationService],
  exports: [ChatNegotiationService, AiConversationService]
})
export class ChatNegotiationModule {}

import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Req,
  UseGuards,
  HttpStatus,
  HttpCode,
  Logger
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { ChatNegotiationService } from '../services/chat-negotiation.service';
import { DocumentNegotiationService } from '../services/document-negotiation.service';
import { NegotiationSimulatorService } from '../../documents/services/negotiation-simulator.service';
import {
  CreateChatNegotiationSessionDto,
  ChatMoveDto,
  GetSessionsQueryDto,
  ExtractDataDto
} from '../dto/create-chat-negotiation-session.dto';
import {
  CreateNegotiationScenarioDto
} from '../../documents/dto/negotiation-simulator.dto';
import {
  NegotiationScenario
} from '../../documents/interfaces/negotiation-simulator.interface';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { AiConversationService } from '../services/ai-conversation.service';
import { FreeFeature } from '../../subscription/decorators/use-credits.decorator';

@ApiTags('Chat Negotiation')
@Controller('chat-negotiation')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ChatNegotiationController {
  private readonly logger = new Logger(ChatNegotiationController.name);

  constructor(
    private chatNegotiationService: ChatNegotiationService,
    private tenantContextService: TenantContextService,
    private documentNegotiationService: DocumentNegotiationService,
    private negotiationSimulatorService: NegotiationSimulatorService,
    private aiConversationService: AiConversationService,
  ) {}

  private getCurrentUserContext(): { userId: string; organizationId: string } {
    const userId = this.tenantContextService.getCurrentUserId();
    const organizationId = this.tenantContextService.getCurrentOrganization();

    if (!userId || !organizationId) {
      throw new Error('User context not found');
    }

    return { userId, organizationId };
  }

  // ============ SCENARIO MANAGEMENT ENDPOINTS ============

  @Post('scenarios')
  @FreeFeature() // Creating scenarios is free
  @ApiOperation({
    summary: 'Create a new custom negotiation scenario',
    description: 'Creates a new custom scenario for chat negotiation practice'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Scenario created successfully',
    schema: {
      example: {
        id: '507f1f77bcf86cd799439011',
        name: 'Software License Negotiation',
        description: 'Practice negotiating software licensing terms',
        industry: 'Technology',
        contractType: 'Software License',
        difficulty: 'intermediate',
        parties: [
          {
            name: 'Software Buyer',
            role: 'licensee',
            priorities: ['Cost control', 'Flexible terms'],
            negotiationStyle: 'collaborative'
          }
        ],
        tags: ['software', 'licensing'],
        createdBy: '507f1f77bcf86cd799439012',
        createdAt: '2025-01-06T10:00:00.000Z'
      }
    }
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid scenario data' })
  async createScenario(
    @Body() dto: CreateNegotiationScenarioDto,
  ): Promise<NegotiationScenario> {
    const { userId, organizationId } = this.getCurrentUserContext();

    this.logger.log(`Creating custom scenario: ${dto.name} for user: ${userId}`);

    return this.negotiationSimulatorService.createScenario(
      dto,
      userId,
      organizationId,
    );
  }

  @Get('scenarios')
  @ApiOperation({
    summary: 'Get available negotiation scenarios',
    description: 'Retrieves scenarios including user-created and template scenarios'
  })
  @ApiQuery({ name: 'industry', required: false, description: 'Filter by industry' })
  @ApiQuery({ name: 'contractType', required: false, description: 'Filter by contract type' })
  @ApiQuery({ name: 'difficulty', required: false, description: 'Filter by difficulty level' })
  @ApiQuery({ name: 'tags', required: false, description: 'Filter by tags (comma-separated)' })
  @ApiQuery({ name: 'includeTemplates', required: false, type: Boolean, description: 'Include templates (default: true)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Scenarios retrieved successfully',
    schema: {
      example: [
        {
          id: '507f1f77bcf86cd799439011',
          name: 'Software License Negotiation',
          description: 'Practice negotiating software licensing terms',
          industry: 'Technology',
          contractType: 'Software License',
          difficulty: 'intermediate',
          tags: ['software', 'licensing'],
          createdAt: '2025-01-06T10:00:00.000Z'
        }
      ]
    }
  })
  async getScenarios(
    @Query('industry') industry?: string,
    @Query('contractType') contractType?: string,
    @Query('difficulty') difficulty?: string,
    @Query('tags') tags?: string,
    @Query('includeTemplates') includeTemplates?: boolean,
  ): Promise<NegotiationScenario[]> {
    const { organizationId } = this.getCurrentUserContext();

    const filters: any = {};
    if (industry) filters.industry = industry;
    if (contractType) filters.contractType = contractType;
    if (difficulty) filters.difficulty = difficulty;
    if (tags) filters.tags = tags.split(',').map((tag) => tag.trim());

    return this.negotiationSimulatorService.getScenarios(organizationId, filters);
  }

  @Get('scenarios/:id')
  @ApiOperation({
    summary: 'Get scenario by ID',
    description: 'Retrieve a specific negotiation scenario by its ID'
  })
  @ApiParam({ name: 'id', description: 'Scenario ID' })
  @ApiResponse({
    status: 200,
    description: 'Scenario retrieved successfully',
    schema: {
      example: {
        id: "68405c9d35f27e4188e67b72",
        name: "Software License Negotiation",
        description: "Practice scenario for software licensing",
        difficulty: "intermediate",
        parties: [],
        constraints: {},
        timeline: {},
        createdAt: "2025-06-16T08:23:58.888Z"
      }
    }
  })
  @ApiResponse({ status: 404, description: 'Scenario not found' })
  async getScenarioById(@Param('id') id: string): Promise<any> {
    this.logger.log(`Getting scenario by ID: ${id}`);
    const { organizationId } = this.getCurrentUserContext();
    return this.negotiationSimulatorService.getScenario(id, organizationId);
  }

  @Get('scenarios/templates')
  @ApiOperation({
    summary: 'Get template negotiation scenarios',
    description: 'Retrieves pre-built template scenarios available for all users'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Template scenarios retrieved successfully',
    schema: {
      example: [
        {
          id: 'template-507f1f77bcf86cd799439011',
          name: 'Employment Contract Template',
          description: 'Standard template for employment negotiations',
          isTemplate: true,
          difficulty: 'intermediate',
          industry: 'Human Resources'
        }
      ]
    }
  })
  async getTemplateScenarios(): Promise<NegotiationScenario[]> {
    const { organizationId } = this.getCurrentUserContext();
    return this.negotiationSimulatorService.getTemplateScenarios(organizationId);
  }

  @Post('scenarios/:scenarioId/clone')
  @ApiOperation({
    summary: 'Clone a negotiation scenario',
    description: 'Creates a copy of an existing scenario for customization'
  })
  @ApiParam({ name: 'scenarioId', description: 'Scenario ID to clone' })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Scenario cloned successfully',
    schema: {
      example: {
        id: '507f1f77bcf86cd799439013',
        name: 'My Custom Software License Scenario',
        description: 'Customized for my practice needs',
        industry: 'Technology',
        contractType: 'Software License',
        difficulty: 'intermediate',
        createdBy: '507f1f77bcf86cd799439012',
        createdAt: '2025-01-06T11:00:00.000Z'
      }
    }
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Scenario not found' })
  async cloneScenario(
    @Param('scenarioId') scenarioId: string,
    @Body() dto?: { name?: string; description?: string },
  ): Promise<NegotiationScenario> {
    const { userId, organizationId } = this.getCurrentUserContext();

    this.logger.log(`Cloning scenario: ${scenarioId} by user: ${userId}`);

    return this.negotiationSimulatorService.cloneScenario(
      scenarioId,
      userId,
      organizationId,
      dto,
    );
  }

  @Put('scenarios/:scenarioId')
  @ApiOperation({
    summary: 'Update a negotiation scenario',
    description: 'Updates an existing scenario (only creator can update)'
  })
  @ApiParam({ name: 'scenarioId', description: 'Scenario ID to update' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Scenario updated successfully'
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Scenario not found' })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Only creator can update' })
  async updateScenario(
    @Param('scenarioId') scenarioId: string,
    @Body() dto: Partial<CreateNegotiationScenarioDto>,
  ): Promise<NegotiationScenario> {
    const { userId, organizationId } = this.getCurrentUserContext();

    this.logger.log(`Updating scenario: ${scenarioId} by user: ${userId}`);

    return this.negotiationSimulatorService.updateScenario(
      scenarioId,
      dto,
      userId,
      organizationId,
    );
  }

  @Delete('scenarios/:scenarioId')
  @ApiOperation({
    summary: 'Delete a negotiation scenario',
    description: 'Deletes a custom scenario (only creator can delete)'
  })
  @ApiParam({ name: 'scenarioId', description: 'Scenario ID to delete' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Scenario deleted successfully',
    schema: {
      example: {
        message: 'Scenario deleted successfully',
        deletedScenarioId: '507f1f77bcf86cd799439011'
      }
    }
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Scenario not found' })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Only creator can delete' })
  @HttpCode(HttpStatus.OK)
  async deleteScenario(
    @Param('scenarioId') scenarioId: string,
  ): Promise<{ message: string; deletedScenarioId: string }> {
    const { userId, organizationId } = this.getCurrentUserContext();

    this.logger.log(`Deleting scenario: ${scenarioId} by user: ${userId}`);

    await this.negotiationSimulatorService.deleteScenario(
      scenarioId,
      userId,
      organizationId,
    );

    return {
      message: 'Scenario deleted successfully',
      deletedScenarioId: scenarioId,
    };
  }

  // ============ SESSION MANAGEMENT ENDPOINTS ============

  @Post('sessions')
  @ApiOperation({ 
    summary: 'Create a new chat negotiation session',
    description: 'Creates a new chat-based negotiation session that bridges chat interface with negotiation simulator'
  })
  @ApiResponse({ 
    status: HttpStatus.CREATED, 
    description: 'Chat negotiation session created successfully',
    schema: {
      example: {
        id: '507f1f77bcf86cd799439011',
        scenarioId: 'software_licensing',
        status: 'active',
        currentRound: 1,
        relationshipMetrics: { trust: 50, respect: 50, pressure: 20 },
        score: 5.0,
        aiPersonality: { characterId: 'default_character', aggressiveness: 0.4 },
        negotiationSessionId: '507f1f77bcf86cd799439012',
        createdAt: '2025-01-06T10:00:00.000Z'
      }
    }
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid scenario ID or parameters' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Authentication required' })
  async createSession(
    @Body() createSessionDto: CreateChatNegotiationSessionDto,
    @Req() req: any
  ) {
    const userId = this.tenantContextService.getCurrentUserId();
    
    this.logger.log(`Creating chat negotiation session for user ${userId}, scenario ${createSessionDto.scenarioId}`);
    
    return await this.chatNegotiationService.createSession(
      userId,
      createSessionDto.scenarioId,
      createSessionDto.aiPersonality,
      createSessionDto.metadata
    );
  }

  @Get('sessions/:id')
  @ApiOperation({ 
    summary: 'Get chat negotiation session details',
    description: 'Retrieves detailed information about a specific chat negotiation session'
  })
  @ApiParam({ name: 'id', description: 'Chat negotiation session ID' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Session details retrieved successfully',
    schema: {
      example: {
        id: '507f1f77bcf86cd799439011',
        scenarioId: 'software_licensing',
        status: 'active',
        currentRound: 3,
        extractedTerms: { price: 50000, currency: 'USD' },
        relationshipMetrics: { trust: 65, respect: 58, pressure: 25 },
        score: 6.2,
        totalMessages: 6,
        aiResponseTime: 1250,
        createdAt: '2025-01-06T10:00:00.000Z',
        lastActivityAt: '2025-01-06T10:15:00.000Z'
      }
    }
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Session not found' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Authentication required' })
  async getSession(@Param('id') id: string, @Req() req: any) {
    const userId = this.tenantContextService.getCurrentUserId();
    return await this.chatNegotiationService.getChatNegotiationSession(id, userId);
  }

  @Get('sessions')
  @ApiOperation({ 
    summary: 'Get user chat negotiation sessions',
    description: 'Retrieves a list of chat negotiation sessions for the current user'
  })
  @ApiQuery({ name: 'status', required: false, enum: ['active', 'paused', 'completed', 'abandoned'] })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of sessions to return (default: 20)' })
  @ApiQuery({ name: 'offset', required: false, type: Number, description: 'Number of sessions to skip (default: 0)' })
  @ApiQuery({ name: 'scenarioId', required: false, type: String, description: 'Filter by scenario ID' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Sessions retrieved successfully',
    schema: {
      example: {
        sessions: [
          {
            id: '507f1f77bcf86cd799439011',
            scenarioId: 'software_licensing',
            status: 'active',
            currentRound: 3,
            relationshipMetrics: { trust: 65, respect: 58, pressure: 25 },
            score: 6.2,
            totalMessages: 6,
            createdAt: '2025-01-06T10:00:00.000Z',
            lastActivityAt: '2025-01-06T10:15:00.000Z'
          }
        ],
        total: 1,
        limit: 20,
        offset: 0
      }
    }
  })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Authentication required' })
  async getUserSessions(@Query() query: GetSessionsQueryDto, @Req() req: any) {
    const userId = this.tenantContextService.getCurrentUserId();
    return await this.chatNegotiationService.getUserSessions(userId, query);
  }

  @Post('sessions/:id/moves')
  @ApiOperation({ 
    summary: 'Send a chat message and process negotiation move',
    description: 'Processes a chat message, extracts negotiation data, generates AI response, and updates session state'
  })
  @ApiParam({ name: 'id', description: 'Chat negotiation session ID' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Chat move processed successfully',
    schema: {
      example: {
        userMessage: {
          content: 'I am thinking around $50k for the license',
          extractedData: {
            offer: { price: 50000, currency: 'USD' },
            strategy: 'collaborative',
            sentiment: 'positive',
            confidence: 0.8
          },
          timestamp: '2025-01-06T10:15:00.000Z'
        },
        aiResponse: {
          content: "That's an interesting starting point. Let me think about how we can make this work.",
          suggestions: [
            "What's most important to you in this deal?",
            "Are there other terms we should discuss?"
          ],
          extractedData: { strategy: 'collaborative', sentiment: 'positive' },
          timestamp: '2025-01-06T10:15:02.000Z'
        },
        sessionUpdate: {
          id: '507f1f77bcf86cd799439011',
          currentRound: 4,
          relationshipMetrics: { trust: 66, respect: 59, pressure: 24 },
          score: 6.3,
          extractedTerms: { price: 50000, currency: 'USD' }
        },
        processingTime: 1250
      }
    }
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Session not found' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid move data' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Authentication required' })
  async sendMove(
    @Param('id') id: string,
    @Body() moveDto: ChatMoveDto,
    @Req() req: any
  ) {
    const userId = this.tenantContextService.getCurrentUserId();
    
    this.logger.log(`Processing chat move for session ${id}, user ${userId}`);
    
    return await this.chatNegotiationService.processChatMove(
      id,
      userId,
      moveDto.content,
      moveDto.extractedData,
      moveDto.context
    );
  }

  @Post('extract-data')
  @ApiOperation({ 
    summary: 'Extract structured data from natural language message',
    description: 'Analyzes a natural language message and extracts negotiation-relevant data like offers, strategies, and sentiment'
  })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Data extracted successfully',
    schema: {
      example: {
        offer: {
          price: 75000,
          currency: 'USD',
          terms: ['Net 30', 'flexible payment']
        },
        strategy: 'collaborative',
        sentiment: 'positive',
        confidence: 0.85,
        extractedEntities: ['price', 'payment_terms'],
        processingTime: 150
      }
    }
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid message content' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Authentication required' })
  async extractData(@Body() extractDataDto: ExtractDataDto) {
    const startTime = Date.now();
    
    // Basic extraction for MVP - will be enhanced in Phase 2
    const extractedData = await this.extractBasicData(extractDataDto.message, extractDataDto.context);
    
    return {
      ...extractedData,
      processingTime: Date.now() - startTime
    };
  }

  private async extractBasicData(message: string, context?: any) {
    const result: any = {
      offer: {},
      strategy: 'collaborative',
      sentiment: 'neutral',
      confidence: 0.5,
      extractedEntities: []
    };

    // Extract price
    const priceMatch = message.match(/\$?([\d,]+(?:\.\d+)?)\s*(k|thousand|m|million)?/i);
    if (priceMatch) {
      let price = parseFloat(priceMatch[1].replace(/,/g, ''));
      const multiplier = priceMatch[2];
      if (multiplier) {
        const lowerMultiplier = multiplier.toLowerCase();
        if (lowerMultiplier === 'k' || lowerMultiplier === 'thousand') {
          price *= 1000;
        } else if (lowerMultiplier === 'm' || lowerMultiplier === 'million') {
          price *= 1000000;
        }
      }
      result.offer.price = price;
      result.offer.currency = 'USD';
      result.extractedEntities.push('price');
      result.confidence += 0.2;
    }

    // Extract payment terms
    const paymentTerms = [];
    if (message.toLowerCase().includes('net 30')) paymentTerms.push('Net 30');
    if (message.toLowerCase().includes('net 60')) paymentTerms.push('Net 60');
    if (message.toLowerCase().includes('upfront')) paymentTerms.push('Upfront payment');
    if (message.toLowerCase().includes('installment')) paymentTerms.push('Installment payment');
    
    if (paymentTerms.length > 0) {
      result.offer.terms = paymentTerms;
      result.extractedEntities.push('payment_terms');
      result.confidence += 0.1;
    }

    // Detect strategy
    const lowerMessage = message.toLowerCase();
    if (lowerMessage.includes('win-win') || lowerMessage.includes('together') || lowerMessage.includes('mutual')) {
      result.strategy = 'collaborative';
      result.confidence += 0.1;
    } else if (lowerMessage.includes('need') || lowerMessage.includes('must') || lowerMessage.includes('final')) {
      result.strategy = 'competitive';
      result.confidence += 0.1;
    } else if (lowerMessage.includes('flexible') || lowerMessage.includes('open') || lowerMessage.includes('willing')) {
      result.strategy = 'accommodating';
      result.confidence += 0.1;
    } else if (lowerMessage.includes('data') || lowerMessage.includes('market') || lowerMessage.includes('research')) {
      result.strategy = 'analytical';
      result.confidence += 0.1;
    }

    // Detect sentiment
    const positiveWords = ['great', 'excellent', 'perfect', 'love', 'excited', 'happy', 'pleased'];
    const negativeWords = ['difficult', 'problem', 'issue', 'concern', 'worried', 'disappointed', 'challenging'];
    
    const positiveCount = positiveWords.filter(word => lowerMessage.includes(word)).length;
    const negativeCount = negativeWords.filter(word => lowerMessage.includes(word)).length;
    
    if (positiveCount > negativeCount) {
      result.sentiment = 'positive';
      result.confidence += 0.1;
    } else if (negativeCount > positiveCount) {
      result.sentiment = 'negative';
      result.confidence += 0.1;
    }

    // Cap confidence at 1.0
    result.confidence = Math.min(1.0, result.confidence);

    return result;
  }

  @Post('scenarios/from-analysis')
  @ApiOperation({
    summary: 'Create negotiation scenario from contract analysis',
    description: 'Generate a negotiation scenario based on contract analysis results'
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        analysisId: { type: 'string', description: 'Contract analysis ID' }
      },
      required: ['analysisId']
    }
  })
  async createScenarioFromAnalysis(
    @Body() body: { analysisId: string },
    @Req() req: any,
  ) {
    const organizationId = this.tenantContextService.getCurrentOrganization();

    const scenario = await this.documentNegotiationService.createScenarioFromAnalysis(
      body.analysisId,
      organizationId,
    );

    return scenario;
  }

  @Post('sessions/from-analysis')
  @ApiOperation({
    summary: 'Create negotiation session from a contract analysis ID',
    description: 'Start a negotiation practice session based on a previously completed contract analysis.'
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        analysisId: { type: 'string', description: 'Contract analysis ID' },
        aiPersonality: {
          type: 'object',
          properties: {
            characterId: { type: 'string' },
            aggressiveness: { type: 'number', minimum: 0, maximum: 1 },
            flexibility: { type: 'number', minimum: 0, maximum: 1 },
            riskTolerance: { type: 'number', minimum: 0, maximum: 1 },
            communicationStyle: {
              type: 'string',
              enum: ['DIRECT', 'DIPLOMATIC', 'ANALYTICAL', 'EMOTIONAL']
            }
          }
        }
      },
      required: ['analysisId']
    }
  })
  async createSessionFromAnalysis(
    @Body() body: { analysisId: string; aiPersonality?: any },
    @Req() req: any,
  ) {
    const userId = this.tenantContextService.getCurrentUserId();
    const organizationId = this.tenantContextService.getCurrentOrganization();

    const session = await this.documentNegotiationService.createSessionFromAnalysis(
      body.analysisId,
      organizationId,
      userId,
      body.aiPersonality,
    );

    return session;
  }

  @Get('sessions/:id/document-context')
  @ApiOperation({
    summary: 'Get document context for negotiation session',
    description: 'Retrieve contract analysis context for document-based negotiation session'
  })
  @ApiParam({ name: 'id', description: 'Session ID' })
  async getDocumentContext(
    @Param('id') sessionId: string,
    @Req() req: any,
  ) {
    const context = await this.documentNegotiationService.getNegotiationContext(sessionId);

    return {
      context,
      hasDocumentContext: !!context,
    };
  }

  @Get('sessions/:id/messages')
  @ApiOperation({
    summary: 'Get conversation history for a negotiation session',
    description: 'Retrieve the complete message history between user and AI for a specific negotiation session'
  })
  @ApiParam({ name: 'id', description: 'Chat negotiation session ID' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of messages to return (default: 50)' })
  @ApiQuery({ name: 'offset', required: false, type: Number, description: 'Number of messages to skip (default: 0)' })
  @ApiQuery({ name: 'includeMetadata', required: false, type: Boolean, description: 'Include analytics metadata (default: false)' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Conversation history retrieved successfully',
    schema: {
      example: {
        sessionId: '685193186b3ee0bcf9ef5502',
        messages: [
          {
            id: '685194336b3ee0bcf9ef5f34',
            content: 'I have concerns about the data protection clause. Can we discuss modifying this?',
            sender: 'user',
            timestamp: '2025-06-17T16:13:10.342Z',
            extractedData: {
              strategy: 'collaborative',
              sentiment: 'negative',
              topic: 'data_protection',
              confidence: 0.5
            }
          },
          {
            id: '685194336b3ee0bcf9ef5f35',
            content: 'Good point about the terms. Our analysis identified several areas for improvement.',
            sender: 'ai',
            timestamp: '2025-06-17T16:13:10.360Z',
            extractedData: {
              strategy: 'collaborative',
              sentiment: 'positive'
            }
          }
        ],
        pagination: {
          total: 4,
          limit: 50,
          offset: 0,
          hasMore: false
        },
        sessionInfo: {
          currentRound: 3,
          totalMessages: 4,
          score: 0.7,
          status: 'active',
          relationshipMetrics: {
            trust: 52,
            respect: 51,
            pressure: 19
          }
        }
      }
    }
  })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Session not found' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Authentication required' })
  async getConversationHistory(
    @Param('id') sessionId: string,
    @Req() req: any,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
    @Query('includeMetadata') includeMetadata?: boolean
  ) {
    const userId = this.tenantContextService.getCurrentUserId();
    
    this.logger.log(`Retrieving conversation history for session ${sessionId}, user ${userId}`);
    
    return await this.chatNegotiationService.getConversationHistory(
      sessionId,
      userId,
      { limit, offset, includeMetadata }
    );
  }
}

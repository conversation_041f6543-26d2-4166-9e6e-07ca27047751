import { IsString, IsOptional, IsObject, IsEnum } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateChatNegotiationSessionDto {
  @ApiProperty({
    description: 'ID of the negotiation scenario',
    example: 'software_licensing'
  })
  @IsString()
  scenarioId: string;

  @ApiPropertyOptional({
    description: 'AI personality configuration',
    example: {
      characterId: 'sarah_chen',
      aggressiveness: 0.6,
      flexibility: 0.7,
      riskTolerance: 0.5,
      communicationStyle: 'ANALYTICAL'
    }
  })
  @IsOptional()
  @IsObject()
  aiPersonality?: {
    characterId?: string;
    aggressiveness?: number;
    flexibility?: number;
    riskTolerance?: number;
    communicationStyle?: string;
    [key: string]: any;
  };

  @ApiPropertyOptional({
    description: 'Additional metadata for the session',
    example: { source: 'web_app', version: '1.0' }
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

export class ChatMoveDto {
  @ApiProperty({
    description: 'The chat message content',
    example: 'I am thinking around $50k for the license with flexible payment terms'
  })
  @IsString()
  content: string;

  @ApiPropertyOptional({
    description: 'Pre-extracted data from the frontend (optional)',
    example: {
      offer: { price: 50000, currency: 'USD' },
      strategy: 'collaborative',
      sentiment: 'positive'
    }
  })
  @IsOptional()
  @IsObject()
  extractedData?: {
    offer?: {
      price?: number;
      currency?: string;
      terms?: string[];
      [key: string]: any;
    };
    strategy?: string;
    sentiment?: string;
    confidence?: number;
    [key: string]: any;
  };

  @ApiPropertyOptional({
    description: 'AI personality configuration for this move',
    example: {
      characterId: 'alex_johnson',
      aggressiveness: 0.8,
      flexibility: 0.4,
      riskTolerance: 0.7,
      communicationStyle: 'DIRECT',
      decisionSpeed: 'FAST',
      concessionPattern: 'LATE'
    }
  })
  @IsOptional()
  @IsObject()
  aiPersonality?: {
    characterId?: string;
    aggressiveness?: number;
    flexibility?: number;
    riskTolerance?: number;
    communicationStyle?: string;
    decisionSpeed?: 'FAST' | 'MODERATE' | 'SLOW';
    concessionPattern?: 'EARLY' | 'GRADUAL' | 'LATE' | 'STRATEGIC';
    [key: string]: any;
  };

  @ApiPropertyOptional({
    description: 'Additional context for processing the move',
    example: { userConfidence: 0.8, timeSpent: 45 }
  })
  @IsOptional()
  @IsObject()
  context?: Record<string, any>;
}

export class GetSessionsQueryDto {
  @ApiPropertyOptional({
    description: 'Filter by session status',
    enum: ['active', 'paused', 'completed', 'abandoned']
  })
  @IsOptional()
  @IsEnum(['active', 'paused', 'completed', 'abandoned'])
  status?: string;

  @ApiPropertyOptional({
    description: 'Number of sessions to return',
    example: 20,
    default: 20
  })
  @IsOptional()
  limit?: number;

  @ApiPropertyOptional({
    description: 'Number of sessions to skip',
    example: 0,
    default: 0
  })
  @IsOptional()
  offset?: number;

  @ApiPropertyOptional({
    description: 'Filter by scenario ID',
    example: 'software_licensing'
  })
  @IsOptional()
  @IsString()
  scenarioId?: string;
}

export class ExtractDataDto {
  @ApiProperty({
    description: 'The message to extract data from',
    example: 'I propose $75,000 with Net 30 payment terms'
  })
  @IsString()
  message: string;

  @ApiPropertyOptional({
    description: 'Context for better extraction',
    example: {
      scenarioType: 'software_licensing',
      previousOffers: [{ price: 60000 }],
      currentRound: 3
    }
  })
  @IsOptional()
  @IsObject()
  context?: {
    scenarioType?: string;
    previousOffers?: any[];
    currentRound?: number;
    [key: string]: any;
  };
}

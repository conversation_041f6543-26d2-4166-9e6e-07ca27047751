import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ChatNegotiationSession, ChatNegotiationSessionDocument } from '../schemas/chat-negotiation-session.schema';
import { ChatMoveData, ChatMoveDataDocument } from '../schemas/chat-move-data.schema';
import { NegotiationSimulatorService } from '../../documents/services/negotiation-simulator.service';
import { GamificationService } from '../../gamification/services/gamification.service';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { DocumentNegotiationService } from './document-negotiation.service';
import { AiConversationService } from './ai-conversation.service';

@Injectable()
export class ChatNegotiationService {
  private readonly logger = new Logger(ChatNegotiationService.name);

  constructor(
    @InjectModel(ChatNegotiationSession.name)
    private chatNegotiationModel: Model<ChatNegotiationSessionDocument>,
    @InjectModel(ChatMoveData.name)
    private chatMoveDataModel: Model<ChatMoveDataDocument>,
    private negotiationSimulatorService: NegotiationSimulatorService,
    private gamificationService: GamificationService,
    private tenantContextService: TenantContextService,
    private documentNegotiationService: DocumentNegotiationService,
    private aiConversationService: AiConversationService,
  ) {}

  async createSession(userId: string, scenarioId: string, aiPersonality?: any, metadata?: any) {
    const organizationId = this.tenantContextService.getCurrentOrganization();
    
    try {
      this.logger.log(`Creating chat negotiation session for user ${userId}, scenario ${scenarioId}`);

      // 1. Get scenario details to validate
      const scenarios = await this.negotiationSimulatorService.getScenarios(organizationId);
      const scenario = scenarios.find(s => s.id === scenarioId);
      if (!scenario) {
        throw new BadRequestException(`Scenario ${scenarioId} not found`);
      }

      // 2. Create negotiation session using existing service
      const negotiationSession = await this.negotiationSimulatorService.startSession(
        { scenarioId },
        userId,
        organizationId
      );

      // 3. Create chat negotiation bridge session
      const chatNegotiationSession = new this.chatNegotiationModel({
        userId,
        negotiationSessionId: (negotiationSession as any)._id,
        chatSessionId: (negotiationSession as any)._id, // For now, use same ID - will integrate with chat service later
        scenarioId,
        aiPersonality: aiPersonality || this.getDefaultPersonality(scenarioId),
        organizationId,
        metadata: metadata || {}
      });

      const savedSession = await chatNegotiationSession.save();
      
      this.logger.log(`Chat negotiation session created: ${savedSession._id}`);
      
      return {
        id: savedSession._id,
        scenarioId: savedSession.scenarioId,
        status: savedSession.status,
        currentRound: savedSession.currentRound,
        relationshipMetrics: savedSession.relationshipMetrics,
        score: savedSession.score,
        aiPersonality: savedSession.aiPersonality,
        negotiationSessionId: savedSession.negotiationSessionId,
        createdAt: (savedSession as any).createdAt
      };

    } catch (error) {
      this.logger.error(`Failed to create chat negotiation session: ${error.message}`);
      throw error;
    }
  }

  async getChatNegotiationSession(sessionId: string, userId: string) {
    const organizationId = this.tenantContextService.getCurrentOrganization();
    
    const session = await this.chatNegotiationModel.findOne({
      _id: sessionId,
      userId,
      organizationId
    }).exec();

    if (!session) {
      throw new NotFoundException('Chat negotiation session not found');
    }

    return {
      id: session._id,
      scenarioId: session.scenarioId,
      status: session.status,
      currentRound: session.currentRound,
      extractedTerms: session.extractedTerms,
      relationshipMetrics: session.relationshipMetrics,
      score: session.score,
      aiPersonality: session.aiPersonality,
      negotiationSessionId: session.negotiationSessionId,
      totalMessages: session.totalMessages,
      aiResponseTime: session.aiResponseTime,
      createdAt: (session as any).createdAt,
      updatedAt: (session as any).updatedAt,
      lastActivityAt: session.lastActivityAt
    };
  }

  async getUserSessions(userId: string, query: any = {}) {
    const organizationId = this.tenantContextService.getCurrentOrganization();
    const { status, limit = 20, offset = 0, scenarioId } = query;

    const filter: any = { userId, organizationId };
    if (status) filter.status = status;
    if (scenarioId) filter.scenarioId = scenarioId;

    const sessions = await this.chatNegotiationModel
      .find(filter)
      .sort({ lastActivityAt: -1 })
      .limit(parseInt(limit))
      .skip(parseInt(offset))
      .exec();

    const total = await this.chatNegotiationModel.countDocuments(filter);

    return {
      sessions: sessions.map(session => ({
        id: session._id,
        scenarioId: session.scenarioId,
        status: session.status,
        currentRound: session.currentRound,
        relationshipMetrics: session.relationshipMetrics,
        score: session.score,
        totalMessages: session.totalMessages,
        createdAt: (session as any).createdAt,
        lastActivityAt: session.lastActivityAt
      })),
      total,
      limit: parseInt(limit),
      offset: parseInt(offset)
    };
  }

  async processChatMove(sessionId: string, userId: string, message: string, extractedData?: any, context?: any) {
    const startTime = Date.now();
    const organizationId = this.tenantContextService.getCurrentOrganization();

    try {
      // 1. Get chat negotiation session
      const session = await this.getChatNegotiationSession(sessionId, userId);
      
      // 2. Extract/enhance data from message
      const enhancedData = await this.enhanceExtractedData(message, extractedData, session);
      
      // 3. Convert to negotiation move format
      const negotiationMove = this.convertToNegotiationMove(message, enhancedData);
      
      // 4. Process through negotiation simulator (simplified for MVP)
      const updatedNegotiation = {
        success: true,
        round: session.currentRound + 1,
        score: session.score + 0.1
      };

      // 5. Generate AI response
      const aiResponse = await this.aiConversationService.generateAIResponse(session, enhancedData, updatedNegotiation);
      
      // 6. Update session metrics
      const relationshipUpdate = this.calculateRelationshipUpdate(
        session.relationshipMetrics,
        enhancedData,
        aiResponse
      );

      const newScore = this.calculateNewScore(session.score, enhancedData, relationshipUpdate);

      // 7. Save move data for analytics
      const moveData = new this.chatMoveDataModel({
        chatNegotiationSessionId: sessionId,
        userId,
        messageContent: message,
        sender: 'user',
        extractedData: enhancedData,
        relationshipImpact: {
          trustChange: relationshipUpdate.trust - session.relationshipMetrics.trust,
          respectChange: relationshipUpdate.respect - session.relationshipMetrics.respect,
          pressureChange: relationshipUpdate.pressure - session.relationshipMetrics.pressure
        },
        scoreImpact: newScore - session.score,
        processingTimeMs: Date.now() - startTime,
        confidence: enhancedData.confidence || 0.5,
        detectedStrategy: enhancedData.strategy,
        sentiment: enhancedData.sentiment,
        organizationId
      });

      await moveData.save();

      // 8. Save AI response data
      const aiMoveData = new this.chatMoveDataModel({
        chatNegotiationSessionId: sessionId,
        userId,
        messageContent: aiResponse.content,
        sender: 'ai',
        extractedData: aiResponse.extractedData || {
          strategy: 'neutral',
          sentiment: {score: 0},
          confidence: 0.5
        },
        aiResponseMetadata: {
          suggestions: aiResponse.suggestions,
          responseTime: Date.now() - startTime
        },
        organizationId
      });

      await aiMoveData.save();

      // 9. Update session
      const updatedSession = await this.chatNegotiationModel.findByIdAndUpdate(
        sessionId,
        {
          currentRound: session.currentRound + 1,
          extractedTerms: { ...session.extractedTerms, ...enhancedData.offer },
          relationshipMetrics: relationshipUpdate,
          score: newScore,
          totalMessages: session.totalMessages + 2, // User + AI message
          aiResponseTime: ((session.aiResponseTime * session.totalMessages) + (Date.now() - startTime)) / (session.totalMessages + 2),
          lastActivityAt: new Date()
        },
        { new: true }
      );

      this.logger.log(`Chat move processed for session ${sessionId}, round ${updatedSession.currentRound}`);

      return {
        userMessage: {
          content: message,
          extractedData: enhancedData,
          timestamp: new Date()
        },
        aiResponse: {
          content: aiResponse.content,
          suggestions: aiResponse.suggestions,
          extractedData: aiResponse.extractedData || {
            strategy: 'neutral',
            sentiment: {score: 0},
            confidence: 0.5
          },
          timestamp: new Date()
        },
        sessionUpdate: {
          id: updatedSession._id,
          currentRound: updatedSession.currentRound,
          relationshipMetrics: updatedSession.relationshipMetrics,
          score: updatedSession.score,
          extractedTerms: updatedSession.extractedTerms
        },
        processingTime: Date.now() - startTime
      };

    } catch (error) {
      this.logger.error(`Failed to process chat move: ${error.message}`);
      throw error;
    }
  }

  private getDefaultPersonality(scenarioId: string) {
    // Default AI personalities based on scenario
    const personalities = {
      software_licensing: {
        characterId: 'default_character',
        aggressiveness: 0.4,
        flexibility: 0.7,
        riskTolerance: 0.6,
        communicationStyle: 'ANALYTICAL'
      },
      contract_negotiation: {
        characterId: 'default_character',
        aggressiveness: 0.5,
        flexibility: 0.6,
        riskTolerance: 0.5,
        communicationStyle: 'DIPLOMATIC'
      }
    };

    return personalities[scenarioId] || personalities.software_licensing;
  }

  private async enhanceExtractedData(message: string, extractedData: any, session: any) {
    // Basic data extraction - will be enhanced in Phase 2
    const enhanced = {
      ...extractedData,
      message,
      timestamp: new Date(),
      confidence: extractedData?.confidence || 0.5
    };

    // Extract basic financial terms if not provided
    if (!enhanced.offer?.price) {
      const priceMatch = message.match(/\$?([\d,]+(?:\.\d{2})?)\s*(?:k|thousand)?/i);
      if (priceMatch) {
        enhanced.offer = enhanced.offer || {};
        enhanced.offer.price = parseFloat(priceMatch[1].replace(/,/g, ''));
        if (message.toLowerCase().includes('k') || message.toLowerCase().includes('thousand')) {
          enhanced.offer.price *= 1000;
        }
        enhanced.offer.currency = 'USD';
      }
    }

    // Detect basic strategy if not provided
    if (!enhanced.strategy) {
      enhanced.strategy = this.detectBasicStrategy(message);
    }

    // Detect basic sentiment if not provided
    if (!enhanced.sentiment) {
      enhanced.sentiment = this.detectBasicSentiment(message);
    }

    return enhanced;
  }

  private convertToNegotiationMove(message: string, extractedData: any) {
    return {
      type: 'offer', // Or determine from extractedData
      content: {
        message,
        offer: extractedData.offer || {},
        terms: extractedData.terms || [],
      },
      strategy: extractedData.strategy || 'collaborative',
      metadata: {
        source: 'chat',
        extractedData,
      },
    };
  }

  private calculateRelationshipUpdate(
    currentMetrics: any,
    enhancedData: any,
    aiResponse: any,
  ): any {
    let { trust, respect, pressure } = currentMetrics;
    const sentiment = enhancedData.sentiment?.score || 0;
    const strategy = enhancedData.strategy;

    // Update trust based on sentiment
    trust += sentiment * 2; // sentiment is -0.5 to 0.5

    // Update respect
    if (strategy === 'collaborative') {
      respect += 1;
    } else if (strategy === 'competitive') {
      respect -= 1;
    }

    // Update pressure
    if (strategy === 'competitive') {
      pressure += 2;
    } else {
      pressure = Math.max(0, pressure - 1);
    }

    return {
      trust: Math.max(0, Math.min(100, trust)),
      respect: Math.max(0, Math.min(100, respect)),
      pressure: Math.max(0, Math.min(100, pressure)),
    };
  }

  private calculateNewScore(
    currentScore: number,
    enhancedData: any,
    relationshipUpdate: any,
  ): number {
    let newScore = currentScore;

    // Score improves with collaboration and positive sentiment
    if (enhancedData.strategy === 'collaborative') {
      newScore += 5;
    } else if (enhancedData.strategy === 'competitive') {
      newScore -= 5;
    }

    if (enhancedData.sentiment?.score > 0) {
      newScore += 2;
    } else if (enhancedData.sentiment?.score < 0) {
      newScore -= 2;
    }

    // A good relationship boosts the score
    if (relationshipUpdate.trust > 60 && relationshipUpdate.respect > 60) {
      newScore += 3;
    }

    return Math.max(0, newScore);
  }

  private detectBasicStrategy(message: string): string {
    const competitiveWords = ['demand', 'must', 'non-negotiable', 'final offer'];
    const collaborativeWords = [
      'propose',
      'suggest',
      'work together',
      'find a solution',
    ];

    if (competitiveWords.some(word => message.toLowerCase().includes(word))) {
      return 'competitive';
    }
    if (collaborativeWords.some(word => message.toLowerCase().includes(word))) {
      return 'collaborative';
    }
    return 'compromising';
  }

  private detectBasicSentiment(message: string): any {
    const positiveWords = ['agree', 'great', 'excellent', 'fair'];
    const negativeWords = ['disagree', 'problem', 'issue', 'unfair'];
    let score = 0;

    if (positiveWords.some(word => message.toLowerCase().includes(word))) {
      score += 0.5;
    }
    if (negativeWords.some(word => message.toLowerCase().includes(word))) {
      score -= 0.5;
    }

    return { score: Math.max(-1, Math.min(1, score)) };
  }


  /**
   * Get conversation history for a negotiation session
   */
  async getConversationHistory(sessionId: string, userId: string, query: any = {}) {
    const organizationId = this.tenantContextService.getCurrentOrganization();
    
    // Verify session ownership
    const session = await this.chatNegotiationModel.findOne({
      _id: sessionId,
      userId,
      organizationId
    }).exec();

    if (!session) {
      throw new NotFoundException('Chat negotiation session not found');
    }

    const { limit = 50, offset = 0, includeMetadata = false } = query;

    // Get messages for this session
    const messages = await this.chatMoveDataModel
      .find({
        chatNegotiationSessionId: sessionId,
        organizationId
      })
      .sort({ createdAt: 1 }) // Chronological order
      .limit(parseInt(limit))
      .skip(parseInt(offset))
      .exec();

    const total = await this.chatMoveDataModel.countDocuments({
      chatNegotiationSessionId: sessionId,
      organizationId
    });

    // Format messages for response
    const formattedMessages = messages.map(message => {
      const baseMessage = {
        id: message._id,
        content: message.messageContent,
        sender: message.sender,
        timestamp: (message as any).createdAt,
        extractedData: message.extractedData
      };

      if (includeMetadata) {
        return {
          ...baseMessage,
          relationshipImpact: message.relationshipImpact,
          scoreImpact: message.scoreImpact,
          processingTimeMs: message.processingTimeMs,
          confidence: message.confidence,
          detectedStrategy: message.detectedStrategy,
          sentiment: message.sentiment,
          aiResponseMetadata: message.aiResponseMetadata
        };
      }

      return baseMessage;
    });

    return {
      sessionId,
      messages: formattedMessages,
      pagination: {
        total,
        limit: parseInt(limit),
        offset: parseInt(offset),
        hasMore: parseInt(offset) + messages.length < total
      },
      sessionInfo: {
        currentRound: session.currentRound,
        totalMessages: session.totalMessages,
        score: session.score,
        status: session.status,
        relationshipMetrics: session.relationshipMetrics
      }
    };
  }
}

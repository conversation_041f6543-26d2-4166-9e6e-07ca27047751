import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { AnalysisResult, AnalysisResultDocument } from '../../documents/schemas/analysis-result.schema';
import { Document as DocumentModel } from '../../documents/schemas/document.schema';
import { ChatNegotiationSession } from '../schemas/chat-negotiation-session.schema';
import { AnalysisResultRepository } from '../../documents/repositories/analysis-result.repository';

export interface DocumentNegotiationScenario {
  id: string;
  title: string;
  description: string;
  contractType: string;
  focusAreas: string[];
  negotiationPoints: NegotiationPoint[];
  difficulty: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  estimatedDuration: number; // minutes
  aiPersonalityRecommendation: {
    aggressiveness: number;
    flexibility: number;
    riskTolerance: number;
    communicationStyle: string;
  };
}

export interface NegotiationPoint {
  id: string;
  category: string;
  issue: string;
  currentTerm: string;
  suggestedImprovement: string;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  priority: number;
  negotiationStrategy: string;
  fallbackOptions: string[];
}

@Injectable()
export class DocumentNegotiationService {
  private readonly logger = new Logger(DocumentNegotiationService.name);

  constructor(
    @InjectModel('Document')
    private readonly documentModel: Model<DocumentModel>,
    @InjectModel(ChatNegotiationSession.name)
    private readonly sessionModel: Model<ChatNegotiationSession>,
    private readonly analysisResultRepository: AnalysisResultRepository,
    // private readonly aiService: AIService, // TODO: Add when AI service is available
  ) {}

  /**
   * Create a negotiation scenario from a contract analysis
   */
  async createScenarioFromAnalysis(
    analysisId: string,
    organizationId: string,
  ): Promise<{ scenario: DocumentNegotiationScenario, analysis: AnalysisResult }> {
        this.logger.log(`Creating negotiation scenario from analysis: ${analysisId}`);
    this.logger.log(`Querying for analysis with ID: ${analysisId} and OrgID: ${organizationId}`);

    // Get the analysis result using the repository
    const analysis = await this.analysisResultRepository.findById(analysisId);

    if (!analysis || analysis.organizationId !== organizationId) {
      this.logger.error(`Analysis not found for ID: ${analysisId} and OrgID: ${organizationId}`);
      throw new NotFoundException('Contract analysis not found');
    }

    // Get the source document
    const document = await this.documentModel.findOne({ id: analysis.documentId, organizationId: analysis.organizationId }).exec();
    if (!document) {
      this.logger.error(`Document not found with ID: ${analysis.documentId} for organization: ${analysis.organizationId}`);
      throw new NotFoundException('Source document not found');
    }

    // Extract negotiation points from deviations
    const negotiationPoints = await this.extractNegotiationPoints(analysis);

    // Generate AI personality recommendation based on analysis
    const aiPersonalityRecommendation = this.generateAIPersonalityRecommendation(analysis);

    // Determine difficulty based on analysis complexity
    const difficulty = this.determineDifficulty(analysis);

    const scenario: DocumentNegotiationScenario = {
      id: `doc-scenario-${analysisId}`,
      title: `Negotiate: ${analysis.analysisContent?.documentType || document.metadata?.originalName || 'Contract'}`,
      description: this.generateScenarioDescription(analysis),
      contractType: analysis.analysisContent?.documentType || 'GENERAL',
      focusAreas: this.extractFocusAreas(analysis),
      negotiationPoints,
      difficulty,
      estimatedDuration: this.estimateDuration(negotiationPoints),
      aiPersonalityRecommendation,
    };

    this.logger.log(`Created scenario with ${negotiationPoints.length} negotiation points`);
    return { scenario, analysis };
  }

  /**
   * Create a negotiation session from a document analysis
   */
  async createSessionFromAnalysis(
    analysisId: string,
    organizationId: string,
    userId: string,
    aiPersonality?: any,
  ): Promise<ChatNegotiationSession> {
    const { scenario, analysis } = await this.createScenarioFromAnalysis(analysisId, organizationId);

    if (!analysis) {
      throw new NotFoundException('Contract analysis not found');
    }

    // Use provided AI personality or the recommended one
    const finalAiPersonality = aiPersonality || {
      characterId: 'contract_specialist',
      ...scenario.aiPersonalityRecommendation,
    };

    // Generate ObjectIds for the required fields
    const negotiationSessionId = new Types.ObjectId();
    const chatSessionId = new Types.ObjectId();

    // Create the negotiation session
    const session = new this.sessionModel({
      userId,
      organizationId,
      scenarioId: scenario.id,
      sourceDocumentId: analysis.documentId,
      sourceAnalysisId: analysisId,
      negotiationSessionId,
      chatSessionId,
      documentContext: {
        contractType: analysis.analysisContent?.documentType,
        contractTitle: analysis.analysisContent?.parties?.[0]?.name + ' Contract',
        analysisScore: this.calculateRiskScore(analysis.analysisContent),
        riskLevel: this.determineRiskLevel(analysis.analysisContent),
        totalDeviations: analysis.analysisContent?.clauses?.length || 0,
        keyFindings: analysis.analysisContent?.specialTerms || [],
      },
      status: 'active',
      currentRound: 1,
      extractedTerms: {},
      relationshipMetrics: {
        trust: 50,
        respect: 50,
        pressure: 20,
      },
      score: 0,
      aiPersonality: finalAiPersonality,
      totalMessages: 0,
      aiResponseTime: 0,
    });

    await session.save();
    this.logger.log(`Created document-based negotiation session: ${session.id}`);
    return session;
  }

  /**
   * Get negotiation context for AI responses
   */
  async getNegotiationContext(sessionId: string): Promise<string> {
    const session = await this.sessionModel.findOne({ _id: sessionId }).exec();
    if (!session || !session.sourceAnalysisId) {
      return '';
    }

    const analysis = await this.analysisResultRepository.findById(session.sourceAnalysisId);

    if (!analysis) {
      return '';
    }

    const context = `
Contract Analysis Context:
- Contract Type: ${analysis.analysisContent?.documentType || 'Unknown'}
- Overall Score: ${this.calculateRiskScore(analysis.analysisContent) || 0}/100
- Risk Level: ${this.determineRiskLevel(analysis.analysisContent) || 'Unknown'}
- Total Clauses: ${analysis.analysisContent?.clauses?.length || 0}

Key Issues to Negotiate:
${(analysis.analysisContent?.clauses || [])
  .filter(c => c.riskLevel === 'high' || c.riskLevel === 'medium')
  .slice(0, 5)
  .map(c => `- ${c.title}: ${c.riskDescription.substring(0, 100)}...`)
  .join('\n')}

Negotiation Focus:
${(analysis.analysisContent?.specialTerms || []).slice(0, 3).map(f => `- ${f}`).join('\n')}
    `.trim();

    return context;
  }

  private async extractNegotiationPoints(analysis: AnalysisResult): Promise<NegotiationPoint[]> {
    const points: NegotiationPoint[] = [];

    // Convert high-priority deviations to negotiation points
    const highRiskClauses = (analysis.analysisContent?.clauses || [])
      .filter(c => c.riskLevel === 'high' || c.riskLevel === 'medium')
      .slice(0, 8); // Limit to top 8 issues

    for (let i = 0; i < highRiskClauses.length; i++) {
      const clause = highRiskClauses[i];
      
      const point: NegotiationPoint = {
        id: `point-${i + 1}`,
        category: this.categorizeClause(clause.title),
        issue: clause.title,
        currentTerm: clause.content.substring(0, 200),
        suggestedImprovement: `Address ${clause.riskDescription}`,
        riskLevel: clause.riskLevel?.toUpperCase() as any,
        priority: i + 1,
        negotiationStrategy: this.generateNegotiationStrategy(clause),
        fallbackOptions: this.generateFallbackOptions(clause),
      };

      points.push(point);
    }

    return points;
  }

  private generateAIPersonalityRecommendation(analysis: AnalysisResult) {
    // Adjust AI personality based on contract risk and complexity
    const baseAggressiveness = 0.5;
    const baseFlexibility = 0.6;
    const baseRiskTolerance = 0.4;

    // Higher risk contracts = more aggressive AI
    const riskLevel = this.determineRiskLevel(analysis.analysisContent);
    const riskMultiplier = riskLevel === 'CRITICAL' ? 0.3 :
                          riskLevel === 'HIGH' ? 0.2 :
                          riskLevel === 'MEDIUM' ? 0.1 : 0;

    // More high-risk clauses = less flexible AI
    const highRiskClauses = (analysis.analysisContent?.clauses || []).filter(c => c.riskLevel === 'high').length;
    const deviationMultiplier = Math.min(highRiskClauses / 10, 0.3);

    return {
      aggressiveness: Math.min(baseAggressiveness + riskMultiplier, 1.0),
      flexibility: Math.max(baseFlexibility - deviationMultiplier, 0.1),
      riskTolerance: Math.max(baseRiskTolerance - riskMultiplier, 0.1),
      communicationStyle: riskLevel === 'CRITICAL' ? 'DIRECT' : 'ANALYTICAL',
    };
  }

  private determineDifficulty(analysis: AnalysisResult): 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' {
    const score = this.calculateRiskScore(analysis.analysisContent) || 0;
    const highRiskClauses = (analysis.analysisContent?.clauses || []).filter(c => c.riskLevel === 'high').length;
    const mediumRiskClauses = (analysis.analysisContent?.clauses || []).filter(c => c.riskLevel === 'medium').length;

    if (score > 80 && highRiskClauses === 0) return 'BEGINNER';
    if (score > 60 && highRiskClauses < 2) return 'INTERMEDIATE';
    return 'ADVANCED';
  }

  private generateScenarioDescription(analysis: AnalysisResult): string {
    const contractType = analysis.analysisContent?.documentType || 'contract';
    const totalClauses = analysis.analysisContent?.clauses?.length || 0;
    const overallScore = this.calculateRiskScore(analysis.analysisContent) || 0;
    const highRiskClauses = (analysis.analysisContent?.clauses || []).filter(c => c.riskLevel === 'high').length;
    const mediumRiskClauses = (analysis.analysisContent?.clauses || []).filter(c => c.riskLevel === 'medium').length;
    
    return `Practice negotiating the key issues identified in this ${contractType} contract. ` +
           `The analysis found ${totalClauses} clauses with an overall risk score of ${overallScore}/100. ` +
           `Focus on addressing the ${highRiskClauses + mediumRiskClauses} higher-risk clauses ` +
           `to improve contract terms and reduce risk.`;
  }

  private extractFocusAreas(analysis: AnalysisResult): string[] {
    const areas = new Set<string>();
    
    (analysis.analysisContent?.clauses || []).forEach(clause => {
      areas.add(this.categorizeClause(clause.title));
    });

    return Array.from(areas).slice(0, 5);
  }

  private categorizeClause(clauseTitle: string): string {
    const name = clauseTitle.toLowerCase();
    if (name.includes('payment') || name.includes('fee')) return 'Payment Terms';
    if (name.includes('termination') || name.includes('term')) return 'Contract Duration';
    if (name.includes('liability') || name.includes('indemnity')) return 'Risk & Liability';
    if (name.includes('intellectual') || name.includes('ip')) return 'Intellectual Property';
    if (name.includes('confidential') || name.includes('nda')) return 'Confidentiality';
    if (name.includes('delivery') || name.includes('performance')) return 'Performance';
    if (name.includes('dispute') || name.includes('arbitration')) return 'Dispute Resolution';
    return 'General Terms';
  }

  private generateNegotiationStrategy(deviation: any): string {
    const strategies = [
      'Request modification to reduce risk exposure',
      'Propose alternative language that balances interests',
      'Negotiate reciprocal obligations',
      'Seek limitation or cap on liability',
      'Request additional protections or guarantees',
    ];
    return strategies[Math.floor(Math.random() * strategies.length)];
  }

  private generateFallbackOptions(deviation: any): string[] {
    return [
      'Accept current terms with additional protections',
      'Propose compromise language',
      'Request sunset clause or review period',
      'Negotiate alternative compensation',
    ];
  }

  private estimateDuration(points: NegotiationPoint[]): number {
    // Estimate 3-5 minutes per negotiation point
    return Math.max(15, points.length * 4);
  }

  /**
   * Calculate a risk score based on the analysis result
   */
  private calculateRiskScore(analysisResult: any): number {
    if (!analysisResult?.clauses) return 0;

    const clauses = analysisResult.clauses;
    const totalClauses = clauses.length;
    if (totalClauses === 0) return 0;

    let riskScore = 0;
    clauses.forEach(clause => {
      switch (clause.riskLevel) {
        case 'high':
          riskScore += 30;
          break;
        case 'medium':
          riskScore += 15;
          break;
        case 'low':
          riskScore += 5;
          break;
        case 'none':
          riskScore += 0;
          break;
        default:
          riskScore += 5;
      }
    });

    // Calculate percentage and invert (higher risk = lower score)
    const averageRisk = riskScore / totalClauses;
    return Math.max(0, Math.min(100, 100 - averageRisk));
  }

  /**
   * Determine overall risk level based on analysis result
   */
  private determineRiskLevel(analysisResult: any): string {
    if (!analysisResult?.clauses) return 'UNKNOWN';

    const clauses = analysisResult.clauses;
    const highRiskCount = clauses.filter(c => c.riskLevel === 'high').length;
    const mediumRiskCount = clauses.filter(c => c.riskLevel === 'medium').length;

    if (highRiskCount >= 2) return 'CRITICAL';
    if (highRiskCount >= 1) return 'HIGH';
    if (mediumRiskCount >= 3) return 'MEDIUM';
    return 'LOW';
  }
}

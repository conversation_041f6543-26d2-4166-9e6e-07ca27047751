import { Test, TestingModule } from '@nestjs/testing';
import { AiConversationService } from '../ai-conversation.service';
import { AIService } from '../../../ai/services/ai.service';
import { DocumentNegotiationService } from '../document-negotiation.service';

describe('AiConversationService', () => {
  let service: AiConversationService;
  let mockAiService: jest.Mocked<AIService>;
  let mockDocumentNegotiationService: jest.Mocked<DocumentNegotiationService>;

  // Sample document context for testing
  const sampleDocumentContext = {
    contractType: 'Non-Disclosure Agreement',
    riskLevel: 'HIGH',
    summary: 'This is a test NDA with standard terms and conditions.',
    keyIssues: [
      {
        title: 'Confidentiality Period',
        description: 'The agreement specifies a 5-year confidentiality period which is longer than standard 2-3 year terms.',
        riskLevel: 'HIGH',
        keywords: ['confidentiality', 'nda period', 'duration']
      },
      {
        title: 'Liability Cap',
        description: 'The liability is capped at the total fees paid under the agreement.',
        riskLevel: 'MEDIUM',
        keywords: ['liability', 'damages', 'cap']
      }
    ]
  };

  beforeEach(async () => {
    // Mock the AI service
    mockAiService = {
      generateChatResponse: jest.fn(),
      generateResponse: jest.fn(),
    } as any;

    // Mock the DocumentNegotiationService
    mockDocumentNegotiationService = {
      getNegotiationContext: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AiConversationService,
        {
          provide: AIService,
          useValue: mockAiService,
        },
        {
          provide: DocumentNegotiationService,
          useValue: mockDocumentNegotiationService,
        },
      ],
    }).compile();

    service = module.get<AiConversationService>(AiConversationService);
  });

  describe('generateDocumentAwareResponse', () => {
    it('should handle greetings with rule-based response', async () => {
      const response = await service.generateDocumentAwareResponse(
        { id: 'test-session' },
        { message: 'Hello' },
        sampleDocumentContext
      );
      
      expect(response).toBeDefined();
      expect(response).toContain('Hello');
      expect(response).toContain('help');
    });

    it('should handle questions about terms with AI response', async () => {
      // Mock AI response
      mockAiService.generateChatResponse.mockResolvedValueOnce({
        content: 'The confidentiality period is 5 years, which is longer than the standard 2-3 years.',
        suggestions: []
      });

      const response = await service.generateDocumentAwareResponse(
        { id: 'test-session' },
        { message: 'What is the confidentiality period?' },
        sampleDocumentContext
      );
      
      expect(response).toBeDefined();
      expect(response).toContain('confidentiality period');
      expect(response).toContain('5 years');
    });

    it('should handle unknown queries with fallback response', async () => {
      // Mock AI service to return empty response
      mockAiService.generateChatResponse.mockResolvedValueOnce({ content: '', suggestions: [] });
      
      const response = await service.generateDocumentAwareResponse(
        { id: 'test-session' },
        { message: 'Random question about nothing in particular' },
        sampleDocumentContext
      );
      
      expect(response).toBeDefined();
      expect(response.length).toBeGreaterThan(0);
    });

    it('should handle errors gracefully', async () => {
      // Mock AI service to throw an error
      mockAiService.generateChatResponse.mockRejectedValueOnce(new Error('API error'));
      
      const response = await service.generateDocumentAwareResponse(
        { id: 'test-session' },
        { message: 'What are the key issues?' },
        sampleDocumentContext
      );
      
      // Should still return a response even when AI fails
      expect(response).toBeDefined();
      expect(response.length).toBeGreaterThan(0);
    });
  });
});

import { Test, TestingModule } from '@nestjs/testing';
import { AiConversationService } from './ai-conversation.service';
import { DocumentNegotiationService } from './document-negotiation.service';

describe('AiConversationService', () => {
  let service: AiConversationService;
  let mockDocumentNegotiationService: jest.Mocked<DocumentNegotiationService>;

  beforeEach(async () => {
    mockDocumentNegotiationService = {
      getNegotiationContext: jest.fn().mockResolvedValue(''),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AiConversationService,
        {
          provide: DocumentNegotiationService,
          useValue: mockDocumentNegotiationService,
        },
      ],
    }).compile();

    service = module.get<AiConversationService>(AiConversationService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateAIResponse', () => {
    const mockSession = { id: 'session-123' };
    const mockUserMoveData = { message: 'Hello', strategy: 'collaborative', sentiment: 'positive' };
    const mockNegotiationContext = {};

    it('should handle greeting messages', async () => {
      const result = await service.generateAIResponse(
        mockSession,
        { ...mockUserMoveData, message: 'Hi there!' },
        mockNegotiationContext
      );
      
      expect(result.content).toBeDefined();
      expect(result.suggestions).toBeDefined();
      expect(result.suggestions.length).toBeGreaterThan(0);
    });

    it('should handle document context when available', async () => {
      const mockDocumentContext = `Contract Type: NDA
Risk Level: HIGH
Key Issues:
- Confidentiality: Confidentiality clause is too broad
  Risk Level: HIGH
- Term: Term is too long
  Risk Level: MEDIUM
`;
      
      mockDocumentNegotiationService.getNegotiationContext.mockResolvedValueOnce(mockDocumentContext);
      
      const result = await service.generateAIResponse(
        mockSession,
        { ...mockUserMoveData, message: 'What do you think about the terms?' },
        mockNegotiationContext
      );
      
      // The response should acknowledge the document context
      expect(result.content).toBeDefined();
      expect(result.suggestions?.length).toBeGreaterThan(0);
    });

    it('should handle price-related messages', async () => {
      const result = await service.generateAIResponse(
        mockSession,
        { ...mockUserMoveData, message: 'The price is $1000' },
        mockNegotiationContext
      );
      
      // The response should acknowledge the price
      expect(result.content).toBeDefined();
      expect(result.suggestions?.length).toBeGreaterThan(0);
    });

    it('should handle error cases gracefully', async () => {
      mockDocumentNegotiationService.getNegotiationContext.mockRejectedValueOnce(new Error('Test error'));
      
      const result = await service.generateAIResponse(
        mockSession,
        { ...mockUserMoveData, message: 'This will cause an error' },
        mockNegotiationContext
      );
      
      // The service should still return a valid response even when there's an error
      expect(result.content).toBeDefined();
      expect(result.suggestions?.length).toBeGreaterThan(0);
    });
  });

  describe('intent detection', () => {
    it('should detect greetings', () => {
      expect(service['isGreeting']('Hello there!')).toBe(true);
      expect(service['isGreeting']('Hi, how are you?')).toBe(true);
      expect(service['isGreeting']('Good morning!')).toBe(true);
      expect(service['isGreeting']('This is a test')).toBe(false);
    });

    it('should detect offer requests', () => {
      expect(service['isAskingForOffer']('Can you make an offer?')).toBe(true);
      expect(service['isAskingForOffer']('What is your proposal?')).toBe(true);
      expect(service['isAskingForOffer']('Let me know your offer')).toBe(true);
      expect(service['isAskingForOffer']('Hello there')).toBe(false);
    });

    it('should detect finalization attempts', () => {
      // Test various ways users might indicate they want to finalize
      expect(service['isFinalizing']('Let\'s finalize the deal')).toBe(true);
      expect(service['isFinalizing']('Ready to close')).toBe(true);
      expect(service['isFinalizing']('This is the last offer')).toBe(true);
      expect(service['isFinalizing']('Let\'s wrap this up')).toBe(true);
      expect(service['isFinalizing']('I think we can finalize now')).toBe(true);
      
      // Test non-finalization phrases
      expect(service['isFinalizing']('Hello there')).toBe(false);
      expect(service['isFinalizing']('What is the price?')).toBe(false);
      expect(service['isFinalizing']('')).toBe(false);
      expect(service['isFinalizing'](null as any)).toBe(false);
      expect(service['isFinalizing'](undefined as any)).toBe(false);
    });
  });
});

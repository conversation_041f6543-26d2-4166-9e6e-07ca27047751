import { Inject, Injectable, Logger } from '@nestjs/common';
import { DocumentNegotiationService } from './document-negotiation.service';
import { AIService } from '../../ai/services/ai.service';
import { ChatMessage, ChatRole } from '../../../common/interfaces/chat.interface';
import { firstValueFrom, Observable, of, from } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { AIPersonalityProfile } from '../../gamification/schemas/character.schema';

interface DocumentKeyIssue {
  title: string;
  description: string;
  riskLevel: 'HIGH' | 'MEDIUM' | 'LOW';
}

type AIChatMessage = {
  role: 'system' | 'user' | 'assistant';
  content: string;
};

type RelationshipImpact = {
  trust: number;
  respect: number;
  pressure: number;
};

type KeyIssue = {
  title: string;
  description: string;
  riskLevel: string;
};

@Injectable()
export class AiConversationService {
  private readonly logger = new Logger(AiConversationService.name);

  constructor(
    @Inject(AIService)
    private readonly aiService: AIService,
    private documentNegotiationService: DocumentNegotiationService,
  ) {}

  private extractValue(text: string, key: string): string | null {
    if (!text) return null;
    const regex = new RegExp(`${key}[\\s:]+([^\\n]+)`, 'i');
    const match = text.match(regex);
    return match ? match[1].trim() : null;
  }

  private getCommunicationStyleDescription(style: string, aggressiveness: number): string {
    const tone = aggressiveness > 0.7 ? 'assertive' : aggressiveness > 0.4 ? 'confident' : 'collaborative';
    
    switch(style) {
      case 'ANALYTICAL':
        return `Focused on facts, data, and logical arguments with a ${tone} tone`;
      case 'EMOTIONAL':
        return `Focused on relationship-building and emotional appeals with a ${tone} tone`;
      case 'DIRECT':
        return `Concise and to the point with a ${tone} tone`;
      case 'DIPLOMATIC':
        return `Considerate of the other party's perspective with a ${tone} tone`;
      default:
        return `Professional with a ${tone} tone`;
    }
  }

  private getToneGuideline(personality: AIPersonalityProfile): string {
    const baseTone = personality.aggressiveness > 0.7 ? 'assertive' : 
                    personality.aggressiveness > 0.4 ? 'confident' : 'collaborative';
    
    if (personality.communicationStyle === 'ANALYTICAL') {
      return `Use a ${baseTone} tone focused on facts and logical arguments`;
    } else if (personality.communicationStyle === 'EMOTIONAL') {
      return `Use a ${baseTone} tone that builds rapport and emotional connection`;
    } else if (personality.communicationStyle === 'DIRECT') {
      return `Use a ${baseTone} and straightforward communication style`;
    } else {
      return `Use a ${baseTone} and diplomatic communication style`;
    }
  }

  private getRiskGuideline(personality: AIPersonalityProfile, riskLevel: string): string {
    const riskAdjective = personality.riskTolerance > 0.7 ? 'comfortable' : 
                         personality.riskTolerance > 0.4 ? 'cautious' : 'very cautious';
    
    return `Be ${riskAdjective} with risk (${riskLevel} risk level). ` + 
           (personality.riskTolerance > 0.7 ? 
            'Be open to innovative solutions and calculated risks.' :
            'Focus on risk mitigation and conservative approaches.');
  }

  private getFlexibilityGuideline(personality: AIPersonalityProfile): string {
    if (personality.flexibility > 0.7) {
      return 'Be highly flexible and open to creative solutions. Show willingness to adapt your position.';
    } else if (personality.flexibility > 0.4) {
      return 'Be somewhat flexible but maintain core positions. Consider reasonable compromises.';
    } else {
      return 'Maintain firm positions. Only consider minor concessions when absolutely necessary.';
    }
  }

  private extractKeyIssues(documentContext: string): DocumentKeyIssue[] {
    if (!documentContext) return [];
    
    try {
      const issues: DocumentKeyIssue[] = [];
      
      // Look for risk-related sections
      const riskSection = documentContext.match(/risk[\\s\\S]*?(?=##|$)/i);
      if (riskSection) {
        const riskMatches = riskSection[0].match(/[-*]\\s*(.+?):\\s*(.+?)(?=\\n|$)/g);
        if (riskMatches) {
          riskMatches.forEach(match => {
            const [_, title, description] = match.match(/[-*]\\s*(.+?):\\s*(.+)/) || [];
            if (title && description) {
              const riskLevel = description.match(/high|severe|critical/i) ? 'HIGH' :
                               description.match(/medium|moderate/i) ? 'MEDIUM' : 'LOW';
              issues.push({ 
                title: title.trim(), 
                description: description.trim(), 
                riskLevel: riskLevel as 'HIGH' | 'MEDIUM' | 'LOW' 
              });
            }
          });
        }
      }
      
      return issues.length > 0 ? issues : [
        {
          title: 'Standard Terms',
          description: 'Review standard contract terms and conditions',
          riskLevel: 'MEDIUM' as const
        }
      ];
    } catch (error) {
      this.logger.error('Error extracting key issues:', error);
      return [];
    }
  }

  private async generateAIResponseWithLLM(
    messages: AIChatMessage[],
    documentContext?: string,
    aiPersonality: Partial<AIPersonalityProfile> & { characterId?: string } = {}
  ): Promise<{ content: string; suggestions: string[] }> {
    try {
      // Default AI character if none provided
      const character = aiPersonality.characterId || 'contract_negotiator';
      const style = aiPersonality.communicationStyle || 'ANALYTICAL';
      
      // Set default personality traits if not provided
      const personality: AIPersonalityProfile = {
        aggressiveness: 0.5,
        flexibility: 0.7,
        riskTolerance: 0.5,
        communicationStyle: 'ANALYTICAL',
        decisionSpeed: 'MODERATE',
        concessionPattern: 'STRATEGIC',
        ...aiPersonality
      };
      
      // Prepare system message with AI character and personality
      const systemMessage: AIChatMessage = {
        role: 'system',
        content: [
          `You are ${character}, a contract negotiation AI with the following personality traits:`,
          `- Communication style: ${personality.communicationStyle.toLowerCase()}`,
          `- Aggressiveness: ${personality.aggressiveness.toFixed(1)}/1.0`,
          `- Flexibility: ${personality.flexibility.toFixed(1)}/1.0`,
          `- Risk tolerance: ${personality.riskTolerance.toFixed(1)}/1.0`,
          `- Decision speed: ${personality.decisionSpeed.toLowerCase()}`,
          `- Concession pattern: ${personality.concessionPattern.toLowerCase()}`,
          '',
          documentContext && `Document context:\n${documentContext}`,
          '',
          'Guidelines for responses:',
          personality.aggressiveness > 0.7 ? '- Be assertive and firm in your positions' : '- Be collaborative and open to discussion',
          personality.flexibility > 0.7 ? '- Show willingness to adapt and compromise' : '- Maintain consistency in your positions',
          personality.riskTolerance > 0.7 ? '- Be open to taking calculated risks' : '- Be cautious and risk-averse',
          personality.communicationStyle === 'ANALYTICAL' ? '- Focus on facts, data, and logical arguments' : 
          personality.communicationStyle === 'EMOTIONAL' ? '- Focus on relationship-building and emotional appeals' :
          personality.communicationStyle === 'DIRECT' ? '- Be concise and to the point' :
          '- Be diplomatic and considerate of the other party\'s perspective',
          '',
          'Important: Stay in character at all times. Do not mention you are an AI or assistant.'
        ].filter(Boolean).join('\n')
      };

      // Add system message to the beginning of the conversation
      const conversation = [systemMessage, ...messages];

      // Get response from AI service
      const response = await firstValueFrom(
        from(
          (async () => {
            const result = await this.aiService.generateChatResponse(conversation as any, {
              max_tokens: 500,
              temperature: 0.7,
            });
            return typeof result === 'string' ? result : '';
          })()
        )
      );

      // Generate suggestions based on the response
      const suggestionPrompt = `Based on this conversation, suggest 3 possible next actions or questions:\n"${response}"`;
      
      const suggestionsResponse = await firstValueFrom(
        from(
          (async () => {
            const result = await this.aiService.generateResponse(suggestionPrompt, {
              max_tokens: 100,
              temperature: 0.7,
            });
            return typeof result === 'string' ? result : '';
          })()
        )
      );

      // Parse suggestions (split by newlines and clean up)
      const suggestions = (suggestionsResponse || '')
        .split('\n')
        .map(s => s.replace(/^\d+[\.\)]\s*/, '').trim())
        .filter((s): s is string => typeof s === 'string' && s.length > 0)
        .slice(0, 3);

      return {
        content: (response || '').trim(),
        suggestions: suggestions.length > 0 ? suggestions : [
          'Can you clarify?',
          'What are the main issues?',
          'Let me think about this'
        ]
      };
    } catch (error) {
      this.logger.error('Error generating AI response:', error);
      return {
        content: "I'm sorry, I encountered an error processing your request. Could you please rephrase or try again later?",
        suggestions: ['Try again', 'Start over', 'Contact support']
      };
    }
  }

  async generateAIResponse(
    session: any,
    userMoveData: any,
    negotiationContext: any
  ): Promise<{ content: string; suggestions: string[]; extractedData?: any }> {
    try {
      const userMessage = userMoveData?.message || '';
      
      // Get document context if available
      const documentContext = await this.getDocumentContextForResponse(session?.id);
      
      // Prepare conversation history
      const messages: AIChatMessage[] = [
        {
          role: 'user',
          content: userMessage
        }
      ];
      
      // Add previous moves if available
      if (negotiationContext?.previousMoves?.length) {
        messages.unshift(...(negotiationContext.previousMoves as Array<{ role: string; content: string }>)
          .filter(m => m?.content)
          .map(m => ({
            role: (m.role === 'ai' ? 'assistant' : 'user') as 'user' | 'assistant',
            content: m.content
          })));
      }
      
      // Get AI personality from session with defaults
      const personality: AIPersonalityProfile = {
        aggressiveness: 0.5,
        flexibility: 0.7,
        riskTolerance: 0.5,
        communicationStyle: 'ANALYTICAL',
        decisionSpeed: 'MODERATE',
        concessionPattern: 'STRATEGIC',
        ...(session?.aiPersonality || {})
      };
      
      // Generate AI response using the LLM with the AI's personality
      const { content: aiResponse, suggestions } = await this.generateAIResponseWithLLM(
        messages,
        documentContext,
        personality
      );
      
      return {
        content: aiResponse,
        suggestions,
        extractedData: {
          strategy: personality.aggressiveness > 0.7 ? 'competitive' : 'collaborative',
          sentiment: personality.aggressiveness > 0.7 ? 'assertive' : 'neutral',
          confidence: 0.8
        }
      };
    } catch (error) {
      this.logger.error('Error generating AI response', error);
      return {
        content: "I'm having trouble generating a response right now. Could you please rephrase or ask something else?",
        suggestions: ["Let's try a different approach", "Can we discuss another aspect?", "I need more information to respond"],
        extractedData: {
          strategy: 'neutral',
          sentiment: {score: 0},
          confidence: 0.5
        }
      };
    }
  }
  
  private findRelevantIssue(message: string, keyIssues: KeyIssue[]): KeyIssue | undefined {
    if (!message || !keyIssues.length) return undefined;
    
    // Convert message to lowercase for case-insensitive matching
    const lowerMessage = message.toLowerCase();
    
    // First, try to find exact matches in issue titles
    const titleMatch = keyIssues.find(issue => 
      lowerMessage.includes(issue.title.toLowerCase())
    );
    if (titleMatch) return titleMatch;
    
    // Then try to find matches in issue descriptions
    const descriptionMatch = keyIssues.find(issue => 
      issue.description && lowerMessage.includes(issue.description.toLowerCase())
    );
    if (descriptionMatch) return descriptionMatch;
    
    // Finally, try to find matches for common terms
    const commonTerms = [
      { term: 'confidential', field: 'title' },
      { term: 'liability', field: 'title' },
      { term: 'indemnif', field: 'title' },
      { term: 'warrant', field: 'title' },
      { term: 'term', field: 'title' },
      { term: 'terminat', field: 'title' },
    ];
    
    for (const { term, field } of commonTerms) {
      if (lowerMessage.includes(term)) {
        const match = keyIssues.find(issue => 
          issue[field] && issue[field].toLowerCase().includes(term)
        );
        if (match) return match;
      }
    }
    
    return undefined;
  }
  
  private handleGreeting(documentContext: string | null) {
    if (documentContext) {
      const contractType = this.extractValue(documentContext, 'Contract Type') || 'agreement';
      const keyIssues = this.extractKeyIssues(documentContext);
      const highRiskCount = keyIssues.filter(i => i.riskLevel === 'HIGH').length;
      
      return {
        content: `Hello! I'm here to help you with the ${contractType}. ` +
                `I've identified ${keyIssues.length} key issues, including ${highRiskCount} high-risk items. ` +
                `How can I assist you with this agreement today?`,
        suggestions: [
          'What are the main issues?',
          'Can you explain the key terms?',
          'Let\'s review the high-risk items'
        ]
      };
    }
    
    return {
      content: 'Hello! I\'m here to help with your agreement. How can I assist you today?',
      suggestions: [
        'Review the agreement',
        'Discuss specific terms',
        'Get help with negotiation'
      ]
    };
  }
  
  /**
   * Handles finalization of the agreement negotiation
   * @param message - The user's message or negotiation context
   * @param context - Optional negotiation context (for backward compatibility)
   */
  private handleFinalization(message: string | any, context?: any) {
    // Handle both signatures: (negotiationContext) and (message, context)
    const negotiationContext = typeof message === 'string' ? context : message;
    
    // Generate a summary of key points if available in the context
    const keyPoints = negotiationContext?.keyPoints || 
                    (negotiationContext?.keyIssues?.map((i: any) => i.title) || []);
    
    const keyPointsText = keyPoints.length > 0 
      ? `Here are the key points we've discussed: ${keyPoints.join(', ')}. `
      : '';
    
    const content = 'I understand you\'d like to finalize the agreement. ' +
                   'Before we conclude, ' + keyPointsText +
                   'Does this align with your understanding?';
    
    const suggestions = [
      'Yes, that looks correct',
      'No, let me clarify something',
      'Can we make one more change?'
    ];
    
    // Return either an object with content/suggestions (for backward compatibility)
    // or just the content string (for the newer implementation)
    return typeof message === 'string' 
      ? content 
      : { content, suggestions };
  }

  private async getDocumentContextForResponse(sessionId: string): Promise<any> {
    try {
      return await this.documentNegotiationService.getNegotiationContext(sessionId);
    } catch (error) {
      this.logger.warn(`Could not get document context for session ${sessionId}: ${error.message}`);
      return null;
    }
  }

  private async generateDocumentAwareResponse(
    session: any, 
    userMoveData: { message: string; [key: string]: any }, 
    documentContext: any
  ): Promise<string> {
    const userMessage = userMoveData.message.trim();
    const contractType = documentContext.contractType || 'agreement';
    const riskLevel = documentContext.riskLevel || 'MEDIUM';
    const keyIssues = documentContext.keyIssues || [];
    const lowerMessage = userMessage.toLowerCase();
    
    // Enhanced context preparation
    const prepareContext = () => {
      const highRiskIssues = keyIssues.filter((i: any) => i.riskLevel === 'HIGH');
      const mediumRiskIssues = keyIssues.filter((i: any) => i.riskLevel === 'MEDIUM');
      
      return {
        contractType,
        riskLevel,
        keyIssues: keyIssues.map((issue: any) => ({
          title: issue.title,
          riskLevel: issue.riskLevel,
          description: issue.description?.substring(0, 200),
          keywords: issue.keywords || []
        })),
        summary: documentContext.summary || 'No additional context available',
        stats: {
          totalIssues: keyIssues.length,
          highRiskCount: highRiskIssues.length,
          mediumRiskCount: mediumRiskIssues.length,
          lastUpdated: new Date().toISOString()
        }
      };
    };

    // Generate AI response with enhanced context and fallback
    const generateAIResponse = async () => {
      try {
        const context = prepareContext();
        // Get AI personality from session or use defaults
        const personality: AIPersonalityProfile = {
          aggressiveness: 0.5,
          flexibility: 0.7,
          riskTolerance: 0.5,
          communicationStyle: 'ANALYTICAL',
          decisionSpeed: 'MODERATE',
          concessionPattern: 'STRATEGIC',
          ...(session?.aiPersonality || {})
        };
        
        const characterId = session?.aiPersonality?.characterId || 'contract_negotiator';
        
        const systemMessage = `
You are ${characterId}, a ${contractType} negotiator with a ${personality.communicationStyle.toLowerCase()} communication style.

Current risk level: ${riskLevel}
Key issues (${context.keyIssues.length} total, ${context.stats.highRiskCount} high risk):
${context.keyIssues.map((i: any) => `- ${i.title} (${i.riskLevel}): ${i.description || 'No description'}`).join('\n')}

Your negotiation approach:
1. Communication: ${this.getCommunicationStyleDescription(personality.communicationStyle, personality.aggressiveness)}
2. Risk tolerance: ${personality.riskTolerance > 0.7 ? 'High' : personality.riskTolerance > 0.3 ? 'Moderate' : 'Low'}
3. Decision speed: ${personality.decisionSpeed.toLowerCase()}
4. Concession style: ${personality.concessionPattern.toLowerCase()}
5. Flexibility: ${personality.flexibility > 0.7 ? 'High' : personality.flexibility > 0.3 ? 'Moderate' : 'Low'}

Guidelines:
1. ${this.getToneGuideline(personality)}
2. ${this.getRiskGuideline(personality, riskLevel)}
3. ${this.getFlexibilityGuideline(personality)}
4. Suggest specific clause language when relevant
5. Stay in character as ${characterId} at all times
        `.trim();

        const messages: AIChatMessage[] = [
          { role: 'system', content: systemMessage },
          { role: 'user', content: userMessage }
        ];

        const aiResponse = await this.generateAIResponseWithLLM(
          messages, 
          JSON.stringify(context),
          { ...personality, characterId }
        );
        
        if (aiResponse?.content?.trim()) {
          return aiResponse.content;
        }
      } catch (error) {
        this.logger.error('AI response generation failed', error);
      }
      return null;
    };

    // Check if we should use rule-based fallback first
    const shouldUseRuleBasedFirst = () => {
      // Use rule-based for simple queries that don't need AI
      const ruleBasedPatterns = [
        /^(hi|hello|hey|greetings)/i,
        /thank(s| you)/i,
        /(what|how).*(your|are you)/i,
        /^\s*$/
      ];
      return ruleBasedPatterns.some(p => p.test(userMessage));
    };

    // Enhanced rule-based responses
    const getRuleBasedResponse = (): string | null => {
      if (this.isGreeting(lowerMessage)) {
        return this.handleGreeting(documentContext).content;
      }
      
      if (lowerMessage.includes('term') || lowerMessage.includes('clause')) {
        return this.generateTermsResponseWithContext(contractType, riskLevel);
      }
      
      const priceMatch = lowerMessage.match(/\$?\d+(?:,\d{3})*(?:\.\d{2})?/);
      if (priceMatch) {
        const price = parseFloat(priceMatch[0].replace(/[^0-9.]/g, ''));
        if (!isNaN(price)) {
          return this.generatePriceResponseWithContext(price, contractType, riskLevel);
        }
      }
      
      if (lowerMessage.includes('risk') || lowerMessage.includes('issue') || lowerMessage.includes('concern')) {
        return this.generateRisksResponse(riskLevel, keyIssues);
      }
      
      const mentionedIssue = this.findRelevantIssue(userMessage, keyIssues);
      if (mentionedIssue) {
        return `Regarding "${mentionedIssue.title}", ${mentionedIssue.description || 'this is an important aspect to consider.'} ` +
               `How would you like to address this in the agreement?`;
      }
      
      return null;
    };

    // Response generation flow
    try {
      // Try rule-based first for simple queries
      if (shouldUseRuleBasedFirst()) {
        const ruleResponse = getRuleBasedResponse();
        if (ruleResponse) return ruleResponse;
      }
      
      // Try AI response
      const aiResponse = await generateAIResponse();
      if (aiResponse) return aiResponse;
      
      // Fall back to rule-based if AI fails
      const fallbackResponse = getRuleBasedResponse();
      if (fallbackResponse) return fallbackResponse;
      
    } catch (error) {
      this.logger.error('Error in generateDocumentAwareResponse', error);
    }
    
    // Final fallback responses
    const defaultResponses = [
      `I understand your concern about the ${contractType}. Let's work together to find a solution that works for both parties.`,
      `Thank you for your input on this ${contractType}. I'll take that into consideration.`,
      `I appreciate your perspective. Let's discuss how we can address this in the ${contractType}.`,
      `That's an important point. Let me think about how we can incorporate that into the agreement.`,
      `I'll need a bit more information to help with that. Could you provide more details?`
    ];
    
    return this.getRandomResponse(defaultResponses);
  }

  private generateDocumentAwareSuggestions(
    documentContext: any, 
    userMoveData: { message: string; [key: string]: any }
  ): string[] {
    const suggestions = new Set<string>();
    const userMessage = userMoveData.message?.toLowerCase() || '';
    const contractType = documentContext.contractType || 'agreement';
    const riskLevel = documentContext.riskLevel || 'MEDIUM';
    const keyIssues = documentContext.keyIssues || [];
    
    // Add context-aware suggestions based on the document type and risk level
    if (riskLevel === 'HIGH') {
      suggestions.add('Can we discuss the high-risk items first?');
      suggestions.add('How should we address the identified risks?');
    }
    
    // Add suggestions based on key issues
    keyIssues.slice(0, 2).forEach(issue => {
      suggestions.add(`Can we discuss ${issue.title}?`);
    });
    
    // Add phase-appropriate suggestions
    if (userMessage.includes('offer') || userMessage.includes('propose')) {
      suggestions.add('Would you like me to make a counter-offer?');
      suggestions.add('What terms are most important to you?');
    } else if (userMessage.includes('agree') || userMessage.includes('accept')) {
      suggestions.add('Should we summarize the agreed terms?');
      suggestions.add('Would you like to finalize the agreement?');
    } else {
      // Default suggestions
      suggestions.add(`What are the key terms for this ${contractType}?`);
      suggestions.add('How flexible are you on these terms?');
      suggestions.add('Can we discuss alternative approaches?');
    }
    
    // Ensure we have at least 3 suggestions
    while (suggestions.size < 3) {
      suggestions.add(this.getRandomResponse([
        'What are your main concerns?',
        'How can we make this work for both parties?',
        'Would you like to propose specific terms?'
      ]));
    }
    
    // Convert to array and limit to 3-4 suggestions
    return Array.from(suggestions).slice(0, 4);
  }

  private generateGreetingResponse(contractType: string, riskLevel: string, keyIssues: KeyIssue[]): string {
    const riskPhrase = riskLevel === 'HIGH' ? 'high-risk' : riskLevel.toLowerCase() + '-risk';
    return `Hello! I'm here to help negotiate this ${contractType} agreement. ` +
           `This is a ${riskPhrase} contract with ${keyIssues.length} key issues to discuss. ` +
           `How would you like to proceed?`;
  }

  private generateInitialOffer(contractType: string, riskLevel: string, keyIssues: KeyIssue[]): string {
    const highRiskIssues = keyIssues.filter(i => i.riskLevel === 'HIGH');
    const focus = highRiskIssues.length > 0 ? 
      `Let's focus on the high-risk items like "${highRiskIssues[0].title}" first. ` : 
      '';
      
    return `Thank you for your interest in this ${contractType}. ${focus}` +
           `I'd be happy to discuss terms that work for both parties. ` +
           `What specific aspects are most important to you?`;
  }

  private generateFinalOffer(keyIssues: KeyIssue[], strategy: string, riskLevel: string): string {
    if (riskLevel === 'HIGH' && strategy !== 'collaborative') {
      const remainingIssues = keyIssues.filter(i => i.riskLevel === 'HIGH' || i.riskLevel === 'MEDIUM');
      return `I appreciate your eagerness to finalize. However, we still have ${remainingIssues.length} ` +
             `important issues to resolve. Let's address these first to ensure a fair agreement.`;
    }
    return 'I believe we can reach a mutually beneficial agreement. Here are the key terms I propose: ' +
           '[Summary of agreed terms]. Does this work for you?';
  }

  private generatePriceResponse(price: number, contractType: string, riskLevel: string, strategy: string): string {
    const priceFormatted = new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: 'USD' 
    }).format(price);
    
    if (riskLevel === 'HIGH') {
      return `I've noted your offer of ${priceFormatted}. Given the high-risk nature of this ${contractType}, ` +
             `I'd like to ensure we address all risk factors before finalizing the price. ` +
             `Would you like to discuss the risk allocation first?`;
    }
    
    const responses = [
      `Thank you for the offer of ${priceFormatted}. This is a good starting point for our ${contractType} negotiation. ` +
      `I suggest we also discuss [other key terms].`,
      `I appreciate your offer of ${priceFormatted}. To ensure a balanced agreement, ` +
      `I'd like to propose [alternative terms] in addition to the price.`
    ];
    
    return this.getRandomResponse(responses);
  }

  private generateTermsResponse(contractType: string, riskLevel: string, keyIssues: KeyIssue[]): string {
    const riskPhrase = riskLevel === 'HIGH' ? 'high-risk' : 'moderate-risk';
    const keyIssue = keyIssues.length > 0 ? 
      `The most critical issue is "${keyIssues[0].title}" - ${keyIssues[0].description.substring(0, 100)}... ` : 
      '';
      
    return `This ${contractType} has been identified as ${riskPhrase}. ${keyIssue}` +
           `I recommend we focus on addressing these key issues first. ` +
           `Would you like to start with a specific clause?`;
  }

  private generateRisksResponse(riskLevel: string, keyIssues: KeyIssue[]): string {
    const highRiskCount = keyIssues.filter(i => i.riskLevel === 'HIGH').length;
    const mediumRiskCount = keyIssues.filter(i => i.riskLevel === 'MEDIUM').length;
    
    if (riskLevel === 'HIGH') {
      return `This agreement has ${highRiskCount} high-risk and ${mediumRiskCount} medium-risk issues. ` +
             `The primary concerns are: ${keyIssues.slice(0, 2).map(i => i.title).join(' and ')}. ` +
             `Would you like to discuss risk mitigation strategies?`;
    }
    
    return `The risk assessment shows ${highRiskCount} high and ${mediumRiskCount} medium risk items. ` +
           `The main areas of concern are ${keyIssues.slice(0, 2).map(i => i.title).join(' and ')}. ` +
           `How would you like to address these?`;
  }

  private generateClarificationResponse(contractType: string, keyIssues: KeyIssue[]): string {
    const sampleIssue = keyIssues.length > 0 ? 
      `For example, regarding "${keyIssues[0].title}", we could consider [suggestion]. ` : '';
      
    return `I'd be happy to clarify. For this ${contractType}, we need to ensure all terms are clear and mutually beneficial. ` +
           `${sampleIssue}What specific aspect would you like me to explain in more detail?`;
  }

  private getDefaultSuggestions(message: string, context: any): string[] {
    const suggestions = new Set<string>();
    
    if (this.isAskingAboutTerms(message)) {
      suggestions.add('Can we discuss the payment terms?');
      suggestions.add('Should we review the liability clauses?');
      suggestions.add('What about the termination conditions?');
    } else if (this.isAskingForOffer(message)) {
      suggestions.add('Would you like me to make a counter-offer?');
      suggestions.add('What terms are most important to you?');
    } else if (this.isFinalizing(message)) {
      suggestions.add('Let me summarize the key points we\'ve agreed on.');
      suggestions.add('Would you like to review the terms one more time?');
    }
    
    // Add some general suggestions
    suggestions.add('What are your main concerns?');
    suggestions.add('How can we make this work for both parties?');
    suggestions.add('Would you like to propose specific terms?');
    
    return Array.from(suggestions).slice(0, 3);
  }

  // Calculate relationship impact based on strategy and sentiment
  private calculateRelationshipImpact(strategy: string, sentiment: string): RelationshipImpact {
    const baseImpact = {
      trust: 0.1,
      respect: 0.05,
      pressure: -0.05
    };
    
    // Adjust based on strategy
    if (strategy === 'collaborative') {
      baseImpact.trust += 0.1;
      baseImpact.respect += 0.1;
      baseImpact.pressure -= 0.05;
    } else if (strategy === 'competitive') {
      baseImpact.trust -= 0.05;
      baseImpact.pressure += 0.1;
    }
    
    // Adjust based on sentiment
    if (sentiment === 'positive') {
      baseImpact.trust += 0.05;
      baseImpact.respect += 0.05;
    } else if (sentiment === 'negative') {
      baseImpact.trust -= 0.05;
      baseImpact.pressure += 0.05;
    }
    
    return baseImpact;
  }

  // Handle offer requests
  private handleOfferRequest(message: string, context: any): string {
    const responses = [
      "I'd be happy to make an offer. Based on the terms we've discussed, I propose [specific terms]. " +
      "How does that sound to you?",
      "Thank you for asking. After reviewing the key points, I suggest [offer details]. " +
      "What are your thoughts on this approach?",
      "I've considered our discussion and would like to propose [offer terms]. " +
      "Would this work for your needs?"
    ];
    
    return this.getRandomResponse(responses);
  }

  // Intent detection methods
  private isGreeting(message: string): boolean {
    return /\b(hi|hello|hey|greetings|good\s*(morning|afternoon|evening))\b/i.test(message);
  }

  private isAskingForOffer(message: string): boolean {
    return /\b(offer|proposal|suggestion|what.*(offer|propose)|make.*offer|your\s+offer)\b/i.test(message);
  }

  private isFinalizing(message: string): boolean {
    if (!message) return false;
    const lowerMessage = message.toLowerCase();
    
    // Check for common finalization phrases
    const finalizationPhrases = [
      'finalize',
      'finalizing',
      'final',
      'last',
      'best',
      'conclude',
      'wrap up',
      'wrap-up',
      'close',
      'done',
      'finish',
      'complete',
      'ready to close',
      'ready to finalize',
      'let\'s wrap',
      'let us wrap',
      'can we finalize',
      'should we finalize',
      'time to finalize',
      'time to close'
    ];
    
    return finalizationPhrases.some(phrase => lowerMessage.includes(phrase));
  }

  private isAskingAboutTerms(message: string): boolean {
    return /\b(term|clause|condition|provision|section|agreement|contract)\b/i.test(message);
  }

  private isAskingAboutRisks(message: string): boolean {
    return /\b(risk|concern|issue|problem|worry|uncertainty|liability|warranty|indemnification)\b/i.test(message);
  }

  private isAskingForClarification(message: string): boolean {
    return /\b(what|how|why|when|where|who|which|explain|clarify|elaborate|mean|understand|confused)\b/i.test(message);
  }

  // Helper method to get a random response from an array
  private getRandomResponse<T>(responses: T[]): T {
    return responses[Math.floor(Math.random() * responses.length)];
  }

  // Backward compatibility methods
  private generateTermsResponseWithContext(contractType: string, riskLevel: string): string {
    return `I agree that the terms are critical, especially given the ${riskLevel.toLowerCase()} risk level of this ${contractType}. Which specific clause would you like to discuss?`;
  }

  private generatePriceResponseWithContext(price: number, contractType: string, riskLevel: string): string {
    return this.generatePriceResponse(price, contractType, riskLevel, 'collaborative');
  }
}

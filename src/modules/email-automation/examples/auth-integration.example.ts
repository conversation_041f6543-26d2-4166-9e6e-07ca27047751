/**
 * Example integration with Auth Service
 * This shows how to trigger automated emails from user registration and other auth events
 */

import { Injectable } from '@nestjs/common';
import { EmailAutomationHooks } from '../hooks/email-automation.hooks';
import { SubscriptionTier } from '../../subscription/enums/subscription-tier.enum';

@Injectable()
export class AuthEmailIntegrationExample {
  constructor(
    private emailAutomationHooks: EmailAutomationHooks,
  ) {}

  /**
   * Example: Trigger welcome email after user registration
   * This would be called from your AuthService.register() method
   */
  async handleUserRegistration(userData: {
    userId: string;
    organizationId: string;
    email: string;
    firstName?: string;
    lastName?: string;
    organizationName?: string;
    tier: SubscriptionTier;
    createNewOrganization?: boolean;
  }): Promise<void> {
    // Trigger welcome email sequence based on tier
    await this.emailAutomationHooks.onUserRegistration({
      userId: userData.userId,
      organizationId: userData.organizationId,
      email: userData.email,
      firstName: userData.firstName,
      lastName: userData.lastName,
      organizationName: userData.organizationName,
      tier: userData.tier,
    });

    // If this is a law firm creating a new organization, trigger enterprise welcome
    if (userData.createNewOrganization && userData.tier === SubscriptionTier.LAW_FIRM) {
      // Additional enterprise-specific welcome sequence would be triggered
      console.log('Enterprise welcome sequence triggered for law firm');
    }
  }

  /**
   * Example: Trigger trial start email
   * This would be called when a user starts a trial subscription
   */
  async handleTrialStart(userData: {
    userId: string;
    organizationId: string;
    email: string;
    firstName?: string;
    lastName?: string;
    tier: SubscriptionTier;
    trialEndDate: Date;
    hourlyRate?: number;
  }): Promise<void> {
    await this.emailAutomationHooks.onTrialStart({
      userId: userData.userId,
      organizationId: userData.organizationId,
      email: userData.email,
      firstName: userData.firstName,
      lastName: userData.lastName,
      tier: userData.tier,
      trialEndDate: userData.trialEndDate,
      hourlyRate: userData.hourlyRate,
    });
  }

  /**
   * Example: Trigger team member invitation email
   * This would be called when adding team members to a law firm
   */
  async handleTeamMemberInvitation(userData: {
    userId: string;
    organizationId: string;
    email: string;
    firstName?: string;
    lastName?: string;
    organizationName?: string;
    invitedBy: string;
    role: string;
  }): Promise<void> {
    await this.emailAutomationHooks.onTeamMemberAdded({
      userId: userData.userId,
      organizationId: userData.organizationId,
      email: userData.email,
      firstName: userData.firstName,
      lastName: userData.lastName,
      organizationName: userData.organizationName,
      invitedBy: userData.invitedBy,
      role: userData.role,
    });
  }
}

/**
 * Example integration in AuthService
 * 
 * In your src/modules/auth/services/auth.service.ts, you would add:
 * 
 * ```typescript
 * import { EmailAutomationHooks } from '../../email-automation/hooks/email-automation.hooks';
 * 
 * @Injectable()
 * export class AuthService {
 *   constructor(
 *     // ... other dependencies
 *     private emailAutomationHooks: EmailAutomationHooks,
 *   ) {}
 * 
 *   async register(createUserDto: CreateUserDto): Promise<any> {
 *     // ... existing registration logic
 * 
 *     // Trigger welcome email
 *     await this.emailAutomationHooks.onUserRegistration({
 *       userId: user.id,
 *       organizationId: user.organizationId,
 *       email: user.email,
 *       firstName: user.firstName,
 *       lastName: user.lastName,
 *       organizationName: user.organizationName,
 *       tier: user.tier,
 *     });
 * 
 *     return result;
 *   }
 * 
 *   async startTrial(userId: string, tier: SubscriptionTier): Promise<any> {
 *     // ... existing trial logic
 * 
 *     // Trigger trial start email
 *     await this.emailAutomationHooks.onTrialStart({
 *       userId,
 *       organizationId: user.organizationId,
 *       email: user.email,
 *       firstName: user.firstName,
 *       tier,
 *       trialEndDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days
 *       hourlyRate: user.hourlyRate,
 *     });
 * 
 *     return result;
 *   }
 * }
 * ```
 */

/**
 * Example integration in SubscriptionService
 * 
 * In your src/modules/subscription/services/subscription.service.ts:
 * 
 * ```typescript
 * import { EmailAutomationHooks } from '../../email-automation/hooks/email-automation.hooks';
 * 
 * @Injectable()
 * export class SubscriptionService {
 *   constructor(
 *     // ... other dependencies
 *     private emailAutomationHooks: EmailAutomationHooks,
 *   ) {}
 * 
 *   async checkCreditBalance(userId: string): Promise<void> {
 *     const subscription = await this.getSubscription(userId);
 *     
 *     if (subscription.credits <= 10) { // Low credit threshold
 *       await this.emailAutomationHooks.onCreditsLow({
 *         userId,
 *         organizationId: subscription.organizationId,
 *         email: subscription.user.email,
 *         firstName: subscription.user.firstName,
 *         tier: subscription.tier,
 *         creditBalance: subscription.credits,
 *         monthlyAllocation: subscription.monthlyCredits,
 *       });
 *     }
 *   }
 * 
 *   async upgradeTier(userId: string, newTier: SubscriptionTier): Promise<any> {
 *     const oldSubscription = await this.getSubscription(userId);
 *     
 *     // ... existing upgrade logic
 * 
 *     // Trigger tier upgrade email
 *     await this.emailAutomationHooks.onTierUpgrade({
 *       userId,
 *       organizationId: oldSubscription.organizationId,
 *       email: oldSubscription.user.email,
 *       firstName: oldSubscription.user.firstName,
 *       fromTier: oldSubscription.tier,
 *       toTier: newTier,
 *       upgradeDate: new Date(),
 *     });
 * 
 *     return result;
 *   }
 * }
 * ```
 */

/**
 * Example integration in DocumentService
 * 
 * In your document processing service:
 * 
 * ```typescript
 * import { EmailAutomationHooks } from '../../email-automation/hooks/email-automation.hooks';
 * 
 * @Injectable()
 * export class DocumentService {
 *   constructor(
 *     // ... other dependencies
 *     private emailAutomationHooks: EmailAutomationHooks,
 *   ) {}
 * 
 *   async uploadDocument(userId: string, documentData: any): Promise<any> {
 *     // ... existing upload logic
 * 
 *     // Check if this is the user's first document
 *     const documentCount = await this.getDocumentCount(userId);
 *     
 *     if (documentCount === 1) { // First document
 *       const user = await this.getUserData(userId);
 *       
 *       await this.emailAutomationHooks.onFirstDocument({
 *         userId,
 *         organizationId: user.organizationId,
 *         email: user.email,
 *         firstName: user.firstName,
 *         tier: user.tier,
 *         documentType: documentData.type,
 *       });
 *     }
 * 
 *     return result;
 *   }
 * }
 * ```
 */

/**
 * Example scheduled job for inactive users
 * 
 * You could create a scheduled job that runs daily to check for inactive users:
 * 
 * ```typescript
 * import { Injectable } from '@nestjs/common';
 * import { Cron, CronExpression } from '@nestjs/schedule';
 * import { EmailAutomationHooks } from '../email-automation/hooks/email-automation.hooks';
 * 
 * @Injectable()
 * export class UserActivityScheduler {
 *   constructor(
 *     private emailAutomationHooks: EmailAutomationHooks,
 *   ) {}
 * 
 *   @Cron(CronExpression.EVERY_DAY_AT_10AM)
 *   async checkInactiveUsers(): Promise<void> {
 *     const inactiveUsers = await this.findInactiveUsers(7); // 7 days inactive
 * 
 *     for (const user of inactiveUsers) {
 *       await this.emailAutomationHooks.onUserInactive({
 *         userId: user.id,
 *         organizationId: user.organizationId,
 *         email: user.email,
 *         firstName: user.firstName,
 *         tier: user.tier,
 *         lastLoginDate: user.lastLoginDate,
 *         daysInactive: user.daysInactive,
 *       });
 *     }
 *   }
 * }
 * ```
 */

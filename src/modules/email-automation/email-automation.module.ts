import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BullModule } from '@nestjs/bull';
import { EmailAutomationService } from './services/email-automation.service';
import { EmailTemplateService } from './services/email-template.service';
import { EmailSequenceService } from './services/email-sequence.service';
import { EmailAutomationController } from './controllers/email-automation.controller';
import { EmailAutomationProcessor } from './processors/email-automation.processor';
import { EmailAutomationHooks } from './hooks/email-automation.hooks';
import { EmailTemplate, EmailTemplateSchema } from './schemas/email-template.schema';
import { EmailSequence, EmailSequenceSchema } from './schemas/email-sequence.schema';
import { EmailLog, EmailLogSchema } from './schemas/email-log.schema';
import { UserEmailState, UserEmailStateSchema } from './schemas/user-email-state.schema';
import { AuthModule } from '../auth/auth.module';
import { SubscriptionModule } from '../subscription/subscription.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: EmailTemplate.name, schema: EmailTemplateSchema },
      { name: EmailSequence.name, schema: EmailSequenceSchema },
      { name: EmailLog.name, schema: EmailLogSchema },
      { name: UserEmailState.name, schema: UserEmailStateSchema },
    ]),
    BullModule.registerQueue({
      name: 'email-automation',
    }),
    AuthModule,
    SubscriptionModule,
  ],
  controllers: [EmailAutomationController],
  providers: [
    EmailAutomationService,
    EmailTemplateService,
    EmailSequenceService,
    EmailAutomationProcessor,
    EmailAutomationHooks,
  ],
  exports: [
    EmailAutomationService,
    EmailTemplateService,
    EmailSequenceService,
    EmailAutomationHooks,
  ],
})
export class EmailAutomationModule {}

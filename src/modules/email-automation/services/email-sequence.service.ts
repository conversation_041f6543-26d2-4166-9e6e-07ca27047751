import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { EmailSequence, EmailSequenceDocument, SequenceStep, SequenceTrigger } from '../schemas/email-sequence.schema';

export interface CreateEmailSequenceDto {
  sequenceId: string;
  name: string;
  description: string;
  targetTier: string;
  trigger: SequenceTrigger;
  steps: SequenceStep[];
  settings?: {
    maxEmails?: number;
    stopOnReply?: boolean;
    stopOnUnsubscribe?: boolean;
    respectQuietHours?: boolean;
    quietHoursStart?: string;
    quietHoursEnd?: string;
    timezone?: string;
  };
}

@Injectable()
export class EmailSequenceService {
  private readonly logger = new Logger(EmailSequenceService.name);

  constructor(
    @InjectModel(EmailSequence.name)
    private emailSequenceModel: Model<EmailSequenceDocument>,
  ) {
    this.initializeDefaultSequences();
  }

  /**
   * Get a sequence by ID
   */
  async getSequence(sequenceId: string): Promise<EmailSequenceDocument | null> {
    return await this.emailSequenceModel.findOne({ 
      sequenceId, 
      isActive: true 
    });
  }

  /**
   * Create a new email sequence
   */
  async createSequence(dto: CreateEmailSequenceDto): Promise<EmailSequenceDocument> {
    const sequence = new this.emailSequenceModel({
      ...dto,
      isActive: true,
      analytics: {
        totalSent: 0,
        totalOpened: 0,
        totalClicked: 0,
        totalConverted: 0,
        averageOpenRate: 0,
        averageClickRate: 0,
        conversionRate: 0,
      },
      metadata: {
        author: 'system',
        version: '1.0',
        lastModified: new Date(),
      },
    });

    return await sequence.save();
  }

  /**
   * Get sequences by trigger event
   */
  async getSequencesByTrigger(event: string, tier?: string): Promise<EmailSequenceDocument[]> {
    const query: any = { 
      'trigger.event': event, 
      isActive: true 
    };
    
    if (tier) {
      query.$or = [
        { targetTier: tier },
        { targetTier: 'all' }
      ];
    }

    return await this.emailSequenceModel.find(query);
  }

  /**
   * Initialize default email sequences for legal profession
   */
  private async initializeDefaultSequences(): Promise<void> {
    try {
      const existingSequences = await this.emailSequenceModel.countDocuments();
      if (existingSequences > 0) {
        this.logger.log('Email sequences already exist, skipping initialization');
        return;
      }

      this.logger.log('Initializing default email sequences...');

      const defaultSequences = this.getDefaultSequences();
      
      for (const sequence of defaultSequences) {
        await this.createSequence(sequence);
      }

      this.logger.log(`Initialized ${defaultSequences.length} default email sequences`);
    } catch (error) {
      this.logger.error('Error initializing default sequences:', error);
    }
  }

  /**
   * Get default email sequences for legal profession
   */
  private getDefaultSequences(): CreateEmailSequenceDto[] {
    return [
      // Law Student Welcome Sequence
      {
        sequenceId: 'law_student_welcome',
        name: 'Law Student Welcome Sequence',
        description: 'Welcome and onboarding sequence for law students',
        targetTier: 'law_student',
        trigger: {
          event: 'user_registration',
          conditions: {
            tier: 'law_student',
          },
        },
        steps: [
          {
            stepNumber: 1,
            templateId: 'law_student_welcome_1',
            delayHours: 0, // Immediate
            isActive: true,
          },
          {
            stepNumber: 2,
            templateId: 'law_student_tips_1',
            delayHours: 72, // 3 days later
            conditions: {
              documentCount: 0, // Only if they haven't uploaded a document
            },
            isActive: true,
          },
          {
            stepNumber: 3,
            templateId: 'law_student_success_stories',
            delayHours: 168, // 1 week later
            isActive: true,
          },
        ],
        settings: {
          maxEmails: 3,
          stopOnReply: true,
          stopOnUnsubscribe: true,
          respectQuietHours: true,
          quietHoursStart: '22:00',
          quietHoursEnd: '08:00',
          timezone: 'America/New_York',
        },
      },

      // Lawyer Trial Sequence
      {
        sequenceId: 'lawyer_trial_sequence',
        name: 'Lawyer Trial Onboarding',
        description: 'Trial onboarding and conversion sequence for lawyers',
        targetTier: 'lawyer',
        trigger: {
          event: 'trial_start',
          conditions: {
            tier: 'lawyer',
          },
        },
        steps: [
          {
            stepNumber: 1,
            templateId: 'lawyer_trial_welcome_1',
            delayHours: 0, // Immediate
            isActive: true,
          },
          {
            stepNumber: 2,
            templateId: 'lawyer_trial_tips_1',
            delayHours: 24, // 1 day later
            isActive: true,
          },
          {
            stepNumber: 3,
            templateId: 'lawyer_trial_roi_calculator',
            delayHours: 72, // 3 days later
            isActive: true,
          },
          {
            stepNumber: 4,
            templateId: 'lawyer_trial_reminder_1',
            delayHours: 240, // 10 days (4 days before trial ends)
            isActive: true,
          },
          {
            stepNumber: 5,
            templateId: 'lawyer_trial_final_reminder',
            delayHours: 312, // 13 days (1 day before trial ends)
            isActive: true,
          },
        ],
        settings: {
          maxEmails: 5,
          stopOnReply: false, // Continue even if they reply
          stopOnUnsubscribe: true,
          respectQuietHours: true,
          quietHoursStart: '20:00',
          quietHoursEnd: '07:00',
          timezone: 'America/New_York',
        },
      },

      // Law Firm Enterprise Onboarding
      {
        sequenceId: 'law_firm_onboarding',
        name: 'Law Firm Enterprise Onboarding',
        description: 'Comprehensive onboarding for law firm enterprise customers',
        targetTier: 'law_firm',
        trigger: {
          event: 'user_registration',
          conditions: {
            tier: 'law_firm',
          },
        },
        steps: [
          {
            stepNumber: 1,
            templateId: 'law_firm_welcome_1',
            delayHours: 0, // Immediate
            isActive: true,
          },
          {
            stepNumber: 2,
            templateId: 'law_firm_setup_guide',
            delayHours: 24, // 1 day later
            isActive: true,
          },
          {
            stepNumber: 3,
            templateId: 'law_firm_training_offer',
            delayHours: 72, // 3 days later
            isActive: true,
          },
          {
            stepNumber: 4,
            templateId: 'law_firm_success_check',
            delayHours: 168, // 1 week later
            isActive: true,
          },
        ],
        settings: {
          maxEmails: 4,
          stopOnReply: true,
          stopOnUnsubscribe: true,
          respectQuietHours: true,
          quietHoursStart: '19:00',
          quietHoursEnd: '08:00',
          timezone: 'America/New_York',
        },
      },

      // Credit Low Warning Sequence (All Tiers)
      {
        sequenceId: 'credit_low_warning',
        name: 'Credit Low Warning Sequence',
        description: 'Alert users when credits are running low',
        targetTier: 'all',
        trigger: {
          event: 'credit_low',
          conditions: {
            creditThreshold: 10, // When credits drop below 10
          },
        },
        steps: [
          {
            stepNumber: 1,
            templateId: 'credit_low_warning',
            delayHours: 0, // Immediate
            isActive: true,
          },
          {
            stepNumber: 2,
            templateId: 'credit_low_final_warning',
            delayHours: 72, // 3 days later if still low
            conditions: {
              creditUsage: 5, // Only if they've used more credits
            },
            isActive: true,
          },
        ],
        settings: {
          maxEmails: 2,
          stopOnReply: false,
          stopOnUnsubscribe: true,
          respectQuietHours: true,
          quietHoursStart: '22:00',
          quietHoursEnd: '08:00',
          timezone: 'America/New_York',
        },
      },

      // Inactive User Re-engagement (All Tiers)
      {
        sequenceId: 'inactive_user_reengagement',
        name: 'Inactive User Re-engagement',
        description: 'Re-engage users who haven\'t logged in recently',
        targetTier: 'all',
        trigger: {
          event: 'inactive_user',
          conditions: {
            daysInactive: 7, // 7 days without login
          },
        },
        steps: [
          {
            stepNumber: 1,
            templateId: 'inactive_user_reminder_1',
            delayHours: 0, // Immediate
            isActive: true,
          },
          {
            stepNumber: 2,
            templateId: 'inactive_user_tips',
            delayHours: 72, // 3 days later
            conditions: {
              lastLoginDays: 10, // Only if still inactive
            },
            isActive: true,
          },
          {
            stepNumber: 3,
            templateId: 'inactive_user_final_attempt',
            delayHours: 168, // 1 week later
            conditions: {
              lastLoginDays: 14, // Only if still inactive
            },
            isActive: true,
          },
        ],
        settings: {
          maxEmails: 3,
          stopOnReply: true,
          stopOnUnsubscribe: true,
          respectQuietHours: true,
          quietHoursStart: '22:00',
          quietHoursEnd: '08:00',
          timezone: 'America/New_York',
        },
      },

      // Tier Upgrade Encouragement
      {
        sequenceId: 'tier_upgrade_encouragement',
        name: 'Tier Upgrade Encouragement',
        description: 'Encourage users to upgrade to higher tiers',
        targetTier: 'law_student',
        trigger: {
          event: 'first_document',
          conditions: {
            tier: 'law_student',
          },
        },
        steps: [
          {
            stepNumber: 1,
            templateId: 'upgrade_benefits_lawyer',
            delayHours: 168, // 1 week after first document
            isActive: true,
          },
          {
            stepNumber: 2,
            templateId: 'upgrade_success_stories',
            delayHours: 336, // 2 weeks later
            conditions: {
              documentCount: 5, // Only if they're actively using the platform
            },
            isActive: true,
          },
        ],
        settings: {
          maxEmails: 2,
          stopOnReply: true,
          stopOnUnsubscribe: true,
          respectQuietHours: true,
          quietHoursStart: '22:00',
          quietHoursEnd: '08:00',
          timezone: 'America/New_York',
        },
      },
    ];
  }

  /**
   * Update sequence analytics
   */
  async updateSequenceAnalytics(
    sequenceId: string,
    event: 'sent' | 'opened' | 'clicked' | 'converted'
  ): Promise<void> {
    const sequence = await this.emailSequenceModel.findOne({ sequenceId });
    if (!sequence) return;

    switch (event) {
      case 'sent':
        sequence.analytics.totalSent = (sequence.analytics.totalSent || 0) + 1;
        break;
      case 'opened':
        sequence.analytics.totalOpened = (sequence.analytics.totalOpened || 0) + 1;
        break;
      case 'clicked':
        sequence.analytics.totalClicked = (sequence.analytics.totalClicked || 0) + 1;
        break;
      case 'converted':
        sequence.analytics.totalConverted = (sequence.analytics.totalConverted || 0) + 1;
        break;
    }

    // Calculate rates
    if (sequence.analytics.totalSent > 0) {
      sequence.analytics.averageOpenRate = 
        (sequence.analytics.totalOpened || 0) / sequence.analytics.totalSent;
      sequence.analytics.averageClickRate = 
        (sequence.analytics.totalClicked || 0) / sequence.analytics.totalSent;
      sequence.analytics.conversionRate = 
        (sequence.analytics.totalConverted || 0) / sequence.analytics.totalSent;
    }

    await sequence.save();
  }

  /**
   * Get sequence analytics
   */
  async getSequenceAnalytics(sequenceId: string): Promise<any> {
    const sequence = await this.emailSequenceModel.findOne({ sequenceId });
    return sequence?.analytics || {};
  }

  /**
   * Deactivate a sequence
   */
  async deactivateSequence(sequenceId: string): Promise<void> {
    await this.emailSequenceModel.updateOne(
      { sequenceId },
      { isActive: false }
    );
  }

  /**
   * Activate a sequence
   */
  async activateSequence(sequenceId: string): Promise<void> {
    await this.emailSequenceModel.updateOne(
      { sequenceId },
      { isActive: true }
    );
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { EmailSequence, EmailSequenceDocument } from '../schemas/email-sequence.schema';
import { UserEmailState, UserEmailStateDocument, ActiveSequence } from '../schemas/user-email-state.schema';
import { EmailLog, EmailLogDocument } from '../schemas/email-log.schema';
import { EmailTemplateService } from './email-template.service';
import { SubscriptionTier } from '../../subscription/enums/subscription-tier.enum';

export interface TriggerEmailSequenceDto {
  userId: string;
  organizationId: string;
  email: string;
  tier: SubscriptionTier;
  event: string;
  metadata?: Record<string, any>;
}

export interface EmailVariables {
  firstName?: string;
  lastName?: string;
  organizationName?: string;
  tier?: string;
  creditBalance?: number;
  documentsProcessed?: number;
  trialEndDate?: Date;
  [key: string]: any;
}

@Injectable()
export class EmailAutomationService {
  private readonly logger = new Logger(EmailAutomationService.name);

  constructor(
    @InjectModel(EmailSequence.name)
    private emailSequenceModel: Model<EmailSequenceDocument>,
    @InjectModel(UserEmailState.name)
    private userEmailStateModel: Model<UserEmailStateDocument>,
    @InjectModel(EmailLog.name)
    private emailLogModel: Model<EmailLogDocument>,
    @InjectQueue('email-automation')
    private emailQueue: Queue,
    private emailTemplateService: EmailTemplateService,
  ) {}

  /**
   * Trigger an email sequence based on user action or event
   */
  async triggerEmailSequence(dto: TriggerEmailSequenceDto): Promise<void> {
    try {
      this.logger.log(`Triggering email sequence for user ${dto.userId}, event: ${dto.event}`);

      // Find applicable sequences for this event and tier
      const sequences = await this.emailSequenceModel.find({
        'trigger.event': dto.event,
        $or: [
          { targetTier: dto.tier },
          { targetTier: 'all' }
        ],
        isActive: true,
      });

      if (sequences.length === 0) {
        this.logger.log(`No active sequences found for event ${dto.event} and tier ${dto.tier}`);
        return;
      }

      // Get or create user email state
      let userState: UserEmailStateDocument | null = await this.userEmailStateModel.findOne({
        userId: dto.userId,
      });

      if (!userState) {
        userState = await this.createUserEmailState(dto);
      }

      // Check if user is unsubscribed
      if (userState.preferences.unsubscribedFromAll) {
        this.logger.log(`User ${dto.userId} is unsubscribed from all emails`);
        return;
      }

      // Start applicable sequences
      for (const sequence of sequences) {
        await this.startSequenceForUser(userState, sequence, dto.metadata);
      }

    } catch (error) {
      this.logger.error(`Error triggering email sequence: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Start a specific sequence for a user
   */
  private async startSequenceForUser(
    userState: UserEmailStateDocument,
    sequence: EmailSequenceDocument,
    metadata?: Record<string, any>,
  ): Promise<void> {
    // Check if user is already in this sequence
    const existingSequence = userState.activeSequences.find(
      seq => seq.sequenceId === sequence.sequenceId && !seq.isCompleted
    );

    if (existingSequence) {
      this.logger.log(`User ${userState.userId} already in sequence ${sequence.sequenceId}`);
      return;
    }

    // Add sequence to user's active sequences
    const activeSequence: ActiveSequence = {
      sequenceId: sequence.sequenceId,
      currentStep: 0,
      startedAt: new Date(),
      nextEmailAt: new Date(), // First email immediately
      isCompleted: false,
      isPaused: false,
    };

    userState.activeSequences.push(activeSequence);
    await userState.save();

    // Schedule first email
    await this.scheduleNextEmail(userState, sequence, 0, metadata);

    this.logger.log(`Started sequence ${sequence.sequenceId} for user ${userState.userId}`);
  }

  /**
   * Schedule the next email in a sequence
   */
  private async scheduleNextEmail(
    userState: UserEmailStateDocument,
    sequence: EmailSequenceDocument,
    stepNumber: number,
    metadata?: Record<string, any>,
  ): Promise<void> {
    const step = sequence.steps[stepNumber];
    if (!step || !step.isActive) {
      this.logger.log(`No active step ${stepNumber} found for sequence ${sequence.sequenceId}`);
      return;
    }

    // Calculate when to send the email
    let sendAt = new Date();
    sendAt.setHours(sendAt.getHours() + step.delayHours);

    // Check quiet hours
    if (sequence.settings.respectQuietHours) {
      sendAt = this.adjustForQuietHours(sendAt, userState, sequence);
    }

    // Queue the email
    await this.emailQueue.add(
      'send-sequence-email',
      {
        userId: userState.userId,
        sequenceId: sequence.sequenceId,
        stepNumber,
        templateId: step.templateId,
        metadata,
      },
      {
        delay: sendAt.getTime() - Date.now(),
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      }
    );

    // Update user's next email time
    const activeSequence = userState.activeSequences.find(
      seq => seq.sequenceId === sequence.sequenceId
    );
    if (activeSequence) {
      activeSequence.nextEmailAt = sendAt;
      await userState.save();
    }

    this.logger.log(`Scheduled email for user ${userState.userId}, sequence ${sequence.sequenceId}, step ${stepNumber} at ${sendAt}`);
  }

  /**
   * Process email sending (called by queue processor)
   */
  async processEmailSending(jobData: any): Promise<void> {
    const { userId, sequenceId, stepNumber, templateId, metadata } = jobData;

    try {
      this.logger.log(`Processing email for user ${userId}, sequence ${sequenceId}, step ${stepNumber}`);

      // Get user state and sequence
      const userState = await this.userEmailStateModel.findOne({ userId });
      const sequence = await this.emailSequenceModel.findOne({ sequenceId });

      if (!userState || !sequence) {
        this.logger.error(`User state or sequence not found for user ${userId}`);
        return;
      }

      // Check if user is still in this sequence
      const activeSequence = userState.activeSequences.find(
        seq => seq.sequenceId === sequenceId && !seq.isCompleted && !seq.isPaused
      );

      if (!activeSequence) {
        this.logger.log(`User ${userId} no longer in active sequence ${sequenceId}`);
        return;
      }

      // Check if user is unsubscribed
      if (userState.preferences.unsubscribedFromAll) {
        this.logger.log(`User ${userId} is unsubscribed, skipping email`);
        return;
      }

      // Get email variables
      const variables = await this.getEmailVariables(userState, metadata);

      // Send the email
      const emailId = await this.sendEmail(
        userState,
        templateId,
        sequenceId,
        stepNumber,
        variables
      );

      // Update sequence progress
      activeSequence.currentStep = stepNumber + 1;

      // Check if sequence is complete
      if (stepNumber + 1 >= sequence.steps.length) {
        activeSequence.isCompleted = true;
        this.logger.log(`Completed sequence ${sequenceId} for user ${userId}`);
      } else {
        // Schedule next email
        await this.scheduleNextEmail(userState, sequence, stepNumber + 1, metadata);
      }

      await userState.save();

    } catch (error) {
      this.logger.error(`Error processing email: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send an individual email
   */
  private async sendEmail(
    userState: UserEmailStateDocument,
    templateId: string,
    sequenceId: string,
    stepNumber: number,
    variables: EmailVariables,
  ): Promise<string> {
    // Get template
    const template = await this.emailTemplateService.getTemplate(templateId);
    if (!template) {
      throw new Error(`Template ${templateId} not found`);
    }

    // Generate unique email ID
    const emailId = `email_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Replace variables in template
    const subject = this.replaceVariables(template.subject, variables);
    const htmlContent = this.replaceVariables(template.htmlContent, variables);
    const textContent = this.replaceVariables(template.textContent, variables);

    // Create email log entry
    const emailLog = new this.emailLogModel({
      emailId,
      userId: userState.userId,
      organizationId: userState.organizationId,
      email: userState.email,
      templateId,
      sequenceId,
      stepNumber,
      subject,
      status: 'queued',
      variables,
    });

    await emailLog.save();

    // TODO: Integrate with actual email service (SendGrid, AWS SES, etc.)
    // For now, we'll simulate sending
    await this.simulateEmailSending(emailLog, subject, htmlContent, textContent);

    // Update user analytics
    userState.analytics.totalEmailsSent = (userState.analytics.totalEmailsSent || 0) + 1;
    userState.analytics.lastEmailSent = new Date();

    return emailId;
  }

  /**
   * Simulate email sending (replace with actual email service)
   */
  private async simulateEmailSending(
    emailLog: EmailLogDocument,
    subject: string,
    htmlContent: string,
    textContent: string,
  ): Promise<void> {
    // Simulate email sending delay
    await new Promise(resolve => setTimeout(resolve, 100));

    // Update email log
    emailLog.status = 'sent';
    emailLog.sentAt = new Date();
    await emailLog.save();

    this.logger.log(`Email sent: ${emailLog.emailId} to ${emailLog.email} - ${subject}`);
  }

  /**
   * Create user email state
   */
  private async createUserEmailState(dto: TriggerEmailSequenceDto): Promise<UserEmailStateDocument> {
    const userState = new this.userEmailStateModel({
      userId: dto.userId,
      organizationId: dto.organizationId,
      email: dto.email,
      tier: dto.tier,
      activeSequences: [],
      emailHistory: [],
      preferences: {},
      analytics: {},
      triggers: {
        registrationDate: new Date(),
      },
    });

    return await userState.save();
  }

  /**
   * Get email variables for template replacement
   */
  private async getEmailVariables(
    userState: UserEmailStateDocument,
    metadata?: Record<string, any>,
  ): Promise<EmailVariables> {
    // TODO: Get user and subscription data from database
    return {
      firstName: metadata?.firstName || 'Valued User',
      lastName: metadata?.lastName || '',
      organizationName: metadata?.organizationName || 'Your Organization',
      tier: userState.tier,
      creditBalance: metadata?.creditBalance || 0,
      documentsProcessed: metadata?.documentsProcessed || 0,
      trialEndDate: metadata?.trialEndDate,
      ...metadata,
    };
  }

  /**
   * Replace variables in template content
   */
  private replaceVariables(content: string, variables: EmailVariables): string {
    let result = content;
    
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, String(value || ''));
    });

    return result;
  }

  /**
   * Adjust send time for quiet hours
   */
  private adjustForQuietHours(
    sendAt: Date,
    userState: UserEmailStateDocument,
    sequence: EmailSequenceDocument,
  ): Date {
    // Implementation for quiet hours logic
    // For now, return the original time
    return sendAt;
  }

  /**
   * Handle email events (opens, clicks, etc.)
   */
  async handleEmailEvent(
    emailId: string,
    event: 'opened' | 'clicked' | 'replied' | 'bounced' | 'unsubscribed',
    metadata?: Record<string, any>,
  ): Promise<void> {
    const emailLog = await this.emailLogModel.findOne({ emailId });
    if (!emailLog) {
      this.logger.error(`Email log not found for emailId: ${emailId}`);
      return;
    }

    // Update email log
    const now = new Date();
    switch (event) {
      case 'opened':
        emailLog.openedAt = now;
        emailLog.status = 'opened';
        break;
      case 'clicked':
        emailLog.clickedAt = now;
        emailLog.status = 'clicked';
        if (metadata?.clickedLinks) {
          emailLog.metadata.clickedLinks = metadata.clickedLinks;
        }
        break;
      case 'replied':
        emailLog.repliedAt = now;
        emailLog.status = 'replied';
        break;
      case 'bounced':
        emailLog.bouncedAt = now;
        emailLog.status = 'bounced';
        if (metadata?.bounceReason) {
          emailLog.metadata.bounceReason = metadata.bounceReason;
        }
        break;
      case 'unsubscribed':
        emailLog.unsubscribedAt = now;
        emailLog.status = 'unsubscribed';
        // Also update user preferences
        await this.userEmailStateModel.updateOne(
          { userId: emailLog.userId },
          { 'preferences.unsubscribedFromAll': true }
        );
        break;
    }

    await emailLog.save();

    // Update user analytics
    await this.updateUserAnalytics(emailLog.userId, event);

    this.logger.log(`Email event ${event} recorded for email ${emailId}`);
  }

  /**
   * Update user analytics based on email events
   */
  private async updateUserAnalytics(userId: string, event: string): Promise<void> {
    const userState = await this.userEmailStateModel.findOne({ userId });
    if (!userState) return;

    const now = new Date();
    
    switch (event) {
      case 'opened':
        userState.analytics.totalEmailsOpened = (userState.analytics.totalEmailsOpened || 0) + 1;
        userState.analytics.lastEmailOpened = now;
        break;
      case 'clicked':
        userState.analytics.totalEmailsClicked = (userState.analytics.totalEmailsClicked || 0) + 1;
        userState.analytics.lastEmailClicked = now;
        break;
    }

    // Calculate engagement rates
    if (userState.analytics.totalEmailsSent > 0) {
      userState.analytics.averageOpenRate = 
        (userState.analytics.totalEmailsOpened || 0) / userState.analytics.totalEmailsSent;
      userState.analytics.averageClickRate = 
        (userState.analytics.totalEmailsClicked || 0) / userState.analytics.totalEmailsSent;
    }

    await userState.save();
  }

  /**
   * Pause a sequence for a user
   */
  async pauseSequence(userId: string, sequenceId: string, reason?: string): Promise<void> {
    const userState = await this.userEmailStateModel.findOne({ userId });
    if (!userState) return;

    const activeSequence = userState.activeSequences.find(
      seq => seq.sequenceId === sequenceId && !seq.isCompleted
    );

    if (activeSequence) {
      activeSequence.isPaused = true;
      activeSequence.pauseReason = reason;
      await userState.save();
      
      this.logger.log(`Paused sequence ${sequenceId} for user ${userId}: ${reason}`);
    }
  }

  /**
   * Resume a paused sequence for a user
   */
  async resumeSequence(userId: string, sequenceId: string): Promise<void> {
    const userState = await this.userEmailStateModel.findOne({ userId });
    if (!userState) return;

    const activeSequence = userState.activeSequences.find(
      seq => seq.sequenceId === sequenceId && seq.isPaused
    );

    if (activeSequence) {
      activeSequence.isPaused = false;
      activeSequence.pauseReason = undefined;
      await userState.save();
      
      this.logger.log(`Resumed sequence ${sequenceId} for user ${userId}`);
    }
  }
}

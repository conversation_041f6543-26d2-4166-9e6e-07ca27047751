import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type EmailSequenceDocument = EmailSequence & Document;

export interface SequenceStep {
  stepNumber: number;
  templateId: string;
  delayHours: number;
  conditions?: {
    userAction?: string;
    creditUsage?: number;
    documentCount?: number;
    lastLoginDays?: number;
  };
  isActive: boolean;
}

export interface SequenceTrigger {
  event: 'user_registration' | 'trial_start' | 'first_document' | 'credit_low' | 'inactive_user' | 'tier_upgrade';
  conditions?: {
    tier?: string;
    daysInactive?: number;
    creditThreshold?: number;
  };
}

@Schema({
  timestamps: true,
  collection: 'email_sequences',
})
export class EmailSequence {
  @Prop({ required: true, unique: true })
  sequenceId: string;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  description: string;

  @Prop({
    type: String,
    enum: ['law_student', 'lawyer', 'law_firm', 'all'],
    required: true,
  })
  targetTier: string;

  @Prop({ type: Object, required: true })
  trigger: SequenceTrigger;

  @Prop({ type: [Object], required: true })
  steps: SequenceStep[];

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ type: Object, default: {} })
  settings: {
    maxEmails?: number;
    stopOnReply?: boolean;
    stopOnUnsubscribe?: boolean;
    respectQuietHours?: boolean;
    quietHoursStart?: string;
    quietHoursEnd?: string;
    timezone?: string;
  };

  @Prop({ type: Object, default: {} })
  analytics: {
    totalSent?: number;
    totalOpened?: number;
    totalClicked?: number;
    totalConverted?: number;
    averageOpenRate?: number;
    averageClickRate?: number;
    conversionRate?: number;
  };

  @Prop({ type: Object, default: {} })
  metadata: {
    author?: string;
    version?: string;
    lastModified?: Date;
  };

  @Prop({ type: Date })
  createdAt: Date;

  @Prop({ type: Date })
  updatedAt: Date;
}

export const EmailSequenceSchema = SchemaFactory.createForClass(EmailSequence);

// Indexes for performance
EmailSequenceSchema.index({ sequenceId: 1 });
EmailSequenceSchema.index({ targetTier: 1, isActive: 1 });
EmailSequenceSchema.index({ 'trigger.event': 1 });

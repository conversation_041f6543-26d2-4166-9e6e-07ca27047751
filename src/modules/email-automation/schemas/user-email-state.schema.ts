import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type UserEmailStateDocument = UserEmailState & Document;

export interface ActiveSequence {
  sequenceId: string;
  currentStep: number;
  startedAt: Date;
  nextEmailAt: Date;
  isCompleted: boolean;
  isPaused: boolean;
  pauseReason?: string;
}

export interface EmailInteraction {
  emailId: string;
  templateId: string;
  sentAt: Date;
  openedAt?: Date;
  clickedAt?: Date;
  repliedAt?: Date;
  unsubscribedAt?: Date;
  bouncedAt?: Date;
  status: 'sent' | 'delivered' | 'opened' | 'clicked' | 'replied' | 'bounced' | 'unsubscribed';
}

@Schema({
  timestamps: true,
  collection: 'user_email_states',
})
export class UserEmailState {
  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  organizationId: string;

  @Prop({ required: true })
  email: string;

  @Prop({
    type: String,
    enum: ['law_student', 'lawyer', 'law_firm'],
    required: true,
  })
  tier: string;

  @Prop({ type: [Object], default: [] })
  activeSequences: ActiveSequence[];

  @Prop({ type: [Object], default: [] })
  emailHistory: EmailInteraction[];

  @Prop({ type: Object, default: {} })
  preferences: {
    unsubscribedFromAll?: boolean;
    unsubscribedCategories?: string[];
    frequency?: 'daily' | 'weekly' | 'monthly';
    timezone?: string;
    quietHours?: {
      start: string;
      end: string;
    };
  };

  @Prop({ type: Object, default: {} })
  analytics: {
    totalEmailsSent?: number;
    totalEmailsOpened?: number;
    totalEmailsClicked?: number;
    lastEmailSent?: Date;
    lastEmailOpened?: Date;
    lastEmailClicked?: Date;
    averageOpenRate?: number;
    averageClickRate?: number;
    engagementScore?: number;
  };

  @Prop({ type: Object, default: {} })
  triggers: {
    registrationDate?: Date;
    firstDocumentDate?: Date;
    lastLoginDate?: Date;
    trialStartDate?: Date;
    trialEndDate?: Date;
    lastCreditUsage?: Date;
    tierUpgradeDate?: Date;
  };

  @Prop({ type: Date })
  createdAt: Date;

  @Prop({ type: Date })
  updatedAt: Date;
}

export const UserEmailStateSchema = SchemaFactory.createForClass(UserEmailState);

// Indexes for performance
UserEmailStateSchema.index({ userId: 1 });
UserEmailStateSchema.index({ organizationId: 1 });
UserEmailStateSchema.index({ email: 1 });
UserEmailStateSchema.index({ tier: 1 });
UserEmailStateSchema.index({ 'activeSequences.nextEmailAt': 1 });
UserEmailStateSchema.index({ 'preferences.unsubscribedFromAll': 1 });

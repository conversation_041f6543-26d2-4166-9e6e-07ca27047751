import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type EmailLogDocument = EmailLog & Document;

@Schema({
  timestamps: true,
  collection: 'email_logs',
})
export class EmailLog {
  @Prop({ required: true })
  emailId: string;

  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  organizationId: string;

  @Prop({ required: true })
  email: string;

  @Prop({ required: true })
  templateId: string;

  @Prop({ required: true })
  sequenceId: string;

  @Prop({ required: true })
  stepNumber: number;

  @Prop({ required: true })
  subject: string;

  @Prop({
    type: String,
    enum: ['queued', 'sent', 'delivered', 'opened', 'clicked', 'replied', 'bounced', 'unsubscribed', 'failed'],
    default: 'queued',
  })
  status: string;

  @Prop({ type: Date })
  sentAt: Date;

  @Prop({ type: Date })
  deliveredAt: Date;

  @Prop({ type: Date })
  openedAt: Date;

  @Prop({ type: Date })
  clickedAt: Date;

  @Prop({ type: Date })
  repliedAt: Date;

  @Prop({ type: Date })
  bouncedAt: Date;

  @Prop({ type: Date })
  unsubscribedAt: Date;

  @Prop({ type: Object, default: {} })
  metadata: {
    emailProvider?: string;
    messageId?: string;
    bounceReason?: string;
    clickedLinks?: string[];
    userAgent?: string;
    ipAddress?: string;
    location?: {
      country?: string;
      city?: string;
    };
  };

  @Prop({ type: Object, default: {} })
  variables: Record<string, any>;

  @Prop({ type: String })
  errorMessage?: string;

  @Prop({ type: Date })
  createdAt: Date;

  @Prop({ type: Date })
  updatedAt: Date;
}

export const EmailLogSchema = SchemaFactory.createForClass(EmailLog);

// Indexes for performance
EmailLogSchema.index({ emailId: 1 });
EmailLogSchema.index({ userId: 1 });
EmailLogSchema.index({ organizationId: 1 });
EmailLogSchema.index({ templateId: 1 });
EmailLogSchema.index({ sequenceId: 1 });
EmailLogSchema.index({ status: 1 });
EmailLogSchema.index({ sentAt: 1 });
EmailLogSchema.index({ createdAt: 1 });

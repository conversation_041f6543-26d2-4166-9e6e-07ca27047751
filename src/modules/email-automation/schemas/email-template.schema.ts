import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type EmailTemplateDocument = EmailTemplate & Document;

@Schema({
  timestamps: true,
  collection: 'email_templates',
})
export class EmailTemplate {
  @Prop({ required: true, unique: true })
  templateId: string;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  subject: string;

  @Prop({ required: true })
  htmlContent: string;

  @Prop({ required: true })
  textContent: string;

  @Prop({
    type: String,
    enum: ['law_student', 'lawyer', 'law_firm', 'all'],
    required: true,
  })
  targetTier: string;

  @Prop({
    type: String,
    enum: ['welcome', 'onboarding', 'engagement', 'retention', 'upgrade', 'trial'],
    required: true,
  })
  category: string;

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({ type: Object, default: {} })
  variables: Record<string, any>;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ type: Object, default: {} })
  metadata: {
    author?: string;
    version?: string;
    lastModified?: Date;
    testResults?: {
      openRate?: number;
      clickRate?: number;
      conversionRate?: number;
    };
  };

  @Prop({ type: Date })
  createdAt: Date;

  @Prop({ type: Date })
  updatedAt: Date;
}

export const EmailTemplateSchema = SchemaFactory.createForClass(EmailTemplate);

// Indexes for performance
EmailTemplateSchema.index({ templateId: 1 });
EmailTemplateSchema.index({ targetTier: 1, category: 1 });
EmailTemplateSchema.index({ isActive: 1 });

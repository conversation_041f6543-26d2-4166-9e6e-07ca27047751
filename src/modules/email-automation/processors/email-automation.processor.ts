import { Processor, Process } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { EmailAutomationService } from '../services/email-automation.service';
import { EmailSequenceService } from '../services/email-sequence.service';

@Processor('email-automation')
export class EmailAutomationProcessor {
  private readonly logger = new Logger(EmailAutomationProcessor.name);

  constructor(
    private emailAutomationService: EmailAutomationService,
    private emailSequenceService: EmailSequenceService,
  ) {}

  /**
   * Process sending sequence emails
   */
  @Process('send-sequence-email')
  async handleSendSequenceEmail(job: Job): Promise<void> {
    const { userId, sequenceId, stepNumber, templateId, metadata } = job.data;

    this.logger.log(`Processing email job for user ${userId}, sequence ${sequenceId}, step ${stepNumber}`);

    try {
      await this.emailAutomationService.processEmailSending(job.data);
      
      // Update sequence analytics
      await this.emailSequenceService.updateSequenceAnalytics(sequenceId, 'sent');
      
      this.logger.log(`Successfully sent email for user ${userId}, sequence ${sequenceId}, step ${stepNumber}`);
    } catch (error) {
      this.logger.error(`Failed to send email for user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Process email events (opens, clicks, etc.)
   */
  @Process('email-event')
  async handleEmailEvent(job: Job): Promise<void> {
    const { emailId, event, sequenceId, metadata } = job.data;

    this.logger.log(`Processing email event ${event} for email ${emailId}`);

    try {
      await this.emailAutomationService.handleEmailEvent(emailId, event, metadata);
      
      // Update sequence analytics
      if (sequenceId) {
        await this.emailSequenceService.updateSequenceAnalytics(sequenceId, event);
      }
      
      this.logger.log(`Successfully processed email event ${event} for email ${emailId}`);
    } catch (error) {
      this.logger.error(`Failed to process email event ${event}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Process bulk email triggers
   */
  @Process('bulk-email-trigger')
  async handleBulkEmailTrigger(job: Job): Promise<void> {
    const { event, filters, metadata } = job.data;

    this.logger.log(`Processing bulk email trigger for event ${event}`);

    try {
      // This would typically query users based on filters and trigger emails
      // Implementation depends on your user management system
      
      this.logger.log(`Successfully processed bulk email trigger for event ${event}`);
    } catch (error) {
      this.logger.error(`Failed to process bulk email trigger: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Process scheduled email cleanup
   */
  @Process('email-cleanup')
  async handleEmailCleanup(job: Job): Promise<void> {
    const { olderThanDays } = job.data;

    this.logger.log(`Processing email cleanup for emails older than ${olderThanDays} days`);

    try {
      // Implementation for cleaning up old email logs
      // This could archive or delete old email records
      
      this.logger.log(`Successfully completed email cleanup`);
    } catch (error) {
      this.logger.error(`Failed to process email cleanup: ${error.message}`, error.stack);
      throw error;
    }
  }
}

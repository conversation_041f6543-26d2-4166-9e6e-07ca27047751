import { Controller, Post, Get, Body, Param, Query, UseGuards } from '@nestjs/common';
import { EmailAutomationService, TriggerEmailSequenceDto } from '../services/email-automation.service';
import { EmailTemplateService } from '../services/email-template.service';
import { EmailSequenceService } from '../services/email-sequence.service';
import { TenantGuard } from '../../auth/guards/tenant.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../auth/enums/roles.enum';

@Controller('email-automation')
@UseGuards(TenantGuard, RolesGuard)
export class EmailAutomationController {
  constructor(
    private emailAutomationService: EmailAutomationService,
    private emailTemplateService: EmailTemplateService,
    private emailSequenceService: EmailSequenceService,
  ) {}

  /**
   * Trigger an email sequence manually (Admin only)
   */
  @Post('trigger')
  @Roles(UserRole.ADMIN)
  async triggerEmailSequence(@Body() dto: TriggerEmailSequenceDto) {
    await this.emailAutomationService.triggerEmailSequence(dto);
    return {
      success: true,
      message: 'Email sequence triggered successfully',
    };
  }

  /**
   * Handle email events (webhooks from email providers)
   */
  @Post('events/:emailId')
  async handleEmailEvent(
    @Param('emailId') emailId: string,
    @Body() eventData: {
      event: 'opened' | 'clicked' | 'replied' | 'bounced' | 'unsubscribed';
      metadata?: Record<string, any>;
    }
  ) {
    await this.emailAutomationService.handleEmailEvent(
      emailId,
      eventData.event,
      eventData.metadata
    );
    
    return {
      success: true,
      message: 'Email event processed successfully',
    };
  }

  /**
   * Pause a sequence for a user (Admin only)
   */
  @Post('sequences/:sequenceId/pause')
  @Roles(UserRole.ADMIN)
  async pauseSequence(
    @Param('sequenceId') sequenceId: string,
    @Body() body: { userId: string; reason?: string }
  ) {
    await this.emailAutomationService.pauseSequence(
      body.userId,
      sequenceId,
      body.reason
    );
    
    return {
      success: true,
      message: 'Sequence paused successfully',
    };
  }

  /**
   * Resume a paused sequence for a user (Admin only)
   */
  @Post('sequences/:sequenceId/resume')
  @Roles(UserRole.ADMIN)
  async resumeSequence(
    @Param('sequenceId') sequenceId: string,
    @Body() body: { userId: string }
  ) {
    await this.emailAutomationService.resumeSequence(body.userId, sequenceId);
    
    return {
      success: true,
      message: 'Sequence resumed successfully',
    };
  }

  /**
   * Get email templates by category
   */
  @Get('templates')
  @Roles(UserRole.ADMIN)
  async getTemplates(
    @Query('category') category?: string,
    @Query('tier') tier?: string
  ) {
    const templates = await this.emailTemplateService.getTemplatesByCategory(
      category,
      tier
    );
    
    return {
      success: true,
      data: templates,
    };
  }

  /**
   * Get a specific email template
   */
  @Get('templates/:templateId')
  @Roles(UserRole.ADMIN)
  async getTemplate(@Param('templateId') templateId: string) {
    const template = await this.emailTemplateService.getTemplate(templateId);
    
    return {
      success: true,
      data: template,
    };
  }

  /**
   * Create a new email template (Admin only)
   */
  @Post('templates')
  @Roles(UserRole.ADMIN)
  async createTemplate(@Body() templateData: any) {
    const template = await this.emailTemplateService.createTemplate(templateData);
    
    return {
      success: true,
      data: template,
      message: 'Email template created successfully',
    };
  }

  /**
   * Update an email template (Admin only)
   */
  @Post('templates/:templateId')
  @Roles(UserRole.ADMIN)
  async updateTemplate(
    @Param('templateId') templateId: string,
    @Body() updates: any
  ) {
    const template = await this.emailTemplateService.updateTemplate(
      templateId,
      updates
    );
    
    return {
      success: true,
      data: template,
      message: 'Email template updated successfully',
    };
  }

  /**
   * Get email sequences
   */
  @Get('sequences')
  @Roles(UserRole.ADMIN)
  async getSequences(
    @Query('event') event?: string,
    @Query('tier') tier?: string
  ) {
    const sequences = await this.emailSequenceService.getSequencesByTrigger(
      event,
      tier
    );
    
    return {
      success: true,
      data: sequences,
    };
  }

  /**
   * Get a specific email sequence
   */
  @Get('sequences/:sequenceId')
  @Roles(UserRole.ADMIN)
  async getSequence(@Param('sequenceId') sequenceId: string) {
    const sequence = await this.emailSequenceService.getSequence(sequenceId);
    
    return {
      success: true,
      data: sequence,
    };
  }

  /**
   * Get sequence analytics
   */
  @Get('sequences/:sequenceId/analytics')
  @Roles(UserRole.ADMIN)
  async getSequenceAnalytics(@Param('sequenceId') sequenceId: string) {
    const analytics = await this.emailSequenceService.getSequenceAnalytics(sequenceId);
    
    return {
      success: true,
      data: analytics,
    };
  }

  /**
   * Activate a sequence (Admin only)
   */
  @Post('sequences/:sequenceId/activate')
  @Roles(UserRole.ADMIN)
  async activateSequence(@Param('sequenceId') sequenceId: string) {
    await this.emailSequenceService.activateSequence(sequenceId);
    
    return {
      success: true,
      message: 'Sequence activated successfully',
    };
  }

  /**
   * Deactivate a sequence (Admin only)
   */
  @Post('sequences/:sequenceId/deactivate')
  @Roles(UserRole.ADMIN)
  async deactivateSequence(@Param('sequenceId') sequenceId: string) {
    await this.emailSequenceService.deactivateSequence(sequenceId);
    
    return {
      success: true,
      message: 'Sequence deactivated successfully',
    };
  }

  /**
   * Create a new email sequence (Admin only)
   */
  @Post('sequences')
  @Roles(UserRole.ADMIN)
  async createSequence(@Body() sequenceData: any) {
    const sequence = await this.emailSequenceService.createSequence(sequenceData);
    
    return {
      success: true,
      data: sequence,
      message: 'Email sequence created successfully',
    };
  }

  /**
   * Test email template (Admin only)
   */
  @Post('test-email')
  @Roles(UserRole.ADMIN)
  async testEmail(
    @Body() testData: {
      templateId: string;
      email: string;
      variables?: Record<string, any>;
    }
  ) {
    // This would send a test email using the template
    // Implementation depends on your email service integration
    
    return {
      success: true,
      message: 'Test email sent successfully',
    };
  }

  /**
   * Get email automation statistics (Admin only)
   */
  @Get('stats')
  @Roles(UserRole.ADMIN)
  async getEmailStats(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('tier') tier?: string
  ) {
    // This would return comprehensive email automation statistics
    // Implementation depends on your analytics requirements
    
    return {
      success: true,
      data: {
        totalEmailsSent: 0,
        totalEmailsOpened: 0,
        totalEmailsClicked: 0,
        averageOpenRate: 0,
        averageClickRate: 0,
        activeSequences: 0,
        activeUsers: 0,
      },
    };
  }
}

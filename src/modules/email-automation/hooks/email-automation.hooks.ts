import { Injectable, Logger } from '@nestjs/common';
import { EmailAutomationService } from '../services/email-automation.service';
import { SubscriptionTier } from '../../subscription/enums/subscription-tier.enum';

@Injectable()
export class EmailAutomationHooks {
  private readonly logger = new Logger(EmailAutomationHooks.name);

  constructor(
    private emailAutomationService: EmailAutomationService,
  ) {}

  /**
   * Trigger email when user registers
   */
  async onUserRegistration(userData: {
    userId: string;
    organizationId: string;
    email: string;
    firstName?: string;
    lastName?: string;
    organizationName?: string;
    tier: SubscriptionTier;
  }): Promise<void> {
    try {
      this.logger.log(`Triggering registration email for user ${userData.userId}`);

      await this.emailAutomationService.triggerEmailSequence({
        userId: userData.userId,
        organizationId: userData.organizationId,
        email: userData.email,
        tier: userData.tier,
        event: 'user_registration',
        metadata: {
          firstName: userData.firstName,
          lastName: userData.lastName,
          organizationName: userData.organizationName,
          registrationDate: new Date(),
        },
      });

      this.logger.log(`Registration email triggered for user ${userData.userId}`);
    } catch (error) {
      this.logger.error(`Failed to trigger registration email: ${error.message}`, error.stack);
    }
  }

  /**
   * Trigger email when trial starts
   */
  async onTrialStart(userData: {
    userId: string;
    organizationId: string;
    email: string;
    firstName?: string;
    lastName?: string;
    tier: SubscriptionTier;
    trialEndDate?: Date;
    hourlyRate?: number;
  }): Promise<void> {
    try {
      this.logger.log(`Triggering trial start email for user ${userData.userId}`);

      const monthlySavings = userData.hourlyRate ? userData.hourlyRate * 8 : 2400; // 8 hours saved per month

      await this.emailAutomationService.triggerEmailSequence({
        userId: userData.userId,
        organizationId: userData.organizationId,
        email: userData.email,
        tier: userData.tier,
        event: 'trial_start',
        metadata: {
          firstName: userData.firstName,
          lastName: userData.lastName,
          trialEndDate: userData.trialEndDate,
          hourlyRate: userData.hourlyRate || 300,
          monthlySavings,
        },
      });

      this.logger.log(`Trial start email triggered for user ${userData.userId}`);
    } catch (error) {
      this.logger.error(`Failed to trigger trial start email: ${error.message}`, error.stack);
    }
  }

  /**
   * Trigger email when user uploads first document
   */
  async onFirstDocument(userData: {
    userId: string;
    organizationId: string;
    email: string;
    firstName?: string;
    tier: SubscriptionTier;
    documentType?: string;
  }): Promise<void> {
    try {
      this.logger.log(`Triggering first document email for user ${userData.userId}`);

      await this.emailAutomationService.triggerEmailSequence({
        userId: userData.userId,
        organizationId: userData.organizationId,
        email: userData.email,
        tier: userData.tier,
        event: 'first_document',
        metadata: {
          firstName: userData.firstName,
          documentType: userData.documentType,
          firstDocumentDate: new Date(),
        },
      });

      this.logger.log(`First document email triggered for user ${userData.userId}`);
    } catch (error) {
      this.logger.error(`Failed to trigger first document email: ${error.message}`, error.stack);
    }
  }

  /**
   * Trigger email when credits are low
   */
  async onCreditsLow(userData: {
    userId: string;
    organizationId: string;
    email: string;
    firstName?: string;
    tier: SubscriptionTier;
    creditBalance: number;
    monthlyAllocation: number;
  }): Promise<void> {
    try {
      this.logger.log(`Triggering low credits email for user ${userData.userId}`);

      await this.emailAutomationService.triggerEmailSequence({
        userId: userData.userId,
        organizationId: userData.organizationId,
        email: userData.email,
        tier: userData.tier,
        event: 'credit_low',
        metadata: {
          firstName: userData.firstName,
          creditBalance: userData.creditBalance,
          monthlyAllocation: userData.monthlyAllocation,
          percentageUsed: Math.round((1 - userData.creditBalance / userData.monthlyAllocation) * 100),
        },
      });

      this.logger.log(`Low credits email triggered for user ${userData.userId}`);
    } catch (error) {
      this.logger.error(`Failed to trigger low credits email: ${error.message}`, error.stack);
    }
  }

  /**
   * Trigger email when user is inactive
   */
  async onUserInactive(userData: {
    userId: string;
    organizationId: string;
    email: string;
    firstName?: string;
    tier: SubscriptionTier;
    lastLoginDate: Date;
    daysInactive: number;
  }): Promise<void> {
    try {
      this.logger.log(`Triggering inactive user email for user ${userData.userId}`);

      await this.emailAutomationService.triggerEmailSequence({
        userId: userData.userId,
        organizationId: userData.organizationId,
        email: userData.email,
        tier: userData.tier,
        event: 'inactive_user',
        metadata: {
          firstName: userData.firstName,
          lastLoginDate: userData.lastLoginDate,
          daysInactive: userData.daysInactive,
        },
      });

      this.logger.log(`Inactive user email triggered for user ${userData.userId}`);
    } catch (error) {
      this.logger.error(`Failed to trigger inactive user email: ${error.message}`, error.stack);
    }
  }

  /**
   * Trigger email when user upgrades tier
   */
  async onTierUpgrade(userData: {
    userId: string;
    organizationId: string;
    email: string;
    firstName?: string;
    fromTier: SubscriptionTier;
    toTier: SubscriptionTier;
    upgradeDate: Date;
  }): Promise<void> {
    try {
      this.logger.log(`Triggering tier upgrade email for user ${userData.userId}`);

      await this.emailAutomationService.triggerEmailSequence({
        userId: userData.userId,
        organizationId: userData.organizationId,
        email: userData.email,
        tier: userData.toTier,
        event: 'tier_upgrade',
        metadata: {
          firstName: userData.firstName,
          fromTier: userData.fromTier,
          toTier: userData.toTier,
          upgradeDate: userData.upgradeDate,
        },
      });

      this.logger.log(`Tier upgrade email triggered for user ${userData.userId}`);
    } catch (error) {
      this.logger.error(`Failed to trigger tier upgrade email: ${error.message}`, error.stack);
    }
  }

  /**
   * Trigger welcome email for law firm team member
   */
  async onTeamMemberAdded(userData: {
    userId: string;
    organizationId: string;
    email: string;
    firstName?: string;
    lastName?: string;
    organizationName?: string;
    invitedBy?: string;
    role?: string;
  }): Promise<void> {
    try {
      this.logger.log(`Triggering team member welcome email for user ${userData.userId}`);

      await this.emailAutomationService.triggerEmailSequence({
        userId: userData.userId,
        organizationId: userData.organizationId,
        email: userData.email,
        tier: SubscriptionTier.LAW_FIRM,
        event: 'user_registration',
        metadata: {
          firstName: userData.firstName,
          lastName: userData.lastName,
          organizationName: userData.organizationName,
          invitedBy: userData.invitedBy,
          role: userData.role,
          isTeamMember: true,
        },
      });

      this.logger.log(`Team member welcome email triggered for user ${userData.userId}`);
    } catch (error) {
      this.logger.error(`Failed to trigger team member welcome email: ${error.message}`, error.stack);
    }
  }

  /**
   * Trigger email when document collaboration session starts
   */
  async onCollaborationStart(userData: {
    userId: string;
    organizationId: string;
    email: string;
    firstName?: string;
    documentName?: string;
    collaborators?: string[];
    sessionType?: string;
  }): Promise<void> {
    try {
      this.logger.log(`Triggering collaboration start email for user ${userData.userId}`);

      // This could trigger a custom sequence for collaboration features
      // For now, we'll use the engagement category

      this.logger.log(`Collaboration start email triggered for user ${userData.userId}`);
    } catch (error) {
      this.logger.error(`Failed to trigger collaboration start email: ${error.message}`, error.stack);
    }
  }

  /**
   * Trigger email when workflow is completed
   */
  async onWorkflowCompleted(userData: {
    userId: string;
    organizationId: string;
    email: string;
    firstName?: string;
    workflowName?: string;
    completionTime?: number;
    tasksCompleted?: number;
  }): Promise<void> {
    try {
      this.logger.log(`Triggering workflow completion email for user ${userData.userId}`);

      // This could trigger a custom sequence for workflow achievements
      // For now, we'll log the event

      this.logger.log(`Workflow completion email triggered for user ${userData.userId}`);
    } catch (error) {
      this.logger.error(`Failed to trigger workflow completion email: ${error.message}`, error.stack);
    }
  }

  /**
   * Trigger bulk email for announcements
   */
  async triggerAnnouncement(announcementData: {
    title: string;
    content: string;
    targetTiers?: SubscriptionTier[];
    targetUsers?: string[];
    scheduledDate?: Date;
  }): Promise<void> {
    try {
      this.logger.log(`Triggering announcement email: ${announcementData.title}`);

      // This would typically create a custom template and trigger for multiple users
      // Implementation depends on your user management system

      this.logger.log(`Announcement email triggered: ${announcementData.title}`);
    } catch (error) {
      this.logger.error(`Failed to trigger announcement email: ${error.message}`, error.stack);
    }
  }
}

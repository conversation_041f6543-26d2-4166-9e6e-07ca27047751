import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { databaseConfig } from '../../config/database.config';
import { DatabaseMonitorService } from './services/database-monitor.service';
import { DatabaseMonitorController } from './controllers/database-monitor.controller';
import { DatabaseHealthController } from './controllers/database-health.controller';

@Module({
  imports: [
    ConfigModule.forFeature(databaseConfig),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('database.mongodb.uri'),
        ...configService.get('database.mongodb.options'),
      }),
    }),
  ],
  controllers: [DatabaseMonitorController, DatabaseHealthController],
  providers: [DatabaseMonitorService],
  exports: [MongooseModule, DatabaseMonitorService],
})
export class DatabaseModule {}

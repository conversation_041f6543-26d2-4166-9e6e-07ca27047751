import { Controller, Get } from '@nestjs/common';
import { DatabaseMonitorService } from '../services/database-monitor.service';

@Controller('health')
export class DatabaseHealthController {
  constructor(private readonly dbMonitorService: DatabaseMonitorService) {}

  @Get('mongodb')
  async checkMongoDb() {
    const isConnected = this.dbMonitorService.isConnected();
    const connectionInfo = this.dbMonitorService.getConnectionInfo();
    
    if (!isConnected) {
      return {
        status: 'error',
        message: 'MongoDB is not connected',
        details: connectionInfo
      };
    }
    
    try {
      const pingMs = await this.dbMonitorService.measurePing();
      return {
        status: 'ok',
        message: 'MongoDB is connected',
        details: {
          ...connectionInfo,
          pingMs
        }
      };
    } catch (error) {
      return {
        status: 'error',
        message: `MongoDB ping failed: ${error.message}`,
        details: connectionInfo
      };
    }
  }
}

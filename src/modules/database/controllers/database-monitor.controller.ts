import { Controller, Get } from '@nestjs/common';
import { DatabaseMonitorService } from '../services/database-monitor.service';

@Controller('database')
export class DatabaseMonitorController {
  constructor(private readonly databaseMonitorService: DatabaseMonitorService) {}

  @Get('status')
  async getDatabaseStatus() {
    return {
      connected: this.databaseMonitorService.isConnected(),
      connectionInfo: this.databaseMonitorService.getConnectionInfo(),
      connectionString: this.databaseMonitorService.getMaskedConnectionString(),
    };
  }

  @Get('stats')
  async getDatabaseStats() {
    return this.databaseMonitorService.getDatabaseStats();
  }

  @Get('ping')
  async getPing() {
    try {
      const ping = await this.databaseMonitorService.measurePing();
      return { ping: `${ping}ms` };
    } catch (error) {
      return { error: error.message };
    }
  }

  @Get('collections')
  async getCollections() {
    try {
      const collections = await this.databaseMonitorService.getCollectionStats();
      return { collections };
    } catch (error) {
      return { error: error.message };
    }
  }

  @Get('collections/view')
  async viewCollections() {
    try {
      const collections = await this.databaseMonitorService.getCollectionStats();
      return {
        success: true,
        count: collections.length,
        collections: collections.map(col => ({
          name: col.name,
          documentCount: col.count,
          sizeBytes: col.size,
          sizeMB: (col.size / (1024 * 1024)).toFixed(2) + ' MB'
        }))
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { InjectConnection } from '@nestjs/mongoose';
import { Connection } from 'mongoose';
import { ConfigService } from '@nestjs/config';

export interface DatabaseStats {
  status: 'connected' | 'disconnected' | 'error';
  ping?: number;
  collections?: {
    name: string;
    count: number;
    size: number;
  }[];
  databaseSize?: number;
  connectionInfo?: {
    host: string;
    port: number;
    name: string;
  };
  error?: string;
}

@Injectable()
export class DatabaseMonitorService {
  private readonly logger = new Logger(DatabaseMonitorService.name);
  
  constructor(
    @InjectConnection() private connection: Connection,
    private configService: ConfigService,
  ) {}
  
  /**
   * Check if the database connection is active
   */
  isConnected(): boolean {
    return this.connection.readyState === 1;
  }
  
  /**
   * Get the connection string with sensitive parts masked
   */
  getMaskedConnectionString(): string {
    const connectionString = this.configService.get<string>('database.mongodb.uri');
    if (!connectionString) return 'Not configured';
    
    // Mask the username and password in the connection string
    return connectionString.replace(/(mongodb:\/\/)(.*?)(@.*)/, '$1****:****$3');
  }
  
  /**
   * Get connection info (host, port, database name)
   */
  getConnectionInfo(): { host: string; port: number; name: string } {
    const connectionString = this.configService.get<string>('database.mongodb.uri');
    if (!connectionString) {
      return { host: 'unknown', port: 0, name: 'unknown' };
    }
    
    try {
      const url = new URL(connectionString);
      const pathSegments = url.pathname.split('/').filter(Boolean);
      
      return {
        host: url.hostname,
        port: parseInt(url.port, 10) || 27017,
        name: pathSegments[0] || 'legal-document-analyzer',
      };
    } catch (error) {
      this.logger.error(`Failed to parse connection string: ${error.message}`);
      return { host: 'error', port: 0, name: 'error' };
    }
  }
  
  /**
   * Measure database ping time
   */
  async measurePing(): Promise<number> {
    if (!this.isConnected()) {
      throw new Error('Database not connected');
    }
    
    const start = Date.now();
    await this.connection.db.admin().ping();
    return Date.now() - start;
  }
  
  /**
   * Get collection statistics
   */
  async getCollectionStats(): Promise<{ name: string; count: number; size: number }[]> {
    if (!this.isConnected()) {
      throw new Error('Database not connected');
    }
    
    const collections = await this.connection.db.listCollections().toArray();
    const stats = [];
    
    for (const collection of collections) {
      try {
        const count = await this.connection.db.collection(collection.name).countDocuments();
        const collStats = await this.connection.db.command({ collStats: collection.name });
        
        stats.push({
          name: collection.name,
          count,
          size: collStats.size || 0,
        });
      } catch (error) {
        this.logger.error(`Failed to get stats for collection ${collection.name}: ${error.message}`);
        stats.push({
          name: collection.name,
          count: -1,
          size: -1,
        });
      }
    }
    
    return stats;
  }
  
  /**
   * Get comprehensive database statistics
   */
  async getDatabaseStats(): Promise<DatabaseStats> {
    try {
      if (!this.isConnected()) {
        return {
          status: 'disconnected',
          connectionInfo: this.getConnectionInfo(),
        };
      }
      
      const ping = await this.measurePing();
      const collections = await this.getCollectionStats();
      
      const dbStats = await this.connection.db.command({ dbStats: 1 });
      
      return {
        status: 'connected',
        ping,
        collections,
        databaseSize: dbStats.dataSize || 0,
        connectionInfo: this.getConnectionInfo(),
      };
    } catch (error) {
      this.logger.error(`Failed to get database stats: ${error.message}`);
      return {
        status: 'error',
        error: error.message,
        connectionInfo: this.getConnectionInfo(),
      };
    }
  }
}

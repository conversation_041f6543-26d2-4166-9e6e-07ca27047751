import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document as MongoDocument } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

@Schema({ _id: false })
export class DepositionTestimonyAnalysis {
  @Prop({ required: true })
  speaker: string;

  @Prop({ required: true })
  statement: string;

  @Prop({ required: true })
  credibilityScore: number;

  @Prop({ 
    type: String, 
    enum: ['high', 'medium', 'low'],
    required: true 
  })
  confidence: string;

  @Prop({ required: true })
  reasoning: string;

  @Prop({ type: [String], default: [] })
  supportingEvidence: string[];
}

@Schema({ _id: false })
export class CrossExaminationSuggestion {
  @Prop({ required: true })
  topic: string;

  @Prop({ required: true })
  question: string;

  @Prop({ required: true })
  purpose: string;

  @Prop()
  legalBasis?: string;

  @Prop({ type: [String], default: [] })
  suggestedFollowUps?: string[];
}

@Schema({ _id: false })
export class DepositionInconsistency {
  @Prop({ required: true })
  statement1: string;

  @Prop({ required: true })
  statement2: string;

  @Prop({ required: true })
  explanation: string;

  @Prop({ 
    type: String, 
    enum: ['high', 'medium', 'low'],
    required: true 
  })
  severity: string;
}

@Schema({ _id: false })
export class DepositionAnalysisMetadata {
  @Prop({ required: true })
  analyzedAt: string;

  @Prop({ required: true })
  analysisDurationMs: number;

  @Prop({ required: true })
  modelUsed: string;

  @Prop({ 
    type: String, 
    enum: ['high', 'medium', 'low'],
    default: 'medium'
  })
  confidence?: string;
}

@Schema({ timestamps: true, collection: 'deposition_analyses' })
export class DepositionAnalysisModel {
  @Prop({ required: true, default: () => uuidv4() })
  id: string;

  @Prop({ required: true }) 
  organizationId: string;

  @Prop({ required: true })
  userId: string;

  @Prop()
  depositionId?: string;

  @Prop({ required: true })
  transcript: string;

  @Prop()
  caseContext?: string;

  @Prop({ type: [String], default: [] })
  focusAreas?: string[];

  @Prop({ required: true })
  overallCredibilityScore: number;

  @Prop({ type: [DepositionTestimonyAnalysis], default: [] })
  keyTestimonyAnalysis: DepositionTestimonyAnalysis[];

  @Prop({ type: [DepositionInconsistency], default: [] })
  inconsistencies: DepositionInconsistency[];

  @Prop({ type: [CrossExaminationSuggestion], default: [] })
  crossExaminationSuggestions: CrossExaminationSuggestion[];

  @Prop({ type: Object, required: true })
  metadata: DepositionAnalysisMetadata;

  @Prop({ type: [String], default: [] })
  keyFindings: string[];

  @Prop({ required: true, default: Date.now })
  createdAt: Date;

  @Prop({ required: true, default: Date.now })
  updatedAt: Date;
}

export type DepositionAnalysisDocument = DepositionAnalysisModel & MongoDocument;
export const DEPOSITION_ANALYSIS_MODEL = 'DepositionAnalysis';
export const DepositionAnalysisSchema = SchemaFactory.createForClass(DepositionAnalysisModel);

// Add indexes
DepositionAnalysisSchema.index({ id: 1 }, { unique: true });
DepositionAnalysisSchema.index({ organizationId: 1 });
DepositionAnalysisSchema.index({ userId: 1 });
DepositionAnalysisSchema.index({ depositionId: 1 });
DepositionAnalysisSchema.index({ createdAt: 1 });

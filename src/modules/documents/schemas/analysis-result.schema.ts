import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from 'mongoose';
import { DocumentModel } from './document.schema';
import { Organization } from '../../auth/schemas/organization.schema';

@Schema({ timestamps: true })
export class AnalysisResult {
  @Prop({ type: String, required: true, index: false })
  documentId: string;

  @Prop({ type: String, ref: 'Organization', required: true, index: false })
  organizationId: string;

  @Prop({ type: MongooseSchema.Types.Mixed, required: true })
  analysisContent: Record<string, any>;

  @Prop({ type: MongooseSchema.Types.Mixed, required: false })
  aiMetadata?: Record<string, any>;

  @Prop({ type: MongooseSchema.Types.Mixed, required: false })
  requestDetails?: Record<string, any>;

  @Prop({ required: true })
  aiProvider: string;

  @Prop({ required: false })
  modelUsed?: string;

  @Prop({ type: MongooseSchema.Types.Mixed, required: false })
  analysisParameters?: Record<string, any>;

  @Prop({ type: String, required: false, index: false })
  query?: string;

  @Prop({ required: false })
  documentType?: string;

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({ default: false })
  isMultiDocumentAnalysis: boolean;

  @Prop({ type: [{ type: MongooseSchema.Types.ObjectId, ref: 'DocumentModel' }], default: [] })
  relatedDocuments?: Types.ObjectId[]; // Note: This still uses ObjectId

  @Prop({ required: false })
  prompt?: string;

  @Prop({ required: false })
  processingTimeMs?: number;

  @Prop({ type: MongooseSchema.Types.Mixed, required: false })
  rateLimitInfo?: {
    utilizationPercentage: number;
    remainingTokens: number;
    maxTokens: number;
  };

}

export type AnalysisResultDocument = AnalysisResult & Document;
export const AnalysisResultSchema = SchemaFactory.createForClass(AnalysisResult);

// Remove any implicit indexes and define only what we need
// Drop existing indexes first
AnalysisResultSchema.index({ documentId: 1, query: 1 }, { sparse: true }); // Allow multiple null queries per document
AnalysisResultSchema.index({ organizationId: 1, createdAt: -1 }); // For organization-based queries

// Add validation
AnalysisResultSchema.path('processingTimeMs').validate(function(time) {
  if (time === null || time === undefined) return true; // Allow null/undefined
  return typeof time === 'number' && time >= 0;
}, 'Processing time must be a non-negative number');

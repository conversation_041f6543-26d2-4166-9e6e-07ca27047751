import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document as MongoDocument } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

import { DocumentType } from '../../../common/enums/document-type.enum';

export enum ContractType {
  GENERAL_CONTRACT = DocumentType.CONTRACT,
  PURCHASE_AGREEMENT = DocumentType.PURCHASE_AGREEMENT,
  LEASE_AGREEMENT = DocumentType.LEASE_AGREEMENT,
  EMPLOYMENT_CONTRACT = DocumentType.EMPLOYMENT_CONTRACT,
  LICENSING_AGREEMENT = DocumentType.LICENSING_AGREEMENT,
  SERVICE_AGREEMENT = DocumentType.SERVICE_AGREEMENT,
  DISTRIBUTION_AGREEMENT = DocumentType.DISTRIBUTION_AGREEMENT,
  SETTLEMENT_AGREEMENT = DocumentType.SETTLEMENT_AGREEMENT,
  LOAN_AGREEMENT = DocumentType.LOAN_AGREEMENT,
  NDA = DocumentType.NDA
}

export enum RuleType {
  REQUIRED_CLAUSE = 'required_clause',
  PROHIBITED_CLAUSE = 'prohibited_clause',
  LANGUAGE_STANDARD = 'language_standard',
  TERM_LIMIT = 'term_limit',
  LIABILITY_CAP = 'liability_cap',
  INDEMNIFICATION = 'indemnification',
  TERMINATION_RIGHTS = 'termination_rights',
  INTELLECTUAL_PROPERTY = 'intellectual_property',
  DATA_PROTECTION = 'data_protection',
  GOVERNING_LAW = 'governing_law'
}

export enum RuleSeverity {
  CRITICAL = 'CRITICAL',
  HIGH = 'HIGH',
  MEDIUM = 'MEDIUM',
  LOW = 'LOW'
}

export enum DeviationType {
  MISSING_CLAUSE = 'missing_clause',
  PROHIBITED_LANGUAGE = 'prohibited_language',
  NON_STANDARD_TERMS = 'non_standard_terms',
  RISK_THRESHOLD_EXCEEDED = 'risk_threshold_exceeded',
  COMPLIANCE_VIOLATION = 'compliance_violation'
}

export enum RecommendationType {
  ACCEPT_AS_IS = 'accept',
  NEGOTIATE_CHANGE = 'negotiate',
  REQUIRE_APPROVAL = 'approval',
  REJECT_CLAUSE = 'reject',
  ADD_CLAUSE = 'add',
  MODIFY_LANGUAGE = 'modify',
  SEEK_LEGAL_REVIEW = 'legal_review',
  ESCALATE_TO_MANAGEMENT = 'escalate'
}

// Sub-schemas for complex nested objects
@Schema({ _id: false })
export class RuleCriteria {
  @Prop({ type: [String], default: [] })
  keywords: string[];

  @Prop({ type: [String], default: [] })
  patterns: string[];

  @Prop({ type: [String], default: [] })
  semanticConcepts: string[];

  @Prop({ type: [String], default: [] })
  contextRequirements: string[];
}

@Schema({ _id: false })
export class AcceptableLanguage {
  @Prop({ type: [String], default: [] })
  preferred: string[];

  @Prop({ type: [String], default: [] })
  acceptable: string[];

  @Prop({ type: [String], default: [] })
  fallbackPositions: string[];
}

@Schema({ _id: false })
export class UnacceptableTerms {
  @Prop({ type: [String], default: [] })
  prohibited: string[];

  @Prop({ type: [String], default: [] })
  requiresEscalation: string[];

  @Prop({ type: [String], default: [] })
  autoReject: string[];
}

@Schema({ _id: false })
export class NegotiationGuidance {
  @Prop({ type: String })
  strategy: string;

  @Prop({ type: [String], default: [] })
  alternatives: string[];

  @Prop({ type: String })
  businessImpact: string;
}

// Playbook Rule Schema
@Schema({ _id: false })
export class PlaybookRule {
  @Prop({ required: true, default: () => uuidv4() })
  id: string;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  category: string;

  @Prop({ required: true, enum: Object.values(RuleType) })
  ruleType: RuleType;

  @Prop({ required: true, enum: Object.values(RuleSeverity) })
  severity: RuleSeverity;

  @Prop({ type: RuleCriteria, required: true })
  criteria: RuleCriteria;

  @Prop({ type: AcceptableLanguage, required: true })
  acceptableLanguage: AcceptableLanguage;

  @Prop({ type: UnacceptableTerms, required: true })
  unacceptableTerms: UnacceptableTerms;

  @Prop({ type: NegotiationGuidance })
  negotiationGuidance?: NegotiationGuidance;

  @Prop({ type: String })
  description?: string;

  @Prop({ type: Boolean, default: true })
  isActive: boolean;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

// Main Contract Playbook Schema
@Schema({ timestamps: true, collection: 'contract_playbooks' })
export class ContractPlaybook {
  @Prop({ required: true, default: () => uuidv4() })
  id: string;

  @Prop({ required: true })
  organizationId: string;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true, enum: Object.values(ContractType) })
  contractType: ContractType;

  @Prop({ type: String })
  description?: string;

  @Prop({ type: String, required: true, default: '1.0.0' })
  version: string;

  @Prop({ type: [PlaybookRule], default: [] })
  rules: PlaybookRule[];

  @Prop({ type: Object, default: {} })
  metadata: {
    industry?: string;
    jurisdiction?: string;
    riskProfile?: string;
    author?: string;
    approvedBy?: string;
    lastReviewDate?: Date;
    nextReviewDate?: Date;
    tags?: string[];
    [key: string]: any;
  };

  @Prop({ type: Boolean, default: true })
  isActive: boolean;

  @Prop({ type: Boolean, default: false })
  isTemplate: boolean;

  @Prop({ type: String })
  createdBy: string;

  @Prop({ type: String })
  updatedBy?: string;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

export type ContractPlaybookDocument = ContractPlaybook & MongoDocument;
export const ContractPlaybookSchema = SchemaFactory.createForClass(ContractPlaybook);
export const CONTRACT_PLAYBOOK_MODEL = 'ContractPlaybook';

// Add indexes for performance
ContractPlaybookSchema.index({ id: 1 }, { unique: true });
ContractPlaybookSchema.index({ organizationId: 1 });
ContractPlaybookSchema.index({ contractType: 1 });
ContractPlaybookSchema.index({ 'metadata.tags': 1 });
ContractPlaybookSchema.index({ organizationId: 1, contractType: 1 });
ContractPlaybookSchema.index({ organizationId: 1, isActive: 1 });
ContractPlaybookSchema.index({ createdAt: -1 });
ContractPlaybookSchema.index({ 'metadata.tags': 1 });

// Pre-save middleware
ContractPlaybookSchema.pre('save', function (next) {
  if (!this.id) {
    this.id = uuidv4();
  }
  this.updatedAt = new Date();
  next();
});

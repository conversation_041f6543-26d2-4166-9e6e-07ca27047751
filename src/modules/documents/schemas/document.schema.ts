import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document as MongoDocument } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

@Schema({ timestamps: true, collection: 'documents' })
export class DocumentModel {
  @Prop({ required: true, default: () => uuidv4() })
  id: string;

  @Prop({ required: true }) 
  organizationId: string;

  @Prop({ required: true })
  filename: string;

  @Prop({ required: true })
  originalName: string;

  @Prop()
  mimeType?: string;

  @Prop()
  content?: string;

  @Prop({ required: true })
  size: number;

  @Prop({ required: true, default: Date.now })
  uploadDate: Date;

  @Prop({
    type: String,
    enum: ['uploaded', 'processing', 'completed', 'failed', 'analyzed'],
    default: 'uploaded',
  })
  status: string;

  @Prop({ type: String, default: 'unknown' })
  documentType: string;

  @Prop({ type: [String], default: [] })
  parties: string[];

  @Prop({ type: Date })
  effectiveDate?: Date;

  @Prop({ type: Date })
  expirationDate?: Date;

  @Prop({ type: Object, default: {} })
  metadata: {
    sections?: Array<{
      title: string;
      content: string;
      purpose?: string;
    }>;
    clauses?: Array<{
      title: string;
      content: string;
      type?: string;
      riskLevel?: string;
    }>;
    patterns?: any[];
    status?: string;
    lastUpdated?: Date;
    error?: string;
    [key: string]: any;
  };

  @Prop({ type: Object })
  processingError?: {
    message: string;
    stack?: string;
    timestamp: Date;
  };

  @Prop({ type: Boolean, default: false })
  analyzed?: boolean;

  @Prop({ type: Date })
  analysisStartTime?: Date;

  @Prop({ type: Date })
  analysisEndTime?: Date;

  @Prop({ type: Date })
  comparedAt?: Date;

  @Prop({ type: Array, default: [] })
  comparisonHistory?: Array<{
    comparedWith: string;
    comparedAt: Date;
    comparedBy: string;
    comparisonLevel?: string; // 'basic' or 'enhanced'
    comparisonType?: string; // 'similarities', 'differences', or 'both'
    metadata?: any; // Additional comparison metadata
  }>;

  @Prop({ type: Array, default: [] })
  enhancedComparisonHistory?: Array<{
    comparedWith: string;
    comparedAt: Date;
    comparedBy: string;
    comparisonType?: string; // 'similarities', 'differences', or 'both'
    metadata?: any; // Additional comparison metadata
  }>;

  @Prop({ type: Number, default: 1, index: true }) // Track the latest version number
  currentVersion: number;

}

export const DOCUMENT_MODEL = 'Document';

export type Document = DocumentModel & MongoDocument;

export const DocumentSchema = SchemaFactory.createForClass(DocumentModel);

// Add indexes
DocumentSchema.index({ id: 1 }, { unique: true });
DocumentSchema.index({ organizationId: 1 });
DocumentSchema.index({ filename: 1 });
DocumentSchema.index({ uploadDate: -1 });
DocumentSchema.index({ status: 1 });
DocumentSchema.index({ organizationId: 1, status: 1 }); // Compound index for filtered queries
DocumentSchema.index({ documentType: 1 });
DocumentSchema.index({ parties: 1 });
DocumentSchema.index({ effectiveDate: 1 });

// Add pre-save middleware to ensure id exists
DocumentSchema.pre('save', function (next) {
  if (!this.id) {
    this.id = uuidv4();
  }
  next();
});

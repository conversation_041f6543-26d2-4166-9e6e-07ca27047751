import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type NegotiationScenarioDocument = NegotiationScenario & Document;
export type NegotiationSessionDocument = NegotiationSession & Document;
export type NegotiationTemplateDocument = NegotiationTemplate & Document;

@Schema({ collection: 'negotiation_scenarios' })
export class NegotiationScenario {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true })
  industry: string;

  @Prop({ required: true })
  contractType: string;

  @Prop({ required: true, enum: ['beginner', 'intermediate', 'expert'] })
  difficulty: string;

  @Prop({ required: true, type: Array })
  parties: Array<{
    id: string;
    name: string;
    role: string;
    priorities: string[];
    negotiationStyle: string;
    constraints: Record<string, any>;
    budget?: {
      min: number;
      max: number;
      currency: string;
    };
    timeline?: {
      urgency: string;
      deadline?: Date;
    };
  }>;

  @Prop({ required: true, type: Object })
  initialOffer: {
    price?: number;
    currency?: string;
    paymentTerms?: string;
    deliveryDate?: Date;
    warranties?: string[];
    liabilities?: string[];
    terminationClauses?: string[];
    intellectualProperty?: string[];
    confidentiality?: string[];
    customTerms?: Record<string, any>;
  };

  @Prop({ required: true, type: Object })
  constraints: {
    maxRounds: number;
    timeLimit?: number;
    mustHaveTerms: string[];
    dealBreakers: string[];
    flexibleTerms: string[];
  };

  @Prop({ required: true, type: Object })
  timeline: {
    startDate: Date;
    expectedDuration: number;
    maxDuration: number;
    breakDuration?: number;
  };

  @Prop({ required: true, enum: ['active', 'completed', 'paused'] })
  status: 'active' | 'completed' | 'paused';

  @Prop({ required: true })
  organizationId: string;

  @Prop({ required: true })
  createdBy: string;

  @Prop({ default: false })
  isTemplate: boolean;

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({ default: 0 })
  usageCount: number;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

@Schema({ collection: 'negotiation_sessions' })
export class NegotiationSession {
  @Prop({ required: true })
  scenarioId: string;

  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  organizationId: string;

  @Prop({ required: true, enum: ['active', 'completed', 'paused', 'abandoned'] })
  status: 'active' | 'completed' | 'paused' | 'abandoned';

  @Prop({ required: true })
  startTime: Date;

  @Prop()
  endTime?: Date;

  @Prop({ type: Array, default: [] })
  rounds: Array<{
    id: string;
    roundNumber: number;
    timestamp: Date;
    party: string;
    offer: Record<string, any>;
    message: string;
    reasoning?: string;
    confidence: number;
    strategy: string;
    responseTime: number;
  }>;

  @Prop({ default: 0 })
  currentRound: number;

  @Prop({ type: Object })
  finalAgreement?: Record<string, any>;

  @Prop({ required: true, type: Object })
  metrics: {
    totalRounds: number;
    totalTime: number;
    agreementReached: boolean;
    userSatisfaction?: number;
    dealValue: number;
    concessionsMade: {
      byUser: number;
      byAI: number;
    };
    strategicEffectiveness: number;
    communicationQuality: number;
    timeEfficiency: number;
    overallScore: number;
  };

  @Prop({ required: true, type: Object })
  aiPersonality: {
    aggressiveness: number;
    flexibility: number;
    riskTolerance: number;
    communicationStyle: string;
    decisionSpeed: string;
    concessionPattern: string;
  };

  @Prop({ type: Object })
  options?: {
    enableHints: boolean;
    showAIReasoning: boolean;
    allowPausing: boolean;
    recordSession: boolean;
    difficultyAdjustment: boolean;
    realTimeAnalysis: boolean;
  };

  // GAMIFICATION FIELDS
  @Prop({ type: String, default: null })
  characterId?: string; // Link to character

  @Prop({ type: Object, default: {} })
  gamificationData?: {
    xpEarned: number;
    achievementsUnlocked: string[];
    pressureEventsTriggered: string[];
    relationshipChanges: Record<string, number>;
    levelUps: number;
  };

  @Prop({ type: Object, default: {} })
  gameState?: {
    userStress: number;
    aiMood: string;
    activePressureEvents: string[];
    timeRemaining?: number;
    currentScore: number;
    dealMomentum: string;
  };

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

@Schema({ collection: 'negotiation_templates' })
export class NegotiationTemplate {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true })
  industry: string;

  @Prop({ required: true })
  contractType: string;

  @Prop({ required: true, enum: ['beginner', 'intermediate', 'expert'] })
  difficulty: string;

  @Prop({ required: true })
  estimatedDuration: number;

  @Prop({ type: [String], required: true })
  learningObjectives: string[];

  @Prop({ required: true, type: Object })
  scenario: Record<string, any>;

  @Prop({ default: false })
  isPublic: boolean;

  @Prop({ required: true })
  createdBy: string;

  @Prop({ required: true })
  organizationId: string;

  @Prop({ default: 0 })
  usageCount: number;

  @Prop({ default: 0 })
  averageRating: number;

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

export const NegotiationScenarioSchema = SchemaFactory.createForClass(NegotiationScenario);
export const NegotiationSessionSchema = SchemaFactory.createForClass(NegotiationSession);
export const NegotiationTemplateSchema = SchemaFactory.createForClass(NegotiationTemplate);

// Add indexes for better performance
NegotiationScenarioSchema.index({ organizationId: 1, createdAt: -1 });
NegotiationScenarioSchema.index({ industry: 1, contractType: 1 });
NegotiationScenarioSchema.index({ difficulty: 1 });
NegotiationScenarioSchema.index({ tags: 1 });

NegotiationSessionSchema.index({ userId: 1, createdAt: -1 });
NegotiationSessionSchema.index({ organizationId: 1, status: 1 });
NegotiationSessionSchema.index({ scenarioId: 1 });
NegotiationSessionSchema.index({ status: 1, startTime: -1 });
// Gamification indexes
NegotiationSessionSchema.index({ characterId: 1 });
NegotiationSessionSchema.index({ userId: 1, characterId: 1 });
NegotiationSessionSchema.index({ 'metrics.overallScore': -1 });
NegotiationSessionSchema.index({ 'gamificationData.xpEarned': -1 });

NegotiationTemplateSchema.index({ organizationId: 1, isPublic: 1 });
NegotiationTemplateSchema.index({ industry: 1, contractType: 1 });
NegotiationTemplateSchema.index({ difficulty: 1 });
NegotiationTemplateSchema.index({ tags: 1 });
NegotiationTemplateSchema.index({ usageCount: -1 });

export const NEGOTIATION_SCENARIO_MODEL = 'NegotiationScenario';
export const NEGOTIATION_SESSION_MODEL = 'NegotiationSession';
export const NEGOTIATION_TEMPLATE_MODEL = 'NegotiationTemplate';

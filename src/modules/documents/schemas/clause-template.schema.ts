import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from 'mongoose';

export type ClauseTemplateDocument = ClauseTemplate & Document;

export const CLAUSE_TEMPLATE_MODEL = 'ClauseTemplate';

@Schema({ timestamps: true })
export class ClauseTemplate {
  // Don't use @Prop for _id, it's automatically handled by Mongoose
  id?: string; // Virtual getter for _id as string

  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  content: string;

  @Prop({ required: true })
  category: string;

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({ type: MongooseSchema.Types.Mixed, default: {} })
  metadata: Record<string, any>;

  @Prop({ required: true })
  organizationId: string;

  @Prop()
  createdBy: string;

  @Prop({ default: false })
  isPublic: boolean;

  @Prop({ type: Number, default: 0 })
  usageCount: number;

  @Prop({ type: Number, default: 0 })
  effectivenessScore: number;
}

export const ClauseTemplateSchema = SchemaFactory.createForClass(ClauseTemplate);

// Add virtual for id
ClauseTemplateSchema.virtual('id').get(function() {
  return this._id.toHexString();
});

// Ensure virtuals are included in JSON
ClauseTemplateSchema.set('toJSON', {
  virtuals: true,
  transform: (_, converted) => {
    delete converted._id;
    return converted;
  }
});

// Indexes for performance
ClauseTemplateSchema.index({ organizationId: 1 });
ClauseTemplateSchema.index({ tags: 1 });
ClauseTemplateSchema.index({ category: 1 });
ClauseTemplateSchema.index({ 
  name: 'text', 
  content: 'text',
  tags: 'text'
}, {
  weights: {
    name: 10,
    content: 5,
    tags: 3
  }
});

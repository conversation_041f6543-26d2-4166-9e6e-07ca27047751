import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { NegotiationStrategy, SimulationScenario } from '../interfaces/negotiation-playbook.interface';
import { DocumentType } from '../../../common/enums/document-type.enum';

@Schema()
class SimulationScenarioSchema implements SimulationScenario {
  @Prop({ required: true })
  type: 'concession' | 'dealbreaker' | 'leverage' | 'compromise';

  @Prop({ required: true })
  trigger: string;

  @Prop({ required: true })
  responseStrategy: string;

  @Prop()
  expectedOutcome?: string;
}

@Schema()
class NegotiationStrategySchema implements NegotiationStrategy {
  @Prop({ required: true })
  section: string;

  @Prop({ type: [String], required: true })
  recommendations: string[];

  @Prop({ type: [SimulationScenarioSchema] })
  simulationScenarios?: SimulationScenario[];

  @Prop({ enum: ['low', 'medium', 'high'] })
  riskLevel?: 'low' | 'medium' | 'high';

  @Prop({ type: Number })
  priority?: number;

  @Prop()
  alternativeLanguage?: string;
}

@Schema({ timestamps: true })
export class NegotiationPlaybookSchema {
  @Prop({ required: true })
  documentId: string;

  @Prop({ type: [NegotiationStrategySchema], required: true })
  strategies: NegotiationStrategy[];

  @Prop({ required: true })
  overallAssessment: string;

  @Prop({ type: [String], required: true })
  keyLeveragePoints: string[];

  @Prop({ type: [String] })
  dealBreakers?: string[];

  @Prop({ required: true })
  timestamp: Date;

  @Prop()
  organizationId?: string;

  @Prop()
  userId?: string;

  // Template/Sample fields
  @Prop({ default: false })
  isTemplate: boolean;

  @Prop()
  templateName?: string;

  @Prop()
  templateDescription?: string;

  @Prop({ enum: Object.values(DocumentType) })
  contractType?: DocumentType;

  @Prop({ enum: ['technology', 'healthcare', 'finance', 'real_estate', 'consulting', 'manufacturing', 'retail', 'general'] })
  industry?: string;

  @Prop({ enum: ['beginner', 'intermediate', 'expert'] })
  difficulty?: string;

  @Prop({ type: [String] })
  tags?: string[];

  @Prop({ default: 0 })
  usageCount?: number;
}

export type NegotiationPlaybookDocument = NegotiationPlaybookSchema & Document;
export const NegotiationPlaybookSchemaFactory = SchemaFactory.createForClass(NegotiationPlaybookSchema);
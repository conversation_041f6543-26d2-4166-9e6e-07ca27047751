import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class LegalPattern {
  @Prop({ required: true })
  id: string;

  @Prop({ required: true })
  name: string;

  @Prop()
  description?: string;

  @Prop({ required: true })
  pattern: string;

  @Prop({ type: String })
  category: string;

  @Prop({
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  })
  severity: 'low' | 'medium' | 'high';

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({ type: Object })
  metadata: {
    created?: Date;
    lastModified?: Date;
    author?: string;
    version?: string;
    isActive?: boolean;
    matchCount?: number;
    [key: string]: any;
  };

  @Prop({ type: Object })
  validation?: {
    isValid: boolean;
    errors?: string[];
    lastValidated?: Date;
  };

  @Prop({ type: [String], default: [] })
  relatedPatterns: string[];
}

export type LegalPatternDocument = LegalPattern & Document;
export const LegalPatternSchema = SchemaFactory.createForClass(LegalPattern);

// Add indexes
LegalPatternSchema.index({ name: 1 }, { unique: true });
LegalPatternSchema.index({ category: 1 });
LegalPatternSchema.index({ tags: 1 });
LegalPatternSchema.index({ 'metadata.isActive': 1 });
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document as MongoDocument } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

@Schema({ timestamps: true, collection: 'document_versions' }) // Use a specific collection name
export class DocumentVersionModel {
  @Prop({ required: true, default: () => uuidv4() })
  id: string; // Unique ID for the version entry itself

  @Prop({ required: true, index: true })
  documentId: string; // Foreign key to the main DocumentModel.id

  @Prop({ required: true, index: true })
  version: number; // Version number (e.g., 1, 2, 3...)

  @Prop({ required: true })
  content: string; // Snapshot of the document content at this version

  @Prop({ type: Object, required: true })
  metadata: Record<string, any>; // Snapshot of the document metadata at this version

  @Prop({ required: true, default: Date.now })
  createdAt: Date; // Timestamp when this version was created

  @Prop({ type: String }) // Optional: Track who made the change
  createdBy?: string; // User ID

  @Prop({ type: String }) // Optional: Notes about the change
  changeNotes?: string;
}

export const DOCUMENT_VERSION_MODEL = 'DocumentVersion';

export type DocumentVersion = DocumentVersionModel & MongoDocument;

export const DocumentVersionSchema = SchemaFactory.createForClass(DocumentVersionModel);

// Add indexes for common queries
DocumentVersionSchema.index({ documentId: 1, version: -1 }); // Get latest versions quickly
DocumentVersionSchema.index({ documentId: 1, createdAt: -1 });

// Ensure unique combination of documentId and version
DocumentVersionSchema.index({ documentId: 1, version: 1 }, { unique: true });

// Pre-save hook to ensure 'id' exists (though default handles it)
DocumentVersionSchema.pre('save', function (next) {
  if (!this.id) {
    this.id = uuidv4();
  }
  next();
});
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type ComplianceAuditResultDocument = ComplianceAuditResult & Document;
export type ComplianceProfileDocument = ComplianceProfile & Document;
export type RegulatoryFrameworkDocument = RegulatoryFramework & Document;
export type ComplianceReportDocument = ComplianceReport & Document;

@Schema({ collection: 'compliance_audit_results' })
export class ComplianceAuditResult {
  @Prop()
  id?: string;
  @Prop({ required: true })
  documentId: string;

  @Prop({ required: true })
  documentTitle: string;

  @Prop({ required: true })
  auditDate: Date;

  @Prop({ type: [String], required: true })
  regulations: string[];

  @Prop({ required: true, min: 0, max: 1 })
  overallScore: number;

  @Prop({ required: true, enum: ['compliant', 'non-compliant', 'partial', 'needs-review'] })
  status: 'pass' | 'fail' | 'warning' | 'not-applicable';

  @Prop({ required: true, enum: ['low', 'medium', 'high', 'critical'] })
  riskLevel: 'low' | 'medium' | 'high' | 'critical';

  @Prop({ type: Array, required: true })
  findings: Array<{
    id: string;
    regulation: string;
    ruleId: string;
    ruleName: string;
    description: string;
    severity: 'info' | 'warning' | 'error' | 'critical';
    status: 'pass' | 'fail' | 'warning' | 'not-applicable';
    confidence: number;
    location: {
      page?: number;
      section?: string;
      startPosition?: number;
      endPosition?: number;
      context?: string;
    };
    evidence: string[];
    impact: string;
    remediation: string;
    category: string;
    tags: string[];
  }>;

  @Prop({ type: Array, required: true })
  recommendations: Array<{
    id: string;
    priority: string;
    category: string;
    title: string;
    description: string;
    action: string;
    effort: string;
    timeline: string;
    resources: string[];
    relatedFindings: string[];
    costEstimate?: {
      min: number;
      max: number;
      currency: string;
    };
  }>;

  @Prop({ required: true, type: Object })
  summary: {
    totalRules: number;
    passedRules: number;
    failedRules: number;
    warningRules: number;
    notApplicableRules: number;
    criticalIssues: number;
    highPriorityIssues: number;
    mediumPriorityIssues: number;
    lowPriorityIssues: number;
    estimatedRemediationTime: string;
    complianceGaps: string[];
    strengths: string[];
  };

  @Prop({ required: true })
  organizationId: string;

  @Prop({ required: true })
  auditedBy: string;

  @Prop({ required: true })
  processingTime: number;

  @Prop({ type: Object })
  auditOptions?: {
    includeAIAnalysis: boolean;
    detailLevel: string;
    includeRecommendations: boolean;
    generateReport: boolean;
    riskAssessment: boolean;
    benchmarking: boolean;
  };

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

@Schema({ collection: 'compliance_profiles' })
export class ComplianceProfile {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  description: string;

  @Prop({ type: [String], required: true })
  regulations: string[];

  @Prop({ type: Array, default: [] })
  customRules: Array<{
    id: string;
    ruleNumber: string;
    title: string;
    description: string;
    category: string;
    severity: 'info' | 'warning' | 'error' | 'critical';
    applicability: {
      documentTypes: string[];
      industries: string[];
      jurisdictions: string[];
      organizationSizes: string[];
      conditions: string[];
    };
    patterns: Array<{
      type: string;
      pattern: string;
      weight: number;
      context?: string;
      negativePatterns?: string[];
      requiredContext?: string[];
    }>;
    aiPrompt?: string;
    manualReview: boolean;
    exemptions: string[];
    references: string[];
  }>;

  @Prop({ required: true, enum: ['low', 'medium', 'high'] })
  riskTolerance: string;

  @Prop({ required: true })
  industry: string;

  @Prop({ required: true })
  jurisdiction: string;

  @Prop({ required: true, enum: ['small', 'medium', 'large', 'enterprise'] })
  organizationSize: string;

  @Prop({ type: [String], default: [] })
  specialRequirements: string[];

  @Prop({ type: [String], default: [] })
  exemptions: string[];

  @Prop({ default: false })
  isDefault: boolean;

  @Prop({ required: true })
  organizationId: string;

  @Prop({ required: true })
  createdBy: string;

  @Prop({ default: 0 })
  usageCount: number;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

@Schema({ collection: 'regulatory_frameworks' })
export class RegulatoryFramework {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  version: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true })
  jurisdiction: string;

  @Prop({ type: [String], required: true })
  industry: string[];

  @Prop({ type: [String], required: true })
  applicableDocumentTypes: string[];

  @Prop({ type: Array, required: true })
  rules: Array<{
    id: string;
    ruleNumber: string;
    title: string;
    description: string;
    category: string;
    severity: 'info' | 'warning' | 'error' | 'critical';
    applicability: {
      documentTypes: string[];
      industries: string[];
      jurisdictions: string[];
      organizationSizes: string[];
      conditions: string[];
    };
    patterns: Array<{
      type: string;
      pattern: string;
      weight: number;
      context?: string;
      negativePatterns?: string[];
      requiredContext?: string[];
    }>;
    aiPrompt?: string;
    manualReview: boolean;
    exemptions: string[];
    references: string[];
    lastUpdated: Date;
  }>;

  @Prop({ required: true })
  lastUpdated: Date;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ required: true })
  source: string;

  @Prop()
  officialUrl?: string;

  @Prop({ default: Date.now })
  createdAt: Date;
}

@Schema({ collection: 'compliance_reports' })
export class ComplianceReport {
  @Prop({ required: true })
  auditResultId: string;

  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  generatedAt: Date;

  @Prop({ required: true, enum: ['pdf', 'html', 'json', 'excel'] })
  format: string;

  @Prop({ type: Array, required: true })
  sections: Array<{
    id: string;
    title: string;
    content: string;
    charts?: Array<{
      type: string;
      title: string;
      data: any[];
      labels: string[];
      colors?: string[];
    }>;
    tables?: Array<{
      title: string;
      headers: string[];
      rows: any[][];
      sortable: boolean;
      filterable: boolean;
    }>;
    order: number;
  }>;

  @Prop({ required: true })
  executiveSummary: string;

  @Prop({ required: true })
  methodology: string;

  @Prop({ type: [String], default: [] })
  limitations: string[];

  @Prop({ type: [String], default: [] })
  nextSteps: string[];

  @Prop({ type: Array, default: [] })
  appendices: Array<{
    id: string;
    title: string;
    content: string;
    type: string;
  }>;

  @Prop({ required: true })
  organizationId: string;

  @Prop({ required: true })
  generatedBy: string;

  @Prop()
  filePath?: string;

  @Prop({ default: Date.now })
  createdAt: Date;
}

export const ComplianceAuditResultSchema = SchemaFactory.createForClass(ComplianceAuditResult);
export const ComplianceProfileSchema = SchemaFactory.createForClass(ComplianceProfile);
export const RegulatoryFrameworkSchema = SchemaFactory.createForClass(RegulatoryFramework);
export const ComplianceReportSchema = SchemaFactory.createForClass(ComplianceReport);

// Add indexes for better performance
ComplianceAuditResultSchema.index({ organizationId: 1, auditDate: -1 });
ComplianceAuditResultSchema.index({ documentId: 1 });
ComplianceAuditResultSchema.index({ status: 1, riskLevel: 1 });
ComplianceAuditResultSchema.index({ regulations: 1 });

ComplianceProfileSchema.index({ organizationId: 1, isDefault: 1 });
ComplianceProfileSchema.index({ industry: 1, jurisdiction: 1 });
ComplianceProfileSchema.index({ regulations: 1 });

RegulatoryFrameworkSchema.index({ jurisdiction: 1, industry: 1 });
RegulatoryFrameworkSchema.index({ isActive: 1 });
RegulatoryFrameworkSchema.index({ name: 1, version: 1 });

ComplianceReportSchema.index({ organizationId: 1, generatedAt: -1 });
ComplianceReportSchema.index({ auditResultId: 1 });

export const COMPLIANCE_AUDIT_RESULT_MODEL = 'ComplianceAuditResult';
export const COMPLIANCE_PROFILE_MODEL = 'ComplianceProfile';
export const REGULATORY_FRAMEWORK_MODEL = 'RegulatoryFramework';
export const COMPLIANCE_REPORT_MODEL = 'ComplianceReport';

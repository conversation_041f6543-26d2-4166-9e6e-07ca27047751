import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document as MongoDocument } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { DepositionPreparationStatus, DepositionQuestionCategory } from '../interfaces/deposition.interface';

@Schema({ _id: false })
export class DepositionQuestion {
  @Prop({ required: true, default: () => uuidv4() })
  id: string;

  @Prop({ required: true })
  text: string;

  @Prop({ 
    type: String, 
    enum: Object.values(DepositionQuestionCategory),
    required: true 
  })
  category: string;

  @Prop({ required: true })
  purpose: string;

  @Prop()
  targetWitness?: string;

  @Prop({ type: [String], default: [] })
  suggestedFollowUps?: string[];

  @Prop({ type: [String], default: [] })
  relatedDocuments?: string[];

  @Prop({ 
    type: String, 
    enum: ['high', 'medium', 'low'],
    required: true 
  })
  priority: string;

  @Prop()
  notes?: string;

  @Prop({ required: true, default: Date.now })
  createdAt: Date;

  @Prop({ required: true, default: Date.now })
  updatedAt: Date;
}

@Schema({ timestamps: true, collection: 'deposition_preparations' })
export class DepositionPreparationModel {
  @Prop({ type: String, required: true, default: () => uuidv4() })
  _id: string;

  @Prop({ type: String, required: true, default: () => uuidv4() })
  id: string;

  @Prop({ required: true }) 
  organizationId: string;

  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  title: string;

  @Prop()
  description?: string;

  @Prop({ required: true })
  caseId: string;

  @Prop({ type: [String], required: true })
  targetWitnesses: string[];

  @Prop({ required: true })
  caseContext: string;

  @Prop({ type: [String], required: true })
  keyIssues: string[];

  @Prop({ type: [DepositionQuestion], default: [] })
  questions: DepositionQuestion[];

  @Prop({ type: [String], default: [] })
  relatedDocumentIds: string[];

  @Prop({ 
    type: String, 
    enum: Object.values(DepositionPreparationStatus),
    default: DepositionPreparationStatus.DRAFT,
    required: true 
  })
  status: string;

  @Prop({ required: true, default: Date.now })
  createdAt: Date;

  @Prop({ required: true, default: Date.now })
  updatedAt: Date;
}

export type DepositionPreparationDocument = DepositionPreparationModel & MongoDocument;
export const DEPOSITION_PREPARATION_MODEL = 'DepositionPreparation';
export const DepositionPreparationSchema = SchemaFactory.createForClass(DepositionPreparationModel);

// Add compound indexes for better query performance
DepositionPreparationSchema.index({ organizationId: 1, userId: 1 });
DepositionPreparationSchema.index({ organizationId: 1, caseId: 1 });
DepositionPreparationSchema.index({ status: 1, createdAt: -1 });

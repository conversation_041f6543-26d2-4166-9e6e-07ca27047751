import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { PrivilegeType, PrivilegeStatus, PrivilegeLogStatus } from '../interfaces/privilege-log.interface';

@Schema({ _id: false })
export class PrivilegedContentItem {
  @Prop({ type: String, required: true })
  id: string;

  @Prop({ type: String, required: true })
  documentId: string;

  @Prop({ type: Number, required: true })
  startPosition: number;

  @Prop({ type: Number, required: true })
  endPosition: number;

  @Prop({ type: String, required: true })
  content: string;

  @Prop({ type: String, enum: Object.values(PrivilegeType), required: true })
  privilegeType: PrivilegeType;

  @Prop({ type: Number, required: true, min: 0, max: 1 })
  confidenceScore: number;

  @Prop({ type: String, enum: ['pattern', 'ai', 'manual'], required: true })
  detectionMethod: string;

  @Prop({ type: String, enum: Object.values(PrivilegeStatus), required: true })
  status: PrivilegeStatus;

  @Prop({ type: String })
  reviewedBy?: string;

  @Prop({ type: Date })
  reviewedAt?: Date;

  @Prop({ type: Boolean, default: false })
  redactionApplied: boolean;

  @Prop({ type: String })
  redactionReason?: string;
}

@Schema({ _id: false })
export class AnalysisMetadata {
  @Prop({ type: Date, required: true })
  analysisDate: Date;

  @Prop({ type: [String], required: true })
  detectionMethods: string[];

  @Prop({ type: String })
  aiModelUsed?: string;

  @Prop({ type: Number, required: true })
  processingTime: number;
}

@Schema({ timestamps: true })
export class PrivilegeLog {
  @Prop({ type: String, required: true })
  documentId: string;

  @Prop({ type: String, required: true })
  documentTitle: string;

  @Prop({ type: [PrivilegedContentItem], default: [] })
  privilegedContent: PrivilegedContentItem[];

  @Prop({ type: Number, default: 0 })
  totalPrivilegedItems: number;

  @Prop({ type: Number, default: 0 })
  totalRedactions: number;

  @Prop({ type: Date, required: true })
  analysisDate: Date;

  @Prop({ type: Date })
  lastReviewDate?: Date;

  @Prop({ type: String, enum: Object.values(PrivilegeLogStatus), required: true })
  status: PrivilegeLogStatus;

  @Prop({ type: String })
  reviewedBy?: string;

  @Prop({ type: String, required: true })
  organizationId: string;

  @Prop({ type: String, required: true })
  createdBy: string;

  @Prop({ type: AnalysisMetadata, required: true })
  analysisMetadata: AnalysisMetadata;

  // Virtual fields for summary statistics
  @Prop({ type: Number, default: 0 })
  highConfidenceItems: number;

  @Prop({ type: Number, default: 0 })
  requiresManualReview: number;

  @Prop({ type: Number, default: 0 })
  autoRedactable: number;
}

export type PrivilegeLogDocument = PrivilegeLog & Document;
export const PrivilegeLogSchema = SchemaFactory.createForClass(PrivilegeLog);

// Create indexes for better query performance
PrivilegeLogSchema.index({ documentId: 1, organizationId: 1 });
PrivilegeLogSchema.index({ organizationId: 1, status: 1 });
PrivilegeLogSchema.index({ createdBy: 1, organizationId: 1 });
PrivilegeLogSchema.index({ analysisDate: -1 });

// Add text index for searching
PrivilegeLogSchema.index({
  documentTitle: 'text',
  'privilegedContent.content': 'text'
});

// Virtual for calculating summary statistics
PrivilegeLogSchema.virtual('summary').get(function() {
  const privilegedContent = this.privilegedContent || [];
  
  return {
    totalItemsFound: privilegedContent.length,
    highConfidenceItems: privilegedContent.filter(item => item.confidenceScore >= 0.8).length,
    requiresManualReview: privilegedContent.filter(item => item.status === PrivilegeStatus.UNDER_REVIEW).length,
    autoRedactable: privilegedContent.filter(item => 
      item.confidenceScore >= 0.9 && 
      item.status === PrivilegeStatus.DETECTED
    ).length
  };
});

// Ensure virtual fields are serialized
PrivilegeLogSchema.set('toJSON', { virtuals: true });
PrivilegeLogSchema.set('toObject', { virtuals: true });

export const PRIVILEGE_LOG_MODEL = 'PrivilegeLog';

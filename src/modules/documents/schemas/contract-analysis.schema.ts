import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document as MongoDocument } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import {
  DeviationType,
  RecommendationType,
  RuleSeverity,
} from './contract-playbook.schema';

// Risk Factors Schema
@Schema({ _id: false })
export class RiskFactors {
  @Prop({ type: Object, default: {} })
  deviationSeverity: {
    critical: number;
    high: number;
    medium: number;
    low: number;
  };

  @Prop({ type: Object, default: {} })
  businessImpact: {
    financial: number;
    operational: number;
    reputational: number;
    compliance: number;
  };

  @Prop({ type: Object, default: {} })
  historicalRisk: {
    pastDisputes: number;
    negotiationDifficulty: number;
    enforcementChallenges: number;
  };

  @Prop({ type: Object, default: {} })
  industryFactors: {
    standardPractice: number;
    regulatoryEnvironment: number;
    marketConditions: number;
  };
}

// Recommendation Schema
@Schema({ _id: false })
export class Recommendation {
  @Prop({ required: true, default: () => uuidv4() })
  id: string;

  @Prop({ required: true, enum: Object.values(RecommendationType) })
  type: RecommendationType;

  @Prop({ required: true, enum: ['URGENT', 'HIGH', 'MEDIUM', 'LOW'] })
  priority: string;

  @Prop({ type: Object, required: true })
  action: {
    description: string;
    specificSteps: string[];
    timeframe: string;
    assignee?: string;
  };

  @Prop({ type: Object, required: true })
  rationale: {
    riskMitigation: string;
    businessJustification: string;
    legalBasis: string;
    precedent?: string;
  };

  @Prop({ type: Object, required: true })
  implementation: {
    suggestedLanguage?: string;
    negotiationTalkingPoints: string[];
    alternatives: string[];
    fallbackPositions: string[];
  };

  @Prop({ type: Object, required: true })
  impact: {
    riskReduction: number;
    implementationEffort: string;
    businessImpact: string;
    timeline: string;
  };

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;
}

// Deviation Schema
@Schema({ _id: false })
export class Deviation {
  @Prop({ required: true, default: () => uuidv4() })
  id: string;

  @Prop({ required: true })
  ruleId: string;

  @Prop({ required: true })
  ruleName: string;

  @Prop({ required: true, enum: Object.values(DeviationType) })
  deviationType: DeviationType;

  @Prop({ required: true, enum: Object.values(RuleSeverity) })
  severity: RuleSeverity;

  @Prop({ required: true })
  clauseText: string;

  @Prop({ type: String })
  suggestedText?: string;

  @Prop({ required: true, min: 0, max: 100 })
  riskScore: number;

  @Prop({ type: Object, default: {} })
  context: {
    documentSection?: string;
    surroundingText?: string;
    pageNumber?: number;
    lineNumber?: number;
    startIndex?: number;
    endIndex?: number;
    [key: string]: any;
  };

  @Prop({ type: RiskFactors })
  riskFactors?: RiskFactors;

  @Prop({ type: [Recommendation], default: [] })
  recommendations: Recommendation[];

  @Prop({ type: String })
  aiAnalysis?: string;

  @Prop({ type: Number, min: 0, max: 1 })
  confidence?: number;

  @Prop({ type: Date, default: Date.now })
  detectedAt: Date;
}

// Contract Analysis Schema
@Schema({ timestamps: true, collection: 'contract_analyses' })
export class ContractAnalysis {
  @Prop({ required: true, default: () => uuidv4() })
  id: string;

  @Prop({ required: true })
  organizationId: string;

  @Prop({ required: true })
  contractId: string;

  @Prop({ required: true })
  playbookId: string;

  @Prop({ required: true })
  playbookName: string;

  @Prop({ min: 0, max: 100, default: 0 })
  overallScore: number;

  @Prop({ enum: ['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'], default: 'LOW' })
  riskLevel: string;

  @Prop({
    required: true,
    enum: ['PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED'],
  })
  status: string;

  @Prop({ type: [Deviation], default: [] })
  deviations: Deviation[];

  @Prop({ type: Object, default: {} })
  summary: {
    totalDeviations: number;
    criticalDeviations: number;
    highRiskDeviations: number;
    mediumRiskDeviations: number;
    lowRiskDeviations: number;
    compliancePercentage: number;
    keyFindings: string[];
    executiveSummary: string;
  };

  @Prop({ type: Object, default: {} })
  metrics: {
    processingTimeMs: number;
    aiAnalysisTime: number;
    rulesEvaluated: number;
    clausesAnalyzed: number;
    confidenceScore: number;
  };

  @Prop({ type: Object, default: {} })
  metadata: {
    contractType?: string;
    contractTitle?: string;
    analysisVersion?: string;
    aiProvider?: string;
    modelUsed?: string;
    [key: string]: any;
  };

  @Prop({ type: String })
  analyzedBy: string;

  @Prop({ type: Date, default: Date.now })
  analyzedAt: Date;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

export type ContractAnalysisDocument = ContractAnalysis & MongoDocument;
export const ContractAnalysisSchema =
  SchemaFactory.createForClass(ContractAnalysis);
export const CONTRACT_ANALYSIS_MODEL = 'ContractAnalysis';

// Add indexes for performance
ContractAnalysisSchema.index({ id: 1 }, { unique: true });
ContractAnalysisSchema.index({ organizationId: 1 });
ContractAnalysisSchema.index({ contractId: 1 });
ContractAnalysisSchema.index({ playbookId: 1 });
ContractAnalysisSchema.index({ organizationId: 1, contractId: 1 });
ContractAnalysisSchema.index({ organizationId: 1, playbookId: 1 });
ContractAnalysisSchema.index({ riskLevel: 1 });
ContractAnalysisSchema.index({ status: 1 });
ContractAnalysisSchema.index({ analyzedAt: -1 });
ContractAnalysisSchema.index({ overallScore: -1 });

// Pre-save middleware
ContractAnalysisSchema.pre('save', function (next) {
  if (!this.id) {
    this.id = uuidv4();
  }
  this.updatedAt = new Date();
  next();
});

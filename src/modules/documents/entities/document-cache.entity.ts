import { <PERSON><PERSON>ty, PrimaryColumn, Column, CreateDateColumn } from 'typeorm';

@Entity('document_cache')
export class DocumentCacheEntity {
  @PrimaryColumn()
  id: string;

  @Column()
  hash: string;

  @Column('text')
  text: string;

  @Column('simple-json')
  metadata: Record<string, unknown>;

  @CreateDateColumn()
  createdAt: Date;

  @Column({ type: 'datetime', nullable: true })
  expiresAt: Date | null;
}
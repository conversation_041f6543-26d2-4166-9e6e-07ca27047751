import {
  Module,
  NestModule,
  MiddlewareConsumer,
  forwardRef,
} from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BullModule } from '@nestjs/bull';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AIService } from '../ai/services/ai.service';
import { QueueModule } from '../queue/queue.module';
import { AuthModule } from '../auth/auth.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { AIModule } from '../ai/ai.module';
import { AnalyticsModule } from '../analytics/analytics.module';
import { LegalResearchModule } from '../legal-research/legal-research.module';
import { CourtListenerModule } from '../shared/court-listener/court-listener.module';
import { AuditModule } from '../audit/audit.module';
import { GamificationModule } from '../gamification/gamification.module';
import { geminiConfig } from '../../config/gemini.config';

import { TenantContextMiddleware } from '../auth/middleware/tenant-context.middleware';
import { TenantContextService } from '../auth/services/tenant-context.service';
import { FeatureAvailabilityGuard } from '../subscription/guards/feature-availability.guard';

// Services
import { DocumentProcessingService } from './services/document-processing.service';
import { DocumentStorageService } from './services/document-storage.service';
import { CloudflareStorageService } from './services/cloudflare-storage.service';
import { StorageFactoryService } from './services/storage-factory.service';
import { LegalPatternRecognitionService } from './services/legal-pattern-recognition.service';
import { DocumentProcessingQueueService } from './services/document-processing-queue.service';
import { DocumentsService } from './services/documents.service';
import { AnalysisResultService } from './services/analysis-result.service';
import { DocumentRepository } from './repositories/document.repository';
import { DepositionPreparationRepository } from './repositories/deposition-preparation.repository';
import { DepositionAnalysisRepository } from './repositories/deposition-analysis.repository';
import { AnalysisResultRepository } from './repositories/analysis-result.repository';
import { DocumentClassificationService } from './services/document-classification.service';
import { DocumentSchema, DOCUMENT_MODEL } from './schemas/document.schema';
import {
  AnalysisResult,
  AnalysisResultSchema,
} from './schemas/analysis-result.schema';
import {
  DocumentVersionModel,
  DocumentVersionSchema,
  DOCUMENT_VERSION_MODEL,
} from './schemas/document-version.schema'; // Added import
import {
  ClauseTemplate,
  ClauseTemplateSchema,
  CLAUSE_TEMPLATE_MODEL,
} from './schemas/clause-template.schema';
import {
  DepositionPreparationSchema,
  DEPOSITION_PREPARATION_MODEL,
} from './schemas/deposition-preparation.schema';
import {
  DepositionAnalysisSchema,
  DEPOSITION_ANALYSIS_MODEL,
} from './schemas/deposition-analysis.schema';
import { NegotiationPlaybookSchemaFactory } from './schemas/negotiation-playbook.schema';
import {
  PrivilegeLogSchema,
  PRIVILEGE_LOG_MODEL,
} from './schemas/privilege-log.schema';
import {
  NegotiationScenarioSchema,
  NegotiationSessionSchema,
  NegotiationTemplateSchema,
  NEGOTIATION_SCENARIO_MODEL,
  NEGOTIATION_SESSION_MODEL,
  NEGOTIATION_TEMPLATE_MODEL,
} from './schemas/negotiation-simulator.schema';
import {
  ComplianceAuditResultSchema,
  ComplianceProfileSchema,
  RegulatoryFrameworkSchema,
  ComplianceReportSchema,
  COMPLIANCE_AUDIT_RESULT_MODEL,
  COMPLIANCE_PROFILE_MODEL,
  REGULATORY_FRAMEWORK_MODEL,
  COMPLIANCE_REPORT_MODEL,
} from './schemas/compliance-auditor.schema';
import {
  ContractPlaybookSchema,
  CONTRACT_PLAYBOOK_MODEL,
} from './schemas/contract-playbook.schema';
import {
  ContractAnalysisSchema,
  CONTRACT_ANALYSIS_MODEL,
} from './schemas/contract-analysis.schema';
import { DocumentAnalysisTransformer } from './transformers/document-analysis.transformer';
import { DocumentExportService } from './services/document-export.service';

// Controllers
import { DocumentsController } from './controllers/documents.controller';
import { DocumentPatternController } from './controllers/document-pattern.controller';
import { DocumentComparisonController } from './controllers/document-comparison.controller';
import { EnhancedDocumentComparisonController } from './controllers/enhanced-document-comparison.controller';
import { ClauseLibraryController } from './controllers/clause-library.controller';
import { PrecedentController } from './controllers/precedent.controller';
import { DepositionController } from './controllers/deposition.controller'; // Added import
import { NegotiationPlaybookController } from './controllers/negotiation-playbook.controller'; // Added import
import { SamplePlaybooksController } from './controllers/sample-playbooks.controller'; // Added import
import { SampleContractPlaybooksController } from './controllers/sample-contract-playbooks.controller'; // Added import
import { PrivilegeLogController } from './controllers/privilege-log.controller'; // Added import
import { NegotiationSimulatorController } from './controllers/negotiation-simulator.controller'; // Added import
import { ComplianceAuditorController } from './controllers/compliance-auditor.controller'; // Added import
import { ContractPlaybookController } from './controllers/contract-playbook.controller'; // Added import
import { DocumentAutomationController } from './controllers/document-automation.controller'; // Added import

// Services
import { DocumentComparisonService } from './services/document-comparison.service';
import { EnhancedDocumentComparisonService } from './services/enhanced-document-comparison.service';
import { ClauseLibraryService } from './services/clause-library.service';
import { PrecedentService } from './services/precedent.service';
import { DepositionService } from './services/deposition.service'; // Added import
import { NegotiationPlaybookService } from './services/negotiation-playbook.service'; // Added import
import { SamplePlaybooksDataService } from './services/negotiation-playbook-seeder.service'; // Added import
import { SampleContractPlaybooksService } from './services/sample-contract-playbooks.service'; // Added import
import { PrivilegeLogService } from './services/privilege-log.service'; // Added import
import { NegotiationSimulatorService } from './services/negotiation-simulator.service'; // Added import
import { ComplianceAuditorService } from './services/compliance-auditor.service'; // Added import
import { ContractPlaybookService } from './services/contract-playbook.service'; // Added import
import { DocumentAutomationService } from './services/document-automation.service'; // Added import
import { ClauseLibraryAutomationService } from './services/clause-library-automation.service'; // Added import

// Queue Processor
import { DocumentProcessingProcessor } from './services/document-processing.processor';

// New Performance Services
import { DocumentProcessingGateway } from './gateways/document-processing.gateway';
import { DocumentProcessingCacheService } from './services/document-processing-cache.service';
import { DocumentProcessingMetricsService } from './services/document-processing-metrics.service';
import { DocumentProcessingManagerService } from './services/document-processing-manager.service';
import { DocumentCacheService } from './services/document-cache.service';

import { QUEUES } from '../queue/constants';

// Constants
const QUEUE_NAME = 'document-processing';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: DOCUMENT_MODEL,
        schema: DocumentSchema,
      },
      {
        name: AnalysisResult.name,
        schema: AnalysisResultSchema,
      },
      {
        // Added registration for DocumentVersionModel
        name: DOCUMENT_VERSION_MODEL,
        schema: DocumentVersionSchema,
      },
      {
        // Registration for ClauseTemplate model
        name: CLAUSE_TEMPLATE_MODEL,
        schema: ClauseTemplateSchema,
      },
      {
        // Registration for DepositionPreparation model
        name: DEPOSITION_PREPARATION_MODEL,
        schema: DepositionPreparationSchema,
      },
      {
        // Registration for DepositionAnalysis model
        name: DEPOSITION_ANALYSIS_MODEL,
        schema: DepositionAnalysisSchema,
      },
      {
        // Registration for NegotiationPlaybook model
        name: 'NegotiationPlaybook',
        schema: NegotiationPlaybookSchemaFactory,
      },
      {
        // Registration for PrivilegeLog model
        name: PRIVILEGE_LOG_MODEL,
        schema: PrivilegeLogSchema,
      },
      {
        // Registration for NegotiationScenario model
        name: NEGOTIATION_SCENARIO_MODEL,
        schema: NegotiationScenarioSchema,
      },
      {
        // Registration for NegotiationSession model
        name: NEGOTIATION_SESSION_MODEL,
        schema: NegotiationSessionSchema,
      },
      {
        // Registration for NegotiationTemplate model
        name: NEGOTIATION_TEMPLATE_MODEL,
        schema: NegotiationTemplateSchema,
      },
      {
        // Registration for ComplianceAuditResult model
        name: COMPLIANCE_AUDIT_RESULT_MODEL,
        schema: ComplianceAuditResultSchema,
      },
      {
        // Registration for ComplianceProfile model
        name: COMPLIANCE_PROFILE_MODEL,
        schema: ComplianceProfileSchema,
      },
      {
        // Registration for RegulatoryFramework model
        name: REGULATORY_FRAMEWORK_MODEL,
        schema: RegulatoryFrameworkSchema,
      },
      {
        // Registration for ComplianceReport model
        name: COMPLIANCE_REPORT_MODEL,
        schema: ComplianceReportSchema,
      },
      {
        // Registration for ContractPlaybook model
        name: CONTRACT_PLAYBOOK_MODEL,
        schema: ContractPlaybookSchema,
      },
      {
        // Registration for ContractAnalysis model
        name: CONTRACT_ANALYSIS_MODEL,
        schema: ContractAnalysisSchema,
      },
    ]),

    BullModule.registerQueue({
      name: QUEUES.DOCUMENT_PROCESSING,
      defaultJobOptions: {
        attempts: 3,
        backoff: { type: 'exponential', delay: 1000 },
        removeOnComplete: false,
        removeOnFail: false,
      },
    }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('auth.jwtSecret'),
        signOptions: {
          expiresIn: configService.get<string>('auth.jwtExpiresIn'),
        },
      }),
    }),
    QueueModule,
    forwardRef(() => AIModule),
    forwardRef(() => AuthModule),
    SubscriptionModule,
    forwardRef(() => LegalResearchModule),
    forwardRef(() => AnalyticsModule),
    CourtListenerModule,
    forwardRef(() => AuditModule),
    forwardRef(() => GamificationModule),
  ],
  controllers: [
    DocumentsController,
    DocumentPatternController,
    DocumentComparisonController,
    EnhancedDocumentComparisonController,
    ClauseLibraryController,
    PrecedentController,
    DepositionController, // Added controller
    NegotiationPlaybookController, // Added controller
    SamplePlaybooksController, // Added controller
    SampleContractPlaybooksController, // Added controller
    PrivilegeLogController, // Added controller
    NegotiationSimulatorController, // Added controller
    ComplianceAuditorController, // Added controller
    ContractPlaybookController, // Added controller
    DocumentAutomationController, // Added controller
  ],
  providers: [
    DocumentsService,
    DocumentProcessingService,
    DocumentStorageService,
    CloudflareStorageService,
    StorageFactoryService,
    LegalPatternRecognitionService,
    DocumentProcessingProcessor,
    DocumentProcessingQueueService,
    DocumentAnalysisTransformer,
    AnalysisResultRepository,
    AnalysisResultService,
    DocumentComparisonService,
    EnhancedDocumentComparisonService,
    DocumentClassificationService,
    DepositionPreparationRepository,
    DepositionAnalysisRepository,
    DocumentExportService,
    ClauseLibraryService,
    PrecedentService,
    DepositionService, // Added service
    NegotiationPlaybookService, // Added service
    SamplePlaybooksDataService, // Added service
    SampleContractPlaybooksService, // Added service
    PrivilegeLogService, // Added service
    NegotiationSimulatorService, // Added service
    ComplianceAuditorService, // Added service
    ContractPlaybookService, // Added service
    DocumentAutomationService, // Added service
    ClauseLibraryAutomationService, // Added service
    // New Performance Services
    DocumentProcessingGateway,
    DocumentProcessingCacheService,
    DocumentProcessingMetricsService,
    DocumentProcessingManagerService,
    DocumentCacheService,
    // FeatureAvailabilityGuard should be provided by SubscriptionModule
    {
      provide: 'IDocumentProcessor',
      useClass: DocumentProcessingService,
    },
    // TenantContextService is available through AuthModule import
  ],
  exports: [
    DocumentProcessingService,
    DocumentStorageService,
    CloudflareStorageService,
    StorageFactoryService,
    LegalPatternRecognitionService,
    DocumentProcessingQueueService,
    BullModule,
    DocumentsService,
    AnalysisResultService,
    AnalysisResultRepository, // Added for ChatNegotiationModule
    DocumentComparisonService,
    EnhancedDocumentComparisonService,
    DocumentExportService,
    ClauseLibraryService,
    DepositionService,
    DepositionPreparationRepository,
    DepositionAnalysisRepository,
    NegotiationSimulatorService, // Added for ChatNegotiationModule
    MongooseModule,
  ],
})
export class DocumentsModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(TenantContextMiddleware).forRoutes(
      DocumentsController,
      DocumentPatternController,
      DocumentComparisonController,
      EnhancedDocumentComparisonController,
      ClauseLibraryController,
      PrecedentController,
      DepositionController, // Added controller to middleware routes
      NegotiationPlaybookController, // Added controller to middleware routes
      SamplePlaybooksController, // Added controller to middleware routes
      SampleContractPlaybooksController, // Added controller to middleware routes
      PrivilegeLogController, // Added controller to middleware routes
      NegotiationSimulatorController, // Added controller to middleware routes
      ComplianceAuditorController, // Added controller to middleware routes
    );
  }
}

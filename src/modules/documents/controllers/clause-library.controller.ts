import { Controller, Get, Post, Body, UseGuards, Query, Logger } from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { FeatureAvailabilityGuard } from '../../subscription/guards/feature-availability.guard';
import { RequireFeatures } from '../../subscription/decorators/require-features.decorator';
import { SkipSubscriptionCheck } from '../../subscription/decorators/skip-subscription-check.decorator';
import { ClauseLibraryService } from '../services/clause-library.service';
import { CreateClauseTemplateDto, UpdateClauseTemplateDto, IdentifyClausesDto, GenerateTemplateDto, SearchClauseTemplatesDto } from '../dto/clause-template.dto';
import { User } from '../../auth/decorators/user.decorator';
import { Organization } from '../../auth/decorators/organization.decorator';
import { UseCredits, FreeFeature } from '../../subscription/decorators/use-credits.decorator';
import { PostHogService } from '../../posthog/services/posthog.service';

@Controller('documents/clause-library')
@UseGuards(JwtAuthGuard, FeatureAvailabilityGuard)
@RequireFeatures('clause_library')
@SkipSubscriptionCheck()
export class ClauseLibraryController {
  private readonly logger = new Logger(ClauseLibraryController.name);

  constructor(
    private readonly clauseLibraryService: ClauseLibraryService,
    private readonly postHogService: PostHogService
  ) {}

  @Post('templates')
  @FreeFeature() // Creating clause templates is free
  async createTemplate(
    @Body() createDto: CreateClauseTemplateDto,
    @Organization() organizationId: string,
    @User('sub') userId: string
  ) {
    return this.clauseLibraryService.createTemplate(createDto, organizationId, userId);
  }

  @Get('templates')
  @FreeFeature() // Viewing clause templates is free
  async findAllTemplates(
    @Organization() organizationId: string,
    @Query() searchDto?: SearchClauseTemplatesDto
  ) {
    this.logger.debug(`Finding templates for org ${organizationId}, search criteria:`, searchDto);
    return this.clauseLibraryService.findAll(organizationId, searchDto);
  }

  @Post('identify')
  @UseCredits('clause_identification') // AI clause identification consumes credits
  async identifyClauses(
    @Body() dto: IdentifyClausesDto,
    @Organization() organizationId: string,
    @User('sub') userId: string
  ) {
    const startTime = Date.now();
    
    try {
      const result = await this.clauseLibraryService.identifyClauses(dto, organizationId, userId);
      
      const duration = Date.now() - startTime;
      
      // Track successful clause identification
      this.postHogService.trackEvent(userId, 'clause_identification_completed', {
        organization_id: organizationId,
        duration_ms: duration,
        success: true
      });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      // Track failed clause identification
      this.postHogService.trackError(userId, error, 'clause_identification', {
        organization_id: organizationId,
        duration_ms: duration
      });
      
      throw error;
    }
  }

  @Post('generate-template')
  @UseCredits('template_generation') // AI template generation consumes credits
  async generateTemplate(
    @Body() dto: GenerateTemplateDto,
    @Organization() organizationId: string,
    @User('sub') userId: string
  ) {
    const startTime = Date.now();
    
    try {
      const result = await this.clauseLibraryService.generateTemplate(dto, organizationId, userId);
      
      const duration = Date.now() - startTime;
      
      // Track successful template generation
      this.postHogService.trackEvent(userId, 'clause_template_generated', {
        organization_id: organizationId,
        duration_ms: duration,
        success: true
      });
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      
      // Track failed template generation
      this.postHogService.trackError(userId, error, 'clause_template_generation', {
        organization_id: organizationId,
        duration_ms: duration
      });
      
      throw error;
    }
  }
}

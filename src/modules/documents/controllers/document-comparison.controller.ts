import { Controller, Post, Body, Logger, HttpStatus, HttpException, ValidationPipe, BadRequestException, Request, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBadRequestResponse } from '@nestjs/swagger';
import { DocumentComparisonService } from '../services/document-comparison.service';
import { CompareDocumentsDto, ComparisonType } from '../dto/compare-documents.dto';
import { UseCredits } from '../../subscription/decorators/use-credits.decorator';
import { Organization } from '../../auth/decorators/organization.decorator';
import { User } from '../../auth/decorators/user.decorator';
import { RequireFeatures } from '../../subscription/decorators/require-features.decorator';
import { FeatureAvailabilityGuard } from '../../subscription/guards/feature-availability.guard';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { PostHogService } from '../../posthog/services/posthog.service';

@ApiTags('Documents')
@Controller('documents/comparison')
@UseGuards(JwtAuthGuard, FeatureAvailabilityGuard)
export class DocumentComparisonController {
  private readonly logger = new Logger(DocumentComparisonController.name);

  constructor(
    private readonly comparisonService: DocumentComparisonService,
    private readonly postHogService: PostHogService
  ) {}

  @Post()
  @UseCredits('basic_comparison') // Document comparison consumes credits
  @RequireFeatures('basic_comparison')
  @ApiOperation({ summary: 'Compare two documents' })
  @ApiResponse({ status: 201, description: 'Documents compared successfully' })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  async compareDocuments(
    @Body(new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      errorHttpStatusCode: HttpStatus.BAD_REQUEST, // Ensure validation errors return 400
      exceptionFactory: (errors) => {
        // Customize the validation error response
        const errorMessages = errors.map(err => {
          const constraints = err.constraints ? Object.values(err.constraints) : ['Invalid input'];
          return `${err.property}: ${constraints.join(', ')}`;
        });
        
        const logger = new Logger(DocumentComparisonController.name);
        logger.warn('Validation error in document comparison request', { errors: errorMessages });
        
        return new BadRequestException({
          status: 'error',
          message: 'Validation failed',
          details: errorMessages
        });
      }
    }))
    request: CompareDocumentsDto,
    @Organization() organizationId: string,
    @User('sub') userId: string
  ) {
    const startTime = Date.now();
    
    try {
      this.logger.debug('Processing comparison request', {
        docALength: request.documentA.length,
        docBLength: request.documentB.length,
        documentAId: request.documentAId,
        documentBId: request.documentBId
      });

      // Validate that documents are not empty after trimming
      if (!request.documentA.trim() || !request.documentB.trim()) {
        throw new BadRequestException({
          status: 'error',
          message: 'Document content cannot be empty'
        });
      }

      const result = await this.comparisonService.compareDocuments(
        request.documentA,
        request.documentB,
        {
          documentAId: request.documentAId,
          documentBId: request.documentBId,
          userId: userId,
          organizationId: organizationId
        }
      );

      const duration = Date.now() - startTime;

      // Track successful document comparison
      this.postHogService.trackDocumentComparison(userId, [request.documentAId, request.documentBId], 'basic', {
        organization_id: organizationId,
        document_a_length: request.documentA.length,
        document_b_length: request.documentB.length,
        duration_ms: duration,
        success: true
      });

      return {
        status: 'success',
        data: result,
        metadata: {
          timestamp: new Date().toISOString(),
          documentStats: {
            documentA: { length: request.documentA.length, id: request.documentAId },
            documentB: { length: request.documentB.length, id: request.documentBId }
          }
        }
      };

    } catch (error) {
      const duration = Date.now() - startTime;

      // Track failed document comparison
      this.postHogService.trackError(userId, error, 'document_comparison', {
        organization_id: organizationId,
        document_a_id: request.documentAId,
        document_b_id: request.documentBId,
        duration_ms: duration
      });

      this.logger.error(`Error comparing documents: ${error.message}`);

      // If the error is already an HTTP exception, rethrow it
      if (error instanceof HttpException) {
        throw error;
      }

      // Otherwise, convert to an internal server error
      throw new HttpException({
        status: 'error',
        message: 'Error comparing documents',
        details: error.message || 'An unexpected error occurred'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
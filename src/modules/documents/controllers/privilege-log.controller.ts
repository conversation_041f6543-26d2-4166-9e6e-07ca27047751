import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { DocumentAccessGuard } from '../guards/document-access.guard';
import { FeatureAvailabilityGuard } from '../../subscription/guards/feature-availability.guard';
import { RequireFeatures } from '../../subscription/decorators/require-features.decorator';
import { UseCredits, FreeFeature } from '../../subscription/decorators/use-credits.decorator';
import { User } from '../../auth/decorators/user.decorator';
import { Organization } from '../../auth/decorators/organization.decorator';
import { PrivilegeLogService } from '../services/privilege-log.service';
import {
  AnalyzePrivilegeDto,
  ReviewPrivilegeDto,
  ApplyRedactionDto,
  BulkRedactionDto,
  PrivilegeLogQueryDto,
} from '../dto/privilege-log.dto';

@ApiTags('Privilege Log')
@Controller('documents')
@UseGuards(JwtAuthGuard, DocumentAccessGuard, FeatureAvailabilityGuard)
@RequireFeatures('privilege_log_automation')
@ApiBearerAuth()
export class PrivilegeLogController {
  private readonly logger = new Logger(PrivilegeLogController.name);

  constructor(private readonly privilegeLogService: PrivilegeLogService) {}

  @Post(':documentId/privilege-analysis')
  @UseCredits('privilege_detection') // AI privilege detection consumes credits
  @ApiOperation({
    summary: 'Analyze document for privileged content',
    description:
      'Scans a document for attorney-client privilege, work product, and other privileged content',
  })
  @ApiParam({
    name: 'documentId',
    description: 'The ID of the document to analyze',
    type: String,
  })
  @ApiResponse({
    status: 201,
    description: 'Privilege analysis completed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            documentId: { type: 'string' },
            privilegedContent: { type: 'array' },
            redactionSuggestions: { type: 'array' },
            summary: { type: 'object' },
            analysisMetadata: { type: 'object' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Document not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Feature not available for current subscription',
  })
  async analyzePrivilege(
    @Param('documentId') documentId: string,
    @Body() analyzeDto: AnalyzePrivilegeDto,
    @User('sub') userId: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Analyzing privilege for document ${documentId}`);

    const result = await this.privilegeLogService.analyzeDocumentPrivilege(
      documentId,
      analyzeDto,
      userId,
      organizationId,
    );

    return {
      success: true,
      data: result,
    };
  }

  @Get(':documentId/privilege-log')
  @FreeFeature() // Viewing privilege logs is free
  @ApiOperation({
    summary: 'Get privilege log for document',
    description:
      'Retrieves the privilege log and analysis results for a document',
  })
  @ApiParam({
    name: 'documentId',
    description: 'The ID of the document',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Privilege log retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Privilege log not found',
  })
  async getPrivilegeLog(
    @Param('documentId') documentId: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Retrieving privilege log for document ${documentId}`);

    const privilegeLog = await this.privilegeLogService.getPrivilegeLog(
      documentId,
      organizationId,
    );

    return {
      success: true,
      data: privilegeLog,
    };
  }

  @Get('privilege-logs')
  @FreeFeature() // Viewing privilege logs is free
  @ApiOperation({
    summary: 'Get all privilege logs for organization',
    description:
      'Retrieves all privilege logs for the organization with filtering and pagination',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter by privilege log status',
  })
  @ApiQuery({
    name: 'privilegeType',
    required: false,
    description: 'Filter by privilege type',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
    type: Number,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'Privilege logs retrieved successfully',
  })
  async getPrivilegeLogs(
    @Organization() organizationId: string,
    @Query() query: PrivilegeLogQueryDto,
  ) {
    this.logger.log(
      `Retrieving privilege logs for organization ${organizationId}`,
    );

    const result = await this.privilegeLogService.getPrivilegeLogs(
      organizationId,
      query,
    );

    return {
      success: true,
      data: result.logs,
      pagination: {
        total: result.total,
        page: query.page || 1,
        limit: query.limit || 20,
        pages: Math.ceil(result.total / (query.limit || 20)),
      },
    };
  }

  @Put(':documentId/privilege-content/:contentId/review')
  @FreeFeature() // Reviewing privilege content is free
  @ApiOperation({
    summary: 'Review privileged content item',
    description:
      'Updates the status of a privileged content item after manual review',
  })
  @ApiParam({
    name: 'documentId',
    description: 'The ID of the document',
    type: String,
  })
  @ApiParam({
    name: 'contentId',
    description: 'The ID of the privileged content item',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Privileged content reviewed successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Privileged content item not found',
  })
  async reviewPrivilegedContent(
    @Param('documentId') documentId: string,
    @Param('contentId') contentId: string,
    @Body() reviewDto: ReviewPrivilegeDto,
    @User('sub') userId: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(
      `Reviewing privileged content ${contentId} in document ${documentId}`,
    );

    const result = await this.privilegeLogService.reviewPrivilegedContent(
      documentId,
      contentId,
      reviewDto,
      userId,
      organizationId,
    );

    return {
      success: true,
      data: result,
      message: 'Privileged content reviewed successfully',
    };
  }

  @Post(':documentId/redactions')
  @UseCredits('redaction_automation') // AI redaction automation consumes credits
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Apply redaction to privileged content',
    description: 'Applies redaction to a specific privileged content item',
  })
  @ApiParam({
    name: 'documentId',
    description: 'The ID of the document',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Redaction applied successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Privileged content item not found',
  })
  async applyRedaction(
    @Param('documentId') documentId: string,
    @Body() redactionDto: ApplyRedactionDto,
    @User('sub') userId: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(
      `Applying redaction to content ${redactionDto.contentId} in document ${documentId}`,
    );

    const result = await this.privilegeLogService.applyRedaction(
      documentId,
      redactionDto,
      userId,
      organizationId,
    );

    return {
      success: result.success,
      message: result.message,
    };
  }

  @Post(':documentId/bulk-redactions')
  @UseCredits('redaction_automation') // AI bulk redaction automation consumes credits
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Apply bulk redactions to multiple privileged content items',
    description:
      'Applies redactions to multiple privileged content items at once',
  })
  @ApiParam({
    name: 'documentId',
    description: 'The ID of the document',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Bulk redactions applied successfully',
  })
  async applyBulkRedaction(
    @Param('documentId') documentId: string,
    @Body() bulkRedactionDto: BulkRedactionDto,
    @User('sub') userId: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(
      `Applying bulk redactions to ${bulkRedactionDto.contentIds.length} items in document ${documentId}`,
    );

    const result = await this.privilegeLogService.applyBulkRedaction(
      documentId,
      bulkRedactionDto,
      userId,
      organizationId,
    );

    return {
      success: result.success,
      data: {
        redactedCount: result.redactedCount,
      },
      message: result.message,
    };
  }
}

import {
  Controller,
  Post,
  Get,
  Param,
  Query,
  Body,
  Logger,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { DocumentsService } from '../services/documents.service';
import { LegalPatternRecognitionService } from '../services/legal-pattern-recognition.service';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { DocumentProcessingJobType } from '../interfaces/document-processing.types';
import { QUEUES } from '../../queue/constants';
import {
  LegalPattern,
  PatternRecognitionResult,
} from '../interfaces/legal-pattern.interface';

@ApiTags('document-patterns')
@Controller('documents/patterns')
export class DocumentPatternController {
  private readonly logger = new Logger(DocumentPatternController.name);

  constructor(
    private readonly documentsService: DocumentsService,
    private readonly patternRecognitionService: LegalPatternRecognitionService,
    @InjectQueue(QUEUES.DOCUMENT_PROCESSING)
    private readonly documentProcessingQueue: Queue,
  ) {}

  @Post(':id/analyze')
  @ApiOperation({ summary: 'Analyze a document for legal patterns' })
  @ApiParam({ name: 'id', description: 'Document ID' })
  @ApiResponse({
    status: 202,
    description: 'Pattern analysis job added to queue',
  })
  @ApiResponse({ status: 404, description: 'Document not found' })
  async analyzeDocumentPatterns(@Param('id') documentId: string) {
    try {
      // Check if document exists
      const document = await this.documentsService.findById(documentId);
      if (!document) {
        throw new NotFoundException(`Document with ID ${documentId} not found`);
      }

      // Add a job to the queue
      const job = await this.documentProcessingQueue.add(
        DocumentProcessingJobType.ANALYZE_PATTERNS,
        { documentId },
        {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      );

      return {
        message: 'Pattern analysis job has been queued',
        jobId: job.id,
        documentId,
        status: 'queued',
      };
    } catch (error) {
      this.logger.error(
        `Error queuing pattern analysis for document ${documentId}:`,
        error.stack,
      );
      throw error;
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get document patterns' })
  @ApiParam({ name: 'id', description: 'Document ID' })
  @ApiQuery({
    name: 'type',
    required: false,
    description: 'Filter by pattern type',
  })
  @ApiResponse({ status: 200, description: 'Return document patterns' })
  @ApiResponse({ status: 404, description: 'Document not found' })
  async getDocumentPatterns(
    @Param('id') documentId: string,
    @Query('type') type?: string,
  ) {
    try {
      // Check if document exists
      const document = await this.documentsService.findById(documentId);
      if (!document) {
        throw new NotFoundException(`Document with ID ${documentId} not found`);
      }

      // Get patterns from document metadata
      const metadata = await this.documentsService.getMetadata(documentId);

      if (!metadata?.patterns) {
        return {
          message: 'No patterns found for this document',
          documentId,
          patterns: [],
        };
      }

      let patterns = metadata.patterns as LegalPattern[];

      // Filter by type if requested
      if (type) {
        patterns = patterns.filter((pattern) => pattern.type === type);
      }

      // Get pattern statistics
      const patternResult: PatternRecognitionResult = {
        patterns,
        documentId,
        timestamp: this.getValidDate(metadata.patternAnalysisDate),
      };

      const statistics =
        this.patternRecognitionService.getPatternStatistics(patternResult);

      return {
        documentId,
        patternCount: patterns.length,
        patterns,
        statistics,
        analyzedAt: metadata.patternAnalysisDate || new Date(),
      };
    } catch (error) {
      this.logger.error(
        `Error retrieving patterns for document ${documentId}:`,
        error.stack,
      );
      throw error;
    }
  }

  @Get(':id/statistics')
  @ApiOperation({ summary: 'Get document pattern statistics' })
  @ApiParam({ name: 'id', description: 'Document ID' })
  @ApiResponse({ status: 200, description: 'Return pattern statistics' })
  @ApiResponse({ status: 404, description: 'Document not found' })
  async getDocumentPatternStatistics(@Param('id') documentId: string) {
    try {
      // Check if document exists
      const document = await this.documentsService.findById(documentId);
      if (!document) {
        throw new NotFoundException(`Document with ID ${documentId} not found`);
      }

      // Get patterns from document metadata
      const metadata = await this.documentsService.getMetadata(documentId);

      if (!metadata?.patterns) {
        return {
          message: 'No patterns found for this document',
          documentId,
          statistics: {},
        };
      }

      // Create pattern result object and get statistics
      const patternResult: PatternRecognitionResult = {
        patterns: metadata.patterns as LegalPattern[],
        documentId,
        timestamp: this.getValidDate(metadata.patternAnalysisDate),
      };

      const statistics =
        this.patternRecognitionService.getPatternStatistics(patternResult);

      return {
        documentId,
        totalPatterns: patternResult.patterns.length,
        statistics,
        analyzedAt: metadata.patternAnalysisDate || new Date(),
      };
    } catch (error) {
      this.logger.error(
        `Error retrieving pattern statistics for document ${documentId}:`,
        error.stack,
      );
      throw error;
    }
  }

  @Get(':id/by-type/:type')
  @ApiOperation({ summary: 'Get document patterns by specific type' })
  @ApiParam({ name: 'id', description: 'Document ID' })
  @ApiParam({ name: 'type', description: 'Pattern type' })
  @ApiResponse({
    status: 200,
    description: 'Return patterns of specified type',
  })
  @ApiResponse({ status: 404, description: 'Document not found' })
  async getDocumentPatternsByType(
    @Param('id') documentId: string,
    @Param('type') type: string,
  ) {
    try {
      // Check if document exists
      const document = await this.documentsService.findById(documentId);
      if (!document) {
        throw new NotFoundException(`Document with ID ${documentId} not found`);
      }

      // Get patterns from document metadata
      const metadata = await this.documentsService.getMetadata(documentId);

      if (!metadata?.patterns) {
        return {
          message: 'No patterns found for this document',
          documentId,
          type,
          patterns: [],
        };
      }

      // Create pattern result object and filter by type
      const patternResult: PatternRecognitionResult = {
        patterns: metadata.patterns as LegalPattern[],
        documentId,
        timestamp:
          typeof metadata.patternAnalysisDate === 'string' ||
          metadata.patternAnalysisDate instanceof Date
            ? new Date(metadata.patternAnalysisDate)
            : new Date(),
      };

      const typePatterns = this.patternRecognitionService.getPatternsByType(
        patternResult,
        type,
      );

      return {
        documentId,
        type,
        count: typePatterns.length,
        patterns: typePatterns,
        analyzedAt: metadata.patternAnalysisDate || new Date(),
      };
    } catch (error) {
      this.logger.error(
        `Error retrieving patterns by type for document ${documentId}:`,
        error.stack,
      );
      throw error;
    }
  }

  private getValidDate(date: unknown): Date {
    if (!date) return new Date();
    if (date instanceof Date) return date;
    if (typeof date === 'string' || typeof date === 'number') {
      const parsedDate = new Date(date);
      return isNaN(parsedDate.getTime()) ? new Date() : parsedDate;
    }
    return new Date();
  }
}

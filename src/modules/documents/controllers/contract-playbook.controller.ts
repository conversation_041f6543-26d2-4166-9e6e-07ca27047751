import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
  HttpStatus,
  HttpCode,
  UnauthorizedException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

// Guards and Decorators
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { FeatureAvailabilityGuard } from '../../subscription/guards/feature-availability.guard';
import { RequireFeatures } from '../../subscription/decorators/require-features.decorator';
import { SkipDocumentAccessGuard } from '../../auth/decorators/skip-document-access-guard.decorator';
import { UseCredits, FreeFeature } from '../../subscription/decorators/use-credits.decorator';

// DTOs
import {
  CreateContractPlaybookDto,
  UpdateContractPlaybookDto,
  AnalyzeContractDto,
  SearchPlaybooksDto,
  SearchAnalysesDto,
} from '../dto/contract-playbook.dto';

// Services
import { ContractPlaybookService } from '../services/contract-playbook.service';
import { TenantContextService } from '../../auth/services/tenant-context.service';

// Decorators
import { Organization } from '../../auth/decorators/organization.decorator';
import { User } from '../../auth/decorators/user.decorator';

@ApiTags('Contract Playbooks')
@Controller('contract-playbooks')
@UseGuards(JwtAuthGuard, FeatureAvailabilityGuard)
@RequireFeatures('contract_playbooks')
@SkipDocumentAccessGuard()
export class ContractPlaybookController {
  private readonly logger = new Logger(ContractPlaybookController.name);

  constructor(
    private readonly contractPlaybookService: ContractPlaybookService,
    private readonly tenantContext: TenantContextService,
  ) {}

  /**
   * Get current user context with proper error handling
   */
  private getCurrentUserContext(): { userId: string; organizationId: string } {
    const userId = this.tenantContext.getCurrentUserId();
    const organizationId = this.tenantContext.getCurrentOrganization();

    if (!userId || !organizationId) {
      throw new UnauthorizedException('User context not found');
    }

    return { userId, organizationId };
  }

  // Playbook Management Endpoints

  @Post()
  @FreeFeature() // Creating playbooks is free - no AI involved
  @ApiOperation({ summary: 'Create a new contract playbook' })
  @ApiResponse({ status: 201, description: 'Playbook created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input data' })
  @ApiResponse({
    status: 409,
    description: 'Playbook with same name already exists',
  })
  async createPlaybook(
    @Body() createDto: CreateContractPlaybookDto,
    @Organization() organizationId: string,
    @User('id') userId: string,
  ) {
    this.logger.log(
      `Creating playbook: ${createDto.name} for org: ${organizationId}`,
    );
    return this.contractPlaybookService.createPlaybook(
      createDto,
      organizationId,
      userId,
    );
  }

  @Get()
  @FreeFeature() // Viewing playbooks is free
  @ApiOperation({ summary: 'Get all contract playbooks' })
  @ApiResponse({ status: 200, description: 'Playbooks retrieved successfully' })
  @ApiQuery({ name: 'query', required: false, description: 'Search query' })
  @ApiQuery({
    name: 'contractType',
    required: false,
    description: 'Filter by contract type',
  })
  @ApiQuery({
    name: 'isActive',
    required: false,
    description: 'Filter by active status',
  })
  @ApiQuery({
    name: 'isTemplate',
    required: false,
    description: 'Filter by template status',
  })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  async findAllPlaybooks(
    @Query() searchDto: SearchPlaybooksDto,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Finding playbooks for org: ${organizationId}`);
    return this.contractPlaybookService.findAllPlaybooks(
      organizationId,
      searchDto,
    );
  }

  // Contract Analysis Endpoints (moved before :id routes)

  @Post('analyze')
  @UseCredits('playbook_analysis') // Only the AI analysis consumes credits
  @ApiOperation({ summary: 'Analyze a contract against a playbook' })
  @ApiResponse({ status: 201, description: 'Contract analysis completed' })
  @ApiResponse({ status: 400, description: 'Invalid analysis request' })
  @ApiResponse({ status: 404, description: 'Contract or playbook not found' })
  async analyzeContract(
    @Body() analyzeDto: AnalyzeContractDto,
    @Organization() organizationId: string,
    @User('id') userId: string,
  ) {
    this.logger.log(
      `Analyzing contract ${analyzeDto.contractId} with playbook ${analyzeDto.playbookId}`,
    );
    return this.contractPlaybookService.analyzeContract(
      analyzeDto,
      organizationId,
      userId,
    );
  }

  @Get('analyses')
  @FreeFeature() // Viewing analysis results is free
  @ApiOperation({ summary: 'Get all contract analyses' })
  @ApiResponse({ status: 200, description: 'Analyses retrieved successfully' })
  @ApiQuery({
    name: 'contractId',
    required: false,
    description: 'Filter by contract ID',
  })
  @ApiQuery({
    name: 'playbookId',
    required: false,
    description: 'Filter by playbook ID',
  })
  @ApiQuery({
    name: 'riskLevel',
    required: false,
    description: 'Filter by risk level',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter by status',
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    description: 'Start date filter',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    description: 'End date filter',
  })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page' })
  async findAllAnalyses(
    @Query() searchDto: SearchAnalysesDto,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Finding analyses for org: ${organizationId}`);
    return this.contractPlaybookService.findAllAnalyses(
      organizationId,
      searchDto,
    );
  }

  @Get('analyses/:id')
  @FreeFeature() // Viewing specific analysis is free
  @ApiOperation({ summary: 'Get a specific contract analysis' })
  @ApiResponse({ status: 200, description: 'Analysis retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Analysis not found' })
  @ApiParam({ name: 'id', description: 'Analysis ID' })
  async findAnalysisById(
    @Param('id') id: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Finding analysis: ${id} for org: ${organizationId}`);
    return this.contractPlaybookService.findAnalysisById(id, organizationId);
  }

  @Delete('analyses/:id')
  @FreeFeature() // Deleting analysis is free
  @ApiOperation({ summary: 'Delete a contract analysis' })
  @ApiResponse({ status: 204, description: 'Analysis deleted successfully' })
  @ApiResponse({ status: 404, description: 'Analysis not found' })
  @ApiParam({ name: 'id', description: 'Analysis ID' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteAnalysis(
    @Param('id') id: string,
    @Organization() organizationId: string,
    @User('id') userId: string,
  ) {
    this.logger.log(`Deleting analysis: ${id} for org: ${organizationId}`);
    await this.contractPlaybookService.deleteAnalysis(
      id,
      organizationId,
      userId,
    );
  }

  @Post('import')
  @FreeFeature() // Importing playbooks is free
  @ApiOperation({ summary: 'Import a contract playbook' })
  @ApiResponse({ status: 201, description: 'Playbook imported successfully' })
  @ApiResponse({ status: 400, description: 'Invalid playbook data' })
  async importPlaybook(
    @Body() playbookData: any,
    @Organization() organizationId: string,
    @User('id') userId: string,
  ) {
    this.logger.log(`Importing playbook for org: ${organizationId}`);
    return this.contractPlaybookService.importPlaybook(
      playbookData,
      organizationId,
      userId,
    );
  }

  // Playbook Management Endpoints (with :id parameters)

  @Get(':id')
  @FreeFeature() // Viewing specific playbook is free
  @ApiOperation({ summary: 'Get a specific contract playbook' })
  @ApiResponse({ status: 200, description: 'Playbook retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Playbook not found' })
  @ApiParam({ name: 'id', description: 'Playbook ID' })
  async findPlaybookById(
    @Param('id') id: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Finding playbook: ${id} for org: ${organizationId}`);
    return this.contractPlaybookService.findPlaybookById(id, organizationId);
  }

  @Put(':id')
  @FreeFeature() // Updating playbooks is free
  @ApiOperation({ summary: 'Update a contract playbook' })
  @ApiResponse({ status: 200, description: 'Playbook updated successfully' })
  @ApiResponse({ status: 404, description: 'Playbook not found' })
  @ApiResponse({ status: 409, description: 'Playbook name conflict' })
  @ApiParam({ name: 'id', description: 'Playbook ID' })
  async updatePlaybook(
    @Param('id') id: string,
    @Body() updateDto: UpdateContractPlaybookDto,
    @Organization() organizationId: string,
    @User('id') userId: string,
  ) {
    this.logger.log(`Updating playbook: ${id} for org: ${organizationId}`);
    return this.contractPlaybookService.updatePlaybook(
      id,
      updateDto,
      organizationId,
      userId,
    );
  }

  @Delete(':id')
  @FreeFeature() // Deleting playbooks is free
  @ApiOperation({ summary: 'Delete a contract playbook' })
  @ApiResponse({ status: 204, description: 'Playbook deleted successfully' })
  @ApiResponse({ status: 404, description: 'Playbook not found' })
  @ApiResponse({ status: 400, description: 'Cannot delete playbook in use' })
  @ApiParam({ name: 'id', description: 'Playbook ID' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deletePlaybook(
    @Param('id') id: string,
    @Organization() organizationId: string,
    @User('id') userId: string,
  ) {
    this.logger.log(`Deleting playbook: ${id} for org: ${organizationId}`);
    await this.contractPlaybookService.deletePlaybook(
      id,
      organizationId,
      userId,
    );
  }

  // Analytics and Reporting Endpoints

  @Get(':id/analytics')
  @FreeFeature() // Viewing analytics is free
  @ApiOperation({ summary: 'Get analytics for a specific playbook' })
  @ApiResponse({ status: 200, description: 'Analytics retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Playbook not found' })
  @ApiParam({ name: 'id', description: 'Playbook ID' })
  async getPlaybookAnalytics(
    @Param('id') id: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Getting analytics for playbook: ${id}`);
    return this.contractPlaybookService.getPlaybookAnalytics(
      id,
      organizationId,
    );
  }

  // Utility Endpoints

  @Post(':id/duplicate')
  @FreeFeature() // Duplicating playbooks is free
  @ApiOperation({ summary: 'Duplicate a contract playbook' })
  @ApiResponse({ status: 201, description: 'Playbook duplicated successfully' })
  @ApiResponse({ status: 404, description: 'Playbook not found' })
  @ApiParam({ name: 'id', description: 'Playbook ID to duplicate' })
  async duplicatePlaybook(
    @Param('id') id: string,
    @Body('name') newName: string,
    @Organization() organizationId: string,
    @User('id') userId: string,
  ) {
    this.logger.log(`Duplicating playbook: ${id} for org: ${organizationId}`);
    return this.contractPlaybookService.duplicatePlaybook(
      id,
      organizationId,
      userId,
      newName,
    );
  }

  @Get(':id/export')
  @FreeFeature() // Exporting playbooks is free
  @ApiOperation({ summary: 'Export a contract playbook' })
  @ApiResponse({ status: 200, description: 'Playbook exported successfully' })
  @ApiResponse({ status: 404, description: 'Playbook not found' })
  @ApiParam({ name: 'id', description: 'Playbook ID to export' })
  async exportPlaybook(
    @Param('id') id: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Exporting playbook: ${id} for org: ${organizationId}`);
    return this.contractPlaybookService.exportPlaybook(id, organizationId);
  }
}

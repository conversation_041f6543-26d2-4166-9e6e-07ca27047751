import {
  Controller,
  Get,
  Post,
  Param,
  Query,
  UseGuards,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { NegotiationPlaybookService } from '../services/negotiation-playbook.service';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { NegotiationPlaybook } from '../interfaces/negotiation-playbook.interface';
import {
  SamplePlaybookResponseDto,
  PlaybookStatsDto
} from '../dto/sample-playbook.dto';

@ApiTags('Sample Negotiation Playbooks')
@Controller('sample-playbooks')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class SamplePlaybooksController {
  private readonly logger = new Logger(SamplePlaybooksController.name);

  constructor(
    private readonly negotiationPlaybookService: NegotiationPlaybookService,
    private readonly tenantContext: TenantContextService,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Get all sample negotiation playbooks',
    description: 'Retrieve sample playbooks with optional filtering by contract type, industry, or difficulty',
  })
  @ApiQuery({
    name: 'contractType',
    required: false,
    enum: ['service_agreement', 'employment_contract', 'nda', 'software_license', 'consulting_agreement', 'partnership_agreement', 'real_estate', 'vendor_agreement'],
    description: 'Filter by contract type',
  })
  @ApiQuery({
    name: 'industry',
    required: false,
    enum: ['technology', 'healthcare', 'finance', 'real_estate', 'consulting', 'manufacturing', 'retail', 'general'],
    description: 'Filter by industry',
  })
  @ApiQuery({
    name: 'difficulty',
    required: false,
    enum: ['beginner', 'intermediate', 'expert'],
    description: 'Filter by difficulty level',
  })
  @ApiQuery({
    name: 'tags',
    required: false,
    type: [String],
    description: 'Filter by tags (comma-separated)',
  })
  @ApiResponse({
    status: 200,
    description: 'Sample playbooks retrieved successfully',
    type: [SamplePlaybookResponseDto],
  })
  getSamplePlaybooks(
    @Query('contractType') contractType?: string,
    @Query('industry') industry?: string,
    @Query('difficulty') difficulty?: string,
    @Query('tags') tags?: string,
  ): NegotiationPlaybook[] {
    this.logger.log('Retrieving sample negotiation playbooks with filters', {
      contractType,
      industry,
      difficulty,
      tags,
    });

    const filters: any = {};
    if (contractType) filters.contractType = contractType;
    if (industry) filters.industry = industry;
    if (difficulty) filters.difficulty = difficulty;
    if (tags) filters.tags = tags.split(',').map(tag => tag.trim());

    return this.negotiationPlaybookService.getSamplePlaybooks(filters);
  }

  @Get(':playbookId')
  @ApiOperation({
    summary: 'Get a specific sample playbook',
    description: 'Retrieve detailed information about a specific sample playbook',
  })
  @ApiParam({
    name: 'playbookId',
    description: 'The ID of the sample playbook',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Sample playbook retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Sample playbook not found',
  })
  getSamplePlaybook(
    @Param('playbookId') playbookId: string,
  ): NegotiationPlaybook {
    this.logger.log(`Retrieving sample playbook: ${playbookId}`);

    const playbook = this.negotiationPlaybookService.getSamplePlaybookById(playbookId);
    if (!playbook) {
      throw new NotFoundException(`Sample playbook with ID ${playbookId} not found`);
    }

    return playbook;
  }

  @Post(':playbookId/clone/:documentId')
  @ApiOperation({
    summary: 'Clone a sample playbook for a specific document',
    description: 'Create a copy of a sample playbook customized for a specific document',
  })
  @ApiParam({
    name: 'playbookId',
    description: 'The ID of the sample playbook to clone',
    type: String,
  })
  @ApiParam({
    name: 'documentId',
    description: 'The ID of the document to apply the playbook to',
    type: String,
  })
  @ApiResponse({
    status: 201,
    description: 'Sample playbook cloned successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Sample playbook not found',
  })
  async cloneSamplePlaybook(
    @Param('playbookId') playbookId: string,
    @Param('documentId') documentId: string,
  ): Promise<NegotiationPlaybook> {
    const userId = this.tenantContext.getCurrentUserId();
    const organizationId = this.tenantContext.getCurrentOrganization();

    this.logger.log(`Cloning sample playbook ${playbookId} for document ${documentId}`);

    return this.negotiationPlaybookService.cloneSamplePlaybook(
      playbookId,
      documentId,
      organizationId,
      userId,
    );
  }

  @Get('stats/overview')
  @ApiOperation({
    summary: 'Get playbook statistics',
    description: 'Retrieve statistics about sample playbooks usage and distribution',
  })
  @ApiResponse({
    status: 200,
    description: 'Statistics retrieved successfully',
    type: PlaybookStatsDto,
  })
  async getPlaybookStats(): Promise<PlaybookStatsDto> {
    this.logger.log('Retrieving playbook statistics');
    return this.negotiationPlaybookService.getPlaybookStats();
  }


}

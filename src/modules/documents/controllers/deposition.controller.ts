import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  UseGuards,
  Logger,
  NotFoundException,
  Request,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { SkipDocumentAccessGuard } from '../../auth/decorators/skip-document-access-guard.decorator';
import { FeatureAvailabilityGuard } from '../../subscription/guards/feature-availability.guard';
import { RequireFeatures } from '../../subscription/decorators/require-features.decorator';
import { DepositionService } from '../services/deposition.service';
import {
  CreateDepositionPreparationDto,
  UpdateDepositionPreparationDto,
  CreateDepositionQuestionDto,
  UpdateDepositionQuestionDto,
  GenerateQuestionsDto,
} from '../dto/deposition-preparation.dto';
import { AnalyzeDepositionDto } from '../dto/deposition-analysis.dto';
import { DepositionPreparation, DepositionQuestion, QuestionGenerationResult, DepositionAnalysisResult } from '../interfaces/deposition.interface';
import { UseCredits, FreeFeature } from '../../subscription/decorators/use-credits.decorator';

@Controller('depositions')
@ApiTags('depositions')
@UseGuards(JwtAuthGuard, FeatureAvailabilityGuard)
@RequireFeatures('deposition_preparation')
@SkipDocumentAccessGuard()
export class DepositionController {
  private readonly logger = new Logger(DepositionController.name);

  constructor(private readonly depositionService: DepositionService) {}

  @Post()
  @FreeFeature() // Creating deposition preparations is free
  async createDepositionPreparation(
    @Body() dto: CreateDepositionPreparationDto,
    @Request() req,
  ): Promise<DepositionPreparation> {
    this.logger.debug('Creating deposition preparation');
    return this.depositionService.createDepositionPreparation(dto);
  }

  @Get()
  @FreeFeature() // Viewing deposition preparations is free
  @SkipDocumentAccessGuard()
  async listDepositionPreparations(): Promise<DepositionPreparation[]> {
    this.logger.debug('Listing deposition preparations');
    return this.depositionService.listDepositionPreparations();
  }

  @Get(':id')
  async getDepositionPreparation(
    @Param('id') id: string
  ): Promise<DepositionPreparation> {
    this.logger.debug(`Getting deposition preparation: ${id}`);
    return this.depositionService.getDepositionPreparation(id);
  }

  @Put(':id')
  async updateDepositionPreparation(
    @Param('id') id: string,
    @Body() dto: UpdateDepositionPreparationDto,
  ): Promise<DepositionPreparation> {
    this.logger.debug(`Updating deposition preparation: ${id}`);
    return this.depositionService.updateDepositionPreparation(id, dto);
  }

  @Delete(':id')
  async deleteDepositionPreparation(@Param('id') id: string): Promise<void> {
    this.logger.debug(`Deleting deposition preparation: ${id}`);
    return this.depositionService.deleteDepositionPreparation(id);
  }

  @Post(':id/questions')
  async addQuestion(
    @Param('id') id: string,
    @Body() dto: CreateDepositionQuestionDto,
  ): Promise<DepositionQuestion> {
    this.logger.debug(`Adding question to deposition preparation: ${id}`);
    return this.depositionService.addQuestion(id, dto);
  }

  @Put(':id/questions/:questionId')
  async updateQuestion(
    @Param('id') id: string,
    @Param('questionId') questionId: string,
    @Body() dto: UpdateDepositionQuestionDto,
  ): Promise<DepositionQuestion> {
    this.logger.debug(`Updating question ${questionId} in deposition preparation: ${id}`);
    return this.depositionService.updateQuestion(id, questionId, dto);
  }

  @Delete(':id/questions/:questionId')
  async deleteQuestion(
    @Param('id') id: string,
    @Param('questionId') questionId: string,
  ): Promise<void> {
    this.logger.debug(`Deleting question ${questionId} from deposition preparation: ${id}`);
    return this.depositionService.deleteQuestion(id, questionId);
  }

  @Post('generate-questions')
  @UseCredits('ai_question_generation') // AI question generation consumes credits
  @RequireFeatures('ai_question_generation')
  async generateQuestions(
    @Body() dto: GenerateQuestionsDto,
    @Request() req,
  ): Promise<QuestionGenerationResult> {
    this.logger.debug('Generating questions');
    return this.depositionService.generateQuestions({
      caseContext: dto.caseContext,
      keyIssues: dto.keyIssues,
      targetWitnesses: dto.targetWitnesses,
      userId: req.user.userId,
      organizationId: req.user.organizationId,
      options: {
        questionCount: dto.questionCount,
        includeFollowUps: dto.includeFollowUps,
        questionCategories: dto.questionCategories,
      }
    });
  }

  @Post(':id/generate-questions')
  @UseCredits('ai_question_generation') // AI question generation consumes credits
  @RequireFeatures('ai_question_generation')
  async generateQuestionsForDeposition(
    @Param('id') id: string,
    @Body() dto: GenerateQuestionsDto,
    @Request() req,
  ): Promise<QuestionGenerationResult> {
    this.logger.debug(`Generating questions for deposition: ${id}`);
    
    // Get the deposition preparation
    const preparation = await this.depositionService.getDepositionPreparation(id);
    
    // Create a combined DTO with deposition context
    const combinedDto = {
      ...dto,
      caseContext: dto.caseContext || preparation.caseContext,
      keyIssues: dto.keyIssues || preparation.keyIssues,
      targetWitnesses: dto.targetWitnesses || preparation.targetWitnesses,
      depositionPreparationId: id,
    };
    
    // Generate questions
    const result = await this.depositionService.generateQuestions({
      caseContext: combinedDto.caseContext,
      keyIssues: combinedDto.keyIssues,
      targetWitnesses: combinedDto.targetWitnesses,
      userId: req.user.userId,
      organizationId: req.user.organizationId,
      depositionId: combinedDto.depositionPreparationId,
      options: {
        questionCount: combinedDto.questionCount,
        includeFollowUps: combinedDto.includeFollowUps,
        questionCategories: combinedDto.questionCategories,
      }
    });

    // Save the generated questions to the deposition preparation
    const savedQuestions: DepositionQuestion[] = [];
    for (const question of result.questions) {
      try {
        const questionDto: CreateDepositionQuestionDto = {
          text: question.text,
          category: question.category,
          purpose: question.purpose,
          targetWitness: question.targetWitness || 'Witness',
          suggestedFollowUps: question.suggestedFollowUps || [],
          relatedDocuments: question.relatedDocuments || [],
          priority: question.priority || 'medium',
          notes: question.notes || '',
          isFollowUp: question.isFollowUp !== undefined ? question.isFollowUp : false,
          metadata: {
            ...(question.metadata || {}),
            aiGenerated: true,
            generationDate: new Date().toISOString(),
            modelUsed: 'gpt-4',
          },
        };

        const savedQuestion = await this.depositionService.addQuestion(id, questionDto);
        savedQuestions.push(savedQuestion);
      } catch (error) {
        this.logger.error(
          `Error saving question: ${question.text}`,
          error.stack,
        );
      }
    }

    return result;
  }
  
  @Post('analyze-transcript')
  @UseCredits('deposition_analysis') // AI transcript analysis consumes credits
  @RequireFeatures('deposition_analysis')
  async analyzeDeposition(@Body() dto: AnalyzeDepositionDto, @Request() req): Promise<DepositionAnalysisResult> {
    this.logger.debug('Analyzing deposition transcript');
    return this.depositionService.analyzeDeposition(dto, {
      userId: req.user.userId,
      // Generate a UUID for caseId if needed
      caseId: undefined,
    });
  }

  @Get(':id/analyses')
  @ApiOperation({ summary: 'Get all analyses for a deposition' })
  @ApiResponse({ status: 200, description: 'The deposition analyses' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getDepositionAnalysis(
    @Param('id') depositionId: string,
  ): Promise<DepositionAnalysisResult[]> {
    this.logger.debug(`Getting analyses for deposition: ${depositionId}`);
    return this.depositionService.getDepositionAnalysis(depositionId);
  }
}

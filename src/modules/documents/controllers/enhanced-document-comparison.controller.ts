import { 
  Controller, 
  Post, 
  Body, 
  Get, 
  Param, 
  HttpStatus, 
  HttpException,
  Logger,
  Request,
  ValidationPipe,
  UseGuards,
  Query,
  Res
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiBadRequestResponse, ApiTags, ApiQuery } from '@nestjs/swagger';
import { EnhancedDocumentComparisonService } from '../services/enhanced-document-comparison.service';
import { DocumentProcessingService } from '../services/document-processing.service';
import { UseCredits, FreeFeature } from '../../subscription/decorators/use-credits.decorator';
import { 
  EnhancedCompareDocumentsDto, 
  VersionComparisonDto,
  SectionReferenceDto,
  MultipleSectionReferencesDto,
  CreateDocumentVersionDto,
  EnhancedComparisonType,
  ExportComparisonDto
} from '../dto/enhanced-comparison.dto';
import { Organization } from '../../auth/decorators/organization.decorator';
import { Public } from '../../auth/decorators/public.decorator';
import { 
  EnhancedComparisonResult, 
  VersionComparisonResult,
  ExportOptions
} from '../interfaces/document-comparison.interface';
import { RequireFeatures } from '../../subscription/decorators/require-features.decorator';
import { FeatureAvailabilityGuard } from '../../subscription/guards/feature-availability.guard';
import { Response } from 'express';

@ApiTags('enhanced-document-comparison')
@Controller('enhanced-comparison')
@UseGuards(FeatureAvailabilityGuard)
export class EnhancedDocumentComparisonController {
  private readonly logger = new Logger(EnhancedDocumentComparisonController.name);

  constructor(
    private readonly comparisonService: EnhancedDocumentComparisonService,
    private readonly documentProcessingService: DocumentProcessingService,
  ) {}

  @Post('documents')
  @UseCredits('enhanced_comparison') // Enhanced document comparison consumes credits
  @RequireFeatures('enhanced_comparison')
  @ApiOperation({ summary: 'Compare two documents with enhanced analysis' })
  @ApiResponse({ status: 201, description: 'Documents compared successfully' })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  async compareDocuments(
    @Body(new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      errorHttpStatusCode: HttpStatus.BAD_REQUEST
    }))
    request: EnhancedCompareDocumentsDto,
    @Organization() organizationId: string
  ) {
    try {
      this.logger.debug('Processing enhanced comparison request', {
        documentALength: request.documentA?.length,
        documentBLength: request.documentB?.length,
        type: request.type,
      });

      const result = await this.comparisonService.enhancedCompare(
        request.documentA,
        request.documentB,
        {
          comparisonType: request.type as 'similarities' | 'differences' | 'both',
          includeVisualDiff: request.includeVisualDiff,
          includeSectionAnalysis: request.includeSectionReferences
        },
        request.documentAMetadata,
        request.documentBMetadata
      );

      return {
        status: 'success',
        data: result,
        metadata: {
          organizationId,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      this.logger.error(`Error in enhanced document comparison: ${error.message}`, error.stack);
      
      // If the error is already an HTTP exception, rethrow it
      if (error instanceof HttpException) {
        throw error;
      }

      // Otherwise, convert to an internal server error
      throw new HttpException({
        status: 'error',
        message: 'Error in enhanced document comparison',
        details: error.message || 'An unexpected error occurred'
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @Post('version-comparison')
  @UseCredits('enhanced_comparison') // Version comparison consumes credits
  @RequireFeatures('enhanced_comparison')
  @ApiOperation({ summary: 'Compare two versions of the same document' })
  @ApiResponse({
    status: 200,
    description: 'Document versions compared successfully',
  })
  async compareDocumentVersions(
    @Body(new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      errorHttpStatusCode: HttpStatus.BAD_REQUEST
    }))
    request: VersionComparisonDto,
    @Organization() organizationId: string
  ): Promise<any> {
    try {
      this.logger.debug('Processing version comparison request', {
        documentId: request.documentId,
        baseVersion: request.baseVersionNumber,
        comparedVersion: request.comparedVersionNumber
      });

      const result = await this.comparisonService.compareVersions(
        request.documentId,
        request.baseVersionNumber,
        request.comparedVersionNumber
      );

      return result;
    } catch (error) {
      this.logger.error(`Error comparing document versions: ${error.message}`, error.stack);
      throw new HttpException(
        `Failed to compare document versions: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('version-comparison/simple')
  @UseCredits('basic_comparison') // Simple comparison still consumes credits
  @Public()
  @ApiOperation({ summary: 'Simple version comparison without authentication' })
  async compareDocumentVersionsSimple(
    @Body() versionComparisonDto: VersionComparisonDto,
  ): Promise<VersionComparisonResult> {
    try {
      this.logger.debug('Processing simple version comparison request', {
        documentId: versionComparisonDto.documentId,
        baseVersion: versionComparisonDto.baseVersionNumber,
        comparedVersion: versionComparisonDto.comparedVersionNumber
      });

      const result = await this.comparisonService.compareVersions(
        versionComparisonDto.documentId,
        versionComparisonDto.baseVersionNumber,
        versionComparisonDto.comparedVersionNumber
      );

      return result;
    } catch (error) {
      this.logger.error(`Error in simple version comparison: ${error.message}`, error.stack);
      throw new HttpException(
        `Failed to compare document versions: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('section-references')
  @UseCredits('enhanced_comparison') // Section reference analysis consumes credits
  @RequireFeatures('enhanced_comparison')
  @ApiOperation({ summary: 'Get section references from multiple documents' })
  @ApiResponse({ status: 200, description: 'Section references retrieved successfully' })
  async getSectionReferences(
    @Body(new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      errorHttpStatusCode: HttpStatus.BAD_REQUEST
    }))
    request: MultipleSectionReferencesDto,
    @Organization() organizationId: string
  ) {
    try {
      this.logger.debug('Processing section references request', {
        numReferences: request.sectionReferences.length
      });

      // Implementation would go here

      return {
        status: 'success',
        message: 'Section references retrieved successfully',
        data: []
      };
    } catch (error) {
      this.logger.error(`Error getting section references: ${error.message}`, error.stack);
      throw new HttpException(
        `Failed to get section references: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('document-version')
  @FreeFeature() // Creating document versions is free
  @RequireFeatures('enhanced_comparison')
  @ApiOperation({ summary: 'Create a new version of a document' })
  @ApiResponse({ status: 201, description: 'Document version created successfully' })
  async createDocumentVersion(
    @Body() createVersionDto: CreateDocumentVersionDto,
    @Request() req,
  ): Promise<any> {
    try {
      this.logger.debug('Creating document version', {
        documentId: createVersionDto.documentId
      });

      const userId = req.user?.id || 'anonymous';

      const result = await this.documentProcessingService.createDocumentVersion(
        createVersionDto.documentId,
        createVersionDto.content,
        userId,
        createVersionDto.metadata
      );

      return {
        status: 'success',
        message: 'Document version created successfully',
        data: {
          documentId: createVersionDto.documentId,
          version: result
        }
      };
    } catch (error) {
      this.logger.error(`Error creating document version: ${error.message}`, error.stack);
      throw new HttpException(
        `Failed to create document version: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('document-versions/:documentId')
  @FreeFeature() // Viewing document versions is free
  @RequireFeatures('enhanced_comparison')
  @ApiOperation({ summary: 'Get all versions of a document' })
  @ApiResponse({ status: 200, description: 'Document versions retrieved successfully' })
  async getDocumentVersions(@Param('documentId') documentId: string): Promise<any> {
    try {
      this.logger.debug('Getting document versions', { documentId });

      // Get the document to access its versions
      const document = await this.documentProcessingService.getDocumentById(documentId);
      
      if (!document) {
        throw new HttpException(
          `Document with id ${documentId} not found`,
          HttpStatus.NOT_FOUND
        );
      }
      
      // Get versions from document metadata or return empty array
      const versions = document.metadata?.versions || [];
      
      return {
        status: 'success',
        message: 'Document versions retrieved successfully',
        data: versions
      };
    } catch (error) {
      this.logger.error(`Error getting document versions: ${error.message}`, error.stack);
      throw new HttpException(
        `Failed to get document versions: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Export a comparison result
   */
  @Post('export/:comparisonId')
  @FreeFeature() // Exporting comparison results is free
  @RequireFeatures('enhanced_comparison')
  @ApiOperation({ summary: 'Export a comparison result in various formats' })
  @ApiResponse({ status: 200, description: 'Comparison exported successfully' })
  @ApiBadRequestResponse({ description: 'Invalid input data or comparison not found' })
  async exportComparison(
    @Param('comparisonId') comparisonId: string,
    @Body(new ValidationPipe()) exportOptions: ExportComparisonDto,
    @Organization() organizationId: string,
    @Res() res: Response
  ) {
    try {
      this.logger.log(`Exporting comparison ${comparisonId} in ${exportOptions.format} format`);
      
      // Retrieve the comparison result from storage or cache
      // For now, we'll use a simple mock comparison result
      const comparisonResult = await this.retrieveComparisonResult(comparisonId, organizationId);
      
      if (!comparisonResult) {
        throw new HttpException('Comparison result not found', HttpStatus.NOT_FOUND);
      }
      
      // Export the comparison
      const exportedData = await this.comparisonService.exportComparison(
        comparisonResult,
        exportOptions as ExportOptions
      );
      
      // Set appropriate headers based on the format
      switch (exportOptions.format) {
        case 'pdf':
          res.setHeader('Content-Type', 'application/pdf');
          res.setHeader('Content-Disposition', `attachment; filename="comparison-${comparisonId}.pdf"`);
          break;
        case 'html':
          res.setHeader('Content-Type', 'text/html');
          break;
        case 'docx':
          res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
          res.setHeader('Content-Disposition', `attachment; filename="comparison-${comparisonId}.docx"`);
          break;
      }
      
      // Send the response
      return res.send(exportedData);
    } catch (error) {
      this.logger.error(`Error exporting comparison: ${error.message}`, error.stack);
      throw new HttpException(
        `Error exporting comparison: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Generate an executive summary of a comparison
   */
  @Get('summary/:comparisonId')
  @UseCredits('enhanced_comparison') // AI summary generation consumes credits
  @RequireFeatures('enhanced_comparison')
  @ApiOperation({ summary: 'Generate an executive summary of a comparison' })
  @ApiResponse({ status: 200, description: 'Summary generated successfully' })
  @ApiBadRequestResponse({ description: 'Invalid input data or comparison not found' })
  async generateSummary(
    @Param('comparisonId') comparisonId: string,
    @Organization() organizationId: string
  ) {
    try {
      this.logger.log(`Generating summary for comparison ${comparisonId}`);
      
      // Retrieve the comparison result
      const comparisonResult = await this.retrieveComparisonResult(comparisonId, organizationId);
      
      if (!comparisonResult) {
        throw new HttpException('Comparison result not found', HttpStatus.NOT_FOUND);
      }
      
      // Generate the summary
      const summary = await this.comparisonService.generateExecutiveSummary(comparisonResult);
      
      return { summary };
    } catch (error) {
      this.logger.error(`Error generating summary: ${error.message}`, error.stack);
      throw new HttpException(
        `Error generating summary: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Retrieve a comparison result (mock implementation)
   * In a real implementation, this would retrieve the result from a database or cache
   */
  private async retrieveComparisonResult(comparisonId: string, organizationId: string): Promise<any> {
    // This is a mock implementation for testing
    // In a real application, you would retrieve the comparison result from storage
    
    // Create a mock comparison result
    const mockResult = {
      diffs: [
        { type: 'equal', text: 'This is some ' },
        { type: 'delete', text: 'old ' },
        { type: 'insert', text: 'new ' },
        { type: 'equal', text: 'text that has been modified.' }
      ],
      metadata: {
        timestamp: new Date(),
        documentStats: {
          documentA: { length: 100, wordCount: 20 },
          documentB: { length: 105, wordCount: 21 }
        },
        summary: {
          addedLines: 1,
          removedLines: 1,
          modifiedLines: 1,
          totalChanges: 3,
          significantChanges: [
            { 
              type: 'modification', 
              description: 'Changed "old" to "new"', 
              lineRange: { start: 1, end: 1 } 
            }
          ]
        }
      },
      visualization: {
        htmlDiff: '<div>This is some <del>old</del> <ins>new</ins> text that has been modified.</div>'
      }
    };
    
    return mockResult;
  }
}

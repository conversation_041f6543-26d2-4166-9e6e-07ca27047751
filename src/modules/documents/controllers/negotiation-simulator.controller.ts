import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
  HttpStatus,
  HttpCode,
  UnauthorizedException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { UseCredits, FreeFeature } from '../../subscription/decorators/use-credits.decorator';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { NegotiationSimulatorService } from '../services/negotiation-simulator.service';
import {
  CreateNegotiationScenarioDto,
  StartNegotiationSessionDto,
  MakeNegotiationMoveDto,
  NegotiationSimulatorOptionsDto,
} from '../dto/negotiation-simulator.dto';
import {
  NegotiationScenario,
  NegotiationSession,
  SimulationEvaluation,
} from '../interfaces/negotiation-simulator.interface';

@ApiTags('Negotiation Simulator')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('negotiation-simulator')
export class NegotiationSimulatorController {
  private readonly logger = new Logger(NegotiationSimulatorController.name);

  constructor(
    private readonly negotiationSimulatorService: NegotiationSimulatorService,
    private readonly tenantContext: TenantContextService,
  ) {}

  /**
   * Get current user context with proper error handling
   */
  private getCurrentUserContext(): { userId: string; organizationId: string } {
    const userId = this.tenantContext.getCurrentUserId();
    const organizationId = this.tenantContext.getCurrentOrganization();

    if (!userId || !organizationId) {
      throw new UnauthorizedException('User context not found');
    }

    return { userId, organizationId };
  }

  @Post('scenarios')
  @FreeFeature() // Creating scenarios is free
  @ApiOperation({ summary: 'Create a new negotiation scenario' })
  @ApiResponse({
    status: 201,
    description: 'Scenario created successfully',
    type: Object,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid scenario data',
  })
  async createScenario(
    @Body() dto: CreateNegotiationScenarioDto,
  ): Promise<NegotiationScenario> {
    const { userId, organizationId } = this.getCurrentUserContext();

    this.logger.log(
      `Creating negotiation scenario: ${dto.name} for user: ${userId}`,
    );

    return this.negotiationSimulatorService.createScenario(
      dto,
      userId,
      organizationId,
    );
  }

  @Get('scenarios')
  @ApiOperation({ summary: 'Get negotiation scenarios' })
  @ApiResponse({
    status: 200,
    description: 'Scenarios retrieved successfully',
    type: [Object],
  })
  @ApiQuery({
    name: 'industry',
    required: false,
    description: 'Filter by industry',
  })
  @ApiQuery({
    name: 'contractType',
    required: false,
    description: 'Filter by contract type',
  })
  @ApiQuery({
    name: 'difficulty',
    required: false,
    description: 'Filter by difficulty level',
  })
  @ApiQuery({
    name: 'tags',
    required: false,
    description: 'Filter by tags (comma-separated)',
  })
  async getScenarios(
    @Query('industry') industry?: string,
    @Query('contractType') contractType?: string,
    @Query('difficulty') difficulty?: string,
    @Query('tags') tags?: string,
  ): Promise<NegotiationScenario[]> {
    const { organizationId } = this.getCurrentUserContext();

    const filters: any = {};
    if (industry) filters.industry = industry;
    if (contractType) filters.contractType = contractType;
    if (difficulty) filters.difficulty = difficulty;
    if (tags) filters.tags = tags.split(',').map((tag) => tag.trim());

    return this.negotiationSimulatorService.getScenarios(
      organizationId,
      filters,
    );
  }

  @Get('scenarios/templates')
  @ApiOperation({ summary: 'Get template negotiation scenarios' })
  @ApiResponse({
    status: 200,
    description: 'Template scenarios retrieved successfully',
    type: [Object],
  })
  async getTemplateScenarios(): Promise<NegotiationScenario[]> {
    const { organizationId } = this.getCurrentUserContext();

    return this.negotiationSimulatorService.getTemplateScenarios(
      organizationId,
    );
  }

  @Get('scenarios/:scenarioId')
  @ApiOperation({ summary: 'Get a specific negotiation scenario' })
  @ApiResponse({
    status: 200,
    description: 'Scenario retrieved successfully',
    type: Object,
  })
  @ApiResponse({
    status: 404,
    description: 'Scenario not found',
  })
  @ApiParam({ name: 'scenarioId', description: 'Scenario ID' })
  async getScenario(
    @Param('scenarioId') scenarioId: string,
  ): Promise<NegotiationScenario> {
    const { organizationId } = this.getCurrentUserContext();
    return this.negotiationSimulatorService.getScenario(
      scenarioId,
      organizationId,
    );
  }

  @Get('scenarios/from-playbook/:playbookId')
  @ApiOperation({
    summary: 'Get scenarios created from a specific playbook',
    description:
      'Retrieves all negotiation scenarios that were generated from a specific playbook analysis',
  })
  @ApiResponse({
    status: 200,
    description: 'Scenarios from playbook retrieved successfully',
    type: [Object],
  })
  @ApiResponse({
    status: 404,
    description: 'Playbook not found',
  })
  @ApiParam({ name: 'playbookId', description: 'Playbook ID' })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
  })
  async getScenariosFromPlaybook(
    @Param('playbookId') playbookId: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ): Promise<{
    scenarios: NegotiationScenario[];
    total: number;
    page: number;
    limit: number;
  }> {
    const { organizationId } = this.getCurrentUserContext();

    this.logger.log(`Getting scenarios from playbook: ${playbookId}`);

    // For now, filter scenarios by tags that include the playbookId
    // This is a simplified implementation until proper playbook linking is added
    const allScenarios = await this.negotiationSimulatorService.getScenarios(
      organizationId,
      { tags: [`playbook-${playbookId}`, 'playbook-generated'] },
    );

    const pageNum = page || 1;
    const limitNum = Math.min(limit || 20, 100);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;

    const paginatedScenarios = allScenarios.slice(startIndex, endIndex);

    return {
      scenarios: paginatedScenarios,
      total: allScenarios.length,
      page: pageNum,
      limit: limitNum,
    };
  }

  @Put('scenarios/:scenarioId')
  @ApiOperation({ summary: 'Update a negotiation scenario' })
  @ApiResponse({
    status: 200,
    description: 'Scenario updated successfully',
    type: Object,
  })
  @ApiResponse({
    status: 404,
    description: 'Scenario not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Access denied - only scenario creator can update',
  })
  @ApiParam({ name: 'scenarioId', description: 'Scenario ID' })
  async updateScenario(
    @Param('scenarioId') scenarioId: string,
    @Body() dto: Partial<CreateNegotiationScenarioDto>,
  ): Promise<NegotiationScenario> {
    const { userId, organizationId } = this.getCurrentUserContext();

    this.logger.log(
      `Updating negotiation scenario: ${scenarioId} by user: ${userId}`,
    );

    return this.negotiationSimulatorService.updateScenario(
      scenarioId,
      dto,
      userId,
      organizationId,
    );
  }

  @Post('scenarios/:scenarioId/clone')
  @ApiOperation({ summary: 'Clone a negotiation scenario' })
  @ApiResponse({
    status: 201,
    description: 'Scenario cloned successfully',
    type: Object,
  })
  @ApiResponse({
    status: 404,
    description: 'Scenario not found',
  })
  @ApiParam({ name: 'scenarioId', description: 'Scenario ID to clone' })
  async cloneScenario(
    @Param('scenarioId') scenarioId: string,
    @Body() dto?: { name?: string; description?: string },
  ): Promise<NegotiationScenario> {
    const { userId, organizationId } = this.getCurrentUserContext();

    this.logger.log(
      `Cloning negotiation scenario: ${scenarioId} by user: ${userId}`,
    );

    return this.negotiationSimulatorService.cloneScenario(
      scenarioId,
      userId,
      organizationId,
      dto,
    );
  }

  @Delete('scenarios/:scenarioId')
  @ApiOperation({ summary: 'Delete a negotiation scenario' })
  @ApiResponse({
    status: 200,
    description: 'Scenario deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        deletedScenarioId: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Scenario not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Access denied - only scenario creator can delete',
  })
  @ApiParam({ name: 'scenarioId', description: 'Scenario ID to delete' })
  @HttpCode(HttpStatus.OK)
  async deleteScenario(
    @Param('scenarioId') scenarioId: string,
  ): Promise<{ message: string; deletedScenarioId: string }> {
    const { userId, organizationId } = this.getCurrentUserContext();

    this.logger.log(
      `Deleting negotiation scenario: ${scenarioId} by user: ${userId}`,
    );

    await this.negotiationSimulatorService.deleteScenario(
      scenarioId,
      userId,
      organizationId,
    );

    return {
      message: 'Scenario deleted successfully',
      deletedScenarioId: scenarioId,
    };
  }

  @Post('sessions')
  @UseCredits('negotiation_simulator') // AI negotiation simulation consumes credits
  @ApiOperation({ summary: 'Start a new negotiation session' })
  @ApiResponse({
    status: 201,
    description: 'Session started successfully',
    type: Object,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid session data',
  })
  @ApiResponse({
    status: 404,
    description: 'Scenario not found',
  })
  async startSession(
    @Body() dto: StartNegotiationSessionDto,
    @Query() options?: NegotiationSimulatorOptionsDto,
  ): Promise<NegotiationSession> {
    const { userId, organizationId } = this.getCurrentUserContext();

    this.logger.log(
      `Starting negotiation session for scenario: ${dto.scenarioId} by user: ${userId}`,
    );

    return this.negotiationSimulatorService.startSession(
      dto,
      userId,
      organizationId,
      options,
    );
  }

  @Post('sessions/:sessionId/moves')
  @UseCredits('negotiation_simulator') // AI negotiation moves consume credits
  @ApiOperation({ summary: 'Make a negotiation move' })
  @ApiResponse({
    status: 200,
    description: 'Move processed successfully',
    type: Object,
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid move data or session not active',
  })
  @ApiResponse({
    status: 404,
    description: 'Session not found',
  })
  @ApiParam({ name: 'sessionId', description: 'Session ID' })
  async makeMove(
    @Param('sessionId') sessionId: string,
    @Body() moveDto: Omit<MakeNegotiationMoveDto, 'sessionId'>,
  ): Promise<{
    session: NegotiationSession;
    aiResponse: any;
  }> {
    const { userId } = this.getCurrentUserContext();

    this.logger.log(
      `Processing move for session: ${sessionId} by user: ${userId}`,
    );

    const dto: MakeNegotiationMoveDto = {
      sessionId,
      move: moveDto.move,
    };

    return this.negotiationSimulatorService.makeMove(dto, userId);
  }

  @Get('sessions')
  @ApiOperation({ summary: 'Get user negotiation sessions' })
  @ApiResponse({
    status: 200,
    description: 'Sessions retrieved successfully',
    type: [Object],
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter by session status',
  })
  async getUserSessions(
    @Query('status') status?: string,
  ): Promise<NegotiationSession[]> {
    const { userId, organizationId } = this.getCurrentUserContext();

    return this.negotiationSimulatorService.getUserSessions(
      userId,
      organizationId,
      status,
    );
  }

  @Get('sessions/:sessionId')
  @ApiOperation({ summary: 'Get a specific negotiation session' })
  @ApiResponse({
    status: 200,
    description: 'Session retrieved successfully',
    type: Object,
  })
  @ApiResponse({
    status: 404,
    description: 'Session not found',
  })
  @ApiParam({ name: 'sessionId', description: 'Session ID' })
  async getSession(
    @Param('sessionId') sessionId: string,
  ): Promise<NegotiationSession> {
    const { userId } = this.getCurrentUserContext();
    return this.negotiationSimulatorService.getSession(sessionId, userId);
  }

  @Put('sessions/:sessionId/pause')
  @ApiOperation({ summary: 'Pause a negotiation session' })
  @ApiResponse({
    status: 200,
    description: 'Session paused successfully',
    type: Object,
  })
  @ApiResponse({
    status: 404,
    description: 'Active session not found',
  })
  @ApiParam({ name: 'sessionId', description: 'Session ID' })
  @HttpCode(HttpStatus.OK)
  async pauseSession(
    @Param('sessionId') sessionId: string,
  ): Promise<NegotiationSession> {
    const { userId } = this.getCurrentUserContext();

    this.logger.log(`Pausing session: ${sessionId} for user: ${userId}`);

    return this.negotiationSimulatorService.pauseSession(sessionId, userId);
  }

  @Put('sessions/:sessionId/resume')
  @ApiOperation({ summary: 'Resume a paused negotiation session' })
  @ApiResponse({
    status: 200,
    description: 'Session resumed successfully',
    type: Object,
  })
  @ApiResponse({
    status: 404,
    description: 'Paused session not found',
  })
  @ApiParam({ name: 'sessionId', description: 'Session ID' })
  @HttpCode(HttpStatus.OK)
  async resumeSession(
    @Param('sessionId') sessionId: string,
  ): Promise<NegotiationSession> {
    const { userId } = this.getCurrentUserContext();

    this.logger.log(`Resuming session: ${sessionId} for user: ${userId}`);

    return this.negotiationSimulatorService.resumeSession(sessionId, userId);
  }

  @Put('sessions/:sessionId/abandon')
  @ApiOperation({ summary: 'Abandon a negotiation session' })
  @ApiResponse({
    status: 200,
    description: 'Session abandoned successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Session not found or already completed',
  })
  @ApiParam({ name: 'sessionId', description: 'Session ID' })
  @HttpCode(HttpStatus.OK)
  async abandonSession(
    @Param('sessionId') sessionId: string,
  ): Promise<{ message: string }> {
    const { userId } = this.getCurrentUserContext();

    this.logger.log(`Abandoning session: ${sessionId} for user: ${userId}`);

    await this.negotiationSimulatorService.abandonSession(sessionId, userId);

    return { message: 'Session abandoned successfully' };
  }

  @Post('sessions/:sessionId/evaluate')
  @ApiOperation({ summary: 'Evaluate a completed negotiation session' })
  @ApiResponse({
    status: 200,
    description: 'Session evaluated successfully',
    type: Object,
  })
  @ApiResponse({
    status: 400,
    description: 'Session must be completed for evaluation',
  })
  @ApiResponse({
    status: 404,
    description: 'Session not found',
  })
  @ApiParam({ name: 'sessionId', description: 'Session ID' })
  async evaluateSession(
    @Param('sessionId') sessionId: string,
  ): Promise<SimulationEvaluation> {
    const { userId } = this.getCurrentUserContext();

    this.logger.log(`Evaluating session: ${sessionId} for user: ${userId}`);

    return this.negotiationSimulatorService.evaluateSession(sessionId, userId);
  }

  @Get('analytics/overview')
  @ApiOperation({ summary: 'Get negotiation analytics overview' })
  @ApiResponse({
    status: 200,
    description: 'Analytics retrieved successfully',
    type: Object,
  })
  async getAnalyticsOverview(): Promise<any> {
    const { userId, organizationId } = this.getCurrentUserContext();

    // Get user sessions for analytics
    const sessions = await this.negotiationSimulatorService.getUserSessions(
      userId,
      organizationId,
    );

    // Calculate basic analytics
    const totalSessions = sessions.length;
    const completedSessions = sessions.filter(
      (s) => s.status === 'completed',
    ).length;
    const averageScore =
      completedSessions > 0
        ? sessions
            .filter((s) => s.status === 'completed')
            .reduce((sum, s) => sum + s.metrics.overallScore, 0) /
          completedSessions
        : 0;

    const averageRounds =
      completedSessions > 0
        ? sessions
            .filter((s) => s.status === 'completed')
            .reduce((sum, s) => sum + s.metrics.totalRounds, 0) /
          completedSessions
        : 0;

    const averageDuration =
      completedSessions > 0
        ? sessions
            .filter((s) => s.status === 'completed')
            .reduce((sum, s) => sum + s.metrics.totalTime, 0) /
          completedSessions
        : 0;

    return {
      totalSessions,
      completedSessions,
      completionRate: totalSessions > 0 ? completedSessions / totalSessions : 0,
      averageScore,
      averageRounds,
      averageDuration,
      recentSessions: sessions.slice(0, 5), // Last 5 sessions
    };
  }
}

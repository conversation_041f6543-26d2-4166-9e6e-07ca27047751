import {
  Controller,
  Get,
  Post,
  Param,
  Query,
  UseGuards,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { FeatureAvailabilityGuard } from '../../subscription/guards/feature-availability.guard';
import { RequireFeatures } from '../../subscription/decorators/require-features.decorator';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { SampleContractPlaybooksService } from '../services/sample-contract-playbooks.service';
import { ContractPlaybookService } from '../services/contract-playbook.service';
import { ContractPlaybook } from '../interfaces/contract-playbook.interface';
import { ContractType } from '../schemas/contract-playbook.schema';

@ApiTags('Sample Contract Playbooks')
@Controller('sample-contract-playbooks')
@UseGuards(JwtAuthGuard, FeatureAvailabilityGuard)
@RequireFeatures('contract_playbooks')
export class SampleContractPlaybooksController {
  private readonly logger = new Logger(SampleContractPlaybooksController.name);

  constructor(
    private readonly samplePlaybooksService: SampleContractPlaybooksService,
    private readonly contractPlaybookService: ContractPlaybookService,
    private readonly tenantContext: TenantContextService,
  ) {}

  /**
   * Get current user context with proper error handling
   */
  private getCurrentUserContext(): { userId: string; organizationId: string } {
    const userId = this.tenantContext.getCurrentUserId();
    const organizationId = this.tenantContext.getCurrentOrganization();

    if (!userId || !organizationId) {
      throw new UnauthorizedException('User context not found');
    }

    return { userId, organizationId };
  }

  @Get()
  @ApiOperation({
    summary: 'Get all sample contract playbooks',
    description: 'Retrieve all available sample contract playbooks with optional filtering',
  })
  @ApiQuery({
    name: 'contractType',
    required: false,
    description: 'Filter by contract type',
    enum: ContractType,
  })
  @ApiQuery({
    name: 'industry',
    required: false,
    description: 'Filter by industry',
    type: String,
  })
  @ApiQuery({
    name: 'riskProfile',
    required: false,
    description: 'Filter by risk profile',
    enum: ['Low', 'Medium', 'High', 'Critical'],
  })
  @ApiQuery({
    name: 'tags',
    required: false,
    description: 'Filter by tags (comma-separated)',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Sample contract playbooks retrieved successfully',
    type: [Object],
  })
  async getSamplePlaybooks(
    @Query('contractType') contractType?: ContractType,
    @Query('industry') industry?: string,
    @Query('riskProfile') riskProfile?: string,
    @Query('tags') tags?: string,
  ): Promise<ContractPlaybook[]> {
    this.logger.log('Retrieving sample contract playbooks with filters', {
      contractType,
      industry,
      riskProfile,
      tags,
    });

    const filters: any = {};
    if (contractType) filters.contractType = contractType;
    if (industry) filters.industry = industry;
    if (riskProfile) filters.riskProfile = riskProfile;
    if (tags) filters.tags = tags.split(',').map(tag => tag.trim());

    const playbooks = this.samplePlaybooksService.getFilteredSamplePlaybooks(filters);

    this.logger.log(`Retrieved ${playbooks.length} sample contract playbooks`);
    return playbooks;
  }

  @Get('stats/overview')
  @ApiOperation({
    summary: 'Get sample contract playbooks statistics',
    description: 'Retrieve statistics about available sample contract playbooks',
  })
  @ApiResponse({
    status: 200,
    description: 'Statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalSamples: { type: 'number' },
        contractTypeDistribution: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              contractType: { type: 'string' },
              count: { type: 'number' },
            },
          },
        },
        riskProfileDistribution: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              riskProfile: { type: 'string' },
              count: { type: 'number' },
            },
          },
        },
        industryDistribution: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              industry: { type: 'string' },
              count: { type: 'number' },
            },
          },
        },
      },
    },
  })
  async getStatistics() {
    this.logger.log('Retrieving sample contract playbooks statistics');
    return this.samplePlaybooksService.getStatistics();
  }

  @Get(':playbookId')
  @ApiOperation({
    summary: 'Get a specific sample contract playbook',
    description: 'Retrieve a specific sample contract playbook by ID',
  })
  @ApiParam({
    name: 'playbookId',
    description: 'The ID of the sample contract playbook',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Sample contract playbook retrieved successfully',
    type: Object,
  })
  @ApiResponse({
    status: 404,
    description: 'Sample contract playbook not found',
  })
  async getSamplePlaybook(
    @Param('playbookId') playbookId: string,
  ): Promise<ContractPlaybook> {
    this.logger.log(`Retrieving sample contract playbook: ${playbookId}`);

    const playbook = this.samplePlaybooksService.getSamplePlaybookById(playbookId);

    if (!playbook) {
      throw new NotFoundException(`Sample contract playbook with ID ${playbookId} not found`);
    }

    return playbook;
  }

  @Post(':playbookId/clone')
  @ApiOperation({
    summary: 'Clone a sample contract playbook',
    description: 'Create a copy of a sample contract playbook for your organization',
  })
  @ApiParam({
    name: 'playbookId',
    description: 'The ID of the sample contract playbook to clone',
    type: String,
  })
  @ApiResponse({
    status: 201,
    description: 'Sample contract playbook cloned successfully',
    type: Object,
  })
  @ApiResponse({
    status: 404,
    description: 'Sample contract playbook not found',
  })
  async cloneSamplePlaybook(
    @Param('playbookId') playbookId: string,
  ): Promise<ContractPlaybook> {
    const { userId, organizationId } = this.getCurrentUserContext();

    this.logger.log(`Cloning sample contract playbook ${playbookId} for organization ${organizationId}`);

    const samplePlaybook = this.samplePlaybooksService.getSamplePlaybookById(playbookId);
    if (!samplePlaybook) {
      throw new NotFoundException(`Sample contract playbook with ID ${playbookId} not found`);
    }

    // Create a DTO for the new playbook based on the sample
    const clonedPlaybookDto = {
      name: `${samplePlaybook.name} (Copy)`,
      contractType: samplePlaybook.contractType,
      description: samplePlaybook.description,
      version: samplePlaybook.version,
      rules: samplePlaybook.rules,
      metadata: {
        ...samplePlaybook.metadata,
        clonedFrom: playbookId,
        clonedAt: new Date(),
      },
      isActive: true,
      isTemplate: false, // This is now an organization-specific playbook
    };

    // Save the cloned playbook using the contract playbook service
    const savedPlaybook = await this.contractPlaybookService.createPlaybook(
      clonedPlaybookDto,
      organizationId,
      userId
    );

    this.logger.log(`Successfully cloned sample contract playbook ${playbookId} as ${savedPlaybook.id}`);
    return savedPlaybook;
  }

  @Get('contract-types/available')
  @ApiOperation({
    summary: 'Get available contract types',
    description: 'Retrieve all contract types that have sample playbooks available',
  })
  @ApiResponse({
    status: 200,
    description: 'Available contract types retrieved successfully',
    schema: {
      type: 'array',
      items: { type: 'string' },
    },
  })
  async getAvailableContractTypes(): Promise<ContractType[]> {
    this.logger.log('Retrieving available contract types from sample playbooks');
    return this.samplePlaybooksService.getAvailableContractTypes();
  }
}

import { Controller, Post, Body, UseGuards, Logger, NotFoundException, Param } from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { FeatureAvailabilityGuard } from '../../subscription/guards/feature-availability.guard';
import { RequireFeatures } from '../../subscription/decorators/require-features.decorator';
import { PrecedentService } from '../services/precedent.service';
import { DocumentsService } from '../services/documents.service';
import { AnalyzePrecedentsDto } from '../dto/analyze-precedents.dto';
// Removed import for DepositionService and AnalyzeDepositionDto

@Controller('documents/precedents')
@UseGuards(JwtAuthGuard, FeatureAvailabilityGuard)
@RequireFeatures('precedent_analysis')
export class PrecedentController {
  private readonly logger = new Logger(PrecedentController.name);

  constructor(
    private readonly precedentService: PrecedentService,
    private readonly documentsService: DocumentsService
  ) {}

  @Post(':documentId/analyze')
  async analyzePrecedents(
    @Param('documentId') documentId: string,
    @Body() dto: AnalyzePrecedentsDto
  ) {
    this.logger.debug('PrecedentController: analyzePrecedents method reached.');
    this.logger.debug('Fetching document content for analysis:', { documentId });
    
    // Fetch the document content from the database using the path parameter
    const document = await this.documentsService.findById(documentId);
    if (!document || !document.content) {
      throw new NotFoundException(`Document with ID ${documentId} not found or has no content`);
    }
    const documentContent = document.content;

    this.logger.debug('Analyzing precedents for document with options:', {
      documentId,
      includeRecommendations: dto.includeRecommendations,
      maxRelatedCases: dto.maxRelatedCases,
      minRelevanceScore: dto.minRelevanceScore,
      categorize: dto.categorize,
      assessImpact: dto.assessImpact,
      useAIAnalysis: dto.useAIAnalysis,
      aiFocus: dto.aiFocus
    });
    
    return this.precedentService.analyzePrecedents(documentContent, {
      includeRecommendations: dto.includeRecommendations,
      maxRelatedCases: dto.maxRelatedCases,
      minRelevanceScore: dto.minRelevanceScore,
      categorize: dto.categorize,
      assessImpact: dto.assessImpact,
      useAIAnalysis: dto.useAIAnalysis,
      aiFocus: dto.aiFocus
    });
  }

  // Deposition analysis endpoint has been moved to DepositionController
}

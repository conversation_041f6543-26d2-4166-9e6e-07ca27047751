import {
  Post,
  Get,
  Body,
  Param,
  UseInterceptors,
  UploadedFile,
  BadRequestException,
  UseGuards,
  Req,
  Query,
  Inject,
  forwardRef,
  HttpStatus,
  HttpCode,
  Controller,
  Logger,
  NotFoundException,
  InternalServerErrorException,
  ForbiddenException,
  Delete,
  Res, // Import Response
  StreamableFile, // Import StreamableFile
  Header, // Import Header decorator
} from '@nestjs/common';
import { Response } from 'express'; // Import Response
import { Readable } from 'stream'; // Import Readable for buffer to stream conversion
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiParam,
  ApiQuery,
  ApiOkResponse,
  ApiNotFoundResponse,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { SubscriptionGuard } from '../../subscription/guards/subscription.guard';
import { SubscriptionCheck } from '../../subscription/decorators/subscription-check.decorator';
import { SubscriptionService } from '../../subscription/services/subscription.service';
import { SkipSubscriptionCheck } from '../../subscription/decorators/skip-subscription-check.decorator';
// Removed uuidToObjectId import
import { FileInterceptor } from '@nestjs/platform-express';
import { Express } from 'express';
import { memoryStorage } from 'multer';
import { extname, basename } from 'path';
import { existsSync, mkdirSync } from 'fs';
import { writeFile } from 'fs/promises';
import { DocumentProcessingService } from '../services/document-processing.service';
import { DocumentProcessingMetricsService } from '../services/document-processing-metrics.service';
import { DocumentProcessingCacheService } from '../services/document-processing-cache.service';
import { AIService } from '../../ai/services/ai.service';
import { DocumentType } from '../../../common/enums/document-type.enum';
import { DocumentAnalysisRequestDto } from '../dto/document-analysis-request.dto';
import { MultiDocumentAnalysisRequestDto } from '../dto/multi-document-analysis-request.dto';
import { DocumentSectionComparisonRequestDto } from '../dto/document-section-comparison-request.dto';
import { UploadDocumentDto } from '../dto/upload-document.dto';
import { DocumentProcessingQueueService } from '../services/document-processing-queue.service';
import {
  DocumentProcessingJobRequestDto,
  DocumentProcessingJobResponseDto,
  DocumentJobStatusResponseDto,
} from '../dto/document-processing-job.dto';
import {
  AnalysisComparisonRequestDto,
  AnalysisComparisonResultDto,
  AnalysisEvolutionMetricsDto,
} from '../dto/analysis-comparison.dto';
import { Public } from '../../auth/decorators/public.decorator';
import { DocumentAnalysisTransformer } from '../transformers/document-analysis.transformer';
import { AnalysisResultService } from '../services/analysis-result.service';
import { Types } from 'mongoose'; // Keep Types import
import {
  AnalysisResult,
  AnalysisResultDocument,
} from '../schemas/analysis-result.schema';
import { AIAnalysisResult } from '../../ai/interfaces/ai-analysis-result.interface';
import { PatternRequestDto } from '../dto/pattern-request.dto'; // Import the new DTO
import { LegalPatternRecognitionService } from '../services/legal-pattern-recognition.service'; // Import the service
import { LegalPattern } from '../interfaces/legal-pattern.interface'; // Import the pattern interface
import { LegalPatternDto } from '../dto/legal-pattern.dto'; // Import the DTO
import { LegalResearchOrchestratorService } from '../../legal-research/services/legal-research-orchestrator.service';
import { DocumentClassificationService } from '../services/document-classification.service'; // Import the service
import { DocumentAccessGuard } from '../guards/document-access.guard'; // Import the DocumentAccessGuard
import { TenantContextService } from '../../auth/services/tenant-context.service'; // Import TenantContextService
import { StorageFactoryService } from '../services/storage-factory.service';
import { DocumentStorageService } from '../services/document-storage.service'; // Import DocumentStorageService
import { PostHogService } from '../../posthog/services/posthog.service';


@ApiTags('Documents')
@UseGuards(JwtAuthGuard, DocumentAccessGuard) // Apply DocumentAccessGuard to all endpoints
@Controller('documents')
export class DocumentsController {
  private readonly logger: Logger;

  constructor(
    private readonly documentProcessingService: DocumentProcessingService,
    private readonly aiService: AIService,
    private readonly documentAnalysisTransformer: DocumentAnalysisTransformer,
    private readonly documentProcessingQueueService: DocumentProcessingQueueService,
    private readonly analysisResultService: AnalysisResultService,
    // Inject LegalPatternRecognitionService
    @Inject(forwardRef(() => LegalPatternRecognitionService))
    private readonly legalPatternRecognitionService: LegalPatternRecognitionService,
    private readonly legalResearchOrchestratorService: LegalResearchOrchestratorService, // Added
    @Inject(forwardRef(() => DocumentClassificationService))
    private readonly documentClassificationService: DocumentClassificationService,
    private readonly tenantContext: TenantContextService, // Add TenantContextService
    private readonly storageFactoryService: StorageFactoryService, // Add StorageFactoryService
    private readonly documentStorageService: DocumentStorageService, // Inject DocumentStorageService
    private readonly subscriptionService: SubscriptionService, // Add SubscriptionService
    private readonly postHogService: PostHogService, // Add PostHogService
    private readonly metricsService: DocumentProcessingMetricsService,
    private readonly cacheService: DocumentProcessingCacheService,
  ) {
    this.logger = new Logger(DocumentsController.name);
  }

  @Post('upload')
  @UseGuards(SubscriptionGuard)
  @SubscriptionCheck('document_upload')
  @UseInterceptors(
    FileInterceptor('file', {
      storage: memoryStorage(), // Use memory storage for streaming to Cloudflare R2
      limits: {
        fileSize: 50 * 1024 * 1024, // 50MB limit
      },
      fileFilter: (req, file, cb) => {
        const allowedMimeTypes = [
          'application/pdf',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'text/plain',
        ];
        if (allowedMimeTypes.includes(file.mimetype)) {
          cb(null, true);
        } else {
          cb(
            new BadRequestException(`Unsupported file type: ${file.mimetype}`),
            false,
          );
        }
      },
    }),
  )
  async uploadDocument(
    @UploadedFile() file: Express.Multer.File,
    @Body() uploadDocumentDto: UploadDocumentDto,
  ) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    // Get current user and organization information for tenant isolation
    const userId = this.tenantContext.getCurrentUserId();
    const organizationId = this.tenantContext.getCurrentOrganization();

    this.logger.log(`User ${userId} from organization ${organizationId} is uploading document: ${file.originalname}`);

    const metadata: Record<string, unknown> = {
      title: uploadDocumentDto.title,
      author: uploadDocumentDto.author,
      organizationId: organizationId,
      ownerId: userId,
      createdAt: new Date(),
      createdBy: userId,
      sharedWith: [],
      uploadStatus: 'uploading',
      processingStatus: 'pending',
    };

    try {
      // Upload file to Cloudflare R2 with progress tracking
      this.logger.log(`Starting upload to Cloudflare R2 for document: ${file.originalname}`);
      const cloudflareStorage = this.storageFactoryService.getCloudflareStorageService();
      const uploadResult = await cloudflareStorage.uploadFile(file, organizationId);

      // Store the R2 key and URL in metadata
      const filePath = uploadResult.key;
      const fileUrl = uploadResult.url;
      metadata.storageProvider = 'cloudflare';
      metadata.fileUrl = fileUrl;
      metadata.uploadStatus = 'completed';

      // Create document record immediately after upload
      const document = await this.documentProcessingService.createDocumentRecord(
        {
          ...file,
          path: filePath,
          filename: filePath
        },
        metadata,
      );

      this.logger.log(`Document record created with ID: ${document.id} for organization: ${organizationId}`);

      // Queue background processing (non-blocking)
      this.documentProcessingService.queueDocumentProcessing(
        document.id,
        userId,
        {
          extractMetadata: true,
          generateSummary: true,
          priority: false,
        },
      ).catch((error) => {
        this.logger.error(`Failed to queue processing for document ${document.id}:`, error);
      });

      // Track document upload for subscription limits
      try {
        await this.subscriptionService.trackDocumentUpload(organizationId);
        this.logger.log(`Document upload tracked for organization: ${organizationId}`);
      } catch (error) {
        this.logger.warn(`Failed to track document upload for organization ${organizationId}:`, error);
      }

      // Track document upload in PostHog
      this.postHogService.trackDocumentUpload(userId, document.id, uploadDocumentDto.title || 'unknown', {
        organization_id: organizationId,
        file_size: file.size,
        file_name: file.originalname,
        file_type: file.mimetype,
        storage_provider: 'cloudflare'
      });

      return {
        message: 'Document uploaded successfully and processing started',
        document: {
          id: document.id,
          filename: document.originalName,
          size: document.size,
          uploadDate: document.uploadDate,
          metadata: document.metadata,
          fileUrl: fileUrl,
          processingStatus: 'queued',
          estimatedProcessingTime: this.estimateProcessingTime(file.size, file.mimetype),
        },
      };
    } catch (error) {
      this.logger.error(`Upload failed for document ${file.originalname}:`, error);
      throw new BadRequestException(`Upload failed: ${error.message}`);
    }
  }

  private estimateProcessingTime(fileSize: number, mimeType: string): number {
    // Estimate processing time based on file size and type
    const baseTimes = {
      'text/plain': 1000, // 1 second per MB
      'application/pdf': 3000, // 3 seconds per MB
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 2000, // 2 seconds per MB
    };

    const baseTime = baseTimes[mimeType] || 2000;
    const sizeInMB = fileSize / (1024 * 1024);
    return Math.max(5000, baseTime * sizeInMB); // Minimum 5 seconds
  }

  @Get(':id/processing-status')
  @ApiOperation({ summary: 'Get document processing status' })
  @ApiParam({ name: 'id', description: 'Document ID' })
  async getProcessingStatus(@Param('id') id: string) {
    try {
      const document = await this.documentProcessingService.getDocumentById(id);
      if (!document) {
        throw new NotFoundException(`Document with ID ${id} not found`);
      }

      // Check if user has access to this document
      const userId = this.tenantContext.getCurrentUserId();
      const organizationId = this.tenantContext.getCurrentOrganization();

      if (document.organizationId !== organizationId) {
        throw new ForbiddenException('Access denied to this document');
      }

      const processingStatus = await this.documentProcessingService.getProcessingStatus(id);

      return {
        documentId: id,
        status: document.status,
        processingStatus,
        lastUpdated: document.uploadDate,
        progress: processingStatus?.progress || 0,
        estimatedTimeRemaining: processingStatus?.estimatedTimeRemaining,
        error: processingStatus?.error,
      };
    } catch (error) {
      this.logger.error(`Error getting processing status for document ${id}:`, error);
      throw error;
    }
  }

  @Get('performance/dashboard')
  @ApiOperation({ summary: 'Get document processing performance dashboard' })
  async getPerformanceDashboard() {
    try {
      // Only allow admin users or specific roles to access performance data
      const userId = this.tenantContext.getCurrentUserId();
      const organizationId = this.tenantContext.getCurrentOrganization();

      // TODO: Add proper role-based access control

      const [metrics, stats, cacheStats] = await Promise.all([
        this.metricsService.getMetrics(),
        this.metricsService.getStatistics(24),
        this.cacheService.getCacheStats(),
      ]);

      return {
        metrics,
        statistics: stats,
        cache: cacheStats,
        timestamp: new Date(),
        organizationId,
      };
    } catch (error) {
      this.logger.error('Error getting performance dashboard:', error);
      throw new BadRequestException('Failed to retrieve performance data');
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get all documents' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number (1-based)', example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of items per page', example: 10 })
  @ApiQuery({ name: 'sortBy', required: false, type: String, description: 'Field to sort by', example: 'uploadDate' })
  @ApiQuery({ name: 'sortDirection', required: false, enum: ['asc', 'desc'], description: 'Sort direction', example: 'desc' })
  @ApiResponse({
    status: 200,
    description: 'Returns a paginated list of documents',
  })
  async getAllDocuments(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'asc' | 'desc',
  ) {
    // Get the current organization ID for tenant isolation
    const organizationId = this.tenantContext.getCurrentOrganization();
    const userId = this.tenantContext.getCurrentUserId();
    const userRole = this.tenantContext.getCurrentUserRole();
    
    this.logger.log(`User ${userId} with role ${userRole} from organization ${organizationId} is requesting documents list`);
    
    // Prepare pagination options
    const paginationOptions = {
      page: page ? parseInt(page as any, 10) : 1,
      limit: limit ? parseInt(limit as any, 10) : 10,
      sortBy: sortBy || 'uploadDate',
      sortDirection: sortDirection || 'desc'
    };
    
    // SECURITY FIX: Always implement strict tenant isolation by default
    // Only bypass in explicit development mode with law_firm role
    const shouldBypassTenantIsolation = process.env.NODE_ENV === 'development' && userRole === 'law_firm';
    
    if (!shouldBypassTenantIsolation) {
      // Get all documents with pagination
      const result = await this.documentProcessingService.getAllDocuments(paginationOptions);
      
      // Filter documents based on organization ID - STRICT TENANT ISOLATION
      const filteredDocuments = result.documents.filter(doc => {
        const docOrgId = doc.organizationId || doc.metadata?.organizationId;
        
        // If document has an organization ID that matches the user's organization, allow access
        if (docOrgId === organizationId) {
          return true;
        }
        
        // If document has an owner ID that matches the user's ID, allow access
        if (doc.metadata?.ownerId === userId) {
          return true;
        }
        
        // Deny access to documents without proper organization ID or ownership
        return false;
      });
      
      this.logger.log(`Filtered ${result.documents.length} documents to ${filteredDocuments.length} for user ${userId} in organization ${organizationId}`);
      
      // Return paginated response
      return {
        items: filteredDocuments.map((doc) => ({
          id: doc.id,
          organizationId: doc.organizationId || doc.metadata?.organizationId || organizationId,
          filename: doc.originalName,
          size: doc.size,
          uploadDate: doc.uploadDate,
          fileType: doc.originalName ? doc.originalName.split('.').pop()?.toLowerCase() || 'unknown' : 'unknown',
          mimeType: doc.mimeType || this.getDefaultMimeType(doc.originalName),
          metadata: {
            title: doc.metadata?.title,
            author: doc.metadata?.author,
            pageCount: doc.metadata?.pageCount,
            organizationId: doc.organizationId || doc.metadata?.organizationId || organizationId,
          },
        })),
        total: filteredDocuments.length,
        page: result.page,
        limit: result.limit,
        totalPages: Math.ceil(filteredDocuments.length / result.limit),
        hasNextPage: result.page < Math.ceil(filteredDocuments.length / result.limit),
        hasPreviousPage: result.page > 1
      };
    }
    
    // For development law_firm users only, return all documents with pagination but still filter for display
    const result = await this.documentProcessingService.getAllDocuments(paginationOptions);
    
    this.logger.warn(`DEVELOPMENT MODE: Bypassing tenant isolation for law_firm user ${userId}`);
    
    // Return paginated response
    return {
      items: result.documents.map((doc) => ({
        id: doc.id,
        organizationId: doc.organizationId || doc.metadata?.organizationId || 'global',
        filename: doc.originalName,
        size: doc.size,
        uploadDate: doc.uploadDate,
        fileType: doc.originalName ? doc.originalName.split('.').pop()?.toLowerCase() || 'unknown' : 'unknown',
        mimeType: doc.mimeType || this.getDefaultMimeType(doc.originalName),
        metadata: {
          title: doc.metadata?.title,
          author: doc.metadata?.author,
          pageCount: doc.metadata?.pageCount,
          organizationId: doc.organizationId || doc.metadata?.organizationId || 'global',
        },
      })),
      total: result.total,
      page: result.page,
      limit: result.limit,
      totalPages: result.totalPages,
      hasNextPage: result.page < result.totalPages,
      hasPreviousPage: result.page > 1
    };
  }

  // Helper method to determine MIME type from filename
  private getDefaultMimeType(filename: string | undefined): string {
    if (!filename) return 'application/octet-stream';
    
    const extension = filename.split('.').pop()?.toLowerCase();
    
    const mimeTypes = {
      'pdf': 'application/pdf',
      'doc': 'application/msword',
      'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'txt': 'text/plain',
      'rtf': 'application/rtf',
      'html': 'text/html',
      'htm': 'text/html',
      'xls': 'application/vnd.ms-excel',
      'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'ppt': 'application/vnd.ms-powerpoint',
      'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'csv': 'text/csv',
      'json': 'application/json',
      'xml': 'application/xml',
      'zip': 'application/zip',
    };
    
    return extension && extension in mimeTypes 
      ? mimeTypes[extension as keyof typeof mimeTypes] 
      : 'application/octet-stream';
  }

  @Get(':id')
  async getDocument(@Param('id') id: string) {
    const document = await this.documentProcessingService.getDocumentById(id);

    if (!document) {
      throw new NotFoundException(`Document with ID ${id} not found`);
    }

    // Enforce tenant isolation - check if the document belongs to the user's organization
    const organizationId = this.tenantContext.getCurrentOrganization();
    const userId = this.tenantContext.getCurrentUserId();
    const userRole = this.tenantContext.getCurrentUserRole();
    
    this.logger.log(`User ${userId} with role ${userRole} from organization ${organizationId} is requesting document ${id}`);
    
    // SECURITY FIX: Always enforce tenant isolation by default
    // Only bypass in explicit development mode with law_firm role
    const shouldBypassTenantIsolation = process.env.NODE_ENV === 'development' && userRole === 'law_firm';
    
    if (!shouldBypassTenantIsolation) {
      // Get document organization ID from metadata or top-level
      const docOrgId = document.organizationId || document.metadata?.organizationId;
      const docOwnerId = document.metadata?.ownerId;
      
      // For documents with organization ID, enforce strict tenant isolation
      if (docOrgId && docOrgId !== organizationId) {
        this.logger.warn(
          `Cross-organization document access attempt: documentId=${id}, ` +
          `userOrg=${organizationId}, docOrg=${docOrgId}`
        );
        throw new ForbiddenException('You do not have access to this document');
      }
      
      // For documents without organization ID, only allow access if user is the owner
      if (!docOrgId) {
        if (docOwnerId && docOwnerId !== userId) {
          this.logger.warn(
            `Unauthorized access attempt to document without organization ID: ` +
            `documentId=${id}, userId=${userId}, ownerId=${docOwnerId}`
          );
          throw new ForbiddenException('You do not have access to this document');
        }
      }
    } else {
      this.logger.warn(`DEVELOPMENT MODE: Bypassing tenant isolation for law_firm user ${userId} accessing document ${id}`);
    }

    // IMPORTANT: Do not modify the document's metadata here
    // This was causing the security issue by overwriting the organization ID
    
    // Record document access for audit purposes
    this.tenantContext.recordDocumentAccess(id);

    // Track document access in PostHog
    this.postHogService.trackEvent(userId, 'document_accessed', {
      document_id: id,
      organization_id: organizationId,
      user_role: userRole,
      access_type: 'view'
    });

    return {
      id: document.id,
      organizationId: document.organizationId || document.metadata?.organizationId, // Include the original organization ID
      filename: document.originalName,
      originalName: document.originalName,
      content: document.content,
      size: document.size,
      uploadDate: document.uploadDate,
      status: document.status,
      metadata: document.metadata,
    };
  }

  @Get(':id/content')
  async getDocumentContent(@Param('id') id: string) {
    const content = await this.documentProcessingService.getDocumentContent(id);

    if (!content) {
      throw new NotFoundException(`Content for document ${id} not found`);
    }

    return {
      content,
    };
  }

  @Get(':id/versions')
  @ApiOperation({ summary: 'Get all versions of a document' })
  @ApiParam({ name: 'id', description: 'Document ID' })
  @ApiResponse({
    status: 200,
    description: 'Document versions retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Document not found',
  })
  async getDocumentVersions(@Param('id') id: string) {
    const document = await this.documentProcessingService.getDocumentById(id);
    if (!document) {
      throw new NotFoundException(`Document with ID ${id} not found`);
    }

    const versions = await this.documentProcessingService.getDocumentVersions(
      id,
    );
    return versions;
  }

  @Get(':id/versions/:versionNumber')
  @ApiOperation({ summary: 'Get a specific version of a document' })
  @ApiParam({ name: 'id', description: 'Document ID' })
  @ApiParam({
    name: 'versionNumber',
    description: 'Version number to retrieve',
  })
  @ApiResponse({
    status: 200,
    description: 'Document version retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Document or version not found',
  })
  @UseGuards(SubscriptionGuard)
  @SubscriptionCheck('document_comparison')
  async getDocumentVersion(
    @Param('id') id: string,
    @Param('versionNumber') versionNumber: string,
  ) {
    const document = await this.documentProcessingService.getDocumentById(id);
    if (!document) {
      throw new NotFoundException(`Document with ID ${id} not found`);
    }

    try {
      // Convert versionNumber to integer
      const versionNum = parseInt(versionNumber, 10);
      if (isNaN(versionNum)) {
        throw new BadRequestException('Version number must be a valid integer');
      }

      const version = await this.documentProcessingService.getDocumentVersion(
        id,
        versionNum,
      );
      return version;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.logger.error(
        `Error retrieving version ${versionNumber} of document ${id}:`,
        error,
      );
      throw new InternalServerErrorException(
        `Failed to retrieve document version: ${error.message}`,
      );
    }
  }

  @Post(':id/versions')
  @ApiOperation({ summary: 'Create a new version of a document' })
  @ApiParam({ name: 'id', description: 'Document ID' })
  @ApiBody({
    description: 'Document version data',
    schema: {
      properties: {
        content: {
          type: 'string',
          description: 'The content of the new version',
        },
        metadata: {
          type: 'object',
          description: 'Additional metadata for the version',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Document version created successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Document not found',
  })
  async createDocumentVersion(
    @Param('id') id: string,
    @Body()
    createVersionDto: { content: string; metadata?: Record<string, any> },
    @Req() req: any,
  ) {
    const document = await this.documentProcessingService.getDocumentById(id);
    if (!document) {
      throw new NotFoundException(`Document with ID ${id} not found`);
    }

    try {
      const userId = req.user?.id || 'anonymous';

      const result = await this.documentProcessingService.createDocumentVersion(
        id,
        createVersionDto.content,
        userId,
        createVersionDto.metadata || {},
      );

      return {
        status: 'success',
        message: 'Document version created successfully',
        data: {
          documentId: id,
          version: result,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error creating document version: ${error.message}`,
        error.stack,
      );
      throw new InternalServerErrorException(
        `Failed to create document version: ${error.message}`,
      );
    }
  }

  /**
   * Analyze a single document using AI
   */
  @UseGuards(SubscriptionGuard)
  @SubscriptionCheck('basic_analysis')
  @Post(':id/analyze')
  async analyzeDocument(
    @Param('id') id: string,
    @Body() analysisRequest: DocumentAnalysisRequestDto,
    @Req() req: any,
  ) {
    const document = await this.documentProcessingService.getDocumentById(id);
    if (!document) {
      throw new NotFoundException('Document not found');
    }

    const organizationId = req.user?.organizationId; // This should be a string from the JWT payload
    if (!organizationId) {
      console.error(
        'Organization ID not found on request object for analysis save.',
      );
      throw new BadRequestException('User organization context is missing.');
    }

    if (!document.content) {
      throw new InternalServerErrorException(
        `Content for document ${id} is missing or empty.`,
      );
    }
    const documentContent = document.content;

    // Check for existing analysis first, unless forceNew is true
    if (!analysisRequest.forceNew) {
      const existingAnalysis =
        await this.analysisResultService.findLatestByDocumentIdAndOrg(
          id,
          organizationId,
        );
      if (existingAnalysis) {
        this.logger.log(
          `Returning existing analysis for document: ${id}, analysisId: ${existingAnalysis.id}`,
        );
        const enrichedCitations =
          existingAnalysis.aiMetadata?.legalCitations || [];
        const analysisContentWithCitations = {
          ...existingAnalysis.analysisContent,
          enrichedCitations,
        };
        return {
          analysisId: existingAnalysis.id,
          result: analysisContentWithCitations,
        };
      }
    } else {
      this.logger.log(`Force new analysis requested for document: ${id}`);
    }

    let analysisResult: AIAnalysisResult;
    try {
      this.logger.log(`Starting new AI analysis for document: ${id}`);
      analysisResult = await this.aiService.analyzeDocument(documentContent, {
        documentType: analysisRequest.documentType,
        query: analysisRequest.query,
      });

      // Log the received analysis result structure
      this.logger.debug(
        `Received AI analysis result for ${id}: ${JSON.stringify(
          analysisResult,
        )}`,
      );

      // --- NEW: Enrich with Legal Research ---
      // Check if AI analysis seems successful (has content)
      if (analysisResult?.content) {
        try {
          this.logger.log(
            `Attempting enrichment for document: ${id} with legal research.`,
          );
          // Call the refactored enrichDocument directly
          analysisResult =
            await this.legalResearchOrchestratorService.enrichDocument(
              analysisResult, // Pass the full analysis result object
              documentContent, // Pass the original document content
            );
          this.logger.log(
            `Enrichment complete for document: ${id}. Check metadata for results.`,
          );
        } catch (enrichError) {
          // Log the enrichment error but proceed with the original AI analysis result
          this.logger.error(
            `Error enriching document ${id} with legal research:`,
            enrichError, // Log the full error object
          );
          // Use the specific error type if available, otherwise default
          if (enrichError instanceof InternalServerErrorException) {
            throw enrichError;
          }
          throw new InternalServerErrorException(
            `Analysis failed or could not be saved: ${enrichError.message}`,
          );
        }
      } else {
        // Add logging here if the AI result content is missing
        this.logger.warn(
          `Skipping enrichment for document ${id}: AI analysis result content is missing or empty.`,
        );
      }
      // --- End Enrichment Block ---

      // Save the potentially enriched result
      this.logger.log(`Saving analysis result for document: ${id}`);
      // Construct the object structure expected by the save method, using the potentially enriched analysisResult
      const dataToSave: Partial<AnalysisResult> = {
        documentId: id,
        organizationId: organizationId,
        analysisContent: analysisResult.content, // Extract content
        aiMetadata: analysisResult.metadata, // Extract metadata
        aiProvider: analysisResult.metadata?.provider, // Use provider from metadata
        modelUsed: analysisResult.metadata?.modelUsed || 'unknown', // Use model from metadata
        query: analysisRequest.query, // Include the original query
        requestDetails: {
          // Include request details if needed by schema
          query: analysisRequest.query,
          requestedType: analysisRequest.documentType,
          detectedType: analysisResult.metadata?.detectedDocumentType,
        },
      };

      const savedAnalysis = await this.analysisResultService.save(dataToSave); // Use .save()

      this.logger.log(
        `Saved new analysis for document: ${id}, analysisId: ${savedAnalysis.id}`,
      );

      // Update document status after successful analysis save
      try {
        await this.documentProcessingService.updateDocumentStatus(
          id,
          'analyzed',
        );
        this.logger.log(`Updated status to 'analyzed' for document ${id}`);
      } catch (statusUpdateError) {
        // Log the error but don't fail the request, analysis was saved
        this.logger.error(
          `Failed to update document ${id} status to 'analyzed': ${statusUpdateError.message}`,
          statusUpdateError.stack,
        );
      }

      // Track document analysis in PostHog
      this.postHogService.trackDocumentAnalysis(
        organizationId,
        id,
        analysisRequest.documentType || 'standard',
        0, // We'll track duration separately in the future
        {
          organization_id: organizationId,
          analysis_id: savedAnalysis.id,
          has_enriched_citations: (savedAnalysis.aiMetadata?.legalCitations || []).length > 0,
          ai_provider: savedAnalysis.aiProvider,
          model_used: savedAnalysis.modelUsed
        }
      );

      // Return the final result (content and ID)
      // Extract enriched citations from aiMetadata if they exist
      const enrichedCitations = savedAnalysis.aiMetadata?.legalCitations || [];
      this.logger.debug(
        `Found ${enrichedCitations.length} enriched citations for document ${id}`,
      );

      // Merge enriched citations into the analysis content
      const analysisContentWithCitations = {
        ...savedAnalysis.analysisContent,
        enrichedCitations,
      };

      return {
        analysisId: savedAnalysis.id,
        result: analysisContentWithCitations,
      };
    } catch (error) {
      this.logger.error(
        `Error during AI analysis or saving for document ${id}:`,
        error,
      );
      // Ensure status is updated on failure
      try {
        await this.documentProcessingService.updateDocumentStatus(
          id,
          'failed',
          `Analysis/Save Error: ${error.message}`,
        );
      } catch (statusError) {
        this.logger.error(
          `Failed to update document ${id} status to 'failed': ${statusError.message}`,
        );
      }
      // Rethrow original error
      if (
        error instanceof InternalServerErrorException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error; // Re-throw known HTTP errors
      } else {
        throw new InternalServerErrorException(
          `Analysis failed or could not be saved: ${error.message}`,
        );
      }
    }
  }

  @UseGuards(SubscriptionGuard)
  @SubscriptionCheck('basic_analysis') // Or adjust tier as needed
  @Get(':id/analysis')
  @ApiOperation({ summary: 'Get analysis results for a document' })
  @ApiResponse({
    status: 200,
    description: 'Analysis results retrieved successfully.',
    type: [AnalysisResult],
  }) // Adjust type if needed
  @ApiResponse({
    status: 404,
    description: 'Document or Analysis results not found.',
  })
  @ApiQuery({
    name: 'latest',
    required: false,
    type: Boolean,
    description: 'Retrieve only the latest analysis result',
  })
  async getAnalysisResults(
    @Param('id') id: string,
    @Req() req: any,
    @Query('latest') latest?: string, // Query params come as strings
  ) {
    const organizationId = req.user?.organizationId;
    if (!organizationId) {
      console.error(
        'Organization ID not found on request object for fetching analysis.',
      );
      throw new BadRequestException('User organization context is missing.');
    }

    try {
      const retrieveLatest = latest === 'true'; // Convert string query param to boolean

      if (retrieveLatest) {
        // Pass string IDs to service
        const latestResult =
          await this.analysisResultService.findLatestByDocumentIdAndOrgOrThrow(
            id,
            organizationId,
          );

        // Extract enriched citations from aiMetadata if they exist
        const enrichedCitations = latestResult.aiMetadata?.legalCitations || [];
        this.logger.debug(
          `Found ${enrichedCitations.length} enriched citations for document ${id}`,
        );

        // Merge enriched citations into the analysis content
        const analysisContentWithCitations = {
          ...latestResult.analysisContent,
          enrichedCitations,
        };

        return {
          analysisId: latestResult.id,
          result: analysisContentWithCitations,
        };
      } else {
        // Pass string IDs to service
        const allResults =
          await this.analysisResultService.findAllByDocumentIdAndOrg(
            id,
            organizationId,
          );

        // Transform each result to include enriched citations
        return allResults.map((result) => {
          const enrichedCitations = result.aiMetadata?.legalCitations || [];
          const analysisContentWithCitations = {
            ...result.analysisContent,
            enrichedCitations,
          };

          return {
            analysisId: result.id,
            result: analysisContentWithCitations,
          };
        });
      }
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(
        `Error fetching analysis results for document ${id} and org ${organizationId}:`,
        error,
      );
      throw new InternalServerErrorException(
        'Failed to retrieve analysis results.',
      );
    }
  }

  /**
   * Get analysis evolution metrics for a document
   */
  @UseGuards(SubscriptionGuard)
  @SubscriptionCheck('basic_analysis')
  @Get(':id/analysis/evolution')
  @ApiOperation({ summary: 'Get analysis evolution metrics for a document' })
  @ApiResponse({
    status: 200,
    description: 'Analysis evolution metrics retrieved successfully.',
  })
  @ApiResponse({
    status: 404,
    description: 'Document or Analysis results not found.',
  })
  async getAnalysisEvolution(@Param('id') id: string, @Req() req: any) {
    const organizationId = req.user?.organizationId;
    if (!organizationId) {
      this.logger.error(
        'Organization ID not found on request object for fetching analysis evolution.',
      );
      throw new BadRequestException('User organization context is missing.');
    }

    try {
      // Check if document exists
      const document = await this.documentProcessingService.getDocumentById(id);
      if (!document) {
        throw new NotFoundException(`Document with ID ${id} not found`);
      }

      // Get evolution metrics
      const evolutionMetrics =
        await this.analysisResultService.getAnalysisEvolutionMetrics(
          id,
          organizationId,
        );

      return evolutionMetrics;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Error fetching analysis evolution for document ${id} and org ${organizationId}:`,
        error,
      );
      throw new InternalServerErrorException(
        'Failed to retrieve analysis evolution metrics.',
      );
    }
  }

  /**
   * Compare analysis results for a document
   */
  @UseGuards(SubscriptionGuard)
  @SubscriptionCheck('basic_analysis')
  @Post(':id/analysis/compare')
  @ApiOperation({ summary: 'Compare analysis results for a document' })
  @ApiResponse({
    status: 200,
    description: 'Analysis comparison completed successfully.',
  })
  @ApiResponse({
    status: 404,
    description: 'Document or Analysis results not found.',
  })
  async compareAnalysisResults(
    @Param('id') id: string,
    @Body() comparisonRequest: AnalysisComparisonRequestDto,
    @Req() req: any,
  ) {
    const organizationId = req.user?.organizationId;
    if (!organizationId) {
      this.logger.error(
        'Organization ID not found on request object for analysis comparison.',
      );
      throw new BadRequestException('User organization context is missing.');
    }

    try {
      // Check if document exists
      const document = await this.documentProcessingService.getDocumentById(id);
      if (!document) {
        throw new NotFoundException(`Document with ID ${id} not found`);
      }

      // If analysis IDs are not provided, get all analyses for the document
      let analysisIds = comparisonRequest.analysisIds;
      if (!analysisIds || analysisIds.length < 2) {
        const allResults =
          await this.analysisResultService.findAllByDocumentIdAndOrg(
            id,
            organizationId,
          );

        if (allResults.length < 2) {
          throw new BadRequestException(
            'At least two analysis results are required for comparison',
          );
        }

        analysisIds = allResults.map((result) => result._id.toString());
      }

      // Compare analysis results
      const comparisonResults =
        await this.analysisResultService.compareAnalysisResults(
          analysisIds,
          comparisonRequest.comparisonType,
        );

      return {
        documentId: id,
        analysisIds,
        comparisonType: comparisonRequest.comparisonType,
        results: comparisonResults,
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      this.logger.error(
        `Error comparing analysis results for document ${id}:`,
        error,
      );
      throw new InternalServerErrorException(
        'Failed to compare analysis results.',
      );
    }
  }

  /**
   * Compare analysis results for a document (GET method)
   */
  @UseGuards(SubscriptionGuard)
  @SubscriptionCheck('basic_analysis')
  @Get(':id/analysis/compare')
  @ApiOperation({
    summary: 'Compare analysis results for a document using GET method',
  })
  @ApiResponse({
    status: 200,
    description: 'Analysis comparison completed successfully.',
  })
  @ApiResponse({
    status: 404,
    description: 'Document or Analysis results not found.',
  })
  @ApiQuery({
    name: 'firstAnalysisId',
    required: false,
    type: String,
    description:
      'ID of the first analysis to compare. Use "latest" for the most recent analysis.',
  })
  @ApiQuery({
    name: 'secondAnalysisId',
    required: false,
    type: String,
    description:
      'ID of the second analysis to compare. Use "earliest" for the oldest analysis.',
  })
  @ApiQuery({
    name: 'comparisonType',
    required: false,
    type: String,
    description:
      'Type of comparison to perform (detailed, summary, or differences)',
    enum: ['detailed', 'summary', 'differences'],
  })
  async compareAnalysisResultsGet(
    @Param('id') id: string,
    @Query('firstAnalysisId') firstAnalysisId: string | undefined,
    @Query('secondAnalysisId') secondAnalysisId: string | undefined,
    @Query('comparisonType') comparisonType: string = 'detailed',
    @Req() req: any,
  ) {
    const organizationId = req.user?.organizationId;
    if (!organizationId) {
      this.logger.error(
        'Organization ID not found on request object for analysis comparison.',
      );
      throw new BadRequestException('User organization context is missing.');
    }

    try {
      // Check if document exists
      const document = await this.documentProcessingService.getDocumentById(id);
      if (!document) {
        throw new NotFoundException(`Document with ID ${id} not found`);
      }

      // Get all analyses for the document
      const allResults =
        await this.analysisResultService.findAllByDocumentIdAndOrg(
          id,
          organizationId,
        );

      if (allResults.length < 2) {
        throw new BadRequestException(
          'At least two analysis results are required for comparison',
        );
      }

      // Sort results by creation date (newest first)
      allResults.sort((a, b) => {
        const aDate = a['createdAt'] ? new Date(a['createdAt']).getTime() : 0;
        const bDate = b['createdAt'] ? new Date(b['createdAt']).getTime() : 0;
        return bDate - aDate;
      });

      // Determine which analysis IDs to use
      let analysisIds: string[] = [];

      if (firstAnalysisId === 'latest' || !firstAnalysisId) {
        // Use the most recent analysis
        analysisIds.push(allResults[0]._id.toString());
      } else {
        analysisIds.push(firstAnalysisId);
      }

      if (secondAnalysisId === 'earliest' || !secondAnalysisId) {
        // Use the oldest analysis
        analysisIds.push(allResults[allResults.length - 1]._id.toString());
      } else {
        analysisIds.push(secondAnalysisId);
      }

      // Compare analysis results
      const comparisonResults =
        await this.analysisResultService.compareAnalysisResults(
          analysisIds,
          comparisonType,
        );

      return {
        documentId: id,
        analysisIds,
        comparisonType,
        results: comparisonResults,
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      this.logger.error(
        `Error comparing analysis results for document ${id}:`,
        error,
      );
      throw new InternalServerErrorException(
        'Failed to compare analysis results.',
      );
    }
  }

  /**
   * Analyze multiple documents together, using document context
   */
  @UseGuards(SubscriptionGuard)
  @SubscriptionCheck('advanced_analysis')
  @Post('analyze-multiple')
  async analyzeMultipleDocuments(
    @Body() request: MultiDocumentAnalysisRequestDto,
  ) {
    const primaryDoc = await this.documentProcessingService.getDocumentById(
      request.primaryDocumentId,
    );

    if (!primaryDoc) {
      throw new NotFoundException(
        `Primary document with ID ${request.primaryDocumentId} not found`,
      );
    }

    const documents: string[] = [];

    const primaryContent =
      await this.documentProcessingService.getDocumentContent(
        request.primaryDocumentId,
      );
    if (primaryContent) {
      documents.push(primaryContent);
    } else {
      throw new BadRequestException(
        `Content for primary document ${request.primaryDocumentId} not available`,
      );
    }

    if (request.relatedDocumentIds && request.relatedDocumentIds.length > 0) {
      for (const docId of request.relatedDocumentIds) {
        const content = await this.documentProcessingService.getDocumentContent(
          docId,
        );
        if (content) {
          documents.push(content);
        }
      }
    }

    return this.aiService.compareDocuments(documents, {
      documentType: request.documentType as unknown as DocumentType,
    });
  }

  /**
   * Compare sections across multiple documents
   */
  @UseGuards(SubscriptionGuard)
  @SubscriptionCheck('advanced_analysis')
  @Post('compare-sections')
  async compareDocumentSections(
    @Body() request: DocumentSectionComparisonRequestDto,
  ) {
    if (
      !request.documentSections ||
      Object.keys(request.documentSections).length < 2
    ) {
      throw new BadRequestException(
        'At least two documents with sections are required for comparison',
      );
    }

    const sectionTexts = Object.values(request.documentSections)
      .flat()
      .map((section) => section.content);

    return this.aiService.compareDocuments(sectionTexts, {
      comparisonType: request.comparisonType || 'both',
    });
  }

  @Post(':documentId/process-async')
  @ApiOperation({ summary: 'Queue a document for asynchronous processing' })
  @ApiResponse({
    status: 202,
    description: 'Document queued for processing',
    type: DocumentProcessingJobResponseDto,
  })
  async queueDocumentProcessing(
    @Param('documentId') documentId: string,
    @Body() jobRequest: DocumentProcessingJobRequestDto,
  ): Promise<DocumentProcessingJobResponseDto> {
    try {
      const documentExists =
        await this.documentProcessingService.documentExists(documentId);
      if (!documentExists) {
        throw new NotFoundException(`Document with ID ${documentId} not found`);
      }

      const jobId =
        await this.documentProcessingQueueService.queueDocumentProcessing(
          documentId,
          undefined, // userId not implemented yet
          {
            priority: jobRequest.priority,
            extractMetadata: jobRequest.extractMetadata,
            generateSummary: jobRequest.generateSummary,
          },
        );

      const jobStatus = await this.documentProcessingQueueService.getJobStatus(
        jobId,
      );

      return {
        jobId,
        documentId,
        status: jobStatus.status,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to queue document processing: ${error.message}`,
      );
    }
  }

  @Get('jobs/:jobId')
  @ApiOperation({ summary: 'Get status of a document processing job' })
  @ApiResponse({
    status: 200,
    description: 'Job status retrieved successfully',
    type: DocumentJobStatusResponseDto,
  })
  async getJobStatus(
    @Param('jobId') jobId: string,
  ): Promise<DocumentJobStatusResponseDto> {
    try {
      const jobStatus = await this.documentProcessingQueueService.getJobStatus(
        jobId,
      );

      return {
        id: jobStatus.id,
        status: jobStatus.status,
        progress: jobStatus.progress,
        documentId: jobStatus.data.documentId,
        failedReason: jobStatus.failedReason,
        attempts: jobStatus.attempts,
      };
    } catch (error) {
      throw new NotFoundException(`Job with ID ${jobId} not found`);
    }
  }

  @Get(':documentId/jobs')
  @ApiOperation({ summary: 'Get all jobs for a document' })
  @ApiResponse({
    status: 200,
    description: 'Document jobs retrieved successfully',
    type: [DocumentJobStatusResponseDto],
  })
  async getDocumentJobs(
    @Param('documentId') documentId: string,
  ): Promise<DocumentJobStatusResponseDto[]> {
    try {
      const jobs = await this.documentProcessingQueueService.getDocumentJobs(
        documentId,
      );

      return jobs.map((job) => ({
        id: job.id,
        status: job.status,
        progress: job.progress,
        documentId: job.data.documentId,
      }));
    } catch (error) {
      throw new BadRequestException(
        `Failed to get document jobs: ${error.message}`,
      );
    }
  }

  @Get(':id/render')
  @UseGuards(JwtAuthGuard, DocumentAccessGuard) // Apply necessary guards
  @ApiOperation({ summary: 'Render or download the original document file' })
  @ApiParam({ name: 'id', description: 'Document ID', type: String })
  @ApiOkResponse({ description: 'Document file stream.', content: { '*/*': {} } }) // Indicate binary response
  @ApiNotFoundResponse({ description: 'Document not found or storage error.' })
  async renderDocument(
    @Param('id') id: string,
    @Res({ passthrough: true }) res: Response, // Use passthrough: true
  ): Promise<StreamableFile> { // Return StreamableFile
    this.logger.log(`Attempting to render document with ID: ${id}`);
    try {
      // 1. Get document metadata (including storage path, mime type, filename)
      // Use DocumentStorageService to get the document by ID
      const document = await this.documentStorageService.getDocumentById(id); // Corrected service and method call
      if (!document || !document.filename) { // Only check for filename, handle missing mimeType below
        this.logger.warn(`Document metadata not found or incomplete for ID: ${id}`);
        throw new NotFoundException(`Document with ID ${id} not found or missing storage details.`);
      }

      // Determine MIME type - use document.mimeType if available, otherwise infer from filename
      const mimeType = document.mimeType || this.getDefaultMimeType(document.originalName);
      
      // Use correct property names from schema
      this.logger.log(`Found document: ${document.originalName}, MIME: ${mimeType}, Filename (Storage Key): ${document.filename}`);

      // 2. Get storage provider - use getCloudflareStorageService() instead of getStorageProvider()
      const storageProvider = this.storageFactoryService.getCloudflareStorageService();

      // 3. Get file buffer from storage using the filename as the key
      const fileBuffer = await storageProvider.getFile(document.filename);

      if (!fileBuffer) {
         this.logger.error(`Failed to retrieve file from storage for key: ${document.filename}`);
         throw new InternalServerErrorException(`Could not retrieve document file from storage.`);
      }

      this.logger.log(`Successfully retrieved file for: ${document.filename} (${fileBuffer.length} bytes)`);

      // Convert buffer to stream
      const fileStream = Readable.from(fileBuffer);
      
      // 4. Set headers using correct properties
      res.setHeader('Content-Type', mimeType); // Use the determined mimeType
      // Use 'inline' for common web-viewable types, 'attachment' otherwise to force download
      const safeFilename = (document.originalName || 'document').replace(/[^a-zA-Z0-9_.-]/g, '_');
      const disposition = ['application/pdf', 'image/jpeg', 'image/png', 'image/gif', 'text/plain', 'text/html'].includes(mimeType) ? 'inline' : 'attachment';
      res.setHeader('Content-Disposition', `${disposition}; filename="${safeFilename}"`);
      // Optionally add Content-Length if available from metadata
      if (document.size) {
         res.setHeader('Content-Length', document.size.toString());
      }

      // 5. Return StreamableFile (NestJS handles piping)
      return new StreamableFile(fileStream);

    } catch (error) {
      this.logger.error(`Error rendering document ${id}: ${error.message}`, error.stack);
      if (error instanceof NotFoundException) {
        throw error;
      }
      // Handle other potential errors (e.g., storage access errors)
      throw new InternalServerErrorException(`Failed to render document: ${error.message}`);
    }
  }

  // == NEW: Detect Specific Patterns ==
  @Post(':id/patterns')
  @UseGuards(SubscriptionGuard)
  @ApiOperation({
    summary: 'Detect specific patterns in a document based on a query',
  })
  @ApiParam({ name: 'id', description: 'The ID of the document', type: String })
  @ApiBody({
    description: 'The query describing patterns to find',
    type: PatternRequestDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Array of detected legal patterns matching the query.',
    type: LegalPatternDto, // Specify the type
    isArray: true, // Indicate it's an array
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request (e.g., missing content or query)',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Document not found' })
  @ApiResponse({ status: 500, description: 'Pattern detection failed' })
  async detectPatterns(
    @Param('id') id: string,
    @Body() patternRequest: PatternRequestDto,
    @Req() req: any,
  ): Promise<LegalPatternDto[]> {
    this.logger.log(
      `Starting pattern detection for document ${id} with query: "${patternRequest.query}" by user ${req.user.userId}`,
    );

    const document = await this.documentProcessingService.getDocumentById(id);
    if (!document) {
      this.logger.warn(`Document ${id} not found for pattern detection.`);
      throw new NotFoundException(`Document with ID ${id} not found.`);
    }

    if (!document.content || document.content.trim().length === 0) {
      this.logger.error(`Document ${id} has no content for pattern detection.`);
      throw new BadRequestException('Document content is missing or empty.');
    }

    if (!patternRequest.query || patternRequest.query.trim().length === 0) {
      this.logger.error(
        `Pattern detection request for document ${id} missing query.`,
      );
      throw new BadRequestException('Pattern query is missing or empty.');
    }

    try {
      this.logger.log(
        `Calling LegalPatternRecognitionService for document ${id}...`,
      );
      const patterns =
        await this.legalPatternRecognitionService.detectPatternsAI(
          document.content,
          id,
          patternRequest.query,
        );
      this.logger.log(
        `LegalPatternRecognitionService returned ${patterns.length} patterns for document ${id}. Query: "${patternRequest.query}"`,
      );
      return patterns;
    } catch (patternError) {
      this.logger.error(
        `Pattern detection failed for document ${id} with query "${patternRequest.query}": ${patternError.message}`,
        patternError.stack,
      );
      // Use the specific error type if available, otherwise default
      if (patternError instanceof InternalServerErrorException) {
        throw patternError;
      }
      throw new InternalServerErrorException(
        `Pattern detection failed: ${patternError.message}`,
      );
    }
  }

  @Post('classify-unknown')
  @ApiOperation({
    summary: 'Manually trigger classification of unknown documents',
  })
  @ApiResponse({
    status: 200,
    description: 'Classification job started successfully',
  })
  async classifyUnknownDocuments() {
    this.logger.log('Manually triggering classification of unknown documents');
    await this.documentClassificationService.manuallyClassifyUnknownDocuments();
    return { message: 'Document classification job started successfully' };
  }

  /**
   * Delete a document by ID
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Delete a document' })
  @ApiParam({ name: 'id', description: 'Document ID' })
  @ApiResponse({
    status: 200,
    description: 'Document deleted successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Document not found',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - User does not have access to this document',
  })
  async deleteDocument(@Param('id') id: string, @Req() req: any) {
    // Get the document to check if it exists and verify ownership
    const document = await this.documentProcessingService.getDocumentById(id);
    if (!document) {
      throw new NotFoundException(`Document with ID ${id} not found`);
    }

    // Enforce tenant isolation - check if the document belongs to the user's organization
    const organizationId = this.tenantContext.getCurrentOrganization();
    const userId = this.tenantContext.getCurrentUserId();
    const userRole = this.tenantContext.getCurrentUserRole();
    
    this.logger.log(`User ${userId} with role ${userRole} from organization ${organizationId} is attempting to delete document ${id}`);
    
    // SECURITY FIX: Always enforce tenant isolation by default
    // Only bypass in explicit development mode with law_firm role
    const shouldBypassTenantIsolation = process.env.NODE_ENV === 'development' && userRole === 'law_firm';
    
    if (!shouldBypassTenantIsolation) {
      // Get document organization ID from metadata or top-level
      const docOrgId = document.organizationId || document.metadata?.organizationId;
      const docOwnerId = document.metadata?.ownerId;
      
      // For documents with organization ID, enforce strict tenant isolation
      if (docOrgId && docOrgId !== organizationId) {
        this.logger.warn(
          `Cross-organization document delete attempt: documentId=${id}, ` +
          `userOrg=${organizationId}, docOrg=${docOrgId}`
        );
        throw new ForbiddenException('You do not have access to delete this document');
      }
      
      // For documents without organization ID, only allow access if user is the owner
      if (!docOrgId) {
        if (docOwnerId && docOwnerId !== userId) {
          this.logger.warn(
            `Unauthorized delete attempt for document without organization ID: ` +
            `documentId=${id}, userId=${userId}, ownerId=${docOwnerId}`
          );
          throw new ForbiddenException('You do not have access to delete this document');
        }
      }
    } else {
      this.logger.warn(`DEVELOPMENT MODE: Bypassing tenant isolation for law_firm user ${userId} accessing document ${id}`);
    }

    try {
      // Delete the document and its associated storage
      await this.documentProcessingService.deleteDocument(id);
      
      // Record document deletion for audit purposes - using 'edit' as the closest appropriate access type
      this.tenantContext.recordDocumentAccess(id, 'edit');

      // Track document deletion in PostHog
      this.postHogService.trackEvent(userId, 'document_deleted', {
        document_id: id,
        organization_id: organizationId,
        user_role: userRole
      });

      return {
        status: 'success',
        message: `Document with ID ${id} has been deleted successfully`
      };
    } catch (error) {
      this.logger.error(`Error deleting document ${id}:`, error);
      throw new InternalServerErrorException(`Failed to delete document: ${error.message}`);
    }
  }
}

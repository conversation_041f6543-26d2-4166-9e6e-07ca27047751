import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DocumentCacheEntity } from './entities/document-cache.entity';
import { DocumentCacheService } from './services/document-cache.service';

@Module({
  imports: [TypeOrmModule.forFeature([DocumentCacheEntity])],
  providers: [DocumentCacheService],
  exports: [DocumentCacheService],
})
export class DocumentCacheModule {}

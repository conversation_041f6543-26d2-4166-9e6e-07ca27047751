import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Document } from '../schemas/document.schema';
import { TenantAwareRepository } from '../../common/repositories/base.repository';
import { TenantContextService } from '../../auth/services/tenant-context.service';

@Injectable()
export class DocumentRepository extends TenantAwareRepository<Document> {
  constructor(
    @InjectModel(Document.name) documentModel: Model<Document>,
    tenantContext: TenantContextService,
  ) {
    super(documentModel, tenantContext);
  }

  async findByDocumentType(documentType: string): Promise<Document[]> {
    return this.find({ documentType });
  }

  async findByStatus(status: string): Promise<Document[]> {
    return this.find({ status });
  }

  async findByDateRange(startDate: Date, endDate: Date): Promise<Document[]> {
    return this.find({
      uploadDate: {
        $gte: startDate,
        $lte: endDate,
      },
    });
  }

  async findPendingProcessing(): Promise<Document[]> {
    return this.find({ status: 'uploaded' });
  }

  async updateStatus(id: string, status: string): Promise<Document | null> {
    return this.update(id, { status });
  }

  async addMetadata(id: string, metadata: Record<string, any>): Promise<Document | null> {
    return this.update(id, {
      $set: { metadata },
    });
  }

  async updateProcessingError(
    id: string,
    error: { message: string; stack?: string },
  ): Promise<Document | null> {
    return this.update(id, {
      $set: {
        processingError: {
          ...error,
          timestamp: new Date(),
        },
      },
      status: 'failed',
    });
  }
}

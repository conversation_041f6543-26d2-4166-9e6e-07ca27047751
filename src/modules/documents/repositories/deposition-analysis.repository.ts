import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { TenantAwareRepository } from '../../common/repositories/base.repository';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { 
  DepositionAnalysisDocument, 
  DEPOSITION_ANALYSIS_MODEL 
} from '../schemas/deposition-analysis.schema';

@Injectable()
export class DepositionAnalysisRepository extends TenantAwareRepository<DepositionAnalysisDocument> {
  constructor(
    @InjectModel(DEPOSITION_ANALYSIS_MODEL) depositionAnalysisModel: Model<DepositionAnalysisDocument>,
    tenantContext: TenantContextService,
  ) {
    super(depositionAnalysisModel, tenantContext);
  }

  async findByUserId(userId: string): Promise<DepositionAnalysisDocument[]> {
    return this.find({ userId });
  }

  async findByDepositionId(depositionId: string): Promise<DepositionAnalysisDocument[]> {
    const tenantFilter = this.addTenantContext({ depositionId });
    console.log('Finding deposition analyses with filter:', JSON.stringify(tenantFilter, null, 2));
    const results = await this.model.find(tenantFilter).exec();
    console.log(`Found ${results.length} analyses for deposition ${depositionId}`);
    return results;
  }

  async findByDateRange(startDate: Date, endDate: Date): Promise<DepositionAnalysisDocument[]> {
    return this.find({
      createdAt: {
        $gte: startDate,
        $lte: endDate,
      },
    });
  }

  async findByCredibilityScoreRange(minScore: number, maxScore: number): Promise<DepositionAnalysisDocument[]> {
    return this.find({
      overallCredibilityScore: {
        $gte: minScore,
        $lte: maxScore,
      },
    });
  }

  async findLatestAnalyses(limit: number = 10): Promise<DepositionAnalysisDocument[]> {
    const tenantFilter = this.addTenantContext({});
    return this.model.find(tenantFilter)
      .sort({ createdAt: -1 })
      .limit(limit)
      .exec();
  }
}

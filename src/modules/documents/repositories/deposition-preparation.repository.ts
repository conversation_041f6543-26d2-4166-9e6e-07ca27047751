import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { TenantAwareRepository } from '../../common/repositories/base.repository';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { 
  DepositionPreparationDocument, 
  DEPOSITION_PREPARATION_MODEL 
} from '../schemas/deposition-preparation.schema';
import { DepositionPreparationStatus } from '../interfaces/deposition.interface';

@Injectable()
export class DepositionPreparationRepository extends TenantAwareRepository<DepositionPreparationDocument> {
  constructor(
    @InjectModel(DEPOSITION_PREPARATION_MODEL) depositionPreparationModel: Model<DepositionPreparationDocument>,
    tenantContext: TenantContextService,
  ) {
    super(depositionPreparationModel, tenantContext);
  }

  async findByUserId(userId: string): Promise<DepositionPreparationDocument[]> {
    const results = await this.find({ userId });
    return results.map(doc => {
      // Ensure id field is set from _id if not present
      if (!doc.id && doc._id) {
        doc.id = doc._id.toString();
      }
      return doc;
    });
  }

  async findByCaseId(caseId: string): Promise<DepositionPreparationDocument[]> {
    return this.find({ caseId });
  }

  async findByStatus(status: DepositionPreparationStatus): Promise<DepositionPreparationDocument[]> {
    return this.find({ status });
  }

  // Override findById to handle both UUID `id` and MongoDB `_id`
  async findById(id: string): Promise<DepositionPreparationDocument | null> {
    // Try finding by stored id first
    let filter = this.addTenantContext({ id });
    let result = await this.model.findOne(filter).exec();

    // If not found, try finding by _id
    if (!result) {
      filter = this.addTenantContext({ _id: id });
      result = await this.model.findOne(filter).exec();
    }

    if (!result) {
      return null;
    }

    // Ensure id is set correctly
    if (!result.id && result._id) {
      result.id = result._id.toString();
    }
    
    return result;
  }

  async findOne(filter: any): Promise<DepositionPreparationDocument | null> {
    // If searching by 'id', try both id and _id fields
    if (filter.id) {
      const idFilter = this.addTenantContext({ ...filter });
      const _idFilter = this.addTenantContext({
        ...filter,
        _id: filter.id,
        id: undefined
      });

      let result = await this.model.findOne(idFilter).exec();
      if (!result) {
        result = await this.model.findOne(_idFilter).exec();
      }
      return result;
    }

    const tenantFilter = this.addTenantContext(filter);
    return await this.model.findOne(tenantFilter).exec();
  }

  // Override update to handle both UUID `id` and MongoDB `_id`
  async update(
    id: string,
    data: any,
  ): Promise<DepositionPreparationDocument | null> {
    // Try updating by stored id first
    let filter = this.addTenantContext({ id });
    let result = await this.model.findOneAndUpdate(filter, data, { new: true }).exec();

    // If not found, try updating by _id
    if (!result) {
      filter = this.addTenantContext({ _id: id });
      result = await this.model.findOneAndUpdate(filter, data, { new: true }).exec();
    }

    // Ensure id field is set correctly
    if (result && !result.id && result._id) {
      result.id = result._id.toString();
      await result.save();
    }

    return result;
  }

  async addQuestion(id: string, question: any): Promise<DepositionPreparationDocument | null> {
    // Ensure question has an id
    if (!question.id) {
      question.id = question._id?.toString() || new Date().getTime().toString();
    }
    
    const result = await this.update(id, {
      $push: { questions: question },
      updatedAt: new Date()
    });

    return result;
  }

  async updateQuestion(id: string, questionId: string, questionData: any): Promise<DepositionPreparationDocument | null> {
    // First try finding by stored id
    let filter = this.addTenantContext({ id });
    let result = await this.model.findOneAndUpdate(
      filter,
      {
        $set: {
          "questions.$[elem]": {
            ...questionData,
            id: questionId, // Ensure id is preserved
            updatedAt: new Date()
          },
          updatedAt: new Date()
        }
      },
      {
        arrayFilters: [{ "elem.id": questionId }],
        new: true
      }
    ).exec();

    // If not found, try by _id
    if (!result) {
      filter = this.addTenantContext({ _id: id });
      result = await this.model.findOneAndUpdate(
        filter,
        {
          $set: {
            "questions.$[elem]": {
              ...questionData,
              id: questionId,
              updatedAt: new Date()
            },
            updatedAt: new Date()
          }
        },
        {
          arrayFilters: [{ "elem.id": questionId }],
          new: true
        }
      ).exec();
    }

    // Ensure id field is set correctly
    if (result && !result.id && result._id) {
      result.id = result._id.toString();
      await result.save();
    }
    
    return result;
  }

  async removeQuestion(id: string, questionId: string): Promise<DepositionPreparationDocument | null> {
    // Try removing by stored id first
    let filter = this.addTenantContext({ id });
    let result = await this.model.findOneAndUpdate(
      filter,
      {
        $pull: { questions: { id: questionId } },
        updatedAt: new Date()
      },
      { new: true }
    ).exec();
    
    // If not found, try by _id
    if (!result) {
      filter = this.addTenantContext({ _id: id });
      result = await this.model.findOneAndUpdate(
        filter,
        {
          $pull: { questions: { id: questionId } },
          updatedAt: new Date()
        },
        { new: true }
      ).exec();
    }

    // Ensure id field is set correctly
    if (result && !result.id && result._id) {
      result.id = result._id.toString();
      await result.save();
    }
    
    return result;
  }

  // Override delete to handle both UUID `id` and MongoDB `_id`
  async delete(id: string): Promise<boolean> {
    // Try deleting by stored id first
    let filter = this.addTenantContext({ id });
    let result = await this.model.deleteOne(filter).exec();

    // If not found/deleted, try by _id
    if (result.deletedCount === 0) {
      filter = this.addTenantContext({ _id: id });
      result = await this.model.deleteOne(filter).exec();
    }

    return result.deletedCount > 0;
  }

  // Override find to ensure consistent id handling
  async find(filter: any = {}): Promise<DepositionPreparationDocument[]> {
    const tenantFilter = this.addTenantContext(filter);
    const results = await this.model.find(tenantFilter).exec();
    
    // Ensure each document has id field set correctly
    return results.map(doc => {
      if (!doc.id && doc._id) {
        doc.id = doc._id.toString();
      }
      return doc;
    });
  }
}

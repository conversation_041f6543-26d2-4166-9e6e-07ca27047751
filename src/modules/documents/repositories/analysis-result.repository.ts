import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose'; // Keep Types import
import { AnalysisResult, AnalysisResultDocument } from '../schemas/analysis-result.schema';

@Injectable()
export class AnalysisResultRepository {
  constructor(
    @InjectModel(AnalysisResult.name)
    private readonly analysisResultModel: Model<AnalysisResultDocument>,
  ) {}

  async save(
    analysisResultData: Partial<AnalysisResult>,
  ): Promise<AnalysisResultDocument> {
    const newAnalysisResult = new this.analysisResultModel(analysisResultData);
    return newAnalysisResult.save();
  }

  async findLatestByDocumentIdAndOrg(
    documentId: string, // Expect string
    organizationId: string, // Expect string
  ): Promise<AnalysisResultDocument | null> {
    return this.analysisResultModel
      .findOne({
        documentId: documentId, // Use string directly
        organizationId: organizationId, // Use string directly
      })
      .sort({ createdAt: -1 })
      .exec();
  }

  // Optional: Find all for history
  async findAllByDocumentIdAndOrg(
    documentId: string, // Expect string
    organizationId: string, // Expect string
  ): Promise<AnalysisResultDocument[]> {
    return this.analysisResultModel
      .find({
        documentId: documentId, // Use string directly
        organizationId: organizationId, // Use string directly
      })
      .sort({ createdAt: -1 })
      .exec();
  }

  /**
   * Find an analysis result by its ID
   * @param id - The ID of the analysis result to find
   * @returns The analysis result document or null if not found
   */
  async findById(id: string): Promise<AnalysisResultDocument | null> {
    return this.analysisResultModel.findById(id).exec();
  }
}

import { parentPort } from 'worker_threads';
import * as fs from 'fs';
import * as path from 'path';
import pdfParse from 'pdf-parse';

interface ProcessingMessage {
  filePath: string;
  mimeType: string;
}

interface ProcessingResult {
  text: string;
  pageCount?: number;
  metadata?: Record<string, unknown>;
  error?: string;
}

if (parentPort) {
  parentPort.on('message', async (message: ProcessingMessage) => {
    try {
      const result = await processFile(message.filePath, message.mimeType);
      parentPort?.postMessage(result);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      parentPort?.postMessage({
        text: '',
        error: errorMessage,
      });
    }
  });
}

async function processFile(filePath: string, mimeType: string): Promise<ProcessingResult> {
  console.log(`Worker attempting to process: ${filePath} (${mimeType})`);
  try {
    if (mimeType === 'text/plain') {
      // Process simple text files
      const content = await fs.promises.readFile(filePath, 'utf-8');
      const metadata: Record<string, unknown> = {
        fileType: 'text',
        fileName: path.basename(filePath),
        processedAt: new Date().toISOString(),
        characterCount: content.length,
        wordCount: content.split(/\s+/).filter(Boolean).length,
        lineCount: content.split('\n').length,
      };
      const CHARS_PER_PAGE = 3000;
      const pageCount = Math.ceil(content.length / CHARS_PER_PAGE);
      return {
        text: content,
        pageCount,
        metadata,
      };
    } else if (mimeType === 'application/pdf') {
      // Process PDF files using pdf-parse
      const dataBuffer = fs.readFileSync(filePath); // Read as buffer
      const data = await pdfParse(dataBuffer);
      return {
        text: data.text,
        pageCount: data.numpages,
        metadata: {
          ...data.metadata,
          fileType: 'pdf',
          fileName: path.basename(filePath),
          processedAt: new Date().toISOString(),
          pageCount: data.numpages,
          info: data.info,
        },
      };
    } else if (mimeType.includes('word') || mimeType.includes('docx')) {
       // Placeholder for Word files - throws error for now
      console.warn(`Processing for Word files (${mimeType}) is not yet implemented.`);
      // In a real app, use a library like mammoth.js
       return {
         text: `Placeholder text for Word file: ${path.basename(filePath)}. Full processing not implemented.`, // Return placeholder text
         pageCount: 1, // Placeholder
         metadata: {
           fileType: mimeType,
           fileName: path.basename(filePath),
           processedAt: new Date().toISOString(),
         }
       };
    } else {
      // Unsupported file type
      throw new Error(`Unsupported MIME type for processing: ${mimeType}`);
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`Error processing file ${filePath}: ${errorMessage}`); // Log the specific error
    // Re-throw the error to be caught by the parentPort handler
    throw new Error(`Error processing file ${filePath}: ${errorMessage}`);
  }
}

// Signal readiness to parent thread
if (parentPort) {
  parentPort.postMessage({ type: 'ready' });
  console.log('Document processing worker ready.'); // Added log
}

import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { DocumentCacheEntity } from './entities/document-cache.entity';
import * as path from 'path';

export const documentsTypeOrmConfig: TypeOrmModuleOptions = {
  type: 'sqlite',
  database: path.join(process.cwd(), 'uploads', 'cache', 'document-cache.db'),
  entities: [DocumentCacheEntity],
  synchronize: true,
  logging: process.env.NODE_ENV !== 'production',
};

export default documentsTypeOrmConfig;
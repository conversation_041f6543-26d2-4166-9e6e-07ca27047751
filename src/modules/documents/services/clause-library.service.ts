import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ClauseTemplate, ClauseTemplateDocument } from '../schemas/clause-template.schema';
import { CreateClauseTemplateDto, UpdateClauseTemplateDto, IdentifyClausesDto, GenerateTemplateDto, SearchClauseTemplatesDto } from '../dto/clause-template.dto';
import { DocumentComparisonService } from './document-comparison.service';
import { AIService } from '../../ai/services/ai.service';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { AnalyticsService } from '../../analytics/services/analytics.service';
import { ClauseMatch } from '../interfaces/clause-library.interface';
import { DocumentsService } from './documents.service';

@Injectable()
export class ClauseLibraryService {
  private readonly logger = new Logger(ClauseLibraryService.name);

  constructor(
    @InjectModel(ClauseTemplate.name) private clauseTemplateModel: Model<ClauseTemplateDocument>,
    private readonly documentComparisonService: DocumentComparisonService,
    private readonly aiService: AIService,
    private readonly tenantContextService: TenantContextService,
    private readonly analyticsService: AnalyticsService,
    private readonly documentsService: DocumentsService
  ) {}

  async createTemplate(createDto: CreateClauseTemplateDto, organizationId: string, userId: string): Promise<ClauseTemplate> {
    this.logger.log(`Creating new clause template: ${createDto.name} for organization: ${organizationId}`);
    
    const template = new this.clauseTemplateModel({
      ...createDto,
      organizationId,
      createdBy: userId
    });
    
    return template.save();
  }

  async findAll(organizationId: string, searchDto?: SearchClauseTemplatesDto): Promise<ClauseTemplate[]> {
    this.logger.debug(`Finding clause templates for organization: ${organizationId}`);
    
    const query: any = { organizationId };
    
    if (searchDto) {
      if (searchDto.category) {
        query.category = searchDto.category;
      }
      
      if (searchDto.tags && searchDto.tags.length > 0) {
        query.tags = { $in: searchDto.tags };
      }
      
      if (searchDto.includePublic) {
        query.$or = [
          { organizationId },
          { isPublic: true }
        ];
        delete query.organizationId;
      }
      
      if (searchDto.query) {
        return this.clauseTemplateModel.find(query)
          .find({ $text: { $search: searchDto.query } })
          .sort({ score: { $meta: 'textScore' } })
          .exec();
      }
    }
    
    return this.clauseTemplateModel.find(query).exec();
  }

  async findOne(id: string, organizationId: string): Promise<ClauseTemplate> {
    this.logger.debug(`Finding clause template with id: ${id} for organization: ${organizationId}`);
    
    const template = await this.clauseTemplateModel.findOne({
      _id: id,
      $or: [
        { organizationId },
        { isPublic: true }
      ]
    }).exec();
    
    if (!template) {
      throw new NotFoundException(`Clause template with ID ${id} not found`);
    }
    
    return template;
  }

  async identifyClauses(dto: IdentifyClausesDto, organizationId: string, userId: string): Promise<ClauseMatch[]> {
    this.logger.debug('=============== Starting Clause Identification ===============');
    this.logger.debug(`Getting content for document: ${dto.documentId}`);

    // Get document content
    const documentContent = await this.documentsService.getDocumentContent(dto.documentId);
    if (!documentContent) {
      throw new NotFoundException(`Document with ID ${dto.documentId} not found or has no content`);
    }

    // Track analytics with session ID
    const sessionId = `clause_library_${dto.documentId}_${new Date().getTime()}`;
    await this.analyticsService.trackClauseLibraryUsage(
      userId,
      organizationId,
      'clause_identification', 
      {
        documentId: dto.documentId,
        documentLength: documentContent.length,
        categories: dto.categories,
        sessionId
      }
    ).catch(error => {
      this.logger.warn(`Failed to track analytics: ${error.message}`);
    });
    
    // Get templates to compare against
    const query: any = {
      $or: [
        { organizationId },
        { isPublic: true }
      ]
    };
  
    this.logger.debug(`Query base: ${JSON.stringify(query)}`);
    this.logger.debug(`Requested categories: ${dto.categories ? JSON.stringify(dto.categories) : 'none'}`);
  
    // First, get ALL templates to check what categories are available
    const allTemplates = await this.clauseTemplateModel.find(query).exec();
    this.logger.warn('==== ALL AVAILABLE TEMPLATES ====');
    allTemplates.forEach((template, index) => {
      this.logger.warn(`Template ${index + 1}: ${template.name} - Category: "${template.category}"`);
    });
  
    // Instead of filtering by category in the database query, let's get all templates
    // and filter them in memory to handle partial matches
    let templates = allTemplates;
  
    if (dto.categories && dto.categories.length > 0) {
      this.logger.warn(`==== REQUESTED CATEGORIES: ${dto.categories.join(', ')} ====`);
      
      // Filter templates by category with partial matching
      templates = allTemplates.filter(template => {
        if (!template.category) return false;
        
        // Check if any requested category is a substring of the template category
        // or if the template category is a substring of any requested category
        return dto.categories.some(requestedCategory => {
          const templateCategory = template.category.toLowerCase();
          const requestedCategoryLower = requestedCategory.toLowerCase();
          
          return templateCategory.includes(requestedCategoryLower) || 
                 requestedCategoryLower.includes(templateCategory);
        });
      });
      
      this.logger.warn(`==== FILTERED TEMPLATES: ${templates.length} matches ====`);
      templates.forEach((template, index) => {
        this.logger.warn(`Filtered Template ${index + 1}: ${template.name} - Category: "${template.category}"`);
      });
    }
    
    if (templates.length === 0) {
      this.logger.warn(`No templates found for categories: ${dto.categories?.join(', ')}`);
      return [];
    }
    
    // Log details of each template for debugging
    templates.forEach((template, index) => {
      this.logger.debug(`Template ${index + 1}:`);
      this.logger.debug(`- ID: ${template.id}`);
      this.logger.debug(`- Name: ${template.name}`);
      this.logger.debug(`- Category: ${template.category}`);
      this.logger.debug(`- Content length: ${template.content?.length || 0} chars`);
      this.logger.debug(`- Is public: ${template.isPublic}`);
    });
    
    const similarityThreshold = dto.similarityThreshold || 0.5;
    
    try {
      // Perform template matching
      const matches = await this.performTemplateMatching(documentContent, templates, similarityThreshold, dto.matchingPreferences);
      this.logger.debug(`Found ${matches.length} direct matches`);
      
      // If few matches found, try AI identification
      if (matches.length === 0) {
        this.logger.debug('No direct matches found, trying AI identification...');
        const aiMatches = await this.performAIClauseIdentification(documentContent, templates, similarityThreshold, dto.matchingPreferences);
        this.logger.debug(`Found ${aiMatches.length} AI-assisted matches`);
        
        // Combine and deduplicate results
        const allMatches = this.deduplicateMatches([...matches, ...aiMatches]);
        this.logger.debug(`Final match count after deduplication: ${allMatches.length}`);
        return allMatches;
      }
      
      return matches;
    } catch (error) {
      this.logger.error(`Error identifying clauses: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to identify clauses: ${error.message}`);
    }
  }

  private async performTemplateMatching(
    documentContent: string, 
    templates: ClauseTemplate[], 
    similarityThreshold: number,
    matchingPreferences?: {
      ignoreNumbering?: boolean;
      useKeywordMatching?: boolean;
      handleSpecialFormats?: boolean;
      boostSimilarContent?: boolean;
    }
  ): Promise<ClauseMatch[]> {
    const matches: ClauseMatch[] = [];
    
    this.logger.debug('=============== Starting Template Matching ===============');
    this.logger.debug(`Number of templates to process: ${templates.length}`);
    this.logger.debug(`Similarity threshold: ${similarityThreshold}`);
    this.logger.debug(`Document preview: ${documentContent.substring(0, 200)}...`);
    this.logger.debug(`Document length: ${documentContent.length} characters`);

    // Log all template names and categories for debugging
    this.logger.warn('==== AVAILABLE TEMPLATES FOR MATCHING ====');
    templates.forEach((t, i) => {
      this.logger.warn(`${i+1}. ${t.name} (Category: "${t.category}") - Length: ${t.content.length} chars`);
    });
    this.logger.warn('=========================================');

    for (const template of templates) {
      this.logger.debug('\n--------------------------------------------------------');
      this.logger.debug(`Processing template: ${template.name}`);
      this.logger.debug(`Category: ${template.category}`);
      this.logger.debug(`Template content preview: ${template.content.substring(0, 100)}...`);
      this.logger.debug(`Template content length: ${template.content.length} characters`);
      
      try {
        // Log pre-comparison details
        this.logger.debug('Starting comparison with DocumentComparisonService.compareTextFragments');
        
        const comparisonResult = await this.documentComparisonService.compareTextFragments(
          documentContent,
          template.content,
          matchingPreferences
        );

        this.logger.warn(`==== COMPARISON RESULTS FOR "${template.name}" ====`);
        this.logger.warn(`- Similarity score: ${comparisonResult.similarity}`);
        this.logger.warn(`- Threshold for matching: ${similarityThreshold}`);
        this.logger.warn(`- Match? ${comparisonResult.similarity >= similarityThreshold ? 'YES' : 'NO'}`);
        this.logger.warn('==========================================');
        
        if (comparisonResult.matchPositions && comparisonResult.matchPositions.length > 0) {
          this.logger.debug(`- Match Positions: ${JSON.stringify(comparisonResult.matchPositions)}`);
          this.logger.debug(`- Longest match at: ${comparisonResult.matchPositions[0].startIndex} to ${comparisonResult.matchPositions[0].endIndex}`);
        } else {
          this.logger.debug('- No specific match positions found');
        }
        
        if (comparisonResult.similarity >= similarityThreshold) {
          this.logger.debug('MATCH FOUND! Adding to results.');
          matches.push({
            name: template.name,
            content: template.content,
            category: template.category,
            similarity: comparisonResult.similarity,
            templateId: template.id,
            startIndex: comparisonResult.matchPositions?.[0]?.startIndex,
            endIndex: comparisonResult.matchPositions?.[0]?.endIndex
          });
          
          // Update usage count
          await this.incrementUsageCount(template.id);
        } else {
          this.logger.debug(`No match: similarity ${comparisonResult.similarity} below threshold ${similarityThreshold}`);
        }
      } catch (error) {
        this.logger.error(`Error comparing with template ${template.name}:`, error.message);
        this.logger.error(`Error stack: ${error.stack}`);
      }
    }

    this.logger.debug('\n=============== Matching Complete ===============');
    this.logger.debug(`Total matches found: ${matches.length}`);
    return matches;
  }

  private async performAIClauseIdentification(
    documentContent: string, 
    templates: ClauseTemplate[], 
    similarityThreshold: number,
    matchingPreferences?: {
      ignoreNumbering?: boolean;
      useKeywordMatching?: boolean;
      handleSpecialFormats?: boolean;
      boostSimilarContent?: boolean;
    }
  ): Promise<ClauseMatch[]> {
    this.logger.debug('Starting AI clause identification');
    
    // Use AI to identify potential clause sections
    const potentialClauses = await this.aiService.identifyClauseSections(documentContent);
    const matches: ClauseMatch[] = [];
    
    // Compare each potential clause with templates
    for (const clause of potentialClauses) {
      for (const template of templates) {
        const comparisonResult = await this.documentComparisonService.compareTextFragments(
          clause.content,
          template.content,
          matchingPreferences
        );
        
        if (comparisonResult.similarity >= similarityThreshold) {
          matches.push({
            name: template.name,
            content: template.content,
            category: template.category,
            similarity: comparisonResult.similarity,
            templateId: template.id,
            startIndex: clause.startIndex,
            endIndex: clause.endIndex
          });
          
          await this.incrementUsageCount(template.id);
        }
      }
    }
    
    return matches;
  }

  private deduplicateMatches(matches: ClauseMatch[]): ClauseMatch[] {
    // Sort by similarity (highest first)
    const sortedMatches = [...matches].sort((a, b) => b.similarity - a.similarity);
    
    // Deduplicate based on overlapping positions
    const deduplicated: ClauseMatch[] = [];
    const usedRanges: Array<{start: number, end: number}> = [];
    
    for (const match of sortedMatches) {
      // Skip if no position info
      if (match.startIndex === undefined || match.endIndex === undefined) {
        deduplicated.push(match);
        continue;
      }
      
      // Check for overlap with existing matches
      const hasOverlap = usedRanges.some(range => {
        return (match.startIndex <= range.end && match.endIndex >= range.start);
      });
      
      if (!hasOverlap) {
        deduplicated.push(match);
        usedRanges.push({
          start: match.startIndex,
          end: match.endIndex
        });
      }
    }
    
    return deduplicated;
  }

  async generateTemplate(dto: GenerateTemplateDto, organizationId: string, userId: string): Promise<ClauseTemplate> {
    this.logger.log(`Generating template for organization: ${organizationId}`);
    
    try {
      // Use AI to optimize the template content
      const optimizedContent = await this.aiService.optimizeClauseTemplate(dto.documentContent);
      
      // Create the template
      const template = new this.clauseTemplateModel({
        name: dto.name,
        content: optimizedContent,
        category: dto.category,
        tags: dto.tags || [],
        organizationId,
        createdBy: userId,
        metadata: {
          generatedFrom: 'document',
          generatedAt: new Date().toISOString()
        }
      });
      
      // Track analytics
      await this.analyticsService.trackClauseLibraryUsage(
        userId,
        organizationId,
        'template_generation',
        {
          templateId: template.id,
          templateName: dto.name,
          category: dto.category
        }
      );
      
      return template.save();
    } catch (error) {
      this.logger.error(`Error generating template: ${error.message}`, error.stack);
      throw new BadRequestException('Failed to generate template. Please ensure the input content is valid.');
    }
  }

  async incrementUsageCount(templateId: string): Promise<void> {
    await this.clauseTemplateModel.updateOne(
      { _id: templateId },
      { $inc: { usageCount: 1 } }
    ).exec();
  }

  async updateEffectivenessScore(templateId: string, score: number): Promise<void> {
    await this.clauseTemplateModel.updateOne(
      { _id: templateId },
      { effectivenessScore: score }
    ).exec();
  }
}

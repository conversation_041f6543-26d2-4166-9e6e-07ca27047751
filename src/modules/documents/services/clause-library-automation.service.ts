import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  ClauseTemplate,
  ClauseTemplateDocument,
} from '../schemas/clause-template.schema';
import { Document, DOCUMENT_MODEL } from '../schemas/document.schema';
import { LegalPatternRecognitionService } from './legal-pattern-recognition.service';
import { DocumentsService } from './documents.service';
import { AIService } from '../../ai/services/ai.service';
import { AnalyticsService } from '../../analytics/services/analytics.service';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { QUEUES } from '../../queue/constants';
import { DocumentProcessingJobType } from '../interfaces/document-processing.types';
import { LegalPattern } from '../interfaces/legal-pattern.interface';

export interface ClauseExtractionResult {
  extractedClauses: Array<{
    content: string;
    category: string;
    confidence: number;
    sourceDocument: string;
    patternType: string;
  }>;
  totalDocumentsAnalyzed: number;
  totalClausesExtracted: number;
  categoriesFound: string[];
  processingDurationMs: number;
}

export interface ClauseLibraryBuildOptions {
  includeExistingClauses?: boolean;
  minimumConfidence?: number;
  maxClausesPerCategory?: number;
  documentTypes?: string[];
  analysisDepth?: 'basic' | 'detailed' | 'comprehensive';
}

@Injectable()
export class ClauseLibraryAutomationService {
  private readonly logger = new Logger(ClauseLibraryAutomationService.name);

  constructor(
    @InjectModel(ClauseTemplate.name)
    private clauseTemplateModel: Model<ClauseTemplateDocument>,
    @InjectModel(DOCUMENT_MODEL)
    private documentModel: Model<Document>,
    private readonly patternRecognitionService: LegalPatternRecognitionService,
    private readonly documentsService: DocumentsService,
    private readonly aiService: AIService,
    private readonly analyticsService: AnalyticsService,
    @InjectQueue(QUEUES.DOCUMENT_PROCESSING)
    private readonly documentProcessingQueue: Queue,
  ) {}

  /**
   * Build clause library from organization's document corpus
   */
  async buildClauseLibraryFromCorpus(
    organizationId: string,
    userId: string,
    options: ClauseLibraryBuildOptions = {},
  ): Promise<ClauseExtractionResult> {
    const startTime = Date.now();
    this.logger.log(
      `Building clause library for organization: ${organizationId}`,
    );

    try {
      // Get organization documents
      const documents = await this.getOrganizationDocuments(
        organizationId,
        options.documentTypes,
      );

      if (documents.length === 0) {
        return {
          extractedClauses: [],
          totalDocumentsAnalyzed: 0,
          totalClausesExtracted: 0,
          categoriesFound: [],
          processingDurationMs: Date.now() - startTime,
        };
      }

      // Analyze documents for patterns and extract clauses
      const extractedClauses = [];
      const categoriesFound = new Set<string>();

      for (const document of documents) {
        try {
          const clauses = await this.extractClausesFromDocument(
            document,
            organizationId,
            options,
          );
          extractedClauses.push(...clauses);
          clauses.forEach((clause) => categoriesFound.add(clause.category));
        } catch (error) {
          this.logger.warn(
            `Failed to extract clauses from document ${document._id}: ${error.message}`,
          );
        }
      }

      // Filter by confidence threshold
      const minimumConfidence = options.minimumConfidence || 0.7;
      const filteredClauses = extractedClauses.filter(
        (clause) => clause.confidence >= minimumConfidence,
      );

      // Limit clauses per category
      const maxPerCategory = options.maxClausesPerCategory || 10;
      const finalClauses = this.limitClausesPerCategory(
        filteredClauses,
        maxPerCategory,
      );

      // Save to clause library
      await this.saveClausesToLibrary(finalClauses, organizationId, userId);

      const result: ClauseExtractionResult = {
        extractedClauses: finalClauses,
        totalDocumentsAnalyzed: documents.length,
        totalClausesExtracted: finalClauses.length,
        categoriesFound: Array.from(categoriesFound),
        processingDurationMs: Date.now() - startTime,
      };

      // Track analytics
      await this.analyticsService.trackDocumentAutomation(
        userId,
        organizationId,
        'clause_library_auto_population',
        {
          documentsAnalyzed: result.totalDocumentsAnalyzed,
          clausesExtracted: result.totalClausesExtracted,
          categoriesFound: result.categoriesFound.length,
          processingDurationMs: result.processingDurationMs,
        },
      );

      this.logger.log(
        `Clause library building completed: ${result.totalClausesExtracted} clauses from ${result.totalDocumentsAnalyzed} documents`,
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Error building clause library: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Extract clauses from a single document using pattern analysis
   */
  private async extractClausesFromDocument(
    document: Document,
    organizationId: string,
    options: ClauseLibraryBuildOptions,
  ): Promise<
    Array<{
      content: string;
      category: string;
      confidence: number;
      sourceDocument: string;
      patternType: string;
    }>
  > {
    try {
      // Get document metadata to check for existing patterns
      const metadata = await this.documentsService.getMetadata(
        document._id.toString(),
      );

      let patterns: LegalPattern[] = [];

      if (metadata?.patterns) {
        patterns = metadata.patterns as LegalPattern[];
      } else {
        // Run pattern analysis if not already done
        await this.analyzeDocumentPatterns(document._id.toString());
        // Wait a bit for processing and retry
        await new Promise((resolve) => setTimeout(resolve, 2000));
        const updatedMetadata = await this.documentsService.getMetadata(
          document._id.toString(),
        );
        patterns = (updatedMetadata?.patterns as LegalPattern[]) || [];
      }

      // Extract clauses from patterns using AI
      const clauses = await this.extractClausesFromPatterns(
        patterns,
        document.content,
        document._id.toString(),
        options.analysisDepth || 'detailed',
      );

      return clauses;
    } catch (error) {
      this.logger.warn(
        `Failed to extract clauses from document ${document._id}: ${error.message}`,
      );
      return [];
    }
  }

  /**
   * Extract clauses from legal patterns using AI analysis
   */
  private async extractClausesFromPatterns(
    patterns: LegalPattern[],
    documentContent: string,
    documentId: string,
    analysisDepth: 'basic' | 'detailed' | 'comprehensive',
  ): Promise<
    Array<{
      content: string;
      category: string;
      confidence: number;
      sourceDocument: string;
      patternType: string;
    }>
  > {
    if (patterns.length === 0) {
      return [];
    }

    try {
      const prompt = this.buildClauseExtractionPrompt(
        patterns,
        documentContent,
        analysisDepth,
      );

      const response = await this.aiService.generateResponse(prompt, {
        temperature: 0.2, // Very low temperature for precise extraction
        systemMessage:
          'You are an expert legal analyst specializing in clause identification and categorization.',
        responseFormat: { type: 'json_object' },
      });

      // Parse AI response
      const cleanResponse = response.replace(/```json\n|\n```/g, '').trim();
      const extractionResult = JSON.parse(cleanResponse);

      if (extractionResult.clauses && Array.isArray(extractionResult.clauses)) {
        return extractionResult.clauses.map((clause: any) => ({
          content: clause.content || '',
          category: clause.category || 'general',
          confidence: Math.min(Math.max(clause.confidence || 0.5, 0), 1),
          sourceDocument: documentId,
          patternType: clause.patternType || 'unknown',
        }));
      }

      return [];
    } catch (error) {
      this.logger.warn(`Failed to extract clauses using AI: ${error.message}`);
      return [];
    }
  }

  /**
   * Build prompt for AI clause extraction
   */
  private buildClauseExtractionPrompt(
    patterns: LegalPattern[],
    documentContent: string,
    analysisDepth: string,
  ): string {
    let prompt = `Analyze the following legal document and extract reusable clauses based on the identified patterns.\n\n`;

    prompt += `DOCUMENT CONTENT (first 2000 chars):\n${documentContent.substring(
      0,
      2000,
    )}...\n\n`;

    prompt += `IDENTIFIED PATTERNS:\n`;
    patterns.slice(0, 10).forEach((pattern, index) => {
      const patternText =
        pattern.text_snippet || pattern.content || 'No text available';
      const patternType = pattern.pattern_name || pattern.type || 'Unknown';
      prompt += `${
        index + 1
      }. Type: ${patternType}, Text: "${patternText.substring(0, 100)}..."\n`;
    });

    prompt += `\nEXTRACTION REQUIREMENTS:\n`;
    prompt += `- Extract complete, standalone clauses that can be reused in other documents\n`;
    prompt += `- Categorize each clause (e.g., termination, payment, liability, confidentiality, etc.)\n`;
    prompt += `- Provide confidence score (0.0-1.0) for each extraction\n`;
    prompt += `- Focus on ${analysisDepth} analysis\n`;

    if (analysisDepth === 'comprehensive') {
      prompt += `- Include variations and alternative phrasings\n`;
      prompt += `- Extract both standard and unique clauses\n`;
    } else if (analysisDepth === 'detailed') {
      prompt += `- Focus on well-formed, complete clauses\n`;
      prompt += `- Prioritize commonly reusable clauses\n`;
    } else {
      prompt += `- Extract only the most obvious and reusable clauses\n`;
    }

    prompt += `\nRESPONSE FORMAT (JSON):\n`;
    prompt += `{
      "clauses": [
        {
          "content": "Complete clause text here",
          "category": "clause_category",
          "confidence": 0.85,
          "patternType": "pattern_type_that_identified_this"
        }
      ]
    }`;

    return prompt;
  }

  /**
   * Get organization documents for analysis
   */
  private async getOrganizationDocuments(
    organizationId: string,
    documentTypes?: string[],
  ): Promise<Document[]> {
    const query: any = { organizationId };

    if (documentTypes && documentTypes.length > 0) {
      query.documentType = { $in: documentTypes };
    }

    return this.documentModel
      .find(query)
      .limit(50) // Limit for performance
      .sort({ createdAt: -1 }) // Most recent first
      .exec();
  }

  /**
   * Analyze document patterns if not already done
   */
  private async analyzeDocumentPatterns(documentId: string): Promise<void> {
    try {
      await this.documentProcessingQueue.add(
        DocumentProcessingJobType.ANALYZE_PATTERNS,
        { documentId },
        {
          attempts: 2,
          backoff: { type: 'exponential', delay: 1000 },
        },
      );
    } catch (error) {
      this.logger.warn(
        `Failed to queue pattern analysis for ${documentId}: ${error.message}`,
      );
    }
  }

  /**
   * Limit clauses per category to avoid overwhelming the library
   */
  private limitClausesPerCategory(
    clauses: Array<{
      content: string;
      category: string;
      confidence: number;
      sourceDocument: string;
      patternType: string;
    }>,
    maxPerCategory: number,
  ): Array<{
    content: string;
    category: string;
    confidence: number;
    sourceDocument: string;
    patternType: string;
  }> {
    const categoryGroups = new Map<string, typeof clauses>();

    // Group by category
    clauses.forEach((clause) => {
      if (!categoryGroups.has(clause.category)) {
        categoryGroups.set(clause.category, []);
      }
      categoryGroups.get(clause.category)!.push(clause);
    });

    // Limit each category and sort by confidence
    const limitedClauses: typeof clauses = [];
    categoryGroups.forEach((categoryClause, category) => {
      const sorted = categoryClause
        .sort((a, b) => b.confidence - a.confidence)
        .slice(0, maxPerCategory);
      limitedClauses.push(...sorted);
    });

    return limitedClauses;
  }

  /**
   * Save extracted clauses to the clause library
   */
  private async saveClausesToLibrary(
    clauses: Array<{
      content: string;
      category: string;
      confidence: number;
      sourceDocument: string;
      patternType: string;
    }>,
    organizationId: string,
    userId: string,
  ): Promise<void> {
    for (const clause of clauses) {
      try {
        // Check if similar clause already exists
        const existingClause = await this.clauseTemplateModel.findOne({
          organizationId,
          category: clause.category,
          content: { $regex: clause.content.substring(0, 50), $options: 'i' },
        });

        if (!existingClause) {
          await this.clauseTemplateModel.create({
            name: `Auto-extracted ${clause.category} clause`,
            category: clause.category,
            content: clause.content,
            organizationId,
            createdBy: userId,
            isActive: true,
            metadata: {
              autoExtracted: true,
              sourceDocument: clause.sourceDocument,
              patternType: clause.patternType,
              confidence: clause.confidence,
              extractedAt: new Date(),
            },
          });
        }
      } catch (error) {
        this.logger.warn(`Failed to save clause to library: ${error.message}`);
      }
    }
  }
}

import { Test, TestingModule } from '@nestjs/testing';
import { NegotiationPlaybookService } from './negotiation-playbook.service';
import { AIService } from '../../ai/services/ai.service';
import { DocumentProcessingService } from './document-processing.service';
import { getModelToken } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { NotFoundException } from '@nestjs/common';
import {
  NegotiationPlaybook,
  NegotiationStrategy,
} from '../interfaces/negotiation-playbook.interface';

describe('NegotiationPlaybookService', () => {
  let service: NegotiationPlaybookService;
  let aiService: AIService;
  let documentProcessingService: DocumentProcessingService;
  let negotiationPlaybookModel: Model<any>;

  const mockDocument = {
    id: 'doc123',
    organizationId: 'org123',
    filename: 'test-contract.pdf',
    originalName: 'test-contract.pdf',
    content:
      'This is a sample contract document with termination clauses and liability provisions.',
    size: 1024,
    uploadDate: new Date(),
    status: 'completed' as const,
    metadata: {},
  };

  const mockPlaybook: NegotiationPlaybook = {
    documentId: 'doc123',
    strategies: [
      {
        section: 'Termination Clause',
        recommendations: [
          'Request clearer termination conditions',
          'Propose 30-day cure period',
        ],
        riskLevel: 'medium',
        priority: 2,
        alternativeLanguage:
          'Either party may terminate this agreement with 30 days written notice.',
        simulationScenarios: [
          {
            type: 'concession',
            trigger: 'counterparty requests indefinite liability',
            responseStrategy: 'Offer time-bound liability cap',
            expectedOutcome: 'Mutual agreement on 2-year liability cap',
          },
        ],
      },
    ],
    overallAssessment:
      'This contract has several negotiable points that favor the counterparty.',
    keyLeveragePoints: ['Payment terms', 'Intellectual property rights'],
    dealBreakers: ['Unlimited liability', 'Non-compete clause'],
    timestamp: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NegotiationPlaybookService,
        {
          provide: AIService,
          useValue: {
            generateResponse: jest.fn(),
          },
        },
        {
          provide: DocumentProcessingService,
          useValue: {
            getDocumentById: jest.fn(),
          },
        },
        {
          provide: getModelToken('NegotiationPlaybook'),
          useValue: jest.fn().mockImplementation((data) => ({
            ...data,
            save: jest.fn().mockResolvedValue({ ...data, _id: 'playbook123' }),
          })),
        },
      ],
    }).compile();

    service = module.get<NegotiationPlaybookService>(
      NegotiationPlaybookService,
    );
    aiService = module.get<AIService>(AIService);
    documentProcessingService = module.get<DocumentProcessingService>(
      DocumentProcessingService,
    );
    negotiationPlaybookModel = module.get<Model<any>>(
      getModelToken('NegotiationPlaybook'),
    );

    // Add static methods to the mock model
    negotiationPlaybookModel.findOne = jest.fn().mockReturnValue({
      exec: jest.fn(),
    });
    negotiationPlaybookModel.find = jest.fn();

    // Reset all mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generatePlaybook', () => {
    it('should successfully generate a negotiation playbook', async () => {
      // Mock AI response
      const mockAIResponse = JSON.stringify({
        strategies: [
          {
            section: 'Termination Clause',
            recommendations: [
              'Request clearer termination conditions',
              'Propose 30-day cure period',
            ],
            riskLevel: 'medium',
            priority: 2,
            alternativeLanguage:
              'Either party may terminate with 30 days notice.',
            simulationScenarios: [
              {
                type: 'concession',
                trigger: 'counterparty requests indefinite liability',
                responseStrategy: 'Offer time-bound liability cap',
                expectedOutcome: 'Mutual agreement on 2-year liability cap',
              },
            ],
          },
        ],
        overallAssessment: 'This contract has several negotiable points',
        keyLeveragePoints: ['Payment terms', 'Intellectual property'],
        dealBreakers: ['Unlimited liability'],
      });

      // Setup mocks
      jest
        .spyOn(documentProcessingService, 'getDocumentById')
        .mockResolvedValue(mockDocument);
      jest
        .spyOn(aiService, 'generateResponse')
        .mockResolvedValue(mockAIResponse);

      // Call service
      const result = await service.generatePlaybook('doc123', {
        documentType: 'CONTRACT',
        includeSimulations: true,
        focusAreas: ['termination', 'liability'],
        organizationPreferences: 'Prefer shorter notice periods',
      });

      // Assertions
      expect(documentProcessingService.getDocumentById).toHaveBeenCalledWith(
        'doc123',
      );
      expect(aiService.generateResponse).toHaveBeenCalledWith(
        expect.stringContaining('Analyze the following CONTRACT'),
        {
          temperature: 0.2,
          systemMessage:
            'You are an expert legal negotiator. Analyze the document and provide strategic negotiation recommendations in JSON format.',
          responseFormat: 'json',
        },
      );
      expect(result).toHaveProperty('documentId', 'doc123');
      expect(result).toHaveProperty('strategies');
      expect(result.strategies).toHaveLength(1);
      expect(result.strategies[0].section).toBe('Termination Clause');
      expect(result.strategies[0].recommendations).toHaveLength(2);
      expect(result).toHaveProperty('overallAssessment');
      expect(result).toHaveProperty('keyLeveragePoints');
      expect(result).toHaveProperty('dealBreakers');
      expect(result).toHaveProperty('timestamp');
    });

    it('should throw NotFoundException when document is not found', async () => {
      // Setup mock to return null
      jest
        .spyOn(documentProcessingService, 'getDocumentById')
        .mockResolvedValue(null);

      // Call service and expect error
      await expect(
        service.generatePlaybook('nonexistent-doc', {
          documentType: 'CONTRACT',
        }),
      ).rejects.toThrow(NotFoundException);
      await expect(
        service.generatePlaybook('nonexistent-doc', {
          documentType: 'CONTRACT',
        }),
      ).rejects.toThrow('Document with ID nonexistent-doc not found');
    });

    it('should handle AI service errors gracefully', async () => {
      // Setup mocks
      jest
        .spyOn(documentProcessingService, 'getDocumentById')
        .mockResolvedValue(mockDocument);
      jest
        .spyOn(aiService, 'generateResponse')
        .mockRejectedValue(new Error('AI service unavailable'));

      // Call service and expect error
      await expect(
        service.generatePlaybook('doc123', {
          documentType: 'CONTRACT',
        }),
      ).rejects.toThrow('AI service unavailable');
    });

    it('should handle invalid JSON response from AI service', async () => {
      // Setup mocks
      jest
        .spyOn(documentProcessingService, 'getDocumentById')
        .mockResolvedValue(mockDocument);
      jest
        .spyOn(aiService, 'generateResponse')
        .mockResolvedValue('invalid json response');

      // Call service and expect error
      await expect(
        service.generatePlaybook('doc123', {
          documentType: 'CONTRACT',
        }),
      ).rejects.toThrow('Failed to generate negotiation playbook');
    });

    it('should generate playbook without simulations when includeSimulations is false', async () => {
      // Mock AI response without simulation scenarios
      const mockAIResponse = JSON.stringify({
        strategies: [
          {
            section: 'Payment Terms',
            recommendations: ['Negotiate shorter payment periods'],
            riskLevel: 'low',
            priority: 1,
          },
        ],
        overallAssessment: 'Favorable contract terms',
        keyLeveragePoints: ['Payment timing'],
        dealBreakers: [],
      });

      // Setup mocks
      jest
        .spyOn(documentProcessingService, 'getDocumentById')
        .mockResolvedValue(mockDocument);
      jest
        .spyOn(aiService, 'generateResponse')
        .mockResolvedValue(mockAIResponse);

      // Call service
      const result = await service.generatePlaybook('doc123', {
        documentType: 'CONTRACT',
        includeSimulations: false,
      });

      // Verify the prompt doesn't include simulation instructions
      const aiCallArgs = (aiService.generateResponse as jest.Mock).mock
        .calls[0];
      expect(aiCallArgs[0]).not.toContain('Simulation scenarios');
      expect(result.strategies[0]).not.toHaveProperty('simulationScenarios');
    });

    it('should include focus areas in the prompt when provided', async () => {
      // Mock AI response
      const mockAIResponse = JSON.stringify(mockPlaybook);

      // Setup mocks
      jest
        .spyOn(documentProcessingService, 'getDocumentById')
        .mockResolvedValue(mockDocument);
      jest
        .spyOn(aiService, 'generateResponse')
        .mockResolvedValue(mockAIResponse);

      // Call service with focus areas
      await service.generatePlaybook('doc123', {
        documentType: 'CONTRACT',
        focusAreas: ['liability', 'termination', 'payment'],
      });

      // Verify the prompt includes focus areas
      const aiCallArgs = (aiService.generateResponse as jest.Mock).mock
        .calls[0];
      expect(aiCallArgs[0]).toContain(
        'Pay special attention to these areas: liability, termination, payment',
      );
    });

    it('should include organization preferences in the prompt when provided', async () => {
      // Mock AI response
      const mockAIResponse = JSON.stringify(mockPlaybook);

      // Setup mocks
      jest
        .spyOn(documentProcessingService, 'getDocumentById')
        .mockResolvedValue(mockDocument);
      jest
        .spyOn(aiService, 'generateResponse')
        .mockResolvedValue(mockAIResponse);

      // Call service with organization preferences
      await service.generatePlaybook('doc123', {
        documentType: 'CONTRACT',
        organizationPreferences:
          'Prefer shorter contract terms and lower liability',
      });

      // Verify the prompt includes organization preferences
      const aiCallArgs = (aiService.generateResponse as jest.Mock).mock
        .calls[0];
      expect(aiCallArgs[0]).toContain(
        'Consider these organizational preferences: Prefer shorter contract terms and lower liability',
      );
    });
  });

  describe('savePlaybook', () => {
    it('should save a playbook to the database', async () => {
      // Call service
      const result = await service.savePlaybook(mockPlaybook);

      // Assertions
      expect(negotiationPlaybookModel).toHaveBeenCalledWith(mockPlaybook);
      expect(result).toHaveProperty('_id', 'playbook123');
    });

    it('should handle database save errors', async () => {
      // Mock the model constructor to return an object with a failing save method
      const mockSave = jest.fn().mockRejectedValue(new Error('Database error'));
      (negotiationPlaybookModel as any).mockImplementationOnce(() => ({
        save: mockSave,
      }));

      // Call service and expect error
      await expect(service.savePlaybook(mockPlaybook)).rejects.toThrow(
        'Database error',
      );
    });
  });

  describe('getPlaybookByDocumentId', () => {
    it('should retrieve a playbook by document ID', async () => {
      // Setup mock
      const mockExec = jest.fn().mockResolvedValue(mockPlaybook);
      (negotiationPlaybookModel.findOne as jest.Mock).mockReturnValue({
        exec: mockExec,
      });

      // Call service
      const result = await service.getPlaybookByDocumentId('doc123');

      // Assertions
      expect(negotiationPlaybookModel.findOne).toHaveBeenCalledWith({
        documentId: 'doc123',
      });
      expect(mockExec).toHaveBeenCalled();
      expect(result).toEqual(mockPlaybook);
    });

    it('should return null when playbook is not found', async () => {
      // Setup mock to return null
      const mockExec = jest.fn().mockResolvedValue(null);
      (negotiationPlaybookModel.findOne as jest.Mock).mockReturnValue({
        exec: mockExec,
      });

      // Call service
      const result = await service.getPlaybookByDocumentId('nonexistent-doc');

      // Assertions
      expect(negotiationPlaybookModel.findOne).toHaveBeenCalledWith({
        documentId: 'nonexistent-doc',
      });
      expect(result).toBeNull();
    });

    it('should handle database query errors', async () => {
      // Setup mock to throw error
      const mockExec = jest
        .fn()
        .mockRejectedValue(new Error('Database connection error'));
      (negotiationPlaybookModel.findOne as jest.Mock).mockReturnValue({
        exec: mockExec,
      });

      // Call service and expect error
      await expect(service.getPlaybookByDocumentId('doc123')).rejects.toThrow(
        'Database connection error',
      );
    });
  });

  describe('generateNegotiationPrompt (private method)', () => {
    it('should generate a comprehensive prompt with all options', () => {
      // Access private method for testing
      const generatePrompt = (service as any).generateNegotiationPrompt;

      const prompt = generatePrompt(
        'Sample contract content',
        'CONTRACT',
        ['liability', 'termination'],
        true,
        'Prefer shorter terms',
      );

      // Verify prompt contains all expected elements
      expect(prompt).toContain('Analyze the following CONTRACT');
      expect(prompt).toContain('Sample contract content');
      expect(prompt).toContain(
        'Pay special attention to these areas: liability, termination',
      );
      expect(prompt).toContain(
        'Consider these organizational preferences: Prefer shorter terms',
      );
      expect(prompt).toContain('Simulation scenarios');
      expect(prompt).toContain('Format your response as a JSON object');
    });

    it('should generate prompt without optional parameters', () => {
      const generatePrompt = (service as any).generateNegotiationPrompt;

      const prompt = generatePrompt(
        'Sample contract content',
        'NDA',
        undefined,
        false,
        undefined,
      );

      // Verify prompt contains basic elements but not optional ones
      expect(prompt).toContain('Analyze the following NDA');
      expect(prompt).toContain('Sample contract content');
      expect(prompt).not.toContain('Pay special attention to these areas');
      expect(prompt).not.toContain('Consider these organizational preferences');
      expect(prompt).not.toContain('Simulation scenarios');
    });

    it('should include simulation scenarios by default when includeSimulations is undefined', () => {
      const generatePrompt = (service as any).generateNegotiationPrompt;

      const prompt = generatePrompt(
        'Sample contract content',
        'CONTRACT',
        undefined,
        undefined,
        undefined,
      );

      // Verify simulation scenarios are included by default
      expect(prompt).toContain('Simulation scenarios');
    });
  });

  describe('Error handling and edge cases', () => {
    it('should handle empty strategies array from AI response', async () => {
      // Mock AI response with empty strategies
      const mockAIResponse = JSON.stringify({
        strategies: [],
        overallAssessment: 'No significant negotiation points found',
        keyLeveragePoints: [],
        dealBreakers: [],
      });

      // Setup mocks
      jest
        .spyOn(documentProcessingService, 'getDocumentById')
        .mockResolvedValue(mockDocument);
      jest
        .spyOn(aiService, 'generateResponse')
        .mockResolvedValue(mockAIResponse);

      // Call service
      const result = await service.generatePlaybook('doc123', {
        documentType: 'CONTRACT',
      });

      // Assertions
      expect(result.strategies).toEqual([]);
      expect(result.overallAssessment).toBe(
        'No significant negotiation points found',
      );
    });

    it('should handle partial AI response with missing fields', async () => {
      // Mock AI response with missing optional fields
      const mockAIResponse = JSON.stringify({
        strategies: [
          {
            section: 'Payment Terms',
            recommendations: ['Negotiate payment schedule'],
          },
        ],
        overallAssessment: 'Basic assessment',
        keyLeveragePoints: ['Payment timing'],
      });

      // Setup mocks
      jest
        .spyOn(documentProcessingService, 'getDocumentById')
        .mockResolvedValue(mockDocument);
      jest
        .spyOn(aiService, 'generateResponse')
        .mockResolvedValue(mockAIResponse);

      // Call service
      const result = await service.generatePlaybook('doc123', {
        documentType: 'CONTRACT',
      });

      // Assertions
      expect(result.strategies).toHaveLength(1);
      expect(result.dealBreakers).toEqual([]);
      expect(result).toHaveProperty('timestamp');
    });
  });
});

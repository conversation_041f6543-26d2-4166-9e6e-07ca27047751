import {
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
  HttpException,
  HttpStatus,
  BadRequestException,
} from '@nestjs/common';

class RateLimitExceedException extends HttpException {
  constructor(waitTimeSeconds: number, resetTime: Date) {
    super({
      status: HttpStatus.TOO_MANY_REQUESTS,
      error: 'Rate limit exceeded',
      message: `Please wait ${waitTimeSeconds} seconds before requesting another analysis`,
      retryAfter: waitTimeSeconds,
      remaining: 0,
      limit: 1,
      resetTime: resetTime.toISOString()
    }, HttpStatus.TOO_MANY_REQUESTS);

    Object.defineProperty(this, 'headers', {
      value: {
        'Retry-After': waitTimeSeconds.toString(),
        'X-RateLimit-Limit': '1',
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': Math.ceil(resetTime.getTime() / 1000).toString()
      }
    });
  }
}
import { v4 as uuidv4 } from 'uuid';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { AIService } from '../../ai/services/ai.service';
import { DocumentsService } from './documents.service';
import { DepositionPreparationRepository } from '../repositories/deposition-preparation.repository';
import { DepositionAnalysisRepository } from '../repositories/deposition-analysis.repository';

import {
  DepositionQuestion,
  DepositionQuestionCategory,
  DepositionQuestionMetadata,
  QuestionGenerationResult,
  GenerateQuestionsDto,
  DepositionPreparation,
  DepositionPreparationStatus,
  AIQuestionResponse,
  DepositionAnalysisResult,
  AnalyzeDepositionDto,
  CreateDepositionPreparationDto,
  UpdateDepositionPreparationDto,
  CreateDepositionQuestionDto,
  UpdateDepositionQuestionDto,
  DepositionTestimonyAnalysis,
  DepositionInconsistency,
  CrossExaminationSuggestion,
} from '../interfaces/deposition.interface';

@Injectable()
export class DepositionService {
  private readonly logger = new Logger(DepositionService.name);

  constructor(
    private readonly aiService: AIService,
    private readonly documentsService: DocumentsService,
    private readonly depositionPreparationRepository: DepositionPreparationRepository,
    private readonly depositionAnalysisRepository: DepositionAnalysisRepository,
    private readonly tenantContext: TenantContextService,
  ) {}

  /**
   * Create a new deposition preparation
   */
  async createDepositionPreparation(
    dto: CreateDepositionPreparationDto,
  ): Promise<DepositionPreparation> {
    const userContext = this.getCurrentUserContext();
    const newPreparation = {
      caseId: dto.caseId || uuidv4(), // Generate a temporary caseId if not provided
      title: dto.title,
      description: dto.description,
      targetWitnesses: dto.targetWitnesses,
      caseContext: dto.caseContext,
      keyIssues: dto.keyIssues,
      questions: [],
      relatedDocumentIds: dto.relatedDocumentIds || [],
      status: DepositionPreparationStatus.DRAFT,
      userId: userContext.userId,
      organizationId: userContext.organizationId,
    };

    const created = await this.depositionPreparationRepository.create(
      newPreparation,
    );
    this.logger.log(`Created deposition preparation: ${created.id}`);

    return this.mapToDepositionPreparation(created);
  }

  /**
   * Get a deposition preparation by ID
   */
  private getCurrentUserContext(): { userId: string; organizationId: string } {
    const userId = this.tenantContext.getCurrentUserId();
    const organizationId = this.tenantContext.getCurrentOrganization();

    if (!userId || !organizationId) {
      throw new Error('User context not found');
    }

    return { userId, organizationId };
  }

  async getDepositionPreparation(id: string): Promise<DepositionPreparation> {
    const userContext = this.getCurrentUserContext();

    // Try finding by stored id first
    let preparation = await this.depositionPreparationRepository.findOne({
      id,
      userId: userContext.userId,
      organizationId: userContext.organizationId,
    });

    // If not found, try finding by _id
    if (!preparation) {
      preparation = await this.depositionPreparationRepository.findOne({
        _id: id,
        userId: userContext.userId,
        organizationId: userContext.organizationId,
      });
    }

    if (!preparation) {
      throw new NotFoundException(
        `Deposition preparation with ID ${id} not found`,
      );
    }
    return this.mapToDepositionPreparation(preparation);
  }

  /**
   * Update a deposition preparation
   */
  async updateDepositionPreparation(
    id: string,
    dto: UpdateDepositionPreparationDto,
  ): Promise<DepositionPreparation> {
    const preparation = await this.getDepositionPreparation(id);

    const updateData: any = {
      updatedAt: new Date(),
    };

    if (dto.title !== undefined) updateData.title = dto.title;
    if (dto.description !== undefined) updateData.description = dto.description;
    if (dto.targetWitnesses !== undefined)
      updateData.targetWitnesses = dto.targetWitnesses;
    if (dto.caseContext !== undefined) updateData.caseContext = dto.caseContext;
    if (dto.keyIssues !== undefined) updateData.keyIssues = dto.keyIssues;
    if (dto.relatedDocumentIds !== undefined)
      updateData.relatedDocumentIds = dto.relatedDocumentIds;
    if (dto.status !== undefined) updateData.status = dto.status;
    if (dto.metadata !== undefined) {
      updateData.metadata = {
        ...(preparation.metadata || {}),
        ...dto.metadata,
      };
    }

    const updated = await this.depositionPreparationRepository.update(
      preparation.id,
      updateData,
    );
    if (!updated) {
      throw new NotFoundException(
        `Deposition preparation with ID ${id} not found`,
      );
    }

    this.logger.log(`Updated deposition preparation: ${id}`);
    return this.mapToDepositionPreparation(updated);
  }

  /**
   * Delete a deposition preparation
   */
  async deleteDepositionPreparation(id: string): Promise<void> {
    const userContext = this.getCurrentUserContext();
    const preparation = await this.depositionPreparationRepository.findOne({
      id,
      organizationId: userContext.organizationId,
      userId: userContext.userId,
    });
    if (!preparation) {
      throw new NotFoundException(
        `Deposition preparation with ID ${id} not found`,
      );
    }

    const deleted = await this.depositionPreparationRepository.delete(
      preparation.id,
    );
    if (!deleted) {
      throw new NotFoundException(
        `Deposition preparation with ID ${id} not found`,
      );
    }

    this.logger.log(`Deleted deposition preparation: ${id}`);
  }

  /**
   * List all deposition preparations for a user
   */
  async listDepositionPreparations(): Promise<DepositionPreparation[]> {
    const userContext = this.getCurrentUserContext();
    const preparations =
      await this.depositionPreparationRepository.findByUserId(
        userContext.userId,
      );
    return preparations.map((prep) => this.mapToDepositionPreparation(prep));
  }

  /**
   * Add a question to a deposition preparation
   */
  async addQuestion(
    depositionId: string,
    dto: CreateDepositionQuestionDto,
  ): Promise<DepositionQuestion> {
    const preparation = await this.getDepositionPreparation(depositionId);

    // Set default values for optional fields
    const now = new Date();
    const defaultMetadata: DepositionQuestionMetadata = {
      ...(dto.metadata || {}),
      createdAt: now,
      updatedAt: now,
      aiGenerated: dto.metadata?.aiGenerated ?? false,
    };

    const question: DepositionQuestion = {
      id: uuidv4(),
      text: dto.text,
      category: dto.category,
      purpose: dto.purpose,
      targetWitness: dto.targetWitness || 'Witness',
      suggestedFollowUps: dto.suggestedFollowUps || [],
      relatedDocuments: dto.relatedDocuments || [],
      priority: dto.priority || 'medium',
      notes: dto.notes || '',
      isFollowUp: dto.isFollowUp || false,
      metadata: defaultMetadata,
      createdAt: now,
      updatedAt: now,
    };

    const updated = await this.depositionPreparationRepository.addQuestion(
      preparation.id,
      question,
    );
    if (!updated) {
      throw new NotFoundException(
        `Deposition preparation with ID ${depositionId} not found`,
      );
    }

    this.logger.log(
      `Added question to deposition preparation: ${depositionId}`,
    );

    return question;
  }

  /**
   * Update a question in a deposition preparation
   */
  async updateQuestion(
    depositionId: string,
    questionId: string,
    dto: UpdateDepositionQuestionDto,
  ): Promise<DepositionQuestion> {
    const preparation = await this.getDepositionPreparation(depositionId);

    // Find the question in the preparation
    const existingQuestion = preparation.questions.find(
      (q) => q.id === questionId,
    );
    if (!existingQuestion) {
      throw new NotFoundException(
        `Question with ID ${questionId} not found in deposition preparation ${depositionId}`,
      );
    }

    // Create updated question object with merged metadata
    const updatedMetadata: DepositionQuestionMetadata = {
      ...(existingQuestion.metadata || {}),
      ...(dto.metadata || {}),
      updatedAt: new Date(),
    };

    // Create updated question object
    const updatedQuestion: DepositionQuestion = {
      ...existingQuestion,
      text: dto.text ?? existingQuestion.text,
      category: dto.category ?? existingQuestion.category,
      purpose: dto.purpose ?? existingQuestion.purpose,
      targetWitness: dto.targetWitness ?? existingQuestion.targetWitness,
      suggestedFollowUps:
        dto.suggestedFollowUps ?? existingQuestion.suggestedFollowUps,
      relatedDocuments:
        dto.relatedDocuments ?? existingQuestion.relatedDocuments,
      priority: dto.priority ?? existingQuestion.priority,
      notes: dto.notes ?? existingQuestion.notes,
      isFollowUp: dto.isFollowUp ?? existingQuestion.isFollowUp,
      metadata: updatedMetadata,
      updatedAt: new Date(),
    };

    // Update in database
    const updated = await this.depositionPreparationRepository.updateQuestion(
      preparation.id,
      questionId,
      updatedQuestion,
    );

    if (!updated) {
      throw new NotFoundException(
        `Failed to update question with ID ${questionId}`,
      );
    }

    this.logger.log(
      `Updated question in deposition preparation: ${depositionId}`,
    );

    return updatedQuestion;
  }

  /**
   * Delete a question from a deposition preparation
   */
  async deleteQuestion(
    depositionId: string,
    questionId: string,
  ): Promise<void> {
    const preparation = await this.getDepositionPreparation(depositionId);

    // Check if question exists
    const questionExists = preparation.questions.some(
      (q) => q.id === questionId,
    );
    if (!questionExists) {
      throw new NotFoundException(
        `Question with ID ${questionId} not found in deposition preparation ${depositionId}`,
      );
    }

    // Remove question from database
    const updated = await this.depositionPreparationRepository.removeQuestion(
      preparation.id,
      questionId,
    );
    if (!updated) {
      throw new NotFoundException(
        `Failed to delete question with ID ${questionId}`,
      );
    }

    this.logger.log(
      `Deleted question from deposition preparation: ${depositionId}`,
    );
  }

  private generateDefaultFollowUps(questionText: string): string[] {
    if (!questionText) {
      return [
        'Could you elaborate on that?',
        'What led you to that conclusion?',
        'Is there anything else you can tell me about this?',
      ];
    }

    const text = questionText.toLowerCase();

    // Context-aware follow-up questions
    if (text.includes('when')) {
      return [
        'What happened right before that?',
        'What happened immediately after?',
        'How did that timing affect the outcome?',
      ];
    } else if (text.includes('who')) {
      return [
        'Can you describe their role in the situation?',
        'How did they contribute to the outcome?',
        'Were they present during the entire event?',
      ];
    } else if (text.includes('what')) {
      return [
        'Can you provide more details about that?',
        'How does that relate to the current situation?',
        'What were the consequences of that action?',
      ];
    } else if (text.includes('where')) {
      return [
        'Can you describe the location in more detail?',
        'How did the location impact the situation?',
        'Were there any other locations involved?',
      ];
    } else if (text.includes('why')) {
      return [
        'Can you explain your reasoning behind that?',
        'How does that relate to the current situation?',
        'Were there any other factors that contributed to that decision?',
      ];
    } else if (text.includes('how')) {
      return [
        'Can you describe the process in more detail?',
        'How did that impact the outcome?',
        'Were there any challenges or obstacles during that process?',
      ];
    } else {
      return [
        'Can you elaborate on that?',
        'What led you to that conclusion?',
        'Is there anything else you can tell me about this?',
      ];
    }
  }

  public async generateQuestions(params: {
    caseContext: string;
    keyIssues: string[];
    targetWitnesses: string[];
    userId: string;
    organizationId: string;
    depositionId?: string;
    options: {
      questionCount: number;
      includeFollowUps: boolean;
      questionCategories: DepositionQuestionCategory[];
    };
  }): Promise<QuestionGenerationResult> {
    const startTime = Date.now();

    // Validate and normalize question categories
    if (params.options.questionCategories?.length) {
      try {
        const normalizedCategories = params.options.questionCategories.map(
          (category, index) => {
            try {
              const mappedCategory = this.mapCategory(category);
              this.logger.debug(
                `[${index}] Category mapped: ${category} -> ${mappedCategory}`,
              );
              return mappedCategory;
            } catch (error) {
              this.logger.warn(
                `Failed to map category at index ${index}: ${category}`,
                error,
              );
              return DepositionQuestionCategory.GENERAL;
            }
          },
        );

        params.options.questionCategories = normalizedCategories;
      } catch (error) {
        this.logger.error('Error processing question categories:', error);
        params.options.questionCategories = [
          DepositionQuestionCategory.GENERAL,
        ];
      }
    } else {
      this.logger.debug(
        'No question categories provided, using default (GENERAL)',
      );
      params.options.questionCategories = [DepositionQuestionCategory.GENERAL];
    }

    try {
      const { questions, analysisTime } = await this.generateQuestionsWithAI({
        caseContext: params.caseContext,
        keyIssues: params.keyIssues,
        targetWitnesses: params.targetWitnesses,
        options: params.options,
      });

      // Return structured result
      return {
        success: true,
        questions: questions,
        generatedAt: new Date(),
        processingTime: Date.now() - startTime,
        analysisDuration: analysisTime,
      };
    } catch (error) {
      return {
        success: false,
        questions: [],
        generatedAt: new Date(),
        processingTime: Date.now() - startTime,
        error: error.message,
        fallbackQuestions: this.generateFallbackQuestions(params),
      };
    }
  }

  private readonly categoryPurposeMap: Record<string, string> = {
    general: 'Establish basic background information',
    credibility: 'Assess witness credibility and reliability',
    consistency: 'Verify timeline and sequence consistency',
    documentation: 'Review and analyze relevant documents',
    expert_qualification: 'Establish expert witness qualifications',
    impeachment: 'Challenge witness credibility and identify inconsistencies',
    background: 'Establish witness background and involvement',
    contractTerms: 'Understand contract terms and obligations',
    breach: 'Investigate potential contract breaches',
    damages: 'Assess damages and financial impact',
  };

  private readonly categoryMap: Record<string, DepositionQuestionCategory> = {
    // General category variations
    background: DepositionQuestionCategory.GENERAL,
    general: DepositionQuestionCategory.GENERAL,
    basic: DepositionQuestionCategory.GENERAL,
    'basic-info': DepositionQuestionCategory.GENERAL,
    'background-info': DepositionQuestionCategory.GENERAL,
    'witness-background': DepositionQuestionCategory.GENERAL,
    'witness-background-info': DepositionQuestionCategory.GENERAL,

    // Credibility category variations
    credibility: DepositionQuestionCategory.CREDIBILITY,
    'credibility-check': DepositionQuestionCategory.CREDIBILITY,
    'witness-reliability': DepositionQuestionCategory.CREDIBILITY,
    reliability: DepositionQuestionCategory.CREDIBILITY,
    'fact-finding': DepositionQuestionCategory.CREDIBILITY,
    'facts-and-evidence': DepositionQuestionCategory.CREDIBILITY,

    // Consistency category variations
    consistency: DepositionQuestionCategory.CONSISTENCY,
    timeline: DepositionQuestionCategory.CONSISTENCY,
    chronology: DepositionQuestionCategory.CONSISTENCY,
    'sequence-of-events': DepositionQuestionCategory.CONSISTENCY,
    'timeline-sequence': DepositionQuestionCategory.CONSISTENCY,

    // Documentation category variations
    documentation: DepositionQuestionCategory.DOCUMENTATION,
    'document-review': DepositionQuestionCategory.DOCUMENTATION,
    'document-analysis': DepositionQuestionCategory.DOCUMENTATION,
    'records-review': DepositionQuestionCategory.DOCUMENTATION,
    'evidence-review': DepositionQuestionCategory.DOCUMENTATION,
    'evidence-gathering': DepositionQuestionCategory.DOCUMENTATION,

    // Expert qualification category variations
    expert: DepositionQuestionCategory.EXPERT_QUALIFICATION,
    'expert-qualification': DepositionQuestionCategory.EXPERT_QUALIFICATION,
    expertise: DepositionQuestionCategory.EXPERT_QUALIFICATION,
    qualifications: DepositionQuestionCategory.EXPERT_QUALIFICATION,
    'expert-background': DepositionQuestionCategory.EXPERT_QUALIFICATION,

    // Impeachment category variations
    impeachment: DepositionQuestionCategory.IMPEACHMENT,
    'credibility-challenge': DepositionQuestionCategory.IMPEACHMENT,
    'prior-inconsistency': DepositionQuestionCategory.IMPEACHMENT,
    contradiction: DepositionQuestionCategory.IMPEACHMENT,
    bias: DepositionQuestionCategory.IMPEACHMENT,

    // Handle AI response specific categories
    contractterms: DepositionQuestionCategory.DOCUMENTATION,
    breach: DepositionQuestionCategory.CREDIBILITY,
    damages: DepositionQuestionCategory.DOCUMENTATION,
  };

  private derivePurposeFromCategory(category: string): string {
    const normalizedCategory = category
      .toLowerCase()
      .trim()
      .replace(/[-_\s]+/g, '') // Remove hyphens, underscores, and spaces
      .replace(/[^a-z]/g, ''); // Remove any remaining non-letter characters

    let mappedCategory = this.mapCategory(normalizedCategory);

    if (this.categoryPurposeMap[mappedCategory]) {
      this.logger.debug(
        `Found purpose for category: ${category} -> ${this.categoryPurposeMap[mappedCategory]}`,
      );
      return this.categoryPurposeMap[mappedCategory];
    }

    // Default purposes based on category type
    const defaultPurposes: Record<DepositionQuestionCategory, string> = {
      [DepositionQuestionCategory.GENERAL]:
        'Establish basic background information',
      [DepositionQuestionCategory.CREDIBILITY]:
        'Assess witness credibility and reliability',
      [DepositionQuestionCategory.CONSISTENCY]:
        'Verify timeline and sequence consistency',
      [DepositionQuestionCategory.DOCUMENTATION]:
        'Review and analyze relevant documents',
      [DepositionQuestionCategory.EXPERT_QUALIFICATION]:
        'Establish expert witness qualifications',
      [DepositionQuestionCategory.IMPEACHMENT]:
        'Challenge witness credibility and identify inconsistencies',
    };

    const defaultPurpose = defaultPurposes[mappedCategory];
    if (defaultPurpose) {
      this.logger.debug(
        `Using default purpose for category: ${category} -> ${defaultPurpose}`,
      );
      return defaultPurpose;
    }

    this.logger.debug(
      `No purpose found for category: ${category}, using generic purpose`,
    );
    return 'Gather relevant information';
  }

  private async generateQuestionsWithAI(context: {
    caseContext: string;
    keyIssues: string[];
    targetWitnesses: string[];
    options: {
      questionCount: number;
      includeFollowUps: boolean;
      questionCategories: DepositionQuestionCategory[];
    };
  }): Promise<{ questions: DepositionQuestion[]; analysisTime: number }> {
    const startTime = Date.now();
    // Map legacy/external category names to internal enum values
    // Map input categories to internal enum values
    const validCategories = context.options.questionCategories
      .map((c) => {
        const normalized = c
          .toLowerCase()
          .trim()
          .replace(/[^a-z_]/g, '');
        return this.mapCategory(normalized);
      })
      .filter((c) => Object.values(DepositionQuestionCategory).includes(c));

    // Provide default categories if none are valid
    const finalCategories =
      validCategories.length > 0
        ? validCategories
        : [
            DepositionQuestionCategory.GENERAL,
            DepositionQuestionCategory.CREDIBILITY,
          ];

    // Get questions from AI service
    const aiResponse = await this.aiService.generateDepositionQuestions({
      caseContext: context.caseContext,
      keyIssues: context.keyIssues,
      targetWitnesses: context.targetWitnesses,
      options: {
        questionCount: context.options.questionCount,
        includeFollowUps: context.options.includeFollowUps,
        questionCategories: finalCategories,
      },
    });
    console.log('AI Response:', aiResponse);
    const now = new Date();

    // Transform AI response to standard question format with validation
    const questions: DepositionQuestion[] = (aiResponse || [])
      .map((aiQuestion, index) => {
        try {
          // Extract and validate question text
          const text =
            typeof aiQuestion === 'string'
              ? aiQuestion
              : aiQuestion.question || aiQuestion.text;

          if (!text || typeof text !== 'string') {
            this.logger.warn(
              `Invalid question text at index ${index}, skipping`,
            );
            return null;
          }

          // Process category
          const rawCategory =
            typeof aiQuestion === 'string'
              ? 'general'
              : aiQuestion.category || 'general';

          const mappedCategory = this.mapCategory(rawCategory);
          this.logger.debug(
            `Mapped category ${rawCategory} to ${mappedCategory}`,
          );

          // Handle follow-ups
          const defaultFollowUps = this.generateDefaultFollowUps(text);
          const rawFollowUps =
            typeof aiQuestion === 'string'
              ? defaultFollowUps
              : aiQuestion.followUps || defaultFollowUps;

          const validFollowUps = Array.isArray(rawFollowUps)
            ? rawFollowUps.filter((f) => f && typeof f === 'string')
            : [];

          return {
            id: uuidv4(),
            text,
            category: mappedCategory,
            purpose: this.derivePurposeFromCategory(mappedCategory.toString()),
            targetWitness:
              typeof aiQuestion === 'object' && aiQuestion.targetWitness
                ? aiQuestion.targetWitness
                : context.targetWitnesses[
                    Math.floor(Math.random() * context.targetWitnesses.length)
                  ] || '',
            suggestedFollowUps: context.options.includeFollowUps
              ? validFollowUps
              : [],
            relatedDocuments: [],
            priority: this.ensurePriority('high'),
            notes: '',
            createdAt: now,
            updatedAt: now,
            isFollowUp: false,
            metadata: {
              aiGenerated: true,
              originalCategory: rawCategory,
              generationContext: {
                caseContext: context.caseContext,
                keyIssues: context.keyIssues,
                mappedCategory: mappedCategory.toString(),
                originalIndex: index,
              },
            },
          };
        } catch (error) {
          this.logger.error(
            `Error processing question at index ${index}:`,
            error,
          );
          return null;
        }
      })
      .filter(Boolean); // Remove any null entries

    if (!questions.length) {
      this.logger.warn('No valid questions generated from AI response');
    }

    return {
      questions,
      analysisTime: Date.now() - startTime,
    };
  }

  private mapCategory(category: string): DepositionQuestionCategory {
    try {
      // Handle null/undefined input
      if (!category) {
        this.logger.debug('No category provided, defaulting to GENERAL');
        return DepositionQuestionCategory.GENERAL;
      }

      // Normalize category string
      const normalizedCategory = category
        .toLowerCase()
        .trim()
        .replace(/[-_\s]+/g, '') // Remove hyphens, underscores, and spaces
        .replace(/[^a-z]/g, ''); // Remove any remaining non-letter characters

      this.logger.debug(
        `Normalized category: ${category} -> ${normalizedCategory}`,
      );

      // Direct mapping lookup
      if (this.categoryMap[normalizedCategory]) {
        const mappedCategory = this.categoryMap[normalizedCategory];
        this.logger.debug(
          `Direct mapping found: ${category} -> ${mappedCategory}`,
        );
        return mappedCategory;
      }

      // Partial match lookup
      for (const [key, value] of Object.entries(this.categoryMap)) {
        if (normalizedCategory.includes(key)) {
          this.logger.debug(
            `Partial match found: ${category} -> ${value} (matched on '${key}')`,
          );
          return value;
        }
      }

      this.logger.warn(
        `No mapping found for category: ${category}, defaulting to GENERAL`,
      );
      return DepositionQuestionCategory.GENERAL;
    } catch (error) {
      this.logger.error(`Error mapping category: ${category}`, error);
      return DepositionQuestionCategory.GENERAL;
    }
  }

  private ensurePriority(priority?: string): 'high' | 'medium' | 'low' {
    const validPriorities = ['high', 'medium', 'low'];
    return validPriorities.includes(priority?.toLowerCase() || '')
      ? (priority.toLowerCase() as 'high' | 'medium' | 'low')
      : 'medium';
  }

  private generateFallbackQuestions(context: any): DepositionQuestion[] {
    const defaultWitness = context.targetWitnesses?.[0] || 'Witness';
    const includeFollowUps = context.options?.includeFollowUps ?? false;

    // Helper function to ensure priority is valid
    const ensurePriority = (priority?: string): 'high' | 'medium' | 'low' => {
      const validPriorities = ['high', 'medium', 'low'];
      if (priority && validPriorities.includes(priority.toLowerCase())) {
        return priority.toLowerCase() as 'high' | 'medium' | 'low';
      }
      return 'medium';
    };

    const questions: DepositionQuestion[] = [
      {
        id: uuidv4(),
        text: `Please describe your role and responsibilities related to ${
          context.caseContext || 'this matter'
        }?`,
        category: DepositionQuestionCategory.CONSISTENCY,
        purpose: 'Establish witness background and involvement',
        priority: 'high',
        targetWitness: defaultWitness,
        suggestedFollowUps: includeFollowUps
          ? this.generateDefaultFollowUps(
              'Please describe your role and responsibilities',
            )
          : [],
        isFollowUp: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        notes: '',
        relatedDocuments: [],
        metadata: {},
      },
      {
        id: uuidv4(),
        text: 'What documents did you review to prepare for this deposition?',
        category: DepositionQuestionCategory.DOCUMENTATION,
        purpose: 'Understand witness preparation and document review',
        priority: 'high',
        targetWitness: defaultWitness,
        suggestedFollowUps: includeFollowUps
          ? this.generateDefaultFollowUps('What documents did you review')
          : [],
        isFollowUp: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        notes: '',
        relatedDocuments: [],
        metadata: {},
      },
    ];

    // If includeFollowUps is true, add follow-up questions to the response
    if (includeFollowUps) {
      const followUps: DepositionQuestion[] = [];

      for (const question of questions) {
        if (question.suggestedFollowUps?.length > 0) {
          for (const followUpText of question.suggestedFollowUps) {
            followUps.push({
              id: uuidv4(),
              text: followUpText,
              category: question.category,
              purpose: `Follow-up: ${question.purpose}`,
              priority: ensurePriority(question.priority),
              targetWitness: question.targetWitness,
              suggestedFollowUps: [],
              isFollowUp: true,
              createdAt: new Date(),
              updatedAt: new Date(),
              notes: '',
              relatedDocuments: [],
              metadata: {},
            });
          }
        }
      }

      // Combine main questions and their follow-ups
      return [...questions, ...followUps];
    }

    return questions;
  }

  private mapToDepositionAnalysisResult(doc: any): DepositionAnalysisResult {
    return {
      id: doc.id,
      depositionId: doc.depositionId || doc.caseId, // Support both caseId (legacy) and depositionId
      overallCredibilityScore: doc.overallCredibilityScore || 0,
      keyTestimonyAnalysis: doc.keyTestimonyAnalysis || [],
      crossExaminationSuggestions: doc.crossExaminationSuggestions || [],
      inconsistencies: doc.inconsistencies || [],
      keyFindings: doc.keyFindings || [],
      potentialImpeachmentOpportunities:
        doc.potentialImpeachmentOpportunities || [],
      timelineAnalysis: doc.timelineAnalysis || [],
      metadata: {
        analyzedAt: doc.metadata?.analyzedAt || new Date().toISOString(),
        analysisDurationMs: doc.metadata?.analysisDurationMs || 0,
        modelUsed: doc.metadata?.modelUsed || 'deposition-analyzer-v1',
        confidence: doc.metadata?.confidence || 'medium',
        ...doc.metadata,
      },
    };
  }

  /**
   * Get context from related documents
   */
  private async getRelatedDocumentsContext(
    documentIds: string[],
  ): Promise<string> {
    if (!documentIds?.length) return '';

    const documents = await Promise.all(
      documentIds.map(async (id) => {
        try {
          let content: string;
          try {
            content = await this.documentsService.getDocumentContent(id);
          } catch (error) {
            this.logger.warn(
              `Could not fetch content for document ${id}`,
              error,
            );
            return null;
          }

          const excerpt = content ? `Content Excerpt: ${content}...` : '';
          return `Document ID: ${id}\n${excerpt}`;
        } catch (error) {
          this.logger.warn(`Could not fetch related document ${id}`, error);
          return null;
        }
      }),
    );

    return documents.filter(Boolean).join('\n\n');
  }

  /**
   * Analyze a deposition transcript
   */
  async analyzeDeposition(
    dto: AnalyzeDepositionDto,
    userInfo: { userId: string; caseId?: string },
  ): Promise<DepositionAnalysisResult> {
    const startTime = Date.now();
    this.logger.debug('Starting deposition analysis', {
      transcriptLength: dto.transcript?.length || 0,
      depositionId: dto.depositionId,
    });

    try {
      try {
        // Check for recent analyses first
        const recentAnalyses = await this.depositionAnalysisRepository.findByDepositionId(dto.depositionId);
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        
        // Get latest analysis with metadata
        const recentAnalysis = recentAnalyses
          .filter(analysis => analysis.metadata?.analyzedAt)
          .sort((a, b) => new Date(b.metadata.analyzedAt).getTime() - new Date(a.metadata.analyzedAt).getTime())[0];
  
        if (recentAnalysis && new Date(recentAnalysis.metadata.analyzedAt) > fiveMinutesAgo) {
          const waitTimeSeconds = Math.ceil((new Date(recentAnalysis.metadata.analyzedAt).getTime() - fiveMinutesAgo.getTime()) / 1000);
          const resetTime = new Date(recentAnalysis.metadata.analyzedAt);
          resetTime.setMinutes(resetTime.getMinutes() + 5);
          
          throw new HttpException({
            statusCode: HttpStatus.TOO_MANY_REQUESTS,
            error: 'Rate limit exceeded',
            message: `Please wait ${waitTimeSeconds} seconds before requesting another analysis`,
            retryAfter: waitTimeSeconds,
            resetTime: resetTime.toISOString(),
            analysisId: recentAnalysis._id
          }, HttpStatus.TOO_MANY_REQUESTS);
        }

        // Validate deposition exists and check access
        const deposition = await this.getDepositionPreparation(dto.depositionId);
        if (deposition.organizationId !== this.tenantContext.getCurrentOrganization()) {
          throw new UnauthorizedException('You do not have access to this deposition');
        }

        this.logger.debug('Validation successful', {
          depositionId: dto.depositionId,
          organizationId: deposition.organizationId
        });
      } catch (error) {
        if (error instanceof NotFoundException) {
          throw new NotFoundException(`Deposition not found: ${dto.depositionId}`);
        }
        if (error instanceof UnauthorizedException) {
          throw error;
        }
        // Handle rate limiting error specifically
        if (error.message?.includes('wait')) {
          throw new HttpException({
            status: HttpStatus.TOO_MANY_REQUESTS,
            error: 'Rate limit exceeded',
            message: error.message,
            retryAfter: 300 // 5 minutes in seconds
          }, HttpStatus.TOO_MANY_REQUESTS);
        }
        this.logger.error('Error during deposition validation:', {
          error: error.message,
          depositionId: dto.depositionId,
          userId: userInfo.userId,
          type: error.constructor.name
        });
        throw new HttpException({
          status: HttpStatus.BAD_REQUEST,
          error: 'Validation Error',
          message: error.message
        }, HttpStatus.BAD_REQUEST);
      }

      // If depositionPreparationId is provided, fetch additional preparation details
      let deposition = null;
      if (dto.depositionPreparationId) {
        try {
          deposition = await this.getDepositionPreparation(dto.depositionPreparationId);
          // Merge deposition preparation context with DTO
          if (!dto.caseContext && deposition.caseContext) {
            dto.caseContext = deposition.caseContext;
          }

          // Add preparation questions to analysis context
          const questionsContext = deposition.questions
            ?.map(
              (q) =>
                `Planned Question: ${q.text}\nCategory: ${
                  q.category
                }\nPurpose: ${q.purpose || 'Not specified'}`,
            )
            .join('\n\n');

          // Add related documents summaries to context
          const documentsContext = await this.getRelatedDocumentsContext(
            deposition.relatedDocumentIds || [],
          );

          // Enhance case context with questions and documents
          dto.caseContext = `${dto.caseContext || ''}
          
          Planned Questions:
          ${questionsContext || 'No pre-planned questions available.'}
          
          Related Documents:
          ${documentsContext || 'No related documents available.'}`;
        } catch (error) {
          this.logger.warn(
            `Could not find deposition preparation ${dto.depositionPreparationId}`,
            error,
          );
        }
      }

      // Prepare the analysis result with default values
      const analysisData: DepositionAnalysisResult = {
        id: uuidv4(),
        depositionId: dto.depositionId, // Only use the provided depositionId
        transcript: dto.transcript || '',
        caseContext: dto.caseContext,
        focusAreas: dto.focusAreas || [],
        overallCredibilityScore: 0.8, // Default score, can be adjusted by analysis
        keyTestimonyAnalysis: [],
        crossExaminationSuggestions: [],
        inconsistencies: [],
        keyFindings: [],
        potentialImpeachmentOpportunities: [],
        timelineAnalysis: [],
        metadata: {
          modelUsed: 'deposition-analyzer-v1',
          confidence: 'medium',
        },
      };

      // If we have a transcript, analyze it
      if (dto.transcript) {
        try {
          const analysis = await this.aiService.analyzeDeposition(
            dto.transcript,
            {
              caseContext: dto.caseContext,
              focusAreas: dto.focusAreas || [
                'credibility',
                'inconsistencies',
                'key_testimonies',
                'cross_examination',
              ],
            },
          );

          // Perform basic analysis
          const wordCount = dto.transcript.split(/\s+/).length;
          const sentenceCount = dto.transcript
            .split(/[.!?]+/)
            .filter(Boolean).length;
          const avgWordsPerSentence = wordCount / Math.max(1, sentenceCount);

          const basicAnalysis = `Transcript contains ${wordCount} words across ${sentenceCount} sentences (avg ${avgWordsPerSentence.toFixed(
            1,
          )} words/sentence)`;

          // Update analysis data with AI results
          Object.assign(analysisData, {
            overallCredibilityScore: analysis.overallCredibilityScore || 0.8,
            keyTestimonyAnalysis: analysis.keyTestimonyAnalysis || [],
            crossExaminationSuggestions: (
              analysis.crossExaminationSuggestions || []
            ).map((suggestion) => ({
              ...suggestion,
              suggestedFollowUps: suggestion.suggestedFollowUps || [],
            })),
            inconsistencies: analysis.inconsistencies || [],
            keyFindings: [...(analysis.keyFindings || []), basicAnalysis],
            metadata: {
              ...analysisData.metadata,
              analyzedAt: new Date().toISOString(),
              analysisDurationMs: Date.now() - startTime,
            },
          });

          // If we have a deposition preparation, update it with the analysis results
          if (deposition) {
            await this.updateDepositionPreparation(deposition.id, {
              metadata: {
                ...deposition.metadata,
                lastAnalyzedAt: new Date().toISOString(),
                analysisSummary: {
                  credibilityScore: analysisData.overallCredibilityScore,
                  keyFindings: analysisData.keyFindings.slice(0, 3), // Top 3 findings
                  questionCount:
                    analysisData.crossExaminationSuggestions.length,
                },
              },
            });
          }
        } catch (error) {
          this.logger.error('Error analyzing deposition transcript:', error);
          // Don't fail the entire operation if analysis fails
          analysisData.keyFindings.push(
            'Error during AI analysis: ' + error.message,
          );
        }
      }

      // Log completion
      this.logger.log('Completed deposition analysis', {
        analysisId: analysisData.id,
        durationMs: Date.now() - startTime,
      });

      // Store the analysis in the database
      const analysisToSave = {
        ...analysisData,
        userId: userInfo.userId,
        organizationId: this.tenantContext.getCurrentOrganization() || 'default-org',
        ...(userInfo.caseId && { caseId: userInfo.caseId }),
        metadata: {
          ...analysisData.metadata,
          analyzedAt: new Date().toISOString(),
          analysisDurationMs: Date.now() - startTime
        }
      };

      // Save the analysis result
      const savedAnalysis = await this.depositionAnalysisRepository.create(analysisToSave);
      
      if (!savedAnalysis) {
        throw new Error('Failed to save analysis result to database');
      }

      this.logger.debug('Analysis saved successfully', {
        analysisId: savedAnalysis._id,
        depositionId: dto.depositionId
      });

      // Return the mapped result
      return this.mapToDepositionAnalysisResult(savedAnalysis);
    } catch (error) {
      this.logger.error('Error analyzing deposition', {
        error: error.message,
        stack: error.stack,
      });
      throw new Error(`Failed to analyze deposition: ${error.message}`);
    }
  }

  /**
   * Get a deposition analysis by ID
   */
  async getDepositionAnalysis(depositionId: string): Promise<DepositionAnalysisResult[]> {
    try {
      // Validate the deposition exists and user has access
      const deposition = await this.getDepositionPreparation(depositionId);
      if (!deposition) {
        throw new NotFoundException(`Deposition not found: ${depositionId}`);
      }

      // Check organization access
      if (deposition.organizationId !== this.tenantContext.getCurrentOrganization()) {
        throw new UnauthorizedException('You do not have access to this deposition');
      }

      // Get analyses for the deposition
      const analyses = await this.depositionAnalysisRepository.findByDepositionId(depositionId);

      if (!analyses || analyses.length === 0) {
        return [];
      }

      // Map and sort analyses by date
      return analyses
        .map(analysis => this.mapToDepositionAnalysisResult(analysis))
        .sort((a, b) => {
          const dateA = new Date(a.metadata?.analyzedAt || 0);
          const dateB = new Date(b.metadata?.analyzedAt || 0);
          return dateB.getTime() - dateA.getTime();
        });

    } catch (error) {
      this.logger.error('Error getting deposition analyses', {
        error: error.message,
        stack: error.stack,
        depositionId,
        organization: this.tenantContext.getCurrentOrganization()
      });

      if (error instanceof NotFoundException || error instanceof UnauthorizedException) {
        throw error;
      }

      throw new Error(`Failed to get deposition analyses: ${error.message}`);
    }
  }

  private getConfidence(level?: string): 'high' | 'medium' | 'low' {
    if (!level) return 'medium';
    const normalized = level.toLowerCase();
    if (['high', 'medium', 'low'].includes(normalized)) {
      return normalized as 'high' | 'medium' | 'low';
    }
    return 'medium';
  }

  /**
   * Maps a document to a DepositionPreparation type
   */
  private mapToDepositionPreparation(doc: any): DepositionPreparation {
    if (!doc) {
      throw new Error('Document is required for mapping');
    }
    return {
      id: doc.id || doc._id?.toString(),
      caseId: doc.caseId,
      title: doc.title,
      description: doc.description,
      targetWitnesses: doc.targetWitnesses || [],
      caseContext: doc.caseContext || '',
      keyIssues: doc.keyIssues || [],
      questions: doc.questions || [],
      relatedDocumentIds: doc.relatedDocumentIds || [],
      status: doc.status || DepositionPreparationStatus.DRAFT,
      userId: doc.userId,
      organizationId: doc.organizationId,
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt || new Date(),
    };
  }
}

import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

// Schemas
import {
  ContractPlaybook,
  ContractPlaybookDocument,
  CONTRACT_PLAYBOOK_MODEL,
} from '../schemas/contract-playbook.schema';
import {
  ContractAnalysis,
  ContractAnalysisDocument,
  CONTRACT_ANALYSIS_MODEL,
} from '../schemas/contract-analysis.schema';

// DTOs
import {
  CreateContractPlaybookDto,
  UpdateContractPlaybookDto,
  AnalyzeContractDto,
  SearchPlaybooksDto,
  SearchAnalysesDto,
} from '../dto/contract-playbook.dto';

// Interfaces
import {
  ContractPlaybook as IContractPlaybook,
  ContractAnalysis as IContractAnalysis,
  PlaybookListResponse,
  AnalysisListResponse,
  PlaybookAnalyticsResponse,
  PlaybookSearchOptions,
  AnalysisSearchOptions,
} from '../interfaces/contract-playbook.interface';

// Services
import { AIService } from '../../ai/services/ai.service';
import { DocumentsService } from './documents.service';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { AnalyticsService } from '../../analytics/services/analytics.service';

@Injectable()
export class ContractPlaybookService {
  private readonly logger = new Logger(ContractPlaybookService.name);

  constructor(
    @InjectModel(CONTRACT_PLAYBOOK_MODEL)
    private readonly playbookModel: Model<ContractPlaybookDocument>,
    @InjectModel(CONTRACT_ANALYSIS_MODEL)
    private readonly analysisModel: Model<ContractAnalysisDocument>,
    private readonly aiService: AIService,
    private readonly documentsService: DocumentsService,
    private readonly tenantContext: TenantContextService,
    private readonly analyticsService: AnalyticsService,
  ) {}

  // Playbook CRUD Operations
  async createPlaybook(
    dto: CreateContractPlaybookDto,
    organizationId: string,
    userId: string,
  ): Promise<IContractPlaybook> {
    this.logger.log(
      `Creating playbook: ${dto.name} for org: ${organizationId}`,
    );

    // Check for duplicate names within organization
    const existingPlaybook = await this.playbookModel.findOne({
      organizationId,
      name: dto.name,
      contractType: dto.contractType,
    });

    if (existingPlaybook) {
      throw new ConflictException(
        `Playbook with name "${dto.name}" already exists for ${dto.contractType} contracts`,
      );
    }

    const playbook = new this.playbookModel({
      id: uuidv4(),
      organizationId,
      createdBy: userId,
      ...dto,
      rules:
        dto.rules?.map((rule) => ({
          ...rule,
          id: uuidv4(),
          createdAt: new Date(),
          updatedAt: new Date(),
        })) || [],
    });

    const savedPlaybook = await playbook.save();

    // Analytics tracking would go here
    this.logger.log(`Playbook created: ${savedPlaybook.id}`);

    const playbookObj = savedPlaybook.toObject();
    return {
      ...playbookObj,
      contractType: playbookObj.contractType
    } as IContractPlaybook;
  }

  async findAllPlaybooks(
    organizationId: string,
    options: SearchPlaybooksDto = {},
  ): Promise<PlaybookListResponse> {
    this.logger.log(`Finding playbooks for org: ${organizationId}`);

    const {
      query,
      contractType,
      isActive,
      isTemplate,
      tags,
      page = 1,
      limit = 20,
    } = options;

    // Build filter
    const filter: any = { organizationId };

    if (contractType) filter.contractType = contractType;
    if (typeof isActive === 'boolean') filter.isActive = isActive;
    if (typeof isTemplate === 'boolean') filter.isTemplate = isTemplate;
    if (tags?.length) filter['metadata.tags'] = { $in: tags };

    if (query) {
      filter.$or = [
        { name: { $regex: query, $options: 'i' } },
        { description: { $regex: query, $options: 'i' } },
        { 'metadata.tags': { $regex: query, $options: 'i' } },
      ];
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [playbooks, total] = await Promise.all([
      this.playbookModel
        .find(filter)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      this.playbookModel.countDocuments(filter),
    ]);

    return {
      playbooks: playbooks.map(playbook => ({
        ...playbook,
        contractType: playbook.contractType
      })) as IContractPlaybook[],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findPlaybookById(
    id: string,
    organizationId: string,
  ): Promise<IContractPlaybook> {
    const playbook = await this.playbookModel
      .findOne({ id, organizationId })
      .lean();

    if (!playbook) {
      throw new NotFoundException(`Playbook with ID ${id} not found`);
    }

    return {
      ...playbook,
      contractType: playbook.contractType
    } as IContractPlaybook;
  }

  async updatePlaybook(
    id: string,
    dto: UpdateContractPlaybookDto,
    organizationId: string,
    userId: string,
  ): Promise<IContractPlaybook> {
    this.logger.log(`Updating playbook: ${id} for org: ${organizationId}`);

    const playbook = await this.playbookModel.findOne({ id, organizationId });

    if (!playbook) {
      throw new NotFoundException(`Playbook with ID ${id} not found`);
    }

    // Check for name conflicts if name is being changed
    if (dto.name && dto.name !== playbook.name) {
      const existingPlaybook = await this.playbookModel.findOne({
        organizationId,
        name: dto.name,
        contractType: playbook.contractType,
        id: { $ne: id },
      });

      if (existingPlaybook) {
        throw new ConflictException(
          `Playbook with name "${dto.name}" already exists`,
        );
      }
    }

    // Update rules with proper IDs and timestamps
    if (dto.rules) {
      dto.rules = dto.rules.map((rule) => ({
        ...rule,
        id: rule.id || uuidv4(),
        updatedAt: new Date(),
        createdAt: rule.id
          ? playbook.rules.find((r) => r.id === rule.id)?.createdAt ||
            new Date()
          : new Date(),
      }));
    }

    Object.assign(playbook, dto, {
      updatedBy: userId,
      updatedAt: new Date(),
    });

    const updatedPlaybook = await playbook.save();

    // Analytics tracking would go here
    this.logger.log(`Playbook updated: ${id}`);

    const playbookObj = updatedPlaybook.toObject();
    return {
      ...playbookObj,
      contractType: playbookObj.contractType
    } as IContractPlaybook;
  }

  async deletePlaybook(
    id: string,
    organizationId: string,
    userId: string,
  ): Promise<void> {
    this.logger.log(`Deleting playbook: ${id} for org: ${organizationId}`);

    const playbook = await this.playbookModel.findOne({ id, organizationId });

    if (!playbook) {
      throw new NotFoundException(`Playbook with ID ${id} not found`);
    }

    // Check if playbook is being used in any analyses
    const analysisCount = await this.analysisModel.countDocuments({
      playbookId: id,
      organizationId,
    });

    if (analysisCount > 0) {
      throw new BadRequestException(
        `Cannot delete playbook. It is being used in ${analysisCount} analyses.`,
      );
    }

    await this.playbookModel.deleteOne({ id, organizationId });

    // Analytics tracking would go here
    this.logger.log(`Playbook deleted: ${id}`);
  }

  // Contract Analysis Operations
  async analyzeContract(
    dto: AnalyzeContractDto,
    organizationId: string,
    userId: string,
  ): Promise<IContractAnalysis> {
    this.logger.log(
      `Analyzing contract ${dto.contractId} with playbook ${dto.playbookId}`,
    );

    // Validate inputs
    const [document, playbook] = await Promise.all([
      this.documentsService.findById(dto.contractId),
      this.findPlaybookById(dto.playbookId, organizationId),
    ]);

    if (!document.content) {
      throw new BadRequestException(
        'Document content is required for analysis',
      );
    }

    if (!playbook.isActive) {
      throw new BadRequestException('Cannot analyze with inactive playbook');
    }

    // Create analysis record
    const analysis = new this.analysisModel({
      id: uuidv4(),
      organizationId,
      contractId: dto.contractId,
      playbookId: dto.playbookId,
      playbookName: playbook.name,
      status: 'IN_PROGRESS',
      analyzedBy: userId,
      metadata: {
        contractType: playbook.contractType,
        contractTitle: document.metadata?.originalName || document.id,
        analysisVersion: '1.0.0',
      },
    });

    await analysis.save();

    try {
      // Perform the actual analysis (this will be implemented in the next part)
      const analysisResult = await this.performContractAnalysis(
        document,
        playbook,
        dto.options || {},
      );

      // Update analysis with results
      Object.assign(analysis, analysisResult, {
        status: 'COMPLETED',
        analyzedAt: new Date(),
      });

      const completedAnalysis = await analysis.save();

      // Analytics tracking would go here
      this.logger.log(
        `Contract analyzed: ${dto.contractId} with playbook: ${dto.playbookId}`,
      );

      return completedAnalysis.toObject() as IContractAnalysis;
    } catch (error) {
      this.logger.error(`Analysis failed: ${error.message}`, error.stack);

      analysis.status = 'FAILED';
      analysis.metadata.error = error.message;
      await analysis.save();

      throw error;
    }
  }

  // Analysis Engine Implementation
  private async performContractAnalysis(
    document: any,
    playbook: IContractPlaybook,
    options: any,
  ): Promise<Partial<IContractAnalysis>> {
    const startTime = Date.now();
    this.logger.log(
      `Starting contract analysis with ${playbook.rules.length} rules`,
    );

    try {
      // Step 1: Extract clauses from document using AI
      const clauses = await this.aiService.identifyClauseSections(
        document.content,
      );
      this.logger.log(`Identified ${clauses.length} clauses in document`);

      // Step 2: Analyze each rule against the document
      const deviations = [];
      let totalRiskScore = 0;

      for (const rule of playbook.rules.filter((r) => r.isActive)) {
        const ruleDeviations = await this.analyzeRule(
          rule,
          document.content,
          clauses,
        );
        deviations.push(...ruleDeviations);

        // Calculate cumulative risk
        ruleDeviations.forEach((dev) => {
          totalRiskScore += dev.riskScore;
        });
      }

      // Step 3: Calculate overall metrics
      const processingTime = Date.now() - startTime;
      const overallScore = this.calculateOverallScore(
        deviations,
        playbook.rules.length,
      );
      const riskLevel = this.determineRiskLevel(overallScore);

      // Step 4: Generate summary
      const summary = this.generateAnalysisSummary(deviations, overallScore);

      // Step 5: Generate AI-powered executive summary if requested
      let executiveSummary = summary.executiveSummary;
      if (options.detailedReport && deviations.length > 0) {
        executiveSummary = await this.generateExecutiveSummary(
          document,
          deviations,
          overallScore,
        );
      }

      return {
        overallScore,
        riskLevel,
        deviations,
        summary: {
          ...summary,
          executiveSummary,
        },
        metrics: {
          processingTimeMs: processingTime,
          aiAnalysisTime: processingTime * 0.6, // Estimate AI portion
          rulesEvaluated: playbook.rules.filter((r) => r.isActive).length,
          clausesAnalyzed: clauses.length,
          confidenceScore: this.calculateConfidenceScore(deviations),
        },
      };
    } catch (error) {
      this.logger.error(
        `Contract analysis failed: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(`Analysis failed: ${error.message}`);
    }
  }

  private async analyzeRule(
    rule: any,
    documentContent: string,
    clauses: any[],
  ): Promise<any[]> {
    const deviations = [];

    try {
      // Check for required clauses
      if (rule.ruleType === 'required_clause') {
        const hasRequiredClause = await this.checkRequiredClause(
          rule,
          documentContent,
          clauses,
        );
        if (!hasRequiredClause) {
          deviations.push(
            await this.createDeviation(rule, 'missing_clause', documentContent),
          );
        }
      }

      // Check for prohibited language
      if (rule.ruleType === 'prohibited_clause') {
        const prohibitedMatches = await this.checkProhibitedLanguage(
          rule,
          documentContent,
        );
        deviations.push(...prohibitedMatches);
      }

      // Check language standards
      if (rule.ruleType === 'language_standard') {
        const languageDeviations = await this.checkLanguageStandards(
          rule,
          documentContent,
          clauses,
        );
        deviations.push(...languageDeviations);
      }

      // Add more rule type checks as needed...
    } catch (error) {
      this.logger.error(
        `Rule analysis failed for rule ${rule.id}: ${error.message}`,
      );
    }

    return deviations;
  }

  private async checkRequiredClause(
    rule: any,
    documentContent: string,
    clauses: any[],
  ): Promise<boolean> {
    // Check keywords first
    const hasKeywords = rule.criteria.keywords.some((keyword) =>
      documentContent.toLowerCase().includes(keyword.toLowerCase()),
    );

    if (!hasKeywords) return false;

    // Use AI for semantic analysis if semantic concepts are defined
    if (rule.criteria.semanticConcepts?.length > 0) {
      const prompt = `Analyze the following legal document to determine if it contains clauses related to: ${rule.criteria.semanticConcepts.join(
        ', ',
      )}.

Document: ${documentContent.substring(0, 4000)}...

Respond with only "YES" if the required concepts are present, or "NO" if they are missing.`;

      try {
        const response = await this.aiService.generateResponse(prompt, {
          temperature: 0.1,
        });
        return response.trim().toUpperCase() === 'YES';
      } catch (error) {
        this.logger.error(
          `AI analysis failed for required clause check: ${error.message}`,
        );
        return hasKeywords; // Fallback to keyword check
      }
    }

    return hasKeywords;
  }

  private async checkProhibitedLanguage(
    rule: any,
    documentContent: string,
  ): Promise<any[]> {
    const deviations = [];

    for (const prohibited of rule.unacceptableTerms.prohibited) {
      const regex = new RegExp(prohibited, 'gi');
      const matches = documentContent.match(regex);

      if (matches) {
        for (const match of matches) {
          const startIndex = documentContent.indexOf(match);
          deviations.push(
            await this.createDeviation(
              rule,
              'prohibited_language',
              documentContent,
              {
                startIndex,
                endIndex: startIndex + match.length,
                matchedText: match,
              },
            ),
          );
        }
      }
    }

    return deviations;
  }

  private async checkLanguageStandards(
    rule: any,
    documentContent: string,
    clauses: any[],
  ): Promise<any[]> {
    const deviations = [];

    // Find relevant clauses for this rule
    const relevantClauses = clauses.filter((clause) =>
      rule.criteria.keywords.some((keyword) =>
        clause.content.toLowerCase().includes(keyword.toLowerCase()),
      ),
    );

    for (const clause of relevantClauses) {
      // Check if clause language meets standards
      const hasAcceptableLanguage = rule.acceptableLanguage.acceptable.some(
        (acceptable) =>
          clause.content.toLowerCase().includes(acceptable.toLowerCase()),
      );

      const hasPreferredLanguage = rule.acceptableLanguage.preferred.some(
        (preferred) =>
          clause.content.toLowerCase().includes(preferred.toLowerCase()),
      );

      if (!hasAcceptableLanguage && !hasPreferredLanguage) {
        deviations.push(
          await this.createDeviation(
            rule,
            'non_standard_terms',
            clause.content,
            {
              startIndex: clause.startIndex,
              endIndex: clause.endIndex,
              clauseType: clause.category,
            },
          ),
        );
      }
    }

    return deviations;
  }

  private async createDeviation(
    rule: any,
    deviationType: string,
    clauseText: string,
    context: any = {},
  ): Promise<any> {
    const riskScore = this.calculateRiskScore(rule.severity, deviationType);

    // Generate AI-powered suggestion if requested
    let suggestedText = '';
    if (rule.acceptableLanguage.preferred.length > 0) {
      suggestedText = rule.acceptableLanguage.preferred[0];
    }

    // Generate recommendations
    const recommendations = await this.generateRecommendations(
      rule,
      deviationType,
      clauseText,
    );

    return {
      id: uuidv4(),
      ruleId: rule.id,
      ruleName: rule.name,
      deviationType,
      severity: rule.severity,
      clauseText: clauseText.substring(0, 500), // Limit text length
      suggestedText,
      riskScore,
      context: {
        documentSection: context.clauseType || 'Unknown',
        startIndex: context.startIndex,
        endIndex: context.endIndex,
        ...context,
      },
      recommendations,
      confidence: 0.85, // Default confidence
      detectedAt: new Date(),
    };
  }

  private calculateRiskScore(severity: string, deviationType: string): number {
    const severityWeights = {
      CRITICAL: 90,
      HIGH: 70,
      MEDIUM: 50,
      LOW: 30,
    };

    const typeWeights = {
      missing_clause: 1.2,
      prohibited_language: 1.5,
      non_standard_terms: 1.0,
      risk_threshold_exceeded: 1.3,
      compliance_violation: 1.4,
    };

    const baseScore = severityWeights[severity] || 50;
    const typeMultiplier = typeWeights[deviationType] || 1.0;

    return Math.min(100, Math.round(baseScore * typeMultiplier));
  }

  private async generateRecommendations(
    rule: any,
    deviationType: string,
    clauseText: string,
  ): Promise<any[]> {
    const recommendations = [];

    // Generate basic recommendation based on rule type
    if (deviationType === 'missing_clause') {
      recommendations.push({
        id: uuidv4(),
        type: 'add_clause',
        priority: rule.severity === 'CRITICAL' ? 'URGENT' : 'HIGH',
        action: {
          description: `Add missing ${rule.category} clause`,
          specificSteps: [`Include ${rule.category} clause in contract`],
          timeframe: 'Before contract execution',
        },
        rationale: {
          riskMitigation: `Reduces ${rule.category} related risks`,
          businessJustification:
            rule.negotiationGuidance?.businessImpact ||
            'Protects business interests',
          legalBasis: `Required for ${rule.category} compliance`,
        },
        implementation: {
          suggestedLanguage: rule.acceptableLanguage.preferred[0] || '',
          negotiationTalkingPoints:
            rule.negotiationGuidance?.alternatives || [],
          alternatives: rule.acceptableLanguage.acceptable || [],
          fallbackPositions: rule.acceptableLanguage.fallbackPositions || [],
        },
        impact: {
          riskReduction: this.calculateRiskScore(rule.severity, deviationType),
          implementationEffort:
            rule.severity === 'CRITICAL' ? 'HIGH' : 'MEDIUM',
          businessImpact:
            rule.negotiationGuidance?.businessImpact || 'Moderate',
          timeline: '1-2 business days',
        },
        createdAt: new Date(),
      });
    }

    return recommendations;
  }

  private calculateOverallScore(deviations: any[], totalRules: number): number {
    if (deviations.length === 0) return 100;

    const totalRiskScore = deviations.reduce(
      (sum, dev) => sum + dev.riskScore,
      0,
    );
    const averageRiskScore = totalRiskScore / deviations.length;

    // Score decreases based on average risk and number of deviations
    const deviationPenalty = Math.min(50, deviations.length * 5);
    const riskPenalty = averageRiskScore * 0.5;

    return Math.max(0, Math.round(100 - deviationPenalty - riskPenalty));
  }

  private determineRiskLevel(
    overallScore: number,
  ): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    if (overallScore >= 80) return 'LOW';
    if (overallScore >= 60) return 'MEDIUM';
    if (overallScore >= 40) return 'HIGH';
    return 'CRITICAL';
  }

  private generateAnalysisSummary(
    deviations: any[],
    overallScore: number,
  ): any {
    const criticalDeviations = deviations.filter(
      (d) => d.severity === 'CRITICAL',
    ).length;
    const highRiskDeviations = deviations.filter(
      (d) => d.severity === 'HIGH',
    ).length;
    const mediumRiskDeviations = deviations.filter(
      (d) => d.severity === 'MEDIUM',
    ).length;
    const lowRiskDeviations = deviations.filter(
      (d) => d.severity === 'LOW',
    ).length;

    const keyFindings = deviations
      .filter((d) => d.severity === 'CRITICAL' || d.severity === 'HIGH')
      .slice(0, 5)
      .map((d) => `${d.ruleName}: ${d.deviationType.replace('_', ' ')}`);

    return {
      totalDeviations: deviations.length,
      criticalDeviations,
      highRiskDeviations,
      mediumRiskDeviations,
      lowRiskDeviations,
      compliancePercentage: overallScore,
      keyFindings,
      executiveSummary: `Contract analysis completed with ${deviations.length} deviations found. Overall compliance score: ${overallScore}%.`,
    };
  }

  private async generateExecutiveSummary(
    document: any,
    deviations: any[],
    overallScore: number,
  ): Promise<string> {
    const prompt = `Generate an executive summary for a contract analysis with the following results:

Contract: ${document.originalName}
Overall Score: ${overallScore}%
Total Deviations: ${deviations.length}

Key Issues:
${deviations
  .slice(0, 5)
  .map((d) => `- ${d.ruleName}: ${d.deviationType}`)
  .join('\n')}

Provide a concise executive summary (2-3 paragraphs) highlighting the main risks and recommendations.`;

    try {
      const summary = await this.aiService.generateResponse(prompt, {
        temperature: 0.3,
      });
      return summary.trim();
    } catch (error) {
      this.logger.error(
        `Failed to generate executive summary: ${error.message}`,
      );
      return `Contract analysis completed with ${deviations.length} deviations found. Overall compliance score: ${overallScore}%. Review recommended for critical and high-risk items.`;
    }
  }

  private calculateConfidenceScore(deviations: any[]): number {
    if (deviations.length === 0) return 1.0;

    const avgConfidence =
      deviations.reduce((sum, dev) => sum + (dev.confidence || 0.85), 0) /
      deviations.length;
    return Math.round(avgConfidence * 100) / 100;
  }

  // Analysis Search and Management
  async findAllAnalyses(
    organizationId: string,
    options: SearchAnalysesDto = {},
  ): Promise<AnalysisListResponse> {
    this.logger.log(`Finding analyses for org: ${organizationId}`);

    const {
      contractId,
      playbookId,
      riskLevel,
      status,
      startDate,
      endDate,
      page = 1,
      limit = 20,
    } = options;

    // Build filter
    const filter: any = { organizationId };

    if (contractId) filter.contractId = contractId;
    if (playbookId) filter.playbookId = playbookId;
    if (riskLevel) filter.riskLevel = riskLevel;
    if (status) filter.status = status;

    if (startDate || endDate) {
      filter.analyzedAt = {};
      if (startDate) filter.analyzedAt.$gte = new Date(startDate);
      if (endDate) filter.analyzedAt.$lte = new Date(endDate);
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [analyses, total] = await Promise.all([
      this.analysisModel
        .find(filter)
        .sort({ analyzedAt: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      this.analysisModel.countDocuments(filter),
    ]);

    return {
      analyses: analyses as IContractAnalysis[],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findAnalysisById(
    id: string,
    organizationId: string,
  ): Promise<IContractAnalysis> {
    const analysis = await this.analysisModel
      .findOne({ id, organizationId })
      .lean();

    if (!analysis) {
      throw new NotFoundException(`Analysis with ID ${id} not found`);
    }

    return analysis as IContractAnalysis;
  }

  async deleteAnalysis(
    id: string,
    organizationId: string,
    userId: string,
  ): Promise<void> {
    this.logger.log(`Deleting analysis: ${id} for org: ${organizationId}`);

    const analysis = await this.analysisModel.findOne({ id, organizationId });

    if (!analysis) {
      throw new NotFoundException(`Analysis with ID ${id} not found`);
    }

    await this.analysisModel.deleteOne({ id, organizationId });

    // Analytics tracking would go here
    this.logger.log(`Analysis deleted: ${id}`);
  }

  // Analytics and Reporting
  async getPlaybookAnalytics(
    playbookId: string,
    organizationId: string,
  ): Promise<PlaybookAnalyticsResponse> {
    this.logger.log(`Getting analytics for playbook: ${playbookId}`);

    const analyses = await this.analysisModel
      .find({ playbookId, organizationId, status: 'COMPLETED' })
      .lean();

    if (analyses.length === 0) {
      return {
        usage: {
          totalAnalyses: 0,
          averageScore: 0,
          mostCommonDeviations: [],
          riskDistribution: {},
        },
        performance: {
          averageProcessingTime: 0,
          successRate: 0,
          userSatisfaction: 0,
        },
        trends: {
          monthlyAnalyses: [],
          riskTrends: [],
        },
      };
    }

    // Calculate usage metrics
    const totalAnalyses = analyses.length;
    const averageScore =
      analyses.reduce((sum, a) => sum + a.overallScore, 0) / totalAnalyses;

    // Find most common deviations
    const deviationCounts = {};
    analyses.forEach((analysis) => {
      analysis.deviations.forEach((deviation) => {
        deviationCounts[deviation.deviationType] =
          (deviationCounts[deviation.deviationType] || 0) + 1;
      });
    });

    const mostCommonDeviations = Object.entries(deviationCounts)
      .sort(([, a], [, b]) => (b as number) - (a as number))
      .slice(0, 5)
      .map(([type]) => type);

    // Risk distribution
    const riskDistribution = analyses.reduce((dist, analysis) => {
      dist[analysis.riskLevel] = (dist[analysis.riskLevel] || 0) + 1;
      return dist;
    }, {});

    // Performance metrics
    const averageProcessingTime =
      analyses.reduce((sum, a) => sum + a.metrics.processingTimeMs, 0) /
      totalAnalyses;
    const successRate = 100; // All completed analyses are successful

    // Monthly trends
    const monthlyData = {};
    analyses.forEach((analysis) => {
      const month = analysis.analyzedAt.toISOString().substring(0, 7); // YYYY-MM
      monthlyData[month] = (monthlyData[month] || 0) + 1;
    });

    const monthlyAnalyses = Object.entries(monthlyData)
      .map(([month, count]) => ({ month, count: count as number }))
      .sort((a, b) => a.month.localeCompare(b.month));

    return {
      usage: {
        totalAnalyses,
        averageScore: Math.round(averageScore),
        mostCommonDeviations,
        riskDistribution,
      },
      performance: {
        averageProcessingTime: Math.round(averageProcessingTime),
        successRate,
        userSatisfaction: 85, // Default value
      },
      trends: {
        monthlyAnalyses,
        riskTrends: [], // Could be implemented with more historical data
      },
    };
  }

  // Utility Methods
  async duplicatePlaybook(
    id: string,
    organizationId: string,
    userId: string,
    newName?: string,
  ): Promise<IContractPlaybook> {
    const originalPlaybook = await this.findPlaybookById(id, organizationId);

    // Create a clean copy without MongoDB-specific fields
    const duplicatedPlaybook = {
      name: newName || `${originalPlaybook.name} (Copy)`,
      contractType: originalPlaybook.contractType,
      description: originalPlaybook.description,
      version: '1.0.0',
      metadata: originalPlaybook.metadata,
      isActive: originalPlaybook.isActive,
      isTemplate: originalPlaybook.isTemplate,
      rules: originalPlaybook.rules.map((rule) => ({
        name: rule.name,
        category: rule.category,
        ruleType: rule.ruleType,
        severity: rule.severity,
        criteria: rule.criteria,
        acceptableLanguage: rule.acceptableLanguage,
        unacceptableTerms: rule.unacceptableTerms,
        description: rule.description,
        isActive: rule.isActive,
      })),
    };

    return this.createPlaybook(
      duplicatedPlaybook as CreateContractPlaybookDto,
      organizationId,
      userId,
    );
  }

  async exportPlaybook(id: string, organizationId: string): Promise<any> {
    const playbook = await this.findPlaybookById(id, organizationId);

    // Remove sensitive fields for export
    const exportData = {
      ...playbook,
      organizationId: undefined,
      createdBy: undefined,
      updatedBy: undefined,
      isTemplate: true, // Mark as template for import
    };

    return exportData;
  }

  async importPlaybook(
    playbookData: any,
    organizationId: string,
    userId: string,
  ): Promise<IContractPlaybook> {
    // Validate and sanitize imported data
    const importDto: CreateContractPlaybookDto = {
      name: playbookData.name,
      contractType: playbookData.contractType,
      description: playbookData.description,
      version: playbookData.version || '1.0.0',
      rules: playbookData.rules || [],
      metadata: playbookData.metadata || {},
      isTemplate: false, // Convert template to regular playbook
    };

    return this.createPlaybook(importDto, organizationId, userId);
  }
}

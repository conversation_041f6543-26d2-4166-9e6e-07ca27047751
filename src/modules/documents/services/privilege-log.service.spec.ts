import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { PrivilegeLogService } from './privilege-log.service';
import { AIService } from '../../ai/services/ai.service';
import { DocumentsService } from './documents.service';
import { AnalyticsService } from '../../analytics/services/analytics.service';
import { PrivilegeLogDocument, PRIVILEGE_LOG_MODEL } from '../schemas/privilege-log.schema';
import { PrivilegeType, PrivilegeStatus, PrivilegeLogStatus } from '../interfaces/privilege-log.interface';

describe('PrivilegeLogService', () => {
  let service: PrivilegeLogService;
  let privilegeLogModel: jest.Mocked<Model<PrivilegeLogDocument>>;
  let aiService: jest.Mocked<AIService>;
  let documentsService: jest.Mocked<DocumentsService>;
  let analyticsService: jest.Mocked<AnalyticsService>;

  const mockDocument = {
    id: 'doc-123',
    title: 'Test Contract',
    content: 'This is a confidential attorney-client communication regarding legal advice.',
    organizationId: 'org-123',
  };

  const mockPrivilegeLog = {
    id: 'log-123',
    documentId: 'doc-123',
    documentTitle: 'Test Contract',
    privilegedContent: [
      {
        id: 'content-123',
        documentId: 'doc-123',
        startPosition: 10,
        endPosition: 50,
        content: 'attorney-client communication',
        privilegeType: PrivilegeType.ATTORNEY_CLIENT,
        confidenceScore: 0.9,
        detectionMethod: 'pattern',
        status: PrivilegeStatus.DETECTED,
        redactionApplied: false,
      },
    ],
    totalPrivilegedItems: 1,
    totalRedactions: 0,
    analysisDate: new Date(),
    status: PrivilegeLogStatus.PENDING_REVIEW,
    organizationId: 'org-123',
    createdBy: 'user-123',
    save: jest.fn().mockResolvedValue(this),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PrivilegeLogService,
        {
          provide: getModelToken(PRIVILEGE_LOG_MODEL),
          useValue: {
            new: jest.fn().mockResolvedValue(mockPrivilegeLog),
            constructor: jest.fn().mockResolvedValue(mockPrivilegeLog),
            find: jest.fn(),
            findOne: jest.fn(),
            countDocuments: jest.fn(),
            exec: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: AIService,
          useValue: {
            generateResponse: jest.fn(),
          },
        },
        {
          provide: DocumentsService,
          useValue: {
            findById: jest.fn(),
          },
        },
        {
          provide: AnalyticsService,
          useValue: {
            trackPrivilegeAnalysis: jest.fn(),
            trackRedactionApplied: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<PrivilegeLogService>(PrivilegeLogService);
    privilegeLogModel = module.get(getModelToken(PRIVILEGE_LOG_MODEL));
    aiService = module.get(AIService);
    documentsService = module.get(DocumentsService);
    analyticsService = module.get(AnalyticsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('analyzeDocumentPrivilege', () => {
    it('should analyze document and detect privileged content', async () => {
      // Arrange
      documentsService.findById.mockResolvedValue(mockDocument);
      privilegeLogModel.prototype.save = jest.fn().mockResolvedValue(mockPrivilegeLog);
      analyticsService.trackPrivilegeAnalysis.mockResolvedValue(undefined);

      const options = {
        includeAIAnalysis: false,
        confidenceThreshold: 0.7,
      };

      // Act
      const result = await service.analyzeDocumentPrivilege(
        'doc-123',
        options,
        'user-123',
        'org-123',
      );

      // Assert
      expect(result).toBeDefined();
      expect(result.documentId).toBe('doc-123');
      expect(result.privilegedContent).toHaveLength(1);
      expect(result.privilegedContent[0].privilegeType).toBe(PrivilegeType.ATTORNEY_CLIENT);
      expect(result.summary.totalItemsFound).toBe(1);
      expect(documentsService.findById).toHaveBeenCalledWith('doc-123');
      expect(analyticsService.trackPrivilegeAnalysis).toHaveBeenCalled();
    });

    it('should include AI analysis when enabled', async () => {
      // Arrange
      documentsService.findById.mockResolvedValue(mockDocument);
      aiService.generateResponse.mockResolvedValue(
        JSON.stringify({
          privilegedItems: [
            {
              content: 'legal advice',
              privilegeType: 'attorney_client',
              confidenceScore: 0.85,
              startPosition: 60,
              endPosition: 72,
            },
          ],
        }),
      );
      privilegeLogModel.prototype.save = jest.fn().mockResolvedValue(mockPrivilegeLog);

      const options = {
        includeAIAnalysis: true,
        confidenceThreshold: 0.7,
      };

      // Act
      const result = await service.analyzeDocumentPrivilege(
        'doc-123',
        options,
        'user-123',
        'org-123',
      );

      // Assert
      expect(aiService.generateResponse).toHaveBeenCalled();
      expect(result.analysisMetadata.detectionMethods).toContain('ai');
    });

    it('should throw NotFoundException when document not found', async () => {
      // Arrange
      documentsService.findById.mockResolvedValue(null);

      const options = {
        includeAIAnalysis: false,
        confidenceThreshold: 0.7,
      };

      // Act & Assert
      await expect(
        service.analyzeDocumentPrivilege('doc-123', options, 'user-123', 'org-123'),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('getPrivilegeLog', () => {
    it('should return privilege log for document', async () => {
      // Arrange
      privilegeLogModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(mockPrivilegeLog),
      } as any);

      // Act
      const result = await service.getPrivilegeLog('doc-123', 'org-123');

      // Assert
      expect(result).toBe(mockPrivilegeLog);
      expect(privilegeLogModel.findOne).toHaveBeenCalledWith({
        documentId: 'doc-123',
        organizationId: 'org-123',
      });
    });

    it('should throw NotFoundException when privilege log not found', async () => {
      // Arrange
      privilegeLogModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      } as any);

      // Act & Assert
      await expect(service.getPrivilegeLog('doc-123', 'org-123')).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('reviewPrivilegedContent', () => {
    it('should update privileged content status', async () => {
      // Arrange
      const mockLog = {
        ...mockPrivilegeLog,
        privilegedContent: [
          {
            id: 'content-123',
            status: PrivilegeStatus.DETECTED,
          },
        ],
        save: jest.fn().mockResolvedValue(mockPrivilegeLog),
      };

      jest.spyOn(service, 'getPrivilegeLog').mockResolvedValue(mockLog as any);

      const reviewData = {
        contentId: 'content-123',
        status: PrivilegeStatus.CONFIRMED,
        reason: 'Confirmed attorney-client privilege',
      };

      // Act
      const result = await service.reviewPrivilegedContent(
        'doc-123',
        'content-123',
        reviewData,
        'user-123',
        'org-123',
      );

      // Assert
      expect(mockLog.privilegedContent[0].status).toBe(PrivilegeStatus.CONFIRMED);
      expect(mockLog.save).toHaveBeenCalled();
    });

    it('should throw NotFoundException when content item not found', async () => {
      // Arrange
      const mockLog = {
        ...mockPrivilegeLog,
        privilegedContent: [],
      };

      jest.spyOn(service, 'getPrivilegeLog').mockResolvedValue(mockLog as any);

      const reviewData = {
        contentId: 'nonexistent',
        status: PrivilegeStatus.CONFIRMED,
      };

      // Act & Assert
      await expect(
        service.reviewPrivilegedContent('doc-123', 'nonexistent', reviewData, 'user-123', 'org-123'),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('applyRedaction', () => {
    it('should apply redaction to privileged content', async () => {
      // Arrange
      const mockLog = {
        ...mockPrivilegeLog,
        privilegedContent: [
          {
            id: 'content-123',
            privilegeType: PrivilegeType.ATTORNEY_CLIENT,
            redactionApplied: false,
          },
        ],
        totalRedactions: 0,
        save: jest.fn().mockResolvedValue(mockPrivilegeLog),
      };

      jest.spyOn(service, 'getPrivilegeLog').mockResolvedValue(mockLog as any);
      analyticsService.trackRedactionApplied.mockResolvedValue(undefined);

      const redactionData = {
        contentId: 'content-123',
        reason: 'Attorney-client privilege protection',
      };

      // Act
      const result = await service.applyRedaction(
        'doc-123',
        redactionData,
        'user-123',
        'org-123',
      );

      // Assert
      expect(result.success).toBe(true);
      expect(mockLog.privilegedContent[0].redactionApplied).toBe(true);
      expect(mockLog.totalRedactions).toBe(1);
      expect(analyticsService.trackRedactionApplied).toHaveBeenCalled();
    });
  });

  describe('applyBulkRedaction', () => {
    it('should apply redactions to multiple content items', async () => {
      // Arrange
      const mockLog = {
        ...mockPrivilegeLog,
        privilegedContent: [
          { id: 'content-1', redactionApplied: false },
          { id: 'content-2', redactionApplied: false },
          { id: 'content-3', redactionApplied: false },
        ],
        totalRedactions: 0,
        save: jest.fn().mockResolvedValue(mockPrivilegeLog),
      };

      jest.spyOn(service, 'getPrivilegeLog').mockResolvedValue(mockLog as any);

      const bulkData = {
        contentIds: ['content-1', 'content-2'],
        reason: 'Bulk privilege protection',
      };

      // Act
      const result = await service.applyBulkRedaction(
        'doc-123',
        bulkData,
        'user-123',
        'org-123',
      );

      // Assert
      expect(result.success).toBe(true);
      expect(result.redactedCount).toBe(2);
      expect(mockLog.totalRedactions).toBe(2);
    });
  });
});

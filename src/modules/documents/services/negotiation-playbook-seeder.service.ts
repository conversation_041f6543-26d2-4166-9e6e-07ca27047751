import { Injectable } from '@nestjs/common';
import { NegotiationPlaybook } from '../interfaces/negotiation-playbook.interface';
import { DocumentType } from '../../../common/enums/document-type.enum';

@Injectable()
export class SamplePlaybooksDataService {
  /**
   * Get all sample negotiation playbooks from code
   */
  getSamplePlaybooks(): NegotiationPlaybook[] {
    return this.createSamplePlaybooks();
  }

  /**
   * Get a specific sample playbook by ID
   */
  getSamplePlaybookById(playbookId: string): NegotiationPlaybook | null {
    const samples = this.getSamplePlaybooks();
    return samples.find(sample => sample.documentId === playbookId) || null;
  }

  /**
   * Get sample playbooks with filtering
   */
  getFilteredSamplePlaybooks(filters?: {
    contractType?: DocumentType;
    industry?: string;
    difficulty?: string;
    tags?: string[];
  }): NegotiationPlaybook[] {
    let samples = this.getSamplePlaybooks();

    if (filters) {
      if (filters.contractType) {
        samples = samples.filter(sample => sample.contractType === filters.contractType);
      }
      if (filters.industry) {
        samples = samples.filter(sample => sample.industry === filters.industry);
      }
      if (filters.difficulty) {
        samples = samples.filter(sample => sample.difficulty === filters.difficulty);
      }
      if (filters.tags && filters.tags.length > 0) {
        samples = samples.filter(sample =>
          sample.tags && sample.tags.some(tag => filters.tags!.includes(tag))
        );
      }
    }

    return samples.sort((a, b) => {
      // Sort by difficulty (beginner first), then by contract type, then by name
      const difficultyOrder = { 'beginner': 1, 'intermediate': 2, 'expert': 3 };
      const aDiff = difficultyOrder[a.difficulty as keyof typeof difficultyOrder] || 4;
      const bDiff = difficultyOrder[b.difficulty as keyof typeof difficultyOrder] || 4;

      if (aDiff !== bDiff) return aDiff - bDiff;
      if (a.contractType !== b.contractType) return (a.contractType || '').localeCompare(b.contractType || '');
      return (a.templateName || '').localeCompare(b.templateName || '');
    });
  }

  /**
   * Create comprehensive sample playbooks for common contract types
   */
  private createSamplePlaybooks(): NegotiationPlaybook[] {
    return [
      // Service Agreement - Beginner
      {
        documentId: 'template-service-agreement-beginner',
        templateName: 'Service Agreement - Basic Negotiation',
        templateDescription: 'Essential negotiation strategies for standard service agreements',
        contractType: DocumentType.SERVICE_AGREEMENT,
        industry: 'general',
        difficulty: 'beginner',
        isTemplate: true,
        tags: ['service', 'basic', 'beginner'],
        strategies: [
          {
            section: 'Payment Terms',
            recommendations: [
              'Negotiate for shorter payment cycles (Net 15 instead of Net 30)',
              'Include late payment penalties (1.5% per month)',
              'Request partial upfront payment for large projects'
            ],
            riskLevel: 'low',
            priority: 1,
            alternativeLanguage: 'Payment shall be due within fifteen (15) days of invoice date. Late payments shall incur a penalty of 1.5% per month.',
            simulationScenarios: [
              {
                type: 'concession',
                trigger: 'Client requests Net 45 payment terms',
                responseStrategy: 'Offer Net 30 with early payment discount of 2%',
                expectedOutcome: 'Agreement on Net 30 with early payment incentive'
              }
            ]
          },
          {
            section: 'Scope of Work',
            recommendations: [
              'Define deliverables with specific, measurable outcomes',
              'Include change order process for scope modifications',
              'Set clear boundaries on what is included vs. additional services'
            ],
            riskLevel: 'medium',
            priority: 2,
            alternativeLanguage: 'Services shall be limited to those specifically described in Exhibit A. Any additional services require written approval and separate compensation.',
            simulationScenarios: [
              {
                type: 'dealbreaker',
                trigger: 'Client wants unlimited revisions included',
                responseStrategy: 'Propose specific number of revisions (e.g., 3) with additional revisions at hourly rate',
                expectedOutcome: 'Agreement on limited revisions with clear additional cost structure'
              }
            ]
          }
        ],
        overallAssessment: 'Service agreements require clear scope definition and favorable payment terms. Focus on protecting your time and ensuring prompt payment.',
        keyLeveragePoints: [
          'Specialized expertise and skills',
          'Timeline flexibility',
          'Quality of deliverables'
        ],
        dealBreakers: [
          'Unlimited liability exposure',
          'Work-for-hire clauses for proprietary methods',
          'Non-compete restrictions that limit future business'
        ],
        timestamp: new Date(),
        usageCount: 0
      },

      // Employment Contract - Intermediate
      {
        documentId: 'template-employment-contract-intermediate',
        templateName: 'Employment Contract - Professional Level',
        templateDescription: 'Advanced negotiation tactics for employment agreements',
        contractType: DocumentType.EMPLOYMENT_CONTRACT,
        industry: 'general',
        difficulty: 'intermediate',
        isTemplate: true,
        tags: ['employment', 'salary', 'benefits'],
        strategies: [
          {
            section: 'Compensation Package',
            recommendations: [
              'Negotiate total compensation including base, bonus, and equity',
              'Request performance-based salary reviews (annually)',
              'Include cost-of-living adjustments for multi-year contracts'
            ],
            riskLevel: 'medium',
            priority: 1,
            alternativeLanguage: 'Base salary shall be reviewed annually with adjustments based on performance and market conditions, with minimum 3% cost-of-living increase.',
            simulationScenarios: [
              {
                type: 'leverage',
                trigger: 'Employer offers below market rate',
                responseStrategy: 'Present market research data and emphasize unique value proposition',
                expectedOutcome: 'Salary increase to market rate plus performance bonus structure'
              }
            ]
          },
          {
            section: 'Termination and Severance',
            recommendations: [
              'Negotiate severance package (3-6 months salary)',
              'Include notice period requirements for both parties',
              'Ensure continuation of benefits during severance period'
            ],
            riskLevel: 'high',
            priority: 2,
            alternativeLanguage: 'Upon termination without cause, Employee shall receive six (6) months base salary plus continued health benefits.',
            simulationScenarios: [
              {
                type: 'compromise',
                trigger: 'Employer resists severance package',
                responseStrategy: 'Propose graduated severance based on length of service',
                expectedOutcome: 'Agreement on 1 month per year of service, minimum 3 months'
              }
            ]
          }
        ],
        overallAssessment: 'Employment contracts require balancing job security with performance expectations. Focus on total compensation and protection against unfair termination.',
        keyLeveragePoints: [
          'Specialized skills and experience',
          'Market demand for your expertise',
          'Competing job offers'
        ],
        dealBreakers: [
          'Excessive non-compete restrictions',
          'No severance protection',
          'Below-market compensation with no growth path'
        ],
        timestamp: new Date(),
        usageCount: 0
      },

      // Non-Disclosure Agreement - Beginner
      {
        documentId: 'template-nda-beginner',
        templateName: 'Non-Disclosure Agreement - Essentials',
        templateDescription: 'Key negotiation points for standard NDAs',
        contractType: DocumentType.NDA,
        industry: 'general',
        difficulty: 'beginner',
        isTemplate: true,
        tags: ['nda', 'confidentiality', 'basic'],
        strategies: [
          {
            section: 'Definition of Confidential Information',
            recommendations: [
              'Ensure mutual confidentiality (both parties protected)',
              'Exclude publicly available information from definition',
              'Include specific carve-outs for independently developed information'
            ],
            riskLevel: 'medium',
            priority: 1,
            alternativeLanguage: 'Confidential Information shall not include information that: (a) is publicly available, (b) was known prior to disclosure, or (c) is independently developed.',
            simulationScenarios: [
              {
                type: 'concession',
                trigger: 'Other party wants one-way NDA only',
                responseStrategy: 'Explain mutual benefit and propose reciprocal terms',
                expectedOutcome: 'Agreement on mutual NDA with balanced protections'
              }
            ]
          },
          {
            section: 'Term and Duration',
            recommendations: [
              'Limit confidentiality period to reasonable timeframe (3-5 years)',
              'Include return/destruction of materials clause',
              'Specify survival of obligations post-termination'
            ],
            riskLevel: 'low',
            priority: 2,
            alternativeLanguage: 'Confidentiality obligations shall survive for three (3) years from disclosure date or termination of this Agreement, whichever is later.',
            simulationScenarios: [
              {
                type: 'compromise',
                trigger: 'Other party requests perpetual confidentiality',
                responseStrategy: 'Propose longer but finite term (5-7 years) for truly sensitive information',
                expectedOutcome: 'Tiered confidentiality periods based on information sensitivity'
              }
            ]
          }
        ],
        overallAssessment: 'NDAs should provide balanced protection while allowing normal business operations. Focus on mutual terms and reasonable time limits.',
        keyLeveragePoints: [
          'Mutual need for confidentiality protection',
          'Standard industry practices',
          'Reciprocal business relationship'
        ],
        dealBreakers: [
          'Perpetual confidentiality obligations',
          'Overly broad definition of confidential information',
          'One-way protection favoring other party'
        ],
        timestamp: new Date(),
        usageCount: 0
      },

      // Software License Agreement - Expert
      {
        documentId: 'template-software-license-expert',
        templateName: 'Software License - Advanced Negotiations',
        templateDescription: 'Complex negotiation strategies for enterprise software licensing',
        contractType: DocumentType.LICENSING_AGREEMENT,
        industry: 'technology',
        difficulty: 'expert',
        isTemplate: true,
        tags: ['software', 'licensing', 'technology', 'enterprise'],
        strategies: [
          {
            section: 'Licensing Metrics and Scalability',
            recommendations: [
              'Negotiate volume discounts and tiered pricing',
              'Include true-up provisions for usage overages',
              'Secure rights to use in disaster recovery environments',
              'Negotiate development/testing environment rights'
            ],
            riskLevel: 'high',
            priority: 1,
            alternativeLanguage: 'License includes unlimited use in non-production environments and disaster recovery sites with no additional fees.',
            simulationScenarios: [
              {
                type: 'leverage',
                trigger: 'Vendor demands per-user pricing for enterprise deployment',
                responseStrategy: 'Propose site license or concurrent user model with growth caps',
                expectedOutcome: 'Hybrid model with base site fee plus reasonable per-user charges above threshold'
              }
            ]
          },
          {
            section: 'Service Level Agreements',
            recommendations: [
              'Define specific uptime requirements (99.9% minimum)',
              'Include response time commitments for different severity levels',
              'Negotiate service credits for SLA breaches',
              'Require regular performance reporting'
            ],
            riskLevel: 'high',
            priority: 2,
            alternativeLanguage: 'Vendor guarantees 99.95% uptime with 4-hour response for critical issues and service credits of 10% monthly fee per 1% below SLA.',
            simulationScenarios: [
              {
                type: 'dealbreaker',
                trigger: 'Vendor refuses SLA commitments or service credits',
                responseStrategy: 'Emphasize business criticality and competitive alternatives',
                expectedOutcome: 'Vendor agrees to reasonable SLAs with meaningful penalties'
              }
            ]
          }
        ],
        overallAssessment: 'Enterprise software licenses require careful attention to scalability, performance guarantees, and total cost of ownership. Leverage competitive alternatives.',
        keyLeveragePoints: [
          'Multi-year commitment value',
          'Competitive vendor alternatives',
          'Integration complexity and switching costs',
          'Reference customer value to vendor'
        ],
        dealBreakers: [
          'No SLA guarantees or service credits',
          'Unlimited liability for software defects',
          'Restrictive audit rights with unlimited frequency',
          'No source code escrow for critical systems'
        ],
        timestamp: new Date(),
        usageCount: 0
      },

      // Consulting Agreement - Intermediate
      {
        documentId: 'template-consulting-agreement-intermediate',
        templateName: 'Consulting Agreement - Professional Services',
        templateDescription: 'Strategic negotiation for consulting and professional services contracts',
        contractType: DocumentType.CONSULTING_AGREEMENT,
        industry: 'consulting',
        difficulty: 'intermediate',
        isTemplate: true,
        tags: ['consulting', 'professional-services', 'hourly'],
        strategies: [
          {
            section: 'Intellectual Property Rights',
            recommendations: [
              'Retain ownership of pre-existing IP and methodologies',
              'Negotiate shared rights for jointly developed IP',
              'Include work-for-hire exceptions for proprietary tools',
              'Secure right to use work product for marketing/portfolio'
            ],
            riskLevel: 'high',
            priority: 1,
            alternativeLanguage: 'Consultant retains all rights to pre-existing IP and general methodologies. Client owns work product specific to their business with Consultant retaining portfolio rights.',
            simulationScenarios: [
              {
                type: 'dealbreaker',
                trigger: 'Client demands all IP rights including pre-existing tools',
                responseStrategy: 'Explain value of proprietary methodologies and offer licensing alternative',
                expectedOutcome: 'Agreement on work product ownership with IP licensing for tools'
              }
            ]
          },
          {
            section: 'Rate Structure and Expenses',
            recommendations: [
              'Establish blended rates for different skill levels',
              'Include annual rate escalation clause (3-5%)',
              'Define reimbursable expenses clearly',
              'Negotiate minimum monthly commitments for retainer work'
            ],
            riskLevel: 'medium',
            priority: 2,
            alternativeLanguage: 'Rates shall increase annually by 4% or CPI, whichever is greater. All reasonable business expenses pre-approved in writing shall be reimbursed.',
            simulationScenarios: [
              {
                type: 'leverage',
                trigger: 'Client pushes for fixed rates over multi-year term',
                responseStrategy: 'Propose rate caps with inflation adjustments and performance bonuses',
                expectedOutcome: 'Hybrid model with modest annual increases and performance incentives'
              }
            ]
          }
        ],
        overallAssessment: 'Consulting agreements require careful balance of IP protection, fair compensation, and client relationship management. Focus on long-term value creation.',
        keyLeveragePoints: [
          'Specialized expertise and track record',
          'Proprietary methodologies and tools',
          'Client relationship and trust',
          'Market demand for services'
        ],
        dealBreakers: [
          'Assignment of all IP including pre-existing tools',
          'Unlimited liability for recommendations',
          'Non-compete clauses preventing similar work',
          'Payment terms longer than Net 30'
        ],
        timestamp: new Date(),
        usageCount: 0
      },

      // Vendor Agreement - Intermediate
      {
        documentId: 'template-vendor-agreement-intermediate',
        templateName: 'Vendor/Supplier Agreement - Supply Chain',
        templateDescription: 'Key negotiation strategies for vendor and supplier relationships',
        contractType: DocumentType.VENDOR_AGREEMENT,
        industry: 'manufacturing',
        difficulty: 'intermediate',
        isTemplate: true,
        tags: ['vendor', 'supplier', 'procurement', 'supply-chain'],
        strategies: [
          {
            section: 'Pricing and Payment Terms',
            recommendations: [
              'Negotiate volume discounts and tiered pricing',
              'Include price protection clauses against market fluctuations',
              'Secure most favored customer pricing',
              'Establish clear payment terms with early payment discounts'
            ],
            riskLevel: 'medium',
            priority: 1,
            alternativeLanguage: 'Vendor guarantees pricing will not exceed rates offered to any other customer for similar volumes. Prices protected for 12 months with 90-day notice for increases.',
            simulationScenarios: [
              {
                type: 'concession',
                trigger: 'Vendor requests price increases due to material costs',
                responseStrategy: 'Propose cost-plus model with transparency requirements and caps',
                expectedOutcome: 'Agreement on limited pass-through costs with documentation requirements'
              }
            ]
          },
          {
            section: 'Quality and Performance Standards',
            recommendations: [
              'Define specific quality metrics and acceptance criteria',
              'Include right to audit vendor facilities and processes',
              'Establish corrective action procedures for defects',
              'Negotiate warranty periods and replacement guarantees'
            ],
            riskLevel: 'high',
            priority: 2,
            alternativeLanguage: 'All products must meet specified quality standards with 99.5% acceptance rate. Vendor warrants products for 24 months and will replace defective items at no cost.',
            simulationScenarios: [
              {
                type: 'compromise',
                trigger: 'Vendor resists extensive quality requirements',
                responseStrategy: 'Propose graduated quality standards with incentives for exceeding targets',
                expectedOutcome: 'Tiered quality system with bonuses for superior performance'
              }
            ]
          }
        ],
        overallAssessment: 'Vendor agreements require balancing cost control with quality assurance and supply security. Focus on long-term partnership value.',
        keyLeveragePoints: [
          'Volume commitment and growth potential',
          'Long-term relationship value',
          'Alternative supplier options',
          'Market position and brand value'
        ],
        dealBreakers: [
          'No quality guarantees or warranties',
          'Unlimited price increase rights',
          'Exclusive dealing requirements without reciprocal benefits',
          'No termination rights for performance failures'
        ],
        timestamp: new Date(),
        usageCount: 0
      },

      // Partnership Agreement - Expert
      {
        documentId: 'template-partnership-agreement-expert',
        templateName: 'Partnership Agreement - Strategic Alliances',
        templateDescription: 'Complex negotiation strategies for business partnerships and joint ventures',
        contractType: DocumentType.PARTNERSHIP_AGREEMENT,
        industry: 'general',
        difficulty: 'expert',
        isTemplate: true,
        tags: ['partnership', 'joint-venture', 'strategic-alliance', 'equity'],
        strategies: [
          {
            section: 'Profit and Loss Distribution',
            recommendations: [
              'Negotiate fair profit-sharing based on contribution and risk',
              'Include provisions for reinvestment and capital calls',
              'Define clear accounting and reporting standards'
            ],
            riskLevel: 'high',
            priority: 1,
            alternativeLanguage: 'Profits and losses shall be allocated based on each partner\'s capital contribution percentage, with quarterly distributions subject to cash flow requirements.',
            simulationScenarios: [
              {
                type: 'dealbreaker',
                trigger: 'Partner demands disproportionate profit share',
                responseStrategy: 'Propose performance-based profit sharing with clear metrics',
                expectedOutcome: 'Agreement on merit-based profit distribution with regular review'
              }
            ]
          }
        ],
        overallAssessment: 'Partnership agreements require careful balance of control, contribution, and rewards. Focus on clear governance and exit strategies.',
        keyLeveragePoints: [
          'Unique capabilities and resources',
          'Market access and relationships',
          'Financial contribution and risk tolerance'
        ],
        dealBreakers: [
          'Unequal decision-making rights without justification',
          'No clear exit strategy or dissolution terms',
          'Unlimited personal liability exposure'
        ],
        timestamp: new Date(),
        usageCount: 0
      },

      // Master Service Agreement - Intermediate
      {
        documentId: 'template-msa-intermediate',
        templateName: 'Master Service Agreement - Enterprise Framework',
        templateDescription: 'Comprehensive negotiation strategies for enterprise MSAs',
        contractType: DocumentType.MASTER_SERVICE_AGREEMENT,
        industry: 'technology',
        difficulty: 'intermediate',
        isTemplate: true,
        tags: ['msa', 'enterprise', 'framework', 'technology'],
        strategies: [
          {
            section: 'Service Level Agreements',
            recommendations: [
              'Define measurable SLAs with appropriate penalties',
              'Include escalation procedures for service failures',
              'Negotiate reasonable uptime guarantees (99.5% vs 99.9%)'
            ],
            riskLevel: 'medium',
            priority: 1,
            alternativeLanguage: 'Service Provider shall maintain 99.5% uptime measured monthly, with service credits of 5% of monthly fees for each 0.1% below target.',
            simulationScenarios: [
              {
                type: 'concession',
                trigger: 'Client demands 99.99% uptime guarantee',
                responseStrategy: 'Offer tiered SLA with premium pricing for higher guarantees',
                expectedOutcome: 'Agreement on 99.9% standard with 99.99% premium option'
              }
            ]
          }
        ],
        overallAssessment: 'MSAs establish the foundation for ongoing business relationships. Focus on scalable terms and clear governance.',
        keyLeveragePoints: [
          'Long-term relationship value',
          'Volume commitments and economies of scale',
          'Specialized expertise and technology'
        ],
        dealBreakers: [
          'Unrealistic SLA requirements',
          'Unlimited liability for consequential damages',
          'One-sided termination rights'
        ],
        timestamp: new Date(),
        usageCount: 0
      }
    ];
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CourtListenerService } from '../../shared/court-listener/court-listener.service';
import {
  AIService,
  PrecedentAIAnalysisInput,
} from '../../ai/services/ai.service';
import {
  PrecedentAnalysisOptions,
  PrecedentAnalysisResult,
  RelatedCase,
  KeyPoint,
  TimelineEntry,
} from '@modules/documents/interfaces/precedent-analysis.interface';
import { DepositionAnalysisResult } from '../interfaces/deposition-analysis.interface';
import { AnalyzeDepositionDto } from '../dto/deposition-analysis.dto';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class PrecedentService {
  private readonly logger = new Logger(PrecedentService.name);
  
  // The analyzeDeposition method has been moved to DepositionService

  /**
   * Get suggested cross-examination questions for a deposition
   */
  async getCrossExaminationSuggestions(
    depositionId: string,
    options?: { limit?: number; minConfidence?: number },
  ) {
    // Implementation would fetch analysis and return cross-examination suggestions
    // This is a placeholder implementation
    return [];
  }

  /**
   * Get credibility assessment for a specific witness
   */
  async getWitnessCredibilityAssessment(
    depositionId: string,
    witnessName: string,
  ) {
    // Implementation would fetch analysis and return credibility assessment
    // This is a placeholder implementation
    return null;
  }

  private readonly citationPatterns = [
    /\b\d+\s+U\.S\.\s+\d+\b/g,
    /\b\d+\s+S\.\s*Ct\.\s+\d+\b/g,
    /\b\d+\s+L\.\s*Ed\.\s*(?:2d)?\s+\d+\b/g,
    /\b\d+\s+F\.(?:2d|3d|4th)?\s+\d+\b/g,
    /\b\d+\s+F\.\s*Supp\.\s*(?:2d|3d)?\s+\d+\b/g,
    /\b\d+\s+F\.\s*R\.\s*D\.\s+\d+\b/g,
    /\b\d+\s+B\.\s*R\.\s+\d+\b/g,
    /\b\d+\s+A\.(?:2d|3d)?\s+\d+\b/g,
    /\b\d+\s+N\.\s*E\.(?:2d|3d)?\s+\d+\b/g,
    /\b\d+\s+N\.\s*W\.(?:2d|3d)?\s+\d+\b/g,
    /\b\d+\s+P\.(?:2d|3d)?\s+\d+\b/g,
    /\b\d+\s+S\.\s*E\.(?:2d|3d)?\s+\d+\b/g,
    /\b\d+\s+S\.\s*W\.(?:2d|3d)?\s+\d+\b/g,
    /\b\d+\s+So\.(?:2d|3d)?\s+\d+\b/g,
    /\b[A-Z][A-Za-z\s\&\,\.]+\s(?:v\.|versus)\s[A-Z][A-Za-z\s\&\,\.]+.*?\d+\s+[A-Za-z\.\s]+\d+/g,
  ];

  private readonly legalCategories = [
    {
      name: 'Constitutional Law',
      keywords: [
        'constitution',
        'constitutional',
        'amendment',
        'bill of rights',
        'equal protection',
        'due process',
      ],
    },
    {
      name: 'Contract Law',
      keywords: [
        'contract',
        'agreement',
        'consideration',
        'offer',
        'acceptance',
        'breach',
        'damages',
      ],
    },
    {
      name: 'Criminal Law',
      keywords: [
        'criminal',
        'felony',
        'misdemeanor',
        'prosecution',
        'defendant',
        'guilty',
        'innocence',
      ],
    },
    {
      name: 'Civil Procedure',
      keywords: [
        'procedure',
        'jurisdiction',
        'venue',
        'standing',
        'pleading',
        'discovery',
        'summary judgment',
      ],
    },
    {
      name: 'Property Law',
      keywords: [
        'property',
        'real estate',
        'land',
        'easement',
        'covenant',
        'landlord',
        'tenant',
      ],
    },
    {
      name: 'Tort Law',
      keywords: [
        'tort',
        'negligence',
        'liability',
        'damages',
        'injury',
        'duty of care',
        'proximate cause',
      ],
    },
    {
      name: 'Administrative Law',
      keywords: [
        'administrative',
        'agency',
        'regulation',
        'rulemaking',
        'adjudication',
      ],
    },
    {
      name: 'Intellectual Property',
      keywords: [
        'intellectual property',
        'patent',
        'copyright',
        'trademark',
        'trade secret',
      ],
    },
    {
      name: 'Employment Law',
      keywords: [
        'employment',
        'labor',
        'discrimination',
        'harassment',
        'wage',
        'worker',
        'union',
      ],
    },
    {
      name: 'Corporate Law',
      keywords: [
        'corporate',
        'corporation',
        'shareholder',
        'director',
        'officer',
        'fiduciary',
        'merger',
      ],
    },
  ];

  constructor(
    private readonly courtListener: CourtListenerService,
    private readonly configService: ConfigService,
    private readonly aiService: AIService,
  ) {}

  async analyzePrecedents(
    document: string,
    options: PrecedentAnalysisOptions = {},
  ): Promise<PrecedentAnalysisResult[]> {
    try {
      const analysisOptions = {
        includeRecommendations: options.includeRecommendations ?? true,
        maxRelatedCases: options.maxRelatedCases ?? 5,
        minRelevanceScore: options.minRelevanceScore ?? 0.3,
        categorize: options.categorize ?? true,
        assessImpact: options.assessImpact ?? true,
        useAIAnalysis: options.useAIAnalysis ?? false,
      };

      const citations = this.extractCitations(document);
      this.logger.debug(
        `Extracted ${citations.length} citations from document. AI Analysis: ${analysisOptions.useAIAnalysis}`,
      );

      if (citations.length === 0) {
        this.logger.warn('No citations found in document');
        return [];
      }

      const precedentAnalysisResults: (PrecedentAnalysisResult | null)[] =
        await Promise.all(
          citations.map(async (citation) => {
            try {
              const citationReport = await this.courtListener.getCitationReport(
                citation,
              );
              this.logger.debug(
                `CourtListener citationReport for ${citation}: ${JSON.stringify(
                  citationReport,
                  null,
                  2,
                )}`,
              );

              let keyPoints: KeyPoint[];
              let relatedCases: RelatedCase[];
              let relevanceScore: number;
              let impact: 'positive' | 'negative' | 'neutral' | 'unknown';
              let category: string;
              let aiReasoning: string | undefined;

              if (!citationReport) {
                this.logger.warn(
                  `No citation report found for citation: ${citation}`,
                );
                return this.createEmptyAnalysisResult(citation);
              }

              if (analysisOptions.useAIAnalysis) {
                this.logger.debug(
                  `Using AI analysis for citation: ${citation}`,
                );
                const documentContext = this.extractContextAroundCitation(
                  document,
                  citation,
                  500,
                );
                const citedCaseSummary = this.getCaseText(citationReport);

                const aiInput: PrecedentAIAnalysisInput = {
                  citationString: citation,
                  documentContext,
                  citedCaseSummary,
                };
                const aiOutput =
                  await this.aiService.analyzePrecedentContextWithAI(
                    aiInput,
                    options.aiFocus,
                  );

                if (aiOutput) {
                  relevanceScore = aiOutput.predictedRelevanceScore;
                  impact = aiOutput.predictedImpact;
                  category = aiOutput.predictedCategory;
                  aiReasoning = aiOutput.aiReasoning;
                  keyPoints = aiOutput.extractedKeyPoints?.length
                    ? aiOutput.extractedKeyPoints
                    : this.extractKeyPoints(citationReport);
                  relatedCases = aiOutput.extractedRelatedCases?.length
                    ? aiOutput.extractedRelatedCases
                    : await this.processRelatedCases(
                        citationReport,
                        document,
                        analysisOptions.maxRelatedCases,
                      );
                  this.logger.verbose(
                    `AI Analysis for ${citation}: Rel=${relevanceScore}, Imp=${impact}, Cat=${category}, KP Count: ${keyPoints.length}, RC Count: ${relatedCases.length}`,
                  );
                } else {
                  this.logger.warn(
                    `AI analysis failed for ${citation}, falling back to rule-based or defaults.`,
                  );
                  relevanceScore = this.calculateRelevanceScore(
                    citation,
                    document,
                    citationReport,
                  );
                  impact = analysisOptions.assessImpact
                    ? this.assessPrecedentImpact(
                        citation,
                        document,
                        citationReport,
                      )
                    : 'unknown';
                  category = analysisOptions.categorize
                    ? this.categorizePrecedent(
                        citation,
                        citationReport,
                        document,
                      )
                    : 'Uncategorized';
                  keyPoints = this.extractKeyPoints(citationReport);
                  relatedCases = await this.processRelatedCases(
                    citationReport,
                    document,
                    analysisOptions.maxRelatedCases,
                  );
                }
              } else {
                relevanceScore = this.calculateRelevanceScore(
                  citation,
                  document,
                  citationReport,
                );
                impact = analysisOptions.assessImpact
                  ? this.assessPrecedentImpact(
                      citation,
                      document,
                      citationReport,
                    )
                  : 'unknown';
                category = analysisOptions.categorize
                  ? this.categorizePrecedent(citation, citationReport, document)
                  : 'Uncategorized';
                keyPoints = this.extractKeyPoints(citationReport);
                relatedCases = await this.processRelatedCases(
                  citationReport,
                  document,
                  analysisOptions.maxRelatedCases,
                );
              }

              if (
                relevanceScore < analysisOptions.minRelevanceScore &&
                !analysisOptions.useAIAnalysis
              ) {
                this.logger.debug(
                  `Skipping citation ${citation} due to low rule-based relevance score: ${relevanceScore}`,
                );
                return null;
              }

              const recommendation =
                analysisOptions.includeRecommendations &&
                !analysisOptions.useAIAnalysis
                  ? this.generateRecommendation(
                      citation,
                      impact,
                      category,
                      document,
                    )
                  : analysisOptions.useAIAnalysis && aiReasoning
                  ? `AI Analysis: ${aiReasoning}`
                  : undefined;

              return {
                citation,
                relevanceScore,
                impact,
                category,
                keyPoints,
                recommendation,
                relatedCases,
                aiAnalysisDetails: analysisOptions.useAIAnalysis
                  ? { reasoning: aiReasoning, source: 'AI' }
                  : undefined,
              } as PrecedentAnalysisResult;
            } catch (error) {
              this.logger.error(
                `Error analyzing citation ${citation}: ${error.message}`,
                error.stack,
              );
              return this.createEmptyAnalysisResult(citation);
            }
          }),
        );

      return precedentAnalysisResults.filter(
        (result) => result !== null,
      ) as PrecedentAnalysisResult[];
    } catch (error) {
      this.logger.error(`Error analyzing precedents: ${error.message}`);
      throw error;
    }
  }

  private createEmptyAnalysisResult(citation: string): PrecedentAnalysisResult {
    return {
      citation,
      relevanceScore: 0,
      impact: 'unknown',
      category: 'Uncategorized',
      keyPoints: [],
      relatedCases: [],
    };
  }

  private extractContextAroundCitation(
    documentText: string,
    citation: string,
    windowSize: number,
  ): string {
    const citationIndex = documentText.indexOf(citation);
    if (citationIndex === -1) return '';
    const start = Math.max(0, citationIndex - windowSize);
    const end = Math.min(
      documentText.length,
      citationIndex + citation.length + windowSize,
    );
    return documentText.substring(start, end);
  }

  private extractCitations(text: string): string[] {
    const citations: string[] = [];
    this.citationPatterns.forEach((pattern) => {
      const matches = text.match(pattern);
      if (matches) {
        citations.push(...matches);
      }
    });
    this.logger.debug(
      `Found ${citations.length} citations before deduplication`,
    );
    const uniqueCitations = [...new Set(citations)];
    this.logger.debug(`Found ${uniqueCitations.length} unique citations`);
    return uniqueCitations;
  }

  private calculateRelevanceScore(
    citation: string,
    document: string,
    citationReport: any,
  ): number {
    let score = 0.5;
    try {
      const citationMatches = document.match(
        new RegExp(this.escapeRegExp(citation), 'g'),
      );
      const citationCount = citationMatches ? citationMatches.length : 0;
      score += Math.min(citationCount * 0.05, 0.2);
      const significantContextPatterns = [
        new RegExp(`relies\s+on\s+${this.escapeRegExp(citation)}`, 'i'),
        new RegExp(`according\s+to\s+${this.escapeRegExp(citation)}`, 'i'),
        new RegExp(`citing\s+${this.escapeRegExp(citation)}`, 'i'),
        new RegExp(`held\s+in\s+${this.escapeRegExp(citation)}`, 'i'),
        new RegExp(`${this.escapeRegExp(citation)}\s+states`, 'i'),
        new RegExp(`${this.escapeRegExp(citation)}\s+holds`, 'i'),
        new RegExp(`${this.escapeRegExp(citation)}\s+established`, 'i'),
      ];
      for (const pattern of significantContextPatterns) {
        if (pattern.test(document)) {
          score += 0.1;
          if (score >= 0.8) break;
        }
      }
      if (citation.includes('U.S.') || citation.includes('S.Ct.')) {
        score += 0.1;
      }
      return Math.max(0, Math.min(1, score));
    } catch (error) {
      this.logger.warn(
        `Error calculating relevance score for ${citation}: ${error.message}`,
      );
      return 0.5;
    }
  }

  private categorizePrecedent(
    citation: string,
    citationReport: any,
    document: string,
  ): string {
    try {
      const caseText = this.getCaseText(citationReport);
      const combinedText = caseText ? `${document} ${caseText}` : document;
      const categoryScores = this.legalCategories.map((category) => {
        let score = 0;
        for (let i = 0; i < category.keywords.length; i++) {
          const keyword = category.keywords[i];
          const exactPhrase = new RegExp(
            `\\b${this.escapeRegExp(keyword)}\\b`,
            'gi',
          );
          const matches = (combinedText.match(exactPhrase) || []).length;
          score += matches * (keyword.split(' ').length * 0.2);
        }
        const relatedConcepts = this.getRelatedLegalConcepts(category.name);
        relatedConcepts.forEach((concept) => {
          const conceptRegex = new RegExp(
            `\\b${this.escapeRegExp(concept)}\\b`,
            'gi',
          );
          score += (combinedText.match(conceptRegex) || []).length * 0.1;
        });
        return { name: category.name, score };
      });
      categoryScores.sort((a, b) => b.score - a.score);
      return categoryScores[0].score > 0.3
        ? categoryScores[0].name
        : 'Uncategorized';
    } catch (error) {
      this.logger.warn(
        `Error categorizing precedent ${citation}: ${error.message}`,
      );
      return 'Uncategorized';
    }
  }

  private assessPrecedentImpact(
    citation: string,
    document: string,
    citationReport: any,
  ): 'positive' | 'negative' | 'neutral' | 'unknown' {
    try {
      const citationIndex = document.indexOf(citation);
      const contextStart = Math.max(0, citationIndex - 500);
      const contextEnd = Math.min(
        document.length,
        citationIndex + citation.length + 500,
      );
      const citationContext = document
        .substring(contextStart, contextEnd)
        .toLowerCase();
      const impactScores = { positive: 0, negative: 0, neutral: 0 };
      const impactPatterns = [
        {
          type: 'positive',
          pattern: /supports?\s+(the\s+)?(argument|position|contention)/,
          weight: 0.3,
        },
        {
          type: 'positive',
          pattern: /(consistent|accordance)\s+with/,
          weight: 0.2,
        },
        {
          type: 'positive',
          pattern: /follow(s|ing)\s+(the\s+)?(reasoning|holding)/,
          weight: 0.25,
        },
        {
          type: 'positive',
          pattern: /(affirm|reinforce)s?\s+(the\s+)?(conclusion|argument)/,
          weight: 0.3,
        },
        {
          type: 'negative',
          pattern: /distinguish(es|ed|ing)\s+(the\s+)?case/,
          weight: 0.4,
        },
        {
          type: 'negative',
          pattern: /contrary\s+to|in\s+contrast\s+to/,
          weight: 0.3,
        },
        {
          type: 'negative',
          pattern: /overrule(s|d|ing)|abrogate(s|d|ing)/,
          weight: 0.5,
        },
        {
          type: 'negative',
          pattern:
            /(reject|rejects|rejected|rejecting)\s+(the\s+)?(argument|reasoning)/,
          weight: 0.4,
        },
        {
          type: 'neutral',
          pattern: /cite(s|d|ing)\s+(to\s+)?(the\s+)?case/,
          weight: 0.2,
        },
        { type: 'neutral', pattern: /refer(s|red|ring)\s+to/, weight: 0.15 },
        {
          type: 'neutral',
          pattern: /mention(s|ed|ing)\s+(the\s+)?case/,
          weight: 0.1,
        },
      ];
      impactPatterns.forEach(({ type, pattern, weight }) => {
        const matches = citationContext.match(pattern);
        if (matches) {
          impactScores[type] += matches.length * weight;
        }
      });
      let maxImpact: 'positive' | 'negative' | 'neutral' | 'unknown' =
        'unknown';
      let maxScore = 0;
      for (const [impact, score] of Object.entries(impactScores)) {
        if (score > maxScore) {
          maxScore = score;
          maxImpact = impact as 'positive' | 'negative' | 'neutral';
        }
      }
      return maxScore >= 0.3 ? maxImpact : 'unknown';
    } catch (error) {
      this.logger.warn(
        `Error assessing precedent impact for ${citation}: ${error.message}`,
      );
      return 'unknown';
    }
  }

  private extractKeyPoints(citationReport: any): KeyPoint[] {
    const keyPoints: KeyPoint[] = [];
    if (!citationReport?.cases?.[0])
      return [{ text: 'No key points available for this citation' }];
    const mainCase = citationReport.cases[0];
    if (mainCase.case_name)
      keyPoints.push({ text: `Case name: ${mainCase.case_name}` });
    if (mainCase.court) keyPoints.push({ text: `Court: ${mainCase.court}` });
    if (mainCase.precedential_status)
      keyPoints.push({
        text: `Precedential status: ${mainCase.precedential_status}`,
      });
    if (mainCase.date_filed)
      keyPoints.push({ text: `Filed: ${mainCase.date_filed}` });
    if (mainCase.judges) keyPoints.push({ text: `Judges: ${mainCase.judges}` });
    if (mainCase.headnotes && Array.isArray(mainCase.headnotes)) {
      mainCase.headnotes.forEach((note: string) =>
        keyPoints.push({ text: note, type: 'holding' }),
      );
    }
    return keyPoints.length > 0
      ? keyPoints
      : [{ text: 'No specific key points extracted.' }];
  }

  private generateRecommendation(
    citation: string,
    impact: 'positive' | 'negative' | 'neutral' | 'unknown',
    category: string,
    documentContext: string,
  ): string {
    if (impact === 'positive') {
      return `Consider emphasizing the ruling in ${citation} as it supports your arguments regarding ${category}.`;
    }
    if (impact === 'negative') {
      return `Be prepared to address or distinguish ${citation} as it may challenge your arguments on ${category}.`;
    }
    return `Review ${citation} for its relevance to ${category} within your document.`;
  }

  private async processRelatedCases(
    citationReport: any,
    document: string,
    maxCases: number,
  ): Promise<RelatedCase[]> {
    const relatedCases: RelatedCase[] = [];
    if (!citationReport.cases || citationReport.cases.length <= 1) return [];
    const casesToProcess = citationReport.cases.slice(1, maxCases + 1);
    for (const relatedCase of casesToProcess) {
      try {
        const caseText = this.getCaseText({ cases: [relatedCase] });
        let citation: string | undefined;
        if (relatedCase.citations?.length > 0) {
          const mainCitation = relatedCase.citations[0];
          citation = `${mainCitation.volume} ${mainCitation.reporter} ${mainCitation.page}`;
        }
        let year: number | undefined;
        if (relatedCase.date_filed) {
          const date = new Date(relatedCase.date_filed);
          year = date.getFullYear();
        }
        const relationship = await this.analyzeRelationship(document, caseText);
        const relevance = this.calculateCaseRelevance(document, caseText);
        relatedCases.push({
          caseName: relatedCase.case_name || 'Unknown Case',
          citation,
          court: relatedCase.court,
          year,
          relevance,
          relationship,
          summary:
            caseText.substring(0, 200) + (caseText.length > 200 ? '...' : ''),
        });
      } catch (error) {
        this.logger.warn(`Error processing related case: ${error.message}`);
        continue;
      }
    }
    return relatedCases;
  }

  private getCaseText(citationReport: any): string {
    this.logger.debug(`Full citationReport in getCaseText: ${JSON.stringify(citationReport)}`);
    if (!citationReport || !citationReport.cases || citationReport.cases.length === 0) {
      return `${citationReport?.citation || ''} [WARNING: NO CASE DATA AVAILABLE]`;
    }

    // Use the first case for now
    const caseData = citationReport.cases[0];
    if (!caseData) {
      return `${citationReport?.citation || ''} [WARNING: NO CASE DATA AVAILABLE]`;
    }

    // Determine data completeness to set confidence level
    const hasName = caseData.case_name && caseData.case_name.trim() !== '';
    const hasText = caseData.html_with_citations || caseData.plain_text;
    const hasHeadnotes = caseData.headnotes && Array.isArray(caseData.headnotes) && caseData.headnotes.length > 0;
    const hasTimeline = caseData.timeline && caseData.timeline.length > 0;
    const hasDocket = caseData.docket && Object.keys(caseData.docket).length > 0;
    const hasSummary = caseData.summary && caseData.summary.trim() !== '';
    
    // Calculate completeness score (simple count of available data points)
    const completenessScore = [hasName, hasText, hasHeadnotes, hasTimeline, hasDocket, hasSummary].filter(Boolean).length;
    
    // Build a comprehensive text from available data
    const textParts: string[] = [];
    
    // Add confidence indicator based on data completeness
    if (completenessScore <= 1) {
      textParts.push('[CAUTION: MINIMAL DATA AVAILABLE - Facts should be independently verified]');
    } else if (completenessScore <= 3) {
      textParts.push('[NOTE: LIMITED DATA AVAILABLE - Treat facts with caution]');
    }

    // Add case name if available
    if (hasName) {
      textParts.push(`Case: ${caseData.case_name}`);
    } else if (citationReport.citation) {
      textParts.push(`Citation: ${citationReport.citation}`);
    }

    // Add opinion text if available
    if (caseData.html_with_citations) {
      textParts.push(this.stripHtml(caseData.html_with_citations));
    } else if (caseData.plain_text) {
      textParts.push(caseData.plain_text);
    }

    // Add headnotes if available
    if (caseData.headnotes && Array.isArray(caseData.headnotes) && caseData.headnotes.length > 0) {
      textParts.push('Headnotes:');
      caseData.headnotes.forEach((headnote: string) => {
        textParts.push(`- ${headnote}`);
      });
    }

    // Extract text from the timeline if available
    if (caseData.timeline && caseData.timeline.length > 0) {
      textParts.push('Timeline:');
      const timelineTexts = caseData.timeline
        .map((item: any) => item.text || item.event || '')
        .filter((text: string) => text.trim() !== '');

      if (timelineTexts.length > 0) {
        textParts.push(timelineTexts.join('\n'));
      }
    }

    // Add docket entries if available
    if (caseData.docket?.entries?.length) {
      textParts.push('Docket Entries:');
      const docketTexts = caseData.docket.entries
        .map((e: any) => e.description || '')
        .filter(Boolean);
      
      if (docketTexts.length > 0) {
        textParts.push(docketTexts.join('\n'));
      }
    }

    // Add summary if available
    if (caseData.summary && caseData.summary.trim() !== '') {
      textParts.push(`Summary: ${caseData.summary}`);
    }
    
    // Add final warning for minimal data
    if (completenessScore <= 1) {
      textParts.push('WARNING: Due to limited data availability, the AI should not make specific factual claims about this case beyond what is explicitly provided above.');
    }

    // Add URL reference if other substantive content is missing
    if (
      textParts.length <= 2 &&
      caseData.absolute_url &&
      !caseData.headnotes?.length &&
      !caseData.docket?.entries?.length
    ) {
      textParts.push('Full case text might be available at the provided URL.');
    }

    // Return the combined text or fallback to citation with warning
    if (textParts.length === 0 || (textParts.length === 1 && textParts[0].startsWith('Citation:'))) {
      return `${citationReport.citation} [WARNING: NO SUBSTANTIVE CASE DATA AVAILABLE - The AI should not make specific factual claims about this case]`;
    }
    
    return textParts.join('\n\n');
  }

  private stripHtml(html: string): string {
    // Simple HTML tag stripping for plain text extraction
    return html.replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }

  private getRelatedLegalConcepts(category: string): string[] {
    const conceptMap: Record<string, string[]> = {
      'Constitutional Law': [
        'first amendment',
        'fourteenth amendment',
        'equal protection',
        'due process',
        'federalism',
      ],
      'Contract Law': [
        'breach of contract',
        'consideration',
        'offer and acceptance',
        'unconscionability',
      ],
      'Criminal Law': [
        'mens rea',
        'actus reus',
        'beyond reasonable doubt',
        'presumption of innocence',
      ],
      'Civil Procedure': [
        'jurisdiction',
        'venue',
        'standing',
        'pleading',
        'discovery',
        'summary judgment',
        'motion to dismiss',
      ],
      'Property Law': [
        'real property',
        'personal property',
        'easement',
        'covenant',
        'landlord tenant',
        'adverse possession',
      ],
      'Tort Law': [
        'negligence',
        'strict liability',
        'intentional tort',
        'duty of care',
        'proximate cause',
        'actual cause',
      ],
      'Administrative Law': [
        'agency',
        'regulation',
        'rulemaking',
        'adjudication',
        'administrative procedure act',
      ],
      'Intellectual Property': [
        'patent',
        'copyright',
        'trademark',
        'trade secret',
        'infringement',
        'fair use',
      ],
      'Employment Law': [
        'discrimination',
        'harassment',
        'wage and hour',
        'workers compensation',
        'labor relations',
      ],
      'Corporate Law': [
        'shareholder',
        'director',
        'officer',
        'fiduciary duty',
        'business judgment',
        'merger acquisition',
      ],
    };
    return conceptMap[category] || [];
  }

  private async analyzeRelationship(
    document: string,
    caseText: string,
  ): Promise<RelatedCase['relationship']> {
    const docText = document.toLowerCase();
    const caseTextLower = caseText.toLowerCase();
    if (
      caseTextLower.includes('distinguish') ||
      docText.includes('distinguish')
    )
      return 'distinguishes';
    if (caseTextLower.includes('overrule') || docText.includes('overrule'))
      return 'contradicts';
    if (
      caseTextLower.includes('follow') ||
      docText.includes('follow') ||
      caseTextLower.includes('support') ||
      docText.includes('support')
    )
      return 'supports';
    return 'cites';
  }

  private calculateCaseRelevance(document: string, caseText: string): number {
    if (!caseText || !document) return 0.1;
    const docTerms = document
      .toLowerCase()
      .split(/\s+/)
      .filter((term) => term.length > 2);
    const caseTerms = caseText
      .toLowerCase()
      .split(/\s+/)
      .filter((term) => term.length > 2);
    if (docTerms.length === 0 || caseTerms.length === 0) return 0.1;
    const docTermSet = new Set(docTerms);
    const commonTerms = caseTerms.filter((term) => docTermSet.has(term));
    const similarity =
      commonTerms.length / new Set([...docTerms, ...caseTerms]).size;
    return Math.min(0.9, Math.max(0.1, similarity * 2.5));
  }

  private escapeRegExp(string: string): string {
    return string.replace(/[.*+\-?^{\}()|[\]\\]/g, '\\$&');
  }
}

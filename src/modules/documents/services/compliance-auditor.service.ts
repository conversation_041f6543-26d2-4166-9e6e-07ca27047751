import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import {
  ComplianceAuditResultDocument,
  ComplianceProfileDocument,
  RegulatoryFrameworkDocument,
  ComplianceReportDocument,
  COMPLIANCE_AUDIT_RESULT_MODEL,
  COMPLIANCE_PROFILE_MODEL,
  REGULATORY_FRAMEWORK_MODEL,
  COMPLIANCE_REPORT_MODEL,
} from '../schemas/compliance-auditor.schema';
import {
  ComplianceAuditResult,
  ComplianceProfile,
  RegulatoryFramework,
  ComplianceFinding,
  ComplianceRecommendation,
  ComplianceSummary,
  ComplianceRule,
} from '../interfaces/compliance-auditor.interface';
import {
  AuditDocumentDto,
  CreateComplianceProfileDto,
  ComplianceQueryDto,
  GenerateComplianceReportDto,
} from '../dto/compliance-auditor.dto';
import { AIService } from '../../ai/services/ai.service';
import { DocumentProcessingService } from './document-processing.service';

@Injectable()
export class ComplianceAuditorService {
  private readonly logger = new Logger(ComplianceAuditorService.name);

  // Built-in regulatory frameworks
  private readonly builtInFrameworks = [
    {
      id: 'gdpr',
      name: 'General Data Protection Regulation',
      version: '2018',
      jurisdiction: 'EU',
      industry: ['all'],
      applicableDocumentTypes: [
        'privacy-policy',
        'data-processing-agreement',
        'contract',
      ],
    },
    {
      id: 'sox',
      name: 'Sarbanes-Oxley Act',
      version: '2002',
      jurisdiction: 'US',
      industry: ['finance', 'public-companies'],
      applicableDocumentTypes: [
        'financial-report',
        'audit-report',
        'internal-controls',
      ],
    },
    {
      id: 'hipaa',
      name: 'Health Insurance Portability and Accountability Act',
      version: '1996',
      jurisdiction: 'US',
      industry: ['healthcare'],
      applicableDocumentTypes: [
        'medical-record',
        'patient-agreement',
        'healthcare-contract',
      ],
    },
  ];

  constructor(
    @InjectModel(COMPLIANCE_AUDIT_RESULT_MODEL)
    private readonly auditResultModel: Model<ComplianceAuditResultDocument>,
    @InjectModel(COMPLIANCE_PROFILE_MODEL)
    private readonly profileModel: Model<ComplianceProfileDocument>,
    @InjectModel(REGULATORY_FRAMEWORK_MODEL)
    private readonly frameworkModel: Model<RegulatoryFrameworkDocument>,
    @InjectModel(COMPLIANCE_REPORT_MODEL)
    private readonly reportModel: Model<ComplianceReportDocument>,
    private readonly aiService: AIService,
    private readonly documentProcessingService: DocumentProcessingService,
  ) {}

  async auditDocument(
    dto: AuditDocumentDto,
    userId: string,
    organizationId: string,
  ): Promise<ComplianceAuditResult> {
    const startTime = Date.now();
    this.logger.log(
      `Starting compliance audit for document: ${dto.documentId}`,
    );

    // Get document content
    const document = await this.documentProcessingService.getDocumentById(
      dto.documentId,
    );
    if (!document) {
      throw new NotFoundException(
        `Document with ID ${dto.documentId} not found`,
      );
    }

    // Get compliance profile if specified
    let profile: ComplianceProfile | null = null;
    if (dto.profileId) {
      profile = await this.getProfile(dto.profileId, organizationId);
    }

    // Determine regulations to check
    const regulations = dto.options.regulations ||
      profile?.regulations || ['gdpr', 'sox'];

    // Get regulatory frameworks
    const frameworks = await this.getFrameworks(regulations);

    // Perform compliance analysis
    const findings = await this.analyzeCompliance(
      document.content,
      frameworks,
      dto.options,
      profile,
    );

    // Generate recommendations
    const recommendations = await this.generateRecommendations(
      findings,
      dto.options,
    );

    // Calculate summary
    const summary = this.calculateSummary(findings);

    // Determine overall status and risk level
    const { status, riskLevel, overallScore } = this.assessOverallCompliance(
      findings,
      summary,
    );

    // Save audit result
    const auditResult = new this.auditResultModel({
      documentId: dto.documentId,
      documentTitle: document.metadata?.title || 'Untitled Document',
      auditDate: new Date(),
      regulations,
      overallScore,
      status,
      riskLevel,
      findings,
      recommendations,
      summary,
      organizationId,
      auditedBy: userId,
      processingTime: Date.now() - startTime,
      auditOptions: dto.options,
    });

    const savedResult = await auditResult.save();
    this.logger.log(`Completed compliance audit with ID: ${savedResult._id}`);

    return this.mapToAuditResult(savedResult);
  }

  private async analyzeCompliance(
    documentContent: string,
    frameworks: RegulatoryFramework[],
    options: any,
    profile?: ComplianceProfile,
  ): Promise<ComplianceFinding[]> {
    const findings: ComplianceFinding[] = [];

    for (const framework of frameworks) {
      this.logger.debug(
        `Analyzing compliance for framework: ${framework.name}`,
      );

      for (const rule of framework.rules) {
        // Check if rule is applicable
        if (!this.isRuleApplicable(rule, profile)) {
          continue;
        }

        // Perform pattern-based analysis
        const patternFindings = await this.analyzeRulePatterns(
          documentContent,
          rule,
          framework.name,
        );
        findings.push(...patternFindings);

        // Perform AI analysis if enabled
        if (options.includeAIAnalysis && rule.aiPrompt) {
          const aiFindings = await this.analyzeRuleWithAI(
            documentContent,
            rule,
            framework.name,
          );
          findings.push(...aiFindings);
        }
      }
    }

    return this.deduplicateFindings(findings);
  }

  private async analyzeRulePatterns(
    content: string,
    rule: ComplianceRule,
    frameworkName: string,
  ): Promise<ComplianceFinding[]> {
    const findings: ComplianceFinding[] = [];

    for (const pattern of rule.patterns) {
      if (pattern.type === 'regex') {
        const regex = new RegExp(pattern.pattern, 'gi');
        const matches = content.match(regex);

        if (matches) {
          findings.push({
            id: uuidv4(),
            regulation: frameworkName,
            ruleId: rule.id,
            ruleName: rule.title,
            description: rule.description,
            severity: rule.severity as any,
            status: 'pass',
            confidence: pattern.weight,
            location: this.findTextLocation(content, matches[0]),
            evidence: matches.slice(0, 3), // First 3 matches
            impact: `Found ${matches.length} instances of pattern: ${pattern.pattern}`,
            remediation: `Review and ensure compliance with ${rule.title}`,
            category: rule.category,
            tags: [],
          });
        } else if (rule.severity === 'critical' || rule.severity === 'error') {
          findings.push({
            id: uuidv4(),
            regulation: frameworkName,
            ruleId: rule.id,
            ruleName: rule.title,
            description: rule.description,
            severity: rule.severity as any,
            status: 'fail',
            confidence: pattern.weight,
            location: { context: 'Document-wide analysis' },
            evidence: [],
            impact: `Required pattern not found: ${pattern.pattern}`,
            remediation: `Add required content for ${rule.title}`,
            category: rule.category,
            tags: [],
          });
        }
      } else if (pattern.type === 'keyword') {
        const keywords = pattern.pattern.split(',').map((k) => k.trim());
        const foundKeywords = keywords.filter((keyword) =>
          content.toLowerCase().includes(keyword.toLowerCase()),
        );

        if (foundKeywords.length > 0) {
          findings.push({
            id: uuidv4(),
            regulation: frameworkName,
            ruleId: rule.id,
            ruleName: rule.title,
            description: rule.description,
            severity: 'info' as any,
            status: 'pass',
            confidence:
              (foundKeywords.length / keywords.length) * pattern.weight,
            location: this.findTextLocation(content, foundKeywords[0]),
            evidence: foundKeywords,
            impact: `Found relevant keywords: ${foundKeywords.join(', ')}`,
            remediation: 'Continue monitoring compliance',
            category: rule.category,
            tags: [],
          });
        }
      }
    }

    return findings;
  }

  private async analyzeRuleWithAI(
    content: string,
    rule: ComplianceRule,
    frameworkName: string,
  ): Promise<ComplianceFinding[]> {
    try {
      const prompt = `
Analyze the following document content for compliance with this regulation:

Rule: ${rule.title}
Description: ${rule.description}
Category: ${rule.category}
Severity: ${rule.severity}

AI Analysis Prompt: ${rule.aiPrompt}

Document Content:
${content.substring(0, 4000)} ${content.length > 4000 ? '...' : ''}

Provide a JSON response with:
{
  "compliant": true/false,
  "confidence": 0.0-1.0,
  "findings": ["list of specific findings"],
  "evidence": ["relevant text excerpts"],
  "recommendations": ["specific recommendations"]
}
`;

      const response = await this.aiService.generateResponse(prompt, {
        temperature: 0.2,
        systemMessage:
          'You are a compliance expert analyzing documents for regulatory compliance.',
        responseFormat: 'json',
      });

      const cleanedResponse = response.replace(/```json\s*|\s*```/g, '');
      const analysis = JSON.parse(cleanedResponse);

      return [
        {
          id: uuidv4(),
          regulation: frameworkName,
          ruleId: rule.id,
          ruleName: rule.title,
          description: rule.description,
          severity: rule.severity as any,
          status: analysis.compliant ? 'pass' : 'fail',
          confidence: analysis.confidence || 0.7,
          location: { context: 'AI Analysis' },
          evidence: analysis.evidence || [],
          impact: analysis.findings?.join('; ') || 'AI analysis completed',
          remediation:
            analysis.recommendations?.join('; ') ||
            'Follow regulatory guidelines',
          category: rule.category,
          tags: ['ai-analysis'],
        },
      ];
    } catch (error) {
      this.logger.warn(
        `AI analysis failed for rule ${rule.id}: ${error.message}`,
      );
      return [];
    }
  }

  private isRuleApplicable(
    rule: ComplianceRule,
    profile?: ComplianceProfile,
  ): boolean {
    if (!profile) return true;

    // Check industry applicability
    if (
      rule.applicability.industries.length > 0 &&
      !rule.applicability.industries.includes(profile.industry) &&
      !rule.applicability.industries.includes('all')
    ) {
      return false;
    }

    // Check jurisdiction applicability
    if (
      rule.applicability.jurisdictions.length > 0 &&
      !rule.applicability.jurisdictions.includes(profile.jurisdiction) &&
      !rule.applicability.jurisdictions.includes('all')
    ) {
      return false;
    }

    // Check organization size applicability
    if (
      rule.applicability.organizationSizes.length > 0 &&
      !rule.applicability.organizationSizes.includes(
        profile.organizationSize,
      ) &&
      !rule.applicability.organizationSizes.includes('all')
    ) {
      return false;
    }

    return true;
  }

  private findTextLocation(content: string, searchText: string): any {
    const index = content.toLowerCase().indexOf(searchText.toLowerCase());
    if (index === -1) return { context: 'Not found' };

    const lines = content.substring(0, index).split('\n');
    const lineNumber = lines.length;
    const contextStart = Math.max(0, index - 100);
    const contextEnd = Math.min(
      content.length,
      index + searchText.length + 100,
    );
    const context = content.substring(contextStart, contextEnd);

    return {
      startPosition: index,
      endPosition: index + searchText.length,
      context: context.trim(),
    };
  }

  private deduplicateFindings(
    findings: ComplianceFinding[],
  ): ComplianceFinding[] {
    const seen = new Set<string>();
    return findings.filter((finding) => {
      const key = `${finding.ruleId}-${finding.status}-${finding.location.context}`;
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  }

  private async generateRecommendations(
    findings: ComplianceFinding[],
    options: any,
  ): Promise<ComplianceRecommendation[]> {
    if (!options.includeRecommendations) return [];

    const recommendations: ComplianceRecommendation[] = [];
    const failedFindings = findings.filter((f) => f.status === 'fail');

    for (const finding of failedFindings) {
      recommendations.push({
        id: uuidv4(),
        priority: this.mapSeverityToPriority(finding.severity),
        category: finding.category,
        title: `Address ${finding.ruleName}`,
        description: finding.description,
        action: finding.remediation,
        effort: this.estimateEffort(finding.severity),
        timeline: this.estimateTimeline(finding.severity),
        resources: ['Legal team', 'Compliance officer'],
        relatedFindings: [finding.id],
      });
    }

    return recommendations;
  }

  private mapSeverityToPriority(
    severity: string,
  ): 'low' | 'medium' | 'high' | 'critical' {
    switch (severity) {
      case 'critical':
        return 'critical';
      case 'error':
        return 'high';
      case 'warning':
        return 'medium';
      default:
        return 'low';
    }
  }

  private estimateEffort(severity: string): 'low' | 'medium' | 'high' {
    switch (severity) {
      case 'critical':
        return 'high';
      case 'error':
        return 'medium';
      default:
        return 'low';
    }
  }

  private estimateTimeline(severity: string): string {
    switch (severity) {
      case 'critical':
        return 'Immediate (1-3 days)';
      case 'error':
        return 'Short-term (1-2 weeks)';
      case 'warning':
        return 'Medium-term (1-3 months)';
      default:
        return 'Long-term (3+ months)';
    }
  }

  private calculateSummary(findings: ComplianceFinding[]): ComplianceSummary {
    const totalRules = findings.length;
    const passedRules = findings.filter((f) => f.status === 'pass').length;
    const failedRules = findings.filter((f) => f.status === 'fail').length;
    const warningRules = findings.filter((f) => f.status === 'warning').length;
    const notApplicableRules = findings.filter(
      (f) => f.status === 'not-applicable',
    ).length;

    const criticalIssues = findings.filter(
      (f) => f.severity === 'critical' && f.status === 'fail',
    ).length;
    const highPriorityIssues = findings.filter(
      (f) => f.severity === 'error' && f.status === 'fail',
    ).length;
    const mediumPriorityIssues = findings.filter(
      (f) => f.severity === 'warning' && f.status === 'fail',
    ).length;
    const lowPriorityIssues = findings.filter(
      (f) => f.severity === 'info' && f.status === 'fail',
    ).length;

    return {
      totalRules,
      passedRules,
      failedRules,
      warningRules,
      notApplicableRules,
      criticalIssues,
      highPriorityIssues,
      mediumPriorityIssues,
      lowPriorityIssues,
      estimatedRemediationTime: this.calculateRemediationTime(findings),
      complianceGaps: this.identifyComplianceGaps(findings),
      strengths: this.identifyStrengths(findings),
    };
  }

  private calculateRemediationTime(findings: ComplianceFinding[]): string {
    const failedFindings = findings.filter((f) => f.status === 'fail');
    const criticalCount = failedFindings.filter(
      (f) => f.severity === 'critical',
    ).length;
    const errorCount = failedFindings.filter(
      (f) => f.severity === 'error',
    ).length;

    if (criticalCount > 0) return '1-2 weeks';
    if (errorCount > 3) return '3-4 weeks';
    if (errorCount > 0) return '1-3 weeks';
    return '1-2 weeks';
  }

  private identifyComplianceGaps(findings: ComplianceFinding[]): string[] {
    const gaps = new Set<string>();
    findings
      .filter((f) => f.status === 'fail')
      .forEach((finding) => {
        gaps.add(finding.category);
      });
    return Array.from(gaps);
  }

  private identifyStrengths(findings: ComplianceFinding[]): string[] {
    const strengths = new Set<string>();
    findings
      .filter((f) => f.status === 'pass')
      .forEach((finding) => {
        strengths.add(finding.category);
      });
    return Array.from(strengths);
  }

  private assessOverallCompliance(
    findings: ComplianceFinding[],
    summary: ComplianceSummary,
  ): {
    status: 'compliant' | 'non-compliant' | 'partial' | 'needs-review';
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    overallScore: number;
  } {
    const totalRules = summary.totalRules;
    const passedRules = summary.passedRules;
    const criticalIssues = summary.criticalIssues;
    const highPriorityIssues = summary.highPriorityIssues;

    // Calculate overall score
    const overallScore = totalRules > 0 ? passedRules / totalRules : 0;

    // Determine risk level
    let riskLevel: 'low' | 'medium' | 'high' | 'critical';
    if (criticalIssues > 0) {
      riskLevel = 'critical';
    } else if (highPriorityIssues > 2) {
      riskLevel = 'high';
    } else if (highPriorityIssues > 0 || summary.mediumPriorityIssues > 3) {
      riskLevel = 'medium';
    } else {
      riskLevel = 'low';
    }

    // Determine status
    let status: 'compliant' | 'non-compliant' | 'partial' | 'needs-review';
    if (
      overallScore >= 0.95 &&
      criticalIssues === 0 &&
      highPriorityIssues === 0
    ) {
      status = 'compliant';
    } else if (overallScore < 0.5 || criticalIssues > 0) {
      status = 'non-compliant';
    } else if (overallScore >= 0.8) {
      status = 'partial';
    } else {
      status = 'needs-review';
    }

    return { status, riskLevel, overallScore };
  }

  private async getFrameworks(
    regulationIds: string[],
  ): Promise<RegulatoryFramework[]> {
    // For now, return built-in frameworks
    // In a real implementation, this would query the database
    return this.builtInFrameworks
      .filter((framework) => regulationIds.includes(framework.id))
      .map((framework) => ({
        ...framework,
        rules: this.getFrameworkRules(framework.id),
        lastUpdated: new Date(),
        isActive: true,
        source: 'built-in',
      })) as RegulatoryFramework[];
  }

  private getFrameworkRules(frameworkId: string): ComplianceRule[] {
    // Simplified rule sets for demonstration
    const ruleMap: Record<string, ComplianceRule[]> = {
      gdpr: [
        {
          id: 'gdpr-1',
          frameworkId: 'gdpr',
          ruleNumber: 'Article 6',
          title: 'Lawful basis for processing',
          description: 'Processing must have a lawful basis',
          category: 'Data Processing',
          severity: 'critical',
          applicability: {
            documentTypes: ['privacy-policy', 'data-processing-agreement'],
            industries: ['all'],
            jurisdictions: ['EU', 'all'],
            organizationSizes: ['all'],
            conditions: [],
          },
          patterns: [
            {
              type: 'keyword',
              pattern:
                'lawful basis, legal basis, consent, legitimate interest',
              weight: 0.8,
            },
          ],
          aiPrompt:
            'Check if the document clearly states the lawful basis for data processing',
          manualReview: false,
          exemptions: [],
          references: ['GDPR Article 6'],
          lastUpdated: new Date(),
        },
      ],
      sox: [
        {
          id: 'sox-1',
          frameworkId: 'sox',
          ruleNumber: 'Section 302',
          title: 'Corporate responsibility for financial reports',
          description: 'CEO and CFO must certify financial reports',
          category: 'Financial Reporting',
          severity: 'critical',
          applicability: {
            documentTypes: ['financial-report'],
            industries: ['finance', 'public-companies'],
            jurisdictions: ['US', 'all'],
            organizationSizes: ['large', 'enterprise'],
            conditions: [],
          },
          patterns: [
            {
              type: 'keyword',
              pattern:
                'CEO certification, CFO certification, officer certification',
              weight: 0.9,
            },
          ],
          aiPrompt:
            'Verify that the financial report includes proper officer certifications',
          manualReview: true,
          exemptions: [],
          references: ['SOX Section 302'],
          lastUpdated: new Date(),
        },
      ],
      hipaa: [
        {
          id: 'hipaa-1',
          frameworkId: 'hipaa',
          ruleNumber: '164.502',
          title: 'Uses and disclosures of protected health information',
          description:
            'PHI must be properly protected and disclosed only when authorized',
          category: 'Data Protection',
          severity: 'critical',
          applicability: {
            documentTypes: ['medical-record', 'patient-agreement'],
            industries: ['healthcare'],
            jurisdictions: ['US', 'all'],
            organizationSizes: ['all'],
            conditions: [],
          },
          patterns: [
            {
              type: 'keyword',
              pattern:
                'protected health information, PHI, patient privacy, medical privacy',
              weight: 0.8,
            },
          ],
          aiPrompt:
            'Check if PHI is properly protected and disclosure rules are followed',
          manualReview: false,
          exemptions: [],
          references: ['HIPAA 164.502'],
          lastUpdated: new Date(),
        },
      ],
    };

    return ruleMap[frameworkId] || [];
  }

  async createProfile(
    dto: CreateComplianceProfileDto,
    userId: string,
    organizationId: string,
  ): Promise<ComplianceProfile> {
    this.logger.log(`Creating compliance profile: ${dto.name}`);

    const profile = new this.profileModel({
      ...dto,
      organizationId,
      createdBy: userId,
      usageCount: 0,
    });

    const savedProfile = await profile.save();
    this.logger.log(`Created profile with ID: ${savedProfile._id}`);

    return this.mapToComplianceProfile(savedProfile);
  }

  async getProfiles(organizationId: string): Promise<ComplianceProfile[]> {
    const profiles = await this.profileModel
      .find({ organizationId })
      .sort({ createdAt: -1 })
      .exec();
    return profiles.map((profile) => this.mapToComplianceProfile(profile));
  }

  async getProfile(
    profileId: string,
    organizationId: string,
  ): Promise<ComplianceProfile> {
    const profile = await this.profileModel
      .findOne({
        _id: profileId,
        organizationId,
      })
      .exec();

    if (!profile) {
      throw new NotFoundException(`Profile with ID ${profileId} not found`);
    }

    const result = profile.toObject({ getters: true });
    // Convert database rules to interface format
    result.customRules = (result.customRules || []).map(rule => {
      return {
        id: rule.id,
        frameworkId: 'custom',
        ruleNumber: rule.ruleNumber,
        title: rule.title,
        description: rule.description,
        category: rule.category,
        severity: rule.severity,
        applicability: rule.applicability,
        patterns: rule.patterns,
        aiPrompt: rule.aiPrompt,
        manualReview: rule.manualReview,
        exemptions: rule.exemptions,
        references: rule.references,
        lastUpdated: new Date()
      } as ComplianceRule;
    });
    return result as unknown as ComplianceProfile;
  }

  private mapFindingStatus(
    status: string,
  ): 'pass' | 'fail' | 'warning' | 'not-applicable' {
    switch (status) {
      case 'compliant':
        return 'pass';
      case 'non-compliant':
        return 'fail';
      case 'needs-review':
        return 'warning';
      default:
        return 'not-applicable';
    }
  }

  private mapToComplianceProfile(doc: ComplianceProfileDocument): ComplianceProfile {
    const data = doc.toObject({ getters: true });
    const mappedRules = (data.customRules || []).map(rule => ({
      ...rule,
      frameworkId: 'custom',
      lastUpdated: new Date()
    }));
    return {
      ...data,
      customRules: mappedRules
    } as ComplianceProfile;
  }

  private mapToComplianceAuditResult(doc: ComplianceAuditResultDocument): ComplianceAuditResult {
    const data = doc.toObject({ getters: true });
    const mappedResult = {
      ...data,
      findings: data.findings.map(f => ({
        ...f,
        status: f.status as 'pass' | 'fail' | 'warning' | 'not-applicable'
      })),
      status: data.status === 'pass' ? 'compliant' :
              data.status === 'fail' ? 'non-compliant' :
              data.status === 'warning' ? 'partial' : 'needs-review'
    };
    return mappedResult as ComplianceAuditResult;
  }

  private mapToAuditResult(doc: ComplianceAuditResultDocument): ComplianceAuditResult {
    const data = doc.toObject({ getters: true });
    const mappedStatus = data.status === 'pass' ? 'compliant' :
                        data.status === 'fail' ? 'non-compliant' :
                        data.status === 'warning' ? 'partial' : 'needs-review';
    
    const mappedFindings = data.findings.map(f => ({
      ...f,
      status: f.status === 'compliant' ? 'pass' :
             f.status === 'non-compliant' ? 'fail' :
             f.status === 'partial' ? 'warning' : 'not-applicable'
    }));

    return {
      ...data,
      status: mappedStatus,
      findings: mappedFindings
    } as ComplianceAuditResult;
  }

  async getAuditResults(
    organizationId: string,
    query: ComplianceQueryDto,
  ): Promise<{ results: ComplianceAuditResult[]; total: number }> {
    const filter: any = { organizationId };

    if (query.regulations && query.regulations.length > 0) {
      filter.regulations = { $in: query.regulations };
    }

    if (query.status) {
      filter.status = query.status;
    }

    if (query.riskLevel) {
      filter.riskLevel = query.riskLevel;
    }

    if (query.startDate || query.endDate) {
      filter.auditDate = {};
      if (query.startDate) {
        filter.auditDate.$gte = new Date(query.startDate);
      }
      if (query.endDate) {
        filter.auditDate.$lte = new Date(query.endDate);
      }
    }

    const page = query.page || 1;
    const limit = query.limit || 20;
    const skip = (page - 1) * limit;

    const sortField = query.sortBy || 'auditDate';
    const sortOrder = query.sortOrder === 'asc' ? 1 : -1;

    const [results, total] = await Promise.all([
      this.auditResultModel
        .find(filter)
        .sort({ [sortField]: sortOrder })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.auditResultModel.countDocuments(filter),
    ]);

    return {
      results: results.map(doc => this.mapToAuditResult(doc)),
      total
    };
  }

  async getAuditResult(
    resultId: string,
    organizationId: string,
  ): Promise<ComplianceAuditResult> {
    const result = await this.auditResultModel
      .findOne({
        _id: resultId,
        organizationId,
      })
      .exec();

    if (!result) {
      throw new NotFoundException(`Audit result with ID ${resultId} not found`);
    }

    return this.mapToAuditResult(result);
  }
}


import { Test, TestingModule } from '@nestjs/testing';
import { LegalPatternRecognitionService } from './legal-pattern-recognition.service';
import { AIService } from '../../ai/services/ai.service';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { LegalPattern } from '../interfaces/legal-pattern.interface';

describe('LegalPatternRecognitionService', () => {
  let service: LegalPatternRecognitionService;
  let aiService: AIService;
  let configService: ConfigService;

  // Mock implementation for AIService
  const mockAIService = {
    generateResponse: jest.fn(),
    // Add other methods used by LegalPatternRecognitionService if any
  };

  // Mock implementation for ConfigService
  const mockConfigService = {
    get: jest.fn((key: string) => {
      // Provide default mock values for config keys used in the service
      // Add other config keys if needed by other methods in the service
      return null;
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LegalPatternRecognitionService,
        {
          provide: AIService,
          useValue: mockAIService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        // Provide mock for Logger if necessary, or NestJS might handle it
        Logger,
      ],
    }).compile();

    service = module.get<LegalPatternRecognitionService>(
      LegalPatternRecognitionService,
    );
    aiService = module.get<AIService>(AIService);
    configService = module.get<ConfigService>(ConfigService);

    // Reset mocks before each test
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('detectPatternsAI', () => {
    it('should call detectAIPatterns and return its result', async () => {
      const mockText = 'This is the document text.';
      const mockDocumentId = 'doc123';
      const mockQuery = 'Find indemnification clauses.';
      const expectedPatterns: LegalPattern[] = [
        {
          pattern_name: 'Indemnification Clause',
          text_snippet: 'Clause text here.',
          explanation: 'Matches query.',
          start_char: 10,
          end_char: 30,
        },
      ];

      // Mock the private method detectAIPatterns
      // We cast to 'any' to access the private method for mocking
      const detectAIPatternsSpy = jest
        .spyOn(service as any, 'detectAIPatterns')
        .mockResolvedValue(expectedPatterns);

      const result = await service.detectPatternsAI(
        mockText,
        mockDocumentId,
        mockQuery,
      );

      expect(result).toEqual(expectedPatterns);
      expect(detectAIPatternsSpy).toHaveBeenCalledTimes(1);
      expect(detectAIPatternsSpy).toHaveBeenCalledWith(
        mockText,
        mockDocumentId,
        mockQuery,
      );
    });

    it('should handle errors during pattern detection', async () => {
        const mockText = 'Some text.';
        const mockDocumentId = 'doc456';
        const mockQuery = 'Find termination conditions.';
        const errorMessage = 'AI Service failed';

        jest.spyOn(service as any, 'detectAIPatterns').mockRejectedValue(new Error(errorMessage));

        await expect(service.detectPatternsAI(mockText, mockDocumentId, mockQuery)).rejects.toThrow(
            `Failed to detect patterns using AI for document ${mockDocumentId}: ${errorMessage}`
        );
      });
  });

  describe('detectAIPatterns (private method, tested via detectPatternsAI or directly)', () => {
    // We are already testing the interaction in 'detectPatternsAI' test.
    // Let's adjust the detailed test to focus on the single interaction with AIService.

    it('should call AI service once with the full text and parse the response', async () => {
      const fullText = 'This is the complete document text. It mentions indemnification multiple times. Indemnify now!';
      const documentId = 'doc-full';
      const query = 'Find indemnification mentions';
      const mockAiResponse = JSON.stringify([
        {
          pattern_name: 'Indemnification Mention 1',
          text_snippet: 'mentions indemnification multiple',
          explanation: 'Found mention 1',
          start_char: 40,
          end_char: 72,
        },
        {
          pattern_name: 'Indemnification Mention 2',
          text_snippet: 'Indemnify now!',
          explanation: 'Found mention 2',
          start_char: 80,
          end_char: 94,
        },
      ]);

      // Mock AI service response for a single call
      (aiService.generateResponse as jest.Mock).mockResolvedValueOnce(mockAiResponse);

      // Access private method for testing
      const result = await service['detectAIPatterns'](fullText, documentId, query);

      // Verify AI service was called exactly once
      expect(aiService.generateResponse).toHaveBeenCalledTimes(1);
      // Verify the prompt contained the query and the full text (simplified check)
      expect(aiService.generateResponse).toHaveBeenCalledWith(
        expect.stringContaining(query),
        expect.any(Object) // Check context object if needed
      );
      expect(aiService.generateResponse).toHaveBeenCalledWith(
        expect.stringContaining(fullText),
        expect.any(Object)
      );

      // Verify the result matches the parsed AI response
      expect(result).toHaveLength(2);
      expect(result[0].pattern_name).toBe('Indemnification Mention 1');
      expect(result[1].pattern_name).toBe('Indemnification Mention 2');
      expect(result[0].start_char).toBe(40);
      expect(result[1].start_char).toBe(80);
    });

    it('should handle non-JSON AI responses gracefully', async () => {
        const text = 'Short text.';
        const documentId = 'doc-invalid';
        const query = 'Find anything.';
        const invalidJsonResponse = 'This is not JSON.';

        (aiService.generateResponse as jest.Mock).mockResolvedValue(invalidJsonResponse);

        const result = await service['detectAIPatterns'](text, documentId, query);

        expect(aiService.generateResponse).toHaveBeenCalledTimes(1);
        expect(result).toEqual([]); // Expect empty array if JSON parsing fails
        // Check logs? (Requires logger mocking)
      });

      it('should handle AI service errors', async () => {
        const text = 'Short text.';
        const documentId = 'doc-ai-error';
        const query = 'Find anything.';
        const aiErrorMessage = 'Rate limit exceeded';

        (aiService.generateResponse as jest.Mock).mockRejectedValue(new Error(aiErrorMessage));

        // Expect the error to be thrown by detectAIPatterns now
        await expect(service['detectAIPatterns'](text, documentId, query)).rejects.toThrow(aiErrorMessage);

        expect(aiService.generateResponse).toHaveBeenCalledTimes(1);
       });
   });
 });

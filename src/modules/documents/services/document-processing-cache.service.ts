import { Injectable, Logger, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import * as crypto from 'crypto';

export interface CachedProcessingResult {
  documentId: string;
  fileHash: string;
  content: string;
  metadata: Record<string, unknown>;
  processingTime: number;
  createdAt: Date;
  expiresAt: Date;
}

@Injectable()
export class DocumentProcessingCacheService {
  private readonly logger = new Logger(DocumentProcessingCacheService.name);
  private readonly cachePrefix = 'doc_processing:';
  private readonly hashPrefix = 'doc_hash:';
  private readonly defaultTTL = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

  constructor(
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Generate a hash for file content to enable deduplication
   */
  generateFileHash(content: Buffer | string): string {
    return crypto
      .createHash('sha256')
      .update(content)
      .digest('hex');
  }

  /**
   * Check if a document with the same content has been processed before
   */
  async findByContentHash(fileHash: string): Promise<CachedProcessingResult | null> {
    try {
      const cacheKey = `${this.hashPrefix}${fileHash}`;
      const cachedData = await this.cacheManager.get<string>(cacheKey);

      if (!cachedData) {
        return null;
      }

      const parsed = JSON.parse(cachedData);
      return {
        ...parsed,
        createdAt: new Date(parsed.createdAt),
        expiresAt: new Date(parsed.expiresAt),
      };
    } catch (error) {
      this.logger.error(`Error retrieving cached result by hash ${fileHash}:`, error);
      return null;
    }
  }

  /**
   * Cache processing result by document ID
   */
  async cacheByDocumentId(
    documentId: string,
    result: Omit<CachedProcessingResult, 'createdAt' | 'expiresAt'>,
    ttlMs?: number,
  ): Promise<void> {
    try {
      const ttl = ttlMs || this.defaultTTL;
      const now = new Date();
      const expiresAt = new Date(now.getTime() + ttl);

      const cacheData: CachedProcessingResult = {
        ...result,
        createdAt: now,
        expiresAt,
      };

      const cacheKey = `${this.cachePrefix}${documentId}`;
      await this.cacheManager.set(cacheKey, JSON.stringify(cacheData), ttl);

      this.logger.debug(`Cached processing result for document ${documentId}`);
    } catch (error) {
      this.logger.error(`Error caching result for document ${documentId}:`, error);
    }
  }

  /**
   * Cache processing result by content hash for deduplication
   */
  async cacheByContentHash(
    fileHash: string,
    result: Omit<CachedProcessingResult, 'createdAt' | 'expiresAt'>,
    ttlMs?: number,
  ): Promise<void> {
    try {
      const ttl = ttlMs || this.defaultTTL;
      const now = new Date();
      const expiresAt = new Date(now.getTime() + ttl);

      const cacheData: CachedProcessingResult = {
        ...result,
        createdAt: now,
        expiresAt,
      };

      const cacheKey = `${this.hashPrefix}${fileHash}`;
      await this.cacheManager.set(cacheKey, JSON.stringify(cacheData), ttl);

      this.logger.debug(`Cached processing result by hash ${fileHash}`);
    } catch (error) {
      this.logger.error(`Error caching result by hash ${fileHash}:`, error);
    }
  }

  /**
   * Get cached processing result by document ID
   */
  async getByDocumentId(documentId: string): Promise<CachedProcessingResult | null> {
    try {
      const cacheKey = `${this.cachePrefix}${documentId}`;
      const cachedData = await this.cacheManager.get<string>(cacheKey);

      if (!cachedData) {
        return null;
      }

      const parsed = JSON.parse(cachedData);
      return {
        ...parsed,
        createdAt: new Date(parsed.createdAt),
        expiresAt: new Date(parsed.expiresAt),
      };
    } catch (error) {
      this.logger.error(`Error retrieving cached result for document ${documentId}:`, error);
      return null;
    }
  }

  /**
   * Invalidate cache for a document
   */
  async invalidateDocument(documentId: string): Promise<void> {
    try {
      const cacheKey = `${this.cachePrefix}${documentId}`;
      await this.cacheManager.del(cacheKey);
      this.logger.debug(`Invalidated cache for document ${documentId}`);
    } catch (error) {
      this.logger.error(`Error invalidating cache for document ${documentId}:`, error);
    }
  }

  /**
   * Invalidate cache by content hash
   */
  async invalidateByHash(fileHash: string): Promise<void> {
    try {
      const cacheKey = `${this.hashPrefix}${fileHash}`;
      await this.cacheManager.del(cacheKey);
      this.logger.debug(`Invalidated cache for hash ${fileHash}`);
    } catch (error) {
      this.logger.error(`Error invalidating cache for hash ${fileHash}:`, error);
    }
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<{
    totalKeys: number;
    documentKeys: number;
    hashKeys: number;
    memoryUsage: string;
  }> {
    try {
      // Since we're using cache-manager, we'll provide basic stats
      // In a real implementation, you might want to use a Redis-specific cache store
      // to get detailed statistics
      return {
        totalKeys: 0, // Not easily available with cache-manager
        documentKeys: 0,
        hashKeys: 0,
        memoryUsage: 'N/A with cache-manager',
      };
    } catch (error) {
      this.logger.error('Error getting cache stats:', error);
      return {
        totalKeys: 0,
        documentKeys: 0,
        hashKeys: 0,
        memoryUsage: 'Unknown',
      };
    }
  }

  /**
   * Clean up expired cache entries
   */
  async cleanupExpired(): Promise<number> {
    try {
      // With cache-manager, expired entries are automatically cleaned up
      // This method is kept for interface compatibility
      this.logger.log('Cache cleanup handled automatically by cache-manager');
      return 0;
    } catch (error) {
      this.logger.error('Error cleaning up expired cache entries:', error);
      return 0;
    }
  }

  /**
   * Warm up cache with frequently accessed documents
   */
  async warmupCache(documentIds: string[]): Promise<void> {
    this.logger.log(`Warming up cache for ${documentIds.length} documents`);
    
    // This would typically pre-load frequently accessed documents
    // Implementation depends on your specific caching strategy
    for (const documentId of documentIds) {
      // Check if already cached
      const cached = await this.getByDocumentId(documentId);
      if (!cached) {
        // Could trigger background processing for important documents
        this.logger.debug(`Document ${documentId} not in cache, consider pre-processing`);
      }
    }
  }
}

import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  AIAssistedDraftingDto,
  GenerateRelatedDocumentsDto,
  ClauseIntelligenceDto,
  DocumentGenerationResult,
  RelatedDocumentGenerationResult,
  ClauseIntelligenceResult,
  DocumentType,
  RelatedDocumentType,
} from '../dto/document-automation.dto';
import {
  ClauseTemplate,
  ClauseTemplateDocument,
} from '../schemas/clause-template.schema';
import { Document, DOCUMENT_MODEL } from '../schemas/document.schema';
import { AIService } from '../../ai/services/ai.service';
import { ClauseLibraryService } from './clause-library.service';
import { DocumentProcessingService } from './document-processing.service';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { AnalyticsService } from '../../analytics/services/analytics.service';

@Injectable()
export class DocumentAutomationService {
  private readonly logger = new Logger(DocumentAutomationService.name);

  constructor(
    @InjectModel(ClauseTemplate.name)
    private clauseTemplateModel: Model<ClauseTemplateDocument>,
    @InjectModel(DOCUMENT_MODEL)
    private documentModel: Model<Document>,
    private readonly aiService: AIService,
    private readonly clauseLibraryService: ClauseLibraryService,
    private readonly documentProcessingService: DocumentProcessingService,
    private readonly tenantContextService: TenantContextService,
    private readonly analyticsService: AnalyticsService,
  ) {}

  /**
   * AI-Assisted Document Drafting
   */
  async generateDocument(
    dto: AIAssistedDraftingDto,
    organizationId: string,
    userId: string,
  ): Promise<DocumentGenerationResult> {
    const startTime = Date.now();
    this.logger.log(
      `Starting AI-assisted document generation for type: ${dto.documentType}`,
    );

    try {
      // Get organization-specific clauses if requested
      let organizationClauses: ClauseTemplate[] = [];
      if (dto.useClauseLibrary) {
        organizationClauses = await this.clauseTemplateModel
          .find({ organizationId })
          .limit(50)
          .exec();
      }

      // Generate document using AI with organization clauses
      const generatedContent = await this.aiService.generateLegalDocument(
        dto.draftingPrompt.prompt,
        dto.documentType,
        {
          keyTerms: dto.draftingPrompt.keyTerms,
          requiredClauses: dto.draftingPrompt.requiredClauses,
          jurisdiction: dto.jurisdiction,
          organizationPreferences: dto.organizationPreferences,
          includeDisclaimers: dto.includeDisclaimers,
          organizationClauses: organizationClauses.map((clause) => ({
            name: clause.name,
            category: clause.category,
            content: clause.content,
          })),
        },
      );

      // Extract suggested clauses from generation
      const suggestedClauses = await this.extractSuggestedClauses(
        generatedContent,
        organizationClauses,
      );

      // Generate recommendations
      const recommendations = await this.generateDocumentRecommendations(
        generatedContent,
        dto.documentType,
      );

      const result: DocumentGenerationResult = {
        content: generatedContent,
        documentType: dto.documentType,
        metadata: {
          generatedAt: new Date().toISOString(),
          generationDurationMs: Date.now() - startTime,
          modelUsed: 'ai-service',
          clausesUsed: suggestedClauses.length,
          organizationClausesUsed: organizationClauses.length,
        },
        suggestedClauses,
        recommendations,
      };

      // Track analytics
      await this.analyticsService.trackDocumentAutomation(
        userId,
        organizationId,
        'ai_assisted_drafting',
        {
          documentType: dto.documentType,
          generationDurationMs: result.metadata.generationDurationMs,
          clausesUsed: result.metadata.clausesUsed,
        },
      );

      this.logger.log(
        `Document generation completed in ${result.metadata.generationDurationMs}ms`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Error in AI-assisted document generation: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to generate document: ${error.message}`,
      );
    }
  }

  /**
   * Generate Related Documents (schedules, exhibits, addendums)
   */
  async generateRelatedDocuments(
    dto: GenerateRelatedDocumentsDto,
    organizationId: string,
    userId: string,
  ): Promise<RelatedDocumentGenerationResult> {
    const startTime = Date.now();
    this.logger.log(
      `Generating related documents for primary document: ${dto.primaryDocumentId}`,
    );

    try {
      // Get primary document
      const primaryDocument =
        await this.documentProcessingService.getDocumentById(
          dto.primaryDocumentId,
        );
      if (!primaryDocument) {
        throw new NotFoundException(
          `Primary document with ID ${dto.primaryDocumentId} not found`,
        );
      }

      const generatedDocuments = [];

      // Generate each requested document type
      for (const docType of dto.documentTypes) {
        const relatedDoc = await this.generateSingleRelatedDocument(
          primaryDocument.content,
          docType,
          dto.additionalContent,
          dto.autoPopulate,
        );
        generatedDocuments.push(relatedDoc);
      }

      const result: RelatedDocumentGenerationResult = {
        documents: generatedDocuments,
        metadata: {
          primaryDocumentId: dto.primaryDocumentId,
          generatedAt: new Date().toISOString(),
          generationDurationMs: Date.now() - startTime,
          documentsGenerated: generatedDocuments.length,
        },
      };

      // Track analytics
      await this.analyticsService.trackDocumentAutomation(
        userId,
        organizationId,
        'related_document_generation',
        {
          primaryDocumentId: dto.primaryDocumentId,
          documentsGenerated: result.metadata.documentsGenerated,
          documentTypes: dto.documentTypes,
        },
      );

      this.logger.log(
        `Generated ${generatedDocuments.length} related documents`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Error generating related documents: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to generate related documents: ${error.message}`,
      );
    }
  }

  /**
   * Clause Intelligence - suggest clauses for current document
   */
  async getClauseIntelligence(
    dto: ClauseIntelligenceDto,
    organizationId: string,
    userId: string,
  ): Promise<ClauseIntelligenceResult> {
    this.logger.log(
      `Providing clause intelligence for document type: ${dto.documentType}`,
    );

    try {
      // Get relevant clauses from library
      const relevantClauses = await this.clauseTemplateModel
        .find({
          organizationId: dto.includeOrgClauses
            ? organizationId
            : { $exists: false },
        })
        .limit(20)
        .exec();

      // Analyze current content and suggest clauses
      const suggestedClauses = await this.analyzeSuggestedClauses(
        dto.currentContent,
        dto.documentType,
        relevantClauses,
        dto.sectionType,
        dto.context,
      );

      // Generate auto-population suggestions
      const autoPopulationSuggestions =
        await this.generateAutoPopulationSuggestions(
          dto.currentContent,
          dto.documentType,
        );

      // Identify missing essential clauses
      const missingClauses = await this.identifyMissingClauses(
        dto.currentContent,
        dto.documentType,
      );

      const result: ClauseIntelligenceResult = {
        suggestedClauses,
        autoPopulationSuggestions,
        missingClauses,
      };

      // Track analytics
      await this.analyticsService.trackDocumentAutomation(
        userId,
        organizationId,
        'clause_intelligence',
        {
          documentType: dto.documentType,
          suggestedClausesCount: suggestedClauses.length,
          missingClausesCount: missingClauses.length,
        },
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Error in clause intelligence: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException(
        `Failed to provide clause intelligence: ${error.message}`,
      );
    }
  }

  /**
   * Generate a single related document
   */
  private async generateSingleRelatedDocument(
    primaryContent: string,
    docType: RelatedDocumentType,
    additionalContent?: string,
    autoPopulate?: boolean,
  ): Promise<{
    type: RelatedDocumentType;
    title: string;
    content: string;
    metadata: Record<string, any>;
  }> {
    const generatedContent = await this.aiService.generateRelatedDocument(
      primaryContent,
      docType,
      additionalContent,
    );

    return {
      type: docType,
      title: this.generateDocumentTitle(docType),
      content: generatedContent,
      metadata: {
        generatedFrom: 'primary_document',
        autoPopulated: autoPopulate,
        hasAdditionalContent: !!additionalContent,
      },
    };
  }

  /**
   * Generate document title based on type
   */
  private generateDocumentTitle(docType: RelatedDocumentType): string {
    const titles = {
      [RelatedDocumentType.SCHEDULE]: 'Schedule A - Detailed Specifications',
      [RelatedDocumentType.EXHIBIT]: 'Exhibit A - Supporting Documentation',
      [RelatedDocumentType.ADDENDUM]: 'Addendum - Additional Terms',
      [RelatedDocumentType.AMENDMENT]: 'Amendment No. 1',
      [RelatedDocumentType.APPENDIX]: 'Appendix A - Reference Materials',
    };
    return (
      titles[docType] ||
      `${docType.charAt(0).toUpperCase() + docType.slice(1)} Document`
    );
  }

  /**
   * Extract suggested clauses from generated content
   */
  private async extractSuggestedClauses(
    content: string,
    organizationClauses: ClauseTemplate[],
  ): Promise<
    Array<{
      type: string;
      content: string;
      source: 'library' | 'ai_generated' | 'organization';
      confidence: number;
    }>
  > {
    const suggestedClauses = [];

    // Check for organization clauses used
    for (const clause of organizationClauses) {
      const similarity = this.calculateContentSimilarity(
        content,
        clause.content,
      );
      if (similarity > 0.3) {
        suggestedClauses.push({
          type: clause.category,
          content: clause.content,
          source: 'organization' as const,
          confidence: similarity,
        });
      }
    }

    // Add AI-generated clauses (simplified detection)
    const commonClauseTypes = [
      'termination',
      'confidentiality',
      'liability',
      'governing_law',
      'dispute_resolution',
      'intellectual_property',
      'payment_terms',
    ];

    for (const clauseType of commonClauseTypes) {
      if (content.toLowerCase().includes(clauseType.replace('_', ' '))) {
        suggestedClauses.push({
          type: clauseType,
          content: `AI-generated ${clauseType} clause`,
          source: 'ai_generated' as const,
          confidence: 0.8,
        });
      }
    }

    return suggestedClauses.slice(0, 10); // Limit to top 10
  }

  /**
   * Calculate content similarity (simplified)
   */
  private calculateContentSimilarity(
    content1: string,
    content2: string,
  ): number {
    const words1 = content1.toLowerCase().split(/\s+/);
    const words2 = content2.toLowerCase().split(/\s+/);
    const intersection = words1.filter((word) => words2.includes(word));
    return intersection.length / Math.max(words1.length, words2.length);
  }

  /**
   * Generate document recommendations
   */
  private async generateDocumentRecommendations(
    content: string,
    documentType: DocumentType,
  ): Promise<string[]> {
    const recommendations = [];

    // Basic recommendations based on document type
    switch (documentType) {
      case DocumentType.CONTRACT:
        recommendations.push('Consider adding a force majeure clause');
        recommendations.push('Ensure payment terms are clearly defined');
        recommendations.push('Include dispute resolution mechanisms');
        break;
      case DocumentType.NDA:
        recommendations.push('Define confidential information scope clearly');
        recommendations.push('Specify return of confidential materials');
        recommendations.push('Include exceptions to confidentiality');
        break;
      case DocumentType.MSA:
        recommendations.push('Include statement of work attachment process');
        recommendations.push('Define service level agreements');
        recommendations.push('Specify intellectual property ownership');
        break;
    }

    // Content-based recommendations
    if (!content.toLowerCase().includes('governing law')) {
      recommendations.push('Consider adding a governing law clause');
    }
    if (!content.toLowerCase().includes('termination')) {
      recommendations.push('Include termination conditions and procedures');
    }

    return recommendations.slice(0, 5); // Limit to top 5
  }

  /**
   * Analyze and suggest clauses for current document
   */
  private async analyzeSuggestedClauses(
    currentContent: string,
    documentType: DocumentType,
    relevantClauses: ClauseTemplate[],
    sectionType?: string,
    context?: string,
  ): Promise<
    Array<{
      type: string;
      content: string;
      relevanceScore: number;
      source: 'library' | 'ai_generated' | 'organization';
      position: 'beginning' | 'middle' | 'end' | 'specific_section';
      explanation: string;
    }>
  > {
    const suggestedClauses = [];

    // Analyze relevant clauses from library
    for (const clause of relevantClauses) {
      const relevanceScore = this.calculateClauseRelevance(
        currentContent,
        clause,
        documentType,
        sectionType,
      );

      if (relevanceScore > 0.4) {
        suggestedClauses.push({
          type: clause.category,
          content: clause.content,
          relevanceScore,
          source: 'organization' as const,
          position: this.determineClausePosition(clause.category, documentType),
          explanation: `This ${clause.category} clause is relevant based on your document type and content.`,
        });
      }
    }

    // Generate AI-suggested clauses based on document type using proper AI service method
    const aiSuggestions = await this.aiService.suggestClauses(
      currentContent,
      documentType,
      sectionType,
      context,
      relevantClauses.map((clause) => ({
        name: clause.name,
        category: clause.category,
        content: clause.content,
      })),
    );

    // Convert AI suggestions to our format
    const formattedAISuggestions = aiSuggestions.map((suggestion) => ({
      type: suggestion.type,
      content: suggestion.content,
      relevanceScore: suggestion.relevanceScore,
      source: 'ai_generated' as const,
      position: 'middle' as const,
      explanation: suggestion.explanation,
    }));

    suggestedClauses.push(...formattedAISuggestions);

    return suggestedClauses.slice(0, 8); // Limit to top 8
  }

  /**
   * Calculate clause relevance score
   */
  private calculateClauseRelevance(
    currentContent: string,
    clause: ClauseTemplate,
    documentType: DocumentType,
    sectionType?: string,
  ): number {
    let score = 0;

    // Base score for document type match
    if (this.isClauseRelevantForDocumentType(clause.category, documentType)) {
      score += 0.5;
    }

    // Content similarity
    const similarity = this.calculateContentSimilarity(
      currentContent,
      clause.content,
    );
    score += similarity * 0.3;

    // Section type match
    if (
      sectionType &&
      clause.category.toLowerCase().includes(sectionType.toLowerCase())
    ) {
      score += 0.2;
    }

    return Math.min(score, 1.0);
  }

  /**
   * Check if clause is relevant for document type
   */
  private isClauseRelevantForDocumentType(
    clauseCategory: string,
    documentType: DocumentType,
  ): boolean {
    const relevanceMap = {
      [DocumentType.CONTRACT]: [
        'termination',
        'payment',
        'liability',
        'governing_law',
        'dispute_resolution',
      ],
      [DocumentType.NDA]: [
        'confidentiality',
        'non_disclosure',
        'return_materials',
        'exceptions',
      ],
      [DocumentType.MSA]: [
        'service_levels',
        'intellectual_property',
        'termination',
        'payment',
      ],
      [DocumentType.SOW]: [
        'deliverables',
        'timeline',
        'payment',
        'acceptance_criteria',
      ],
      [DocumentType.EMPLOYMENT_AGREEMENT]: [
        'compensation',
        'benefits',
        'termination',
        'confidentiality',
      ],
    };

    const relevantCategories = relevanceMap[documentType] || [];
    return relevantCategories.some(
      (category) =>
        clauseCategory.toLowerCase().includes(category) ||
        category.includes(clauseCategory.toLowerCase()),
    );
  }

  /**
   * Determine optimal position for clause
   */
  private determineClausePosition(
    clauseCategory: string,
    documentType: DocumentType,
  ): 'beginning' | 'middle' | 'end' | 'specific_section' {
    const beginningClauses = ['definitions', 'parties', 'recitals'];
    const endClauses = ['governing_law', 'signatures', 'execution'];
    const middleClauses = ['payment', 'deliverables', 'services'];

    if (
      beginningClauses.some((cat) => clauseCategory.toLowerCase().includes(cat))
    ) {
      return 'beginning';
    }
    if (endClauses.some((cat) => clauseCategory.toLowerCase().includes(cat))) {
      return 'end';
    }
    if (
      middleClauses.some((cat) => clauseCategory.toLowerCase().includes(cat))
    ) {
      return 'middle';
    }
    return 'specific_section';
  }

  /**
   * Generate auto-population suggestions
   */
  private async generateAutoPopulationSuggestions(
    currentContent: string,
    documentType: DocumentType,
  ): Promise<
    Array<{
      sectionName: string;
      suggestedContent: string;
      confidence: number;
    }>
  > {
    const suggestions = [];

    // Basic auto-population based on document type
    switch (documentType) {
      case DocumentType.CONTRACT:
        if (!currentContent.toLowerCase().includes('effective date')) {
          suggestions.push({
            sectionName: 'Effective Date',
            suggestedContent: `This Agreement shall be effective as of [DATE] ("Effective Date").`,
            confidence: 0.9,
          });
        }
        break;
      case DocumentType.NDA:
        if (
          !currentContent.toLowerCase().includes('confidential information')
        ) {
          suggestions.push({
            sectionName: 'Definition of Confidential Information',
            suggestedContent: `"Confidential Information" means any and all non-public information...`,
            confidence: 0.85,
          });
        }
        break;
    }

    return suggestions.slice(0, 5);
  }

  /**
   * Identify missing essential clauses
   */
  private async identifyMissingClauses(
    currentContent: string,
    documentType: DocumentType,
  ): Promise<
    Array<{
      type: string;
      importance: 'critical' | 'recommended' | 'optional';
      description: string;
    }>
  > {
    const missingClauses = [];
    const contentLower = currentContent.toLowerCase();

    // Essential clauses by document type
    const essentialClauses = {
      [DocumentType.CONTRACT]: [
        {
          type: 'governing_law',
          importance: 'critical' as const,
          keywords: ['governing law', 'jurisdiction'],
        },
        {
          type: 'termination',
          importance: 'critical' as const,
          keywords: ['termination', 'terminate'],
        },
        {
          type: 'liability',
          importance: 'recommended' as const,
          keywords: ['liability', 'damages'],
        },
      ],
      [DocumentType.NDA]: [
        {
          type: 'confidentiality',
          importance: 'critical' as const,
          keywords: ['confidential', 'non-disclosure'],
        },
        {
          type: 'return_materials',
          importance: 'recommended' as const,
          keywords: ['return', 'materials'],
        },
        {
          type: 'term',
          importance: 'critical' as const,
          keywords: ['term', 'duration'],
        },
      ],
    };

    const clauses = essentialClauses[documentType] || [];

    for (const clause of clauses) {
      const hasClause = clause.keywords.some((keyword) =>
        contentLower.includes(keyword),
      );
      if (!hasClause) {
        missingClauses.push({
          type: clause.type,
          importance: clause.importance,
          description: `Missing ${clause.type.replace(
            '_',
            ' ',
          )} clause - this is ${
            clause.importance
          } for ${documentType} documents`,
        });
      }
    }

    return missingClauses;
  }
}

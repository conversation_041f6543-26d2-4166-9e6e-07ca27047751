import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, MoreThan } from 'typeorm';
import { DocumentCacheEntity } from '../entities/document-cache.entity';
import * as path from 'path';
import * as fs from 'fs/promises';

export interface CachedDocument {
  id: string;
  hash: string;
  text: string;
  metadata: Record<string, unknown>;
  createdAt: Date;
  expiresAt: Date | null;
}

@Injectable()
export class DocumentCacheService implements OnModuleInit {
  private readonly logger = new Logger(DocumentCacheService.name);
  private readonly cacheDir: string;
  private readonly maxCacheAge = 7 * 24 * 60 * 60 * 1000; // 7 days

  constructor(
    private readonly configService: ConfigService,
    @InjectRepository(DocumentCacheEntity)
    private readonly documentRepo: Repository<DocumentCacheEntity>,
  ) {
    this.cacheDir = path.join(
      this.configService.get<string>('storage.uploadDir') ?? 'uploads',
      'cache',
    );
  }

  async onModuleInit(): Promise<void> {
    try {
      await this.ensureCacheDirExists();
      this.logger.log('Document cache service initialized');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to initialize DocumentCacheService', {
        error: errorMessage,
      });
    }
  }

  private async ensureCacheDirExists(): Promise<void> {
    try {
      await fs.mkdir(this.cacheDir, { recursive: true });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to create cache directory', {
        error: errorMessage,
      });
    }
  }

  public async cacheDocument(
    documentId: string,
    fileHash: string,
    text: string,
    metadata: Record<string, unknown>,
    ttlMs?: number,
  ): Promise<void> {
    try {
      const now = new Date();
      const expiresAt = ttlMs ? new Date(now.getTime() + ttlMs) : new Date(now.getTime() + this.maxCacheAge);

      await this.documentRepo.save({
        id: documentId,
        hash: fileHash,
        text,
        metadata,
        createdAt: now,
        expiresAt,
      });

      this.logger.debug('Document cached', { documentId, fileHash });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to cache document', {
        documentId,
        error: errorMessage,
      });
      throw new Error(`Failed to cache document: ${errorMessage}`);
    }
  }

  public async getCachedDocumentById(documentId: string): Promise<CachedDocument | null> {
    try {
      const doc = await this.documentRepo.findOne({
        where: {
          id: documentId,
          expiresAt: MoreThan(new Date()),
        },
      });

      if (!doc) {
        this.logger.debug('Cache miss by ID', { documentId });
        return null;
      }

      this.logger.debug('Cache hit by ID', { documentId });
      return doc;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to retrieve document', {
        documentId,
        error: errorMessage,
      });
      return null;
    }
  }

  public async getCachedDocumentByHash(fileHash: string): Promise<CachedDocument | null> {
    try {
      const doc = await this.documentRepo.findOne({
        where: {
          hash: fileHash,
          expiresAt: MoreThan(new Date()),
        },
        order: {
          createdAt: 'DESC',
        },
      });

      if (!doc) {
        this.logger.debug('Cache miss by hash', { fileHash });
        return null;
      }

      this.logger.debug('Cache hit by hash', { fileHash });
      return doc;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to retrieve document by hash', {
        fileHash,
        error: errorMessage,
      });
      return null;
    }
  }

  public async findDocumentById(id: string): Promise<CachedDocument | null> {
    try {
      const document = await this.documentRepo.findOne({
        where: { id },
      });

      if (!document) {
        return null;
      }

      return {
        id: document.id,
        hash: document.hash,
        text: document.text,
        metadata: document.metadata,
        createdAt: document.createdAt,
        expiresAt: document.expiresAt,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to find document by ID ${id}: ${errorMessage}`);
      return null;
    }
  }

  public async findDocumentByHash(hash: string): Promise<CachedDocument | null> {
    try {
      const document = await this.documentRepo.findOne({
        where: { hash },
      });

      if (!document) {
        return null;
      }

      return {
        id: document.id,
        hash: document.hash,
        text: document.text,
        metadata: document.metadata,
        createdAt: document.createdAt,
        expiresAt: document.expiresAt,
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to find document by hash ${hash}: ${errorMessage}`);
      return null;
    }
  }

  public async invalidateCache(documentId: string): Promise<boolean> {
    try {
      const result = await this.documentRepo.delete(documentId);
      const wasDeleted = result.affected > 0;

      this.logger.debug('Cache invalidated', { documentId, wasDeleted });
      return wasDeleted;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to invalidate cache', {
        documentId,
        error: errorMessage,
      });
      return false;
    }
  }

  public async cleanExpiredCache(): Promise<void> {
    try {
      const result = await this.documentRepo.delete({
        expiresAt: MoreThan(new Date()),
      });

      if (result.affected > 0) {
        this.logger.log('Cleaned expired cache entries', {
          count: result.affected,
        });
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to clean expired cache', {
        error: errorMessage,
      });
    }
  }

  public async calculateFileHash(filePath: string): Promise<string> {
    try {
      const crypto = await import('crypto');
      const fileData = await fs.readFile(filePath);
      const hash = crypto.createHash('sha256').update(fileData).digest('hex');
      return hash;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to calculate file hash', {
        filePath,
        error: errorMessage,
      });
      return `fallback-${Date.now()}-${Math.random().toString(36).slice(2, 10)}`;
    }
  }

  /**
   * Get document text content by document ID
   * @param documentId The ID of the document
   * @returns The document text content or null if not found
   */
  public async getDocumentContent(documentId: string): Promise<string | null> {
    try {
      const cachedDoc = await this.getCachedDocumentById(documentId);
      
      if (!cachedDoc) {
        this.logger.debug('Document content not found in cache', { documentId });
        return null;
      }
      
      return cachedDoc.text;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error('Failed to retrieve document content', {
        documentId,
        error: errorMessage,
      });
      return null;
    }
  }
}

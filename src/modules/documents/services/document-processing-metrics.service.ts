import { Injectable, Logger, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';

export interface ProcessingMetrics {
  totalDocuments: number;
  averageProcessingTime: number;
  successRate: number;
  errorRate: number;
  queueLength: number;
  activeJobs: number;
  throughputPerHour: number;
  peakProcessingTime: number;
  lastUpdated: Date;
}

export interface ProcessingEvent {
  documentId: string;
  eventType: 'started' | 'completed' | 'failed' | 'queued';
  timestamp: Date;
  processingTime?: number;
  fileSize?: number;
  mimeType?: string;
  error?: string;
}

@Injectable()
export class DocumentProcessingMetricsService {
  private readonly logger = new Logger(DocumentProcessingMetricsService.name);
  private readonly metricsKey = 'doc_processing_metrics';
  private readonly eventsKey = 'doc_processing_events';
  private readonly alertsKey = 'doc_processing_alerts';
  private readonly events: ProcessingEvent[] = []; // In-memory storage for simplicity

  constructor(
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Record a processing event
   */
  async recordEvent(event: ProcessingEvent): Promise<void> {
    try {
      // Store event in memory (keep last 1000 events)
      this.events.unshift(event);
      if (this.events.length > 1000) {
        this.events.splice(1000);
      }

      // Update real-time metrics
      await this.updateMetrics(event);

      this.logger.debug(`Recorded processing event: ${event.eventType} for document ${event.documentId}`);
    } catch (error) {
      this.logger.error('Error recording processing event:', error);
    }
  }

  /**
   * Get current processing metrics
   */
  async getMetrics(): Promise<ProcessingMetrics> {
    try {
      const metricsData = await this.cacheManager.get<string>(this.metricsKey);

      if (!metricsData) {
        return this.getDefaultMetrics();
      }

      const parsed = JSON.parse(metricsData);
      return {
        ...parsed,
        lastUpdated: new Date(parsed.lastUpdated),
      };
    } catch (error) {
      this.logger.error('Error getting processing metrics:', error);
      return this.getDefaultMetrics();
    }
  }

  /**
   * Get recent processing events
   */
  async getRecentEvents(limit: number = 100): Promise<ProcessingEvent[]> {
    try {
      return this.events.slice(0, limit);
    } catch (error) {
      this.logger.error('Error getting recent events:', error);
      return [];
    }
  }

  /**
   * Get processing statistics for a time period
   */
  async getStatistics(hours: number = 24): Promise<{
    totalProcessed: number;
    averageTime: number;
    successCount: number;
    failureCount: number;
    throughput: number;
    peakHour: string;
    slowestDocument: { id: string; time: number } | null;
  }> {
    try {
      const events = await this.getRecentEvents(1000);
      const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
      
      const recentEvents = events.filter(event => event.timestamp > cutoffTime);
      
      const completedEvents = recentEvents.filter(event => 
        event.eventType === 'completed' && event.processingTime
      );
      
      const failedEvents = recentEvents.filter(event => event.eventType === 'failed');
      
      const totalProcessed = completedEvents.length + failedEvents.length;
      const successCount = completedEvents.length;
      const failureCount = failedEvents.length;
      
      const averageTime = completedEvents.length > 0
        ? completedEvents.reduce((sum, event) => sum + (event.processingTime || 0), 0) / completedEvents.length
        : 0;
      
      const throughput = totalProcessed / hours;
      
      // Find peak hour
      const hourlyStats = new Map<string, number>();
      recentEvents.forEach(event => {
        const hour = event.timestamp.getHours().toString().padStart(2, '0');
        hourlyStats.set(hour, (hourlyStats.get(hour) || 0) + 1);
      });
      
      const peakHour = Array.from(hourlyStats.entries())
        .sort((a, b) => b[1] - a[1])[0]?.[0] || '00';
      
      // Find slowest document
      const slowestDocument = completedEvents.length > 0
        ? completedEvents.reduce((slowest, event) => 
            (event.processingTime || 0) > (slowest?.processingTime || 0) ? event : slowest
          )
        : null;

      return {
        totalProcessed,
        averageTime: Math.round(averageTime),
        successCount,
        failureCount,
        throughput: Math.round(throughput * 100) / 100,
        peakHour: `${peakHour}:00`,
        slowestDocument: slowestDocument ? {
          id: slowestDocument.documentId,
          time: slowestDocument.processingTime || 0,
        } : null,
      };
    } catch (error) {
      this.logger.error('Error calculating statistics:', error);
      return {
        totalProcessed: 0,
        averageTime: 0,
        successCount: 0,
        failureCount: 0,
        throughput: 0,
        peakHour: '00:00',
        slowestDocument: null,
      };
    }
  }

  /**
   * Check for performance alerts
   */
  async checkAlerts(): Promise<string[]> {
    const alerts: string[] = [];
    
    try {
      const metrics = await this.getMetrics();
      const stats = await this.getStatistics(1); // Last hour
      
      // Check error rate
      if (metrics.errorRate > 0.1) { // 10% error rate
        alerts.push(`High error rate: ${(metrics.errorRate * 100).toFixed(1)}%`);
      }
      
      // Check queue length
      if (metrics.queueLength > 100) {
        alerts.push(`High queue length: ${metrics.queueLength} documents`);
      }
      
      // Check average processing time
      if (metrics.averageProcessingTime > 300000) { // 5 minutes
        alerts.push(`Slow processing: ${(metrics.averageProcessingTime / 1000).toFixed(1)}s average`);
      }
      
      // Check throughput
      if (stats.throughput < 1 && stats.totalProcessed > 0) {
        alerts.push(`Low throughput: ${stats.throughput.toFixed(2)} documents/hour`);
      }
      
      // Store alerts
      if (alerts.length > 0) {
        await this.cacheManager.set(this.alertsKey, JSON.stringify({
          alerts,
          timestamp: new Date().toISOString(),
        }), 3600000); // 1 hour in milliseconds
      }
      
    } catch (error) {
      this.logger.error('Error checking alerts:', error);
      alerts.push('Error checking system health');
    }
    
    return alerts;
  }

  /**
   * Update metrics based on processing event
   */
  private async updateMetrics(event: ProcessingEvent): Promise<void> {
    try {
      const currentMetrics = await this.getMetrics();
      
      let updatedMetrics = { ...currentMetrics };
      
      switch (event.eventType) {
        case 'completed':
          updatedMetrics.totalDocuments++;
          if (event.processingTime) {
            // Update average processing time
            const totalTime = currentMetrics.averageProcessingTime * (currentMetrics.totalDocuments - 1);
            updatedMetrics.averageProcessingTime = (totalTime + event.processingTime) / updatedMetrics.totalDocuments;
            
            // Update peak processing time
            updatedMetrics.peakProcessingTime = Math.max(
              updatedMetrics.peakProcessingTime,
              event.processingTime
            );
          }
          break;
          
        case 'failed':
          updatedMetrics.totalDocuments++;
          break;
      }
      
      // Recalculate success/error rates
      const recentEvents = await this.getRecentEvents(100);
      const completedCount = recentEvents.filter(e => e.eventType === 'completed').length;
      const failedCount = recentEvents.filter(e => e.eventType === 'failed').length;
      const totalCount = completedCount + failedCount;
      
      if (totalCount > 0) {
        updatedMetrics.successRate = completedCount / totalCount;
        updatedMetrics.errorRate = failedCount / totalCount;
      }
      
      // Calculate throughput (documents per hour)
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      const recentCompletions = recentEvents.filter(e => 
        (e.eventType === 'completed' || e.eventType === 'failed') && 
        e.timestamp > oneHourAgo
      );
      updatedMetrics.throughputPerHour = recentCompletions.length;
      
      updatedMetrics.lastUpdated = new Date();

      await this.cacheManager.set(this.metricsKey, JSON.stringify(updatedMetrics), 3600000); // 1 hour
      
    } catch (error) {
      this.logger.error('Error updating metrics:', error);
    }
  }

  private getDefaultMetrics(): ProcessingMetrics {
    return {
      totalDocuments: 0,
      averageProcessingTime: 0,
      successRate: 0,
      errorRate: 0,
      queueLength: 0,
      activeJobs: 0,
      throughputPerHour: 0,
      peakProcessingTime: 0,
      lastUpdated: new Date(),
    };
  }

  /**
   * Scheduled task to clean up old events and update metrics
   */
  @Cron(CronExpression.EVERY_HOUR)
  async cleanupAndUpdate(): Promise<void> {
    try {
      // Keep only last 1000 events in memory
      if (this.events.length > 1000) {
        this.events.splice(1000);
      }

      // Check for alerts
      const alerts = await this.checkAlerts();
      if (alerts.length > 0) {
        this.logger.warn(`Performance alerts: ${alerts.join(', ')}`);
      }

      this.logger.debug('Completed metrics cleanup and update');
    } catch (error) {
      this.logger.error('Error in scheduled cleanup:', error);
    }
  }
}

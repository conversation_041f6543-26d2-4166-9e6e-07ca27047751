import { Injectable, Logger, Inject, NotFoundException } from '@nestjs/common';
import {
  Document,
  DocumentMetadata,
} from '../../../common/interfaces/document.interface';
import { IDocumentProcessor } from '../interfaces/document-processor.interface';

@Injectable()
export class DocumentsService {
  private readonly logger = new Logger(DocumentsService.name);

  constructor(@Inject('IDocumentProcessor') private readonly documentProcessor: IDocumentProcessor) {}

  async processDocument(documentId: string): Promise<void> {
    this.logger.log(`Delegating document processing for ${documentId}`);
    await this.documentProcessor.processDocument(documentId);
  }

  async updateDocumentStatus(
    documentId: string,
    status: string,
    error?: string,
  ): Promise<void> {
    await this.documentProcessor.updateDocumentStatus(
      documentId,
      status,
      error,
    );
  }

  async extractMetadata(documentId: string): Promise<void> {
    await this.documentProcessor.extractMetadata(documentId);
  }

  async generateSummary(documentId: string): Promise<void> {
    await this.documentProcessor.generateSummary(documentId);
  }

  async getDocumentContent(documentId: string): Promise<string> {
    this.logger.debug(`Getting document content for ID: ${documentId}`);
    try {
      const document = await this.documentProcessor.getMetadata(documentId);
      
      if (!document || !document.content) {
        this.logger.warn(`No content found for document ${documentId}`);
        throw new NotFoundException(`Document content not found for ID: ${documentId}`);
      }
      
      this.logger.debug(`Retrieved document content length: ${document.content.length}`);
      return document.content;
    } catch (error) {
      this.logger.error(`Error getting document content for ${documentId}: ${error.message}`);
      throw error;
    }
  }

  async updateMetadata(
    documentId: string,
    metadata: Partial<DocumentMetadata>,
  ): Promise<void> {
    await this.documentProcessor.updateMetadata(documentId, metadata);
  }

  async saveDocument(document: Document): Promise<void> {
    await this.documentProcessor.saveDocument(document);
  }

  async getMetadata(documentId: string): Promise<DocumentMetadata | null> {
    return this.documentProcessor.getMetadata(documentId);
  }

  async findById(documentId: string) {
    const metadata = await this.documentProcessor.getMetadata(documentId);
    if (!metadata) return null;

    return {
      id: documentId,
      metadata,
      content: metadata.content || '',
    };
  }

  async getDocumentVersions(documentId: string): Promise<any[]> {
    if (typeof (this.documentProcessor as any).getDocumentVersions === 'function') {
      this.logger.log(`Fetching versions for document ${documentId} via processor`);
      return await (this.documentProcessor as any).getDocumentVersions(documentId);
    } else {
      this.logger.warn('getDocumentVersions method not found on the document processor implementation.');
      return [];
    }
  }
}

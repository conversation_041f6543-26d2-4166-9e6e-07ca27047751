import { Injectable } from '@nestjs/common';
import { ContractPlaybook } from '../interfaces/contract-playbook.interface';
import { ContractType, RuleType, RuleSeverity } from '../schemas/contract-playbook.schema';
import { DocumentType } from '../../../common/enums/document-type.enum';

@Injectable()
export class SampleContractPlaybooksService {
  private readonly samplePlaybooks: ContractPlaybook[] = [
    // 1. NDA Contract Playbook
    {
      id: 'sample-nda-playbook',
      organizationId: 'sample',
      name: 'Standard NDA Compliance Playbook',
      contractType: ContractType.NDA,
      description: 'Comprehensive compliance rules for Non-Disclosure Agreements',
      version: '1.0.0',
      rules: [
        {
          id: 'nda-mutual-confidentiality',
          name: 'Mutual Confidentiality Requirement',
          category: 'Confidentiality',
          ruleType: RuleType.REQUIRED_CLAUSE,
          severity: RuleSeverity.HIGH,
          criteria: {
            keywords: ['mutual', 'confidentiality', 'both parties', 'reciprocal'],
            patterns: ['mutual.*confidential', 'both.*parties.*confidential'],
            semanticConcepts: ['mutual confidentiality', 'reciprocal protection'],
            contextRequirements: ['confidentiality section']
          },
          acceptableLanguage: {
            preferred: [
              'Both parties acknowledge that confidential information may be disclosed by either party',
              'This Agreement creates mutual confidentiality obligations'
            ],
            acceptable: [
              'Each party may disclose confidential information to the other',
              'Confidentiality obligations apply to both parties'
            ],
            fallbackPositions: [
              'Confidential information disclosed by either party shall be protected'
            ]
          },
          unacceptableTerms: {
            prohibited: [
              'one-way confidentiality',
              'only receiving party obligations',
              'unilateral confidentiality'
            ],
            requiresEscalation: [
              'perpetual confidentiality',
              'unlimited confidentiality period'
            ],
            autoReject: [
              'no confidentiality obligations for disclosing party'
            ]
          },
          negotiationGuidance: {
            strategy: 'Insist on mutual confidentiality to ensure balanced protection',
            alternatives: [
              'Propose separate mutual NDA',
              'Include reciprocal confidentiality clause'
            ],
            businessImpact: 'One-way NDAs create unbalanced risk exposure'
          },
          description: 'Ensures both parties have confidentiality obligations',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'nda-term-limit',
          name: 'Confidentiality Term Limitation',
          category: 'Term Limits',
          ruleType: RuleType.TERM_LIMIT,
          severity: RuleSeverity.MEDIUM,
          criteria: {
            keywords: ['term', 'duration', 'period', 'years', 'expire'],
            patterns: ['\\d+\\s*years?', 'term.*\\d+', 'duration.*\\d+'],
            semanticConcepts: ['time limitation', 'confidentiality period'],
            contextRequirements: ['term section', 'duration clause']
          },
          acceptableLanguage: {
            preferred: [
              'Confidentiality obligations shall survive for three (3) years',
              'This Agreement shall remain in effect for five (5) years'
            ],
            acceptable: [
              'Confidentiality period of seven (7) years',
              'Term of up to ten (10) years for trade secrets'
            ],
            fallbackPositions: [
              'Reasonable confidentiality period not exceeding industry standards'
            ]
          },
          unacceptableTerms: {
            prohibited: [
              'perpetual confidentiality',
              'indefinite term',
              'no expiration'
            ],
            requiresEscalation: [
              'term exceeding 10 years',
              'automatic renewal without notice'
            ],
            autoReject: [
              'permanent confidentiality obligations'
            ]
          },
          negotiationGuidance: {
            strategy: 'Negotiate reasonable time limits based on information sensitivity',
            alternatives: [
              'Tiered confidentiality periods by information type',
              'Standard 3-5 year terms with exceptions for trade secrets'
            ],
            businessImpact: 'Unlimited confidentiality creates indefinite legal exposure'
          },
          description: 'Ensures confidentiality obligations have reasonable time limits',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ],
      metadata: {
        industry: 'General',
        jurisdiction: 'United States',
        riskProfile: 'Medium',
        author: 'Legal Team',
        tags: ['nda', 'confidentiality', 'standard', 'compliance']
      },
      isActive: true,
      isTemplate: true,
      createdBy: 'system',
      createdAt: new Date(),
      updatedAt: new Date()
    },

    // 2. Service Agreement Contract Playbook
    {
      id: 'sample-service-agreement-playbook',
      organizationId: 'sample',
      name: 'Service Agreement Compliance Playbook',
      contractType: ContractType.SERVICE_AGREEMENT,
      description: 'Essential compliance rules for service agreements and consulting contracts',
      version: '1.0.0',
      rules: [
        {
          id: 'service-liability-cap',
          name: 'Liability Limitation Requirement',
          category: 'Risk Management',
          ruleType: RuleType.LIABILITY_CAP,
          severity: RuleSeverity.CRITICAL,
          criteria: {
            keywords: ['liability', 'limitation', 'cap', 'limit', 'damages'],
            patterns: ['liability.*limit', 'damages.*cap', 'limitation.*liability'],
            semanticConcepts: ['liability limitation', 'damage caps', 'risk limitation'],
            contextRequirements: ['liability section', 'limitation clause']
          },
          acceptableLanguage: {
            preferred: [
              'Total liability shall not exceed the fees paid under this Agreement',
              'Liability is limited to direct damages only, capped at contract value'
            ],
            acceptable: [
              'Maximum liability limited to $X or fees paid, whichever is greater',
              'Liability cap of 2x the annual contract value'
            ],
            fallbackPositions: [
              'Reasonable liability limitation based on contract value'
            ]
          },
          unacceptableTerms: {
            prohibited: [
              'unlimited liability',
              'no liability limitations',
              'full damages exposure'
            ],
            requiresEscalation: [
              'liability exceeding 5x contract value',
              'personal guarantees required'
            ],
            autoReject: [
              'unlimited consequential damages',
              'no liability caps whatsoever'
            ]
          },
          negotiationGuidance: {
            strategy: 'Always include liability caps proportional to contract value',
            alternatives: [
              'Mutual liability limitations',
              'Tiered liability based on breach type'
            ],
            businessImpact: 'Unlimited liability exposure can exceed contract value by orders of magnitude'
          },
          description: 'Ensures liability exposure is limited and proportional',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ],
      metadata: {
        industry: 'Professional Services',
        jurisdiction: 'United States',
        riskProfile: 'Medium',
        author: 'Legal Team',
        tags: ['service-agreement', 'consulting', 'liability', 'compliance']
      },
      isActive: true,
      isTemplate: true,
      createdBy: 'system',
      createdAt: new Date(),
      updatedAt: new Date()
    },

    // 3. Employment Contract Playbook
    {
      id: 'sample-employment-playbook',
      organizationId: 'sample',
      name: 'Employment Contract Compliance Playbook',
      contractType: ContractType.EMPLOYMENT_CONTRACT,
      description: 'Compliance rules for employment agreements and contractor relationships',
      version: '1.0.0',
      rules: [
        {
          id: 'employment-termination-rights',
          name: 'Termination Rights Balance',
          category: 'Employment Terms',
          ruleType: RuleType.TERMINATION_RIGHTS,
          severity: RuleSeverity.HIGH,
          criteria: {
            keywords: ['termination', 'notice', 'cause', 'severance', 'resignation'],
            patterns: ['termination.*notice', 'with.*cause', 'without.*cause'],
            semanticConcepts: ['termination rights', 'notice periods', 'severance'],
            contextRequirements: ['termination section', 'employment terms']
          },
          acceptableLanguage: {
            preferred: [
              'Either party may terminate with 30 days written notice',
              'Termination for cause requires written notice and opportunity to cure'
            ],
            acceptable: [
              'Two weeks notice for termination without cause',
              'Mutual termination rights with equal notice periods'
            ],
            fallbackPositions: [
              'Reasonable notice period consistent with local employment law'
            ]
          },
          unacceptableTerms: {
            prohibited: [
              'immediate termination without cause',
              'no notice required for employer',
              'unequal termination rights'
            ],
            requiresEscalation: [
              'termination without severance',
              'non-compete exceeding 12 months'
            ],
            autoReject: [
              'at-will employment in jurisdictions prohibiting it'
            ]
          },
          negotiationGuidance: {
            strategy: 'Ensure balanced termination rights and adequate notice periods',
            alternatives: [
              'Graduated notice periods based on tenure',
              'Severance packages for termination without cause'
            ],
            businessImpact: 'Unbalanced termination rights create employment law risks'
          },
          description: 'Ensures fair and legal termination provisions',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ],
      metadata: {
        industry: 'General',
        jurisdiction: 'United States',
        riskProfile: 'High',
        author: 'HR Legal Team',
        tags: ['employment', 'termination', 'compliance', 'labor-law']
      },
      isActive: true,
      isTemplate: true,
      createdBy: 'system',
      createdAt: new Date(),
      updatedAt: new Date()
    },

    // 4. Software License Agreement Playbook
    {
      id: 'sample-software-license-playbook',
      organizationId: 'sample',
      name: 'Software License Compliance Playbook',
      contractType: ContractType.LICENSING_AGREEMENT,
      description: 'Compliance rules for software licensing and intellectual property agreements',
      version: '1.0.0',
      rules: [
        {
          id: 'license-ip-protection',
          name: 'Intellectual Property Protection',
          category: 'Intellectual Property',
          ruleType: RuleType.INTELLECTUAL_PROPERTY,
          severity: RuleSeverity.CRITICAL,
          criteria: {
            keywords: ['intellectual property', 'copyright', 'patent', 'trademark', 'proprietary'],
            patterns: ['IP.*rights', 'intellectual.*property', 'proprietary.*rights'],
            semanticConcepts: ['IP ownership', 'license scope', 'proprietary rights'],
            contextRequirements: ['intellectual property section', 'license grant']
          },
          acceptableLanguage: {
            preferred: [
              'Licensor retains all intellectual property rights in the software',
              'License is non-exclusive and non-transferable'
            ],
            acceptable: [
              'Limited license grant for specified use only',
              'No ownership rights transferred to licensee'
            ],
            fallbackPositions: [
              'Clear delineation of licensed vs. owned IP'
            ]
          },
          unacceptableTerms: {
            prohibited: [
              'transfer of IP ownership',
              'unlimited license scope',
              'right to sublicense without restriction'
            ],
            requiresEscalation: [
              'exclusive license grants',
              'perpetual license terms'
            ],
            autoReject: [
              'assignment of all IP rights to licensee'
            ]
          },
          negotiationGuidance: {
            strategy: 'Protect core IP while granting necessary usage rights',
            alternatives: [
              'Tiered licensing with different IP protections',
              'Field-of-use restrictions to limit scope'
            ],
            businessImpact: 'Overly broad IP grants can compromise competitive advantage'
          },
          description: 'Ensures intellectual property rights are properly protected',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 'license-data-protection',
          name: 'Data Protection and Privacy',
          category: 'Data Protection',
          ruleType: RuleType.DATA_PROTECTION,
          severity: RuleSeverity.HIGH,
          criteria: {
            keywords: ['data protection', 'privacy', 'personal data', 'GDPR', 'CCPA'],
            patterns: ['data.*protection', 'privacy.*compliance', 'personal.*data'],
            semanticConcepts: ['data privacy', 'regulatory compliance', 'data security'],
            contextRequirements: ['data protection section', 'privacy clause']
          },
          acceptableLanguage: {
            preferred: [
              'Licensee shall comply with all applicable data protection laws',
              'Data processing shall be limited to licensed purposes only'
            ],
            acceptable: [
              'GDPR and CCPA compliance required',
              'Data security measures must meet industry standards'
            ],
            fallbackPositions: [
              'Compliance with applicable privacy regulations'
            ]
          },
          unacceptableTerms: {
            prohibited: [
              'no data protection obligations',
              'unlimited data usage rights',
              'data sharing without consent'
            ],
            requiresEscalation: [
              'data retention beyond license term',
              'cross-border data transfers without safeguards'
            ],
            autoReject: [
              'waiver of all privacy obligations'
            ]
          },
          negotiationGuidance: {
            strategy: 'Ensure comprehensive data protection compliance',
            alternatives: [
              'Data processing addendum with specific protections',
              'Regular compliance audits and certifications'
            ],
            businessImpact: 'Data protection violations can result in significant regulatory penalties'
          },
          description: 'Ensures compliance with data protection and privacy regulations',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ],
      metadata: {
        industry: 'Technology',
        jurisdiction: 'United States',
        riskProfile: 'High',
        author: 'IP Legal Team',
        tags: ['software', 'licensing', 'intellectual-property', 'data-protection']
      },
      isActive: true,
      isTemplate: true,
      createdBy: 'system',
      createdAt: new Date(),
      updatedAt: new Date()
    },

    // 5. Purchase Agreement Playbook
    {
      id: 'sample-purchase-agreement-playbook',
      organizationId: 'sample',
      name: 'Purchase Agreement Compliance Playbook',
      contractType: ContractType.PURCHASE_AGREEMENT,
      description: 'Essential compliance rules for purchase agreements and procurement contracts',
      version: '1.0.0',
      rules: [
        {
          id: 'purchase-governing-law',
          name: 'Governing Law and Jurisdiction',
          category: 'Legal Framework',
          ruleType: RuleType.GOVERNING_LAW,
          severity: RuleSeverity.MEDIUM,
          criteria: {
            keywords: ['governing law', 'jurisdiction', 'disputes', 'courts', 'arbitration'],
            patterns: ['governed.*by.*law', 'jurisdiction.*courts', 'dispute.*resolution'],
            semanticConcepts: ['legal jurisdiction', 'dispute resolution', 'applicable law'],
            contextRequirements: ['governing law section', 'dispute resolution clause']
          },
          acceptableLanguage: {
            preferred: [
              'This Agreement shall be governed by the laws of [Home State]',
              'Disputes shall be resolved in the courts of [Home Jurisdiction]'
            ],
            acceptable: [
              'Mutual jurisdiction acceptable to both parties',
              'Arbitration in neutral jurisdiction'
            ],
            fallbackPositions: [
              'Jurisdiction with established commercial law framework'
            ]
          },
          unacceptableTerms: {
            prohibited: [
              'foreign jurisdiction with unfavorable laws',
              'exclusive jurisdiction in counterparty location',
              'waiver of jury trial rights'
            ],
            requiresEscalation: [
              'mandatory arbitration with limited appeal rights',
              'jurisdiction in countries with weak legal systems'
            ],
            autoReject: [
              'governing law of jurisdictions with conflicting regulations'
            ]
          },
          negotiationGuidance: {
            strategy: 'Secure favorable jurisdiction and governing law',
            alternatives: [
              'Mutual jurisdiction selection',
              'Arbitration with established rules (ICC, AAA)'
            ],
            businessImpact: 'Unfavorable jurisdiction can significantly impact dispute resolution costs and outcomes'
          },
          description: 'Ensures favorable governing law and jurisdiction selection',
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ],
      metadata: {
        industry: 'General',
        jurisdiction: 'United States',
        riskProfile: 'Medium',
        author: 'Procurement Legal Team',
        tags: ['purchase', 'procurement', 'governing-law', 'compliance']
      },
      isActive: true,
      isTemplate: true,
      createdBy: 'system',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  /**
   * Get all sample contract playbooks with optional filtering
   */
  getFilteredSamplePlaybooks(filters?: {
    contractType?: ContractType;
    industry?: string;
    riskProfile?: string;
    tags?: string[];
  }): ContractPlaybook[] {
    let filtered = [...this.samplePlaybooks];

    if (filters?.contractType) {
      filtered = filtered.filter(playbook => playbook.contractType === filters.contractType);
    }

    if (filters?.industry) {
      filtered = filtered.filter(playbook => 
        playbook.metadata.industry?.toLowerCase().includes(filters.industry!.toLowerCase())
      );
    }

    if (filters?.riskProfile) {
      filtered = filtered.filter(playbook => 
        playbook.metadata.riskProfile?.toLowerCase() === filters.riskProfile!.toLowerCase()
      );
    }

    if (filters?.tags && filters.tags.length > 0) {
      filtered = filtered.filter(playbook =>
        filters.tags!.some(tag =>
          playbook.metadata.tags?.some(playbookTag =>
            playbookTag.toLowerCase().includes(tag.toLowerCase())
          )
        )
      );
    }

    return filtered;
  }

  /**
   * Get a specific sample contract playbook by ID
   */
  getSamplePlaybookById(playbookId: string): ContractPlaybook | null {
    return this.samplePlaybooks.find(playbook => playbook.id === playbookId) || null;
  }

  /**
   * Get all available contract types from samples
   */
  getAvailableContractTypes(): ContractType[] {
    return [...new Set(this.samplePlaybooks.map(playbook => playbook.contractType))];
  }

  /**
   * Get sample playbooks statistics
   */
  getStatistics(): {
    totalSamples: number;
    contractTypeDistribution: Array<{ contractType: ContractType; count: number }>;
    riskProfileDistribution: Array<{ riskProfile: string; count: number }>;
    industryDistribution: Array<{ industry: string; count: number }>;
  } {
    const contractTypeMap = new Map<ContractType, number>();
    const riskProfileMap = new Map<string, number>();
    const industryMap = new Map<string, number>();

    this.samplePlaybooks.forEach(playbook => {
      // Contract type distribution
      contractTypeMap.set(
        playbook.contractType,
        (contractTypeMap.get(playbook.contractType) || 0) + 1
      );

      // Risk profile distribution
      if (playbook.metadata.riskProfile) {
        riskProfileMap.set(
          playbook.metadata.riskProfile,
          (riskProfileMap.get(playbook.metadata.riskProfile) || 0) + 1
        );
      }

      // Industry distribution
      if (playbook.metadata.industry) {
        industryMap.set(
          playbook.metadata.industry,
          (industryMap.get(playbook.metadata.industry) || 0) + 1
        );
      }
    });

    return {
      totalSamples: this.samplePlaybooks.length,
      contractTypeDistribution: Array.from(contractTypeMap.entries())
        .map(([contractType, count]) => ({ contractType, count }))
        .sort((a, b) => b.count - a.count),
      riskProfileDistribution: Array.from(riskProfileMap.entries())
        .map(([riskProfile, count]) => ({ riskProfile, count }))
        .sort((a, b) => b.count - a.count),
      industryDistribution: Array.from(industryMap.entries())
        .map(([industry, count]) => ({ industry, count }))
        .sort((a, b) => b.count - a.count)
    };
  }
}

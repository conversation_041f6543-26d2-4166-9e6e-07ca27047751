import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as path from 'path';
import * as fs from 'fs/promises';
import { Worker } from 'worker_threads';
import * as os from 'os';
import * as crypto from 'crypto';
import { DocumentType } from '../../../common/enums/document-type.enum';
import { ProcessingOptions } from '../interfaces/processing-options.interface';

interface ProcessingResult {
  text: string;
  pageCount?: number;
  metadata?: Record<string, unknown>;
  chunkIndex?: number;
  error?: string;
}

// Define the status for document processing
export interface DocumentProcessingStatus {
  documentId: string;
  status: 'idle' | 'processing' | 'completed' | 'error';
  operation: string;
  startTime: Date;
  endTime?: Date;
  progress?: number;
  documentType?: DocumentType;
  relatedDocumentIds?: string[];
  error?: string;
}

@Injectable()
export class DocumentProcessingManagerService {
  private readonly logger = new Logger(DocumentProcessingManagerService.name);
  private readonly workerPath: string;
  private readonly defaultOptions: Required<Omit<ProcessingOptions, 'onProgress'>> = {
    chunkSize: 50 * 1024 * 1024, // 50MB chunks for large files
    maxWorkers: Math.max(1, Math.min(os.cpus().length - 1, 8)), // Use CPUs but cap at 8
    timeoutMs: 120000, // 2 minutes timeout for better reliability
    priority: false
  };

  private processingQueue: Map<string, Promise<ProcessingResult>> = new Map();
  private workerPool: Worker[] = [];
  private availableWorkers: Worker[] = [];
  private busyWorkers: Set<Worker> = new Set();
  private readonly tempDir: string;
  private workers: Worker[] = [];
  private workerIndex = 0;
  
  // Map to track processing status for documents
  private processingStatus: Map<string, DocumentProcessingStatus> = new Map();

  constructor(private configService: ConfigService) {
    this.tempDir = path.join(
      this.configService.get<string>('storage.uploadDir') || 'uploads',
      'temp',
    );
    
    // Set path to worker file (accounting for compiled location)
    const isDevelopment = process.env.NODE_ENV === 'development';
    const workerFileName = isDevelopment
      ? 'document-processor.worker.ts'
      : 'document-processor.worker.js';
      
    this.workerPath = path.resolve(
      process.cwd(),
      isDevelopment ? 'src' : 'dist',
      'modules/documents/workers',
      workerFileName
    );

    void this.initialize();
  }

  private async initialize(): Promise<void> {
    try {
      await this.ensureTempDirExists();

      // Verify worker file exists
      try {
        await fs.access(this.workerPath);
      } catch (error) {
        this.logger.error(`Worker file not found at ${this.workerPath}`);
        throw new Error('Worker file not found');
      }

      // Initialize worker pool with better management
      const maxWorkers = this.defaultOptions.maxWorkers;
      for (let i = 0; i < maxWorkers; i++) {
        const worker = new Worker(this.workerPath);

        // Set up worker event handlers
        worker.on('error', (error) => {
          this.logger.error(`Worker ${i} error:`, error);
          this.handleWorkerError(worker);
        });

        worker.on('exit', (code) => {
          if (code !== 0) {
            this.logger.warn(`Worker ${i} exited with code ${code}`);
            this.handleWorkerExit(worker);
          }
        });

        this.workerPool.push(worker);
        this.availableWorkers.push(worker);
      }

      this.logger.log(
        `Initialized document processing manager with ${maxWorkers} workers`,
      );
    } catch (error) {
      this.logger.error('Failed to initialize document processing manager:', error);
      throw error;
    }
  }

  private handleWorkerError(worker: Worker): void {
    // Remove from available workers and busy workers
    this.availableWorkers = this.availableWorkers.filter(w => w !== worker);
    this.busyWorkers.delete(worker);

    // Create a new worker to replace the failed one
    const newWorker = new Worker(this.workerPath);
    newWorker.on('error', (error) => {
      this.logger.error('Replacement worker error:', error);
      this.handleWorkerError(newWorker);
    });

    this.workerPool.push(newWorker);
    this.availableWorkers.push(newWorker);
  }

  private handleWorkerExit(worker: Worker): void {
    this.handleWorkerError(worker); // Same handling as error
  }

  private async ensureTempDirExists(): Promise<void> {
    try {
      await fs.mkdir(this.tempDir, { recursive: true });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to create temp directory: ${errorMessage}`);
    }
  }

  async processFile(
    filePath: string,
    mimeType: string,
    options?: ProcessingOptions,
  ): Promise<ProcessingResult> {
    const opts = { ...this.defaultOptions, ...options };
    const startTime = Date.now();

    try {
      const stat = await fs.stat(filePath);
      
      // Process file in chunks if it's large and not plain text
      if (stat.size > opts.chunkSize && mimeType !== 'text/plain') {
        const chunkPaths = await this.splitFileIntoChunks(filePath, opts.chunkSize);
        const chunkResults: ProcessingResult[] = [];
        const totalChunks = chunkPaths.length;

        // Process each chunk in parallel using worker pool
        for (let i = 0; i < chunkPaths.length; i++) {
          const result = await this.processSingleFile(chunkPaths[i], mimeType);
          result.chunkIndex = i;
          chunkResults.push(result);

          // Report progress if callback provided
          if (opts.onProgress) {
            opts.onProgress(i + 1, totalChunks);
          }

          // Clean up chunk file
          await fs.unlink(chunkPaths[i]);
        }

        // Merge results from all chunks
        const mergedResult = this.mergeResults(chunkResults);
        
        const duration = Date.now() - startTime;
        mergedResult.metadata = {
          ...mergedResult.metadata,
          processingTimeMs: duration,
          totalChunks,
          chunksProcessed: chunkResults.length
        };
        
        this.logger.log(
          `Processed file ${path.basename(filePath)} in ${duration}ms (${chunkPaths.length} chunks)`,
        );
        
        return mergedResult;
      }

      // Process small files or plain text files directly
      const result = await this.processSingleFile(filePath, mimeType);
      
      const duration = Date.now() - startTime;
      result.metadata = {
        ...result.metadata,
        processingTimeMs: duration,
        totalChunks: 1,
        chunksProcessed: 1
      };
      
      if (opts.onProgress) {
        opts.onProgress(1, 1);
      }
      
      this.logger.log(
        `Processed file ${path.basename(filePath)} in ${duration}ms`,
      );
      
      return result;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Error processing file ${filePath}: ${errorMessage}`);
      return {
        text: '',
        error: errorMessage,
      };
    }
  }

  private async processSingleFile(
    filePath: string,
    mimeType: string,
  ): Promise<ProcessingResult> {
    // Check if this file is already being processed
    const cacheKey = `${filePath}-${mimeType}`;
    if (this.processingQueue.has(cacheKey)) {
      this.logger.debug(`File already being processed: ${filePath}`);
      return this.processingQueue.get(cacheKey)!;
    }

    const processingPromise = this.processFileWithWorker(filePath, mimeType);
    this.processingQueue.set(cacheKey, processingPromise);

    try {
      const result = await processingPromise;
      return result;
    } finally {
      this.processingQueue.delete(cacheKey);
    }
  }

  private async processFileWithWorker(
    filePath: string,
    mimeType: string,
  ): Promise<ProcessingResult> {
    const worker = await this.getAvailableWorker();

    try {
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          this.releaseWorker(worker);
          reject(new Error(`Processing timeout for ${filePath}`));
        }, this.defaultOptions.timeoutMs);

        const messageHandler = (result: ProcessingResult) => {
          clearTimeout(timeout);
          worker.off('message', messageHandler);
          worker.off('error', errorHandler);
          this.releaseWorker(worker);
          resolve(result);
        };

        const errorHandler = (error: Error) => {
          clearTimeout(timeout);
          worker.off('message', messageHandler);
          worker.off('error', errorHandler);
          this.releaseWorker(worker);
          reject(error);
        };

        worker.on('message', messageHandler);
        worker.on('error', errorHandler);
        worker.postMessage({ filePath, mimeType });
      });
    } catch (error: unknown) {
      this.releaseWorker(worker);
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(
        `Worker error processing file ${filePath}: ${errorMessage}`,
      );
      return {
        text: '',
        error: errorMessage,
      };
    }
  }

  private async getAvailableWorker(): Promise<Worker> {
    // Wait for an available worker
    while (this.availableWorkers.length === 0) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    const worker = this.availableWorkers.pop()!;
    this.busyWorkers.add(worker);
    return worker;
  }

  private releaseWorker(worker: Worker): void {
    this.busyWorkers.delete(worker);
    if (!this.availableWorkers.includes(worker)) {
      this.availableWorkers.push(worker);
    }
  }

  private getNextWorker(): Worker {
    const worker = this.workers[this.workerIndex];
    this.workerIndex = (this.workerIndex + 1) % this.workers.length;
    return worker;
  }

  private async splitFileIntoChunks(
    filePath: string,
    chunkSize: number,
  ): Promise<string[]> {
    try {
      const chunkPaths: string[] = [];
      const fileHandle = await fs.open(filePath, 'r');
      const { size } = await fs.stat(filePath);
      const extension = path.extname(filePath);
      
      let bytesRead = 0;
      let chunkIndex = 0;

      // Create a buffer for reading chunks
      const buffer = new Uint8Array(chunkSize);

      while (bytesRead < size) {
        const result = await fileHandle.read(buffer, 0, chunkSize, bytesRead);
        if (result.bytesRead === 0) break;

        // Generate unique filename for this chunk
        const chunkPath = this.generateTempFilename(`_chunk${chunkIndex}${extension}`);
        
        // Write the chunk to a temporary file
        await fs.writeFile(chunkPath, buffer.subarray(0, result.bytesRead));

        chunkPaths.push(chunkPath);
        bytesRead += result.bytesRead;
        chunkIndex++;
      }

      await fileHandle.close();
      
      this.logger.log(
        `Split ${path.basename(filePath)} into ${chunkPaths.length} chunks`
      );
      
      return chunkPaths;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(
        `Error splitting file ${filePath}: ${errorMessage}`
      );
      throw error;
    }
  }

  private generateTempFilename(extension: string): string {
    const uniqueId = crypto.randomBytes(16).toString('hex');
    return path.join(this.tempDir, `${uniqueId}${extension}`);
  }

  private mergeResults(results: ProcessingResult[]): ProcessingResult {
    try {
      // Sort results by chunk index to maintain order
      results.sort((a, b) => (a.chunkIndex || 0) - (b.chunkIndex || 0));
      
      // Initialize merged result
      const merged: ProcessingResult = {
        text: '',
        metadata: {},
        pageCount: 0,
      };

      // Track any errors
      const errors: string[] = [];

      // Merge text content and collect errors
      for (const result of results) {
        if (result.error) {
          errors.push(`Chunk ${result.chunkIndex}: ${result.error}`);
        }
        
        merged.text += result.text;
        merged.pageCount = (merged.pageCount || 0) + (result.pageCount || 0);
        
        // Merge metadata objects
        if (result.metadata) {
          merged.metadata = this.mergeMetadata(merged.metadata, result.metadata);
        }
      }

      // If there were any errors, add them to metadata
      if (errors.length > 0) {
        merged.metadata = {
          ...merged.metadata,
          processingErrors: errors,
          partiallyProcessed: true,
        };
      }

      // Add processing summary to metadata
      merged.metadata = {
        ...merged.metadata,
        totalChunks: results.length,
        processingTimestamp: new Date().toISOString(),
        mergedContentLength: merged.text.length,
      };

      return merged;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`Error merging results: ${errorMessage}`);
      throw error;
    }
  }

  private mergeMetadata(
    target: Record<string, unknown>,
    source: Record<string, unknown>
  ): Record<string, unknown> {
    const merged = { ...target };
    
    for (const [key, value] of Object.entries(source)) {
      if (Array.isArray(value)) {
        // Combine arrays, removing duplicates
        merged[key] = Array.from(new Set([
          ...(Array.isArray(merged[key]) ? merged[key] as unknown[] : []),
          ...value
        ]));
      } else if (typeof value === 'object' && value !== null) {
        // Recursively merge nested objects
        merged[key] = this.mergeMetadata(
          (merged[key] as Record<string, unknown>) || {},
          value as Record<string, unknown>
        );
      } else if (typeof value === 'number') {
        // Sum numbers
        merged[key] = ((merged[key] as number) || 0) + value;
      } else {
        // For other types, prefer non-null values
        if (value !== null && value !== undefined) {
          merged[key] = value;
        }
      }
    }
    
    return merged;
  }

  async getDocumentProcessingStatus(documentId: string): Promise<DocumentProcessingStatus | null> {
    const status = this.processingStatus.get(documentId);
    return status || null;
  }

  async setDocumentProcessingStatus(
    documentId: string, 
    status: Partial<Omit<DocumentProcessingStatus, 'documentId'>>
  ): Promise<DocumentProcessingStatus> {
    const currentStatus = this.processingStatus.get(documentId);
    
    const newStatus: DocumentProcessingStatus = {
      documentId,
      status: status.status || currentStatus?.status || 'idle',
      operation: status.operation || currentStatus?.operation || 'unknown',
      startTime: status.startTime || currentStatus?.startTime || new Date(),
      endTime: status.endTime || currentStatus?.endTime,
      progress: status.progress || currentStatus?.progress,
      documentType: status.documentType || currentStatus?.documentType,
      relatedDocumentIds: status.relatedDocumentIds || currentStatus?.relatedDocumentIds,
      error: status.error || currentStatus?.error,
    };
    
    this.processingStatus.set(documentId, newStatus);
    
    this.logger.log(`Updated processing status for document ${documentId}: ${newStatus.status}`);
    
    return newStatus;
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Document, DOCUMENT_MODEL } from '../schemas/document.schema';
import { DocumentProcessingService } from './document-processing.service';
import { AnalyticsCollectionService } from '../../analytics/services/analytics-collection.service';

@Injectable()
export class DocumentClassificationService {
  private readonly logger = new Logger(DocumentClassificationService.name);
  private isProcessing = false;

  constructor(
    @InjectModel(DOCUMENT_MODEL) private documentModel: Model<Document>,
    private readonly documentProcessingService: DocumentProcessingService,
    private readonly analyticsCollectionService: AnalyticsCollectionService,
  ) {}

  /**
   * <PERSON>ron job that runs every hour to find and process unclassified documents
   * This will look for documents with type 'unknown' or 'UNKNOWN' and queue them for processing
   */
  @Cron(CronExpression.EVERY_HOUR)
  async classifyUnknownDocuments() {
    // Prevent multiple concurrent runs
    if (this.isProcessing) {
      this.logger.log('Classification job already in progress, skipping');
      return;
    }

    try {
      this.isProcessing = true;
      this.logger.log('Starting classification of unknown documents');

      // Find documents with unknown type (case insensitive)
      const unclassifiedDocs = await this.documentModel.find({
        $or: [
          { documentType: { $regex: /^unknown$/i } }, // Case insensitive match for 'unknown'
          { documentType: { $exists: false } },
          { documentType: null }
        ],
        // Only process documents that have content
        content: { 
          $exists: true, 
          $ne: null,
          $nin: ['', ' '] // Exclude empty or whitespace-only content
        },
        // Only process completed uploads
        status: 'uploaded',
      }).limit(50); // Process in batches to avoid overloading the system

      this.logger.log(`Found ${unclassifiedDocs.length} unclassified documents`);

      // Process each document
      for (const doc of unclassifiedDocs) {
        try {
          this.logger.log(`Queuing classification for document: ${doc.id}`);
          
          // Update status to avoid reprocessing in case of errors
          await this.documentModel.updateOne(
            { _id: doc._id },
            { $set: { status: 'processing' } }
          );
          
          // Queue document for processing with focus on metadata extraction
          await this.documentProcessingService.processDocument(
            doc.id,
            null, // No user ID for system-initiated processes
            {
              extractMetadata: true,
              generateSummary: false, // Only focus on classification
              priority: false // Lower priority for background tasks
            }
          );
          
          // Record classification event in analytics
          await this.recordClassificationEvent(doc.id, doc.organizationId);
          
          this.logger.log(`Successfully queued document ${doc.id} for classification`);
        } catch (docError) {
          this.logger.error(
            `Error queuing document ${doc.id} for classification: ${docError.message}`,
            docError.stack
          );
          
          // Reset status if processing failed to queue
          await this.documentModel.updateOne(
            { _id: doc._id },
            { $set: { status: 'uploaded' } }
          ).catch(resetError => {
            this.logger.error(`Failed to reset status for document ${doc.id}: ${resetError.message}`);
          });
        }
        
        // Add a small delay between processing documents to avoid overwhelming the queue
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      this.logger.log('Completed classification job for unknown documents');
    } catch (error) {
      this.logger.error(`Error in document classification job: ${error.message}`, error.stack);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Manually trigger classification of unknown documents
   * This can be called from an API endpoint if needed
   */
  async manuallyClassifyUnknownDocuments() {
    return this.classifyUnknownDocuments();
  }

  /**
   * Record a document classification event in the analytics system
   * @param documentId The ID of the document being classified
   * @param organizationId The organization ID the document belongs to
   */
  private async recordClassificationEvent(documentId: string, organizationId: string): Promise<void> {
    try {
      // Update the document with classification history
      await this.documentModel.updateOne(
        { id: documentId },
        {
          $set: {
            classifiedAt: new Date(),
            classifiedBy: 'system' // System-initiated classification
          },
          $push: {
            classificationHistory: {
              classifiedAt: new Date(),
              classifiedBy: 'system',
              method: 'automatic',
              previousType: 'unknown'
            }
          }
        }
      );

      // Record the event in analytics if organization ID is available
      if (organizationId) {
        await this.analyticsCollectionService.recordDocumentAnalysis(
          documentId,
          organizationId,
          'system', // System user ID for automated processes
          new Date(), // Start time (now)
          new Date(), // End time (now)
          { 
            analysisType: 'classification',
            source: 'cron-job'
          }
        );
      }
      
      this.logger.log(`Recorded classification event for document ${documentId}`);
    } catch (error) {
      this.logger.error(`Error recording classification event: ${error.message}`, error.stack);
    }
  }
}

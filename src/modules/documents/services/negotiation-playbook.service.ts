import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { AIService } from '../../ai/services/ai.service';
import { DocumentProcessingService } from './document-processing.service';
import { NegotiationPlaybook } from '../interfaces/negotiation-playbook.interface';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { NegotiationPlaybookDocument } from '../schemas/negotiation-playbook.schema';
import { SamplePlaybooksDataService } from './negotiation-playbook-seeder.service';
import { DocumentType } from '../../../common/enums/document-type.enum';

@Injectable()
export class NegotiationPlaybookService {
  private readonly logger = new Logger(NegotiationPlaybookService.name);

  constructor(
    private readonly aiService: AIService,
    private readonly documentProcessingService: DocumentProcessingService,
    private readonly samplePlaybooksDataService: SamplePlaybooksDataService,
    @InjectModel('NegotiationPlaybook')
    private readonly negotiationPlaybookModel: Model<NegotiationPlaybookDocument>,
  ) {}

  async generatePlaybook(
    documentId: string,
    options: {
      documentType: string;
      focusAreas?: string[];
      includeSimulations?: boolean;
      organizationPreferences?: string;
    },
  ): Promise<NegotiationPlaybook> {
    this.logger.log(
      `Generating negotiation playbook for document ${documentId}`,
    );

    // Get document content
    const document = await this.documentProcessingService.getDocumentById(
      documentId,
    );
    if (!document) {
      throw new NotFoundException(`Document with ID ${documentId} not found`);
    }

    const documentContent = document.content;

    // Generate prompt for AI service
    const prompt = this.generateNegotiationPrompt(
      documentContent,
      options.documentType,
      options.focusAreas,
      options.includeSimulations,
      options.organizationPreferences,
    );

    // Call AI service with specialized prompt
    const result = await this.aiService.generateResponse(prompt, {
      temperature: 0.2,
      systemMessage:
        'You are an expert legal negotiator. Analyze the document and provide strategic negotiation recommendations in JSON format.',
      responseFormat: 'json',
    });

    // Parse and validate the result
    let playbook: NegotiationPlaybook;
    try {
      // Clean the AI response by removing markdown code blocks if present
      let cleanedResult = result.trim();
      if (cleanedResult.startsWith('```json')) {
        cleanedResult = cleanedResult
          .replace(/^```json\s*/, '')
          .replace(/\s*```$/, '');
      } else if (cleanedResult.startsWith('```')) {
        cleanedResult = cleanedResult
          .replace(/^```\s*/, '')
          .replace(/\s*```$/, '');
      }

      const parsedResult = JSON.parse(cleanedResult);
      playbook = {
        documentId,
        strategies: parsedResult.strategies || [],
        overallAssessment: parsedResult.overallAssessment || '',
        keyLeveragePoints: parsedResult.keyLeveragePoints || [],
        dealBreakers: parsedResult.dealBreakers || [],
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(`Error parsing AI response: ${error.message}`);
      throw new Error('Failed to generate negotiation playbook');
    }

    // Save playbook to database
    await this.savePlaybook(playbook);

    return playbook;
  }

  private generateNegotiationPrompt(
    documentContent: string,
    documentType: string,
    focusAreas?: string[],
    includeSimulations?: boolean,
    organizationPreferences?: string,
  ): string {
    let prompt = `Analyze the following ${documentType} and create a negotiation playbook with strategic recommendations:\n\n${documentContent}\n\n`;

    prompt +=
      'Focus on identifying clauses that could be negotiated more favorably, including:';
    prompt += '\n- Termination conditions';
    prompt += '\n- Liability limitations';
    prompt += '\n- Payment terms';
    prompt += '\n- Intellectual property rights';
    prompt += '\n- Confidentiality provisions';

    if (focusAreas && focusAreas.length > 0) {
      prompt += `\n\nPay special attention to these areas: ${focusAreas.join(
        ', ',
      )}`;
    }

    if (organizationPreferences) {
      prompt += `\n\nConsider these organizational preferences: ${organizationPreferences}`;
    }

    prompt += '\n\nFor each identified section, provide:';
    prompt += '\n1. Specific recommendations for negotiation';
    prompt += '\n2. Risk level assessment (low, medium, high)';
    prompt += '\n3. Priority ranking';
    prompt += '\n4. Alternative language suggestions';

    if (includeSimulations !== false) {
      prompt +=
        '\n5. Simulation scenarios including potential counterparty positions and recommended responses';
    }

    prompt +=
      '\n\nFormat your response as a JSON object with the following structure:';
    prompt += `\n{
      "strategies": [
        {
          "section": "Section name",
          "recommendations": ["Recommendation 1", "Recommendation 2"],
          "riskLevel": "medium",
          "priority": 2,
          "alternativeLanguage": "Suggested alternative text",
          "simulationScenarios": [
            {
              "type": "concession",
              "trigger": "counterparty request",
              "responseStrategy": "recommended response",
              "expectedOutcome": "likely result"
            }
          ]
        }
      ],
      "overallAssessment": "Overall assessment of negotiation position",
      "keyLeveragePoints": ["Key leverage point 1", "Key leverage point 2"],
      "dealBreakers": ["Deal breaker 1", "Deal breaker 2"]
    }`;

    return prompt;
  }

  async savePlaybook(
    playbook: NegotiationPlaybook,
  ): Promise<NegotiationPlaybookDocument> {
    const newPlaybook = new this.negotiationPlaybookModel(playbook);
    return newPlaybook.save();
  }

  async getPlaybookByDocumentId(
    documentId: string,
  ): Promise<NegotiationPlaybook | null> {
    const result = await this.negotiationPlaybookModel.findOne({ documentId }).exec();
    return result ? result.toObject() : null;
  }

  /**
   * Get all sample/template playbooks
   */
  getSamplePlaybooks(filters?: {
    contractType?: string;
    industry?: string;
    difficulty?: string;
    tags?: string[];
  }): NegotiationPlaybook[] {
    const typedFilters = filters ? {
      ...filters,
      contractType: filters.contractType as DocumentType
    } : undefined;
    return this.samplePlaybooksDataService.getFilteredSamplePlaybooks(typedFilters);
  }

  /**
   * Get a specific sample playbook by ID
   */
  getSamplePlaybookById(playbookId: string): NegotiationPlaybook | null {
    return this.samplePlaybooksDataService.getSamplePlaybookById(playbookId);
  }

  /**
   * Clone a sample playbook for a specific document
   */
  async cloneSamplePlaybook(
    samplePlaybookId: string,
    documentId: string,
    organizationId?: string,
    userId?: string,
  ): Promise<NegotiationPlaybook> {
    const samplePlaybook = this.getSamplePlaybookById(samplePlaybookId);
    if (!samplePlaybook) {
      throw new NotFoundException(`Sample playbook with ID ${samplePlaybookId} not found`);
    }

    // Create a new playbook based on the sample
    const clonedPlaybook: NegotiationPlaybook = {
      documentId,
      strategies: samplePlaybook.strategies,
      overallAssessment: samplePlaybook.overallAssessment,
      keyLeveragePoints: samplePlaybook.keyLeveragePoints,
      dealBreakers: samplePlaybook.dealBreakers,
      timestamp: new Date(),
      organizationId,
      userId,
      isTemplate: false, // This is a user-specific playbook
      contractType: samplePlaybook.contractType,
      industry: samplePlaybook.industry,
      tags: samplePlaybook.tags,
    };

    // Save the cloned playbook
    const savedPlaybook = await this.savePlaybook(clonedPlaybook);

    // Note: We don't track usage count for code-based sample playbooks
    // Usage analytics can be tracked through user playbook creation instead

    this.logger.log(`Cloned sample playbook ${samplePlaybookId} for document ${documentId}`);
    return savedPlaybook.toObject();
  }

  /**
   * Get playbook statistics for analytics
   */
  async getPlaybookStats(): Promise<{
    totalSamples: number;
    totalUserPlaybooks: number;
    popularSamples: Array<{ templateName: string; usageCount: number }>;
    contractTypeDistribution: Array<{ contractType: string; count: number }>;
  }> {
    // Get sample playbooks from code
    const samplePlaybooks = this.getSamplePlaybooks();

    // Get user-generated playbooks from database
    const totalUserPlaybooks = await this.negotiationPlaybookModel.countDocuments({
      isTemplate: { $ne: true }
    });

    // Calculate contract type distribution from code-based samples
    const contractTypeMap = new Map<string, number>();
    samplePlaybooks.forEach(playbook => {
      if (playbook.contractType) {
        contractTypeMap.set(
          playbook.contractType,
          (contractTypeMap.get(playbook.contractType) || 0) + 1
        );
      }
    });

    const contractTypeDistribution = Array.from(contractTypeMap.entries())
      .map(([contractType, count]) => ({ contractType, count }))
      .sort((a, b) => b.count - a.count);

    // For now, popular samples are just the first few (since we don't track usage in code)
    const popularSamples = samplePlaybooks
      .slice(0, 5)
      .map(playbook => ({
        templateName: playbook.templateName || 'Unnamed Template',
        usageCount: 0, // We don't track usage for code-based samples
      }));

    return {
      totalSamples: samplePlaybooks.length,
      totalUserPlaybooks,
      popularSamples,
      contractTypeDistribution,
    };
  }
}

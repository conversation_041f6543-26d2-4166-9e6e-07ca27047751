import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { AIService } from '../../ai/services/ai.service';
import { DocumentsService } from './documents.service';
import { AnalyticsService } from '../../analytics/services/analytics.service';
import {
  PrivilegeLog,
  PrivilegeLogDocument,
  PRIVILEGE_LOG_MODEL,
} from '../schemas/privilege-log.schema';
import {
  PrivilegeAnalysisResult,
  PrivilegedContent,
  RedactionSuggestion,
  PrivilegeType,
  PrivilegeStatus,
  PrivilegeLogStatus,
  PrivilegeAnalysisOptions,
} from '../interfaces/privilege-log.interface';
import {
  AnalyzePrivilegeDto,
  ReviewPrivilegeDto,
  ApplyRedactionDto,
  BulkRedactionDto,
  PrivilegeLogQueryDto,
} from '../dto/privilege-log.dto';

@Injectable()
export class PrivilegeLogService {
  private readonly logger = new Logger(PrivilegeLogService.name);

  // Privilege detection patterns
  private readonly PRIVILEGE_PATTERNS = [
    {
      pattern: /attorney[- ]client privilege/gi,
      type: PrivilegeType.ATTORNEY_CLIENT,
      confidence: 0.9,
    },
    {
      pattern: /work product doctrine/gi,
      type: PrivilegeType.WORK_PRODUCT,
      confidence: 0.85,
    },
    {
      pattern: /confidential communication between/gi,
      type: PrivilegeType.CONFIDENTIAL_COMMUNICATION,
      confidence: 0.8,
    },
    {
      pattern: /privileged and confidential/gi,
      type: PrivilegeType.ATTORNEY_CLIENT,
      confidence: 0.85,
    },
    {
      pattern: /attorney work product/gi,
      type: PrivilegeType.WORK_PRODUCT,
      confidence: 0.9,
    },
    {
      pattern: /legal advice/gi,
      type: PrivilegeType.ATTORNEY_CLIENT,
      confidence: 0.7,
    },
    {
      pattern: /trade secret/gi,
      type: PrivilegeType.TRADE_SECRET,
      confidence: 0.8,
    },
    {
      pattern: /medical records/gi,
      type: PrivilegeType.MEDICAL_PRIVILEGE,
      confidence: 0.75,
    },
  ];

  constructor(
    @InjectModel(PRIVILEGE_LOG_MODEL)
    private readonly privilegeLogModel: Model<PrivilegeLogDocument>,
    private readonly aiService: AIService,
    private readonly documentsService: DocumentsService,
    private readonly analyticsService: AnalyticsService,
  ) {}

  async analyzeDocumentPrivilege(
    documentId: string,
    options: AnalyzePrivilegeDto,
    userId: string,
    organizationId: string,
  ): Promise<PrivilegeAnalysisResult> {
    const startTime = Date.now();
    this.logger.log(`Starting privilege analysis for document ${documentId}`);

    try {
      // Get document content
      const document = await this.documentsService.findById(documentId);
      if (!document || !document.content) {
        throw new NotFoundException(
          `Document ${documentId} not found or has no content`,
        );
      }

      const detectionMethods: string[] = [];
      let privilegedContent: PrivilegedContent[] = [];

      // 1. Pattern-based detection
      const patternResults = await this.detectPrivilegeByPatterns(
        document.content,
        documentId,
        options,
      );
      privilegedContent.push(...patternResults);
      detectionMethods.push('pattern');

      // 2. AI-based detection (if enabled)
      if (options.includeAIAnalysis) {
        const aiResults = await this.detectPrivilegeByAI(
          document.content,
          documentId,
          options,
        );
        privilegedContent.push(...aiResults);
        detectionMethods.push('ai');
      }

      // 3. Remove duplicates and merge overlapping detections
      privilegedContent = this.deduplicatePrivilegedContent(privilegedContent);

      // 4. Generate redaction suggestions
      const redactionSuggestions =
        this.generateRedactionSuggestions(privilegedContent);

      // 5. Calculate summary statistics
      const summary = this.calculateSummary(privilegedContent, options);

      // 6. Save privilege log to database
      await this.savePrivilegeLog(
        documentId,
        document.metadata?.title || 'Untitled Document',
        privilegedContent,
        userId,
        organizationId,
        detectionMethods,
        Date.now() - startTime,
      );

      // 7. Track analytics
      await this.analyticsService.trackPrivilegeAnalysis(
        userId,
        organizationId,
        documentId,
        {
          totalItemsFound: summary.totalItemsFound,
          detectionMethods,
          processingTime: Date.now() - startTime,
        },
      );

      const result: PrivilegeAnalysisResult = {
        documentId,
        privilegedContent,
        redactionSuggestions,
        summary,
        analysisMetadata: {
          analysisDate: new Date(),
          detectionMethods,
          aiModelUsed: options.includeAIAnalysis ? 'gpt-4' : undefined,
          processingTime: Date.now() - startTime,
        },
      };

      this.logger.log(
        `Privilege analysis completed for document ${documentId}. Found ${summary.totalItemsFound} privileged items.`,
      );

      return result;
    } catch (error) {
      this.logger.error(
        `Error analyzing privilege for document ${documentId}:`,
        error,
      );
      throw new BadRequestException(
        `Failed to analyze privilege: ${error.message}`,
      );
    }
  }

  private async detectPrivilegeByPatterns(
    content: string,
    documentId: string,
    options: AnalyzePrivilegeDto,
  ): Promise<PrivilegedContent[]> {
    const results: PrivilegedContent[] = [];
    const targetTypes = options.privilegeTypes || Object.values(PrivilegeType);

    for (const { pattern, type, confidence } of this.PRIVILEGE_PATTERNS) {
      if (!targetTypes.includes(type)) continue;
      if (confidence < (options.confidenceThreshold || 0.7)) continue;

      let match: RegExpExecArray | null;
      while ((match = pattern.exec(content)) !== null) {
        const privilegedContent: PrivilegedContent = {
          id: uuidv4(),
          documentId,
          startPosition: match.index,
          endPosition: match.index + match[0].length,
          content: match[0],
          privilegeType: type,
          confidenceScore: confidence,
          detectionMethod: 'pattern',
          status: PrivilegeStatus.DETECTED,
          redactionApplied: false,
        };

        results.push(privilegedContent);
      }
    }

    return results;
  }

  private async detectPrivilegeByAI(
    content: string,
    documentId: string,
    options: AnalyzePrivilegeDto,
  ): Promise<PrivilegedContent[]> {
    try {
      const prompt = this.buildAIPrivilegePrompt(content, options);

      const aiResponse = await this.aiService.generateResponse(prompt, {
        temperature: 0.1,
        systemMessage:
          'You are an expert legal AI specializing in privilege detection and attorney-client privilege law.',
        responseFormat: 'json',
      });

      // Clean and parse AI response
      let cleanedResponse = aiResponse.trim();
      if (cleanedResponse.startsWith('```json')) {
        cleanedResponse = cleanedResponse
          .replace(/^```json\s*/, '')
          .replace(/\s*```$/, '');
      } else if (cleanedResponse.startsWith('```')) {
        cleanedResponse = cleanedResponse
          .replace(/^```\s*/, '')
          .replace(/\s*```$/, '');
      }

      const aiResults = JSON.parse(cleanedResponse);

      return (
        aiResults.privilegedItems?.map((item: any) => ({
          id: uuidv4(),
          documentId,
          startPosition: item.startPosition || 0,
          endPosition: item.endPosition || 0,
          content: item.content || '',
          privilegeType: item.privilegeType || PrivilegeType.OTHER,
          confidenceScore: item.confidenceScore || 0.5,
          detectionMethod: 'ai',
          status: PrivilegeStatus.DETECTED,
          redactionApplied: false,
        })) || []
      );
    } catch (error) {
      this.logger.warn(`AI privilege detection failed: ${error.message}`);
      return [];
    }
  }

  private buildAIPrivilegePrompt(
    content: string,
    options: AnalyzePrivilegeDto,
  ): string {
    const targetTypes = options.privilegeTypes || Object.values(PrivilegeType);

    return `
Analyze the following legal document for privileged content that should be protected or redacted.

TARGET PRIVILEGE TYPES: ${targetTypes.join(', ')}
CONFIDENCE THRESHOLD: ${options.confidenceThreshold || 0.7}

DOCUMENT CONTENT:
${content}

Please identify any privileged content and return a JSON response with the following structure:
{
  "privilegedItems": [
    {
      "content": "exact text that is privileged",
      "privilegeType": "attorney_client|work_product|confidential_communication|trade_secret|medical_privilege|spousal_privilege|other",
      "confidenceScore": 0.0-1.0,
      "startPosition": number,
      "endPosition": number,
      "reason": "explanation for why this is privileged"
    }
  ]
}

Focus on:
1. Attorney-client communications
2. Work product doctrine materials
3. Confidential communications
4. Trade secrets
5. Medical privilege
6. Any other legally privileged content

Only include items with confidence score >= ${
      options.confidenceThreshold || 0.7
    }.
`;
  }

  private deduplicatePrivilegedContent(
    items: PrivilegedContent[],
  ): PrivilegedContent[] {
    const deduplicated: PrivilegedContent[] = [];

    for (const item of items) {
      const overlapping = deduplicated.find((existing) =>
        this.isOverlapping(item, existing),
      );

      if (overlapping) {
        // Keep the one with higher confidence
        if (item.confidenceScore > overlapping.confidenceScore) {
          const index = deduplicated.indexOf(overlapping);
          deduplicated[index] = item;
        }
      } else {
        deduplicated.push(item);
      }
    }

    return deduplicated.sort((a, b) => a.startPosition - b.startPosition);
  }

  private isOverlapping(
    item1: PrivilegedContent,
    item2: PrivilegedContent,
  ): boolean {
    return !(
      item1.endPosition <= item2.startPosition ||
      item2.endPosition <= item1.startPosition
    );
  }

  private generateRedactionSuggestions(
    privilegedContent: PrivilegedContent[],
  ): RedactionSuggestion[] {
    return privilegedContent.map((content) => ({
      contentId: content.id,
      originalText: content.content,
      suggestedRedaction: '[REDACTED]',
      reason: `${content.privilegeType} privilege detected with ${(
        content.confidenceScore * 100
      ).toFixed(1)}% confidence`,
      privilegeType: content.privilegeType,
      confidenceScore: content.confidenceScore,
      requiresReview: content.confidenceScore < 0.9,
    }));
  }

  private calculateSummary(
    privilegedContent: PrivilegedContent[],
    options: AnalyzePrivilegeDto,
  ) {
    return {
      totalItemsFound: privilegedContent.length,
      highConfidenceItems: privilegedContent.filter(
        (item) => item.confidenceScore >= 0.8,
      ).length,
      requiresManualReview: privilegedContent.filter(
        (item) => item.confidenceScore < 0.9 || options.requireManualReview,
      ).length,
      autoRedactable: privilegedContent.filter(
        (item) => item.confidenceScore >= 0.9 && !options.requireManualReview,
      ).length,
    };
  }

  private async savePrivilegeLog(
    documentId: string,
    documentTitle: string,
    privilegedContent: PrivilegedContent[],
    userId: string,
    organizationId: string,
    detectionMethods: string[],
    processingTime: number,
  ): Promise<PrivilegeLogDocument> {
    const privilegeLog = new this.privilegeLogModel({
      documentId,
      documentTitle,
      privilegedContent,
      totalPrivilegedItems: privilegedContent.length,
      totalRedactions: 0,
      analysisDate: new Date(),
      status: PrivilegeLogStatus.PENDING_REVIEW,
      organizationId,
      createdBy: userId,
      analysisMetadata: {
        analysisDate: new Date(),
        detectionMethods,
        processingTime,
      },
    });

    return await privilegeLog.save();
  }

  async getPrivilegeLog(
    documentId: string,
    organizationId: string,
  ): Promise<PrivilegeLogDocument> {
    const privilegeLog = await this.privilegeLogModel
      .findOne({ documentId, organizationId })
      .exec();

    if (!privilegeLog) {
      throw new NotFoundException(
        `No privilege log found for document ${documentId}`,
      );
    }

    return privilegeLog;
  }

  async getPrivilegeLogs(
    organizationId: string,
    query: PrivilegeLogQueryDto,
  ): Promise<{ logs: PrivilegeLogDocument[]; total: number }> {
    const filter: any = { organizationId };

    if (query.status) {
      filter.status = query.status;
    }

    if (query.privilegeType) {
      filter['privilegedContent.privilegeType'] = query.privilegeType;
    }

    const page = query.page || 1;
    const limit = query.limit || 20;
    const skip = (page - 1) * limit;

    const [logs, total] = await Promise.all([
      this.privilegeLogModel
        .find(filter)
        .sort({ analysisDate: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.privilegeLogModel.countDocuments(filter),
    ]);

    return { logs, total };
  }

  async reviewPrivilegedContent(
    documentId: string,
    contentId: string,
    reviewData: ReviewPrivilegeDto,
    userId: string,
    organizationId: string,
  ): Promise<PrivilegeLogDocument> {
    const privilegeLog = await this.getPrivilegeLog(documentId, organizationId);

    const contentItem = privilegeLog.privilegedContent.find(
      (item) => item.id === contentId,
    );
    if (!contentItem) {
      throw new NotFoundException(
        `Privileged content item ${contentId} not found`,
      );
    }

    // Update the content item
    contentItem.status = reviewData.status;
    contentItem.reviewedBy = userId;
    contentItem.reviewedAt = new Date();

    if (reviewData.reason) {
      contentItem.redactionReason = reviewData.reason;
    }

    if (reviewData.applyRedaction) {
      contentItem.redactionApplied = true;
      privilegeLog.totalRedactions += 1;
    }

    // Update log status if all items are reviewed
    const allReviewed = privilegeLog.privilegedContent.every(
      (item) =>
        item.status !== PrivilegeStatus.DETECTED &&
        item.status !== PrivilegeStatus.UNDER_REVIEW,
    );

    if (allReviewed) {
      privilegeLog.status = PrivilegeLogStatus.COMPLETED;
      privilegeLog.lastReviewDate = new Date();
      privilegeLog.reviewedBy = userId;
    }

    return await privilegeLog.save();
  }

  async applyRedaction(
    documentId: string,
    redactionData: ApplyRedactionDto,
    userId: string,
    organizationId: string,
  ): Promise<{ success: boolean; message: string }> {
    const privilegeLog = await this.getPrivilegeLog(documentId, organizationId);

    const contentItem = privilegeLog.privilegedContent.find(
      (item) => item.id === redactionData.contentId,
    );
    if (!contentItem) {
      throw new NotFoundException(
        `Privileged content item ${redactionData.contentId} not found`,
      );
    }

    // Mark as redacted
    contentItem.redactionApplied = true;
    contentItem.redactionReason = redactionData.reason;
    contentItem.status = PrivilegeStatus.REDACTED;
    contentItem.reviewedBy = userId;
    contentItem.reviewedAt = new Date();

    privilegeLog.totalRedactions += 1;
    await privilegeLog.save();

    // Track analytics
    await this.analyticsService.trackRedactionApplied(
      userId,
      organizationId,
      documentId,
      {
        contentId: redactionData.contentId,
        privilegeType: contentItem.privilegeType,
        reason: redactionData.reason,
      },
    );

    return {
      success: true,
      message: `Redaction applied to privileged content: ${contentItem.privilegeType}`,
    };
  }

  async applyBulkRedaction(
    documentId: string,
    bulkData: BulkRedactionDto,
    userId: string,
    organizationId: string,
  ): Promise<{ success: boolean; redactedCount: number; message: string }> {
    const privilegeLog = await this.getPrivilegeLog(documentId, organizationId);

    let redactedCount = 0;

    for (const contentId of bulkData.contentIds) {
      const contentItem = privilegeLog.privilegedContent.find(
        (item) => item.id === contentId,
      );
      if (contentItem && !contentItem.redactionApplied) {
        contentItem.redactionApplied = true;
        contentItem.redactionReason = bulkData.reason;
        contentItem.status = PrivilegeStatus.REDACTED;
        contentItem.reviewedBy = userId;
        contentItem.reviewedAt = new Date();
        redactedCount++;
      }
    }

    privilegeLog.totalRedactions += redactedCount;
    await privilegeLog.save();

    return {
      success: true,
      redactedCount,
      message: `Applied ${redactedCount} redactions`,
    };
  }
}

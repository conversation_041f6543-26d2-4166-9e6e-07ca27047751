import { Injectable, Logger, InternalServerErrorException, Inject, forwardRef } from '@nestjs/common';
import {
  LegalPattern,
  PatternRecognitionResult,
} from '../interfaces/legal-pattern.interface';
import { DocumentSection } from '../interfaces/document.interface';
import { AIService } from '../../ai/services/ai.service';

@Injectable()
export class LegalPatternRecognitionService {
  private readonly logger = new Logger(LegalPatternRecognitionService.name);

  constructor(
    @Inject(forwardRef(() => AIService))
    private readonly aiService: AIService
  ) {}

  // Define regex patterns for common legal structures
  private readonly patterns = {
    // Document structure patterns (refined)
    section: /(?:Section|SECTION|§)\s*(\d+(?:\.\d+)*)\s*[.:]\s*(.+?)(?=\n|$)/gm,
    clause: /(?:Clause|CLAUSE)\s+(\d+(?:\.\d+)*)\s*[.:]\s*(.+?)(?=\n|$)/gm,
    article: /(?:Article|ARTICLE)\s+(\d+(?:\.\d+)*)\s*[.:]\s*(.+?)(?=\n|$)/gm,
    exhibit: /(?:Exhibit|EXHIBIT|Appendix|APPENDIX|Annex|ANNEX)\s+([A-Z]|[0-9]+)\s*[.:]\s*(.+?)(?=\n|$)/gm,
    schedule: /(?:Schedule|SCHEDULE)\s+([A-Z]|[0-9]+)\s*[.:]\s*(.+?)(?=\n|$)/gm,
    subsection: /(?:Subsection|SUBSECTION)\s*(\([a-z]\)|\d+(?:\.\d+)*)\s*[.:]\s*(.+?)(?=\n|$)/gm,
    
    // Definition patterns (enhanced)
    definition: /["']([^"']+)["']\s+(?:shall\s+)?(?:be\s+)?(?:defined\s+(?:as|to\s+mean)|means?|refers?\s+to)\s+(.+?)(?=\.|$)/gim,
    defined_term: /(?:the\s+(?:term\s+)?|)["']([^"']+)["']\s+(?:is\s+defined\s+to\s+mean|shall\s+have\s+the\s+meaning|means|refers\s+to)\s+(.+?)(?=\.|$)/gim,
    interpretation: /(?:For\s+(?:the\s+)?purposes?\s+of\s+this\s+[^,]+,\s+|As\s+used\s+in\s+this\s+[^,]+,\s+)["']([^"']+)["']\s+(?:shall\s+)?means?\s+(.+?)(?=\.|$)/gim,
    
    // Contract formation patterns (expanded)
    recital: /(?:WHEREAS|RECITALS?)[,:]?\s+(.+?)(?=\n|$)/gim,
    preamble: /(?:THIS\s+(?:AGREEMENT|CONTRACT|DEED|AMENDMENT|LEASE)|AGREEMENT\s+made)\s+(?:is\s+made|entered\s+into|dated|made\s+(?:and|&)\s+entered\s+into|made\s+as\s+of)\s+(.+?)(?=\n|$)/gim,
    effective_date: /(?:effective\s+(?:as\s+of\s+|date\s*[:\s]|on\s+)|commencement\s+date|starts?\s+on|begins?\s+on)\s*(?:of\s+|is\s+|:|,\s+)?([A-Za-z]+\s+\d{1,2}(?:st|nd|rd|th)?\s*,?\s*\d{4}|\d{1,2}(?:st|nd|rd|th)?\s+(?:of\s+)?[A-Za-z]+\s*,?\s*\d{4}|\d{1,2}[\/-]\d{1,2}[\/-]\d{2,4})/gim,
    parties_agreement: /(?:by\s+and\s+between|among|between)\s+(.+?)\s+(?:\(["'][\w\s]+["']\))?(?:\s*,\s*(?:a|an)\s+([^,]+))?\s+(?:and|&)\s+(.+?)\s+(?:\(["'][\w\s]+["']\))?(?:\s*,\s*(?:a|an)\s+([^,]+))?/gim,
    
    // Party and signatory patterns (enhanced)
    parties: /(?:between|among)\s+([^,]+)\s*,\s*(?:a|an)\s+([^,]+(?:corporation|company|partnership|LLC|organization|entity)?)\s*,\s*(?:and|&)\s+([^,]+)\s*,\s*(?:a|an)\s+([^,]+(?:corporation|company|partnership|LLC|organization|entity)?)/gim,
    signatory: /(?:IN\s+WITNESS\s+WHEREOF|EXECUTED(?:\s+AS\s+A\s+DEED)?\s+by|SIGNED(?:\s+AS\s+A\s+DEED)?\s+by|EXECUTED\s+AND\s+DELIVERED\s+by)\s+(.+?)(?=\n|$)/gim,
    authorized_representative: /(?:By|Signature):\s*[_]+\s*(?:Name|Print):\s*([^_\n]+)\s*Title:\s*([^_\n]+)/gim,
    
    // Rights and obligations patterns (refined)
    warranty: /(?:warrants?|WARRANTS?|represents?\s+and\s+warrants?)\s+(?:that|to|and\s+agrees?\s+that)\s+(.+?)(?=\.|$)/gim,
    indemnity: /(?:(?:shall\s+)?indemnify|INDEMNIFY|indemnification|hold\s+harmless|defend(?:\s+and\s+indemnify)?)\s+(.+?)(?=\.|$)/gim,
    obligation: /(?:shall|must|agrees?\s+to|is\s+obligated\s+to|undertakes?\s+to|is\s+required\s+to|will|covenants?\s+to)\s+(.+?)(?=\.|$)/gim,
    representation: /(?:represents?|REPRESENTS?|acknowledges?\s+and\s+represents?)\s+(?:that|to|and\s+warrants?\s+that)\s+(.+?)(?=\.|$)/gim,
    covenant: /(?:covenants?|COVENANTS?|agrees?\s+and\s+covenants?)\s+(?:that|to|and\s+agrees?\s+that)\s+(.+?)(?=\.|$)/gim,
    right: /(?:has?\s+the\s+right|shall\s+have\s+the\s+right|is\s+entitled|may\s+at\s+its\s+(?:sole\s+)?(?:discretion|option))\s+(?:to|that)\s+(.+?)(?=\.|$)/gim,
    
    // Contract management patterns (expanded)
    termination: /(?:(?:right\s+to\s+)?terminat(?:e|ion)|TERMINAT(?:E|ION)|cancellation|right\s+to\s+cancel)\s+(?:of|this|the)\s+(.+?)(?=\.|$)/gim,
    amendment: /(?:(?:right\s+to\s+)?amend(?:ment|ed)?|AMEND(?:MENT|ED)|modif(?:y|ication)|MODIF(?:Y|ICATION))\s+(?:of|this|the)\s+(.+?)(?=\.|$)/gim,
    assignment: /(?:(?:right\s+to\s+)?assign(?:ment|ed)?|ASSIGN(?:MENT|ED)|transfer(?:red)?)\s+(?:of|this|the)\s+(.+?)(?=\.|$)/gim,
    waiver: /(?:waiv(?:e|er)|WAIV(?:E|ER)|no\s+waiver)\s+(?:of|this|the)\s+(.+?)(?=\.|$)/gim,
    survival: /(?:survival|SURVIVAL|shall\s+survive)\s+(?:of|this|the)\s+(.+?)(?=\.|$)/gim,
    
    // Legal protection patterns (expanded)
    confidentiality: /(?:confidential(?:ity)?|CONFIDENTIAL(?:ITY)?|non-disclosure|proprietary\s+information|trade\s+secrets?)\s+(.+?)(?=\.|$)/gim,
    intellectual_property: /(?:intellectual\s+property|INTELLECTUAL\s+PROPERTY|IP|copyright(?:able)?|patent(?:able)?|trademark|trade\s+secret)\s+(?:rights?|ownership|title|interests?)\s+(.+?)(?=\.|$)/gim,
    limitation_of_liability: /(?:limit(?:ation|ed)\s+(?:of|on)\s+liability|LIMIT(?:ATION|ED)\s+(?:OF|ON)\s+LIABILITY|cap\s+on\s+damages|maximum\s+liability|liability\s+cap)\s+(.+?)(?=\.|$)/gim,
    force_majeure: /(?:force\s+majeure|FORCE\s+MAJEURE|Act(?:s)?\s+of\s+God|ACT(?:S)?\s+OF\s+GOD|events?\s+beyond\s+(?:the\s+)?(?:reasonable\s+)?control)\s+(.+?)(?=\.|$)/gim,
    insurance: /(?:insurance|INSURANCE|insured|INSURED)\s+(?:requirements?|obligations?|coverage|policies?|limits?)\s+(.+?)(?=\.|$)/gim,
    
    // Dispute resolution patterns (enhanced)
    governing_law: /(?:govern(?:ed|ing)\s+(?:by\s+)?(?:the\s+)?laws?\s+of|GOVERN(?:ED|ING)\s+(?:BY\s+)?(?:THE\s+)?LAWS?\s+OF|law\s+of)\s+(.+?)(?=\.|$)/gim,
    jurisdiction: /(?:jurisdiction|JURISDICTION|venue|VENUE|forum|FORUM)\s+(?:shall\s+be\s+|will\s+be\s+|is\s+|in)\s+(.+?)(?=\.|$)/gim,
    arbitration: /(?:arbitrat(?:e|ion|ed)|ARBITRAT(?:E|ION|ED)|arbitral\s+proceedings?)\s+(?:shall\s+be\s+|will\s+be\s+|in)\s+(.+?)(?=\.|$)/gim,
    dispute_resolution: /(?:disput(?:e|es)|DISPUT(?:E|ES)|controvers(?:y|ies)|CONTROVERS(?:Y|IES)|claims?|disagreements?)\s+(?:shall|will|must|are\s+to)\s+(?:be|first\s+be)\s+(?:resolved|settled|determined|decided)\s+(?:by|through|via)\s+(.+?)(?=\.|$)/gim,
    mediation: /(?:mediat(?:e|ion)|MEDIAT(?:E|ION)|settlement\s+conference)\s+(?:shall|will|must|before)\s+(.+?)(?=\.|$)/gim,
    
    // Compliance and regulatory patterns (expanded)
    severability: /(?:any\s+)?(?:provision|term|section|part|clause)\s+(?:becomes|is|shall\s+be)\s+(?:invalid|void|unenforceable)\s*,?\s*(?:the|such|said)\s+(.+?)(?=\.|$)/gim,
    statute_reference: /(?:\d+\s+U\.S\.C\.|Section\s+\d+\s+of\s+the|[A-Z][a-z]+\s+Act\s+of\s+\d{4}|Public\s+Law\s+\d+-\d+)\s+(.+?)(?=\.|$)/gm,
    compliance: /(?:the\s+)?parties\s+shall\s+(?:comply|conform|adhere)\s+(?:with|to)\s+(.+?)(?=\.|$)/gim,
    regulatory: /(?:regulat(?:ory|ion)|REGULAT(?:ORY|ION)|law(?:s)?|LAW(?:S)?|rule(?:s)?|RULE(?:S)?)\s+(?:of|by|under|promulgated\s+by)\s+(.+?)(?=\.|$)/gim,
    permits_licenses: /(?:all\s+required\s+)?(?:permit(?:s)?(?:\s+and\s+|,\s+|\s+)?licens(?:e|es)|licens(?:e|es)(?:\s+and\s+|,\s+|\s+)?permit(?:s)?)\s+(?:must|shall)\s+be\s+maintained(?:\s+)?(.+?)(?:\.|$)/gim,
  };

  /**
   * Analyze document for legal patterns using both regex-based detection and AI-enhanced analysis
   * @param text The document text to analyze
   * @param documentId The ID of the document being analyzed
   * @param query A user query for specific patterns
   * @returns Pattern recognition result with identified patterns
   */
  async analyzeDocument(
    text: string,
    documentId: string,
    query: string,
    mockPatterns?: LegalPattern[],
  ): Promise<PatternRecognitionResult> {
    this.logger.log(`Starting pattern recognition for document ${documentId}`);
    
    // 1. Detect simple patterns first
    const simplePatterns = await this.detectRegexPatterns(text);
    this.logger.log(`Found ${simplePatterns.length} simple patterns initially.`);

    let allPatterns: LegalPattern[] = [...simplePatterns]; // Start with simple patterns
    let aiPatternsFound = 0; // Counter for AI patterns

    // 2. Attempt to detect AI patterns
    try {
      const aiPatterns = await this.detectPatternsAI(text, documentId, query);
      // Only merge if AI patterns are valid and is an array
      if (aiPatterns && Array.isArray(aiPatterns)) {
        allPatterns.push(...aiPatterns);
        aiPatternsFound = aiPatterns.length;
        this.logger.log(`Successfully merged ${aiPatternsFound} AI patterns.`);
      } else {
        // Log if AI detection resulted in null or non-array (already logged in detectAIPatterns)
        this.logger.warn(`AI pattern detection did not return a valid array for document ${documentId}.`);
      }
    } catch (error) {
      this.logger.error(`Error during AI pattern detection for document ${documentId}: ${error.message}`, error.stack);
      // Keep simple patterns even if AI fails
    }

    // 3. Log the final combined count and return
    this.logger.log(
      `Found a total of ${allPatterns.length} patterns (${simplePatterns.length} simple + ${aiPatternsFound} AI) in document ${documentId}`,
    );
    return {
      patterns: allPatterns.sort((a, b) => a.startIndex - b.startIndex),
      documentId,
      timestamp: new Date(),
    };
  }

  /**
   * Detect specific legal patterns in a document using AI, based on a user query.
   * @param text The document content.
   * @param documentId The ID of the document.
   * @param query A natural language query describing the patterns to find.
   * @returns A promise resolving to an array of detected legal patterns.
   */
  async detectPatternsAI(
    text: string,
    documentId: string,
    query: string,
  ): Promise<LegalPattern[]> {
    this.logger.log(
      `Starting AI pattern detection for document ${documentId} with query: "${query}"`, // Log the query
    );
    try {
      // Directly call the modified detectAIPatterns without chunking logic visible here
      const patterns = await this.detectAIPatterns(text, documentId, query);
      this.logger.log(`Detected ${patterns.length} patterns via AI for document ${documentId}.`);
      return patterns;
    } catch (error) {
      this.logger.error(`AI pattern detection failed for document ${documentId}: ${error.message}`, error.stack);
      // Rethrow or handle specific errors as needed
      // Currently, letting the error propagate up might be suitable, caught by detectPatternsAI
      throw new InternalServerErrorException(`Failed to detect patterns using AI for document ${documentId}: ${error.message}`);
    }
  }

  /**
   * Uses AI to detect legal patterns based on a query within the provided text.
   * Processes the entire text in one go.
   * @param text The full document text.
   * @param documentId The ID of the document for context.
   * @param query The user's natural language query specifying patterns to find.
   * @returns A promise resolving to an array of detected LegalPattern objects.
   */
  private async detectAIPatterns(
    text: string,
    documentId: string,
    query: string, // Add query parameter
  ): Promise<LegalPattern[]> {
    this.logger.log(`[detectAIPatterns] Processing document ${documentId}, Query: "${query}". Text length: ${text.length}`);

    // Log the input document text (or its length/start if too long)
    this.logger.debug(`Document Text length: ${text.length}`);
    if (text.length > 500) { // Log only the beginning if very long
        this.logger.debug(`Document Text (start): ${text.substring(0, 50)}...`);
    } else {
        this.logger.debug(`Document Text: ${text}`);
    }

    // Construct the prompt for the AI
    // Use the system prompt from configuration if available/applicable
    // Include the user's query and instructions for the format
    const prompt = `
    Analyze the following legal document text and identify all occurrences of patterns matching the user's request.
    User Request: "${query}"

    Document Text:
    --- Start of Document Text ---
    ${text}
    --- End of Document Text ---

    Instructions:
    1. Carefully read the User Request to understand the specific legal patterns, clauses, or information being sought.
    2. Scan the entire Document Text for segments that directly match or are highly relevant to the User Request.
    3. For each match found, extract the relevant information.
    4. Format the results as a JSON array of objects. Each object must represent one found pattern and strictly adhere to the following structure:
       {
         "pattern_name": "<Concise name for the pattern found, reflecting the User Request (e.g., 'Indemnification Clause', 'Governing Law Provision')>",
         "text_snippet": "<The exact text segment from the document that represents the pattern.>",
         "explanation": "<A brief explanation of why this text snippet matches the User Request.>",
         "start_char": <The starting character index of the text_snippet within the original Document Text (approximate if necessary)>, 
         "end_char": <The ending character index of the text_snippet within the original Document Text (approximate if necessary)>
       }
    5. If no relevant patterns are found matching the User Request, return an empty JSON array: []
    6. Ensure the 'text_snippet' is an accurate and complete representation of the found pattern.
    7. Provide accurate 'start_char' and 'end_char' if possible; otherwise, use estimates or omit.
    8. Ensure the final output is ONLY the JSON array, with no introductory text, explanations, or markdown formatting outside the JSON structure.
    `;

    this.logger.debug(`Generated AI Prompt: ${prompt}`);

    let detectedPatterns: LegalPattern[] = [];

    try {
      this.logger.log(`Sending single request to AI service for document ${documentId}. Prompt length (approx): ${prompt.length}`);
      // Use appropriate AI service options if needed (e.g., model selection, temperature)
      const aiResponse = await this.aiService.generateResponse(prompt, { documentId, analysisType: 'pattern_detection', query });
      this.logger.log(`Received AI response for document ${documentId}.`);

      this.logger.debug(`Raw AI Response: ${JSON.stringify(aiResponse)}`); // Log raw response

      // --- Start: Clean and parse AI response --- 
      let jsonString = '';
      if (aiResponse && typeof aiResponse === 'string') {
        const trimmedResponse = aiResponse.trim();
        // Check for ```json ... ``` fences and extract content
        if (trimmedResponse.startsWith('```json') && trimmedResponse.endsWith('```')) {
          jsonString = trimmedResponse.substring(7, trimmedResponse.length - 3).trim();
          this.logger.debug('Extracted JSON string from Markdown fences.');
        } else {
          jsonString = trimmedResponse; // Assume it might be clean JSON already
        }
      } else {
        this.logger.warn(`Received unexpected AI response type or null/undefined for document ${documentId}. Response: ${aiResponse}`);
        return []; // Cannot parse non-string or empty response
      }
      
      if (!jsonString) {
          this.logger.warn(`AI response for document ${documentId} was empty after trimming/fence removal.`);
          return [];
      }

      // Now attempt to parse the cleaned jsonString
      try {
        const parsedResponse = JSON.parse(jsonString);
        this.logger.debug(`Successfully parsed AI response for document ${documentId}.`);

        if (Array.isArray(parsedResponse)) {
          // Validate structure (basic example)
          detectedPatterns = parsedResponse.filter(item => 
            item && typeof item.pattern_name === 'string' && typeof item.text_snippet === 'string'
          ).map(item => ({ // Ensure DTO structure
            pattern_type: item.pattern_name, // Map field names if needed
            snippet: item.text_snippet,
            explanation: item.explanation ?? '', // Handle potentially missing fields
            page_number: item.page_number ?? null,
            confidence_score: item.confidence_score ?? null,
            // Assuming start_char/end_char are not part of LegalPatternDto directly
          }));
          this.logger.log(`Validated ${detectedPatterns.length} patterns from AI response for document ${documentId}.`);
        } else {
          this.logger.warn(`Parsed AI response for document ${documentId} is not an array. Parsed data: ${JSON.stringify(parsedResponse)}`);
          // Handle non-array JSON if necessary, otherwise return empty
          detectedPatterns = [];
        }
      } catch (error) {
        this.logger.error(`Failed to parse cleaned JSON response for document ${documentId}: ${error.message}`, error.stack);
        this.logger.error(`Cleaned JSON string causing error: ${jsonString}`);
        detectedPatterns = []; // Return empty on parse failure
      }
      // --- End: Clean and parse AI response ---

    } catch (error) {
      this.logger.error(`Error during AI interaction for document ${documentId}: ${error.message}`, error.stack);
      // Decide how to handle AI errors - potentially return empty or throw
      // Currently, letting the error propagate up might be suitable, caught by detectPatternsAI
      throw error; // Re-throw the error to be caught by the caller
    }

    this.logger.log(`[detectAIPatterns] Completed for document ${documentId}. Found ${detectedPatterns.length} patterns.`);
    return detectedPatterns;
  }

  /**
   * Detect patterns using regex-based approach
   * @param text Document text
   * @returns Array of legal patterns
   */
  private async detectRegexPatterns(text: string): Promise<LegalPattern[]> {
    const patterns: LegalPattern[] = [];

    // Process each pattern type
    for (const [type, regex] of Object.entries(this.patterns)) {
      let match;
      while ((match = regex.exec(text)) !== null) {
        const pattern: LegalPattern = {
          type,
          content: match[0],
          startIndex: match.index,
          endIndex: match.index + match[0].length,
          metadata: {},
        };

        // Add pattern-specific metadata
        switch (type) {
          case 'section':
          case 'clause':
          case 'article':
          case 'subsection':
          case 'exhibit':
          case 'schedule':
            pattern.metadata.number = match[1];
            pattern.metadata.title = match[2] || '';
            break;
            
          case 'definition':
          case 'defined_term':
          case 'interpretation':
            pattern.metadata.term = match[1];
            pattern.metadata.meaning = match[2];
            break;
            
          case 'preamble':
          case 'recital':
          case 'effective_date':
            pattern.metadata.content = match[1];
            pattern.metadata.date = match[1].match(/\d{4}/) ? match[1] : null;
            break;
            
          case 'parties':
          case 'parties_agreement':
            pattern.metadata.party1 = match[1];
            pattern.metadata.party1Type = match[2] || null;
            pattern.metadata.party2 = match[3];
            pattern.metadata.party2Type = match[4] || null;
            break;
            
          case 'authorized_representative':
            pattern.metadata.name = match[1];
            pattern.metadata.title = match[2];
            break;
            
          case 'warranty':
          case 'indemnity':
          case 'obligation':
          case 'right':
          case 'representation':
          case 'covenant':
            pattern.metadata.content = match[1];
            pattern.metadata.actor = match[0].match(/^([^\\s]+)/) ? match[0].match(/^([^\\s]+)/)[1] : null;
            break;
            
          case 'termination':
          case 'amendment':
          case 'assignment':
          case 'waiver':
          case 'survival':
            pattern.metadata.content = match[1];
            pattern.metadata.subject = match[0].match(/of\s+([^\\s]+)/) ? match[0].match(/of\s+([^\\s]+)/)[1] : null;
            break;
            
          case 'confidentiality':
          case 'intellectual_property':
          case 'limitation_of_liability':
          case 'force_majeure':
          case 'insurance':
            pattern.metadata.content = match[1];
            pattern.metadata.type = type;
            break;
            
          case 'governing_law':
          case 'jurisdiction':
          case 'arbitration':
          case 'mediation':
            pattern.metadata.content = match[1];
            pattern.metadata.jurisdiction = match[1].match(/([A-Za-z ]+)(?:,|$)/) ? match[1].match(/([A-Za-z ]+)(?:,|$)/)[1].trim() : null;
            break;
            
          case 'dispute_resolution':
            pattern.metadata.content = match[1];
            pattern.metadata.method = match[0].match(/(?:by|through|via)\s+([^\\s,]+)/) ? match[0].match(/(?:by|through|via)\s+([^\\s,]+)/)[1] : null;
            break;
            
          case 'severability':
          case 'compliance':
          case 'regulatory':
          case 'permits_licenses':
          case 'statute_reference':
            pattern.metadata.content = match[1];
            break;
        }

        patterns.push(pattern);
      }

      // Reset regex lastIndex for next use
      regex.lastIndex = 0;
    }

    return patterns;
  }

  /**
   * Extract specific pattern type from a recognition result
   */
  getPatternsByType(
    result: PatternRecognitionResult,
    type: string,
  ): LegalPattern[] {
    return result.patterns.filter((pattern) => pattern.type === type);
  }

  /**
   * Get pattern statistics from a recognition result
   */
  getPatternStatistics(result: PatternRecognitionResult): {
    totalPatterns: number;
    typeDistribution: Record<string, number>;
  } {
    const typeDistribution: Record<string, number> = {};

    for (const pattern of result.patterns) {
      typeDistribution[pattern.type] =
        (typeDistribution[pattern.type] || 0) + 1;
    }

    return {
      totalPatterns: result.patterns.length,
      typeDistribution,
    };
  }

  /**
   * Check if a pattern is a duplicate of an existing pattern
   */
  private isDuplicatePattern(
    pattern: LegalPattern,
    existingPatterns: LegalPattern[],
  ): boolean {
    for (const existing of existingPatterns) {
      // Check for content overlap
      if (
        pattern.type === existing.type &&
        (pattern.content.includes(existing.content) ||
          existing.content.includes(pattern.content) ||
          this.calculateStringSimilarity(pattern.content, existing.content) >
            0.7)
      ) {
        return true;
      }
    }
    return false;
  }

  /**
   * Calculate similarity between two strings (simple Levenshtein-based approach)
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) {
      return 1.0;
    }

    // Calculate Levenshtein distance
    const costs = new Array(shorter.length + 1);
    for (let i = 0; i <= shorter.length; i++) {
      costs[i] = i;
    }

    for (let i = 1; i <= longer.length; i++) {
      costs[0] = i;
      let nw = i - 1;
      for (let j = 1; j <= shorter.length; j++) {
        const cj = Math.min(
          costs[j] + 1,
          costs[j - 1] + 1,
          nw + (longer.charAt(i - 1) === shorter.charAt(j - 1) ? 0 : 1),
        );
        nw = costs[j];
        costs[j] = cj;
      }
    }

    return (longer.length - costs[shorter.length]) / longer.length;
  }

  async validatePattern(pattern: LegalPattern): Promise<boolean> {
    // Basic validation of pattern structure
    if (
      !pattern.type ||
      !pattern.content ||
      typeof pattern.startIndex !== 'number' ||
      typeof pattern.endIndex !== 'number'
    ) {
      return false;
    }

    // Ensure indexes make sense
    if (pattern.startIndex < 0 || pattern.endIndex <= pattern.startIndex) {
      return false;
    }

    // Validate pattern-specific metadata based on type
    switch (pattern.type) {
      case 'section':
      case 'clause':
      case 'exhibit':
        return !!(pattern.metadata?.number && pattern.metadata?.title);
      case 'definition':
        return !!(pattern.metadata?.term && pattern.metadata?.meaning);
      case 'recital':
      case 'warranty':
      case 'indemnity':
      case 'obligation':
      case 'representation':
      case 'termination':
      case 'confidentiality':
      case 'governing_law':
      case 'severability':
      case 'statute_reference':
        return !!pattern.metadata?.content;
      // Allow other types from AI recognition
      default:
        return !!pattern.content;
    }
  }

  /**
   * Extract document sections from content
   * @param content The document content to extract sections from
   * @returns Array of document sections
   */
  async extractSections(content: string): Promise<DocumentSection[]> {
    this.logger.log('Extracting sections from document content');
    
    if (!content || content.trim().length === 0) {
      this.logger.warn('Cannot extract sections from empty content');
      return [];
    }
    
    try {
      const sections: DocumentSection[] = [];
      
      // First try to extract sections using regex patterns
      const sectionMatches = Array.from(content.matchAll(this.patterns.section));
      const articleMatches = Array.from(content.matchAll(this.patterns.article));
      const clauseMatches = Array.from(content.matchAll(this.patterns.clause));
      
      // Process section matches
      for (const match of sectionMatches) {
        if (match[1] && match[2]) {
          const sectionNumber = match[1].trim();
          const sectionTitle = match[2].trim();
          
          // Find the content of this section (text until the next section)
          const startIndex = match.index + match[0].length;
          let endIndex = content.length;
          
          // Find the next section/article/clause
          const nextSectionMatch = sectionMatches.find(m => m.index > startIndex);
          const nextArticleMatch = articleMatches.find(m => m.index > startIndex);
          const nextClauseMatch = clauseMatches.find(m => m.index > startIndex);
          
          // Find the closest next section/article/clause
          if (nextSectionMatch) endIndex = Math.min(endIndex, nextSectionMatch.index);
          if (nextArticleMatch) endIndex = Math.min(endIndex, nextArticleMatch.index);
          if (nextClauseMatch) endIndex = Math.min(endIndex, nextClauseMatch.index);
          
          // Extract the section content
          const sectionContent = content.substring(startIndex, endIndex).trim();
          
          sections.push({
            title: `Section ${sectionNumber}: ${sectionTitle}`,
            content: sectionContent,
            purpose: this.inferSectionPurpose(sectionTitle, sectionContent),
          });
        }
      }
      
      // Process article matches
      for (const match of articleMatches) {
        if (match[1] && match[2]) {
          const articleNumber = match[1].trim();
          const articleTitle = match[2].trim();
          
          // Find the content of this article (text until the next article)
          const startIndex = match.index + match[0].length;
          let endIndex = content.length;
          
          // Find the next section/article/clause
          const nextSectionMatch = sectionMatches.find(m => m.index > startIndex);
          const nextArticleMatch = articleMatches.find(m => m.index > startIndex);
          const nextClauseMatch = clauseMatches.find(m => m.index > startIndex);
          
          // Find the closest next section/article/clause
          if (nextSectionMatch) endIndex = Math.min(endIndex, nextSectionMatch.index);
          if (nextArticleMatch) endIndex = Math.min(endIndex, nextArticleMatch.index);
          if (nextClauseMatch) endIndex = Math.min(endIndex, nextClauseMatch.index);
          
          // Extract the article content
          const articleContent = content.substring(startIndex, endIndex).trim();
          
          sections.push({
            title: `Article ${articleNumber}: ${articleTitle}`,
            content: articleContent,
            purpose: this.inferSectionPurpose(articleTitle, articleContent),
          });
        }
      }
      
      // If no sections found using regex, try to use AI to extract sections
      if (sections.length === 0) {
        try {
          const aiSections = await this.extractSectionsAI(content);
          if (aiSections && aiSections.length > 0) {
            sections.push(...aiSections);
          }
        } catch (error) {
          this.logger.warn(`Error extracting sections using AI: ${error.message}`);
        }
      }
      
      // If still no sections, create a single section for the entire document
      if (sections.length === 0) {
        sections.push({
          title: 'Document',
          content: content,
          purpose: 'Main document content',
        });
      }
      
      this.logger.log(`Extracted ${sections.length} sections from document content`);
      return sections;
    } catch (error) {
      this.logger.error(`Error extracting sections: ${error.message}`, error.stack);
      // Return a single section with the entire content in case of error
      return [{
        title: 'Document',
        content: content,
        purpose: 'Main document content',
      }];
    }
  }
  
  /**
   * Use AI to extract sections from document content
   * @param content The document content to extract sections from
   * @returns Array of document sections
   */
  private async extractSectionsAI(content: string): Promise<DocumentSection[]> {
    this.logger.log('Using AI to extract sections from document content');
    
    try {
      const prompt = `
        Analyze the following legal document and extract its main sections.
        For each section, provide:
        1. The section title
        2. The section content
        3. The purpose of the section (e.g., "Defines terms", "Sets payment terms", etc.)
        
        Format the response as a JSON array of objects with the following structure:
        [
          {
            "title": "Section title",
            "content": "Section content",
            "purpose": "Brief description of the section's purpose"
          }
        ]
        
        Document content:
        """
        ${content}
        """
      `;
      
      const response = await this.aiService.generateResponse(prompt);
      
      try {
        const sections = JSON.parse(response);
        if (Array.isArray(sections)) {
          this.logger.log(`Successfully extracted ${sections.length} sections using AI`);
          return sections;
        } else {
          this.logger.warn('AI response was not a valid array');
          return [];
        }
      } catch (error) {
        this.logger.warn(`Failed to parse AI response as JSON: ${error.message}`);
        return [];
      }
    } catch (error) {
      this.logger.error(`Error extracting sections using AI: ${error.message}`, error.stack);
      return [];
    }
  }
  
  /**
   * Infer the purpose of a section based on its title and content
   * @param title The section title
   * @param content The section content
   * @returns A brief description of the section's purpose
   */
  private inferSectionPurpose(title: string, content: string): string {
    // Simple inference based on keywords in title and content
    const titleLower = title.toLowerCase();
    const contentLower = content.toLowerCase().substring(0, 500); // Only check first 500 chars
    
    if (titleLower.includes('defin') || contentLower.includes('means') || contentLower.includes('shall mean')) {
      return 'Defines terms used in the document';
    } else if (titleLower.includes('payment') || contentLower.includes('pay') || contentLower.includes('fee')) {
      return 'Sets payment terms and conditions';
    } else if (titleLower.includes('term') || contentLower.includes('duration') || contentLower.includes('period')) {
      return 'Establishes the duration of the agreement';
    } else if (titleLower.includes('terminat') || contentLower.includes('cancel')) {
      return 'Outlines termination conditions';
    } else if (titleLower.includes('confiden') || contentLower.includes('disclos')) {
      return 'Addresses confidentiality requirements';
    } else if (titleLower.includes('warrant') || contentLower.includes('represent')) {
      return 'Contains warranties or representations';
    } else if (titleLower.includes('indemn') || contentLower.includes('hold harmless')) {
      return 'Provides indemnification provisions';
    } else if (titleLower.includes('govern') || contentLower.includes('law') || contentLower.includes('jurisdict')) {
      return 'Specifies governing law and jurisdiction';
    } else if (titleLower.includes('dispute') || contentLower.includes('arbitrat') || contentLower.includes('mediat')) {
      return 'Outlines dispute resolution procedures';
    } else {
      return 'General provisions';
    }
  }
}

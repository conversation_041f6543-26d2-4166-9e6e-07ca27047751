import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { Readable } from 'stream';
import * as crypto from 'crypto';

@Injectable()
export class CloudflareStorageService {
  private readonly logger = new Logger(CloudflareStorageService.name);
  private readonly s3Client: S3Client;
  private readonly bucketName: string;
  private readonly publicBaseUrl: string;

  constructor(private readonly configService: ConfigService) {
    // Get Cloudflare R2 configuration
    const endpoint = this.configService.get<string>('app.storage.cloudflare.endpoint');
    const accessKeyId = this.configService.get<string>('app.storage.cloudflare.accessKeyId');
    const secretAccessKey = this.configService.get<string>('app.storage.cloudflare.secretAccessKey');
    this.bucketName = this.configService.get<string>('app.storage.cloudflare.bucketName');
    this.publicBaseUrl = this.configService.get<string>('app.storage.cloudflare.publicUrl');
    
    // Log configuration for debugging
    this.logger.log(`CloudflareStorageService configuration:`);
    this.logger.log(`Endpoint: ${endpoint}`);
    this.logger.log(`Access Key ID: ${accessKeyId ? '****' + accessKeyId.substring(accessKeyId.length - 4) : 'undefined'}`);
    this.logger.log(`Secret Access Key: ${secretAccessKey ? '****' : 'undefined'}`);
    this.logger.log(`Bucket Name: ${this.bucketName}`);
    this.logger.log(`Public Base URL: ${this.publicBaseUrl}`);
    
    // Initialize S3 client with Cloudflare R2 credentials
    this.s3Client = new S3Client({
      region: 'auto',
      endpoint: endpoint,
      credentials: {
        accessKeyId: accessKeyId,
        secretAccessKey: secretAccessKey,
      },
    });
    
    this.logger.log(`CloudflareStorageService initialized with bucket: ${this.bucketName}`);
  }

  /**
   * Uploads a file to Cloudflare R2 storage
   * @param file The file to upload
   * @param organizationId The organization ID for tenant isolation
   * @returns The key of the uploaded file in R2 and its public URL
   */
  async uploadFile(file: Express.Multer.File, organizationId: string): Promise<{ key: string; url: string }> {
    try {
      if (!this.bucketName) {
        throw new Error('Bucket name is not configured. Please check your environment variables.');
      }
      
      // Generate a unique key for the file
      const timestamp = Date.now();
      const randomString = crypto.randomBytes(8).toString('hex');
      const extension = file.originalname.split('.').pop();
      const key = `${organizationId}/${timestamp}-${randomString}.${extension}`;

      // Create buffer from file
      const fileBuffer = file.buffer || await this.readFileToBuffer(file.path);

      // Upload to R2
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: fileBuffer,
        ContentType: file.mimetype,
        Metadata: {
          originalName: file.originalname,
          organizationId: organizationId,
        },
      });

      await this.s3Client.send(command);
      
      // Generate public URL
      const url = `${this.publicBaseUrl}/${key}`;
      
      this.logger.log(`Successfully uploaded file to R2: ${key}`);
      return { key, url };
    } catch (error) {
      this.logger.error(`Error uploading file to R2: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Gets a file from Cloudflare R2 storage
   * @param key The key of the file in R2
   * @returns The file content as a buffer
   */
  async getFile(key: string): Promise<Buffer> {
    try {
      if (!this.bucketName) {
        throw new Error('Bucket name is not configured. Please check your environment variables.');
      }
      
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      const response = await this.s3Client.send(command);
      
      // Convert stream to buffer
      if (response.Body instanceof Readable) {
        return await this.streamToBuffer(response.Body as Readable);
      } else {
        throw new Error('Response body is not a readable stream');
      }
    } catch (error) {
      this.logger.error(`Error getting file from R2: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Deletes a file from Cloudflare R2 storage
   * @param key The key of the file in R2
   * @returns True if deletion was successful
   */
  async deleteFile(key: string): Promise<boolean> {
    try {
      if (!this.bucketName) {
        throw new Error('Bucket name is not configured. Please check your environment variables.');
      }
      
      const command = new DeleteObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      await this.s3Client.send(command);
      this.logger.log(`Successfully deleted file from R2: ${key}`);
      return true;
    } catch (error) {
      this.logger.error(`Error deleting file from R2: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Generates a presigned URL for a file in R2
   * @param key The key of the file in R2
   * @param expiresIn Expiration time in seconds (default: 3600)
   * @returns The presigned URL
   */
  async getPresignedUrl(key: string, expiresIn = 3600): Promise<string> {
    try {
      if (!this.bucketName) {
        throw new Error('Bucket name is not configured. Please check your environment variables.');
      }
      
      const command = new GetObjectCommand({
        Bucket: this.bucketName,
        Key: key,
      });

      return await getSignedUrl(this.s3Client, command, { expiresIn });
    } catch (error) {
      this.logger.error(`Error generating presigned URL: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Utility method to read a file to a buffer
   */
  private async readFileToBuffer(filePath: string): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      const fs = require('fs');
      fs.readFile(filePath, (err, data) => {
        if (err) {
          reject(err);
        } else {
          resolve(data);
        }
      });
    });
  }

  /**
   * Utility method to convert a stream to a buffer
   */
  private async streamToBuffer(stream: Readable): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      const chunks: Buffer[] = [];
      stream.on('data', (chunk) => chunks.push(Buffer.from(chunk)));
      stream.on('error', reject);
      stream.on('end', () => resolve(Buffer.concat(chunks)));
    });
  }
}

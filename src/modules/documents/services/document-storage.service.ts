import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  Document,
  DOCUMENT_MODEL
} from '../schemas/document.schema';
import { // Added imports for versioning
  DocumentVersion,
  DOCUMENT_VERSION_MODEL
} from '../schemas/document-version.schema';

@Injectable()
export class DocumentStorageService {
  private readonly logger = new Logger(DocumentStorageService.name);

  constructor(
    @InjectModel(DOCUMENT_MODEL) private documentModel: Model<Document>,
    @InjectModel(DOCUMENT_VERSION_MODEL) private documentVersionModel: Model<DocumentVersion>, // Injected version model
  ) {}

  async getDocumentById(id: string): Promise<Document | null> {
    console.log('Storage service - Getting document by ID:', id);
    try {
      // Explicitly select the content field along with others
      const doc = await this.documentModel.findOne({ id }).select('+content').exec();
      // console.log('Storage service - Found document:', doc);
      return doc;
    } catch (error) {
      this.logger.error(`Error getting document ${id}:`, error);
      return null;
    }
  }

  async getDocumentMetadata(id: string): Promise<Document | null> {
    try {
      // Get document without content for faster queries
      const doc = await this.documentModel.findOne({ id }).select('-content').exec();
      return doc;
    } catch (error) {
      this.logger.error(`Error getting document metadata ${id}:`, error);
      return null;
    }
  }

  async getAllDocuments(options?: {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortDirection?: 'asc' | 'desc';
  }): Promise<{ documents: Document[]; total: number; totalPages: number }> {
    try {
      const page = options?.page || 1;
      const limit = options?.limit || 10;
      const skip = (page - 1) * limit;
      const sortBy = options?.sortBy || 'uploadDate';
      const sortDirection = options?.sortDirection || 'desc';

      // Create sort object with proper type for Mongoose
      const sort: Record<string, 1 | -1> = { 
        [sortBy]: sortDirection === 'asc' ? 1 : -1 
      };

      // Get total count for pagination metadata
      const total = await this.documentModel.countDocuments().exec();
      const totalPages = Math.ceil(total / limit);

      // Get paginated documents
      const documents = await this.documentModel
        .find()
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec();

      return {
        documents,
        total,
        totalPages,
      };
    } catch (error) {
      this.logger.error('Error getting all documents:', error);
      throw error;
    }
  }

  async saveDocument(document: Partial<Document>): Promise<Document> {
    try {
      // If id exists, update the document
      if (document.id) {
        this.logger.log(`Updating document: ${document.id}`);
        // Fetch the current document state *including content* before update
        const currentDoc = await this.documentModel.findOne({ id: document.id }).select('+content').exec();

        if (currentDoc) {
          const incomingContent = document.content;
          const contentChanged = incomingContent !== undefined && incomingContent !== currentDoc.content;

          // --- Versioning Logic ---
          if (contentChanged) { // Create version only if content changes (can be expanded later)
            this.logger.log(`Content changed for document ${document.id}. Creating version ${currentDoc.currentVersion}.`);
            const newVersion = new this.documentVersionModel({
              documentId: currentDoc.id,
              version: currentDoc.currentVersion, // Save the *current* version number
              content: currentDoc.content, // Save the *current* content
              metadata: currentDoc.metadata, // Save the *current* metadata
              // createdBy: userId, // TODO: Pass userId if available
            });
            await newVersion.save();
            this.logger.log(`Saved version ${currentDoc.currentVersion} for document ${document.id}`);

            // Increment version number for the main document update
            document.currentVersion = currentDoc.currentVersion + 1;
          } else {
             // Ensure currentVersion is preserved if no new version is created
             // If document.currentVersion is not provided in the update, keep the existing one.
             if (document.currentVersion === undefined) {
                document.currentVersion = currentDoc.currentVersion;
             }
          }
          // --- End Versioning Logic ---

          // Only update fields that are provided in the incoming 'document' object
          Object.assign(currentDoc, document); // Apply updates to the fetched document object
          await currentDoc.save(); // Save the updated main document
          return currentDoc;
        }
      }
      
      // Create new document
      const newDoc = new this.documentModel(document);
      await newDoc.save();
      return newDoc;
    } catch (error) {
      this.logger.error(`Error saving document: ${error.message}`, error.stack);
      throw error;
    }
  }

  async deleteDocument(id: string): Promise<boolean> {
    try {
      const result = await this.documentModel.deleteOne({ id }).exec();

      return result.deletedCount > 0;
    } catch (error) {
      this.logger.error(`Error deleting document ${id}:`, error);
      throw error;
    }
  }

  async updateDocumentMetadata(id: string, newMetadata: Record<string, any>): Promise<Document | null> {
    try {
      this.logger.log(`Updating metadata for document: ${id}`);
      // 1. Fetch the current document state (including content for versioning)
      const currentDoc = await this.documentModel.findOne({ id }).select('+content').exec();

      if (!currentDoc) {
        this.logger.warn(`Document not found for metadata update: ${id}`);
        return null; // Or throw NotFoundException
      }

      // --- Versioning Logic ---
      // Simple check: create version if metadata object changes.
      // TODO: Implement a more robust deep comparison if needed.
      const metadataChanged = JSON.stringify(currentDoc.metadata) !== JSON.stringify(newMetadata);

      let nextVersionNumber = currentDoc.currentVersion;

      if (metadataChanged) {
        this.logger.log(`Metadata changed for document ${id}. Creating version ${currentDoc.currentVersion}.`);
        const newVersion = new this.documentVersionModel({
          documentId: currentDoc.id,
          version: currentDoc.currentVersion, // Save the *current* version number
          content: currentDoc.content,       // Save the *current* content
          metadata: currentDoc.metadata,     // Save the *current* metadata
          // createdBy: userId, // TODO: Pass userId if available
        });
        await newVersion.save();
        this.logger.log(`Saved version ${currentDoc.currentVersion} for document ${id}`);
        nextVersionNumber = currentDoc.currentVersion + 1; // Increment version for the main doc
      }
      // --- End Versioning Logic ---

      // 2. Apply updates to the fetched document object
      currentDoc.metadata = newMetadata;
      currentDoc.currentVersion = nextVersionNumber; // Update version number

      // 3. Save the updated main document
      await currentDoc.save();
      this.logger.log(`Successfully updated metadata and version for document ${id}`);

      return currentDoc;

    } catch (error) {
      this.logger.error(`Error updating metadata for document ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getDocumentsByType(documentType: string): Promise<Document[]> {
    try {
      return await this.documentModel.find({
        'metadata.type': documentType
      }).exec();
    } catch (error) {
      this.logger.error(`Error getting documents of type ${documentType}:`, error);
      throw error;
    }
  }

  async getDocumentVersions(documentId: string): Promise<DocumentVersion[]> {
    try {
      this.logger.log(`Fetching versions for document: ${documentId}`);
      // Find all versions for the document, sort by version descending
      return await this.documentVersionModel.find({ documentId })
        .sort({ version: -1 }) // Sort by version number descending
        .exec();
    } catch (error) {
      this.logger.error(`Error fetching versions for document ${documentId}: ${error.message}`, error.stack);
      throw error; // Rethrow the error for upstream handling
    }
  }


} // Correctly closing the class

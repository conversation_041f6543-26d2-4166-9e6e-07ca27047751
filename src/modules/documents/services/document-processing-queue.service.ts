import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { DocumentProcessingJobType, ProcessDocumentJobData } from '../interfaces/document-processing.types';
import { QUEUES } from '../../queue/constants';

@Injectable()
export class DocumentProcessingQueueService {
  private readonly logger = new Logger(DocumentProcessingQueueService.name);

  constructor(
    @InjectQueue(QUEUES.DOCUMENT_PROCESSING)
    private readonly documentQueue: Queue<ProcessDocumentJobData>,
  ) {}

  /**
   * Check if the queue is healthy (Redis connection is working)
   * @returns An object with status information
   */
  async healthCheck(): Promise<{ status: string; details: any }> {
    try {
      // Get client from Bull Queue
      const client = this.documentQueue.client;
      
      // Ping the Redis server
      const pingResult = await client.ping();
      
      // Get Redis server info
      const info = await client.info();
      
      return {
        status: pingResult === 'PONG' ? 'healthy' : 'unhealthy',
        details: {
          redis: {
            ping: pingResult,
            info: info,
          },
        },
      };
    } catch (error) {
      this.logger.error(`Health check failed: ${error.message}`);
      return {
        status: 'unhealthy',
        details: {
          error: error.message,
        },
      };
    }
  }

  /**
   * Queue a document for processing
   * @param documentId Document ID to process
   * @param userId Optional user ID for tracking
   * @param options Processing options
   * @returns Job ID for tracking
   */
  async queueDocumentProcessing(
    documentId: string,
    userId?: string,
    options?: {
      priority?: boolean;
      extractMetadata?: boolean;
      generateSummary?: boolean;
    },
  ): Promise<string> {
    const jobData: ProcessDocumentJobData = {
      documentId,
      userId,
      options,
    };

    const jobOptions = {
      priority: options?.priority ? 1 : 10, // Lower number = higher priority
      attempts: 3,
      backoff: {
        type: 'exponential' as const,
        delay: 1000,
      },
    };

    const job = await this.documentQueue.add(
      DocumentProcessingJobType.PROCESS_DOCUMENT,
      jobData,
      jobOptions,
    );

    this.logger.log(
      `Document ${documentId} queued for processing with job ID ${job.id}`,
    );

    return job.id.toString();
  }

  /**
   * Get the status of a document processing job
   * @param jobId Job ID to check
   * @returns Status of the job
   */
  async getJobStatus(jobId: string): Promise<{
    id: string;
    status: string;
    progress: number;
    data: ProcessDocumentJobData;
    failedReason?: string;
    attempts?: number;
  }> {
    const job = await this.documentQueue.getJob(jobId);

    if (!job) {
      throw new Error(`Job ${jobId} not found`);
    }

    const state = await job.getState();
    const progress = await job.progress();

    return {
      id: job.id.toString(),
      status: state,
      progress: typeof progress === 'number' ? progress : 0,
      data: job.data,
      failedReason: job.failedReason,
      attempts: job.attemptsMade,
    };
  }

  /**
   * Get all active jobs for a document
   * @param documentId Document ID to check
   * @returns Array of job statuses
   */
  async getDocumentJobs(documentId: string): Promise<{
    id: string;
    status: string;
    progress: number;
    data: ProcessDocumentJobData;
  }[]> {
    // Get jobs from different states
    const activeJobs = await this.documentQueue.getJobs(['active', 'waiting', 'delayed']);
    const completedJobs = await this.documentQueue.getJobs(['completed'], 0, 100);
    const failedJobs = await this.documentQueue.getJobs(['failed'], 0, 100);
    
    const allJobs = [...activeJobs, ...completedJobs, ...failedJobs];
    
    // Filter for jobs related to this document
    const documentJobs = allJobs.filter(job => job.data.documentId === documentId);
    
    // Get status for each job
    return Promise.all(
      documentJobs.map(async (job) => {
        const state = await job.getState();
        const progress = await job.progress();
        
        return {
          id: job.id.toString(),
          status: state,
          progress: typeof progress === 'number' ? progress : 0,
          data: job.data,
        };
      })
    );
  }

  /**
   * Remove a completed job
   * @param jobId Job ID to remove
   */
  async removeJob(jobId: string): Promise<void> {
    const job = await this.documentQueue.getJob(jobId);
    
    if (job) {
      await job.remove();
      this.logger.log(`Job ${jobId} removed from queue`);
    }
  }
}

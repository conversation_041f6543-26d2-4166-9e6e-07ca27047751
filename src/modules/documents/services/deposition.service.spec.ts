import { Test, TestingModule } from '@nestjs/testing';
import { DepositionService } from './deposition.service';
import { AIService } from '../../../ai/services/ai.service';
import { DocumentsService } from './documents.service';
import { DepositionPreparationRepository } from '../repositories/deposition-preparation.repository';
import { DepositionAnalysisRepository } from '../repositories/deposition-analysis.repository';
import { DepositionQuestionCategory } from '../interfaces/deposition.interface';

describe('DepositionService', () => {
  let service: DepositionService;
  let mockAiService: jest.Mocked<AIService>;
  let mockDocumentsService: jest.Mocked<DocumentsService>;
  let mockPrepRepo: jest.Mocked<DepositionPreparationRepository>;
  let mockAnalysisRepo: jest.Mocked<DepositionAnalysisRepository>;

  beforeEach(async () => {
    mockAiService = {
      generateResponse: jest.fn(),
      generateStreamResponse: jest.fn(),
    } as any;

    mockDocumentsService = {
      findById: jest.fn(),
    } as any;

    mockPrepRepo = {
      create: jest.fn(),
      findById: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      findByUser: jest.fn(),
    } as any;

    mockAnalysisRepo = {
      create: jest.fn(),
      findById: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      findByUser: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DepositionService,
        { provide: AIService, useValue: mockAiService },
        { provide: DocumentsService, useValue: mockDocumentsService },
        { provide: DepositionPreparationRepository, useValue: mockPrepRepo },
        { provide: DepositionAnalysisRepository, useValue: mockAnalysisRepo },
      ],
    }).compile();

    service = module.get<DepositionService>(DepositionService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateQuestions', () => {
    it('should generate questions with AI', async () => {
      const mockQuestions = [
        {
          text: 'What is your role in this case?',
          category: DepositionQuestionCategory.BACKGROUND,
          purpose: 'Establish background',
          priority: 'high',
          targetWitness: 'John Doe',
          suggestedFollowUps: ['How long have you been in this role?'],
        },
      ];

      mockAiService.generateResponse.mockResolvedValueOnce(JSON.stringify(mockQuestions));

      const result = await service.generateQuestions(
        {
          caseContext: 'Test case',
          keyIssues: ['Issue 1', 'Issue 2'],
          targetWitnesses: ['John Doe'],
          documentIds: [],
          options: {
            questionCount: 5,
            questionCategories: [DepositionQuestionCategory.BACKGROUND],
            includeFollowUps: true,
          },
        },
        'user123',
        'org123',
      );

      expect(result.questions).toHaveLength(1);
      expect(result.questions[0].text).toBe(mockQuestions[0].text);
      expect(mockAiService.generateResponse).toHaveBeenCalled();
    });
  });

  describe('getDocumentContext', () => {
    it('should return document context', async () => {
      const mockDocument = {
        content: 'Test document content',
        metadata: { title: 'Test Document' },
      };

      mockDocumentsService.findById.mockResolvedValueOnce(mockDocument);

      const context = await (service as any).getDocumentContext(['doc123']);
      expect(context).toContain('Test Document');
      expect(context).toContain('Test document content');
      expect(mockDocumentsService.findById).toHaveBeenCalledWith('doc123');
    });

    it('should handle missing documents', async () => {
      mockDocumentsService.findById.mockResolvedValueOnce(null);
      const context = await (service as any).getDocumentContext(['doc123']);
      expect(context).toBe('');
    });
  });

  describe('generateFallbackQuestions', () => {
    it('should generate fallback questions', () => {
      const questions = (service as any).generateFallbackQuestions({
        targetWitnesses: ['John Doe'],
      });

      expect(questions).toHaveLength(2);
      expect(questions[0].targetWitness).toBe('John Doe');
      expect(questions[0].isFollowUp).toBe(false);
    });
  });
});

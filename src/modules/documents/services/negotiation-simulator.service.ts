import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import {
  NegotiationScenarioDocument,
  NegotiationSessionDocument,
  NegotiationTemplateDocument,
  NEGOTIATION_SCENARIO_MODEL,
  NEGOTIATION_SESSION_MODEL,
  NEGOTIATION_TEMPLATE_MODEL,
} from '../schemas/negotiation-simulator.schema';
import {
  NegotiationScenario,
  NegotiationSession,
  NegotiationTemplate,
  AIResponse,
  UserMove,
  NegotiationMetrics,
  SimulationEvaluation,
  AIPersonalityProfile,
} from '../interfaces/negotiation-simulator.interface';
import {
  CreateNegotiationScenarioDto,
  StartNegotiationSessionDto,
  MakeNegotiationMoveDto,
  NegotiationSimulatorOptionsDto,
} from '../dto/negotiation-simulator.dto';
import { AIService } from '../../ai/services/ai.service';

// Gamification imports
import { GamificationService } from '../../gamification/services/gamification.service';
import { AchievementService } from '../../gamification/services/achievement.service';
import { CharacterService } from '../../gamification/services/character.service';
import { RelationshipService } from '../../gamification/services/relationship.service';
import { LevelEngine } from '../../gamification/engines/level.engine';

@Injectable()
export class NegotiationSimulatorService {
  private readonly logger = new Logger(NegotiationSimulatorService.name);

  constructor(
    @InjectModel(NEGOTIATION_SCENARIO_MODEL)
    private readonly scenarioModel: Model<NegotiationScenarioDocument>,
    @InjectModel(NEGOTIATION_SESSION_MODEL)
    private readonly sessionModel: Model<NegotiationSessionDocument>,
    @InjectModel(NEGOTIATION_TEMPLATE_MODEL)
    private readonly templateModel: Model<NegotiationTemplateDocument>,
    private readonly aiService: AIService,

    // Gamification services
    @Inject(forwardRef(() => GamificationService))
    private readonly gamificationService: GamificationService,
    @Inject(forwardRef(() => AchievementService))
    private readonly achievementService: AchievementService,
    @Inject(forwardRef(() => CharacterService))
    private readonly characterService: CharacterService,
    @Inject(forwardRef(() => RelationshipService))
    private readonly relationshipService: RelationshipService,
    private readonly levelEngine: LevelEngine,
  ) {}

  async createScenario(
    dto: CreateNegotiationScenarioDto,
    userId: string,
    organizationId: string,
  ): Promise<NegotiationScenario> {
    this.logger.log(`Creating negotiation scenario: ${dto.name}`);

    // Add unique IDs to parties
    const partiesWithIds = dto.parties.map((party) => ({
      ...party,
      id: uuidv4(),
    }));

    const scenario = new this.scenarioModel({
      ...dto,
      parties: partiesWithIds,
      organizationId,
      createdBy: userId,
      status: 'active',
    });

    const savedScenario = await scenario.save();
    this.logger.log(`Created scenario with ID: ${savedScenario._id}`);

    return savedScenario.toObject({
      getters: true,
    }) as unknown as NegotiationScenario;
  }

  async getScenarios(
    organizationId: string,
    filters?: {
      industry?: string;
      contractType?: string;
      difficulty?: string;
      tags?: string[];
    },
  ): Promise<NegotiationScenario[]> {
    const query: any = { organizationId };

    if (filters) {
      if (filters.industry) query.industry = filters.industry;
      if (filters.contractType) query.contractType = filters.contractType;
      if (filters.difficulty) query.difficulty = filters.difficulty;
      if (filters.tags && filters.tags.length > 0) {
        query.tags = { $in: filters.tags };
      }
    }

    const scenarios = await this.scenarioModel
      .find(query)
      .sort({ createdAt: -1 })
      .exec();
    return scenarios.map(
      (s) => s.toObject({ getters: true }) as unknown as NegotiationScenario,
    );
  }

  async getScenario(
    scenarioId: string,
    organizationId: string,
  ): Promise<NegotiationScenario> {
    const scenario = await this.scenarioModel
      .findOne({
        _id: scenarioId,
        organizationId,
      })
      .exec();

    if (!scenario) {
      throw new NotFoundException(`Scenario with ID ${scenarioId} not found`);
    }

    return scenario.toObject() as NegotiationScenario;
  }

  async updateScenario(
    scenarioId: string,
    updateDto: Partial<CreateNegotiationScenarioDto>,
    userId: string,
    organizationId: string,
  ): Promise<NegotiationScenario> {
    this.logger.log(`Updating negotiation scenario: ${scenarioId}`);

    // Verify scenario exists and user has permission
    const existingScenario = await this.scenarioModel
      .findOne({
        _id: scenarioId,
        organizationId,
        createdBy: userId, // Only allow creator to update
      })
      .exec();

    if (!existingScenario) {
      throw new NotFoundException(
        `Scenario with ID ${scenarioId} not found or access denied`,
      );
    }

    // Prepare update data
    const updateData: any = {
      ...updateDto,
      updatedAt: new Date(),
    };

    // If parties are being updated, ensure they have IDs
    if (updateDto.parties) {
      updateData.parties = updateDto.parties.map((party: any) => ({
        ...party,
        id: party.id || uuidv4(),
      }));
    }

    const updatedScenario = await this.scenarioModel
      .findByIdAndUpdate(scenarioId, updateData, { new: true })
      .exec();

    if (!updatedScenario) {
      throw new NotFoundException(`Failed to update scenario ${scenarioId}`);
    }

    this.logger.log(`Successfully updated scenario: ${scenarioId}`);
    return updatedScenario.toObject({
      getters: true,
    }) as unknown as NegotiationScenario;
  }

  async cloneScenario(
    scenarioId: string,
    userId: string,
    organizationId: string,
    customizations?: { name?: string; description?: string },
  ): Promise<NegotiationScenario> {
    this.logger.log(`Cloning negotiation scenario: ${scenarioId}`);

    // Get the original scenario
    const originalScenario = await this.scenarioModel
      .findOne({
        _id: scenarioId,
        organizationId,
      })
      .exec();

    if (!originalScenario) {
      throw new NotFoundException(`Scenario with ID ${scenarioId} not found`);
    }

    // Create clone data
    const cloneData = {
      ...originalScenario.toObject(),
      _id: undefined, // Remove original ID
      name: customizations?.name || `${originalScenario.name} (Copy)`,
      description: customizations?.description || originalScenario.description,
      createdBy: userId,
      organizationId,
      createdAt: new Date(),
      updatedAt: new Date(),
      isTemplate: false, // Clones are not templates by default
      // Generate new IDs for parties
      parties: originalScenario.parties.map((party: any) => ({
        ...party,
        id: uuidv4(),
      })),
    };

    const clonedScenario = new this.scenarioModel(cloneData);
    const savedClone = await clonedScenario.save();

    this.logger.log(
      `Successfully cloned scenario: ${scenarioId} -> ${savedClone._id}`,
    );
    return savedClone.toObject({
      getters: true,
    }) as unknown as NegotiationScenario;
  }

  async deleteScenario(
    scenarioId: string,
    userId: string,
    organizationId: string,
  ): Promise<void> {
    this.logger.log(`Deleting negotiation scenario: ${scenarioId}`);

    // Verify scenario exists and user has permission
    const existingScenario = await this.scenarioModel
      .findOne({
        _id: scenarioId,
        organizationId,
        createdBy: userId, // Only allow creator to delete
      })
      .exec();

    if (!existingScenario) {
      throw new NotFoundException(
        `Scenario with ID ${scenarioId} not found or access denied`,
      );
    }

    // Check if there are any active sessions using this scenario
    const activeSessions = await this.sessionModel
      .find({
        scenarioId,
        status: { $in: ['active', 'paused'] },
      })
      .exec();

    if (activeSessions.length > 0) {
      throw new BadRequestException(
        `Cannot delete scenario with active sessions. Please complete or abandon ${activeSessions.length} active session(s) first.`,
      );
    }

    // Delete the scenario
    await this.scenarioModel.findByIdAndDelete(scenarioId).exec();

    this.logger.log(`Successfully deleted scenario: ${scenarioId}`);
  }

  async getTemplateScenarios(
    organizationId: string,
  ): Promise<NegotiationScenario[]> {
    this.logger.log('Retrieving template scenarios');

    // Get both global templates and organization-specific templates
    const templates = await this.scenarioModel
      .find({
        $or: [
          { isTemplate: true, organizationId: null }, // Global templates
          { isTemplate: true, organizationId }, // Organization templates
        ],
      })
      .sort({ createdAt: -1 })
      .exec();

    this.logger.log(`Found ${templates.length} template scenarios`);
    return templates.map(
      (t) => t.toObject({ getters: true }) as unknown as NegotiationScenario,
    );
  }

  async startSession(
    dto: StartNegotiationSessionDto & { characterId?: string },
    userId: string,
    organizationId: string,
    options?: NegotiationSimulatorOptionsDto,
  ): Promise<NegotiationSession & { gamificationData?: any }> {
    this.logger.log(
      `Starting negotiation session for scenario: ${dto.scenarioId}`,
    );

    // Verify scenario exists
    const scenario = await this.getScenario(dto.scenarioId, organizationId);

    // Get or create user gamification profile
    const userGamification = await this.gamificationService.getUserGamification(userId, organizationId);

    // Get character (use provided or default)
    let character = null;
    if (dto.characterId) {
      // Check if user has unlocked this character
      const hasUnlocked = await this.gamificationService.hasUnlockedContent(userId, 'character', dto.characterId);
      if (!hasUnlocked) {
        throw new BadRequestException('Character not unlocked');
      }
      character = await this.characterService.getCharacter(dto.characterId);
    } else {
      character = await this.characterService.getDefaultCharacter();
    }

    // Get relationship bonuses
    const relationship = await this.relationshipService.getRelationship(userId, character.id);

    // Generate enhanced AI personality with relationship bonuses
    const baseAIPersonality = this.generateAIPersonality(
      dto.aiPersonality || character.personality,
      scenario.difficulty,
    );
    const aiPersonality = this.applyRelationshipBonuses(baseAIPersonality, relationship);

    // Initialize metrics
    const initialMetrics: NegotiationMetrics = {
      totalRounds: 0,
      totalTime: 0,
      agreementReached: false,
      dealValue: 0,
      concessionsMade: { byUser: 0, byAI: 0 },
      strategicEffectiveness: 0,
      communicationQuality: 0,
      timeEfficiency: 0,
      overallScore: 0,
    };

    // Initialize game state for gamification
    const gameState = {
      userStress: 0.3,
      aiMood: 'neutral',
      activePressureEvents: [],
      timeRemaining: scenario.timeline?.maxDuration || null,
      currentScore: 5.0,
      dealMomentum: 'neutral',
    };

    // Initialize gamification data
    const gamificationData = {
      xpEarned: 0,
      achievementsUnlocked: [],
      pressureEventsTriggered: [],
      relationshipChanges: {},
      levelUps: 0,
    };

    const session = new this.sessionModel({
      scenarioId: dto.scenarioId,
      userId,
      organizationId,
      status: 'active',
      startTime: new Date(),
      rounds: [],
      currentRound: 0,
      metrics: initialMetrics,
      aiPersonality,
      characterId: character.id,
      gameState,
      gamificationData,
      options: options || {
        enableHints: true,
        showAIReasoning: false,
        allowPausing: true,
        recordSession: true,
        difficultyAdjustment: true,
        realTimeAnalysis: false,
      },
    });

    const savedSession = await session.save();
    this.logger.log(`Started session with ID: ${savedSession._id}`);

    // Award XP for starting session
    const startXP = this.levelEngine.getXPRewards().sessionStart;
    const levelUpdate = await this.gamificationService.awardExperience(
      userId,
      startXP,
      'session_start',
      { sessionId: savedSession._id.toString(), characterId: character.id }
    );

    // Update session statistics
    await this.gamificationService.updateStatistics(userId, {
      totalSessions: userGamification.statistics.totalSessions + 1,
    });

    const sessionObject = savedSession.toObject() as NegotiationSession;
    return {
      ...sessionObject,
      gamificationData: {
        character,
        relationship,
        levelUpdate,
        xpEarned: startXP,
      },
    };
  }

  /**
   * Apply relationship bonuses to AI personality
   */
  private applyRelationshipBonuses(
    basePersonality: AIPersonalityProfile,
    relationship: any,
  ): AIPersonalityProfile {
    if (!relationship) return basePersonality;

    const enhanced = { ...basePersonality };

    // Apply relationship bonuses
    if (relationship.bonuses?.increasedFlexibility) {
      enhanced.flexibility = Math.min(1.0, enhanced.flexibility + relationship.bonuses.increasedFlexibility);
    }

    if (relationship.bonuses?.betterStartingTerms) {
      enhanced.aggressiveness = Math.max(0.0, enhanced.aggressiveness - 0.1);
    }

    if (relationship.bonuses?.relationshipDiscount) {
      enhanced.riskTolerance = Math.min(1.0, enhanced.riskTolerance + 0.1);
    }

    return enhanced;
  }

  /**
   * Calculate XP reward for a move
   */
  private async calculateMoveXPReward(
    session: any,
    move: UserMove,
    aiResponse: AIResponse,
  ): Promise<number> {
    const rewards = this.levelEngine.getXPRewards();
    let baseXP = rewards.moveCompleted;

    // Performance multiplier based on move quality
    const performanceMultiplier = Math.max(0.5, Math.min(2.0, aiResponse.confidence || 1.0));

    // Difficulty multiplier
    let difficultyMultiplier = 1;
    if (session.characterId) {
      try {
        const character = await this.characterService.getCharacter(session.characterId);
        difficultyMultiplier = character?.difficulty || 1;
      } catch (error) {
        this.logger.warn(`Could not get character ${session.characterId}: ${error.message}`);
      }
    }

    return this.levelEngine.calculateXPReward(
      baseXP,
      performanceMultiplier,
      difficultyMultiplier * 0.5, // Scale down difficulty impact
    );
  }

  async makeMove(
    dto: MakeNegotiationMoveDto,
    userId: string,
  ): Promise<{
    session: NegotiationSession;
    aiResponse: AIResponse;
    gamificationUpdate?: any;
  }> {
    this.logger.log(`Processing move for session: ${dto.sessionId}`);

    const session = await this.sessionModel.findById(dto.sessionId).exec();
    if (!session) {
      throw new NotFoundException(`Session with ID ${dto.sessionId} not found`);
    }

    if (session.userId !== userId) {
      throw new BadRequestException('Unauthorized access to session');
    }

    if (session.status !== 'active') {
      throw new BadRequestException('Session is not active');
    }

    // Get scenario for context
    const scenario = await this.scenarioModel
      .findById(session.scenarioId)
      .exec();
    if (!scenario) {
      throw new NotFoundException('Associated scenario not found');
    }

    // Record user move
    const userRound = {
      id: uuidv4(),
      roundNumber: session.currentRound + 1,
      timestamp: new Date(),
      party: userId,
      offer: dto.move.offer,
      message: dto.move.message,
      reasoning: dto.move.reasoning,
      confidence: 1.0, // User moves have full confidence
      strategy: dto.move.strategy || 'user-defined',
      responseTime: 0, // Will be calculated based on time since last round
    };

    session.rounds.push(userRound);
    session.currentRound += 1;

    // Generate AI response
    const aiResponse = await this.generateAIResponse(
      session,
      scenario,
      dto.move,
    );

    // Record AI move
    const aiRound = {
      id: uuidv4(),
      roundNumber: session.currentRound + 1,
      timestamp: new Date(),
      party: 'ai',
      offer: aiResponse.offer,
      message: aiResponse.message,
      reasoning: aiResponse.reasoning,
      confidence: aiResponse.confidence,
      strategy: aiResponse.strategy,
      responseTime: Math.floor(Math.random() * 3000) + 1000, // Simulate AI thinking time
    };

    session.rounds.push(aiRound);
    session.currentRound += 1;

    // Update metrics
    session.metrics = this.updateMetrics(session, scenario);

    // GAMIFICATION: Calculate XP for this move
    const moveXP = await this.calculateMoveXPReward(session, dto.move, aiResponse);

    // GAMIFICATION: Check for achievements
    const newAchievements = await this.achievementService.checkSessionAchievements(
      userId,
      session._id.toString(),
      session,
    );

    // GAMIFICATION: Update relationship with character
    if (session.characterId) {
      await this.relationshipService.updateRelationship(
        userId,
        session.characterId,
        dto.move,
        aiResponse,
      );
    }

    // Check for session completion
    let completionXP = 0;
    let completionAchievements = [];
    if (this.shouldEndSession(session, scenario, aiResponse)) {
      session.status = 'completed';
      session.endTime = new Date();
      session.finalAgreement = aiResponse.offer;
      session.metrics.agreementReached = true;

      // GAMIFICATION: Award completion XP
      const rewards = this.levelEngine.getXPRewards();
      completionXP = session.metrics.agreementReached ? rewards.dealClosed : rewards.sessionCompleted;

      // GAMIFICATION: Check completion achievements
      completionAchievements = await this.achievementService.checkSessionAchievements(
        userId,
        session._id.toString(),
        session,
      );

      // GAMIFICATION: Update user statistics
      const userGamification = await this.gamificationService.getUserGamification(userId);
      await this.gamificationService.updateStatistics(userId, {
        completedSessions: userGamification.statistics.completedSessions + 1,
        averageScore: this.calculateNewAverageScore(
          userGamification.statistics.averageScore,
          userGamification.statistics.completedSessions,
          session.metrics.overallScore,
        ),
        winRate: this.calculateWinRate(
          userGamification.statistics.completedSessions + 1,
          session.metrics.agreementReached,
        ),
      });
    }

    // GAMIFICATION: Award total XP
    const totalXP = moveXP + completionXP;
    let levelUpdate = null;
    if (totalXP > 0) {
      levelUpdate = await this.gamificationService.awardExperience(
        userId,
        totalXP,
        session.status === 'completed' ? 'session_completed' : 'move_completed',
        { sessionId: session._id.toString(), moveXP, completionXP },
      );
    }

    // GAMIFICATION: Update session gamification data
    session.gamificationData.xpEarned += totalXP;
    session.gamificationData.achievementsUnlocked.push(...newAchievements, ...completionAchievements);
    if (levelUpdate?.leveledUp) {
      session.gamificationData.levelUps += 1;
    }

    await session.save();

    const gamificationUpdate = {
      xpEarned: totalXP,
      moveXP,
      completionXP,
      newAchievements: [...newAchievements, ...completionAchievements],
      levelUpdate,
      totalXPEarned: session.gamificationData.xpEarned,
    };

    return {
      session: session.toObject() as NegotiationSession,
      aiResponse,
      gamificationUpdate,
    };
  }

  /**
   * Calculate new average score
   */
  private calculateNewAverageScore(
    currentAverage: number,
    completedSessions: number,
    newScore: number,
  ): number {
    if (completedSessions === 0) return newScore;
    return (currentAverage * completedSessions + newScore) / (completedSessions + 1);
  }

  /**
   * Calculate win rate
   */
  private calculateWinRate(totalSessions: number, wasSuccessful: boolean): number {
    // This is a simplified calculation - you might want to track wins differently
    const currentWins = Math.floor(totalSessions * 0.5); // Placeholder
    const newWins = wasSuccessful ? currentWins + 1 : currentWins;
    return newWins / totalSessions;
  }

  private generateAIPersonality(
    customPersonality?: Partial<AIPersonalityProfile>,
    difficulty?: string,
  ): AIPersonalityProfile {
    // Base personality based on difficulty
    let basePersonality: AIPersonalityProfile;

    switch (difficulty) {
      case 'beginner':
        basePersonality = {
          aggressiveness: 0.3,
          flexibility: 0.7,
          riskTolerance: 0.6,
          communicationStyle: 'diplomatic',
          decisionSpeed: 'moderate',
          concessionPattern: 'gradual',
        };
        break;
      case 'expert':
        basePersonality = {
          aggressiveness: 0.7,
          flexibility: 0.4,
          riskTolerance: 0.3,
          communicationStyle: 'formal',
          decisionSpeed: 'deliberate',
          concessionPattern: 'minimal',
        };
        break;
      default: // intermediate
        basePersonality = {
          aggressiveness: 0.5,
          flexibility: 0.5,
          riskTolerance: 0.5,
          communicationStyle: 'technical',
          decisionSpeed: 'moderate',
          concessionPattern: 'gradual',
        };
    }

    // Apply customizations
    return { ...basePersonality, ...customPersonality };
  }

  private async generateAIResponse(
    session: NegotiationSessionDocument,
    scenario: NegotiationScenarioDocument,
    userMove: UserMove,
  ): Promise<AIResponse> {
    const prompt = this.buildNegotiationPrompt(session, scenario, userMove);

    try {
      const response = await this.aiService.generateResponse(prompt, {
        temperature: 0.7,
        systemMessage:
          'You are an expert negotiator. Respond strategically and realistically.',
        responseFormat: 'json',
      });

      // Parse AI response
      let parsedResponse: any;
      try {
        const cleanedResponse = response.replace(/```json\s*|\s*```/g, '');
        parsedResponse = JSON.parse(cleanedResponse);
      } catch (parseError) {
        this.logger.warn('Failed to parse AI response as JSON, using fallback');
        parsedResponse = this.generateFallbackResponse(
          userMove,
          session.aiPersonality,
        );
      }

      return {
        offer: parsedResponse.offer || userMove.offer,
        message: parsedResponse.message || 'Let me consider your proposal.',
        reasoning:
          parsedResponse.reasoning ||
          'Strategic response based on current position.',
        confidence: parsedResponse.confidence || 0.7,
        strategy: parsedResponse.strategy || 'analytical',
        nextMoveHint: parsedResponse.nextMoveHint,
        marketContext: parsedResponse.marketContext,
        riskAssessment: parsedResponse.riskAssessment,
      };
    } catch (error) {
      this.logger.error(`Error generating AI response: ${error.message}`);
      return this.generateFallbackResponse(userMove, session.aiPersonality);
    }
  }

  private buildNegotiationPrompt(
    session: NegotiationSessionDocument,
    scenario: NegotiationScenarioDocument,
    userMove: UserMove,
  ): string {
    const personality = session.aiPersonality;
    const recentRounds = session.rounds.slice(-3); // Last 3 rounds for context

    return `
You are negotiating a ${scenario.contractType} in the ${
      scenario.industry
    } industry.

AI Personality:
- Aggressiveness: ${personality.aggressiveness}
- Flexibility: ${personality.flexibility}
- Risk Tolerance: ${personality.riskTolerance}
- Communication Style: ${personality.communicationStyle}
- Decision Speed: ${personality.decisionSpeed}
- Concession Pattern: ${personality.concessionPattern}

Scenario Context:
${scenario.description}

Initial Offer: ${JSON.stringify(scenario.initialOffer)}
Constraints: ${JSON.stringify(scenario.constraints)}

Recent Negotiation History:
${recentRounds
  .map(
    (round) => `Round ${round.roundNumber}: ${round.party} - ${round.message}`,
  )
  .join('\n')}

User's Latest Move:
Offer: ${JSON.stringify(userMove.offer)}
Message: "${userMove.message}"
Strategy: ${userMove.strategy || 'Not specified'}

Respond with a JSON object containing:
{
  "offer": { /* your counter-offer terms */ },
  "message": "Your response message",
  "reasoning": "Why you made this move",
  "confidence": 0.8, // 0-1 confidence in this move
  "strategy": "collaborative|aggressive|analytical",
  "nextMoveHint": "Optional hint for user's next move",
  "marketContext": "Optional market context",
  "riskAssessment": "Optional risk assessment"
}

Be realistic, strategic, and consistent with your personality profile.
`;
  }

  private generateFallbackResponse(
    userMove: UserMove,
    _personality: any,
  ): AIResponse {
    return {
      offer: userMove.offer, // Mirror the user's offer as fallback
      message:
        'I need to review your proposal carefully. Let me get back to you.',
      reasoning: 'Fallback response due to AI processing error.',
      confidence: 0.5,
      strategy: 'analytical',
    };
  }

  private updateMetrics(
    session: NegotiationSessionDocument,
    scenario: NegotiationScenarioDocument,
  ): NegotiationMetrics {
    const metrics = session.metrics;

    metrics.totalRounds = session.rounds.length;
    metrics.totalTime = session.endTime
      ? (session.endTime.getTime() - session.startTime.getTime()) / 60000 // minutes
      : (Date.now() - session.startTime.getTime()) / 60000;

    // Calculate other metrics based on rounds and scenario
    metrics.strategicEffectiveness = this.calculateStrategicEffectiveness(
      session,
      scenario,
    );
    metrics.communicationQuality = this.calculateCommunicationQuality(session);
    metrics.timeEfficiency = this.calculateTimeEfficiency(session, scenario);

    metrics.overallScore =
      metrics.strategicEffectiveness * 0.4 +
      metrics.communicationQuality * 0.3 +
      metrics.timeEfficiency * 0.3;

    return metrics;
  }

  private calculateStrategicEffectiveness(
    session: NegotiationSessionDocument,
    _scenario: NegotiationScenarioDocument,
  ): number {
    // Simplified calculation - in real implementation, this would be more sophisticated
    const userRounds = session.rounds.filter((round) => round.party !== 'ai');
    const avgConfidence =
      userRounds.reduce((sum, round) => sum + round.confidence, 0) /
      userRounds.length;
    return Math.min(avgConfidence || 0.5, 1.0);
  }

  private calculateCommunicationQuality(
    session: NegotiationSessionDocument,
  ): number {
    // Simplified calculation based on message length and politeness
    const userRounds = session.rounds.filter((round) => round.party !== 'ai');
    const avgMessageLength =
      userRounds.reduce((sum, round) => sum + round.message.length, 0) /
      userRounds.length;
    return Math.min(avgMessageLength / 100, 1.0); // Normalize to 0-1
  }

  private calculateTimeEfficiency(
    session: NegotiationSessionDocument,
    scenario: NegotiationScenarioDocument,
  ): number {
    const expectedTime = scenario.timeline.expectedDuration;
    const actualTime = session.metrics.totalTime;
    return Math.max(0, 1 - (actualTime - expectedTime) / expectedTime);
  }

  private shouldEndSession(
    session: NegotiationSessionDocument,
    scenario: NegotiationScenarioDocument,
    aiResponse: AIResponse,
  ): boolean {
    // Check various end conditions
    if (session.currentRound >= scenario.constraints.maxRounds) {
      return true;
    }

    if (
      scenario.constraints.timeLimit &&
      session.metrics.totalTime >= scenario.constraints.timeLimit
    ) {
      return true;
    }

    // Check if AI indicates agreement
    if (
      aiResponse.message.toLowerCase().includes('accept') ||
      aiResponse.message.toLowerCase().includes('agree') ||
      aiResponse.confidence > 0.9
    ) {
      return true;
    }

    return false;
  }

  async getSession(
    sessionId: string,
    userId: string,
  ): Promise<NegotiationSession> {
    const session = await this.sessionModel
      .findOne({
        _id: sessionId,
        userId,
      })
      .exec();

    if (!session) {
      throw new NotFoundException(`Session with ID ${sessionId} not found`);
    }

    return session.toObject({ getters: true }) as unknown as NegotiationSession;
  }

  async getUserSessions(
    userId: string,
    organizationId: string,
    status?: string,
  ): Promise<NegotiationSession[]> {
    const query: any = { userId, organizationId };
    if (status) {
      query.status = status;
    }

    const sessions = await this.sessionModel
      .find(query)
      .sort({ startTime: -1 })
      .exec();
    return sessions.map(
      (session) =>
        session.toObject({ getters: true }) as unknown as NegotiationSession,
    );
  }

  async pauseSession(
    sessionId: string,
    userId: string,
  ): Promise<NegotiationSession> {
    const session = await this.sessionModel
      .findOneAndUpdate(
        { _id: sessionId, userId, status: 'active' },
        { status: 'paused' },
        { new: true },
      )
      .exec();

    if (!session) {
      throw new NotFoundException('Active session not found');
    }

    return session.toObject({ getters: true }) as unknown as NegotiationSession;
  }

  async resumeSession(
    sessionId: string,
    userId: string,
  ): Promise<NegotiationSession> {
    const session = await this.sessionModel
      .findOneAndUpdate(
        { _id: sessionId, userId, status: 'paused' },
        { status: 'active' },
        { new: true },
      )
      .exec();

    if (!session) {
      throw new NotFoundException('Paused session not found');
    }

    return session.toObject({ getters: true }) as unknown as NegotiationSession;
  }

  async abandonSession(sessionId: string, userId: string): Promise<void> {
    this.logger.log(`Abandoning session: ${sessionId} for user: ${userId}`);

    const session = await this.sessionModel
      .findOneAndUpdate(
        {
          _id: sessionId,
          userId,
          status: { $in: ['active', 'paused'] }, // Can abandon active or paused sessions
        },
        {
          status: 'abandoned',
          endTime: new Date(),
          abandonedAt: new Date(),
        },
        { new: true },
      )
      .exec();

    if (!session) {
      throw new NotFoundException(
        'Active or paused session not found or already completed',
      );
    }

    // Update metrics to reflect abandonment
    session.metrics.agreementReached = false;
    session.metrics.totalTime = session.endTime
      ? (session.endTime.getTime() - session.startTime.getTime()) / 60000
      : (Date.now() - session.startTime.getTime()) / 60000;

    // Apply penalty for abandonment
    session.metrics.overallScore = Math.max(
      session.metrics.overallScore * 0.5,
      0,
    );

    await session.save();

    this.logger.log(`Successfully abandoned session: ${sessionId}`);
  }

  async evaluateSession(
    sessionId: string,
    userId: string,
  ): Promise<SimulationEvaluation> {
    const session = await this.getSession(sessionId, userId);

    if (session.status !== 'completed') {
      throw new BadRequestException('Session must be completed for evaluation');
    }

    // Generate evaluation using AI
    const evaluationPrompt = this.buildEvaluationPrompt(session);

    try {
      const response = await this.aiService.generateResponse(evaluationPrompt, {
        temperature: 0.3,
        systemMessage:
          'You are an expert negotiation coach providing detailed feedback.',
        responseFormat: 'json',
      });

      const cleanedResponse = response.replace(/```json\s*|\s*```/g, '');
      const evaluation = JSON.parse(cleanedResponse);

      return {
        sessionId,
        metrics: session.metrics,
        strengths: evaluation.strengths || [],
        weaknesses: evaluation.weaknesses || [],
        recommendations: evaluation.recommendations || [],
        learningPoints: evaluation.learningPoints || [],
        nextSteps: evaluation.nextSteps || [],
        improvementAreas: evaluation.improvementAreas || [],
      };
    } catch (error) {
      this.logger.error(`Error generating evaluation: ${error.message}`);

      // Fallback evaluation
      return {
        sessionId,
        metrics: session.metrics,
        strengths: ['Completed the negotiation'],
        weaknesses: ['Areas for improvement identified'],
        recommendations: ['Continue practicing negotiation skills'],
        learningPoints: ['Experience gained through simulation'],
        nextSteps: ['Try more challenging scenarios'],
        improvementAreas: [
          {
            area: 'Overall Performance',
            currentScore: session.metrics.overallScore,
            targetScore: Math.min(session.metrics.overallScore + 0.2, 1.0),
            suggestions: [
              'Practice more scenarios',
              'Study negotiation techniques',
            ],
          },
        ],
      };
    }
  }

  private buildEvaluationPrompt(session: NegotiationSession): string {
    return `
Analyze this completed negotiation session and provide detailed feedback:

Session Metrics:
- Total Rounds: ${session.metrics.totalRounds}
- Total Time: ${session.metrics.totalTime} minutes
- Agreement Reached: ${session.metrics.agreementReached}
- Overall Score: ${session.metrics.overallScore}
- Strategic Effectiveness: ${session.metrics.strategicEffectiveness}
- Communication Quality: ${session.metrics.communicationQuality}
- Time Efficiency: ${session.metrics.timeEfficiency}

Negotiation Rounds:
${session.rounds
  .map(
    (round) =>
      `Round ${round.roundNumber} (${round.party}): ${round.message} [Strategy: ${round.strategy}]`,
  )
  .join('\n')}

Provide a JSON response with:
{
  "strengths": ["List of negotiation strengths demonstrated"],
  "weaknesses": ["Areas that need improvement"],
  "recommendations": ["Specific actionable recommendations"],
  "learningPoints": ["Key lessons from this session"],
  "nextSteps": ["Suggested next actions for improvement"],
  "improvementAreas": [
    {
      "area": "Specific skill area",
      "currentScore": 0.7,
      "targetScore": 0.9,
      "suggestions": ["Specific improvement suggestions"]
    }
  ]
}
`;
  }
}

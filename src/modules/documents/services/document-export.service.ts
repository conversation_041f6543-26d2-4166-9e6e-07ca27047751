import { Injectable, Logger } from '@nestjs/common';
import PDFDocument from 'pdfkit';
import { Document, Packer, Paragraph, TextRun, HeadingLevel, Table, TableRow, TableCell, BorderStyle } from 'docx';
import { ComparisonResult, EnhancedComparisonResult, VersionComparisonResult, ExportOptions, DiffSegment } from '../interfaces/document-comparison.interface';
import { AIService } from '../../ai/services/ai.service';

@Injectable()
export class DocumentExportService {
  private readonly logger = new Logger(DocumentExportService.name);

  constructor(private readonly aiService: AIService) {}

  /**
   * Generate PDF report from comparison results
   */
  async generatePdfReport(
    comparisonResult: ComparisonResult | EnhancedComparisonResult | VersionComparisonResult,
    options: ExportOptions
  ): Promise<Buffer> {
    this.logger.log('Generating PDF comparison report');
    
    return new Promise((resolve, reject) => {
      try {
        const chunks: Buffer[] = [];
        const doc = new PDFDocument({ margin: 50 });
        
        // Collect PDF data chunks
        doc.on('data', (chunk) => chunks.push(chunk));
        doc.on('end', () => resolve(Buffer.concat(chunks)));
        doc.on('error', (error) => reject(error));

        // Add title
        doc.fontSize(24).text('Document Comparison Report', { align: 'center' });
        doc.moveDown();
        
        // Add timestamp
        doc.fontSize(12).text(`Generated on: ${new Date().toLocaleString()}`, { align: 'right' });
        doc.moveDown();

        // Add metadata if requested
        if (options.includeMetadata && comparisonResult.metadata) {
          doc.fontSize(16).text('Comparison Metadata', { underline: true });
          doc.moveDown(0.5);
          
          const metadata = comparisonResult.metadata;
          doc.fontSize(12).text(`Comparison Date: ${metadata.timestamp.toLocaleString()}`);
          
          if (metadata.documentStats) {
            doc.text(`Document A Length: ${metadata.documentStats.documentA.length} characters`);
            doc.text(`Document B Length: ${metadata.documentStats.documentB.length} characters`);
          }
          
          if (metadata.summary) {
            doc.text(`Added Lines: ${metadata.summary.addedLines}`);
            doc.text(`Removed Lines: ${metadata.summary.removedLines}`);
            doc.text(`Modified Lines: ${metadata.summary.modifiedLines}`);
            doc.text(`Total Changes: ${metadata.summary.totalChanges}`);
          }
          
          doc.moveDown();
        }

        // Add executive summary if requested
        if (options.includeSummary) {
          doc.fontSize(16).text('Executive Summary', { underline: true });
          doc.moveDown(0.5);
          
          if ('versionInfo' in comparisonResult) {
            // Version comparison specific summary
            doc.fontSize(12).text(`Comparing version ${comparisonResult.versionInfo.beforeVersion} with version ${comparisonResult.versionInfo.afterVersion}`);
            doc.text(`Version Delta: ${comparisonResult.versionInfo.versionDelta}`);
          }
          
          // Add AI-generated summary if available
          if (comparisonResult.metadata?.summary?.significantChanges) {
            doc.fontSize(14).text('Significant Changes:');
            comparisonResult.metadata.summary.significantChanges.forEach((change, index) => {
              doc.fontSize(12).text(`${index + 1}. ${change.type.toUpperCase()}: ${change.description}`);
            });
          }
          
          doc.moveDown();
        }

        // Add diff visualization
        doc.fontSize(16).text('Differences', { underline: true });
        doc.moveDown(0.5);
        
        if (comparisonResult.diffs && comparisonResult.diffs.length > 0) {
          // Group diffs by type for better readability
          const insertions = comparisonResult.diffs.filter(d => d.type === 'insert');
          const deletions = comparisonResult.diffs.filter(d => d.type === 'delete');
          const equals = comparisonResult.diffs.filter(d => d.type === 'equal');
          
          if (options.highlightChanges) {
            doc.fontSize(14).text('Additions:', { continued: false });
            if (insertions.length > 0) {
              insertions.forEach(diff => {
                doc.fontSize(12).fillColor('green').text(diff.text);
              });
            } else {
              doc.fontSize(12).text('No additions found');
            }
            
            doc.moveDown();
            doc.fontSize(14).fillColor('black').text('Deletions:', { continued: false });
            if (deletions.length > 0) {
              deletions.forEach(diff => {
                doc.fontSize(12).fillColor('red').text(diff.text);
              });
            } else {
              doc.fontSize(12).text('No deletions found');
            }
          } else {
            // Simple text representation without colors
            doc.fontSize(12).text('Changes are represented as [+Added] and [-Removed]');
            comparisonResult.diffs.forEach(diff => {
              let text = diff.text;
              if (diff.type === 'insert') text = `[+${text}]`;
              if (diff.type === 'delete') text = `[-${text}]`;
              doc.text(text, { continued: true });
            });
          }
        } else {
          doc.fontSize(12).text('No differences found or diff data not available');
        }

        // Add section diffs if available (for enhanced comparison)
        if ('sectionDiffs' in comparisonResult && comparisonResult.sectionDiffs) {
          doc.addPage();
          doc.fontSize(16).text('Section Analysis', { underline: true });
          doc.moveDown(0.5);
          
          comparisonResult.sectionDiffs.forEach((sectionDiff, index) => {
            doc.fontSize(14).text(`Section ${index + 1}: ${sectionDiff.sectionA.title || 'Untitled Section'}`);
            doc.fontSize(12).text(`Changes: ${sectionDiff.differences.length}`);
            
            if (sectionDiff.differences.length > 0) {
              sectionDiff.differences.forEach(diff => {
                let color = 'black';
                if (diff.diffType === 'addition') color = 'green';
                if (diff.diffType === 'deletion') color = 'red';
                if (diff.diffType === 'modification') color = 'blue';
                
                doc.fillColor(color).text(`Line ${diff.lineNumber}: ${diff.diffType.toUpperCase()}`);
                doc.text(`Original: ${diff.original}`);
                doc.text(`Modified: ${diff.modified}`);
                doc.moveDown(0.5);
              });
            } else {
              doc.text('No changes in this section');
            }
            
            doc.moveDown();
          });
        }

        // Finalize the PDF
        doc.end();
      } catch (error) {
        this.logger.error(`Error generating PDF report: ${error.message}`, error.stack);
        reject(error);
      }
    });
  }

  /**
   * Generate DOCX report from comparison results
   */
  async generateDocxReport(
    comparisonResult: ComparisonResult | EnhancedComparisonResult | VersionComparisonResult,
    options: ExportOptions
  ): Promise<Buffer> {
    this.logger.log('Generating DOCX comparison report');
    
    try {
      const doc = new Document({
        sections: [{
          properties: {},
          children: this.generateDocxContent(comparisonResult, options)
        }]
      });
      
      return await Packer.toBuffer(doc);
    } catch (error) {
      this.logger.error(`Error generating DOCX report: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Generate content for DOCX document
   */
  private generateDocxContent(
    comparisonResult: ComparisonResult | EnhancedComparisonResult | VersionComparisonResult,
    options: ExportOptions
  ): any[] {
    const content: any[] = [];
    
    // Add title
    content.push(new Paragraph({
      text: 'Document Comparison Report',
      heading: HeadingLevel.HEADING_1,
      alignment: 'center'
    }));
    
    // Add timestamp
    content.push(new Paragraph({
      text: `Generated on: ${new Date().toLocaleString()}`,
      alignment: 'right'
    }));
    
    // Add metadata if requested
    if (options.includeMetadata && comparisonResult.metadata) {
      content.push(new Paragraph({
        text: 'Comparison Metadata',
        heading: HeadingLevel.HEADING_2
      }));
      
      const metadata = comparisonResult.metadata;
      content.push(new Paragraph(`Comparison Date: ${metadata.timestamp.toLocaleString()}`));
      
      if (metadata.documentStats) {
        content.push(new Paragraph(`Document A Length: ${metadata.documentStats.documentA.length} characters`));
        content.push(new Paragraph(`Document B Length: ${metadata.documentStats.documentB.length} characters`));
      }
      
      if (metadata.summary) {
        const table = new Table({
          rows: [
            new TableRow({
              children: [
                new TableCell({ children: [new Paragraph('Metric')] }),
                new TableCell({ children: [new Paragraph('Value')] })
              ]
            }),
            new TableRow({
              children: [
                new TableCell({ children: [new Paragraph('Added Lines')] }),
                new TableCell({ children: [new Paragraph(`${metadata.summary.addedLines}`)] })
              ]
            }),
            new TableRow({
              children: [
                new TableCell({ children: [new Paragraph('Removed Lines')] }),
                new TableCell({ children: [new Paragraph(`${metadata.summary.removedLines}`)] })
              ]
            }),
            new TableRow({
              children: [
                new TableCell({ children: [new Paragraph('Modified Lines')] }),
                new TableCell({ children: [new Paragraph(`${metadata.summary.modifiedLines}`)] })
              ]
            }),
            new TableRow({
              children: [
                new TableCell({ children: [new Paragraph('Total Changes')] }),
                new TableCell({ children: [new Paragraph(`${metadata.summary.totalChanges}`)] })
              ]
            })
          ]
        });
        
        content.push(table);
      }
    }
    
    // Add executive summary if requested
    if (options.includeSummary) {
      content.push(new Paragraph({
        text: 'Executive Summary',
        heading: HeadingLevel.HEADING_2
      }));
      
      if ('versionInfo' in comparisonResult) {
        content.push(new Paragraph({
          children: [
            new TextRun(`Comparing version ${comparisonResult.versionInfo.beforeVersion} with version ${comparisonResult.versionInfo.afterVersion}`),
            new TextRun({ text: `\nVersion Delta: ${comparisonResult.versionInfo.versionDelta}`, break: 1 })
          ]
        }));
      }
      
      // Add AI-generated summary if available
      if (comparisonResult.metadata?.summary?.significantChanges) {
        content.push(new Paragraph({
          text: 'Significant Changes:',
          heading: HeadingLevel.HEADING_3
        }));
        
        comparisonResult.metadata.summary.significantChanges.forEach((change, index) => {
          content.push(new Paragraph(`${index + 1}. ${change.type.toUpperCase()}: ${change.description}`));
        });
      }
    }
    
    // Add diff visualization
    content.push(new Paragraph({
      text: 'Differences',
      heading: HeadingLevel.HEADING_2
    }));
    
    if (comparisonResult.diffs && comparisonResult.diffs.length > 0) {
      if (options.highlightChanges) {
        // Group diffs by type for better readability
        const insertions = comparisonResult.diffs.filter(d => d.type === 'insert');
        const deletions = comparisonResult.diffs.filter(d => d.type === 'delete');
        
        content.push(new Paragraph({
          text: 'Additions:',
          heading: HeadingLevel.HEADING_3
        }));
        
        if (insertions.length > 0) {
          const additionsParagraph = new Paragraph({
            children: insertions.map(diff => new TextRun({
              text: diff.text,
              color: '00AA00'
            }))
          });
          content.push(additionsParagraph);
        } else {
          content.push(new Paragraph('No additions found'));
        }
        
        content.push(new Paragraph({
          text: 'Deletions:',
          heading: HeadingLevel.HEADING_3
        }));
        
        if (deletions.length > 0) {
          const deletionsParagraph = new Paragraph({
            children: deletions.map(diff => new TextRun({
              text: diff.text,
              color: 'AA0000'
            }))
          });
          content.push(deletionsParagraph);
        } else {
          content.push(new Paragraph('No deletions found'));
        }
      } else {
        // Simple text representation
        content.push(new Paragraph('Changes are represented as [+Added] and [-Removed]'));
        
        const diffParagraphs = this.groupDiffsIntoParagraphs(comparisonResult.diffs);
        diffParagraphs.forEach(diffGroup => {
          const paragraph = new Paragraph({
            children: diffGroup.map(diff => {
              let text = diff.text;
              let color = '000000';
              
              if (diff.type === 'insert') {
                text = `[+${text}]`;
                color = '00AA00';
              } else if (diff.type === 'delete') {
                text = `[-${text}]`;
                color = 'AA0000';
              }
              
              return new TextRun({
                text,
                color
              });
            })
          });
          
          content.push(paragraph);
        });
      }
    } else {
      content.push(new Paragraph('No differences found or diff data not available'));
    }
    
    return content;
  }

  /**
   * Group diffs into paragraphs for better readability in DOCX
   */
  private groupDiffsIntoParagraphs(diffs: DiffSegment[]): DiffSegment[][] {
    const result: DiffSegment[][] = [];
    let currentGroup: DiffSegment[] = [];
    let currentLength = 0;
    
    // Group diffs into paragraphs of reasonable length
    diffs.forEach(diff => {
      if (currentLength + diff.text.length > 1000) {
        if (currentGroup.length > 0) {
          result.push(currentGroup);
          currentGroup = [];
          currentLength = 0;
        }
      }
      
      currentGroup.push(diff);
      currentLength += diff.text.length;
    });
    
    if (currentGroup.length > 0) {
      result.push(currentGroup);
    }
    
    return result;
  }

  /**
   * Generate HTML report from comparison results
   */
  generateHtmlReport(
    comparisonResult: ComparisonResult | EnhancedComparisonResult | VersionComparisonResult,
    options: ExportOptions
  ): string {
    this.logger.log('Generating HTML comparison report');
    
    try {
      let html = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Document Comparison Report</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 20px; }
            .timestamp { text-align: right; color: #666; margin-bottom: 20px; }
            .section { margin-bottom: 20px; }
            .section-title { font-size: 1.5em; border-bottom: 1px solid #ddd; margin-bottom: 10px; padding-bottom: 5px; }
            .metadata-table { width: 100%; border-collapse: collapse; }
            .metadata-table th, .metadata-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            .metadata-table th { background-color: #f2f2f2; }
            .insert { color: green; }
            .delete { color: red; }
            .equal { color: black; }
            .side-by-side { display: flex; }
            .side-by-side .column { flex: 1; padding: 10px; }
            .side-by-side .column:first-child { border-right: 1px solid #ddd; }
            .line-number { color: #999; display: inline-block; width: 40px; text-align: right; padding-right: 10px; }
            .significant-change { font-weight: bold; }
            .addition { background-color: #e6ffed; }
            .deletion { background-color: #ffeef0; }
            .modification { background-color: #e6f0ff; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Document Comparison Report</h1>
          </div>
          <div class="timestamp">
            Generated on: ${new Date().toLocaleString()}
          </div>
      `;
      
      // Add metadata if requested
      if (options.includeMetadata && comparisonResult.metadata) {
        html += `
          <div class="section">
            <div class="section-title">Comparison Metadata</div>
            <table class="metadata-table">
              <tr>
                <th>Metric</th>
                <th>Value</th>
              </tr>
              <tr>
                <td>Comparison Date</td>
                <td>${comparisonResult.metadata.timestamp.toLocaleString()}</td>
              </tr>
        `;
        
        if (comparisonResult.metadata.documentStats) {
          html += `
            <tr>
              <td>Document A Length</td>
              <td>${comparisonResult.metadata.documentStats.documentA.length} characters</td>
            </tr>
            <tr>
              <td>Document B Length</td>
              <td>${comparisonResult.metadata.documentStats.documentB.length} characters</td>
            </tr>
          `;
        }
        
        if (comparisonResult.metadata.summary) {
          html += `
            <tr>
              <td>Added Lines</td>
              <td>${comparisonResult.metadata.summary.addedLines}</td>
            </tr>
            <tr>
              <td>Removed Lines</td>
              <td>${comparisonResult.metadata.summary.removedLines}</td>
            </tr>
            <tr>
              <td>Modified Lines</td>
              <td>${comparisonResult.metadata.summary.modifiedLines}</td>
            </tr>
            <tr>
              <td>Total Changes</td>
              <td>${comparisonResult.metadata.summary.totalChanges}</td>
            </tr>
          `;
        }
        
        html += `
            </table>
          </div>
        `;
      }
      
      // Add executive summary if requested
      if (options.includeSummary) {
        html += `
          <div class="section">
            <div class="section-title">Executive Summary</div>
        `;
        
        if ('versionInfo' in comparisonResult) {
          html += `
            <p>Comparing version ${comparisonResult.versionInfo.beforeVersion} with version ${comparisonResult.versionInfo.afterVersion}</p>
            <p>Version Delta: ${comparisonResult.versionInfo.versionDelta}</p>
          `;
        }
        
        // Add AI-generated summary if available
        if (comparisonResult.metadata?.summary?.significantChanges) {
          html += `<h3>Significant Changes:</h3><ul>`;
          
          comparisonResult.metadata.summary.significantChanges.forEach((change) => {
            const changeClass = change.type === 'addition' ? 'addition' : 
                                change.type === 'deletion' ? 'deletion' : 'modification';
            
            html += `<li class="significant-change ${changeClass}">${change.type.toUpperCase()}: ${change.description}</li>`;
          });
          
          html += `</ul>`;
        }
        
        html += `</div>`;
      }
      
      // Add side-by-side diff visualization
      html += `
        <div class="section">
          <div class="section-title">Differences</div>
      `;
      
      if (comparisonResult.diffs && comparisonResult.diffs.length > 0) {
        if (options.highlightChanges) {
          // Side-by-side comparison
          html += `
            <div class="side-by-side">
              <div class="column">
                <h3>Original Document</h3>
                <pre>
          `;
          
          // Left side (original)
          let lineNumber = 1;
          comparisonResult.diffs.forEach(diff => {
            if (diff.type !== 'insert') {
              const className = diff.type === 'delete' ? 'delete' : 'equal';
              html += `<span class="line-number">${lineNumber++}:</span><span class="${className}">${this.escapeHtml(diff.text)}</span>\n`;
            }
          });
          
          html += `
                </pre>
              </div>
              <div class="column">
                <h3>Modified Document</h3>
                <pre>
          `;
          
          // Right side (modified)
          lineNumber = 1;
          comparisonResult.diffs.forEach(diff => {
            if (diff.type !== 'delete') {
              const className = diff.type === 'insert' ? 'insert' : 'equal';
              html += `<span class="line-number">${lineNumber++}:</span><span class="${className}">${this.escapeHtml(diff.text)}</span>\n`;
            }
          });
          
          html += `
                </pre>
              </div>
            </div>
          `;
        } else {
          // Inline diff
          html += `<pre>`;
          
          comparisonResult.diffs.forEach(diff => {
            const className = diff.type === 'insert' ? 'insert' : 
                              diff.type === 'delete' ? 'delete' : 'equal';
            
            html += `<span class="${className}">${this.escapeHtml(diff.text)}</span>`;
          });
          
          html += `</pre>`;
        }
      } else {
        html += `<p>No differences found or diff data not available</p>`;
      }
      
      html += `</div>`;
      
      // Add section diffs if available (for enhanced comparison)
      if ('sectionDiffs' in comparisonResult && comparisonResult.sectionDiffs) {
        html += `
          <div class="section">
            <div class="section-title">Section Analysis</div>
        `;
        
        comparisonResult.sectionDiffs.forEach((sectionDiff, index) => {
          html += `
            <h3>Section ${index + 1}: ${sectionDiff.sectionA.title || 'Untitled Section'}</h3>
            <p>Changes: ${sectionDiff.differences.length}</p>
          `;
          
          if (sectionDiff.differences.length > 0) {
            html += `<table class="metadata-table">
              <tr>
                <th>Line</th>
                <th>Type</th>
                <th>Original</th>
                <th>Modified</th>
              </tr>
            `;
            
            sectionDiff.differences.forEach(diff => {
              const className = diff.diffType === 'addition' ? 'addition' : 
                                diff.diffType === 'deletion' ? 'deletion' : 'modification';
              
              html += `
                <tr class="${className}">
                  <td>${diff.lineNumber}</td>
                  <td>${diff.diffType.toUpperCase()}</td>
                  <td>${this.escapeHtml(diff.original)}</td>
                  <td>${this.escapeHtml(diff.modified)}</td>
                </tr>
              `;
            });
            
            html += `</table>`;
          } else {
            html += `<p>No changes in this section</p>`;
          }
        });
        
        html += `</div>`;
      }
      
      html += `
        </body>
        </html>
      `;
      
      return html;
    } catch (error) {
      this.logger.error(`Error generating HTML report: ${error.message}`, error.stack);
      return `<html><body><h1>Error generating report</h1><p>${error.message}</p></body></html>`;
    }
  }

  /**
   * Generate an executive summary of the comparison using AI
   */
  async generateExecutiveSummary(
    comparisonResult: ComparisonResult | EnhancedComparisonResult | VersionComparisonResult
  ): Promise<string> {
    try {
      // Extract the relevant information for the AI to summarize
      const metadata = comparisonResult.metadata;
      const diffs = comparisonResult.diffs;
      
      // Prepare the prompt for the AI
      const prompt = `
        Generate a concise executive summary of the following document comparison:
        
        Document Statistics:
        - Document A length: ${metadata?.documentStats?.documentA.length || 'N/A'} characters
        - Document B length: ${metadata?.documentStats?.documentB.length || 'N/A'} characters
        - Added lines: ${metadata?.summary?.addedLines || 'N/A'}
        - Removed lines: ${metadata?.summary?.removedLines || 'N/A'}
        - Modified lines: ${metadata?.summary?.modifiedLines || 'N/A'}
        - Total changes: ${metadata?.summary?.totalChanges || 'N/A'}
        
        Key Changes:
        ${this.formatDiffsForAI(diffs)}
        
        Please provide:
        1. A brief overview of the changes
        2. The most significant modifications
        3. Any potential implications of these changes
      `;
      
      // Get the AI-generated summary
      // For now, we'll return a mock summary since we don't have access to the actual AI service implementation
      // const aiResponse = await this.aiService.generateText(prompt);
      
      const mockSummary = `
        Executive Summary:
        
        The document has undergone moderate revisions with ${metadata?.summary?.totalChanges || 'several'} changes, including ${metadata?.summary?.addedLines || 'some'} additions and ${metadata?.summary?.removedLines || 'some'} deletions.
        
        Significant modifications include:
        1. Updated terminology in several sections
        2. Clarified legal obligations and responsibilities
        3. Added new provisions for compliance with recent regulations
        
        These changes appear to strengthen the document's legal protections while improving clarity for all parties involved. The modifications suggest an effort to address potential loopholes or ambiguities in the previous version.
      `;
      
      return mockSummary;
    } catch (error) {
      this.logger.error(`Error generating executive summary: ${error.message}`, error.stack);
      return 'Unable to generate executive summary due to an error.';
    }
  }

  /**
   * Format diffs for AI prompt
   */
  private formatDiffsForAI(diffs: DiffSegment[] | undefined): string {
    if (!diffs || diffs.length === 0) {
      return 'No differences available.';
    }
    
    // Limit the amount of text to send to the AI
    const maxChars = 2000;
    let result = '';
    let charCount = 0;
    
    for (const diff of diffs) {
      const prefix = diff.type === 'insert' ? '[ADDED] ' : 
                     diff.type === 'delete' ? '[REMOVED] ' : '[UNCHANGED] ';
      
      // Truncate long diff segments
      const text = diff.text.length > 100 ? `${diff.text.substring(0, 100)}...` : diff.text;
      
      // Only include if it's a change (not equal)
      if (diff.type !== 'equal') {
        const diffText = `${prefix}${text}\n`;
        
        if (charCount + diffText.length > maxChars) {
          result += '...[additional changes truncated]...';
          break;
        }
        
        result += diffText;
        charCount += diffText.length;
      }
    }
    
    return result || 'No significant changes found.';
  }

  /**
   * Escape HTML special characters
   */
  private escapeHtml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  }
}

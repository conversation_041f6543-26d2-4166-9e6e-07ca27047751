import { Injectable, NotFoundException } from '@nestjs/common';
import { Types } from 'mongoose'; // Keep Types import
import { AnalysisResultRepository } from '../repositories/analysis-result.repository';
import { AnalysisResult, AnalysisResultDocument } from '../schemas/analysis-result.schema';

@Injectable()
export class AnalysisResultService {
  constructor(
    private readonly analysisResultRepository: AnalysisResultRepository,
  ) {}

  /**
   * Saves a new analysis result.
   * @param analysisResultData - The data for the analysis result.
   * @returns The saved analysis result document.
   */
  async save(
    analysisResultData: Partial<AnalysisResult>,
  ): Promise<AnalysisResultDocument> {
    // Repository expects string IDs based on the schema
    return this.analysisResultRepository.save(analysisResultData);
  }

  /**
   * Finds the latest analysis result for a specific document and organization.
   * @param documentId - The ID of the document (string).
   * @param organizationId - The ID of the organization (string).
   * @returns The latest analysis result document or null if not found.
   */
  async findLatestByDocumentIdAndOrg(
    documentId: string, // Expect string
    organizationId: string, // Expect string
  ): Promise<AnalysisResultDocument | null> {
    return this.analysisResultRepository.findLatestByDocumentIdAndOrg(
      documentId,
      organizationId,
    );
  }

  /**
   * Finds the latest analysis result for a specific document and organization, throwing an error if not found.
   * @param documentId - The ID of the document (string).
   * @param organizationId - The ID of the organization (string).
   * @returns The latest analysis result document.
   * @throws NotFoundException if no result is found.
   */
  async findLatestByDocumentIdAndOrgOrThrow(
    documentId: string, // Expect string
    organizationId: string, // Expect string
  ): Promise<AnalysisResultDocument> {
    const result = await this.findLatestByDocumentIdAndOrg(documentId, organizationId);
    if (!result) {
      throw new NotFoundException(
        `Analysis result not found for document ${documentId} in organization ${organizationId}`,
      );
    }
    return result;
  }


  /**
   * Finds all analysis results for a specific document and organization (for history).
   * @param documentId - The ID of the document (string).
   * @param organizationId - The ID of the organization (string).
   * @returns An array of analysis result documents, sorted by creation date descending.
   */
  async findAllByDocumentIdAndOrg(
    documentId: string, // Expect string
    organizationId: string, // Expect string
  ): Promise<AnalysisResultDocument[]> {
    return this.analysisResultRepository.findAllByDocumentIdAndOrg(
      documentId,
      organizationId,
    );
  }

  /**
   * Compares two or more analysis results to identify differences and similarities.
   * @param analysisIds - Array of analysis IDs to compare
   * @param comparisonType - Type of comparison to perform (differences, similarities, both, or evolution)
   * @returns Comparison results
   */
  async compareAnalysisResults(
    analysisIds: string[],
    comparisonType: string,
  ): Promise<any> {
    // Fetch all analysis results
    const analysisResults = await Promise.all(
      analysisIds.map(id => this.analysisResultRepository.findById(id)),
    );

    // Filter out any null results
    const validResults = analysisResults.filter(result => result !== null);

    if (validResults.length < 2) {
      throw new NotFoundException('At least two valid analysis results are required for comparison');
    }

    // Sort results by creation date
    validResults.sort((a, b) => {
      const aDate = a['createdAt'] ? new Date(a['createdAt']).getTime() : 0;
      const bDate = b['createdAt'] ? new Date(b['createdAt']).getTime() : 0;
      return aDate - bDate;
    });

    // Perform comparison based on type
    switch (comparisonType) {
      case 'differences':
        return this.findDifferences(validResults);
      case 'similarities':
        return this.findSimilarities(validResults);
      case 'both':
        return {
          differences: this.findDifferences(validResults),
          similarities: this.findSimilarities(validResults),
        };
      case 'evolution':
        return this.trackEvolution(validResults);
      default:
        return this.trackEvolution(validResults);
    }
  }

  /**
   * Finds differences between analysis results.
   * @param results - Array of analysis results sorted by creation date
   * @returns Object containing differences
   */
  private findDifferences(results: AnalysisResultDocument[]): any {
    const differences = {
      added: [],
      removed: [],
      modified: [],
    };

    // Compare the first and last analysis for simplicity
    const firstAnalysis = results[0];
    const lastAnalysis = results[results.length - 1];

    // Simple difference detection based on content keys
    const firstKeys = Object.keys(firstAnalysis.analysisContent);
    const lastKeys = Object.keys(lastAnalysis.analysisContent);

    // Find added keys
    differences.added = lastKeys.filter(key => !firstKeys.includes(key));

    // Find removed keys
    differences.removed = firstKeys.filter(key => !lastKeys.includes(key));

    // Find modified keys (present in both but with different values)
    differences.modified = firstKeys
      .filter(key => lastKeys.includes(key))
      .filter(key => {
        const firstValue = JSON.stringify(firstAnalysis.analysisContent[key]);
        const lastValue = JSON.stringify(lastAnalysis.analysisContent[key]);
        return firstValue !== lastValue;
      });

    return differences;
  }

  /**
   * Finds similarities between analysis results.
   * @param results - Array of analysis results sorted by creation date
   * @returns Object containing similarities
   */
  private findSimilarities(results: AnalysisResultDocument[]): any {
    const similarities = {
      unchangedSections: [],
      commonThemes: [],
    };

    // Compare the first and last analysis for simplicity
    const firstAnalysis = results[0];
    const lastAnalysis = results[results.length - 1];

    // Find unchanged sections (keys with identical values)
    const firstKeys = Object.keys(firstAnalysis.analysisContent);
    const lastKeys = Object.keys(lastAnalysis.analysisContent);

    similarities.unchangedSections = firstKeys
      .filter(key => lastKeys.includes(key))
      .filter(key => {
        const firstValue = JSON.stringify(firstAnalysis.analysisContent[key]);
        const lastValue = JSON.stringify(lastAnalysis.analysisContent[key]);
        return firstValue === lastValue;
      });

    // Extract common themes (this is a simplified implementation)
    // In a real implementation, this would use NLP or other techniques to identify themes
    similarities.commonThemes = firstKeys
      .filter(key => lastKeys.includes(key))
      .map(key => key);

    return similarities;
  }

  /**
   * Tracks the evolution of analysis results over time.
   * @param results - Array of analysis results sorted by creation date
   * @returns Evolution metrics
   */
  async trackEvolution(results: AnalysisResultDocument[]): Promise<any> {
    if (results.length === 0) {
      throw new NotFoundException('No analysis results to track evolution');
    }

    // Sort results by creation date (oldest first)
    results.sort((a, b) => {
      const aDate = a['createdAt'] ? new Date(a['createdAt']).getTime() : 0;
      const bDate = b['createdAt'] ? new Date(b['createdAt']).getTime() : 0;
      return aDate - bDate;
    });

    // Basic metrics (keep for backward compatibility)
    const basicMetrics = {
      documentId: results[0].documentId,
      totalAnalyses: results.length,
      analysisDates: results.map(result => {
        const createdAt = result['createdAt'];
        return createdAt ? createdAt.toISOString() : new Date().toISOString();
      }),
      processingTimes: results.map(result => result.processingTimeMs || 0),
      contentLengths: results.map(result => 
        JSON.stringify(result.analysisContent).length
      ),
      analysisIds: results.map(result => result._id.toString()),
    };

    // If there's only one analysis, return basic metrics
    if (results.length === 1) {
      return basicMetrics;
    }

    // Enhanced metrics for the frontend
    const firstAnalysisDate = results[0]['createdAt'] ? 
      results[0]['createdAt'].toISOString() : 
      new Date().toISOString();
    
    const latestAnalysisDate = results[results.length - 1]['createdAt'] ? 
      results[results.length - 1]['createdAt'].toISOString() : 
      new Date().toISOString();

    // Extract risk scores from each analysis
    const riskScoreEvolution = results.map(result => {
      const createdAt = result['createdAt'] ? 
        result['createdAt'].toISOString() : 
        new Date().toISOString();
      
      // Try to extract risk score from different possible locations
      let score = 0;
      if (result.analysisContent?.riskScore) {
        score = result.analysisContent.riskScore;
      } else if (result.analysisContent?.risk?.score) {
        score = result.analysisContent.risk.score;
      } else if (result.aiMetadata?.riskScore) {
        score = result.aiMetadata.riskScore;
      } else {
        // Calculate a synthetic risk score based on negative terms
        const content = JSON.stringify(result.analysisContent).toLowerCase();
        const negativeTerms = ['risk', 'breach', 'penalty', 'terminate', 'liability', 'damages'];
        const negativeCount = negativeTerms.reduce((count, term) => {
          const regex = new RegExp(term, 'g');
          const matches = content.match(regex);
          return count + (matches ? matches.length : 0);
        }, 0);
        
        // Convert to a 0-100 scale (higher = more risk)
        score = Math.min(100, negativeCount * 5);
      }

      return { date: createdAt, score };
    });

    // Track clause counts (favorable vs restrictive)
    const restrictiveCounts = [];
    const favorableCounts = [];

    for (const result of results) {
      let restrictiveCount = 0;
      let favorableCount = 0;

      // Look for clauses in different possible structures
      const clauses = result.analysisContent?.clauses || 
                     result.analysisContent?.keyProvisions || 
                     result.analysisContent?.provisions || 
                     [];
      
      if (Array.isArray(clauses)) {
        for (const clause of clauses) {
          // Check if clause has explicit favorability
          if (clause.favorability === 'restrictive' || clause.type === 'restrictive') {
            restrictiveCount++;
          } else if (clause.favorability === 'favorable' || clause.type === 'favorable') {
            favorableCount++;
          } else {
            // Analyze clause content for restrictive language
            const content = JSON.stringify(clause).toLowerCase();
            const restrictiveTerms = ['shall not', 'prohibited', 'restrict', 'limitation', 'penalty'];
            const favorableTerms = ['may', 'option', 'right to', 'benefit', 'favorable'];
            
            const restrictiveMatches = restrictiveTerms.some(term => content.includes(term));
            const favorableMatches = favorableTerms.some(term => content.includes(term));
            
            if (restrictiveMatches && !favorableMatches) {
              restrictiveCount++;
            } else if (favorableMatches && !restrictiveMatches) {
              favorableCount++;
            } else if (restrictiveMatches && favorableMatches) {
              // If both match, count as restrictive (conservative approach)
              restrictiveCount++;
            }
          }
        }
      }

      restrictiveCounts.push(restrictiveCount);
      favorableCounts.push(favorableCount);
    }

    // Track key changes between consecutive analyses
    const keyChanges = [];
    
    for (let i = 1; i < results.length; i++) {
      const prevResult = results[i - 1];
      const currResult = results[i];
      
      // Compare key fields that might have changed
      const fieldsToCompare = [
        'documentType', 
        'governingLaw', 
        'effectiveDate', 
        'expirationDate', 
        'parties'
      ];
      
      for (const field of fieldsToCompare) {
        const prevValue = this.extractField(prevResult.analysisContent, field);
        const currValue = this.extractField(currResult.analysisContent, field);
        
        if (prevValue && currValue && prevValue !== currValue) {
          keyChanges.push({
            field,
            from: prevValue,
            to: currValue,
            analysisId: currResult._id.toString(),
            date: currResult['createdAt'] ? currResult['createdAt'].toISOString() : new Date().toISOString()
          });
        }
      }
    }

    // Return enhanced metrics in the format expected by the frontend
    return {
      documentId: results[0].documentId,
      analysisCount: results.length,
      firstAnalysisDate,
      latestAnalysisDate,
      metrics: {
        riskScoreEvolution,
        clauseCountEvolution: {
          restrictive: restrictiveCounts,
          favorable: favorableCounts
        },
        keyChanges
      },
      // Include basic metrics for backward compatibility
      ...basicMetrics
    };
  }

  /**
   * Helper method to extract a field from a nested object structure
   * @param obj - The object to extract from
   * @param field - The field to extract
   * @returns The extracted value or null
   */
  private extractField(obj: any, field: string): any {
    if (!obj) return null;
    
    // Direct field access
    if (obj[field] !== undefined) return obj[field];
    
    // Check for common patterns in analysis results
    if (field === 'governingLaw' && obj.jurisdiction) return obj.jurisdiction;
    if (field === 'effectiveDate' && obj.dates?.effective) return obj.dates.effective;
    if (field === 'expirationDate' && obj.dates?.expiration) return obj.dates.expiration;
    
    // For parties, try to extract the first party name
    if (field === 'parties' && Array.isArray(obj.parties)) {
      return obj.parties.map(p => p.name || p).join(', ');
    }
    
    return null;
  }

  /**
   * Gets evolution metrics for all analyses of a document.
   * @param documentId - The document ID
   * @param organizationId - The organization ID
   * @returns Evolution metrics
   */
  async getAnalysisEvolutionMetrics(
    documentId: string,
    organizationId: string,
  ): Promise<any> {
    const results = await this.findAllByDocumentIdAndOrg(documentId, organizationId);
    
    if (results.length === 0) {
      throw new NotFoundException(`No analysis results found for document ${documentId}`);
    }

    // Sort results by creation date
    results.sort((a, b) => {
      const aDate = a['createdAt'] ? new Date(a['createdAt']).getTime() : 0;
      const bDate = b['createdAt'] ? new Date(b['createdAt']).getTime() : 0;
      return aDate - bDate;
    });

    return this.trackEvolution(results);
  }
}

import { Injectable, Logger } from '@nestjs/common';
import DiffMatchPatch from 'diff-match-patch';
import { AnalyticsService } from '../../analytics/services/analytics.service';
import { AIService } from '../../ai/services/ai.service';
import {
  ComparisonResult,
  DiffMetadata,
  DiffSegment,
  ExportOptions,
} from '../interfaces/document-comparison.interface';
import { TextFragmentComparisonResult } from '../interfaces/clause-library.interface';

@Injectable()
export class DocumentComparisonService {
  private readonly logger = new Logger(DocumentComparisonService.name);

  constructor(
    private readonly analyticsService: AnalyticsService,
    private readonly aiService: AIService,
  ) {}

  public async compareTextFragments(
    fragment1: string,
    fragment2: string,
    matchingPreferences?: {
      ignoreNumbering?: boolean;
      useKeywordMatching?: boolean;
      handleSpecialFormats?: boolean;
      boostSimilarContent?: boolean;
    }
  ): Promise<TextFragmentComparisonResult> {
    try {
      this.logger.debug('=============== Starting Text Comparison ===============');
      this.logger.debug(`Fragment 1 length: ${fragment1?.length || 0}`);
      this.logger.debug(`Fragment 2 length: ${fragment2?.length || 0}`);

      if (!fragment1 || !fragment2) {
        this.logger.warn('One or both fragments are empty');
        return { similarity: 0 };
      }

      // Set default preferences if not provided
      const preferences = {
        ignoreNumbering: matchingPreferences?.ignoreNumbering !== false, // Default to true
        useKeywordMatching: matchingPreferences?.useKeywordMatching !== false, // Default to true
        handleSpecialFormats: matchingPreferences?.handleSpecialFormats !== false, // Default to true
        boostSimilarContent: matchingPreferences?.boostSimilarContent !== false // Default to true
      };
      
      this.logger.debug('Using matching preferences:', preferences);
      
      // Check if fragment2 (template) contains JSON or markdown formatting
      const hasSpecialFormat = fragment2.includes('```') || 
                              fragment2.includes('{"') || 
                              fragment2.includes('```json');
      
      if (hasSpecialFormat && preferences.handleSpecialFormats) {
        this.logger.debug('Template contains JSON or markdown formatting - using content-based matching');
        // For templates with special formatting, extract plain text content if possible
        // or use a more lenient similarity threshold
        return this.handleSpecialFormatComparison(fragment1, fragment2);
      }

      // Clean the text fragments with our improved cleaning method
      // Remove section numbering from document content for better matching if enabled
      const cleaned1 = this.cleanText(fragment1, preferences.ignoreNumbering); // Remove section numbering if enabled
      const cleaned2 = this.cleanText(fragment2, false); // Keep template as-is
      
      this.logger.debug('Original fragments:');
      this.logger.debug(`Document preview: ${fragment1.substring(0, 200)}...`);
      this.logger.debug(`Template preview: ${fragment2.substring(0, 200)}...`);

      this.logger.debug('Cleaned text samples:');
      this.logger.debug(`Fragment 1 (first 100 chars): ${cleaned1.substring(0, 100)}...`);
      this.logger.debug(`Fragment 2 (first 100 chars): ${cleaned2.substring(0, 100)}...`);

      const dmp = new DiffMatchPatch();
      const diffs = dmp.diff_main(cleaned1, cleaned2);
      dmp.diff_cleanupSemantic(diffs);

      this.logger.debug(`Generated ${diffs.length} diff segments`);

      // Calculate similarity score based on matching content
      let matchingChars = 0;
      let currentIndex = 0;
      let longestMatch = { startIndex: 0, endIndex: 0, length: 0 };
      let currentMatchStart = 0;
      let inMatch = false;

      for (const [op, text] of diffs) {
        this.logger.debug(`Diff segment - Operation: ${op}, Length: ${text.length}`);
        
        if (op === 0) { // Equal
          matchingChars += text.length;
          
          if (!inMatch) {
            currentMatchStart = currentIndex;
            inMatch = true;
          }
          
          if (text.length > longestMatch.length) {
            longestMatch = {
              startIndex: currentMatchStart,
              endIndex: currentIndex + text.length,
              length: text.length
            };
          }
        } else {
          inMatch = false;
        }
        
        if (op !== -1) {
          currentIndex += text.length;
        }
      }

      const totalChars = Math.max(cleaned1.length, cleaned2.length);
      
      // Apply a boost factor to improve matching likelihood if enabled
      const boostFactor = preferences.boostSimilarContent ? 1.25 : 1.0; // Increase similarity by 25% if enabled
      let similarity = totalChars > 0 ? (matchingChars / totalChars) * boostFactor : 0;
      
      // Cap at 1.0 maximum
      similarity = Math.min(similarity, 1.0);
      
      let boostedKeywordSimilarity = 0;
      
      // Try keyword-based matching as an alternative approach if enabled
      if (preferences.useKeywordMatching) {
        this.logger.debug('Trying keyword-based matching as an alternative approach');
        
        // Split into words and filter out short words
        const words1 = cleaned1.split(' ').filter(w => w.length > 4); // Increased minimum word length for more precision
        const words2 = cleaned2.split(' ').filter(w => w.length > 4);
        
        // Count matching significant words
        let matchCount = 0;
        const uniqueWords = new Set<string>();
        
        // Add all significant words from template to a set
        words2.forEach(word => uniqueWords.add(word));
        
        // Count how many significant words from the document match the template
        for (const word of words1) {
          if (uniqueWords.has(word)) {
            matchCount++;
            uniqueWords.delete(word); // Count each match only once
          }
        }
        
        // Calculate keyword similarity
        const totalUniqueWords = words1.length + words2.length - matchCount;
        
        // Require a minimum number of matching words for a good similarity score
        const minMatchCount = 5; // At least 5 significant words should match for a good score
        const matchCountFactor = matchCount >= minMatchCount ? 1.0 : matchCount / minMatchCount;
        
        // Calculate similarity based on the ratio of matching words to total words
        const keywordSimilarity = totalUniqueWords > 0 ? 
          (matchCount / Math.min(words1.length, words2.length)) * matchCountFactor : 0;
        
        // Apply a moderate boost factor for keyword matching if enabled
        const keywordBoostFactor = preferences.boostSimilarContent ? 1.5 : 1.2; // Reduced boost factor for more realistic scores
        boostedKeywordSimilarity = Math.min(keywordSimilarity * keywordBoostFactor, 1.0);
        
        this.logger.debug(`Keyword matching: ${matchCount} matches out of ${words1.length}/${words2.length} words`);
        this.logger.debug(`Raw keyword similarity: ${keywordSimilarity}`);
        this.logger.debug(`Boosted keyword similarity: ${boostedKeywordSimilarity}`);
        
        // Use the higher of the two similarity scores
        if (boostedKeywordSimilarity > similarity) {
          this.logger.debug(`Using keyword similarity (${boostedKeywordSimilarity}) instead of diff similarity (${similarity})`);
          similarity = boostedKeywordSimilarity;
        }
      }

      this.logger.debug('Similarity calculation:');
      this.logger.debug(`- Matching characters: ${matchingChars}`);
      this.logger.debug(`- Total characters: ${totalChars}`);
      this.logger.debug(`- Raw similarity: ${matchingChars / totalChars}`);
      this.logger.debug(`- Final similarity: ${similarity}`);

      if (longestMatch.length > 0) {
        this.logger.debug('Longest matching segment:');
        this.logger.debug(`- Start: ${longestMatch.startIndex}`);
        this.logger.debug(`- End: ${longestMatch.endIndex}`);
        this.logger.debug(`- Length: ${longestMatch.length}`);
        this.logger.debug(`- Content: "${cleaned1.substring(longestMatch.startIndex, longestMatch.endIndex)}"`);
      }

      const result: TextFragmentComparisonResult = {
        similarity,
        matchPositions: longestMatch.length > 0 ? [{
          startIndex: longestMatch.startIndex,
          endIndex: longestMatch.endIndex
        }] : undefined
      };

      this.logger.debug('Final comparison result:', result);
      return result;

    } catch (error) {
      this.logger.error(`Error comparing text fragments: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
 * Clean and normalize text for comparison
 * @param text The text to clean
 * @param removeNumbering Whether to remove section numbering (e.g., "5.1", "5.2")
 */
private cleanText(text: string, removeNumbering: boolean = false): string {
  let cleaned = text;
  
  // Remove section numbering if requested
  if (removeNumbering) {
    // Remove patterns like "5.", "5.1", "5.1.2" at the beginning of lines or after newlines
    cleaned = cleaned.replace(/(^|\n)\s*\d+(\.\d+)*\s+/g, '$1');
  }
  
  return cleaned
    .replace(/\s+/g, ' ') // Normalize whitespace
    .replace(/[^\w\s.,;:()"'-]/g, '') // Keep more punctuation
    .toLowerCase() // Convert to lowercase
    .trim(); // Remove leading/trailing whitespace
}

  /**
   * Special handling for templates with JSON or markdown formatting
   * Attempts to extract plain text content or uses a more lenient matching approach
   */
  private handleSpecialFormatComparison(documentContent: string, templateContent: string): TextFragmentComparisonResult {
    this.logger.debug('Using special format comparison for template with JSON/markdown');
    
    try {
      // Try to extract plain text from JSON if possible
      if (templateContent.includes('{"') || templateContent.includes('```json')) {
        let extractedText = '';
        
        // Extract text from JSON structure if possible
        try {
          // Remove markdown code block markers if present
          const jsonContent = templateContent.replace(/```json\n|```/g, '');
          const parsed = JSON.parse(jsonContent);
          
          // Extract text from common JSON fields that might contain relevant text
          if (parsed.content) extractedText += parsed.content + ' ';
          if (parsed.title) extractedText += parsed.title + ' ';
          if (parsed.contentSummary) extractedText += parsed.contentSummary + ' ';
          
          // Extract text from arrays like keyPoints
          if (parsed.keyPoints && Array.isArray(parsed.keyPoints)) {
            extractedText += parsed.keyPoints.join(' ') + ' ';
          }
          
          this.logger.debug(`Extracted text from JSON: ${extractedText.substring(0, 100)}...`);
        } catch (e) {
          this.logger.debug(`Failed to parse JSON: ${e.message}`);
          // If JSON parsing fails, use the original template content
          extractedText = templateContent;
        }
        
        if (extractedText) {
          // Use the extracted text for comparison with a boost
          const cleaned1 = this.cleanText(documentContent);
          const cleaned2 = this.cleanText(extractedText);
          
          // Simple keyword-based matching for JSON templates
          const words2 = cleaned2.split(' ').filter(w => w.length > 4); // Only use significant words
          let matchCount = 0;
          
          for (const word of words2) {
            if (cleaned1.includes(word)) matchCount++;
          }
          
          const matchRatio = words2.length > 0 ? matchCount / words2.length : 0;
          // Apply an additional boost for JSON templates
          const boostedSimilarity = Math.min(matchRatio * 1.5, 1.0);
          
          this.logger.debug(`JSON template matching: ${matchCount}/${words2.length} keywords matched`);
          this.logger.debug(`Boosted similarity: ${boostedSimilarity}`);
          
          // Find a position in the document that contains the most matches
          const startIndex = Math.floor(documentContent.length / 3); // Approximate position
          const endIndex = Math.min(startIndex + 200, documentContent.length);
          
          return {
            similarity: boostedSimilarity,
            matchPositions: [{ startIndex, endIndex }]
          };
        }
      }
      
      // For other special formats or if extraction failed, use a more lenient approach
      // with our regular comparison but with a boost
      const cleaned1 = this.cleanText(documentContent);
      const cleaned2 = this.cleanText(templateContent);
      
      const dmp = new DiffMatchPatch();
      const diffs = dmp.diff_main(cleaned1, cleaned2);
      dmp.diff_cleanupSemantic(diffs);
      
      let matchingChars = 0;
      for (const [op, text] of diffs) {
        if (op === 0) matchingChars += text.length;
      }
      
      const totalChars = Math.max(cleaned1.length, cleaned2.length);
      // Apply a higher boost factor for special format templates
      const specialBoostFactor = 1.75; // 75% boost
      const similarity = totalChars > 0 ? Math.min((matchingChars / totalChars) * specialBoostFactor, 1.0) : 0;
      
      this.logger.debug(`Special format template - raw similarity: ${matchingChars / totalChars}`);
      this.logger.debug(`Special format template - boosted similarity: ${similarity}`);
      
      // Approximate match position
      const startIndex = Math.floor(documentContent.length / 3);
      const endIndex = Math.min(startIndex + 200, documentContent.length);
      
      return {
        similarity,
        matchPositions: [{ startIndex, endIndex }]
      };
    } catch (error) {
      this.logger.error(`Error in special format comparison: ${error.message}`, error.stack);
      // Fallback to a default similarity
      return { similarity: 0.3 };
    }
  }

  public async compareDocuments(
    documentA: string,
    documentB: string,
    options?: {
      documentAId?: string;
      documentBId?: string;
      userId?: string;
      organizationId?: string;
      exportOptions?: ExportOptions;
    }
  ): Promise<ComparisonResult> {
    this.logger.log('Starting basic document comparison');

    try {
      this.logger.debug('Performing diff', {
        docALength: documentA.length,
        docBLength: documentB.length,
      });

      const dmp = new DiffMatchPatch();
      const rawDiffs = dmp.diff_main(documentA, documentB);
      dmp.diff_cleanupSemantic(rawDiffs);
      this.logger.debug(`Generated ${rawDiffs.length} raw diff segments`);

      const diffSegments: DiffSegment[] = rawDiffs.map(([op, text]) => ({
        type: op === 1 ? 'insert' : op === -1 ? 'delete' : 'equal',
        text: text,
      }));

      const metadata: DiffMetadata = this.calculateMetadata(documentA, documentB, diffSegments);

      const htmlVisualization = this.generateHtmlVisualization(diffSegments);

      // Record comparison in analytics
      if (options?.documentAId && options?.documentBId && options?.organizationId && options?.userId) {
        try {
          await this.analyticsService.trackClauseLibraryUsage(
            options.userId,
            options.organizationId,
            'document_comparison',
            {
              documentAId: options.documentAId,
              documentBId: options.documentBId,
              comparisonLevel: 'basic',
              timestamp: new Date()
            }
          );
          this.logger.log(`Recorded basic comparison between documents ${options.documentAId} and ${options.documentBId}`);
        } catch (analyticsError) {
          this.logger.error(`Failed to record comparison analytics: ${analyticsError.message}`, analyticsError.stack);
        }
      }

      this.logger.log('Basic document comparison completed successfully');

      const result: ComparisonResult = {
        diffs: diffSegments,
        metadata: metadata,
        visualization: {
          htmlDiff: htmlVisualization,
          colors: {
            addition: '#e6ffe6',
            deletion: '#ffe6e6',
            modification: '#fff5e6'
          }
        }
      };

      if (options?.exportOptions) {
        this.logger.warn('Export requested, but basic export is not fully implemented.');
      }

      return result;

    } catch (error) {
      this.logger.error(`Error comparing documents: ${error.message}`, error.stack);
      throw error;
    }
  }

  private calculateMetadata(docA: string, docB: string, diffs: DiffSegment[]): DiffMetadata {
    const statsA = this.getDocumentStats(docA);
    const statsB = this.getDocumentStats(docB);

    let addedLines = 0;
    let removedLines = 0;
    let modifiedLines = 0;

    diffs.forEach((seg, index) => {
      const lineCount = (seg.text.match(/\n/g) || []).length + (seg.text.endsWith('\n') ? 0 : 1);
      if (seg.type === 'insert') addedLines += lineCount;
      if (seg.type === 'delete') removedLines += lineCount;
      if (seg.type === 'insert' && index > 0 && diffs[index - 1].type === 'delete') {
        const modCount = Math.min((diffs[index - 1].text.match(/\n/g) || []).length + 1, lineCount);
        modifiedLines += modCount;
        addedLines -= modCount;
        removedLines -= modCount;
      }
    });

    return {
      timestamp: new Date(),
      documentStats: {
        documentA: {
          length: docA.length,
          wordCount: statsA.wordCount,
          charCount: statsA.charCount
        },
        documentB: {
          length: docB.length,
          wordCount: statsB.wordCount,
          charCount: statsB.charCount
        },
      },
      summary: {
        addedLines,
        removedLines,
        modifiedLines,
        totalChanges: addedLines + removedLines + modifiedLines,
      },
    };
  }

  private getDocumentStats(doc: string): { wordCount: number; charCount: number } {
    const words = doc.match(/\b\w+\b/g) || [];
    return {
      wordCount: words.length,
      charCount: doc.length,
    };
  }

  private generateHtmlVisualization(diffs: DiffSegment[]): string {
    let html = '<pre style="white-space: pre-wrap; word-wrap: break-word; font-family: monospace; line-height: 1.4;">';
    const colors = { insert: '#e6ffe6', delete: '#ffe6e6', equal: 'transparent' };
    diffs.forEach(part => {
      const style = `background-color: ${colors[part.type]};`;
      const escapedText = part.text
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;');
      html += `<span style="${style}">${escapedText}</span>`;
    });
    html += '</pre>';
    return html;
  }
}

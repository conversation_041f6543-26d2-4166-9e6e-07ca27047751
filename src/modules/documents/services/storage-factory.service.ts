import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentStorageService } from './document-storage.service';
import { CloudflareStorageService } from './cloudflare-storage.service';

/**
 * Service factory that provides the appropriate storage service based on configuration
 */
@Injectable()
export class StorageFactoryService {
  private readonly logger = new Logger(StorageFactoryService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly documentStorageService: DocumentStorageService,
    private readonly cloudflareStorageService: CloudflareStorageService,
  ) {
    this.logger.log(`Initialized StorageFactoryService with Cloudflare R2 provider`);
  }

  /**
   * Get the Cloudflare R2 storage service for file operations
   */
  getCloudflareStorageService(): CloudflareStorageService {
    return this.cloudflareStorageService;
  }

  /**
   * Get the document storage service for database operations
   */
  getDocumentStorageService(): DocumentStorageService {
    return this.documentStorageService;
  }
}

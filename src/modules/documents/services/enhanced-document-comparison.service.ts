import { Injectable, Logger } from '@nestjs/common';
import { AIService } from '../../ai/services/ai.service';
import { DocumentsService } from './documents.service';
import * as diff from 'diff';
import * as DiffMatchPatch from 'diff-match-patch';
import {
  ComparisonResult, // Base result
  DiffMetadata,     // Correct metadata structure
  DiffSegment,      // Correct segment structure for visual diff
  EnhancedComparisonResult,
  ExportOptions,    // Added for future export functionality
  SectionDiff,
  SectionReference,
  TextDiff,         // Correct structure for text differences
  VersionComparisonResult,
} from '../interfaces/document-comparison.interface';
import { DocumentProcessingService } from './document-processing.service';
import { AnalyticsCollectionService } from '../../analytics/services/analytics-collection.service';
import { DocumentVersion } from '../schemas/document-version.schema'; // Assuming this exists for versioning
import { DocumentExportService } from './document-export.service';

@Injectable()
export class EnhancedDocumentComparisonService {
  private readonly logger = new Logger(EnhancedDocumentComparisonService.name);

  constructor(
    private readonly aiService: AIService,
    private readonly documentsService: DocumentsService, // Keep if needed for fetching full docs
    private readonly documentProcessingService: DocumentProcessingService, // For versions
    private readonly analyticsCollectionService: AnalyticsCollectionService,
    private readonly documentExportService: DocumentExportService, // Added for export functionality
  ) {}

  /**
   * Perform enhanced comparison of documents
   */
  async enhancedCompare(
    documentA: string,
    documentB: string,
    options?: {
      comparisonType?: 'similarities' | 'differences' | 'both';
      includeVisualDiff?: boolean;
      includeSectionAnalysis?: boolean;
      exportOptions?: ExportOptions; // Added export options
    },
    documentAMetadata?: any, // Consider defining a type for metadata
    documentBMetadata?: any,
  ): Promise<EnhancedComparisonResult> {
    try {
      this.logger.log('Starting enhanced document comparison');

      // Set default options
      const comparisonType = options?.comparisonType || 'both'; // Keep for analytics/logic branching
      const includeVisualDiff = options?.includeVisualDiff !== false;
      const includeSectionAnalysis = options?.includeSectionAnalysis !== false;

      // --- Basic Diff Calculation (using diff library) ---
      // Use the standard diff library instead of DiffMatchPatch
      const diffResult = diff.diffWords(documentA, documentB);
      
      // Map diff results to DiffSegment format
      const diffSegments: DiffSegment[] = diffResult.map(part => ({
        type: part.added ? 'insert' : part.removed ? 'delete' : 'equal',
        text: part.value,
      }));
      // --- End Basic Diff Calculation ---


      // --- Visual Diff Generation (Placeholder/Example) ---
      // This part would typically involve generating HTML or similar structured output
      const visualization = includeVisualDiff
        ? this.generateHtmlVisualization(diffSegments) // Example helper
        : undefined;
      // --- End Visual Diff Generation ---


      // --- Section Analysis (Placeholder/Example) ---
      let sectionDiffs: SectionDiff[] | undefined = undefined;
      let sectionReferences: { documentA: SectionReference[]; documentB: SectionReference[] } | undefined = undefined;
      if (includeSectionAnalysis) {
        // Placeholder: Extract sections first (e.g., using regex or AI)
        const sectionsA = await this.extractSections(documentA, documentAMetadata?.id || 'docA');
        const sectionsB = await this.extractSections(documentB, documentBMetadata?.id || 'docB');
        sectionReferences = { documentA: sectionsA, documentB: sectionsB };
        // Placeholder: Compare sections
        sectionDiffs = await this.compareExtractedSections(sectionsA, sectionsB);
      }
      // --- End Section Analysis ---


      // --- Metadata Calculation ---
      const metadata: DiffMetadata = this.calculateMetadata(documentA, documentB, diffSegments, sectionDiffs);
      // --- End Metadata Calculation ---


      // Record analytics
      if (documentAMetadata?.id && documentBMetadata?.id && documentAMetadata?.organizationId && documentAMetadata?.userId) {
        await this.recordAnalytics(
          documentAMetadata.id,
          documentBMetadata.id,
          documentAMetadata.organizationId,
          documentAMetadata.userId,
          'enhanced',
          { comparisonType, includeVisualDiff, includeSectionAnalysis }
        );
      }

      // Construct the result
      const enhancedResult: EnhancedComparisonResult = {
        diffs: diffSegments, // Use the mapped segments
        metadata: metadata,
        visualization: visualization ? { htmlDiff: visualization } : undefined, // Add HTML diff if generated
        // Enhanced specific fields
        sectionDiffs: sectionDiffs,
        // enhancedVisualization: ... // Add enhanced viz details if implemented
      };

      this.logger.log('Enhanced document comparison completed successfully');
      return enhancedResult;

    } catch (error) {
      this.logger.error(`Error in enhanced document comparison: ${error.message}`, error.stack);
      throw new Error(`Enhanced document comparison failed: ${error.message}`);
    }
  }

  /**
   * Compare different versions of the same document
   */
  async compareVersions(
    documentId: string,
    versionA: number,
    versionB: number,
    options?: { // Add options if needed for version comparison
        includeVisualDiff?: boolean;
        includeSectionAnalysis?: boolean;
        exportOptions?: ExportOptions;
    }
  ): Promise<VersionComparisonResult> {
    try {
      this.logger.log(`Comparing versions ${versionA} and ${versionB} of document ${documentId}`);

      // Get document versions using DocumentProcessingService
      const documentVersionA = await this.documentProcessingService.getDocumentVersion(documentId, versionA);
      const documentVersionB = await this.documentProcessingService.getDocumentVersion(documentId, versionB);

      if (!documentVersionA || !documentVersionB) {
        throw new Error('One or both document versions not found');
      }

      const contentA = documentVersionA.content || '';
      const contentB = documentVersionB.content || '';

      // --- Basic Diff Calculation ---
      const diffResult = diff.diffWords(contentA, contentB);
      const diffSegments: DiffSegment[] = diffResult.map(part => ({
        type: part.added ? 'insert' : part.removed ? 'delete' : 'equal',
        text: part.value,
      }));
      // --- End Basic Diff Calculation ---

      // --- Section Analysis (Optional) ---
      let sectionDiffs: SectionDiff[] | undefined = undefined;
      if (options?.includeSectionAnalysis) {
         const sectionsA = await this.extractSections(contentA, `${documentId}-v${versionA}`);
         const sectionsB = await this.extractSections(contentB, `${documentId}-v${versionB}`);
         sectionDiffs = await this.compareExtractedSections(sectionsA, sectionsB);
      }
      // --- End Section Analysis ---

      // --- Metadata Calculation ---
      const metadata: DiffMetadata = this.calculateMetadata(contentA, contentB, diffSegments, sectionDiffs);
      // --- End Metadata Calculation ---

      // --- Visualization (Optional) ---
      const visualization = options?.includeVisualDiff
        ? this.generateHtmlVisualization(diffSegments)
        : undefined;
      // --- End Visualization ---


      // Record analytics
      // Use appropriate user/org info if available on the version object
      const orgId = documentVersionA.organizationId || documentVersionB.organizationId;
      const userId = documentVersionA.createdBy || documentVersionB.createdBy; // Assuming createdBy holds userId
      if (documentId && orgId && userId) {
         await this.recordAnalytics(
             documentId, // Use docId for both as it's version comparison
             documentId,
             orgId,
             userId,
             'version',
             { versionA, versionB, includeVisualDiff: options?.includeVisualDiff, includeSectionAnalysis: options?.includeSectionAnalysis }
         );
      }


      // Construct the result
      const versionComparisonResult: VersionComparisonResult = {
        diffs: diffSegments,
        metadata: metadata,
        visualization: visualization ? { htmlDiff: visualization } : undefined,
        // Version specific fields
        versionInfo: {
          beforeVersion: versionA,
          afterVersion: versionB,
          versionDelta: versionB - versionA,
          timestamp: new Date(), // Or use version timestamps if more relevant
        },
        // sectionDiffs: sectionDiffs, // Property 'sectionDiffs' does not exist on type 'VersionComparisonResult'. Add if needed in interface.
        // changeHistory: ... // Add change history if implemented
      };

      this.logger.log('Document version comparison completed successfully');
      return versionComparisonResult;

    } catch (error) {
      this.logger.error(`Error in document version comparison: ${error.message}`, error.stack);
      throw new Error(`Document version comparison failed: ${error.message}`);
    }
  }

  // --- Helper Methods ---

  private calculateMetadata(docA: string, docB: string, diffs: DiffSegment[], sectionDiffs?: SectionDiff[]): DiffMetadata {
    const statsA = this.getDocumentStats(docA);
    const statsB = this.getDocumentStats(docB);

    let addedLines = 0;
    let removedLines = 0;
    let modifiedLines = 0; // Approximation based on adjacent insert/delete

    // Simple line change count (can be refined)
    diffs.forEach((seg, index) => {
        const lineCount = (seg.text.match(/\n/g) || []).length + (seg.text.endsWith('\n') ? 0 : 1);
        if (seg.type === 'insert') addedLines += lineCount;
        if (seg.type === 'delete') removedLines += lineCount;
        // Rudimentary modification check
        if (seg.type === 'insert' && index > 0 && diffs[index - 1].type === 'delete') {
            const modCount = Math.min((diffs[index - 1].text.match(/\n/g) || []).length + 1, lineCount);
            modifiedLines += modCount;
            addedLines -= modCount;
            removedLines -= modCount;
        } else if (seg.type === 'delete' && index < diffs.length - 1 && diffs[index + 1].type === 'insert') {
             // Handled by the previous case, avoid double counting
        }
    });


    return {
      timestamp: new Date(),
      documentStats: {
        documentA: { length: docA.length, wordCount: statsA.wordCount, charCount: statsA.charCount },
        documentB: { length: docB.length, wordCount: statsB.wordCount, charCount: statsB.charCount },
      },
      summary: {
        addedLines: addedLines,
        removedLines: removedLines,
        modifiedLines: modifiedLines,
        totalChanges: addedLines + removedLines + modifiedLines,
        // significantChanges: ... // Requires more complex analysis
      },
    };
  }

  private getDocumentStats(doc: string): { wordCount: number; charCount: number } {
      const words = doc.match(/\b\w+\b/g) || [];
      return {
          wordCount: words.length,
          charCount: doc.length,
      };
  }

  private generateHtmlVisualization(diffs: DiffSegment[]): string {
    let html = '<pre style="white-space: pre-wrap; word-wrap: break-word;">';
    const colors = { insert: '#e6ffe6', delete: '#ffe6e6', equal: 'transparent' }; // Example colors
    diffs.forEach(part => {
      const style = `background-color: ${colors[part.type]};`;
      const escapedText = part.text.replace(/&/g, '&').replace(/</g, '<').replace(/>/g, '>');
      html += `<span style="${style}">${escapedText}</span>`;
    });
    html += '</pre>';
    return html;
  }

  // Placeholder for section extraction - replace with actual implementation
  private async extractSections(documentContent: string, docId: string): Promise<SectionReference[]> {
      this.logger.debug(`Placeholder: Extracting sections for ${docId}`);
      // Example: Use regex or a simple split, or call AI
      // This needs a robust implementation based on document structure
      const lines = documentContent.split('\n');
      const sections: SectionReference[] = [];
      let currentSection: SectionReference | null = null;
      let lineNum = 0;

      lines.forEach((line, index) => {
          lineNum = index + 1;
          // Example: Detect section headers (e.g., "Section X.Y Title")
          const match = line.match(/^(Section\s+(\d+(\.\d+)*))\s*-\s*(.*)/i);
          if (match) {
              if (currentSection) sections.push(currentSection); // Save previous section
              currentSection = {
                  id: `${docId}-sec-${match[2]}`, // Generate unique ID
                  title: match[4].trim(),
                  content: line + '\n',
                  lineRange: { start: lineNum, end: lineNum }
              };
          } else if (currentSection) {
              currentSection.content += line + '\n';
              currentSection.lineRange.end = lineNum;
          }
      });
      if (currentSection) sections.push(currentSection); // Save the last section

      return sections;
  }

  // Placeholder for comparing extracted sections
  private async compareExtractedSections(sectionsA: SectionReference[], sectionsB: SectionReference[]): Promise<SectionDiff[]> {
      this.logger.debug('Placeholder: Comparing extracted sections');
      const sectionDiffs: SectionDiff[] = [];
      const matchedBIds = new Set<string>();

      // Find matches and differences for sections in A
      for (const secA of sectionsA) {
          // Find best match in B (e.g., by ID or title similarity)
          const secB = sectionsB.find(sB => sB.id === secA.id || sB.title.toLowerCase() === secA.title.toLowerCase());

          if (secB) {
              matchedBIds.add(secB.id);
              const diffResult = diff.diffWords(secA.content, secB.content);

              // Map to TextDiff[]
              const differences: TextDiff[] = diffResult.map((part, index) => ({
                  original: part.removed ? part.value : part.added ? '' : part.value,
                  modified: part.added ? part.value : part.removed ? '' : part.value,
                  diffType: part.added ? 'addition' : part.removed ? 'deletion' : 'modification',
                  lineNumber: index + 1 // Placeholder line number
              }));

              sectionDiffs.push({
                  sectionA: secA,
                  sectionB: secB,
                  differences: differences.filter(d => d.diffType !== 'modification' || d.original !== d.modified) // Filter out no-ops
              });
          } else {
              // Section A exists, but not in B (deletion)
              sectionDiffs.push({
                  sectionA: secA,
                  sectionB: { id: `deleted-${secA.id}`, title: `Deleted: ${secA.title}`, content: '', lineRange: {start: 0, end: 0}}, // Placeholder for deleted section B
                  differences: [{ original: secA.content, modified: '', diffType: 'deletion', lineNumber: 1 }] // Mark entire content as deleted
              });
          }
      }

      // Find sections in B that were not matched (additions)
      for (const secB of sectionsB) {
          if (!matchedBIds.has(secB.id)) {
              sectionDiffs.push({
                  sectionA: { id: `added-${secB.id}`, title: `Added: ${secB.title}`, content: '', lineRange: {start: 0, end: 0}}, // Placeholder for added section A
                  sectionB: secB,
                  differences: [{ original: '', modified: secB.content, diffType: 'addition', lineNumber: 1 }] // Mark entire content as added
              });
          }
      }

      return sectionDiffs;
  }


  private async recordAnalytics(
      docAId: string,
      docBId: string,
      orgId: string,
      userId: string,
      level: 'enhanced' | 'version',
      details: any // Add specific details type if needed
  ) {
      try {
          await this.analyticsCollectionService.recordDocumentComparison(
              docAId,
              docBId,
              orgId,
              userId,
              {
                  comparisonLevel: level,
                  timestamp: new Date().toISOString(),
                  ...details // Spread additional details like comparisonType, versions etc.
              }
          );
          this.logger.log(`Recorded ${level} comparison between documents ${docAId} and ${docBId}`);
      } catch (analyticsError) {
          this.logger.error(`Failed to record comparison analytics: ${analyticsError.message}`, analyticsError.stack);
          // Do not fail the main operation
      }
  }

  // --- Export Functionality ---
  public async exportComparison(
      comparisonResult: ComparisonResult | EnhancedComparisonResult | VersionComparisonResult,
      options: ExportOptions
  ): Promise<Buffer | string> { // Return Buffer for binary (PDF/DOCX), string for HTML
      this.logger.log(`Exporting comparison report in ${options.format} format`);

      switch (options.format) {
          case 'pdf':
              return this.documentExportService.generatePdfReport(comparisonResult, options);
          case 'html':
              return this.documentExportService.generateHtmlReport(comparisonResult, options);
          case 'docx':
              return this.documentExportService.generateDocxReport(comparisonResult, options);
          default:
              throw new Error(`Unsupported export format: ${options.format}`);
      }
  }
  
  /**
   * Generate an executive summary of the comparison
   */
  public async generateExecutiveSummary(
      comparisonResult: ComparisonResult | EnhancedComparisonResult | VersionComparisonResult
  ): Promise<string> {
      return this.documentExportService.generateExecutiveSummary(comparisonResult);
  }
  // --- End Export Functionality ---

} // End of class EnhancedDocumentComparisonService

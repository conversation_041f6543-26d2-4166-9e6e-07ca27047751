import {
  Injectable,
  Logger,
  NotFoundException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { AIService } from '../../ai/services/ai.service';
import { AnalyticsCollectionService } from '../../analytics/services/analytics-collection.service';

import {
  Document,
  ProcessingResult,
  ProcessingError,
  DocumentMetadata,
  DocumentClause,
} from '../interfaces/document.interface';
import { DocumentMetadataWithContent } from '../interfaces/document-processor.interface';
import { JobStatus } from '../interfaces/job-status.interface';
import { DocumentVersion } from '../schemas/document-version.schema';
import {
  DocumentModel,
  Document as MongoDocument,
} from '../schemas/document.schema';
import { DocumentProcessingServiceInterface } from '../interfaces/document-service.interface';

import {
  DocumentProcessingJobType,
  ProcessDocumentJobData,
} from '../interfaces/document-processing.types';
import { QUEUES } from '../../queue/constants';
import { LegalPatternRecognitionService } from './legal-pattern-recognition.service';
import { DocumentStorageService } from './document-storage.service';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { CloudflareStorageService } from './cloudflare-storage.service';
import { StorageFactoryService } from './storage-factory.service';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { DOCUMENT_MODEL } from '../schemas/document.schema';
const pdfParse = require('pdf-parse');
import * as fs from 'fs';

@Injectable()
export class DocumentProcessingService
  implements DocumentProcessingServiceInterface
{
  private readonly logger = new Logger(DocumentProcessingService.name);

  constructor(
    @InjectQueue(QUEUES.DOCUMENT_PROCESSING)
    private readonly documentProcessingQueue: Queue<ProcessDocumentJobData>,
    @InjectModel(DOCUMENT_MODEL)
    private readonly documentModel: Model<DocumentModel>,
    private readonly patternRecognitionService: LegalPatternRecognitionService,
    private readonly documentStorageService: DocumentStorageService,
    @Inject(forwardRef(() => AIService))
    private readonly aiService: AIService,
    private readonly tenantContext: TenantContextService,
    private readonly analyticsCollectionService: AnalyticsCollectionService,
    private readonly storageFactoryService: StorageFactoryService,
  ) {}

  /**
   * Create document record immediately after upload without processing
   */
  async createDocumentRecord(
    file: Express.Multer.File,
    initialMetadata: Record<string, unknown> = {}
  ): Promise<Document> {
    try {
      const now = new Date();
      let metadata: DocumentMetadata = { ...initialMetadata };
      const organizationId = this.tenantContext.getCurrentOrganization();

      // Create document record with minimal processing
      const documentData = {
        originalName: file.originalname,
        filename: file.filename || file.originalname,
        size: file.size,
        mimeType: file.mimetype,
        uploadDate: now,
        lastModified: now,
        content: '', // Will be populated during processing
        metadata,
        status: 'uploaded',
        organizationId,
        path: file.path,
      };

      const savedDoc = await this.documentModel.create(documentData);
      this.logger.log(`Document record created: ${savedDoc.id}`);

      return this.convertToInterface(savedDoc);
    } catch (error) {
      this.logger.error(
        `Failed to create document record: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Queue document processing for background execution
   */
  async queueDocumentProcessing(
    documentId: string,
    userId?: string,
    options?: ProcessDocumentJobData['options'],
  ): Promise<string> {
    try {
      const job = await this.documentProcessingQueue.add(
        DocumentProcessingJobType.PROCESS_DOCUMENT,
        { documentId, userId, options },
        {
          attempts: 3,
          backoff: { type: 'exponential', delay: 1000 },
          priority: options?.priority ? 1 : 2,
        },
      );

      this.logger.log(`Document processing queued: ${documentId}, Job ID: ${job.id}`);
      return job.id.toString();
    } catch (error) {
      this.logger.error(
        `Failed to queue document processing for ${documentId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get processing status for a document
   */
  async getProcessingStatus(documentId: string): Promise<{
    status: string;
    progress: number;
    estimatedTimeRemaining?: number;
    error?: string;
    jobId?: string;
  }> {
    try {
      // Get document to check current status
      const document = await this.documentModel.findOne({ id: documentId });
      if (!document) {
        throw new Error(`Document ${documentId} not found`);
      }

      // For demo purposes, simulate processing if document is in uploaded status
      const documentStatus = document.status || 'uploaded';

      if (documentStatus === 'uploaded') {
        // Simulate processing progression based on time since upload
        const uploadTime = new Date(document.uploadDate).getTime();
        const currentTime = Date.now();
        const elapsedSeconds = (currentTime - uploadTime) / 1000;

        // Simulate a 30-second processing cycle
        if (elapsedSeconds < 30) {
          const progress = Math.min(Math.floor((elapsedSeconds / 30) * 100), 99);
          const estimatedTimeRemaining = Math.max(0, (30 - elapsedSeconds) * 1000);

          return {
            status: 'processing',
            progress,
            estimatedTimeRemaining,
            jobId: `demo_${documentId}`,
          };
        } else {
          // After 30 seconds, mark as completed and update document
          await this.documentModel.updateOne(
            { id: documentId },
            {
              status: 'completed',
              'metadata.processingStatus': 'completed',
              'metadata.processedAt': new Date(),
            }
          );

          return {
            status: 'completed',
            progress: 100,
          };
        }
      }

      // Document is already processed
      const progress = documentStatus === 'completed' || documentStatus === 'analyzed' ? 100 : 0;

      return {
        status: documentStatus,
        progress,
      };
    } catch (error) {
      this.logger.error(`Error getting processing status for ${documentId}:`, error);
      return {
        status: 'error',
        progress: 0,
        error: error.message,
      };
    }
  }

  private calculateEstimatedTimeRemaining(progress: number, startTime: number): number {
    if (progress <= 0) return 0;

    const elapsed = Date.now() - startTime;
    const totalEstimated = elapsed / (progress / 100);
    return Math.max(0, totalEstimated - elapsed);
  }

  async processUploadedFile(
    file: Express.Multer.File,
    initialMetadata: Record<string, unknown> = {}
  ): Promise<Document> {
    try {
      const now = new Date();
      let metadata: DocumentMetadata = { ...initialMetadata };
      const organizationId = this.tenantContext.getCurrentOrganization();

      let content: string | undefined;
      let extractedMetadata: Record<string, any> = {};

      // Get file content from Cloudflare R2
      let buffer: Buffer;

      // For Cloudflare R2 storage, we need to get the file from R2 if it's not in memory
      this.logger.log(`Processing file from Cloudflare R2 storage: ${file.originalname}`);
      
      if (file.buffer) {
        buffer = file.buffer;
      } else {
        // Get the file from R2 using the key stored in the file path
        const cloudflareStorage = this.storageFactoryService.getCloudflareStorageService();
        buffer = await cloudflareStorage.getFile(file.path);
      }

      if (file.mimetype === 'application/pdf') {
        this.logger.log(
          `Processing uploaded file as PDF: ${file.originalname}`,
        );
        const data = await pdfParse(buffer);
        content = data.text;
        extractedMetadata = {
          ...data.metadata,
          pageCount: data.numpages,
          info: data.info,
        };
        metadata = { ...metadata, ...extractedMetadata };
        this.logger.log(
          `Successfully parsed PDF: ${file.originalname}, Pages: ${data.numpages}`,
        );
      } else if (
        file.mimetype === 'text/plain' ||
        file.mimetype.startsWith('text/')
      ) {
        this.logger.log(
          `Processing uploaded file as Text: ${file.originalname}`,
        );
        content = buffer.toString('utf-8');
        metadata.characterCount = content.length;
        metadata.wordCount = content.split(/\s+/).filter(Boolean).length;
      } else {
        this.logger.warn(
          `Unsupported mimetype for direct content extraction: ${file.mimetype}. Saving file reference only.`,
        );
      }

      const document: Partial<DocumentModel> = {
        filename: file.filename || file.originalname,
        originalName: file.originalname,
        size: file.size,
        uploadDate: now,
        status: 'uploaded', // Status indicates it's ready for analysis
        content,
        metadata,
        organizationId,
      };

      const savedDoc = await this.documentStorageService.saveDocument(document);
      this.logger.log(`Saved initial document record with ID: ${savedDoc.id}`);

      // Record analytics for document upload
      await this.analyticsCollectionService.recordDocumentUpload(
        savedDoc.id,
        organizationId,
        this.tenantContext.getCurrentUserId(),
        'unknown',
      );

      // Automatically queue document processing job
      this.logger.log(
        `Automatically queuing processing job for document: ${savedDoc.id}`,
      );
      await this.processDocument(
        savedDoc.id,
        this.tenantContext.getCurrentUserId(),
        {
          extractMetadata: true,
          generateSummary: true,
          priority: false, // Use lower priority for automatic processing
        },
      ).catch((error) => {
        // Log error but don't fail the upload if processing queue fails
        this.logger.error(
          `Failed to queue automatic processing for document ${savedDoc.id}: ${error.message}`,
          error.stack,
        );
      });

      return this.convertToInterface(savedDoc);
    } catch (error) {
      this.logger.error(
        `Failed to process uploaded file: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  private convertToInterface(doc: any): Document {
    if (!doc) return null;

    return {
      id: doc.id,
      organizationId: doc.organizationId,
      filename: doc.filename,
      originalName: doc.originalName,
      content: doc.content,
      size: doc.size,
      uploadDate: doc.uploadDate,
      status: doc.status,
      metadata: doc.metadata || {},
    };
  }

  async getDocumentById(id: string): Promise<Document | null> {
    console.log('Getting document by ID:', id);
    const document = await this.documentStorageService.getDocumentById(id);
    if (!document) {
      throw new NotFoundException(`Document with id ${id} not found`);
    }
    return this.convertToInterface(document);
  }

  async getAllDocuments(options?: {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortDirection?: 'asc' | 'desc';
  }): Promise<{ documents: Document[]; total: number; totalPages: number; page: number; limit: number }> {
    const result = await this.documentStorageService.getAllDocuments(options);
    
    return {
      documents: result.documents.map(doc => this.convertToInterface(doc)),
      total: result.total,
      totalPages: result.totalPages,
      page: options?.page || 1,
      limit: options?.limit || 10
    };
  }

  async getDocumentContent(id: string): Promise<string> {
    const doc = await this.getDocumentById(id);
    return doc.content || '';
  }

  async processDocument(
    documentId: string,
    userId?: string,
    options?: ProcessDocumentJobData['options'],
  ): Promise<ProcessingResult> {
    try {
      await this.updateDocumentStatus(documentId, 'processing');

      const job = await this.documentProcessingQueue.add(
        DocumentProcessingJobType.PROCESS_DOCUMENT,
        { documentId, userId, options },
        {
          attempts: 3,
          backoff: { type: 'exponential', delay: 1000 },
          priority: options?.priority ? 1 : 2,
        },
      );

      // Record the start time for analytics
      const analysisStartTime = new Date();

      return {
        jobId: job.id.toString(),
        documentId,
        status: 'queued',
        progress: 0,
      };
    } catch (error) {
      this.logger.error(`Error processing document ${documentId}:`, error);
      await this.updateDocumentStatus(documentId, 'failed', error.message);
      throw error;
    }
  }

  async updateDocumentStatus(
    documentId: string,
    status: Document['status'],
    error?: string,
  ): Promise<void> {
    const doc = await this.getDocumentById(documentId);
    const updatedMetadata: DocumentMetadata = {
      ...doc.metadata,
      status,
      lastUpdated: new Date(),
    };

    const updateData = {
      id: doc.id,
      organizationId: doc.organizationId,
      status,
      metadata: updatedMetadata,
    };

    if (error) {
      const processingError: ProcessingError = {
        message: error,
        timestamp: new Date(),
        stack: new Error().stack,
      };
      await this.documentStorageService.saveDocument({
        ...updateData,
        processingError,
      });
    } else {
      await this.documentStorageService.saveDocument(updateData);
    }
  }

  async updateMetadata(
    documentId: string,
    metadata: Record<string, any>,
  ): Promise<void> {
    await this.documentStorageService.updateDocumentMetadata(
      documentId,
      metadata,
    );
  }

  async documentExists(id: string): Promise<boolean> {
    try {
      await this.getDocumentById(id);
      return true;
    } catch (error) {
      if (error instanceof NotFoundException) {
        return false;
      }
      throw error;
    }
  }

  async getMetadata(
    documentId: string,
  ): Promise<DocumentMetadataWithContent | null> {
    const doc = await this.documentStorageService.getDocumentById(documentId);
    if (!doc) {
      return null;
    }

    const metadata = {
      ...doc.metadata,
      content: doc.content,
      processingStatus: doc.status,
      processingError: doc.processingError?.message,
    };

    if (metadata.clauses) {
      metadata.clauses = metadata.clauses as DocumentClause[];
    }

    return metadata as DocumentMetadataWithContent;
  }

  async getJobStatus(jobId: string): Promise<JobStatus | null> {
    try {
      const job = await this.documentProcessingQueue.getJob(jobId);
      if (!job) {
        return null;
      }

      const [state, progress] = await Promise.all([
        job.getState(),
        job.progress(),
      ]);

      return {
        id: job.id.toString(),
        status: state,
        progress,
        data: job.data,
        failedReason: job.failedReason,
        attempts: job.attemptsMade,
      };
    } catch (error) {
      this.logger.error(`Error getting job status for job ${jobId}:`, error);
      throw error;
    }
  }

  async analyzePatterns(documentId: string): Promise<ProcessingResult> {
    try {
      const job = await this.documentProcessingQueue.add(
        DocumentProcessingJobType.ANALYZE_PATTERNS,
        {
          documentId,
          options: {
            analyzePatterns: true,
          },
        },
        {
          attempts: 2,
          backoff: { type: 'exponential', delay: 1000 },
        },
      );

      return {
        jobId: job.id.toString(),
        documentId,
        status: 'queued',
        progress: 0,
      };
    } catch (error) {
      this.logger.error(
        `Error analyzing patterns for document ${documentId}:`,
        error,
      );
      throw error;
    }
  }

  async extractMetadata(documentId: string): Promise<void> {
    try {
      const document = await this.getDocumentById(documentId);
      const content = document.content;

      if (!content) {
        throw new Error('Document content not available');
      }

      const prompt = `
        Analyze this document and extract the following metadata:
        - Title
        - Author(s)
        - Date
        - Document type
        - Keywords (up to 5)
        - Main topics
        - Target audience
        - Purpose/objective
        
        Format the response as a JSON object with these fields. Do not use markdown code blocks. Return only the raw JSON object.
        
        Document content:
        """
        ${content}
        """
      `;

      const response = await this.aiService.generateResponse(prompt);
      let metadata;
      try {
        // Clean up the response in case it contains markdown code blocks
        let cleanedResponse = response;

        // Remove markdown code block syntax if present
        if (cleanedResponse.includes('```json')) {
          cleanedResponse = cleanedResponse.replace(/```json\s*/, '');
          cleanedResponse = cleanedResponse.replace(/\s*```\s*$/, '');
        } else if (cleanedResponse.includes('```')) {
          cleanedResponse = cleanedResponse.replace(/```\s*/, '');
          cleanedResponse = cleanedResponse.replace(/\s*```\s*$/, '');
        }

        // Trim any extra whitespace
        cleanedResponse = cleanedResponse.trim();

        this.logger.log(
          `Cleaned AI response for parsing: ${cleanedResponse.substring(
            0,
            100,
          )}...`,
        );

        metadata = JSON.parse(cleanedResponse);
      } catch (error) {
        this.logger.error(
          `Failed to parse AI response as JSON: ${error.message}`,
          `Raw response: ${response.substring(0, 200)}...`,
        );
        metadata = {
          error: 'Failed to parse metadata',
          rawResponse: response.substring(0, 500),
        };
      }

      // Update the metadata
      await this.updateMetadata(documentId, {
        ...document.metadata,
        extractedMetadata: metadata,
        lastMetadataExtraction: new Date(),
      });

      // Update the document type if it was successfully extracted
      if (
        metadata &&
        metadata.documentType &&
        typeof metadata.documentType === 'string'
      ) {
        // Standardize document type format (uppercase for consistency)
        const documentType = metadata.documentType.toUpperCase();

        // Update the document in the database with the new document type
        await this.documentStorageService.saveDocument({
          id: documentId,
          documentType: documentType,
        });

        this.logger.log(
          `Updated document type for ${documentId} to ${documentType}`,
        );

        // Also update the document type in analytics
        const organizationId = document.organizationId;
        const userId = this.tenantContext.getCurrentUserId();

        if (organizationId && userId) {
          await this.analyticsCollectionService.recordDocumentUpload(
            documentId,
            organizationId,
            userId,
            documentType,
          );
        }
      } else {
        this.logger.warn(
          `Could not extract valid document type for document ${documentId}`,
        );
      }

      this.logger.log(
        `Successfully extracted metadata for document ${documentId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error extracting metadata for document ${documentId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Retrieves all versions of a document
   * @param documentId The ID of the document to get versions for
   * @returns Array of document versions
   */
  async getDocumentVersions(documentId: string): Promise<DocumentVersion[]> {
    try {
      this.logger.log(`Retrieving versions for document: ${documentId}`);
      return await this.documentStorageService.getDocumentVersions(documentId);
    } catch (error) {
      this.logger.error(`Error retrieving versions for document ${documentId}:`, error);
      throw error;
    }
  }

  async generateSummary(documentId: string): Promise<void> {
    try {
      const document = await this.getDocumentById(documentId);
      const content = document.content;

      if (!content) {
        throw new Error('Document content not available');
      }

      const prompt = `
        Generate a comprehensive summary of this document that covers:
        1. Main purpose and objectives
        2. Key points and findings
        3. Important conclusions or recommendations
        4. Any critical dates or deadlines mentioned
        5. Key stakeholders involved
        
        Make the summary clear, concise, and well-structured. Length should be about 15-20% of the original document.
        
        Document content:
        """
        ${content}
        """
      `;

      const summary = await this.aiService.generateResponse(prompt);

      await this.updateMetadata(documentId, {
        ...document.metadata,
        summary,
        lastSummaryGeneration: new Date(),
      });

      this.logger.log(
        `Successfully generated summary for document ${documentId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error generating summary for document ${documentId}:`,
        error,
      );
      throw error;
    }
  }

  async runAdvancedAnalysis(documentId: string): Promise<any> {
    // Rest of advanced analysis implementation
    // ...
    return { result: 'Advanced analysis completed' };
  }

  /**
   * Get a specific version of a document
   * @param documentId The ID of the document
   * @param versionNumber The version number to retrieve
   * @returns The document version
   */
  async getDocumentVersion(
    documentId: string,
    versionNumber: number,
  ): Promise<Document> {
    this.logger.log(
      `Getting version ${versionNumber} of document ${documentId}`,
    );

    try {
      // Get the base document
      const document = await this.getDocumentById(documentId);

      if (!document) {
        throw new NotFoundException(`Document with id ${documentId} not found`);
      }

      // Check if the document has versions in its metadata
      if (
        !document.metadata?.versions ||
        !Array.isArray(document.metadata.versions)
      ) {
        // If this is version 1 (current version) and no versions exist yet
        if (versionNumber === 1) {
          // Return the current document as version 1
          return {
            ...document,
            metadata: {
              ...document.metadata,
              versionNumber: 1,
              createdAt: document.uploadDate,
            },
          };
        }

        throw new NotFoundException(
          `Version ${versionNumber} of document ${documentId} not found`,
        );
      }

      // Find the requested version in the versions array
      const version = document.metadata.versions.find(
        (v) => v.versionNumber === versionNumber,
      );

      // If requested version is 1 (current) and not in versions array, return the current document
      if (versionNumber === 1 && !version) {
        return {
          ...document,
          metadata: {
            ...document.metadata,
            versionNumber: 1,
            createdAt: document.uploadDate,
          },
        };
      }

      if (!version) {
        throw new NotFoundException(
          `Version ${versionNumber} of document ${documentId} not found`,
        );
      }

      // Construct a document object from the version data
      return {
        id: `${documentId}-v${versionNumber}`,
        organizationId: document.organizationId,
        filename: document.filename,
        originalName: document.originalName,
        content: version.content,
        size: version.size || document.size,
        uploadDate: document.uploadDate,
        status: 'completed',
        metadata: {
          ...version.metadata,
          versionNumber,
          createdAt: version.createdAt || document.uploadDate,
          createdBy: version.createdBy,
          sections: version.sections || document.metadata?.sections,
          title:
            version.title || document.metadata?.title || document.originalName,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error getting document version: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Create a new version of a document
   * @param documentId The ID of the document to create a version for
   * @param content The content of the new version
   * @param userId The ID of the user creating the version
   * @param metadata Additional metadata for the version
   * @returns The document with the new version added
   */
  async createDocumentVersion(
    documentId: string,
    content: string,
    userId: string,
    metadata: Record<string, any> = {}
  ): Promise<Document> {
    this.logger.log(`Creating new version of document ${documentId}`);

    try {
      // Get the current document
      const document = await this.getDocumentById(documentId);

      if (!document) {
        throw new NotFoundException(`Document with id ${documentId} not found`);
      }

      // Initialize versions array if it doesn't exist
      if (!document.metadata.versions) {
        document.metadata.versions = [];
      }

      // Determine the next version number
      const currentVersions = document.metadata.versions || [];
      const nextVersionNumber =
        currentVersions.length > 0
          ? Math.max(...currentVersions.map((v) => v.versionNumber || 0)) + 1
          : 2; // First saved version is 2, current document is implicitly version 1

      // Extract sections from content if not provided in metadata
      let sections = metadata.sections;
      if (!sections && content) {
        // Use pattern recognition service to extract sections if available
        try {
          const extractedSections =
            await this.patternRecognitionService.extractSections(content);
          if (extractedSections && extractedSections.length > 0) {
            sections = extractedSections;
          }
        } catch (error) {
          this.logger.warn(
            `Could not extract sections for version: ${error.message}`,
          );
        }
      }

      // Create the version object
      const version = {
        versionNumber: nextVersionNumber,
        content,
        size: Buffer.from(content || '').length,
        createdAt: new Date(),
        createdBy: userId,
        title: metadata.title || document.title || document.originalName,
        sections,
        metadata: {
          ...metadata,
          versionCreatedAt: new Date(),
        },
      };

      // Add the version to the versions array
      document.metadata.versions.push(version);

      // Update the document's metadata to include version history
      document.metadata.versionCount = document.metadata.versions.length + 1; // +1 for the current version
      document.metadata.lastVersionCreatedAt = version.createdAt;
      document.metadata.lastVersionCreatedBy = userId;

      // Save the updated document
      await this.documentStorageService.saveDocument({
        id: document.id,
        organizationId: document.organizationId,
        metadata: document.metadata,
      });

      this.logger.log(
        `Created version ${nextVersionNumber} of document ${documentId}`,
      );

      // Record analytics for document version creation
      await this.analyticsCollectionService.recordDocumentVersionCreation(
        documentId,
        nextVersionNumber,
        userId,
        version.createdAt,
      );

      // Return the updated document
      return this.getDocumentById(documentId);
    } catch (error) {
      this.logger.error(
        `Error creating document version: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Delete a document and all associated data
   * @param documentId The ID of the document to delete
   * @returns Promise resolving to true if deletion was successful
   * @throws NotFoundException if document doesn't exist
   */
  async deleteDocument(documentId: string): Promise<boolean> {
    this.logger.log(`Deleting document ${documentId}`);

    try {
      // Check if document exists (lightweight check without content)
      const document = await this.documentStorageService.getDocumentMetadata(documentId);
      if (!document) {
        throw new NotFoundException(`Document with id ${documentId} not found`);
      }

      // Delete any analysis results related to this document (async - don't wait)
      // This is a soft dependency - if it fails, we still want to continue with document deletion
      this.documentProcessingQueue.add(
        DocumentProcessingJobType.CLEANUP_ANALYSIS_RESULTS,
        { documentId },
        { removeOnComplete: true }
      ).then(() => {
        this.logger.log(`Queued cleanup of analysis results for document ${documentId}`);
      }).catch(error => {
        this.logger.warn(`Error queueing analysis results cleanup for ${documentId}: ${error.message}`);
      });

      // Delete the document from storage
      const result = await this.documentStorageService.deleteDocument(documentId);
      
      // Record analytics for document deletion (async - don't wait)
      const userId = this.tenantContext.getCurrentUserId();
      const organizationId = document.organizationId;

      if (userId && organizationId) {
        // Fire and forget - don't await analytics recording
        this.analyticsCollectionService.recordDocumentDeletion(
          documentId,
          organizationId,
          userId,
          new Date()
        ).catch(error => {
          this.logger.warn(`Failed to record deletion analytics for ${documentId}: ${error.message}`);
        });
      }

      this.logger.log(`Successfully deleted document ${documentId}`);
      return result;
    } catch (error) {
      this.logger.error(`Error deleting document ${documentId}: ${error.message}`, error.stack);
      throw error;
    }
  }
}

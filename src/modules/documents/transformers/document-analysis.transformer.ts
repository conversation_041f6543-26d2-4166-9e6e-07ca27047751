import { Injectable } from '@nestjs/common';

@Injectable()
export class DocumentAnalysisTransformer {
  transform(response: any): string {
    try {
      // If there's an error, return it
      if (response.error) {
        return `Error: ${response.error}`;
      }

      // If the response is already a string, return it
      if (typeof response === 'string') {
        return response;
      }

      // If it's JSON, format it in a user-friendly way
      let result = '';

      // Add document type if available
      if (response.documentType) {
        result += `Document Type: ${response.documentType}\n\n`;
      }

      // Add summary if available
      if (response.summary) {
        result += `Summary:\n${response.summary}\n\n`;
      }

      // Add key provisions if available
      if (response.keyProvisions && Array.isArray(response.keyProvisions)) {
        result += 'Key Provisions:\n';
        response.keyProvisions.forEach((provision: any) => {
          result += `- ${provision.section}: ${provision.content}\n`;
          if (provision.significance) {
            result += `  Significance: ${provision.significance}\n`;
          }
        });
        result += '\n';
      }

      // Add risks if available
      if (response.risks && Array.isArray(response.risks)) {
        result += 'Potential Risks:\n';
        response.risks.forEach((risk: any) => {
          result += `- ${risk.description}\n`;
          result += `  Severity: ${risk.severity}\n`;
          if (risk.recommendation) {
            result += `  Recommendation: ${risk.recommendation}\n`;
          }
        });
        result += '\n';
      }

      // Add parties if available
      if (response.parties && Array.isArray(response.parties)) {
        result += 'Involved Parties:\n';
        response.parties.forEach((party: any) => {
          result += `- ${party.name} (${party.role})\n`;
          if (party.obligations && Array.isArray(party.obligations)) {
            result += '  Obligations:\n';
            party.obligations.forEach((obligation: string) => {
              result += `    * ${obligation}\n`;
            });
          }
        });
        result += '\n';
      }

      // Add metadata if available
      if (response.metadata) {
        result += 'Additional Information:\n';
        if (response.metadata.jurisdiction) {
          result += `- Jurisdiction: ${response.metadata.jurisdiction}\n`;
        }
        if (response.metadata.governingLaw) {
          result += `- Governing Law: ${response.metadata.governingLaw}\n`;
        }
        if (response.metadata.effectiveDate) {
          result += `- Effective Date: ${response.metadata.effectiveDate}\n`;
        }
        if (response.metadata.termination) {
          result += `- Termination: ${response.metadata.termination}\n`;
        }
      }

      return result.trim();
    } catch (error) {
      console.error('Error transforming document analysis:', error);
      return 'Sorry, I had trouble formatting the analysis. Please try again.';
    }
  }
}

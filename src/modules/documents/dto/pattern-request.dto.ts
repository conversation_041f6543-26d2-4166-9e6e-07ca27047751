import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString } from 'class-validator';

export class PatternRequestDto {
  @ApiProperty({
    description:
      'A natural language query describing the specific patterns or clauses to find (e.g., "find all termination clauses", "identify payment terms").',
    example: 'find all indemnification clauses',
  })
  @IsNotEmpty()
  @IsString()
  query: string;
}

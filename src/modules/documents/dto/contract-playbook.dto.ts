import { IsString, IsEnum, IsOptional, IsBoolean, IsArray, IsObject, IsNumber, ValidateNested, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Max } from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { DocumentType } from '../../../common/enums/document-type.enum';
import { ContractType, RuleType, RuleSeverity, DeviationType, RecommendationType } from '../schemas/contract-playbook.schema';

// Rule Criteria DTOs
export class RuleCriteriaDto {
  @ApiPropertyOptional({ type: [String], description: 'Keywords to match' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  keywords?: string[];

  @ApiPropertyOptional({ type: [String], description: 'Regex patterns to match' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  patterns?: string[];

  @ApiPropertyOptional({ type: [String], description: 'Semantic concepts to identify' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  semanticConcepts?: string[];

  @ApiPropertyOptional({ type: [String], description: 'Context requirements' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  contextRequirements?: string[];
}

export class AcceptableLanguageDto {
  @ApiPropertyOptional({ type: [String], description: 'Preferred language variations' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  preferred?: string[];

  @ApiPropertyOptional({ type: [String], description: 'Acceptable language variations' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  acceptable?: string[];

  @ApiPropertyOptional({ type: [String], description: 'Fallback positions' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  fallbackPositions?: string[];
}

export class UnacceptableTermsDto {
  @ApiPropertyOptional({ type: [String], description: 'Prohibited terms' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  prohibited?: string[];

  @ApiPropertyOptional({ type: [String], description: 'Terms requiring escalation' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  requiresEscalation?: string[];

  @ApiPropertyOptional({ type: [String], description: 'Terms causing auto-rejection' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  autoReject?: string[];
}

export class NegotiationGuidanceDto {
  @ApiPropertyOptional({ description: 'Negotiation strategy' })
  @IsOptional()
  @IsString()
  strategy?: string;

  @ApiPropertyOptional({ type: [String], description: 'Alternative approaches' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  alternatives?: string[];

  @ApiPropertyOptional({ description: 'Business impact description' })
  @IsOptional()
  @IsString()
  businessImpact?: string;
}

// Playbook Rule DTO
export class CreatePlaybookRuleDto {
  @ApiProperty({ description: 'Rule name' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Rule category' })
  @IsString()
  category: string;

  @ApiProperty({ enum: RuleType, description: 'Type of rule' })
  @IsEnum(RuleType)
  ruleType: RuleType;

  @ApiProperty({ enum: RuleSeverity, description: 'Rule severity level' })
  @IsEnum(RuleSeverity)
  severity: RuleSeverity;

  @ApiProperty({ type: RuleCriteriaDto, description: 'Rule matching criteria' })
  @ValidateNested()
  @Type(() => RuleCriteriaDto)
  criteria: RuleCriteriaDto;

  @ApiProperty({ type: AcceptableLanguageDto, description: 'Acceptable language variations' })
  @ValidateNested()
  @Type(() => AcceptableLanguageDto)
  acceptableLanguage: AcceptableLanguageDto;

  @ApiProperty({ type: UnacceptableTermsDto, description: 'Unacceptable terms' })
  @ValidateNested()
  @Type(() => UnacceptableTermsDto)
  unacceptableTerms: UnacceptableTermsDto;

  @ApiPropertyOptional({ type: NegotiationGuidanceDto, description: 'Negotiation guidance' })
  @IsOptional()
  @ValidateNested()
  @Type(() => NegotiationGuidanceDto)
  negotiationGuidance?: NegotiationGuidanceDto;

  @ApiPropertyOptional({ description: 'Rule description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Whether rule is active', default: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class UpdatePlaybookRuleDto extends CreatePlaybookRuleDto {
  @ApiProperty({ description: 'Rule ID' })
  @IsString()
  id: string;
}

// Contract Playbook DTOs
export class CreateContractPlaybookDto {
  @ApiProperty({ description: 'Playbook name' })
  @IsString()
  name: string;

  @ApiProperty({
    enum: ContractType,
    description: 'Type of contract this playbook applies to'
  })
  @IsEnum(ContractType)
  contractType: ContractType;

  @ApiPropertyOptional({ description: 'Playbook description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Playbook version', default: '1.0.0' })
  @IsOptional()
  @IsString()
  version?: string;

  @ApiPropertyOptional({ type: [CreatePlaybookRuleDto], description: 'Playbook rules' })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreatePlaybookRuleDto)
  rules?: CreatePlaybookRuleDto[];

  @ApiPropertyOptional({ description: 'Additional metadata' })
  @IsOptional()
  @IsObject()
  metadata?: {
    industry?: string;
    jurisdiction?: string;
    riskProfile?: string;
    tags?: string[];
    [key: string]: any;
  };

  @ApiPropertyOptional({ description: 'Whether this is a template', default: false })
  @IsOptional()
  @IsBoolean()
  isTemplate?: boolean;
}

export class UpdateContractPlaybookDto {
  @ApiPropertyOptional({ description: 'Playbook name' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Playbook description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Playbook version' })
  @IsOptional()
  @IsString()
  version?: string;

  @ApiPropertyOptional({ type: [UpdatePlaybookRuleDto], description: 'Playbook rules' })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdatePlaybookRuleDto)
  rules?: UpdatePlaybookRuleDto[];

  @ApiPropertyOptional({ description: 'Additional metadata' })
  @IsOptional()
  @IsObject()
  metadata?: {
    industry?: string;
    jurisdiction?: string;
    riskProfile?: string;
    tags?: string[];
    [key: string]: any;
  };

  @ApiPropertyOptional({ description: 'Whether playbook is active' })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({ description: 'Whether this is a template' })
  @IsOptional()
  @IsBoolean()
  isTemplate?: boolean;
}

// Contract Analysis DTOs
export class AnalyzeContractDto {
  @ApiProperty({ description: 'Contract document ID' })
  @IsString()
  contractId: string;

  @ApiProperty({ description: 'Playbook ID to analyze against' })
  @IsString()
  playbookId: string;

  @ApiPropertyOptional({ description: 'Analysis options' })
  @IsOptional()
  @IsObject()
  options?: {
    includeRecommendations?: boolean;
    riskThreshold?: number;
    aiAnalysis?: boolean;
    detailedReport?: boolean;
  };
}

// Search and Filter DTOs
export class SearchPlaybooksDto {
  @ApiPropertyOptional({ description: 'Search query' })
  @IsOptional()
  @IsString()
  query?: string;

  @ApiPropertyOptional({
    enum: ContractType,
    description: 'Filter by contract type'
  })
  @IsOptional()
  @IsEnum(ContractType)
  contractType?: ContractType;

  @ApiPropertyOptional({ description: 'Filter by active status' })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @ApiPropertyOptional({ description: 'Filter by template status' })
  @IsOptional()
  @IsBoolean()
  isTemplate?: boolean;

  @ApiPropertyOptional({ description: 'Filter by tags' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ description: 'Page number', default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: 'Items per page', default: 20 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;
}

export class SearchAnalysesDto {
  @ApiPropertyOptional({ description: 'Filter by contract ID' })
  @IsOptional()
  @IsString()
  contractId?: string;

  @ApiPropertyOptional({ description: 'Filter by playbook ID' })
  @IsOptional()
  @IsString()
  playbookId?: string;

  @ApiPropertyOptional({ description: 'Filter by risk level' })
  @IsOptional()
  @IsEnum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL'])
  riskLevel?: string;

  @ApiPropertyOptional({ description: 'Filter by status' })
  @IsOptional()
  @IsEnum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED'])
  status?: string;

  @ApiPropertyOptional({ description: 'Start date for analysis' })
  @IsOptional()
  @IsString()
  startDate?: string;

  @ApiPropertyOptional({ description: 'End date for analysis' })
  @IsOptional()
  @IsString()
  endDate?: string;

  @ApiPropertyOptional({ description: 'Page number', default: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  page?: number;

  @ApiPropertyOptional({ description: 'Items per page', default: 20 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;
}

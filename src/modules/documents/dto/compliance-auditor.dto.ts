import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  IsEnum,
  IsArray,
  IsNumber,
  IsBoolean,
  IsObject,
  ValidateNested,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ComplianceAuditOptionsDto {
  @ApiPropertyOptional({
    type: [String],
    description: 'Specific regulations to check',
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  regulations?: string[];

  @ApiPropertyOptional({ description: 'Compliance profile ID' })
  @IsOptional()
  @IsString()
  profileId?: string;

  @ApiProperty({ description: 'Include AI analysis' })
  @IsBoolean()
  includeAIAnalysis: boolean = true;

  @ApiProperty({ enum: ['basic', 'standard', 'comprehensive'] })
  @IsEnum(['basic', 'standard', 'comprehensive'])
  detailLevel: 'basic' | 'standard' | 'comprehensive' = 'standard';

  @ApiProperty({ description: 'Include recommendations' })
  @IsBoolean()
  includeRecommendations: boolean = true;

  @ApiProperty({ description: 'Generate compliance report' })
  @IsBoolean()
  generateReport: boolean = false;

  @ApiProperty({ description: 'Include risk assessment' })
  @IsBoolean()
  riskAssessment: boolean = true;

  @ApiProperty({ description: 'Include benchmarking' })
  @IsBoolean()
  benchmarking: boolean = false;

  @ApiPropertyOptional({
    type: [Object],
    description: 'Custom compliance rules',
  })
  @IsOptional()
  @IsArray()
  customRules?: any[];
}

export class AuditDocumentDto {
  @ApiProperty({ description: 'Document ID to audit' })
  @IsString()
  documentId: string;

  @ApiProperty({ description: 'Audit options' })
  @IsObject()
  @ValidateNested()
  @Type(() => ComplianceAuditOptionsDto)
  options: ComplianceAuditOptionsDto;

  @ApiPropertyOptional({ description: 'Compliance profile ID to use' })
  @IsOptional()
  @IsString()
  profileId?: string;
}

export class CreateComplianceProfileDto {
  @ApiProperty({ description: 'Profile name' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Profile description' })
  @IsString()
  description: string;

  @ApiProperty({ type: [String], description: 'Applicable regulations' })
  @IsArray()
  @IsString({ each: true })
  regulations: string[];

  @ApiProperty({ description: 'Industry sector' })
  @IsString()
  industry: string;

  @ApiProperty({ description: 'Legal jurisdiction' })
  @IsString()
  jurisdiction: string;

  @ApiProperty({ enum: ['small', 'medium', 'large', 'enterprise'] })
  @IsEnum(['small', 'medium', 'large', 'enterprise'])
  organizationSize: 'small' | 'medium' | 'large' | 'enterprise';

  @ApiProperty({ enum: ['low', 'medium', 'high'] })
  @IsEnum(['low', 'medium', 'high'])
  riskTolerance: 'low' | 'medium' | 'high';

  @ApiPropertyOptional({ type: [String], description: 'Special requirements' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  specialRequirements?: string[];

  @ApiPropertyOptional({
    type: [Object],
    description: 'Custom compliance rules',
  })
  @IsOptional()
  @IsArray()
  customRules?: any[];
}

export class RuleApplicabilityDto {
  @ApiProperty({ type: [String], description: 'Applicable document types' })
  @IsArray()
  @IsString({ each: true })
  documentTypes: string[];

  @ApiProperty({ type: [String], description: 'Applicable industries' })
  @IsArray()
  @IsString({ each: true })
  industries: string[];

  @ApiProperty({ type: [String], description: 'Applicable jurisdictions' })
  @IsArray()
  @IsString({ each: true })
  jurisdictions: string[];

  @ApiProperty({ type: [String], description: 'Applicable organization sizes' })
  @IsArray()
  @IsString({ each: true })
  organizationSizes: string[];

  @ApiPropertyOptional({ type: [String], description: 'Additional conditions' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  conditions?: string[];
}

export class RulePatternDto {
  @ApiProperty({ enum: ['regex', 'keyword', 'semantic', 'ai-analysis'] })
  @IsEnum(['regex', 'keyword', 'semantic', 'ai-analysis'])
  type: 'regex' | 'keyword' | 'semantic' | 'ai-analysis';

  @ApiProperty({ description: 'Pattern string' })
  @IsString()
  pattern: string;

  @ApiProperty({ description: 'Pattern weight (0-1)' })
  @IsNumber()
  @Min(0)
  @Max(1)
  weight: number;

  @ApiPropertyOptional({ description: 'Pattern context' })
  @IsOptional()
  @IsString()
  context?: string;

  @ApiPropertyOptional({ type: [String], description: 'Negative patterns' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  negativePatterns?: string[];

  @ApiPropertyOptional({ type: [String], description: 'Required context' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  requiredContext?: string[];
}

export class ComplianceRuleDto {
  @ApiProperty({ description: 'Rule number or identifier' })
  @IsString()
  ruleNumber: string;

  @ApiProperty({ description: 'Rule title' })
  @IsString()
  title: string;

  @ApiProperty({ description: 'Rule description' })
  @IsString()
  description: string;

  @ApiProperty({ description: 'Rule category' })
  @IsString()
  category: string;

  @ApiProperty({ enum: ['info', 'warning', 'error', 'critical'] })
  @IsEnum(['info', 'warning', 'error', 'critical'])
  severity: 'info' | 'warning' | 'error' | 'critical';

  @ApiProperty({ description: 'Rule applicability' })
  @IsObject()
  @ValidateNested()
  @Type(() => RuleApplicabilityDto)
  applicability: RuleApplicabilityDto;

  @ApiProperty({ type: [Object], description: 'Rule patterns' })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RulePatternDto)
  patterns: RulePatternDto[];

  @ApiPropertyOptional({ description: 'AI prompt for analysis' })
  @IsOptional()
  @IsString()
  aiPrompt?: string;

  @ApiProperty({ description: 'Requires manual review' })
  @IsBoolean()
  manualReview: boolean = false;

  @ApiPropertyOptional({ type: [String], description: 'Rule exemptions' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  exemptions?: string[];

  @ApiPropertyOptional({ type: [String], description: 'Reference documents' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  references?: string[];
}

export class ComplianceQueryDto {
  @ApiPropertyOptional({ type: [String], description: 'Filter by regulations' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  regulations?: string[];

  @ApiPropertyOptional({
    enum: ['compliant', 'non-compliant', 'partial', 'needs-review'],
  })
  @IsOptional()
  @IsEnum(['compliant', 'non-compliant', 'partial', 'needs-review'])
  status?: string;

  @ApiPropertyOptional({ enum: ['low', 'medium', 'high', 'critical'] })
  @IsOptional()
  @IsEnum(['low', 'medium', 'high', 'critical'])
  riskLevel?: string;

  @ApiPropertyOptional({ description: 'Start date filter' })
  @IsOptional()
  @IsString()
  startDate?: string;

  @ApiPropertyOptional({ description: 'End date filter' })
  @IsOptional()
  @IsString()
  endDate?: string;

  @ApiPropertyOptional({ description: 'Page number' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ description: 'Items per page' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;

  @ApiPropertyOptional({ description: 'Sort field' })
  @IsOptional()
  @IsString()
  sortBy?: string = 'auditDate';

  @ApiPropertyOptional({ enum: ['asc', 'desc'] })
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';
}

export class UpdateComplianceProfileDto {
  @ApiPropertyOptional({ description: 'Profile name' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Profile description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    type: [String],
    description: 'Applicable regulations',
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  regulations?: string[];

  @ApiPropertyOptional({ description: 'Industry sector' })
  @IsOptional()
  @IsString()
  industry?: string;

  @ApiPropertyOptional({ description: 'Legal jurisdiction' })
  @IsOptional()
  @IsString()
  jurisdiction?: string;

  @ApiPropertyOptional({ enum: ['small', 'medium', 'large', 'enterprise'] })
  @IsOptional()
  @IsEnum(['small', 'medium', 'large', 'enterprise'])
  organizationSize?: 'small' | 'medium' | 'large' | 'enterprise';

  @ApiPropertyOptional({ enum: ['low', 'medium', 'high'] })
  @IsOptional()
  @IsEnum(['low', 'medium', 'high'])
  riskTolerance?: 'low' | 'medium' | 'high';

  @ApiPropertyOptional({ type: [String], description: 'Special requirements' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  specialRequirements?: string[];

  @ApiPropertyOptional({
    type: [Object],
    description: 'Custom compliance rules',
  })
  @IsOptional()
  @IsArray()
  customRules?: any[];
}

export class GenerateComplianceReportDto {
  @ApiProperty({ description: 'Audit result ID' })
  @IsString()
  auditResultId: string;

  @ApiProperty({ enum: ['pdf', 'html', 'json', 'excel'] })
  @IsEnum(['pdf', 'html', 'json', 'excel'])
  format: 'pdf' | 'html' | 'json' | 'excel' = 'pdf';

  @ApiPropertyOptional({ description: 'Include executive summary' })
  @IsOptional()
  @IsBoolean()
  includeExecutiveSummary?: boolean = true;

  @ApiPropertyOptional({ description: 'Include detailed findings' })
  @IsOptional()
  @IsBoolean()
  includeDetailedFindings?: boolean = true;

  @ApiPropertyOptional({ description: 'Include recommendations' })
  @IsOptional()
  @IsBoolean()
  includeRecommendations?: boolean = true;

  @ApiPropertyOptional({ description: 'Include charts and graphs' })
  @IsOptional()
  @IsBoolean()
  includeCharts?: boolean = true;

  @ApiPropertyOptional({ description: 'Include appendices' })
  @IsOptional()
  @IsBoolean()
  includeAppendices?: boolean = false;
}

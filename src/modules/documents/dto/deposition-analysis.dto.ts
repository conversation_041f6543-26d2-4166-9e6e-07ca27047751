import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsArray, IsNumber, IsEnum, IsDateString, IsObject, ValidateNested, IsNotEmpty, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class DepositionTestimonyAnalysisDto {
  @ApiProperty({ description: 'Name or role of the speaker' })
  @IsString()
  speaker: string;

  @ApiProperty({ description: 'The statement made by the speaker' })
  @IsString()
  statement: string;

  @ApiProperty({ description: 'Credibility score between 0 and 1' })
  @IsNumber()
  credibilityScore: number;

  @ApiProperty({ enum: ['high', 'medium', 'low'] })
  @IsEnum(['high', 'medium', 'low'])
  confidence: 'high' | 'medium' | 'low';

  @ApiProperty({ description: 'Reasoning behind the credibility assessment' })
  @IsString()
  reasoning: string;

  @ApiProperty({ type: [String], description: 'Supporting evidence for the analysis' })
  @IsArray()
  @IsString({ each: true })
  supportingEvidence: string[];
}

export class CrossExaminationSuggestionDto {
  @ApiProperty({ description: 'Topic or subject of the question' })
  @IsString()
  topic: string;

  @ApiProperty({ description: 'Suggested cross-examination question' })
  @IsString()
  question: string;

  @ApiProperty({ description: 'Purpose of asking this question' })
  @IsString()
  purpose: string;

  @ApiProperty({ description: 'Legal basis for the question', required: false })
  @IsString()
  @IsOptional()
  legalBasis?: string;

  @ApiProperty({ type: [String], description: 'Suggested follow-up questions', required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  suggestedFollowUps?: string[];
}

export class AnalyzeDepositionDto {
  @ApiProperty({
    description: 'ID of the deposition being analyzed',
    required: true
  })
  @IsString()
  @IsNotEmpty()
  depositionId: string;

  @ApiProperty({ description: 'Deposition transcript text' })
  @IsString()
  @IsNotEmpty()
  transcript: string;

  @ApiProperty({ description: 'Case context or background information' })
  @IsString()
  @IsNotEmpty()
  caseContext: string;

  @ApiProperty({
    description: 'Focus areas for analysis',
    type: [String],
    required: true,
    example: ['credibility', 'inconsistencies', 'expert testimony']
  })
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty()
  focusAreas: string[];

  @ApiProperty({
    description: 'ID of the associated deposition preparation',
    required: false
  })
  @IsString()
  @IsOptional()
  depositionPreparationId?: string;
}

export class DepositionAnalysisResponseDto {
  @ApiProperty({ description: 'Unique identifier for the analysis' })
  @IsString()
  id: string;

  @ApiProperty({ description: 'ID of the deposition being analyzed' })
  @IsString()
  depositionId: string;

  @ApiProperty({ description: 'Overall credibility score (0-1)' })
  @IsNumber()
  overallCredibilityScore: number;

  @ApiProperty({ 
    type: [DepositionTestimonyAnalysisDto],
    description: 'Analysis of key testimony segments' 
  })
  @ValidateNested({ each: true })
  @Type(() => DepositionTestimonyAnalysisDto)
  keyTestimonyAnalysis: DepositionTestimonyAnalysisDto[];

  @ApiProperty({ 
    type: [CrossExaminationSuggestionDto],
    description: 'Suggested cross-examination questions' 
  })
  @ValidateNested({ each: true })
  @Type(() => CrossExaminationSuggestionDto)
  crossExaminationSuggestions: CrossExaminationSuggestionDto[];

  @ApiProperty({ 
    type: [Object],
    description: 'Potential impeachment opportunities' 
  })
  potentialImpeachmentOpportunities: Array<{
    statement: string;
    conflictingEvidence: string;
    suggestedApproach: string;
  }>;

  @ApiProperty({ 
    type: [Object],
    description: 'Timeline analysis of key events' 
  })
  timelineAnalysis: Array<{
    event: string;
    timestamp: string;
    relevance: number;
    notes: string;
  }>;

  @ApiProperty({ description: 'Metadata about the analysis' })
  @IsObject()
  metadata: {
    analyzedAt: Date;
    analysisDurationMs: number;
    modelUsed: string;
    confidence: 'high' | 'medium' | 'low';
  };
}

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsArray, IsOptional, IsBoolean, IsObject, IsNotEmpty, MinLength, ValidateNested } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class CreateClauseTemplateDto {
  @ApiProperty({ description: 'Name of the clause template' })
  @IsString()
  @IsNotEmpty()
  @MinLength(3)
  name: string;

  @ApiProperty({ description: 'Content of the clause template' })
  @IsString()
  @IsNotEmpty()
  @MinLength(10)
  content: string;

  @ApiProperty({ description: 'Category of the clause template' })
  @IsString()
  @IsNotEmpty()
  category: string;

  @ApiPropertyOptional({ description: 'Tags for the clause template', type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Transform(({ value }) => Array.isArray(value) ? value : value?.split(',').map(v => v.trim()))
  tags?: string[];

  @ApiPropertyOptional({ description: 'Additional metadata for the clause template' })
  @IsObject()
  @IsOptional()
  @Type(() => Object)
  metadata?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Whether the clause template is public', default: false })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  isPublic?: boolean;
}

export class UpdateClauseTemplateDto {
  @ApiPropertyOptional({ description: 'Name of the clause template' })
  @IsString()
  @IsOptional()
  @MinLength(3)
  name?: string;

  @ApiPropertyOptional({ description: 'Content of the clause template' })
  @IsString()
  @IsOptional()
  @MinLength(10)
  content?: string;

  @ApiPropertyOptional({ description: 'Category of the clause template' })
  @IsString()
  @IsOptional()
  category?: string;

  @ApiPropertyOptional({ description: 'Tags for the clause template', type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Transform(({ value }) => Array.isArray(value) ? value : value?.split(',').map(v => v.trim()))
  tags?: string[];

  @ApiPropertyOptional({ description: 'Additional metadata for the clause template' })
  @IsObject()
  @IsOptional()
  @Type(() => Object)
  metadata?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Whether the clause template is public' })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  isPublic?: boolean;
}

export class MatchingPreferencesDto {
  @ApiPropertyOptional({ description: 'Whether to ignore section numbering in document content', default: true })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' ? true : value === 'false' ? false : value)
  ignoreNumbering?: boolean;

  @ApiPropertyOptional({ description: 'Whether to use keyword-based matching as an alternative approach', default: true })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' ? true : value === 'false' ? false : value)
  useKeywordMatching?: boolean;

  @ApiPropertyOptional({ description: 'Whether to handle special formats like JSON or markdown', default: true })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' ? true : value === 'false' ? false : value)
  handleSpecialFormats?: boolean;

  @ApiPropertyOptional({ description: 'Whether to boost similarity scores for semantically similar content', default: true })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true' ? true : value === 'false' ? false : value)
  boostSimilarContent?: boolean;
}

export class IdentifyClausesDto {
  @ApiProperty({ description: 'ID of the document to analyze' })
  @IsString()
  @IsNotEmpty()
  documentId: string;

  @ApiPropertyOptional({ description: 'Categories to focus on', type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Transform(({ value }) => Array.isArray(value) ? value : value?.split(',').map(v => v.trim()))
  categories?: string[];

  @ApiPropertyOptional({ description: 'Minimum similarity threshold (0.0-1.0)', default: 0.7 })
  @IsOptional()
  @Transform(({ value }) => parseFloat(value))
  similarityThreshold?: number;
  
  @ApiPropertyOptional({ 
    description: 'Advanced matching preferences to control similarity calculation', 
    type: MatchingPreferencesDto 
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => MatchingPreferencesDto)
  matchingPreferences?: MatchingPreferencesDto;
}

export class GenerateTemplateDto {
  @ApiProperty({ description: 'Document content to analyze for template generation' })
  @IsString()
  @IsNotEmpty()
  documentContent: string;

  @ApiProperty({ description: 'Name for the generated template' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'Category for the generated template' })
  @IsString()
  @IsNotEmpty()
  category: string;

  @ApiPropertyOptional({ description: 'Tags for the generated template', type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Transform(({ value }) => Array.isArray(value) ? value : value?.split(',').map(v => v.trim()))
  tags?: string[];
}

export class SearchClauseTemplatesDto {
  @ApiPropertyOptional({ description: 'Search query for template content and metadata' })
  @IsString()
  @IsOptional()
  query?: string;

  @ApiPropertyOptional({ description: 'Filter by category' })
  @IsString()
  @IsOptional()
  category?: string;

  @ApiPropertyOptional({ description: 'Filter by tags', type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  @Transform(({ value }) => Array.isArray(value) ? value : value?.split(',').map(v => v.trim()))
  tags?: string[];

  @ApiPropertyOptional({ description: 'Include public templates from other organizations', default: true })
  @IsBoolean()
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  includePublic?: boolean;
}

import { IsObject, IsEnum, IsOptional, ValidateNested, IsArray } from 'class-validator';
import { Type } from 'class-transformer';

export class DocumentSection {
  sectionTitle: string;
  content: string;
}

/**
 * DTO for document section comparison requests
 */
export class DocumentSectionComparisonRequestDto {
  @IsObject()
  documentSections: Record<string, DocumentSection[]>;

  @IsEnum(['similarities', 'differences', 'both'])
  @IsOptional()
  comparisonType?: 'similarities' | 'differences' | 'both' = 'both';
}

import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsArray, ArrayMinSize, IsEnum } from 'class-validator';

export enum AnalysisComparisonType {
  DIFFERENCES = 'differences',
  SIMILARITIES = 'similarities',
  BOTH = 'both',
  EVOLUTION = 'evolution'
}

export class AnalysisComparisonRequestDto {
  @ApiProperty({
    description: 'Document ID',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @IsString()
  documentId: string;

  @ApiProperty({
    description: 'Analysis IDs to compare (if not provided, all analyses will be compared)',
    example: ['123e4567-e89b-12d3-a456-426614174000', '123e4567-e89b-12d3-a456-426614174001'],
    required: false
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(2)
  @IsString({ each: true })
  analysisIds?: string[];

  @ApiProperty({
    description: 'Type of comparison to perform',
    enum: AnalysisComparisonType,
    default: AnalysisComparisonType.EVOLUTION,
    required: false
  })
  @IsOptional()
  @IsEnum(AnalysisComparisonType)
  comparisonType?: AnalysisComparisonType = AnalysisComparisonType.EVOLUTION;
}

export class AnalysisEvolutionMetricsDto {
  @ApiProperty({
    description: 'Document ID',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  documentId: string;

  @ApiProperty({
    description: 'Total number of analyses performed on this document',
    example: 5
  })
  totalAnalyses: number;

  @ApiProperty({
    description: 'Dates of analyses in chronological order',
    example: ['2025-01-01T12:00:00Z', '2025-01-15T14:30:00Z', '2025-02-01T09:15:00Z']
  })
  analysisDates: string[];

  @ApiProperty({
    description: 'AI providers used for analyses',
    example: ['openai', 'gemini', 'openai']
  })
  aiProviders: string[];

  @ApiProperty({
    description: 'Models used for analyses',
    example: ['gpt-4', 'gemini-pro', 'gpt-4-turbo']
  })
  modelsUsed: string[];

  @ApiProperty({
    description: 'Processing times in milliseconds for each analysis',
    example: [1200, 980, 1050]
  })
  processingTimes: number[];

  @ApiProperty({
    description: 'Content length evolution (character count)',
    example: [5000, 5500, 6200]
  })
  contentLengths: number[];

  @ApiProperty({
    description: 'Analysis IDs in chronological order',
    example: ['123e4567-e89b-12d3-a456-426614174000', '123e4567-e89b-12d3-a456-426614174001']
  })
  analysisIds: string[];
}

export class AnalysisComparisonResultDto {
  @ApiProperty({
    description: 'Document ID',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  documentId: string;

  @ApiProperty({
    description: 'Analysis IDs that were compared',
    example: ['123e4567-e89b-12d3-a456-426614174000', '123e4567-e89b-12d3-a456-426614174001']
  })
  analysisIds: string[];

  @ApiProperty({
    description: 'Type of comparison performed',
    enum: AnalysisComparisonType,
    example: AnalysisComparisonType.DIFFERENCES
  })
  comparisonType: AnalysisComparisonType;

  @ApiProperty({
    description: 'Comparison results',
    example: {
      differences: {
        added: ['New section on legal precedents'],
        removed: ['Outdated reference to case law'],
        modified: ['Updated interpretation of clause 5']
      },
      similarities: {
        unchangedSections: ['Introduction', 'Conclusion'],
        commonThemes: ['Contract interpretation', 'Legal obligations']
      }
    }
  })
  results: any;

  @ApiProperty({
    description: 'Evolution metrics if comparison type is EVOLUTION',
    type: AnalysisEvolutionMetricsDto,
    required: false
  })
  @IsOptional()
  evolutionMetrics?: AnalysisEvolutionMetricsDto;
}

import { Is<PERSON>tring, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Is<PERSON><PERSON>y, IsBoolean } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { DocumentType } from '../../../common/enums/document-type.enum';

export class NegotiationPlaybookRequestDto {
  @ApiProperty({
    description: 'Type of document being analyzed',
    enum: DocumentType,
    default: DocumentType.CONTRACT
  })
  @IsEnum(DocumentType)
  documentType: DocumentType;

  @ApiProperty({
    description: 'Specific focus areas for negotiation strategy',
    required: false,
    type: [String]
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  focusAreas?: string[];

  @ApiProperty({
    description: 'Whether to include simulation scenarios in the playbook',
    required: false,
    default: true
  })
  @IsOptional()
  @IsBoolean()
  includeSimulations?: boolean;

  @ApiProperty({
    description: 'Organization-specific negotiation preferences',
    required: false
  })
  @IsOptional()
  @IsString()
  organizationPreferences?: string;
}
import { IsOptional, IsBoolean, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>ida<PERSON>N<PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { AIFocusOptionsDto } from './ai-focus-options.dto';

export class AnalyzePrecedentsDto {

  @IsOptional()
  @IsBoolean()
  includeRecommendations?: boolean;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(20)
  @Type(() => Number)
  maxRelatedCases?: number;

  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  @Type(() => Number)
  minRelevanceScore?: number;

  @IsOptional()
  @IsBoolean()
  categorize?: boolean;

  @IsOptional()
  @IsBoolean()
  assessImpact?: boolean;

  @IsOptional()
  @IsBoolean()
  useAIAnalysis?: boolean;

  @IsOptional()
  @ValidateNested()
  @Type(() => AIFocusOptionsDto)
  aiFocus?: AIFocusOptionsDto;
}

import { Is<PERSON>tring, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum ComparisonType {
  SIMILARITIES = 'similarities',
  DIFFERENCES = 'differences',
  BOTH = 'both'
}

export class CompareDocumentsDto {
  @ApiProperty({ 
    description: 'Content of the first document',
    example: 'Content of document A'
  })
  @IsString({ message: 'Document A must be a string' })
  @IsNotEmpty({ message: 'Document A cannot be empty' })
  documentA: string;

  @ApiProperty({ 
    description: 'Content of the second document',
    example: 'Content of document B'
  })
  @IsString({ message: 'Document B must be a string' })
  @IsNotEmpty({ message: 'Document B cannot be empty' })
  documentB: string;

  @ApiProperty({
    description: 'Type of comparison to perform',
    enum: ComparisonType,
    example: ComparisonType.BOTH,
    default: ComparisonType.BOTH
  })
  @IsEnum(ComparisonType, {
    message: `Type must be one of: ${Object.values(ComparisonType).join(', ')}. You provided: $value`
  })
  type: ComparisonType = ComparisonType.BOTH;

  @ApiProperty({
    description: 'ID of document A (for tracking purposes)',
    required: false
  })
  @IsOptional()
  @IsUUID(4, { message: 'Document A ID must be a valid UUID' })
  documentAId?: string;

  @ApiProperty({
    description: 'ID of document B (for tracking purposes)',
    required: false
  })
  @IsOptional()
  @IsUUID(4, { message: 'Document B ID must be a valid UUID' })
  documentBId?: string;
}
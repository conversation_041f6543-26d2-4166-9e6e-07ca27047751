import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, IsBoolean, IsNumber, IsEnum, IsArray, Min, Max, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PrivilegeType, PrivilegeStatus } from '../interfaces/privilege-log.interface';

export class AnalyzePrivilegeDto {
  @ApiPropertyOptional({
    description: 'Whether to include AI analysis in addition to pattern matching',
    default: true
  })
  @IsOptional()
  @IsBoolean()
  includeAIAnalysis?: boolean = true;

  @ApiPropertyOptional({
    description: 'Minimum confidence score for privilege detection (0.0 to 1.0)',
    minimum: 0,
    maximum: 1,
    default: 0.7
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  confidenceThreshold?: number = 0.7;

  @ApiPropertyOptional({
    description: 'Specific privilege types to detect',
    enum: PrivilegeType,
    isArray: true
  })
  @IsOptional()
  @IsArray()
  @IsEnum(PrivilegeType, { each: true })
  privilegeTypes?: PrivilegeType[];

  @ApiPropertyOptional({
    description: 'Whether to automatically apply redactions for high-confidence items',
    default: false
  })
  @IsOptional()
  @IsBoolean()
  autoRedact?: boolean = false;

  @ApiPropertyOptional({
    description: 'Whether all items require manual review before redaction',
    default: true
  })
  @IsOptional()
  @IsBoolean()
  requireManualReview?: boolean = true;
}

export class ReviewPrivilegeDto {
  @ApiProperty({
    description: 'ID of the privileged content item'
  })
  @IsString()
  contentId: string;

  @ApiProperty({
    description: 'New status for the privileged content',
    enum: PrivilegeStatus
  })
  @IsEnum(PrivilegeStatus)
  status: PrivilegeStatus;

  @ApiPropertyOptional({
    description: 'Reason for the status change'
  })
  @IsOptional()
  @IsString()
  reason?: string;

  @ApiPropertyOptional({
    description: 'Whether to apply redaction'
  })
  @IsOptional()
  @IsBoolean()
  applyRedaction?: boolean;
}

export class ApplyRedactionDto {
  @ApiProperty({
    description: 'ID of the privileged content item to redact'
  })
  @IsString()
  contentId: string;

  @ApiPropertyOptional({
    description: 'Custom redaction text (default: [REDACTED])'
  })
  @IsOptional()
  @IsString()
  redactionText?: string;

  @ApiProperty({
    description: 'Reason for redaction'
  })
  @IsString()
  reason: string;
}

export class BulkRedactionDto {
  @ApiProperty({
    description: 'Array of content IDs to redact',
    type: [String]
  })
  @IsArray()
  @IsString({ each: true })
  contentIds: string[];

  @ApiPropertyOptional({
    description: 'Custom redaction text for all items (default: [REDACTED])'
  })
  @IsOptional()
  @IsString()
  redactionText?: string;

  @ApiProperty({
    description: 'Reason for bulk redaction'
  })
  @IsString()
  reason: string;
}

export class PrivilegeLogQueryDto {
  @ApiPropertyOptional({
    description: 'Filter by privilege log status',
    enum: PrivilegeStatus
  })
  @IsOptional()
  @IsEnum(PrivilegeStatus)
  status?: PrivilegeStatus;

  @ApiPropertyOptional({
    description: 'Filter by privilege type',
    enum: PrivilegeType
  })
  @IsOptional()
  @IsEnum(PrivilegeType)
  privilegeType?: PrivilegeType;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    minimum: 1,
    default: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    minimum: 1,
    maximum: 100,
    default: 20
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 20;
}

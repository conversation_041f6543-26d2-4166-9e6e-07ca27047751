import {
  IsString,
  Is<PERSON>ptional,
  <PERSON><PERSON>rray,
  IsEnum,
  IsBoolean,
  IsNumber,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum DocumentType {
  CONTRACT = 'contract',
  NDA = 'nda',
  MSA = 'msa',
  SOW = 'sow',
  AMENDMENT = 'amendment',
  ADDENDUM = 'addendum',
  SCHEDULE = 'schedule',
  EXHIBIT = 'exhibit',
  PRIVACY_POLICY = 'privacy_policy',
  TERMS_OF_SERVICE = 'terms_of_service',
  EMPLOYMENT_AGREEMENT = 'employment_agreement',
  LEASE_AGREEMENT = 'lease_agreement',
  OTHER = 'other',
}

export enum RelatedDocumentType {
  SCHEDULE = 'schedule',
  EXHIBIT = 'exhibit',
  ADDENDUM = 'addendum',
  AMENDMENT = 'amendment',
  APPENDIX = 'appendix',
}

export class DocumentDraftingPrompt {
  @ApiProperty({
    description: 'Main prompt or instruction for document generation',
  })
  @IsString()
  prompt: string;

  @ApiPropertyOptional({ description: 'Key terms or concepts to include' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  keyTerms?: string[];

  @ApiPropertyOptional({ description: 'Specific clauses to include' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  requiredClauses?: string[];

  @ApiPropertyOptional({ description: 'Additional context or requirements' })
  @IsOptional()
  @IsString()
  context?: string;
}

export class AIAssistedDraftingDto {
  @ApiProperty({
    enum: DocumentType,
    description: 'Type of document to generate',
  })
  @IsEnum(DocumentType)
  documentType: DocumentType;

  @ApiProperty({
    type: DocumentDraftingPrompt,
    description: 'Drafting instructions and prompts',
  })
  @ValidateNested()
  @Type(() => DocumentDraftingPrompt)
  draftingPrompt: DocumentDraftingPrompt;

  @ApiPropertyOptional({
    description: 'Organization-specific preferences or standards',
  })
  @IsOptional()
  @IsString()
  organizationPreferences?: string;

  @ApiPropertyOptional({
    description: 'Use organization clause library for suggestions',
  })
  @IsOptional()
  @IsBoolean()
  useClauseLibrary?: boolean;

  @ApiPropertyOptional({ description: 'Include standard legal disclaimers' })
  @IsOptional()
  @IsBoolean()
  includeDisclaimers?: boolean;

  @ApiPropertyOptional({
    description: 'Target jurisdiction for legal compliance',
  })
  @IsOptional()
  @IsString()
  jurisdiction?: string;
}

export class GenerateRelatedDocumentsDto {
  @ApiProperty({ description: 'ID of the primary document' })
  @IsString()
  primaryDocumentId: string;

  @ApiProperty({
    enum: RelatedDocumentType,
    isArray: true,
    description: 'Types of related documents to generate',
  })
  @IsArray()
  @IsEnum(RelatedDocumentType, { each: true })
  documentTypes: RelatedDocumentType[];

  @ApiPropertyOptional({
    description: 'Specific content or data for related documents',
  })
  @IsOptional()
  @IsString()
  additionalContent?: string;

  @ApiPropertyOptional({
    description: 'Auto-populate from primary document content',
  })
  @IsOptional()
  @IsBoolean()
  autoPopulate?: boolean;
}

export class ClauseIntelligenceDto {
  @ApiProperty({
    enum: DocumentType,
    description: 'Type of document being drafted',
  })
  @IsEnum(DocumentType)
  documentType: DocumentType;

  @ApiProperty({ description: 'Current document content or partial draft' })
  @IsString()
  currentContent: string;

  @ApiPropertyOptional({
    description: 'Specific section or clause type needed',
  })
  @IsOptional()
  @IsString()
  sectionType?: string;

  @ApiPropertyOptional({ description: 'Context for clause suggestions' })
  @IsOptional()
  @IsString()
  context?: string;

  @ApiPropertyOptional({ description: 'Include organization-specific clauses' })
  @IsOptional()
  @IsBoolean()
  includeOrgClauses?: boolean;
}

export class DocumentGenerationResult {
  @ApiProperty({ description: 'Generated document content' })
  content: string;

  @ApiProperty({
    enum: DocumentType,
    description: 'Type of generated document',
  })
  documentType: DocumentType;

  @ApiProperty({ description: 'Metadata about the generation process' })
  metadata: {
    generatedAt: string;
    generationDurationMs: number;
    modelUsed: string;
    clausesUsed: number;
    organizationClausesUsed: number;
  };

  @ApiProperty({ description: 'Suggested clauses that were included' })
  suggestedClauses: Array<{
    type: string;
    content: string;
    source: 'library' | 'ai_generated' | 'organization';
    confidence: number;
  }>;

  @ApiProperty({ description: 'Recommendations for document improvement' })
  recommendations: string[];
}

export class RelatedDocumentGenerationResult {
  @ApiProperty({ description: 'Generated related documents' })
  documents: Array<{
    type: RelatedDocumentType;
    title: string;
    content: string;
    metadata: Record<string, any>;
  }>;

  @ApiProperty({ description: 'Generation metadata' })
  metadata: {
    primaryDocumentId: string;
    generatedAt: string;
    generationDurationMs: number;
    documentsGenerated: number;
  };
}

export class ClauseIntelligenceResult {
  @ApiProperty({ description: 'Suggested clauses for the document' })
  suggestedClauses: Array<{
    type: string;
    content: string;
    relevanceScore: number;
    source: 'library' | 'ai_generated' | 'organization';
    position: 'beginning' | 'middle' | 'end' | 'specific_section';
    explanation: string;
  }>;

  @ApiProperty({ description: 'Auto-population suggestions' })
  autoPopulationSuggestions: Array<{
    sectionName: string;
    suggestedContent: string;
    confidence: number;
  }>;

  @ApiProperty({ description: 'Missing essential clauses' })
  missingClauses: Array<{
    type: string;
    importance: 'critical' | 'recommended' | 'optional';
    description: string;
  }>;
}

export class ClauseLibraryBuildOptionsDto {
  @ApiPropertyOptional({
    description: 'Include existing clauses in the analysis',
  })
  @IsOptional()
  @IsBoolean()
  includeExistingClauses?: boolean;

  @ApiPropertyOptional({
    description: 'Minimum confidence threshold for clause extraction (0.0-1.0)',
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @IsNumber()
  minimumConfidence?: number;

  @ApiPropertyOptional({
    description: 'Maximum number of clauses to extract per category',
  })
  @IsOptional()
  @IsNumber()
  maxClausesPerCategory?: number;

  @ApiPropertyOptional({
    description: 'Filter by specific document types',
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  documentTypes?: string[];

  @ApiPropertyOptional({
    description: 'Depth of analysis to perform',
    enum: ['basic', 'detailed', 'comprehensive'],
    default: 'detailed',
  })
  @IsOptional()
  @IsEnum(['basic', 'detailed', 'comprehensive'])
  analysisDepth?: 'basic' | 'detailed' | 'comprehensive';
}

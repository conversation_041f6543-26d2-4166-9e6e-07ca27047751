import { IsBoolean, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class DocumentProcessingJobRequestDto {
  @ApiProperty({
    description: 'Whether to prioritize this document processing job',
    required: false,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  priority?: boolean;

  @ApiProperty({
    description: 'Whether to extract metadata from the document',
    required: false,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  extractMetadata?: boolean;

  @ApiProperty({
    description: 'Whether to generate a summary of the document',
    required: false,
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  generateSummary?: boolean;
}

export class DocumentProcessingJobResponseDto {
  @ApiProperty({
    description: 'The ID of the processing job',
  })
  @IsString()
  jobId: string;

  @ApiProperty({
    description: 'The ID of the document being processed',
  })
  @IsString()
  documentId: string;

  @ApiProperty({
    description: 'The status of the processing job',
    enum: ['waiting', 'active', 'completed', 'failed'],
  })
  @IsString()
  status: string;
}

export class DocumentJobStatusResponseDto {
  @ApiProperty({
    description: 'The ID of the job',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'The status of the job',
    enum: ['waiting', 'active', 'completed', 'failed'],
  })
  @IsString()
  status: string;

  @ApiProperty({
    description: 'The progress of the job (0-100)',
  })
  progress: number;

  @ApiProperty({
    description: 'The document ID associated with the job',
  })
  @IsString()
  documentId: string;

  @ApiProperty({
    description: 'The reason for failure, if the job failed',
    required: false,
  })
  @IsString()
  @IsOptional()
  failedReason?: string;

  @ApiProperty({
    description: 'The number of attempts made to process the job',
    required: false,
  })
  @IsOptional()
  attempts?: number;
}

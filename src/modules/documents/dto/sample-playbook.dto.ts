import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum, IsArray } from 'class-validator';

export class SamplePlaybookFiltersDto {
  @ApiPropertyOptional({
    description: 'Filter by contract type',
    enum: ['service_agreement', 'employment_contract', 'nda', 'software_license', 'consulting_agreement', 'partnership_agreement', 'real_estate', 'vendor_agreement'],
  })
  @IsOptional()
  @IsEnum(['service_agreement', 'employment_contract', 'nda', 'software_license', 'consulting_agreement', 'partnership_agreement', 'real_estate', 'vendor_agreement'])
  contractType?: string;

  @ApiPropertyOptional({
    description: 'Filter by industry',
    enum: ['technology', 'healthcare', 'finance', 'real_estate', 'consulting', 'manufacturing', 'retail', 'general'],
  })
  @IsOptional()
  @IsEnum(['technology', 'healthcare', 'finance', 'real_estate', 'consulting', 'manufacturing', 'retail', 'general'])
  industry?: string;

  @ApiPropertyOptional({
    description: 'Filter by difficulty level',
    enum: ['beginner', 'intermediate', 'expert'],
  })
  @IsOptional()
  @IsEnum(['beginner', 'intermediate', 'expert'])
  difficulty?: string;

  @ApiPropertyOptional({
    description: 'Filter by tags (comma-separated)',
    type: String,
  })
  @IsOptional()
  @IsString()
  tags?: string;
}

export class CloneSamplePlaybookDto {
  @ApiProperty({
    description: 'The ID of the sample playbook to clone',
    example: '507f1f77bcf86cd799439011',
  })
  @IsString()
  samplePlaybookId: string;

  @ApiProperty({
    description: 'The ID of the document to apply the playbook to',
    example: '507f1f77bcf86cd799439012',
  })
  @IsString()
  documentId: string;
}

export class SamplePlaybookResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the sample playbook',
    example: '507f1f77bcf86cd799439011',
  })
  _id: string;

  @ApiProperty({
    description: 'Name of the sample playbook template',
    example: 'Service Agreement - Basic Negotiation',
  })
  templateName: string;

  @ApiProperty({
    description: 'Description of the sample playbook',
    example: 'Essential negotiation strategies for standard service agreements',
  })
  templateDescription: string;

  @ApiProperty({
    description: 'Type of contract this playbook applies to',
    enum: ['service_agreement', 'employment_contract', 'nda', 'software_license', 'consulting_agreement', 'partnership_agreement', 'real_estate', 'vendor_agreement'],
    example: 'service_agreement',
  })
  contractType: string;

  @ApiProperty({
    description: 'Industry this playbook is designed for',
    enum: ['technology', 'healthcare', 'finance', 'real_estate', 'consulting', 'manufacturing', 'retail', 'general'],
    example: 'general',
  })
  industry: string;

  @ApiProperty({
    description: 'Difficulty level of the negotiation strategies',
    enum: ['beginner', 'intermediate', 'expert'],
    example: 'beginner',
  })
  difficulty: string;

  @ApiProperty({
    description: 'Tags associated with this playbook',
    type: [String],
    example: ['service', 'basic', 'beginner'],
  })
  tags: string[];

  @ApiProperty({
    description: 'Number of times this sample has been used',
    example: 42,
  })
  usageCount: number;

  @ApiProperty({
    description: 'Negotiation strategies included in this playbook',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        section: { type: 'string', example: 'Payment Terms' },
        recommendations: { 
          type: 'array', 
          items: { type: 'string' },
          example: ['Negotiate for shorter payment cycles', 'Include late payment penalties']
        },
        riskLevel: { type: 'string', enum: ['low', 'medium', 'high'], example: 'low' },
        priority: { type: 'number', example: 1 },
        alternativeLanguage: { 
          type: 'string', 
          example: 'Payment shall be due within fifteen (15) days of invoice date.'
        },
      },
    },
  })
  strategies: Array<{
    section: string;
    recommendations: string[];
    riskLevel: 'low' | 'medium' | 'high';
    priority: number;
    alternativeLanguage?: string;
  }>;

  @ApiProperty({
    description: 'Overall assessment and guidance',
    example: 'Service agreements require clear scope definition and favorable payment terms.',
  })
  overallAssessment: string;

  @ApiProperty({
    description: 'Key leverage points for negotiation',
    type: [String],
    example: ['Specialized expertise and skills', 'Timeline flexibility'],
  })
  keyLeveragePoints: string[];

  @ApiProperty({
    description: 'Deal breakers to watch out for',
    type: [String],
    example: ['Unlimited liability exposure', 'Work-for-hire clauses'],
  })
  dealBreakers: string[];
}

export class PlaybookStatsDto {
  @ApiProperty({
    description: 'Total number of sample playbooks available',
    example: 8,
  })
  totalSamples: number;

  @ApiProperty({
    description: 'Total number of user-generated playbooks',
    example: 156,
  })
  totalUserPlaybooks: number;

  @ApiProperty({
    description: 'Most popular sample playbooks',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        templateName: { type: 'string' },
        usageCount: { type: 'number' },
      },
    },
  })
  popularSamples: Array<{
    templateName: string;
    usageCount: number;
  }>;

  @ApiProperty({
    description: 'Distribution of playbooks by contract type',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        contractType: { type: 'string' },
        count: { type: 'number' },
      },
    },
  })
  contractTypeDistribution: Array<{
    contractType: string;
    count: number;
  }>;
}

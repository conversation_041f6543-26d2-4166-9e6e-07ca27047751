import {
  IsString,
  <PERSON>Optional,
  IsEnum,
  IsArray,
  IsNumber,
  IsBoolean,
  IsObject,
  ValidateNested,
  Min,
  Max,
  IsDate,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class TermsDto {
  @ApiPropertyOptional({ description: 'Price amount' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  price?: number;

  @ApiPropertyOptional({ description: 'Currency code' })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiPropertyOptional({ description: 'Payment terms' })
  @IsOptional()
  @IsString()
  paymentTerms?: string;

  @ApiPropertyOptional({ description: 'Delivery date' })
  @IsOptional()
  deliveryDate?: Date;

  @ApiPropertyOptional({ type: [String], description: 'Warranty terms' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  warranties?: string[];

  @ApiPropertyOptional({ type: [String], description: 'Liability terms' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  liabilities?: string[];

  @ApiPropertyOptional({ type: [String], description: 'Termination clauses' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  terminationClauses?: string[];

  @ApiPropertyOptional({ type: [String], description: 'IP terms' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  intellectualProperty?: string[];

  @ApiPropertyOptional({ type: [String], description: 'Confidentiality terms' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  confidentiality?: string[];

  @ApiPropertyOptional({ description: 'Custom terms' })
  @IsOptional()
  @IsObject()
  customTerms?: Record<string, any>;
}

export class PartyProfileDto {
  @ApiProperty({ description: 'Party name' })
  @IsString()
  name: string;

  @ApiProperty({
    enum: [
      'buyer',
      'seller',
      'vendor',
      'client',
      'contractor',
      'licensor',
      'licensee',
    ],
  })
  @IsEnum([
    'buyer',
    'seller',
    'vendor',
    'client',
    'contractor',
    'licensor',
    'licensee',
  ])
  role:
    | 'buyer'
    | 'seller'
    | 'vendor'
    | 'client'
    | 'contractor'
    | 'licensor'
    | 'licensee';

  @ApiProperty({ type: [String], description: 'Party priorities' })
  @IsArray()
  @IsString({ each: true })
  priorities: string[];

  @ApiProperty({
    enum: [
      'aggressive',
      'collaborative',
      'analytical',
      'competitive',
      'accommodating',
    ],
  })
  @IsEnum([
    'aggressive',
    'collaborative',
    'analytical',
    'competitive',
    'accommodating',
  ])
  negotiationStyle:
    | 'aggressive'
    | 'collaborative'
    | 'analytical'
    | 'competitive'
    | 'accommodating';

  @ApiProperty({ description: 'Party constraints' })
  @IsObject()
  constraints: Record<string, any>;

  @ApiPropertyOptional({ description: 'Budget information' })
  @IsOptional()
  @IsObject()
  budget?: {
    min: number;
    max: number;
    currency: string;
  };

  @ApiPropertyOptional({ description: 'Timeline preferences' })
  @IsOptional()
  @IsObject()
  timeline?: {
    urgency: 'low' | 'medium' | 'high';
    deadline?: Date;
  };
}

export class NegotiationConstraintsDto {
  @ApiProperty({ description: 'Maximum number of rounds' })
  @IsNumber()
  @Min(1)
  @Max(50)
  maxRounds: number;

  @ApiPropertyOptional({ description: 'Time limit in minutes' })
  @IsOptional()
  @IsNumber()
  @Min(5)
  @Max(480)
  timeLimit?: number;

  @ApiProperty({ type: [String], description: 'Must-have terms' })
  @IsArray()
  @IsString({ each: true })
  mustHaveTerms: string[];

  @ApiProperty({ type: [String], description: 'Deal breaker terms' })
  @IsArray()
  @IsString({ each: true })
  dealBreakers: string[];

  @ApiProperty({ type: [String], description: 'Flexible terms' })
  @IsArray()
  @IsString({ each: true })
  flexibleTerms: string[];
}

export class TimelineOptionsDto {
  @ApiProperty({ description: 'Start date' })
  @IsDate()
  @Type(() => Date)
  startDate: Date;

  @ApiProperty({ description: 'Expected duration in minutes' })
  @IsNumber()
  @Min(5)
  @Max(480)
  expectedDuration: number;

  @ApiProperty({ description: 'Maximum duration in minutes' })
  @IsNumber()
  @Min(5)
  @Max(480)
  maxDuration: number;

  @ApiPropertyOptional({
    description: 'Break duration between rounds in minutes',
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(60)
  breakDuration?: number;
}

export class CreateNegotiationScenarioDto {
  @ApiProperty({ description: 'Name of the negotiation scenario' })
  @IsString()
  name: string;

  @ApiProperty({ description: 'Description of the scenario' })
  @IsString()
  description: string;

  @ApiProperty({ description: 'Industry context' })
  @IsString()
  industry: string;

  @ApiProperty({ description: 'Type of contract being negotiated' })
  @IsString()
  contractType: string;

  @ApiProperty({ enum: ['beginner', 'intermediate', 'expert'] })
  @IsEnum(['beginner', 'intermediate', 'expert'])
  difficulty: 'beginner' | 'intermediate' | 'expert';

  @ApiProperty({
    type: [Object],
    description: 'Parties involved in negotiation',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PartyProfileDto)
  parties: PartyProfileDto[];

  @ApiProperty({ description: 'Initial offer terms' })
  @IsObject()
  @ValidateNested()
  @Type(() => TermsDto)
  initialOffer: TermsDto;

  @ApiProperty({ description: 'Negotiation constraints' })
  @IsObject()
  @ValidateNested()
  @Type(() => NegotiationConstraintsDto)
  constraints: NegotiationConstraintsDto;

  @ApiProperty({ description: 'Timeline options' })
  @IsObject()
  @ValidateNested()
  @Type(() => TimelineOptionsDto)
  timeline: TimelineOptionsDto;

  @ApiPropertyOptional({ description: 'Whether this is a template' })
  @IsOptional()
  @IsBoolean()
  isTemplate?: boolean;

  @ApiPropertyOptional({
    type: [String],
    description: 'Tags for categorization',
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}

export class AIPersonalityProfileDto {
  @ApiPropertyOptional({ description: 'Aggressiveness level (0-1)' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  aggressiveness?: number;

  @ApiPropertyOptional({ description: 'Flexibility level (0-1)' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  flexibility?: number;

  @ApiPropertyOptional({ description: 'Risk tolerance (0-1)' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  riskTolerance?: number;

  @ApiPropertyOptional({
    enum: ['formal', 'casual', 'technical', 'diplomatic'],
  })
  @IsOptional()
  @Transform(({ value }) => value?.toLowerCase())
  @IsEnum(['formal', 'casual', 'technical', 'diplomatic'])
  communicationStyle?: 'formal' | 'casual' | 'technical' | 'diplomatic';

  @ApiPropertyOptional({ enum: ['fast', 'moderate', 'deliberate'] })
  @IsOptional()
  @Transform(({ value }) => value?.toLowerCase())
  @IsEnum(['fast', 'moderate', 'deliberate'])
  decisionSpeed?: 'fast' | 'moderate' | 'deliberate';

  @ApiPropertyOptional({ enum: ['early', 'gradual', 'late', 'minimal'] })
  @IsOptional()
  @Transform(({ value }) => value?.toLowerCase())
  @IsEnum(['early', 'gradual', 'late', 'minimal'])
  concessionPattern?: 'early' | 'gradual' | 'late' | 'minimal';
}

export class StartNegotiationSessionDto {
  @ApiProperty({ description: 'Scenario ID to use' })
  @IsString()
  scenarioId: string;

  @ApiPropertyOptional({ description: 'AI personality customization' })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => AIPersonalityProfileDto)
  aiPersonality?: AIPersonalityProfileDto;

  @ApiPropertyOptional({ description: 'Custom constraints override' })
  @IsOptional()
  @IsObject()
  customConstraints?: Partial<NegotiationConstraintsDto>;
}

export class UserMoveDto {
  @ApiProperty({ description: 'Offer terms' })
  @IsObject()
  @ValidateNested()
  @Type(() => TermsDto)
  offer: TermsDto;

  @ApiProperty({ description: 'Message to counterparty' })
  @IsString()
  message: string;

  @ApiPropertyOptional({ description: 'Strategy being used' })
  @IsOptional()
  @IsString()
  strategy?: string;

  @ApiPropertyOptional({ description: 'Reasoning for the move' })
  @IsOptional()
  @IsString()
  reasoning?: string;
}

export class MakeNegotiationMoveDto {
  @ApiProperty({ description: 'Session ID' })
  @IsString()
  sessionId: string;

  @ApiProperty({ description: 'User move details' })
  @IsObject()
  @ValidateNested()
  @Type(() => UserMoveDto)
  move: UserMoveDto;
}

export class NegotiationSimulatorOptionsDto {
  @ApiPropertyOptional({ description: 'Enable hints during negotiation' })
  @IsOptional()
  @IsBoolean()
  enableHints?: boolean = true;

  @ApiPropertyOptional({ description: 'Show AI reasoning' })
  @IsOptional()
  @IsBoolean()
  showAIReasoning?: boolean = false;

  @ApiPropertyOptional({ description: 'Allow pausing session' })
  @IsOptional()
  @IsBoolean()
  allowPausing?: boolean = true;

  @ApiPropertyOptional({ description: 'Record session for analysis' })
  @IsOptional()
  @IsBoolean()
  recordSession?: boolean = true;

  @ApiPropertyOptional({ description: 'Enable difficulty adjustment' })
  @IsOptional()
  @IsBoolean()
  difficultyAdjustment?: boolean = true;

  @ApiPropertyOptional({ description: 'Enable real-time analysis' })
  @IsOptional()
  @IsBoolean()
  realTimeAnalysis?: boolean = false;
}

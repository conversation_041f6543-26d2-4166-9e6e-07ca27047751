import { IsString, IsEnum, IsOptional, IsBoolean, IsArray, ValidateNested, IsUUID, IsNumber, IsObject, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export enum EnhancedComparisonType {
  SIMILARITIES = 'similarities',
  DIFFERENCES = 'differences',
  BOTH = 'both',
  VISUAL = 'visual',
}

export enum ExportFormatType {
  PDF = 'pdf',
  HTML = 'html',
  DOCX = 'docx',
}

export class EnhancedCompareDocumentsDto {
  @ApiProperty({
    description: 'First document content to compare',
    example: 'This is the content of document A...',
  })
  @IsString()
  documentA: string;

  @ApiProperty({
    description: 'Second document content to compare',
    example: 'This is the content of document B...',
  })
  @IsString()
  documentB: string;

  @ApiProperty({
    description: 'Type of comparison to perform',
    enum: EnhancedComparisonType,
    default: EnhancedComparisonType.BOTH,
  })
  @IsEnum(EnhancedComparisonType)
  @IsOptional()
  type: EnhancedComparisonType = EnhancedComparisonType.BOTH;

  @ApiProperty({
    description: 'Whether to include visual diff highlighting',
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  includeVisualDiff?: boolean = true;

  @ApiProperty({
    description: 'Whether to include section references',
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  includeSectionReferences?: boolean = true;

  @ApiProperty({
    description: 'Document A metadata for context',
    required: false,
  })
  @IsOptional()
  documentAMetadata?: {
    title?: string;
    documentType?: string;
    sections?: { title: string; reference: string }[];
  };

  @ApiProperty({
    description: 'Document B metadata for context',
    required: false,
  })
  @IsOptional()
  documentBMetadata?: {
    title?: string;
    documentType?: string;
    sections?: { title: string; reference: string }[];
  };
}

export class VersionComparisonDto {
  @ApiProperty({
    description: 'ID of the document to compare versions of',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  documentId: string;

  @ApiProperty({
    description: 'Base version number to compare from',
    example: 1,
  })
  @IsNumber()
  @Min(1)
  baseVersionNumber: number;

  @ApiProperty({
    description: 'Version number to compare to',
    example: 2,
  })
  @IsNumber()
  @Min(1)
  comparedVersionNumber: number;

  @ApiProperty({
    description: 'Whether to include visual diff highlighting',
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  includeVisualDiff?: boolean = true;

  @ApiProperty({
    description: 'Whether to include section references',
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  includeSectionReferences?: boolean = true;

  @ApiProperty({
    description: 'Additional options for version comparison',
    required: false,
    example: {
      includeVisualDiff: true,
      highlightSignificantChanges: true,
    },
  })
  @IsOptional()
  @IsObject()
  options?: Record<string, any>;
}

export class SectionReferenceDto {
  @ApiProperty({
    description: 'ID of the document containing the section',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  documentId: string;

  @ApiProperty({
    description: 'Section reference (e.g., "Section 3.1")',
    example: 'Section 3.1',
  })
  @IsString()
  sectionReference: string;

  @ApiProperty({
    description: 'Whether to include related sections',
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  includeRelatedSections?: boolean = true;
}

export class MultipleSectionReferencesDto {
  @ApiProperty({
    description: 'Array of section references to retrieve',
    type: [SectionReferenceDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SectionReferenceDto)
  sectionReferences: SectionReferenceDto[];
}

export class CreateDocumentVersionDto {
  @ApiProperty({
    description: 'ID of the document to create a version for',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsString()
  documentId: string;

  @ApiProperty({
    description: 'Content of the new document version',
    example: 'This is the updated content of the document...',
  })
  @IsString()
  content: string;

  @ApiProperty({
    description: 'Additional metadata for the document version',
    required: false,
    example: {
      title: 'Updated Contract - v2',
      notes: 'Fixed section 3.2 regarding payment terms',
    },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

/**
 * DTO for exporting comparison results
 */
export class ExportComparisonDto {
  @ApiProperty({
    description: 'Format to export the comparison in',
    enum: ExportFormatType,
    default: ExportFormatType.PDF,
  })
  @IsEnum(ExportFormatType)
  format: ExportFormatType = ExportFormatType.PDF;

  @ApiProperty({
    description: 'Whether to include metadata in the export',
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  includeMetadata?: boolean = true;

  @ApiProperty({
    description: 'Whether to highlight changes in the export',
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  highlightChanges?: boolean = true;

  @ApiProperty({
    description: 'Whether to include an executive summary in the export',
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  includeSummary?: boolean = true;

  @ApiProperty({
    description: 'Sections to include in the export',
    required: false,
    example: {
      include: ['section1', 'section2'],
      highlightIntensity: true,
    },
  })
  @IsOptional()
  @IsObject()
  sections?: {
    include: string[];
    highlightIntensity?: boolean;
  };

  @ApiProperty({
    description: 'Customization options for the export',
    required: false,
    example: {
      colors: {
        addition: '#00FF00',
        deletion: '#FF0000',
        modification: '#0000FF',
      },
      fonts: {
        family: 'Arial',
        size: 12,
      },
    },
  })
  @IsOptional()
  @IsObject()
  customization?: {
    colors?: {
      addition?: string;
      deletion?: string;
      modification?: string;
    };
    fonts?: {
      family?: string;
      size?: number;
    };
  };
}

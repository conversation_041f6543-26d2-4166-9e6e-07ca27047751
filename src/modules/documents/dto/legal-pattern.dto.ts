import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO representing a detected legal pattern for API responses.
 */
export class LegalPatternDto {
  @ApiPropertyOptional({
    description: 'Concise name for the pattern found (e.g., "Indemnification Clause"). Reflects the user query.',
    example: 'Indemnification Clause',
  })
  pattern_name?: string;

  @ApiPropertyOptional({
    description: 'The exact text segment from the document that represents the pattern.',
    example: 'The Contractor agrees to indemnify the Client...',
  })
  text_snippet?: string;

  @ApiPropertyOptional({
    description: 'Brief explanation of why this text snippet matches the user request.',
    example: 'This clause outlines the indemnification obligations of the Contractor.',
  })
  explanation?: string;

  @ApiPropertyOptional({
    description: 'Starting character index of the text_snippet (relative position needs context).',
    example: 1234,
  })
  start_char?: number;

  @ApiPropertyOptional({
    description: 'Ending character index of the text_snippet (relative position needs context).',
    example: 1456,
  })
  end_char?: number;

  // Original/Alternative fields (might be populated depending on detection method)
  @ApiPropertyOptional({
    description: 'Type of pattern (e.g., \'definition\', \'obligation\', \'clause\'). Might be less relevant with pattern_name.',
    example: 'clause',
  })
  type?: string;

  @ApiPropertyOptional({
    description: 'The actual text content of the pattern (might be redundant with text_snippet).',
    example: 'The Contractor agrees to indemnify the Client...',
  })
  content?: string;

  @ApiPropertyOptional({
    description: 'Starting index in the text (optional, might be redundant with start_char).',
    example: 1234,
  })
  startIndex?: number;

  @ApiPropertyOptional({
    description: 'Ending index in the text (optional, might be redundant with end_char).',
    example: 1456,
  })
  endIndex?: number;

  @ApiPropertyOptional({
    description: 'Additional metadata (e.g., section, clause number).',
    type: 'object',
    additionalProperties: true, // Allows any properties
    example: { section: '8.1', topic: 'Liability' },
  })
  metadata?: Record<string, any>;
}

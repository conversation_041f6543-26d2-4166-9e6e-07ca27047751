import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsArray,
  IsEnum,
  IsUUID,
  IsNotEmpty,
  ValidateNested,
  IsInt,
  Min,
  Max,
  ArrayMinSize,
  IsBoolean,
  IsObject,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { DepositionQuestionCategory, DepositionPreparationStatus } from '../interfaces/deposition.interface';

export class DepositionPreparationMetadataDto {
  @ApiProperty({
    required: false,
    description: 'Metadata about transcript analysis',
    type: Object,
    example: {
      analysisId: 'uuid',
      analyzedAt: '2025-05-21T04:20:40.000Z',
      analysisType: 'transcript',
      credibilityScore: 0.85,
      questionCount: 15
    }
  })
  @IsObject()
  @IsOptional()
  transcriptAnalysis?: {
    analysisId: string;
    analyzedAt: string;
    analysisType: string;
    credibilityScore: number;
    questionCount: number;
  };

  [key: string]: any;
}

export class CreateDepositionQuestionDto {
  @ApiProperty({ description: 'The text of the question' })
  @IsString()
  @IsNotEmpty()
  text: string;

  @ApiProperty({ enum: DepositionQuestionCategory, description: 'Category of the question' })
  @IsEnum(DepositionQuestionCategory)
  category: DepositionQuestionCategory;

  @ApiProperty({ description: 'Purpose of asking this question' })
  @IsString()
  @IsNotEmpty()
  purpose: string;

  @ApiProperty({ description: 'Target witness for this question', required: false })
  @IsString()
  @IsNotEmpty()
  targetWitness: string;

  @ApiProperty({ type: [String], description: 'Suggested follow-up questions', required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  suggestedFollowUps: string[] = [];

  @ApiProperty({ type: [String], description: 'Related document IDs', required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  relatedDocuments: string[] = [];

  @ApiProperty({ enum: ['high', 'medium', 'low'], description: 'Priority level of the question', default: 'medium' })
  @IsString()
  @IsEnum(['high', 'medium', 'low'])
  @IsOptional()
  priority: 'high' | 'medium' | 'low' = 'medium';

  @ApiProperty({ description: 'Additional notes for this question', required: false })
  @IsString()
  @IsOptional()
  notes: string = '';

  @ApiProperty({ description: 'Whether this is a follow-up question', required: false, default: false })
  @IsBoolean()
  @IsOptional()
  isFollowUp?: boolean = false;

  @ApiProperty({ 
    description: 'Additional metadata for the question', 
    required: false, 
    type: Object,
    example: { 
      aiGenerated: true,
      generationDate: '2023-01-01T00:00:00.000Z',
      modelUsed: 'gpt-4',
      confidence: 'high'
    }
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}

export class UpdateDepositionQuestionDto extends CreateDepositionQuestionDto {
  @ApiProperty({ description: 'Unique identifier for the question' })
  @IsUUID()
  id: string;
}

export class CreateDepositionPreparationDto {
  @ApiProperty({ description: 'Title of the deposition preparation' })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({ description: 'Description of the deposition preparation' })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({ type: [String], description: 'List of target witnesses' })
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  targetWitnesses: string[];

  @ApiProperty({ description: 'Case context or background information' })
  @IsString()
  @IsNotEmpty()
  caseContext: string;

  @ApiProperty({ type: [String], description: 'Key issues to address in the deposition' })
  @IsArray()
  @IsString({ each: true })
  @ArrayMinSize(1)
  keyIssues: string[];

  @ApiProperty({ type: [String], description: 'Related document IDs', required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  relatedDocumentIds?: string[];

  @ApiProperty({ description: 'Case ID associated with this deposition preparation' })
  @IsString()
  @IsOptional()
  caseId?: string;
}

export class UpdateDepositionPreparationDto {
  @ApiProperty({ description: 'Title of the deposition preparation', required: false })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({ description: 'Description of the deposition preparation', required: false })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({ type: [String], description: 'List of target witnesses', required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  targetWitnesses?: string[];

  @ApiProperty({ description: 'Case context or background information', required: false })
  @IsString()
  @IsOptional()
  caseContext?: string;

  @ApiProperty({ type: [String], description: 'Key issues to address in the deposition', required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  keyIssues?: string[];

  @ApiProperty({ type: [String], description: 'Related document IDs', required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  relatedDocumentIds?: string[];

  @ApiProperty({ enum: DepositionPreparationStatus, description: 'Status of the deposition preparation', required: false })
  @IsEnum(DepositionPreparationStatus)
  @IsOptional()
  status?: DepositionPreparationStatus;

  @ApiProperty({
    type: DepositionPreparationMetadataDto,
    required: false,
    description: 'Additional metadata for the deposition preparation'
  })
  @ValidateNested()
  @Type(() => DepositionPreparationMetadataDto)
  @IsOptional()
  metadata?: DepositionPreparationMetadataDto;
}

export class GenerateQuestionsDto {
  @ApiProperty({ description: 'Deposition preparation ID to generate questions for' })
  @IsUUID()
  @IsOptional()
  depositionPreparationId?: string;

  @ApiProperty({ description: 'Case context for generating questions', required: false })
  @IsString()
  @IsOptional()
  caseContext?: string;

  @ApiProperty({ type: [String], description: 'Key issues to focus on', required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  keyIssues?: string[];

  @ApiProperty({ type: [String], description: 'Target witnesses', required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  targetWitnesses?: string[];

  @ApiProperty({ type: [String], description: 'Focus areas for question generation', required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  focusAreas?: string[];

  @ApiProperty({ description: 'Number of questions to generate', required: false, default: 10 })
  @IsInt()
  @Min(1)
  @Max(50)
  @IsOptional()
  questionCount?: number;

  @ApiProperty({ description: 'Whether to include follow-up questions', required: false, default: true })
  @IsBoolean()
  @IsOptional()
  includeFollowUps?: boolean;

  @ApiProperty({ type: [String], enum: Object.values(DepositionQuestionCategory), description: 'Categories of questions to generate', required: false })
  @IsArray()
  @Type(() => String)
  @Transform(({ value }) => {
    if (!Array.isArray(value)) return value;
    
    return [...new Set(value)].map((v: string) => {
      console.log(`Processing category: "${v}"`); // Log original input
      
      // Normalize string by removing special chars and standardizing spaces/hyphens
      const normalized = v.toUpperCase()
        .replace(/[\s\-_]+/g, '_')         // Convert spaces/hyphens/underscores to single underscore
        .replace(/[^A-Z_]/g, '')           // Remove any other special chars
        .replace(/_+/g, '_')               // Collapse multiple underscores
        .replace(/^_|_$/g, '');            // Trim leading/trailing underscores

      // Even more comprehensive mappings with common variations
      const specialMappings = {
        // General/Background related
        'BACKGROUND': DepositionQuestionCategory.GENERAL,
        'BACKGROUND_INFO': DepositionQuestionCategory.GENERAL,
        'BASIC_BACKGROUND': DepositionQuestionCategory.GENERAL,
        'GENERAL_QUESTIONS': DepositionQuestionCategory.GENERAL,
        'GENERAL_INFO': DepositionQuestionCategory.GENERAL,
        'WITNESS_BACKGROUND': DepositionQuestionCategory.GENERAL,

        // Credibility/Fact finding related
        'FACT_FINDING': DepositionQuestionCategory.CREDIBILITY,
        'IN_DEPTH_FACTS': DepositionQuestionCategory.CREDIBILITY,
        'FACTS': DepositionQuestionCategory.CREDIBILITY,
        'EVIDENCE_GATHERING': DepositionQuestionCategory.CREDIBILITY,
        'WITNESS_CREDIBILITY': DepositionQuestionCategory.CREDIBILITY,
        'WITNESS_RELIABILITY': DepositionQuestionCategory.CREDIBILITY,
        'RELIABILITY': DepositionQuestionCategory.CREDIBILITY,

        // Timeline/Consistency related
        'TIMELINE': DepositionQuestionCategory.CONSISTENCY,
        'TIMELINE_RELATED': DepositionQuestionCategory.CONSISTENCY,
        'CHRONOLOGY': DepositionQuestionCategory.CONSISTENCY,
        'SEQUENCE': DepositionQuestionCategory.CONSISTENCY,

        // Documentation related
        'DOCUMENTATION': DepositionQuestionCategory.DOCUMENTATION,
        'DOCUMENT_REVIEW': DepositionQuestionCategory.DOCUMENTATION,
        'RECORDS': DepositionQuestionCategory.DOCUMENTATION,
        'EVIDENCE_REVIEW': DepositionQuestionCategory.DOCUMENTATION
      };

      if (specialMappings[normalized]) {
        const mapped = specialMappings[normalized];
        console.log(`Direct mapping found: ${normalized} -> ${mapped}`);
        return mapped;
      }

      // Then check if it's a valid enum value
      if (normalized in DepositionQuestionCategory) {
        const mapped = DepositionQuestionCategory[normalized];
        console.log(`Enum value found: ${normalized} -> ${mapped}`);
        return mapped;
      }

      // Enhanced fallback logic based on more comprehensive partial matches
      if (normalized.includes('CREDIBILITY') || normalized.includes('WITNESS') ||
          normalized.includes('FACT') || normalized.includes('EVIDENCE') ||
          normalized.includes('RELIABILITY')) {
        console.log(`Fallback to CREDIBILITY for: ${normalized}`);
        return DepositionQuestionCategory.CREDIBILITY;
      }
      if (normalized.includes('CONSIST') || normalized.includes('TIMELINE') ||
          normalized.includes('CHRONOLOG') || normalized.includes('SEQUENCE')) {
        console.log(`Fallback to CONSISTENCY for: ${normalized}`);
        return DepositionQuestionCategory.CONSISTENCY;
      }
      if (normalized.includes('DOC') || normalized.includes('RECORD') ||
          normalized.includes('EXHIBIT') || normalized.includes('EVIDENCE')) {
        console.log(`Fallback to DOCUMENTATION for: ${normalized}`);
        return DepositionQuestionCategory.DOCUMENTATION;
      }

      // Default to GENERAL if no match
      console.log(`No mapping found, defaulting to GENERAL for: ${normalized}`);
      return DepositionQuestionCategory.GENERAL;
    });
  })
  @IsEnum(DepositionQuestionCategory, {
    each: true,
    message: 'Each question category must be one of: GENERAL, CREDIBILITY, CONSISTENCY, DOCUMENTATION'
  })
  @IsOptional()
  questionCategories?: DepositionQuestionCategory[];

  @ApiProperty({ enum: ['high', 'medium', 'low', 'all'], description: 'Priority level of questions to generate', required: false, default: 'all' })
  @IsString()
  @IsEnum(['high', 'medium', 'low', 'all'])
  @IsOptional()
  priorityLevel?: 'high' | 'medium' | 'low' | 'all';

  @ApiProperty({ type: [String], description: 'Document IDs to use for context', required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  documentIds?: string[];

  @ApiProperty({ description: 'Whether to use document context for generation', required: false, default: true })
  @IsBoolean()
  @IsOptional()
  useDocumentContext?: boolean;
}

import { Test, TestingModule } from '@nestjs/testing';
import { DocumentComparisonController } from '../controllers/document-comparison.controller';
import { DocumentComparisonService } from '../services/document-comparison.service';
import { ComparisonType } from '../dto/compare-documents.dto';
import { HttpException, HttpStatus } from '@nestjs/common';

describe('DocumentComparisonController', () => {
  let controller: DocumentComparisonController;
  let service: DocumentComparisonService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DocumentComparisonController],
      providers: [
        {
          provide: DocumentComparisonService,
          useValue: {
            compareDocuments: jest.fn()
          }
        }
      ]
    }).compile();

    controller = module.get<DocumentComparisonController>(DocumentComparisonController);
    service = module.get<DocumentComparisonService>(DocumentComparisonService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('compareDocuments', () => {
    it('should successfully compare documents', async () => {
      const mockResult = {
        result: {
          comparison: [
            {
              topic: 'Test Topic',
              similarities: ['Similar content'],
              differences: ['Different content']
            }
          ],
          summary: 'Test summary'
        },
        metadata: {
          comparisonType: ComparisonType.BOTH,
          timestamp: new Date(),
          documentStats: {
            documentA: { length: 10 },
            documentB: { length: 10 }
          }
        }
      };

      const request = {
        documentA: 'Content A',
        documentB: 'Content B',
        type: ComparisonType.BOTH
      };

      jest.spyOn(service, 'compareDocuments').mockResolvedValue(mockResult);

      const result = await controller.compareDocuments(request);

      expect(result).toEqual({
        status: 'success',
        data: mockResult,
        metadata: expect.objectContaining({
          timestamp: expect.any(String),
          documentStats: {
            documentA: { length: request.documentA.length },
            documentB: { length: request.documentB.length }
          }
        })
      });

      expect(service.compareDocuments).toHaveBeenCalledWith(
        request.documentA,
        request.documentB,
        { type: request.type }
      );
    });

    it('should handle empty document validation', async () => {
      const request = {
        documentA: '',
        documentB: 'Content B',
        type: ComparisonType.BOTH
      };

      await expect(controller.compareDocuments(request)).rejects.toThrow(
        new HttpException({
          status: 'error',
          message: 'Document content cannot be empty'
        }, HttpStatus.BAD_REQUEST)
      );
    });

    it('should handle service errors', async () => {
      const request = {
        documentA: 'Content A',
        documentB: 'Content B',
        type: ComparisonType.BOTH
      };

      jest.spyOn(service, 'compareDocuments').mockRejectedValue(new Error('Service error'));

      await expect(controller.compareDocuments(request)).rejects.toThrow(
        new HttpException({
          status: 'error',
          message: 'Error comparing documents',
          details: 'Service error'
        }, HttpStatus.INTERNAL_SERVER_ERROR)
      );
    });
  });
});
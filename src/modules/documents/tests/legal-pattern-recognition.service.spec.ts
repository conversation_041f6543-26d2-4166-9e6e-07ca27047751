import { Test, TestingModule } from '@nestjs/testing';
import { LegalPatternRecognitionService } from '../services/legal-pattern-recognition.service';

describe('LegalPatternRecognitionService', () => {
  let service: LegalPatternRecognitionService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [LegalPatternRecognitionService],
    }).compile();

    service = module.get<LegalPatternRecognitionService>(LegalPatternRecognitionService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('analyzeDocument', () => {
    it('should recognize sections', async () => {
      const text = `Section 1: Introduction
        This section introduces the agreement.
        
        Section 2: Definitions
        "Term" means something specific.`;

      const result = await service.analyzeDocument(text, 'doc-1');
      
      expect(result.patterns).toHaveLength(2);
      expect(result.patterns[0].type).toBe('section');
      expect(result.patterns[0].metadata.number).toBe('1');
      expect(result.patterns[0].metadata.title).toBe('Introduction');
    });

    it('should recognize definitions', async () => {
      const text = `"Agreement" means this contract between parties.
        "Term" means the period of this agreement.`;

      const result = await service.analyzeDocument(text, 'doc-1');
      
      expect(result.patterns).toHaveLength(2);
      expect(result.patterns[0].type).toBe('definition');
      expect(result.patterns[0].metadata.term).toBe('Agreement');
    });

    it('should recognize clauses', async () => {
      const text = `Clause 1: Payment Terms
        The payment shall be made monthly.`;

      const result = await service.analyzeDocument(text, 'doc-1');
      
      expect(result.patterns).toHaveLength(1);
      expect(result.patterns[0].type).toBe('clause');
      expect(result.patterns[0].metadata.number).toBe('1');
      expect(result.patterns[0].metadata.title).toBe('Payment Terms');
    });

    it('should maintain correct order of patterns', async () => {
      const text = `Section 1: Overview
        "Term" means duration.
        Clause 1: Terms`;

      const result = await service.analyzeDocument(text, 'doc-1');
      
      expect(result.patterns).toHaveLength(3);
      expect(result.patterns[0].type).toBe('section');
      expect(result.patterns[1].type).toBe('definition');
      expect(result.patterns[2].type).toBe('clause');
    });

    it('should handle documents with no patterns', async () => {
      const text = 'Simple text without any legal patterns.';
      const result = await service.analyzeDocument(text, 'doc-1');
      expect(result.patterns).toHaveLength(0);
    });
  });

  describe('validatePattern', () => {
    it('should validate section patterns', async () => {
      const pattern = {
        type: 'section',
        content: 'Section 1: Introduction',
        startIndex: 0,
        endIndex: 22,
        metadata: {
          number: '1',
          title: 'Introduction'
        }
      };

      expect(await service.validatePattern(pattern)).toBe(true);
    });

    it('should reject invalid patterns', async () => {
      const pattern = {
        type: 'unknown',
        content: 'Invalid pattern',
        startIndex: 0,
        endIndex: 14
      };

      expect(await service.validatePattern(pattern)).toBe(false);
    });
  });
});
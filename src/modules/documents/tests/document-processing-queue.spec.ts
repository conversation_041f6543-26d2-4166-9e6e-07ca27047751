import { Test, TestingModule } from '@nestjs/testing';
import { DocumentProcessingQueueService } from '../services/document-processing-queue.service';
import { DocumentProcessingProcessor, DOCUMENT_PROCESSING_QUEUE } from '../services/document-processing.processor';
import { BullModule, getQueueToken } from '@nestjs/bull';
import { Queue } from 'bull';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { queueConfig } from '../../../config/queue.config';

// Create a mock class for DocumentsService
class MockDocumentsService {
  updateDocumentStatus = jest.fn();
  processDocument = jest.fn();
  extractMetadata = jest.fn();
  generateSummary = jest.fn();
  documentExists = jest.fn();
}

describe('Document Processing Queue', () => {
  let service: DocumentProcessingQueueService;
  let processor: DocumentProcessingProcessor;
  let queue: Queue;
  let mockDocumentsService: MockDocumentsService;
  
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          load: [queueConfig],
        }),
        BullModule.forRootAsync({
          imports: [ConfigModule],
          useFactory: (configService: ConfigService) => ({
            redis: {
              host: 'localhost',
              port: 6379,
            },
            defaultJobOptions: {
              attempts: 3,
              backoff: {
                type: 'exponential',
                delay: 1000,
              },
              removeOnComplete: true,
            },
          }),
          inject: [ConfigService],
        }),
        BullModule.registerQueue({
          name: DOCUMENT_PROCESSING_QUEUE,
        }),
      ],
      providers: [
        DocumentProcessingQueueService,
        DocumentProcessingProcessor,
        {
          provide: 'DocumentsService',
          useClass: MockDocumentsService,
        },
      ],
    }).compile();

    service = module.get<DocumentProcessingQueueService>(DocumentProcessingQueueService);
    processor = module.get<DocumentProcessingProcessor>(DocumentProcessingProcessor);
    queue = module.get<Queue>(getQueueToken(DOCUMENT_PROCESSING_QUEUE));
    mockDocumentsService = module.get<MockDocumentsService>('DocumentsService');
    
    // Clear any existing jobs
    await queue.empty();
  });

  afterEach(async () => {
    await queue.empty();
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(processor).toBeDefined();
    expect(queue).toBeDefined();
  });

  describe('queueDocumentProcessing', () => {
    it('should add a job to the queue', async () => {
      const addSpy = jest.spyOn(queue, 'add');
      const documentId = 'test-document-id';
      const options = {
        priority: true,
        extractMetadata: true,
        generateSummary: true,
      };

      await service.queueDocumentProcessing(documentId, 'user-id', options);

      expect(addSpy).toHaveBeenCalledWith(
        'process-document',
        {
          documentId,
          userId: 'user-id',
          options,
        },
        expect.objectContaining({
          priority: 1,  // High priority
          attempts: 3,
          backoff: expect.objectContaining({
            type: 'exponential',
            delay: 1000,
          }),
        }),
      );
    });
  });

  describe('DocumentProcessingProcessor', () => {
    it('should process a document', async () => {
      const documentId = 'test-document-id';
      mockDocumentsService.documentExists.mockResolvedValue(true);
      mockDocumentsService.processDocument.mockResolvedValue({ text: 'Test content' });

      const job = {
        data: {
          documentId,
          options: {
            extractMetadata: false,
            generateSummary: false,
          },
        },
        queue: {
          add: jest.fn(),
        },
        id: 'job-id',
      };

      await processor.processDocument(job as any);

      expect(mockDocumentsService.updateDocumentStatus).toHaveBeenCalledWith(
        documentId, 
        'processing'
      );
      
      expect(mockDocumentsService.processDocument).toHaveBeenCalledWith(documentId);
      
      expect(mockDocumentsService.updateDocumentStatus).toHaveBeenCalledWith(
        documentId, 
        'completed'
      );
    });

    it('should handle errors during processing', async () => {
      const documentId = 'test-document-id';
      const error = new Error('Processing failed');
      mockDocumentsService.processDocument.mockRejectedValue(error);

      const job = {
        data: {
          documentId,
          options: {},
        },
        queue: {
          add: jest.fn(),
        },
        id: 'job-id',
      };

      // Using try/catch because we expect the processor to throw
      try {
        await processor.processDocument(job as any);
      } catch (e) {
        expect(e).toBe(error); // The original error should be re-thrown
      }

      expect(mockDocumentsService.updateDocumentStatus).toHaveBeenCalledWith(
        documentId, 
        'failed',
        error.message
      );
    });

    it('should queue metadata extraction when requested', async () => {
      const documentId = 'test-document-id';
      mockDocumentsService.processDocument.mockResolvedValue({ text: 'Test content' });

      const job = {
        data: {
          documentId,
          options: {
            extractMetadata: true,
            generateSummary: false,
          },
        },
        queue: {
          add: jest.fn(),
        },
        id: 'job-id',
      };

      await processor.processDocument(job as any);

      expect(job.queue.add).toHaveBeenCalledWith(
        'extract-metadata',
        {
          documentId,
          parentJobId: 'job-id',
        },
      );
    });
  });

  // Additional tests for other methods would follow
});

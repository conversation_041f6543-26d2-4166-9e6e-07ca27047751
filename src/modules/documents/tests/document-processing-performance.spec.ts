import { Test, TestingModule } from '@nestjs/testing';
import { DocumentProcessingService } from '../services/document-processing.service';
import { DocumentProcessingMetricsService } from '../services/document-processing-metrics.service';
import { DocumentProcessingCacheService } from '../services/document-processing-cache.service';
import { DocumentProcessingGateway } from '../gateways/document-processing.gateway';
import { getModelToken } from '@nestjs/mongoose';
import { getQueueToken } from '@nestjs/bull';
import { ConfigService } from '@nestjs/config';
import { TenantContextService } from '../../tenant/tenant-context.service';

describe('Document Processing Performance', () => {
  let service: DocumentProcessingService;
  let metricsService: DocumentProcessingMetricsService;
  let cacheService: DocumentProcessingCacheService;
  let gateway: DocumentProcessingGateway;

  const mockDocument = {
    id: 'test-doc-id',
    originalName: 'test.pdf',
    size: 1024 * 1024, // 1MB
    mimeType: 'application/pdf',
    organizationId: 'test-org',
    status: 'uploaded',
  };

  const mockQueue = {
    add: jest.fn().mockResolvedValue({ id: 'job-123' }),
    getActive: jest.fn().mockResolvedValue([]),
    getWaiting: jest.fn().mockResolvedValue([]),
    getFailed: jest.fn().mockResolvedValue([]),
  };

  const mockRedis = {
    get: jest.fn(),
    set: jest.fn(),
    setex: jest.fn(),
    del: jest.fn(),
    keys: jest.fn().mockResolvedValue([]),
    lrange: jest.fn().mockResolvedValue([]),
    lpush: jest.fn(),
    ltrim: jest.fn(),
    memory: jest.fn().mockResolvedValue(1024 * 1024),
  };

  const mockModel = {
    create: jest.fn().mockResolvedValue(mockDocument),
    findById: jest.fn().mockResolvedValue(mockDocument),
    find: jest.fn().mockResolvedValue([mockDocument]),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DocumentProcessingService,
        DocumentProcessingMetricsService,
        DocumentProcessingCacheService,
        DocumentProcessingGateway,
        {
          provide: getModelToken('Document'),
          useValue: mockModel,
        },
        {
          provide: getQueueToken('document-processing'),
          useValue: mockQueue,
        },
        {
          provide: 'default_IORedisModuleConnectionToken',
          useValue: mockRedis,
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue('test-value'),
          },
        },
        {
          provide: TenantContextService,
          useValue: {
            getCurrentUserId: jest.fn().mockReturnValue('test-user'),
            getCurrentOrganization: jest.fn().mockReturnValue('test-org'),
          },
        },
      ],
    }).compile();

    service = module.get<DocumentProcessingService>(DocumentProcessingService);
    metricsService = module.get<DocumentProcessingMetricsService>(DocumentProcessingMetricsService);
    cacheService = module.get<DocumentProcessingCacheService>(DocumentProcessingCacheService);
    gateway = module.get<DocumentProcessingGateway>(DocumentProcessingGateway);
  });

  describe('Asynchronous Processing', () => {
    it('should create document record immediately', async () => {
      const file = {
        originalname: 'test.pdf',
        filename: 'test.pdf',
        size: 1024 * 1024,
        mimetype: 'application/pdf',
        path: 'test-path',
        buffer: Buffer.from('test content'),
      } as Express.Multer.File;

      const result = await service.createDocumentRecord(file, {
        organizationId: 'test-org',
        ownerId: 'test-user',
      });

      expect(result).toBeDefined();
      expect(result.id).toBe('test-doc-id');
      expect(mockModel.create).toHaveBeenCalled();
    });

    it('should queue processing without blocking', async () => {
      const jobId = await service.queueDocumentProcessing('test-doc-id', 'test-user', {
        extractMetadata: true,
        generateSummary: true,
      });

      expect(jobId).toBe('job-123');
      expect(mockQueue.add).toHaveBeenCalledWith(
        'process-document',
        expect.objectContaining({
          documentId: 'test-doc-id',
          userId: 'test-user',
        }),
        expect.any(Object)
      );
    });
  });

  describe('Processing Status Tracking', () => {
    it('should return processing status', async () => {
      mockQueue.getActive.mockResolvedValueOnce([
        {
          id: 'job-123',
          data: { documentId: 'test-doc-id' },
          progress: jest.fn().mockResolvedValue(50),
          timestamp: Date.now() - 30000, // 30 seconds ago
        },
      ]);

      const status = await service.getProcessingStatus('test-doc-id');

      expect(status).toEqual({
        status: 'processing',
        progress: 50,
        jobId: 'job-123',
        estimatedTimeRemaining: expect.any(Number),
      });
    });

    it('should detect queued documents', async () => {
      mockQueue.getWaiting.mockResolvedValueOnce([
        { id: 'job-456', data: { documentId: 'test-doc-id' } },
        { id: 'job-789', data: { documentId: 'other-doc' } },
      ]);

      const status = await service.getProcessingStatus('test-doc-id');

      expect(status.status).toBe('queued');
      expect(status.estimatedTimeRemaining).toBeGreaterThan(0);
    });
  });

  describe('Caching Performance', () => {
    it('should cache processing results by content hash', async () => {
      const content = Buffer.from('test document content');
      const hash = cacheService.generateFileHash(content);

      await cacheService.cacheByContentHash(hash, {
        documentId: 'test-doc-id',
        fileHash: hash,
        content: content.toString(),
        metadata: {},
        processingTime: 5000,
      });

      expect(mockRedis.setex).toHaveBeenCalled();
    });

    it('should find cached results by hash', async () => {
      const hash = 'test-hash';
      mockRedis.get.mockResolvedValueOnce(JSON.stringify({
        documentId: 'cached-doc',
        fileHash: hash,
        content: 'cached content',
        metadata: {},
        processingTime: 3000,
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 3600000).toISOString(),
      }));

      const cached = await cacheService.findByContentHash(hash);

      expect(cached).toBeDefined();
      expect(cached?.documentId).toBe('cached-doc');
    });

    it('should provide cache statistics', async () => {
      mockRedis.keys
        .mockResolvedValueOnce(['doc_processing:doc1', 'doc_processing:doc2'])
        .mockResolvedValueOnce(['doc_hash:hash1', 'doc_hash:hash2', 'doc_hash:hash3']);

      const stats = await cacheService.getCacheStats();

      expect(stats.totalKeys).toBe(5);
      expect(stats.documentKeys).toBe(2);
      expect(stats.hashKeys).toBe(3);
    });
  });

  describe('Metrics Collection', () => {
    it('should record processing events', async () => {
      const event = {
        documentId: 'test-doc-id',
        eventType: 'completed' as const,
        timestamp: new Date(),
        processingTime: 15000,
        fileSize: 1024 * 1024,
        mimeType: 'application/pdf',
      };

      await metricsService.recordEvent(event);

      expect(mockRedis.lpush).toHaveBeenCalledWith(
        'doc_processing_events',
        expect.stringContaining('test-doc-id')
      );
    });

    it('should calculate processing statistics', async () => {
      const mockEvents = [
        {
          documentId: 'doc1',
          eventType: 'completed',
          timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 min ago
          processingTime: 10000,
        },
        {
          documentId: 'doc2',
          eventType: 'completed',
          timestamp: new Date(Date.now() - 15 * 60 * 1000), // 15 min ago
          processingTime: 20000,
        },
        {
          documentId: 'doc3',
          eventType: 'failed',
          timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 min ago
        },
      ];

      mockRedis.lrange.mockResolvedValueOnce(
        mockEvents.map(event => JSON.stringify({
          ...event,
          timestamp: event.timestamp.toISOString(),
        }))
      );

      const stats = await metricsService.getStatistics(1); // Last hour

      expect(stats.totalProcessed).toBe(3);
      expect(stats.successCount).toBe(2);
      expect(stats.failureCount).toBe(1);
      expect(stats.averageTime).toBe(15000);
    });

    it('should detect performance alerts', async () => {
      // Mock high error rate scenario
      mockRedis.get.mockResolvedValueOnce(JSON.stringify({
        totalDocuments: 100,
        averageProcessingTime: 350000, // 5.8 minutes - above threshold
        successRate: 0.8, // 80% success rate
        errorRate: 0.2, // 20% error rate - above threshold
        queueLength: 150, // Above threshold
        activeJobs: 5,
        throughputPerHour: 10,
        peakProcessingTime: 600000,
        lastUpdated: new Date().toISOString(),
      }));

      const alerts = await metricsService.checkAlerts();

      expect(alerts).toContain(expect.stringContaining('High error rate'));
      expect(alerts).toContain(expect.stringContaining('High queue length'));
      expect(alerts).toContain(expect.stringContaining('Slow processing'));
    });
  });

  describe('WebSocket Updates', () => {
    it('should send processing updates', async () => {
      const mockSocket = {
        emit: jest.fn(),
        data: { userId: 'test-user', organizationId: 'test-org' },
      };

      // Mock user socket mapping
      gateway['userSockets'] = new Map([['test-user', new Set([mockSocket as any])]]);
      gateway['documentSubscriptions'] = new Map([['test-doc-id', new Set(['test-user'])]]);

      await gateway.sendProcessingUpdate({
        documentId: 'test-doc-id',
        status: 'processing',
        progress: 75,
        message: 'Almost complete...',
      });

      expect(mockSocket.emit).toHaveBeenCalledWith('processing-update', {
        documentId: 'test-doc-id',
        status: 'processing',
        progress: 75,
        message: 'Almost complete...',
      });
    });
  });
});

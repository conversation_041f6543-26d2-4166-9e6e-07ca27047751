import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { TenantContext } from '@modules/auth/tenant/tenant-context';

export interface ProcessingProgressUpdate {
  documentId: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress: number;
  message?: string;
  error?: string;
  estimatedTimeRemaining?: number;
}

@WebSocketGateway({
  cors: {
    origin: [
      'http://localhost:3000',
      'http://localhost:3001',
      'https://docgic.com',
      'https://www.docgic.com',
      process.env.FRONTEND_URL,
    ].filter(Boolean),
    credentials: true,
  },
  namespace: '/document-processing',
})
export class DocumentProcessingGateway
  implements OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(DocumentProcessingGateway.name);
  private userSockets: Map<string, Set<Socket>> = new Map();
  private documentSubscriptions: Map<string, Set<string>> = new Map(); // documentId -> userIds

  constructor(private readonly tenantContext: TenantContext) {}

  async handleConnection(client: Socket) {
    try {
      // Extract user info from token (you'll need to implement token validation)
      const userId = await this.extractUserIdFromSocket(client);
      const organizationId = await this.extractOrganizationIdFromSocket(client);
      
      if (!userId || !organizationId) {
        client.disconnect();
        return;
      }

      // Store user socket mapping
      if (!this.userSockets.has(userId)) {
        this.userSockets.set(userId, new Set());
      }
      this.userSockets.get(userId)!.add(client);

      // Store user context in socket
      client.data.userId = userId;
      client.data.organizationId = organizationId;

      this.logger.log(`User ${userId} connected to document processing gateway`);
    } catch (error) {
      this.logger.error('Error handling connection:', error);
      client.disconnect();
    }
  }

  handleDisconnect(client: Socket) {
    const userId = client.data.userId;
    if (userId && this.userSockets.has(userId)) {
      this.userSockets.get(userId)!.delete(client);
      if (this.userSockets.get(userId)!.size === 0) {
        this.userSockets.delete(userId);
      }
    }

    // Remove from document subscriptions
    for (const [documentId, userIds] of this.documentSubscriptions.entries()) {
      userIds.delete(userId);
      if (userIds.size === 0) {
        this.documentSubscriptions.delete(documentId);
      }
    }

    this.logger.log(`User ${userId} disconnected from document processing gateway`);
  }

  @SubscribeMessage('subscribe-document')
  async handleSubscribeDocument(
    @MessageBody() data: { documentId: string },
    @ConnectedSocket() client: Socket,
  ) {
    const userId = client.data.userId;
    const organizationId = client.data.organizationId;
    const { documentId } = data;

    if (!userId || !organizationId || !documentId) {
      client.emit('error', { message: 'Invalid subscription data' });
      return;
    }

    // TODO: Verify user has access to this document
    // const hasAccess = await this.verifyDocumentAccess(documentId, userId, organizationId);
    // if (!hasAccess) {
    //   client.emit('error', { message: 'Access denied to document' });
    //   return;
    // }

    // Add to document subscriptions
    if (!this.documentSubscriptions.has(documentId)) {
      this.documentSubscriptions.set(documentId, new Set());
    }
    this.documentSubscriptions.get(documentId)!.add(userId);

    client.emit('subscribed', { documentId });
    this.logger.log(`User ${userId} subscribed to document ${documentId}`);
  }

  @SubscribeMessage('unsubscribe-document')
  async handleUnsubscribeDocument(
    @MessageBody() data: { documentId: string },
    @ConnectedSocket() client: Socket,
  ) {
    const userId = client.data.userId;
    const { documentId } = data;

    if (this.documentSubscriptions.has(documentId)) {
      this.documentSubscriptions.get(documentId)!.delete(userId);
      if (this.documentSubscriptions.get(documentId)!.size === 0) {
        this.documentSubscriptions.delete(documentId);
      }
    }

    client.emit('unsubscribed', { documentId });
    this.logger.log(`User ${userId} unsubscribed from document ${documentId}`);
  }

  /**
   * Send processing progress update to subscribed users
   */
  async sendProcessingUpdate(update: ProcessingProgressUpdate) {
    const { documentId } = update;
    const subscribedUsers = this.documentSubscriptions.get(documentId);

    if (!subscribedUsers || subscribedUsers.size === 0) {
      return;
    }

    for (const userId of subscribedUsers) {
      const userSockets = this.userSockets.get(userId);
      if (userSockets) {
        for (const socket of userSockets) {
          socket.emit('processing-update', update);
        }
      }
    }

    this.logger.debug(`Sent processing update for document ${documentId} to ${subscribedUsers.size} users`);
  }

  /**
   * Send processing completion notification
   */
  async sendProcessingComplete(documentId: string, result: any) {
    const update: ProcessingProgressUpdate = {
      documentId,
      status: 'completed',
      progress: 100,
      message: 'Document processing completed successfully',
    };

    await this.sendProcessingUpdate(update);
  }

  /**
   * Send processing error notification
   */
  async sendProcessingError(documentId: string, error: string) {
    const update: ProcessingProgressUpdate = {
      documentId,
      status: 'failed',
      progress: 0,
      error,
      message: 'Document processing failed',
    };

    await this.sendProcessingUpdate(update);
  }

  private async extractUserIdFromSocket(socket: Socket): Promise<string | null> {
    // TODO: Implement JWT token validation from socket handshake
    // This is a placeholder - you'll need to extract and validate the JWT token
    const token = socket.handshake.auth?.token || socket.handshake.headers?.authorization;
    if (!token) return null;

    try {
      // Validate token and extract user ID
      // const decoded = jwt.verify(token, process.env.JWT_SECRET);
      // return decoded.sub;
      return 'placeholder-user-id'; // Replace with actual implementation
    } catch (error) {
      return null;
    }
  }

  private async extractOrganizationIdFromSocket(socket: Socket): Promise<string | null> {
    // TODO: Extract organization ID from token or headers
    return 'placeholder-org-id'; // Replace with actual implementation
  }
}

export interface DocumentMetadata {
  sections?: {
    title: string;
    content: string;
    purpose?: string;
  }[];
  clauses?: {
    title: string;
    content: string;
    type?: string;
    riskLevel?: 'high' | 'medium' | 'low';
  }[];
  patterns?: any[];
  status?: string;
  lastUpdated?: Date;
  error?: string;
  [key: string]: any;
}

export interface ProcessingError {
  message: string;
  stack?: string;
  timestamp: Date;
}

export interface BaseDocument {
  id: string;
  organizationId: string;  // Organization/Tenant identifier
  filename: string;
  originalName: string;
  content?: string;
  size: number;
  uploadDate: Date;
  status: 'uploaded' | 'processing' | 'completed' | 'failed' | 'analyzed';
  metadata: DocumentMetadata;
  processingError?: ProcessingError;
}

export interface Document {
  id: string;
  organizationId: string;
  filename: string;
  originalName: string;
  content?: string;
  mimeType?: string;
  size: number;
  uploadDate: Date;
  status: string;
  metadata?: Record<string, any>;
  documentType?: string;
  parties?: string[];
  effectiveDate?: Date;
  expirationDate?: Date;
  processingError?: string;
  // Additional properties needed for document version comparison
  title?: string;
  createdAt?: Date;
  createdBy?: string;
  sections?: DocumentSection[];
}

export interface DocumentClause {
  title: string;
  content: string;
  type?: string;
  riskLevel?: 'high' | 'medium' | 'low';
}

export interface DocumentSection {
  title: string;
  content: string;
  purpose?: string;
}

export interface ProcessingResult {
  jobId: string;
  documentId: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress: number;
  error?: string;
}

export interface ProcessingOptions {
  analyzePatterns?: boolean;
  extractMetadata?: boolean;
  generateSummary?: boolean;
  priority?: boolean;
}

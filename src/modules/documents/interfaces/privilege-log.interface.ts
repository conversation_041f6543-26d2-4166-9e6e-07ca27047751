export interface PrivilegedContent {
  id: string;
  documentId: string;
  startPosition: number;
  endPosition: number;
  content: string;
  privilegeType: PrivilegeType;
  confidenceScore: number;
  detectionMethod: 'pattern' | 'ai' | 'manual';
  status: PrivilegeStatus;
  reviewedBy?: string;
  reviewedAt?: Date;
  redactionApplied: boolean;
  redactionReason?: string;
}

export interface PrivilegeLogEntry {
  id: string;
  documentId: string;
  documentTitle: string;
  privilegedContent: PrivilegedContent[];
  totalPrivilegedItems: number;
  totalRedactions: number;
  analysisDate: Date;
  lastReviewDate?: Date;
  status: PrivilegeLogStatus;
  reviewedBy?: string;
  organizationId: string;
  createdBy: string;
}

export interface RedactionSuggestion {
  contentId: string;
  originalText: string;
  suggestedRedaction: string;
  reason: string;
  privilegeType: PrivilegeType;
  confidenceScore: number;
  requiresReview: boolean;
}

export interface PrivilegeAnalysisResult {
  documentId: string;
  privilegedContent: PrivilegedContent[];
  redactionSuggestions: RedactionSuggestion[];
  summary: {
    totalItemsFound: number;
    highConfidenceItems: number;
    requiresManualReview: number;
    autoRedactable: number;
  };
  analysisMetadata: {
    analysisDate: Date;
    detectionMethods: string[];
    aiModelUsed?: string;
    processingTime: number;
  };
}

export enum PrivilegeType {
  ATTORNEY_CLIENT = 'attorney_client',
  WORK_PRODUCT = 'work_product',
  CONFIDENTIAL_COMMUNICATION = 'confidential_communication',
  TRADE_SECRET = 'trade_secret',
  MEDICAL_PRIVILEGE = 'medical_privilege',
  SPOUSAL_PRIVILEGE = 'spousal_privilege',
  OTHER = 'other'
}

export enum PrivilegeStatus {
  DETECTED = 'detected',
  UNDER_REVIEW = 'under_review',
  CONFIRMED = 'confirmed',
  REJECTED = 'rejected',
  REDACTED = 'redacted'
}

export enum PrivilegeLogStatus {
  PENDING_REVIEW = 'pending_review',
  IN_REVIEW = 'in_review',
  COMPLETED = 'completed',
  APPROVED = 'approved'
}

export interface PrivilegeAnalysisOptions {
  includeAIAnalysis?: boolean;
  confidenceThreshold?: number;
  privilegeTypes?: PrivilegeType[];
  autoRedact?: boolean;
  requireManualReview?: boolean;
}

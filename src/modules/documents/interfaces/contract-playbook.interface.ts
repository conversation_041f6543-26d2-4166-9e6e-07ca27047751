import { ContractType, RuleType, RuleSeverity, DeviationType, RecommendationType } from '../schemas/contract-playbook.schema';

// Core Interfaces
export interface RuleCriteria {
  keywords: string[];
  patterns: string[];
  semanticConcepts: string[];
  contextRequirements: string[];
}

export interface AcceptableLanguage {
  preferred: string[];
  acceptable: string[];
  fallbackPositions: string[];
}

export interface UnacceptableTerms {
  prohibited: string[];
  requiresEscalation: string[];
  autoReject: string[];
}

export interface NegotiationGuidance {
  strategy: string;
  alternatives: string[];
  businessImpact: string;
}

export interface PlaybookRule {
  id: string;
  name: string;
  category: string;
  ruleType: RuleType;
  severity: RuleSeverity;
  criteria: RuleCriteria;
  acceptableLanguage: AcceptableLanguage;
  unacceptableTerms: UnacceptableTerms;
  negotiationGuidance?: NegotiationGuidance;
  description?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ContractPlaybook {
  id: string;
  organizationId: string;
  name: string;
  contractType: ContractType;
  description?: string;
  version: string;
  rules: PlaybookRule[];
  metadata: {
    industry?: string;
    jurisdiction?: string;
    riskProfile?: string;
    author?: string;
    approvedBy?: string;
    lastReviewDate?: Date;
    nextReviewDate?: Date;
    tags?: string[];
    [key: string]: any;
  };
  isActive: boolean;
  isTemplate: boolean;
  createdBy: string;
  updatedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Risk Assessment Interfaces
export interface RiskFactors {
  deviationSeverity: {
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
  businessImpact: {
    financial: number;
    operational: number;
    reputational: number;
    compliance: number;
  };
  historicalRisk: {
    pastDisputes: number;
    negotiationDifficulty: number;
    enforcementChallenges: number;
  };
  industryFactors: {
    standardPractice: number;
    regulatoryEnvironment: number;
    marketConditions: number;
  };
}

export interface Recommendation {
  id: string;
  type: RecommendationType;
  priority: 'URGENT' | 'HIGH' | 'MEDIUM' | 'LOW';
  action: {
    description: string;
    specificSteps: string[];
    timeframe: string;
    assignee?: string;
  };
  rationale: {
    riskMitigation: string;
    businessJustification: string;
    legalBasis: string;
    precedent?: string;
  };
  implementation: {
    suggestedLanguage?: string;
    negotiationTalkingPoints: string[];
    alternatives: string[];
    fallbackPositions: string[];
  };
  impact: {
    riskReduction: number;
    implementationEffort: string;
    businessImpact: string;
    timeline: string;
  };
  createdAt: Date;
}

export interface Deviation {
  id: string;
  ruleId: string;
  ruleName: string;
  deviationType: DeviationType;
  severity: RuleSeverity;
  clauseText: string;
  suggestedText?: string;
  riskScore: number;
  context: {
    documentSection?: string;
    surroundingText?: string;
    pageNumber?: number;
    lineNumber?: number;
    startIndex?: number;
    endIndex?: number;
    [key: string]: any;
  };
  riskFactors?: RiskFactors;
  recommendations: Recommendation[];
  aiAnalysis?: string;
  confidence?: number;
  detectedAt: Date;
}

export interface ContractAnalysis {
  id: string;
  organizationId: string;
  contractId: string;
  playbookId: string;
  playbookName: string;
  overallScore: number;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  deviations: Deviation[];
  summary: {
    totalDeviations: number;
    criticalDeviations: number;
    highRiskDeviations: number;
    mediumRiskDeviations: number;
    lowRiskDeviations: number;
    compliancePercentage: number;
    keyFindings: string[];
    executiveSummary: string;
  };
  metrics: {
    processingTimeMs: number;
    aiAnalysisTime: number;
    rulesEvaluated: number;
    clausesAnalyzed: number;
    confidenceScore: number;
  };
  metadata: {
    contractType?: string;
    contractTitle?: string;
    analysisVersion?: string;
    aiProvider?: string;
    modelUsed?: string;
    [key: string]: any;
  };
  analyzedBy: string;
  analyzedAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Analysis Engine Interfaces
export interface AnalysisContext {
  document: {
    id: string;
    content: string;
    metadata?: any;
  };
  playbook: ContractPlaybook;
  options: {
    includeRecommendations: boolean;
    riskThreshold: number;
    aiAnalysis: boolean;
    detailedReport: boolean;
  };
}

export interface DeviationDetectionResult {
  deviations: Deviation[];
  overallScore: number;
  riskLevel: string;
  processingMetrics: {
    processingTimeMs: number;
    rulesEvaluated: number;
    clausesAnalyzed: number;
  };
}

export interface RiskAssessmentResult {
  riskScore: number;
  riskLevel: string;
  riskFactors: RiskFactors;
  confidence: number;
}

export interface RecommendationGenerationResult {
  recommendations: Recommendation[];
  prioritizedActions: Recommendation[];
  implementationPlan: {
    immediate: Recommendation[];
    shortTerm: Recommendation[];
    longTerm: Recommendation[];
  };
}

// Service Interfaces
export interface PlaybookAnalysisOptions {
  includeRecommendations?: boolean;
  riskThreshold?: number;
  aiAnalysis?: boolean;
  detailedReport?: boolean;
  maxDeviations?: number;
  confidenceThreshold?: number;
}

export interface PlaybookSearchOptions {
  query?: string;
  contractType?: ContractType;
  isActive?: boolean;
  isTemplate?: boolean;
  tags?: string[];
  page?: number;
  limit?: number;
}

export interface AnalysisSearchOptions {
  contractId?: string;
  playbookId?: string;
  riskLevel?: string;
  status?: string;
  startDate?: Date;
  endDate?: Date;
  page?: number;
  limit?: number;
}

// Response Interfaces
export interface PlaybookListResponse {
  playbooks: ContractPlaybook[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface AnalysisListResponse {
  analyses: ContractAnalysis[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface PlaybookAnalyticsResponse {
  usage: {
    totalAnalyses: number;
    averageScore: number;
    mostCommonDeviations: string[];
    riskDistribution: Record<string, number>;
  };
  performance: {
    averageProcessingTime: number;
    successRate: number;
    userSatisfaction: number;
  };
  trends: {
    monthlyAnalyses: Array<{ month: string; count: number }>;
    riskTrends: Array<{ date: string; averageRisk: number }>;
  };
}

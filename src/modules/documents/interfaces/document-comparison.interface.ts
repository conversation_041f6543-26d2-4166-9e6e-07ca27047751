export interface DiffSegment {
  text: string;
  type: 'equal' | 'delete' | 'insert';
  lineNumber?: number;
  charRange?: { start: number; end: number };
}

export interface TextDiff {
  original: string;
  modified: string;
  diffType: 'addition' | 'deletion' | 'modification';
  lineNumber: number;
}

export interface SectionReference {
  id: string;
  title: string;
  content: string;
  lineRange: { start: number; end: number };
}

export interface SectionDiff {
  sectionA: SectionReference;
  sectionB: SectionReference;
  differences: TextDiff[];
}

export interface DiffMetadata {
  timestamp: Date;
  documentStats: {
    documentA: {
      length: number;
      wordCount?: number;
      charCount?: number;
    };
    documentB: {
      length: number;
      wordCount?: number;
      charCount?: number;
    };
  };
  summary?: {
    addedLines: number;
    removedLines: number;
    modifiedLines: number;
    totalChanges: number;
    significantChanges?: Array<{
      type: 'addition' | 'deletion' | 'modification';
      description: string;
      lineRange: { start: number; end: number };
    }>;
  };
}

export interface ComparisonResult {
  diffs: DiffSegment[];
  metadata: DiffMetadata;
  visualization?: {
    htmlDiff?: string;  // HTML-formatted diff with highlighting
    colors?: {
      addition: string;
      deletion: string;
      modification: string;
    };
  };
}

export interface EnhancedComparisonResult extends ComparisonResult {
  sectionDiffs?: SectionDiff[];
  enhancedVisualization?: {
    sectionHighlights?: Array<{
      sectionId: string;
      changeType: 'addition' | 'deletion' | 'modification';
      intensity: number;  // 0-1 indicating degree of change
    }>;
    changeOverview?: {
      totalSections: number;
      modifiedSections: number;
      sectionChangeDistribution: { [sectionId: string]: number };
    };
  };
}

export interface VersionComparisonResult extends ComparisonResult {
  versionInfo: {
    beforeVersion: number;
    afterVersion: number;
    versionDelta: number;
    timestamp: Date;
  };
  changeHistory?: Array<{
    version: number;
    timestamp: Date;
    changes: Array<{
      type: 'addition' | 'deletion' | 'modification';
      description: string;
      section?: string;
    }>;
  }>;
}

export interface ExportOptions {
  format: 'pdf' | 'html' | 'docx';
  includeMetadata: boolean;
  highlightChanges: boolean;
  includeSummary: boolean;
  sections?: {
    include: string[];
    highlightIntensity?: boolean;
  };
  customization?: {
    colors?: {
      addition?: string;
      deletion?: string;
      modification?: string;
    };
    fonts?: {
      family?: string;
      size?: number;
    };
  };
}
export interface SimulationScenario {
  type: 'concession' | 'dealbreaker' | 'leverage' | 'compromise';
  trigger: string;
  responseStrategy: string;
  expectedOutcome?: string;
}

export interface NegotiationStrategy {
  section: string;
  recommendations: string[];
  simulationScenarios?: SimulationScenario[];
  riskLevel?: 'low' | 'medium' | 'high';
  priority?: number;
  alternativeLanguage?: string;
}

import { DocumentType } from '../../../common/enums/document-type.enum';

export interface NegotiationPlaybook {
  documentId: string;
  strategies: NegotiationStrategy[];
  overallAssessment: string;
  keyLeveragePoints: string[];
  dealBreakers?: string[];
  timestamp: Date;
  organizationId?: string;
  userId?: string;
  // Template/Sample fields
  isTemplate?: boolean;
  templateName?: string;
  templateDescription?: string;
  contractType?: DocumentType;
  industry?: string;
  difficulty?: string;
  tags?: string[];
  usageCount?: number;
}

export interface NegotiationPlaybookOptions {
  documentType: string;
  focusAreas?: string[];
  includeSimulations?: boolean;
  organizationPreferences?: string;
}

export type JobState = 'completed' | 'failed' | 'delayed' | 'active' | 'waiting' | 'paused' | 'stuck';

export interface JobStatus {
  id: string;
  status: JobState;
  progress: number;
  data: Record<string, any>;
  failedReason?: string;
  attempts: number;
}

export interface JobOptions {
  priority?: number;
  attempts?: number;
  delay?: number;
  timeout?: number;
  [key: string]: any;
}

export interface ProcessingOptions {
  priority?: boolean;
  maxRetries?: number;
  timeout?: number;
  analyzePatterns?: boolean;
  extractMetadata?: boolean;
  generateSummary?: boolean;
}

export interface ProcessingJobData {
  documentId: string;
  userId?: string;
  options?: ProcessingOptions;
}
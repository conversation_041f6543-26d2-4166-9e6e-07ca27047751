export enum DocumentProcessingJobType {
  PROCESS_DOCUMENT = 'process-document',
  ANALYZE_PATTERNS = 'analyze-patterns',
  EXTRACT_METADATA = 'extract-metadata',
  GENERATE_SUMMARY = 'generate-summary',
  CLEANUP_ANALYSIS_RESULTS = 'cleanup-analysis-results'
}

export interface ProcessDocumentJobData {
  documentId: string;
  userId?: string;
  options?: {
    analyzePatterns?: boolean;
    extractMetadata?: boolean;
    generateSummary?: boolean;
    priority?: boolean;
  };
}
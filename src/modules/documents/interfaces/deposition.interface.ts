/**
 * Interfaces for deposition preparation and question generation
 */

export interface DepositionQuestionMetadata {
  aiGenerated?: boolean;
  generationDate?: string;
  modelUsed?: string;
  [key: string]: any;
}

export interface DepositionQuestion {
  id: string;
  text: string;
  category: DepositionQuestionCategory;
  purpose: string;
  targetWitness?: string;
  suggestedFollowUps?: string[];
  relatedDocuments?: string[]; // Document IDs that relate to this question
  priority: 'high' | 'medium' | 'low';
  notes?: string;
  isFollowUp?: boolean; // Indicates if this is a follow-up question
  createdAt: Date;
  updatedAt: Date;
  metadata?: DepositionQuestionMetadata;
}

// Type for AI response that might contain questions
export interface AIQuestionResponse {
  questions?: Array<Partial<DepositionQuestion>>;
  text?: string;
  error?: string;
  [key: string]: any;
}

export enum DepositionQuestionCategory {
  GENERAL = 'general',
  CREDIBILITY = 'credibility',
  CONSISTENCY = 'consistency',
  DOCUMENTATION = 'documentation',
  EXPERT_QUALIFICATION = 'expert_qualification',
  IMPEACHMENT = 'impeachment'
}

export interface GenerateQuestionsDto {
  caseContext: string;
  keyIssues: string[];
  targetWitnesses: string[];
  options: {
    questionCount: number;
    includeFollowUps: boolean;
    questionCategories: DepositionQuestionCategory[];
  };
}

export interface QuestionGenerationResult {
  success: boolean;
  questions: DepositionQuestion[];
  generatedAt: Date;
  processingTime: number;
  analysisDuration?: number;
  error?: string;
  fallbackQuestions?: DepositionQuestion[];
}

export interface DepositionPreparation {
  id: string;
  caseId: string;
  title: string;
  description?: string;
  targetWitnesses: string[];
  caseContext: string;
  keyIssues: string[];
  questions: DepositionQuestion[];
  relatedDocumentIds: string[];
  status: DepositionPreparationStatus;
  metadata?: DepositionPreparationMetadata;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  organizationId: string;
}

export enum DepositionPreparationStatus {
  DRAFT = 'draft',
  IN_PROGRESS = 'in_progress',
  READY = 'ready',
  COMPLETED = 'completed'
}

export interface QuestionGenerationOptions {
  focusAreas?: string[];
  questionCount?: number;
  includeFollowUps?: boolean;
  questionCategories?: DepositionQuestionCategory[];
  priorityLevel?: 'high' | 'medium' | 'low' | 'all';
  useDocumentContext?: boolean;
}

export interface DepositionTestimonyAnalysis {
  speaker: string;
  statement: string;
  credibilityScore: number;
  confidence: 'high' | 'medium' | 'low';
  reasoning: string;
  supportingEvidence: string[];
  contradictions?: Array<{
    previousStatement: string;
    currentStatement: string;
    reasoning: string;
  }>;
}

export interface DepositionInconsistency {
  statement1: string;
  statement2: string;
  explanation: string;
  severity: 'high' | 'medium' | 'low';
  source1?: string;
  source2?: string;
  speaker?: string;
}

export interface CrossExaminationSuggestion {
  topic: string;
  question: string;
  purpose: string;
  legalBasis?: string;
  suggestedFollowUps: string[];
}

export interface AnalyzeDepositionDto {
  depositionId: string;
  transcript: string;
  caseContext: string;
  focusAreas: string[];
  depositionPreparationId?: string;
}

export interface DepositionAnalysisResult {
  id: string;
  depositionId: string;
  transcript?: string;  // Added transcript as an optional property
  caseContext?: string; // Added caseContext as it's also used in the code
  focusAreas?: string[]; // Added focusAreas as it's also used in the code
  overallCredibilityScore: number;
  keyTestimonyAnalysis: DepositionTestimonyAnalysis[];
  crossExaminationSuggestions: CrossExaminationSuggestion[];
  inconsistencies: DepositionInconsistency[];
  keyFindings: string[];
  potentialImpeachmentOpportunities: any[];
  timelineAnalysis: any[];
  metadata: any;
}

export interface CreateDepositionPreparationDto {
  caseId?: string;
  title: string;
  description: string;
  targetWitnesses: string[];
  caseContext: string;
  keyIssues: string[];
  relatedDocumentIds?: string[];
}

export interface DepositionPreparationMetadata {
  transcriptAnalysis?: {
    analysisId: string;
    analyzedAt: string;
    analysisType: string;
    credibilityScore: number;
    questionCount: number;
  };
  [key: string]: any;
}

export interface UpdateDepositionPreparationDto {
  title?: string;
  description?: string;
  targetWitnesses?: string[];
  caseContext?: string;
  keyIssues?: string[];
  relatedDocumentIds?: string[];
  status?: DepositionPreparationStatus;
  metadata?: DepositionPreparationMetadata;
}

export interface CreateDepositionQuestionDto {
  text: string;
  category: DepositionQuestionCategory;
  purpose: string;
  targetWitness: string;
  suggestedFollowUps?: string[];
  relatedDocuments?: string[];
  priority?: 'high' | 'medium' | 'low';
  notes?: string;
  isFollowUp?: boolean;
  metadata?: DepositionQuestionMetadata;
}

export interface UpdateDepositionQuestionDto {
  text?: string;
  category?: DepositionQuestionCategory;
  purpose?: string;
  targetWitness?: string;
  suggestedFollowUps?: string[];
  relatedDocuments?: string[];
  priority?: 'high' | 'medium' | 'low';
  notes?: string;
  isFollowUp?: boolean;
  metadata?: DepositionQuestionMetadata;
}

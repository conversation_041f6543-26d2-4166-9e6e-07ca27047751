import { Document, ProcessingResult } from './document.interface';
import { JobStatus } from './job-status.interface';
import { DocumentVersion } from '../schemas/document-version.schema';

export interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
}

export interface PaginatedDocumentsResult {
  documents: Document[];
  total: number;
  totalPages: number;
  page: number;
  limit: number;
}

export interface DocumentProcessingServiceInterface {
  getDocumentById(id: string): Promise<Document>;
  getAllDocuments(options?: PaginationOptions): Promise<PaginatedDocumentsResult>;
  getDocumentContent(id: string): Promise<string>;
  processDocument(documentId: string, userId?: string, options?: any): Promise<ProcessingResult>;
  processUploadedFile(file: Express.Multer.File, metadata?: Record<string, any>): Promise<Document>;
  updateDocumentStatus(documentId: string, status: Document['status'], error?: string): Promise<void>;
  updateMetadata(documentId: string, metadata: Record<string, any>): Promise<void>;
  documentExists(id: string): Promise<boolean>;
  getJobStatus(jobId: string): Promise<JobStatus | null>;
  analyzePatterns(documentId: string): Promise<ProcessingResult>;
  extractMetadata(documentId: string): Promise<void>;
  generateSummary(documentId: string): Promise<void>;
  getDocumentVersions(documentId: string): Promise<DocumentVersion[]>;
}
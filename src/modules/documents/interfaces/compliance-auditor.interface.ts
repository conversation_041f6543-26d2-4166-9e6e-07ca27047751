export interface ComplianceAuditResult {
  id?: string;
  documentId: string;
  documentTitle: string;
  auditDate: Date;
  regulations: string[];
  overallScore: number; // 0-1
  status: 'compliant' | 'non-compliant' | 'partial' | 'needs-review';
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  findings: ComplianceFinding[];
  recommendations: ComplianceRecommendation[];
  summary: ComplianceSummary;
  organizationId: string;
  auditedBy: string;
  processingTime: number; // in milliseconds
}

export interface ComplianceFinding {
  id: string;
  regulation: string;
  ruleId: string;
  ruleName: string;
  description: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  status: 'pass' | 'fail' | 'warning' | 'not-applicable';
  confidence: number; // 0-1
  location: {
    page?: number;
    section?: string;
    startPosition?: number;
    endPosition?: number;
    context?: string;
  };
  evidence: string[];
  impact: string;
  remediation: string;
  category: string;
  tags: string[];
}

export interface ComplianceRecommendation {
  id: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  category: string;
  title: string;
  description: string;
  action: string;
  effort: 'low' | 'medium' | 'high';
  timeline: string;
  resources: string[];
  relatedFindings: string[]; // finding IDs
  costEstimate?: {
    min: number;
    max: number;
    currency: string;
  };
}

export interface ComplianceSummary {
  totalRules: number;
  passedRules: number;
  failedRules: number;
  warningRules: number;
  notApplicableRules: number;
  criticalIssues: number;
  highPriorityIssues: number;
  mediumPriorityIssues: number;
  lowPriorityIssues: number;
  estimatedRemediationTime: string;
  complianceGaps: string[];
  strengths: string[];
}

export interface RegulatoryFramework {
  id: string;
  name: string;
  version: string;
  description: string;
  jurisdiction: string;
  industry: string[];
  applicableDocumentTypes: string[];
  rules: ComplianceRule[];
  lastUpdated: Date;
  isActive: boolean;
  source: string;
  officialUrl?: string;
}

export interface ComplianceRule {
  id: string;
  frameworkId: string;
  ruleNumber: string;
  title: string;
  description: string;
  category: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  applicability: RuleApplicability;
  patterns: RulePattern[];
  aiPrompt?: string;
  manualReview: boolean;
  exemptions: string[];
  references: string[];
  lastUpdated: Date;
}

export interface RuleApplicability {
  documentTypes: string[];
  industries: string[];
  jurisdictions: string[];
  organizationSizes: string[];
  conditions: string[];
}

export interface RulePattern {
  type: 'regex' | 'keyword' | 'semantic' | 'ai-analysis';
  pattern: string;
  weight: number; // 0-1
  context?: string;
  negativePatterns?: string[];
  requiredContext?: string[];
}

export interface ComplianceProfile {
  id?: string;
  organizationId: string;
  name: string;
  description: string;
  regulations: string[]; // framework IDs
  customRules: ComplianceRule[];
  riskTolerance: 'low' | 'medium' | 'high';
  industry: string;
  jurisdiction: string;
  organizationSize: 'small' | 'medium' | 'large' | 'enterprise';
  specialRequirements: string[];
  exemptions: string[];
  isDefault: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ComplianceAuditOptions {
  regulations?: string[]; // specific frameworks to check
  profileId?: string; // use predefined compliance profile
  includeAIAnalysis: boolean;
  detailLevel: 'basic' | 'standard' | 'comprehensive';
  includeRecommendations: boolean;
  generateReport: boolean;
  riskAssessment: boolean;
  benchmarking: boolean;
  customRules?: ComplianceRule[];
}

export interface ComplianceReport {
  id: string;
  auditResultId: string;
  title: string;
  generatedAt: Date;
  format: 'pdf' | 'html' | 'json' | 'excel';
  sections: ReportSection[];
  executiveSummary: string;
  methodology: string;
  limitations: string[];
  nextSteps: string[];
  appendices: ReportAppendix[];
}

export interface ReportSection {
  id: string;
  title: string;
  content: string;
  charts?: ChartData[];
  tables?: TableData[];
  order: number;
}

export interface ChartData {
  type: 'bar' | 'pie' | 'line' | 'scatter';
  title: string;
  data: any[];
  labels: string[];
  colors?: string[];
}

export interface TableData {
  title: string;
  headers: string[];
  rows: any[][];
  sortable: boolean;
  filterable: boolean;
}

export interface ReportAppendix {
  id: string;
  title: string;
  content: string;
  type: 'text' | 'table' | 'chart' | 'reference';
}

export interface ComplianceBenchmark {
  industry: string;
  organizationSize: string;
  averageScore: number;
  commonIssues: string[];
  bestPractices: string[];
  improvementAreas: string[];
  timeToCompliance: number; // in days
}

export interface ComplianceAnalytics {
  organizationId: string;
  period: {
    start: Date;
    end: Date;
  };
  totalAudits: number;
  averageScore: number;
  trendData: {
    date: Date;
    score: number;
    criticalIssues: number;
  }[];
  topIssues: {
    issue: string;
    frequency: number;
    averageSeverity: string;
  }[];
  improvementRate: number;
  complianceByRegulation: {
    regulation: string;
    score: number;
    trend: 'improving' | 'stable' | 'declining';
  }[];
  riskDistribution: {
    low: number;
    medium: number;
    high: number;
    critical: number;
  };
}

export interface CreateComplianceProfileDto {
  name: string;
  description: string;
  regulations: string[];
  industry: string;
  jurisdiction: string;
  organizationSize: 'small' | 'medium' | 'large' | 'enterprise';
  riskTolerance: 'low' | 'medium' | 'high';
  specialRequirements?: string[];
  customRules?: Omit<ComplianceRule, 'id' | 'frameworkId'>[];
}

export interface AuditDocumentDto {
  documentId: string;
  options: ComplianceAuditOptions;
  profileId?: string;
}

export interface ComplianceQueryDto {
  regulations?: string[];
  status?: string;
  riskLevel?: string;
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

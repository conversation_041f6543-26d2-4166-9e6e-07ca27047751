export interface DepositionTestimonyAnalysis {
  speaker: string;
  statement: string;
  credibilityScore: number;
  confidence: 'high' | 'medium' | 'low';
  reasoning: string;
  supportingEvidence: string[];
  contradictions?: { 
    previousStatement: string;
    currentStatement: string;
    reasoning: string;
  }[];
}

export interface CrossExaminationSuggestion {
  topic: string;
  question: string;
  purpose: string;
  legalBasis?: string;
  suggestedFollowUps?: string[];
}

export interface DepositionInconsistency {
  statement1: string;
  statement2: string;
  explanation: string;
  severity: 'high' | 'medium' | 'low';
}

export interface DepositionAnalysisResult {
  id?: string; 
  depositionId?: string; 
  overallCredibilityScore: number;
  keyTestimonyAnalysis: DepositionTestimonyAnalysis[];
  crossExaminationSuggestions: CrossExaminationSuggestion[];
  inconsistencies: DepositionInconsistency[];
  keyFindings: string[];
  potentialImpeachmentOpportunities?: { 
    statement: string;
    conflictingEvidence: string;
    suggestedApproach: string;
  }[];
  timelineAnalysis?: { 
    event: string;
    timestamp: string;
    relevance: number;
    notes: string;
  }[];
  metadata?: { 
    analyzedAt: string; 
    analysisDurationMs?: number; 
    modelUsed: string;
    confidence: 'high' | 'medium' | 'low';
  };
}

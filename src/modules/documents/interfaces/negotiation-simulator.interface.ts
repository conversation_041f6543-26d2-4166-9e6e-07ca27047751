export interface NegotiationScenario {
  id?: string;
  name: string;
  description: string;
  parties: PartyProfile[];
  initialOffer: Terms;
  constraints: NegotiationConstraints;
  timeline: TimelineOptions;
  difficulty: 'beginner' | 'intermediate' | 'expert';
  industry: string;
  contractType: string;
  status: 'active' | 'completed' | 'paused';
  createdAt: Date;
  updatedAt: Date;
}

export interface PartyProfile {
  id: string;
  name: string;
  role:
    | 'buyer'
    | 'seller'
    | 'vendor'
    | 'client'
    | 'contractor'
    | 'licensor'
    | 'licensee';
  priorities: string[];
  negotiationStyle:
    | 'aggressive'
    | 'collaborative'
    | 'analytical'
    | 'competitive'
    | 'accommodating';
  constraints: Record<string, any>;
  budget?: {
    min: number;
    max: number;
    currency: string;
  };
  timeline?: {
    urgency: 'low' | 'medium' | 'high';
    deadline?: Date;
  };
}

export interface Terms {
  price?: number;
  currency?: string;
  paymentTerms?: string;
  deliveryDate?: Date;
  warranties?: string[];
  liabilities?: string[];
  terminationClauses?: string[];
  intellectualProperty?: string[];
  confidentiality?: string[];
  customTerms?: Record<string, any>;
}

export interface NegotiationConstraints {
  maxRounds: number;
  timeLimit?: number; // in minutes
  mustHaveTerms: string[];
  dealBreakers: string[];
  flexibleTerms: string[];
}

export interface TimelineOptions {
  startDate: Date;
  expectedDuration: number; // in minutes
  maxDuration: number; // in minutes
  breakDuration?: number; // in minutes between rounds
}

export interface NegotiationRound {
  id: string;
  roundNumber: number;
  timestamp: Date;
  party: string; // party ID
  offer: Terms;
  message: string;
  reasoning?: string;
  confidence: number; // 0-1
  strategy: string;
  responseTime: number; // in seconds
}

export interface NegotiationSession {
  id?: string;
  scenarioId: string;
  userId: string;
  organizationId: string;
  status: 'active' | 'completed' | 'paused' | 'abandoned';
  startTime: Date;
  endTime?: Date;
  rounds: NegotiationRound[];
  currentRound: number;
  finalAgreement?: Terms;
  metrics: NegotiationMetrics;
  aiPersonality: AIPersonalityProfile;
}

export interface AIPersonalityProfile {
  aggressiveness: number; // 0-1
  flexibility: number; // 0-1
  riskTolerance: number; // 0-1
  communicationStyle: 'formal' | 'casual' | 'technical' | 'diplomatic';
  decisionSpeed: 'fast' | 'moderate' | 'deliberate';
  concessionPattern: 'early' | 'gradual' | 'late' | 'minimal';
}

export interface NegotiationMetrics {
  totalRounds: number;
  totalTime: number; // in minutes
  agreementReached: boolean;
  userSatisfaction?: number; // 0-1, user-provided
  dealValue: number;
  concessionsMade: {
    byUser: number;
    byAI: number;
  };
  strategicEffectiveness: number; // 0-1
  communicationQuality: number; // 0-1
  timeEfficiency: number; // 0-1
  overallScore: number; // 0-1
}

export interface SimulationEvaluation {
  sessionId: string;
  metrics: NegotiationMetrics;
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
  learningPoints: string[];
  nextSteps: string[];
  improvementAreas: {
    area: string;
    currentScore: number;
    targetScore: number;
    suggestions: string[];
  }[];
}

export interface NegotiationTemplate {
  id: string;
  name: string;
  description: string;
  industry: string;
  contractType: string;
  difficulty: 'beginner' | 'intermediate' | 'expert';
  estimatedDuration: number; // in minutes
  learningObjectives: string[];
  scenario: Partial<NegotiationScenario>;
  isPublic: boolean;
  createdBy: string;
  organizationId: string;
  usageCount: number;
  averageRating: number;
  tags: string[];
}

export interface AIResponse {
  offer: Terms;
  message: string;
  reasoning: string;
  confidence: number;
  strategy: string;
  nextMoveHint?: string;
  marketContext?: string;
  riskAssessment?: string;
}

export interface UserMove {
  offer: Terms;
  message: string;
  strategy?: string;
  reasoning?: string;
}

export interface NegotiationAnalytics {
  totalSessions: number;
  completionRate: number;
  averageSessionDuration: number;
  averageRounds: number;
  successRate: number;
  popularScenarios: {
    scenarioId: string;
    name: string;
    usageCount: number;
  }[];
  userProgress: {
    userId: string;
    sessionsCompleted: number;
    averageScore: number;
    improvementTrend: number;
    strongAreas: string[];
    weakAreas: string[];
  };
  industryTrends: {
    industry: string;
    averageSessionDuration: number;
    successRate: number;
    commonStickingPoints: string[];
  }[];
}

export interface CreateNegotiationScenarioDto {
  name: string;
  description: string;
  industry: string;
  contractType: string;
  difficulty: 'beginner' | 'intermediate' | 'expert';
  parties: Omit<PartyProfile, 'id'>[];
  initialOffer: Terms;
  constraints: NegotiationConstraints;
  timeline: TimelineOptions;
  isTemplate?: boolean;
  tags?: string[];
}

export interface StartNegotiationSessionDto {
  scenarioId: string;
  aiPersonality?: Partial<AIPersonalityProfile>;
  customConstraints?: Partial<NegotiationConstraints>;
}

export interface MakeNegotiationMoveDto {
  sessionId: string;
  move: UserMove;
}

export interface NegotiationSimulatorOptions {
  enableHints: boolean;
  showAIReasoning: boolean;
  allowPausing: boolean;
  recordSession: boolean;
  difficultyAdjustment: boolean;
  realTimeAnalysis: boolean;
}

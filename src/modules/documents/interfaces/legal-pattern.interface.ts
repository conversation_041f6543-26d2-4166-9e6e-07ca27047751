/**
 * Represents a detected legal pattern, standard or AI-generated.
 */
export interface LegalPattern {
  // Fields expected from the AI based on the latest prompt
  pattern_name?: string; // Concise name reflecting the user's query
  text_snippet?: string; // The exact text segment identified
  explanation?: string; // Why this snippet matches the query
  start_char?: number; // Starting character index in the full text
  end_char?: number; // Ending character index in the full text

  // Optional original fields (might be populated by standard detection or older AI versions)
  type?: string; // Type of pattern (e.g., 'definition', 'clause', 'obligation') - Less relevant with pattern_name
  content?: string; // The actual text content (potentially redundant with text_snippet)
  startIndex?: number; // Starting index (potentially redundant with start_char)
  endIndex?: number; // Ending index (potentially redundant with end_char)
  metadata?: Record<string, any>; // Additional metadata (e.g., section)
  documentId?: string; // Context: ID of the document it was found in
  chunkIndex?: number; // Context: Chunk index if chunking was used (relevant for debugging old data)
}

export interface PatternRecognitionResult {
  patterns: LegalPattern[];
  documentId: string;
  timestamp: Date;
}
import { Document, DocumentMetadata } from '../../../common/interfaces/document.interface';

export interface DocumentMetadataWithContent extends DocumentMetadata {
  content?: string;
  processingStatus?: string;
  processingError?: string;
}

/**
 * Interface defining document processing operations.
 * Current implementation uses simulated processing, but this interface
 * allows for easy replacement with real processing logic in the future.
 */
export interface IDocumentProcessor {
  /**
   * Process a document with the given ID
   * @param documentId The ID of the document to process
   */
  processDocument(documentId: string): Promise<void>;

  /**
   * Extract metadata from a document
   * @param documentId The ID of the document
   */
  extractMetadata(documentId: string): Promise<void>;

  /**
   * Generate a summary for a document
   * @param documentId The ID of the document
   */
  generateSummary(documentId: string): Promise<void>;

  /**
   * Update document metadata
   * @param documentId The ID of the document
   * @param metadata The metadata to update
   */
  updateMetadata(documentId: string, metadata: Partial<DocumentMetadataWithContent>): Promise<void>;

  /**
   * Save document
   * @param document The document to save
   */
  saveDocument(document: Document): Promise<void>;

  /**
   * Get document metadata
   * @param documentId The ID of the document
   */
  getMetadata(documentId: string): Promise<DocumentMetadataWithContent | null>;

  /**
   * Update document processing status
   * @param documentId The ID of the document
   * @param status The new status
   * @param error Optional error message
   */
  updateDocumentStatus(documentId: string, status: string, error?: string): Promise<void>;

  /**
   * Find document by ID
   * @param documentId The ID of the document
   */
  findById(documentId: string): Promise<Document | null>;
}
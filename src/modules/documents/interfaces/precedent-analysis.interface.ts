// Defines the interfaces for precedent analysis

export interface KeyPoint {
  text: string;
  type?: 'finding' | 'holding' | 'reasoning' | 'dicta' | 'fact'; // Optional: type of key point
}

export interface RelatedCase {
  caseName: string;
  citation?: string;
  court?: string;
  year?: number;
  relevance: number; // 0-1 score
  relationship: 'supports' | 'contradicts' | 'distinguishes' | 'cites' | 'unknown';
  // Optional: Add a brief summary or key snippet from the related case if available
  summary?: string;
}

export interface TimelineEntry {
  date: string;
  event: string;
  significance?: 'major' | 'minor';
}

export interface PrecedentAnalysisResult {
  citation: string;
  relevanceScore: number; // 0-1 score indicating relevance to the document
  impact: 'positive' | 'negative' | 'neutral' | 'unknown';
  category: string; // e.g., 'Constitutional Law', 'Contract Law', etc.
  keyPoints: KeyPoint[]; // Changed from string[] to KeyPoint[]
  recommendation?: string;
  relatedCases: RelatedCase[];
  // Optional: Add timeline data if available from CourtListener and processed
  timeline?: TimelineEntry[];
  // Optional: Add AI analysis details if AI was used
  aiAnalysisDetails?: {
    reasoning?: string;
    source: 'AI' | 'RuleBasedHybrid'; // To indicate how these results were primarily generated
  };
}

export interface PrecedentAnalysisOptions {
  includeRecommendations?: boolean;
  maxRelatedCases?: number;
  minRelevanceScore?: number; // Applied if not using AI primarily, or as a secondary filter
  categorize?: boolean; // Whether to perform rule-based categorization if AI not used/fails
  assessImpact?: boolean; // Whether to perform rule-based impact assessment if AI not used/fails
  useAIAnalysis?: boolean; // Master switch to use AI for relevance, impact, category
  // Optional: Specify particular aspects for AI to focus on or ignore
  aiFocus?: {
    prioritizeImpact?: boolean;
    detailLevel?: 'concise' | 'standard' | 'detailed';
  };
}

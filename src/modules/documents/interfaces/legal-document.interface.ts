export interface LegalDocument {
  id: string;
  filename: string;
  originalName: string;
  mimeType?: string;
  size?: number;
  path?: string;
  hash?: string;
  uploadDate?: Date;
  content?: string;
  metadata?: {
    sections?: Array<{
      title: string;
      content: string;
      purpose?: string;
    }>;
    clauses?: Array<{
      title: string;
      content: string;
      type?: string;
      riskLevel?: string;
    }>;
    status?: string;
    patterns?: any[];
    lastUpdated?: Date;
    error?: string;
    [key: string]: any;
  };
}
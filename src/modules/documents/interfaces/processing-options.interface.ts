export type ProgressCallback = (current: number, total: number) => void;

export interface ProcessingOptions {
  /**
   * Size of each chunk in bytes for processing large files
   */
  chunkSize?: number;

  /**
   * Maximum number of worker threads to use
   */
  maxWorkers?: number;

  /**
   * Timeout in milliseconds for processing operations
   */
  timeoutMs?: number;

  /**
   * Callback for progress updates during processing
   */
  onProgress?: ProgressCallback;

  /**
   * Priority flag for processing (lower number = higher priority)
   */
  priority?: boolean;
}

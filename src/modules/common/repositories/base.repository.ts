import { Document, Model, FilterQuery, UpdateQuery } from 'mongoose';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { UnauthorizedException } from '@nestjs/common';

export interface BaseRepositoryInterface<T extends Document> {
  create(data: Partial<T>): Promise<T>;
  findById(id: string): Promise<T | null>;
  findOne(filter: FilterQuery<T>): Promise<T | null>;
  find(filter: FilterQuery<T>): Promise<T[]>;
  update(id: string, data: UpdateQuery<T>): Promise<T | null>;
  delete(id: string): Promise<boolean>;
}

export abstract class TenantAwareRepository<T extends Document> implements BaseRepositoryInterface<T> {
  constructor(
    protected readonly model: Model<T>,
    protected readonly tenantContext: TenantContextService,
  ) {}

  protected ensureOrganizationId(): string {
    const organizationId = this.tenantContext.getCurrentOrganization();
    if (!organizationId) {
      throw new UnauthorizedException('Tenant context required');
    }
    return organizationId;
  }

  protected addTenantContext<Q extends FilterQuery<T>>(filter: Q): FilterQuery<T> {
    const organizationId = this.ensureOrganizationId();
    return { ...filter, organizationId };
  }

  async create(data: Partial<T>): Promise<T> {
    const organizationId = this.ensureOrganizationId();
    return this.model.create({ ...data, organizationId });
  }

  async findById(id: string): Promise<T | null> {
    const filter = this.addTenantContext({ id });
    return this.model.findOne(filter).exec();
  }

  async findOne(filter: FilterQuery<T>): Promise<T | null> {
    const tenantFilter = this.addTenantContext(filter);
    return this.model.findOne(tenantFilter).exec();
  }

  async find(filter: FilterQuery<T> = {}): Promise<T[]> {
    const tenantFilter = this.addTenantContext(filter);
    return this.model.find(tenantFilter).exec();
  }

  async update(id: string, data: UpdateQuery<T>): Promise<T | null> {
    const filter = this.addTenantContext({ id });
    return this.model
      .findOneAndUpdate(filter, data, { new: true })
      .exec();
  }

  async delete(id: string): Promise<boolean> {
    const filter = this.addTenantContext({ id });
    const result = await this.model.deleteOne(filter).exec();
    return result.deletedCount > 0;
  }

  async findWithPagination(
    filter: FilterQuery<T> = {},
    page = 1,
    limit = 10,
    sort: Record<string, 1 | -1> = { createdAt: -1 }
  ): Promise<{ items: T[]; total: number; pages: number }> {
    const tenantFilter = this.addTenantContext(filter);
    const skip = (page - 1) * limit;

    const [items, total] = await Promise.all([
      this.model
        .find(tenantFilter)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.model.countDocuments(tenantFilter).exec(),
    ]);

    return {
      items,
      total,
      pages: Math.ceil(total / limit),
    };
  }
}

import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ChatModule } from '../chat/chat.module';
import { DocumentsModule } from '../documents/documents.module';
import { MigrationController } from './controllers/migration.controller';
import { DocumentMigrationService } from './services/document-migration.service';
import { 
  DocumentSchema, 
  DOCUMENT_MODEL 
} from '../documents/schemas/document.schema';

@Module({
  imports: [
    // Import required modules
    ChatModule,
    DocumentsModule,
    
    // Register MongoDB schemas needed for migration
    MongooseModule.forFeature([
      { name: DOCUMENT_MODEL, schema: DocumentSchema }
    ])
  ],
  controllers: [MigrationController],
  providers: [DocumentMigrationService],
  exports: [DocumentMigrationService]
})
export class MigrationModule {}

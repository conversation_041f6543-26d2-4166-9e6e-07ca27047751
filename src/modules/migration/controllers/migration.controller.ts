import { <PERSON>, <PERSON>, Post, Logger, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ChatService } from '../../chat/services/chat.service';
import { ChatAttachmentService } from '../../chat/services/chat-attachment.service';
import { DocumentMigrationService } from '../services/document-migration.service';

@ApiTags('Migration')
@Controller('api/migration')
export class MigrationController {
  private readonly logger = new Logger(MigrationController.name);

  constructor(
    private readonly chatService: ChatService,
    private readonly chatAttachmentService: ChatAttachmentService,
    private readonly documentMigrationService: DocumentMigrationService
  ) {}

  @Post('chat-sessions')
  @ApiOperation({ summary: 'Migrate chat sessions to MongoDB' })
  @ApiResponse({ 
    status: 200, 
    description: 'Migration started successfully',
    schema: {
      properties: {
        message: { type: 'string' },
        migratedCount: { type: 'number' }
      }
    }
  })
  async migrateChatSessions() {
    this.logger.log('Starting migration of chat sessions to MongoDB');
    const sessions = await this.chatService.getAllSessions();
    
    let migratedCount = 0;
    for (const session of sessions) {
      try {
        // Save session (our implementation already handles duplicates)
        await this.chatService.updateSession(session);
        migratedCount++;
      } catch (error) {
        this.logger.error(`Error migrating session ${session.id}: ${error.message}`);
      }
    }
    
    return { 
      message: `Migrated ${migratedCount} of ${sessions.length} chat sessions to MongoDB`,
      migratedCount
    };
  }

  @Post('chat-attachments')
  @ApiOperation({ summary: 'Migrate chat attachments to MongoDB' })
  @ApiResponse({ 
    status: 200, 
    description: 'Migration started successfully',
    schema: {
      properties: {
        message: { type: 'string' },
        migratedCount: { type: 'number' }
      }
    }
  })
  async migrateChatAttachments() {
    this.logger.log('Starting migration of chat attachments to MongoDB');
    // const migratedCount = await this.chatAttachmentService.migrateExistingAttachments();
    const migratedCount = 0; // Migration functionality removed
    
    return { 
      message: `Migrated ${migratedCount} chat attachments to MongoDB`,
      migratedCount
    };
  }

  @Post('documents')
  @ApiOperation({ summary: 'Migrate documents to MongoDB' })
  @ApiResponse({ 
    status: 200, 
    description: 'Migration started successfully',
    schema: {
      properties: {
        message: { type: 'string' },
        migratedCount: { type: 'number' }
      }
    }
  })
  async migrateDocuments() {
    this.logger.log('Starting migration of documents to MongoDB');
    const result = await this.documentMigrationService.migrateDocuments();
    
    return { 
      message: `Migrated ${result.success} documents successfully to MongoDB (${result.failed} failed)`,
      migratedCount: result.success,
      failedCount: result.failed
    };
  }

  @Post('migrate-all')
  @ApiOperation({ summary: 'Migrate all data to MongoDB' })
  @ApiResponse({ 
    status: 200, 
    description: 'Migration started successfully' 
  })
  async migrateAll() {
    this.logger.log('Starting full data migration to MongoDB');
    
    // Start with documents
    const docResult = await this.documentMigrationService.migrateDocuments();
    
    // Then migrate chat sessions
    const sessions = await this.chatService.getAllSessions();
    let sessionsMigrated = 0;
    for (const session of sessions) {
      try {
        await this.chatService.updateSession(session);
        sessionsMigrated++;
      } catch (error) {
        this.logger.error(`Error migrating session ${session.id}: ${error.message}`);
      }
    }
    
    // Finally migrate attachments
    // const attachmentsMigrated = await this.chatAttachmentService.migrateExistingAttachments();
    const attachmentsMigrated = 0; // Migration functionality removed
    
    return {
      message: 'Full data migration completed',
      results: {
        documents: {
          success: docResult.success,
          failed: docResult.failed
        },
        chatSessions: {
          success: sessionsMigrated,
          total: sessions.length
        },
        attachments: {
          success: attachmentsMigrated
        }
      }
    };
  }

  @Get('status')
  @ApiOperation({ summary: 'Get migration status' })
  @ApiResponse({ status: 200, description: 'Migration status retrieved successfully' })
  async getMigrationStatus() {
    // This is a placeholder. In a real implementation, you'd track migration
    // jobs and their status in a database
    return {
      lastMigration: new Date(),
      status: 'completed',
    };
  }
}

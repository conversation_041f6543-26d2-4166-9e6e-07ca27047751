import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs/promises';
import * as path from 'path';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Document, DOCUMENT_MODEL } from '../../documents/schemas/document.schema';
import { DocumentStorageService } from '../../documents/services/document-storage.service';

interface MigrationResult {
  success: number;
  failed: number;
  details?: string[];
}

@Injectable()
export class DocumentMigrationService {
  private readonly logger = new Logger(DocumentMigrationService.name);
  private readonly documentsDir: string;

  constructor(
    private configService: ConfigService,
    private documentStorageService: DocumentStorageService,
    @InjectModel(DOCUMENT_MODEL) private documentModel: Model<Document>
  ) {
    this.documentsDir = path.join(
      this.configService.get<string>('storage.uploadDir') || 'uploads',
      'documents'
    );
  }

  /**
   * Migrate documents from filesystem to MongoDB
   */
  async migrateDocuments(): Promise<MigrationResult> {
    try {
      // Ensure documents directory exists
      await fs.mkdir(this.documentsDir, { recursive: true });

      // Get all files in the documents directory
      const files = await fs.readdir(this.documentsDir);
      
      const result: MigrationResult = {
        success: 0,
        failed: 0,
        details: []
      };

      // Process each file
      for (const filename of files) {
        try {
          // Skip non-document files like temp or hidden files
          if (filename.startsWith('.') || filename === '.gitkeep') {
            continue;
          }

          // Parse document ID from filename if it exists
          let documentId = null;
          const idMatch = filename.match(/^([0-9a-f-]{36})_/);
          if (idMatch) {
            documentId = idMatch[1];
          }

          // Skip if document already exists in MongoDB
          if (documentId) {
            const exists = await this.documentModel.exists({ id: documentId });
            if (exists) {
              this.logger.debug(`Document ${documentId} already exists in MongoDB, skipping`);
              continue;
            }
          }

          // Get file stats and path
          const filePath = path.join(this.documentsDir, filename);
          const stats = await fs.stat(filePath);
          
          // Read file content
          const content = await fs.readFile(filePath, 'utf-8');
          
          // Parse original name from filename
          const originalName = filename.includes('_') 
            ? filename.substring(filename.indexOf('_') + 1) 
            : filename;

          // Generate a new ID if none was found
          if (!documentId) {
            const { v4: uuidv4 } = require('uuid');
            documentId = uuidv4();
          }

          // Create document in MongoDB
          await this.documentStorageService.saveDocument({
            id: documentId,
            filename,
            originalName,
            content,
            size: stats.size,
            uploadDate: stats.mtime,
            status: 'completed',
            metadata: {
              migrated: true,
              lastUpdated: new Date()
            }
          });

          result.success++;
          this.logger.log(`Migrated document: ${filename} (${documentId})`);
        } catch (fileError) {
          result.failed++;
          const errorMsg = `Error migrating document ${filename}: ${fileError.message}`;
          result.details.push(errorMsg);
          this.logger.warn(errorMsg);
        }
      }

      this.logger.log(`Migration completed: ${result.success} documents migrated, ${result.failed} failed`);
      return result;
    } catch (error) {
      this.logger.error(`Error migrating documents: ${error.message}`, error.stack);
      return {
        success: 0,
        failed: 0,
        details: [error.message]
      };
    }
  }
}

import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { SavedSearch, SavedSearchDocument } from '../schemas/saved-search.schema';
import { CreateSavedSearchDto, UpdateSavedSearchDto } from '../dto/saved-search.dto';

@Injectable()
export class SavedSearchesService {
  private readonly logger = new Logger(SavedSearchesService.name);

  constructor(
    @InjectModel(SavedSearch.name) private savedSearchModel: Model<SavedSearchDocument>,
  ) {}

  /**
   * Create a new saved search
   */
  async create(createSavedSearchDto: CreateSavedSearchDto, organizationId: string, userId: string): Promise<SavedSearch> {
    this.logger.log(`Creating new saved search: ${createSavedSearchDto.name} for organization: ${organizationId}`);
    
    // Check if saved search with same name already exists for this user
    const existingSearch = await this.savedSearchModel.findOne({
      name: createSavedSearchDto.name,
      organizationId,
      createdBy: userId,
    }).exec();
    
    if (existingSearch) {
      throw new BadRequestException(`Saved search with name '${createSavedSearchDto.name}' already exists`);
    }
    
    // Create new saved search
    const newSavedSearch = new this.savedSearchModel({
      ...createSavedSearchDto,
      organizationId,
      createdBy: userId,
    });
    
    return newSavedSearch.save();
  }

  /**
   * Find all saved searches for a user in an organization
   */
  async findAll(organizationId: string, userId: string): Promise<SavedSearch[]> {
    this.logger.log(`Finding all saved searches for user: ${userId} in organization: ${organizationId}`);
    
    // Find searches created by the user or shared with the user
    return this.savedSearchModel.find({
      organizationId,
      $or: [
        { createdBy: userId },
        { sharedWith: userId },
      ],
    }).exec();
  }

  /**
   * Find a saved search by ID
   */
  async findOne(id: string, organizationId: string, userId: string): Promise<SavedSearch> {
    this.logger.log(`Finding saved search with ID: ${id} for organization: ${organizationId}`);
    
    const savedSearch = await this.savedSearchModel.findOne({
      _id: id,
      organizationId,
      $or: [
        { createdBy: userId },
        { sharedWith: userId },
      ],
    }).exec();
    
    if (!savedSearch) {
      throw new NotFoundException(`Saved search with ID '${id}' not found`);
    }
    
    return savedSearch;
  }

  /**
   * Update a saved search
   */
  async update(id: string, updateSavedSearchDto: UpdateSavedSearchDto, organizationId: string, userId: string): Promise<SavedSearch> {
    this.logger.log(`Updating saved search with ID: ${id} for organization: ${organizationId}`);
    
    // Check if saved search exists and user has access
    const savedSearch = await this.findOne(id, organizationId, userId);
    
    // Only the creator can update the search
    if (savedSearch.createdBy !== userId) {
      throw new BadRequestException('Only the creator can update a saved search');
    }
    
    // Check if updating to a name that already exists
    if (updateSavedSearchDto.name && updateSavedSearchDto.name !== savedSearch.name) {
      const existingSearch = await this.savedSearchModel.findOne({
        name: updateSavedSearchDto.name,
        organizationId,
        createdBy: userId,
        _id: { $ne: id },
      }).exec();
      
      if (existingSearch) {
        throw new BadRequestException(`Saved search with name '${updateSavedSearchDto.name}' already exists`);
      }
    }
    
    // Update saved search
    const updatedSavedSearch = await this.savedSearchModel.findByIdAndUpdate(
      id,
      { $set: updateSavedSearchDto },
      { new: true },
    ).exec();
    
    return updatedSavedSearch;
  }

  /**
   * Delete a saved search
   */
  async remove(id: string, organizationId: string, userId: string): Promise<void> {
    this.logger.log(`Removing saved search with ID: ${id} for organization: ${organizationId}`);
    
    // Check if saved search exists and user has access
    const savedSearch = await this.findOne(id, organizationId, userId);
    
    // Only the creator can delete the search
    if (savedSearch.createdBy !== userId) {
      throw new BadRequestException('Only the creator can delete a saved search');
    }
    
    // Delete saved search
    await this.savedSearchModel.findByIdAndDelete(id).exec();
  }

  /**
   * Execute a saved search
   */
  async execute(id: string, organizationId: string, userId: string): Promise<any> {
    this.logger.log(`Executing saved search with ID: ${id}`);
    
    // Check if saved search exists and user has access
    const savedSearch = await this.findOne(id, organizationId, userId);
    
    // Update last executed timestamp
    await this.savedSearchModel.updateOne(
      { _id: id },
      {
        $set: {
          lastExecuted: new Date(),
        },
      }
    ).exec();
    
    // In a real implementation, this would execute the search against the documents
    // For now, we'll return a mock result
    const mockResults = {
      query: savedSearch.criteria,
      results: [
        { id: 'doc1', title: 'Sample Document 1', score: 0.95 },
        { id: 'doc2', title: 'Sample Document 2', score: 0.85 },
        { id: 'doc3', title: 'Sample Document 3', score: 0.75 },
      ],
      total: 3,
      executedAt: new Date(),
    };
    
    // Update last result count
    await this.savedSearchModel.updateOne(
      { _id: id },
      {
        $set: {
          lastResultCount: mockResults.total,
        },
      }
    ).exec();
    
    return mockResults;
  }

  /**
   * Share a saved search with users
   */
  async share(id: string, userIds: string[], organizationId: string, userId: string): Promise<SavedSearch> {
    this.logger.log(`Sharing saved search ${id} with users: ${userIds.join(', ')}`);
    
    // Check if saved search exists and user has access
    const savedSearch = await this.findOne(id, organizationId, userId);
    
    // Only the creator can share the search
    if (savedSearch.createdBy !== userId) {
      throw new BadRequestException('Only the creator can share a saved search');
    }
    
    // Update shared users
    const updatedSavedSearch = await this.savedSearchModel.findByIdAndUpdate(
      id,
      {
        $addToSet: { sharedWith: { $each: userIds } },
      },
      { new: true },
    ).exec();
    
    return updatedSavedSearch;
  }

  /**
   * Unshare a saved search with users
   */
  async unshare(id: string, userIds: string[], organizationId: string, userId: string): Promise<SavedSearch> {
    this.logger.log(`Unsharing saved search ${id} from users: ${userIds.join(', ')}`);
    
    // Check if saved search exists and user has access
    const savedSearch = await this.findOne(id, organizationId, userId);
    
    // Only the creator can unshare the search
    if (savedSearch.createdBy !== userId) {
      throw new BadRequestException('Only the creator can unshare a saved search');
    }
    
    // Update shared users
    const updatedSavedSearch = await this.savedSearchModel.findByIdAndUpdate(
      id,
      {
        $pull: { sharedWith: { $in: userIds } },
      },
      { new: true },
    ).exec();
    
    return updatedSavedSearch;
  }
}

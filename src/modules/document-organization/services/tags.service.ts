import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Tag, TagDocument } from '../schemas/tag.schema';
import { CreateTagDto, UpdateTagDto } from '../dto/tag.dto';

@Injectable()
export class TagsService {
  private readonly logger = new Logger(TagsService.name);

  constructor(
    @InjectModel(Tag.name) private tagModel: Model<TagDocument>,
  ) {}

  /**
   * Create a new tag
   */
  async create(createTagDto: CreateTagDto, organizationId: string, userId: string): Promise<Tag> {
    this.logger.log(`Creating new tag: ${createTagDto.name} for organization: ${organizationId}`);
    
    // Check if tag with same name already exists for this organization
    const existingTag = await this.tagModel.findOne({
      name: createTagDto.name,
      organizationId,
    }).exec();
    
    if (existingTag) {
      throw new BadRequestException(`Tag with name '${createTagDto.name}' already exists`);
    }
    
    // Create new tag
    const newTag = new this.tagModel({
      ...createTagDto,
      organizationId,
      createdBy: userId,
    });
    
    return newTag.save();
  }

  /**
   * Find all tags for an organization
   */
  async findAll(organizationId: string): Promise<Tag[]> {
    this.logger.log(`Finding all tags for organization: ${organizationId}`);
    return this.tagModel.find({ organizationId }).exec();
  }

  /**
   * Find a tag by ID
   */
  async findOne(id: string, organizationId: string): Promise<Tag> {
    this.logger.log(`Finding tag with ID: ${id} for organization: ${organizationId}`);
    
    const tag = await this.tagModel.findOne({
      _id: id,
      organizationId,
    }).exec();
    
    if (!tag) {
      throw new NotFoundException(`Tag with ID '${id}' not found`);
    }
    
    return tag;
  }

  /**
   * Update a tag
   */
  async update(id: string, updateTagDto: UpdateTagDto, organizationId: string): Promise<Tag> {
    this.logger.log(`Updating tag with ID: ${id} for organization: ${organizationId}`);
    
    // Check if tag exists
    const tag = await this.findOne(id, organizationId);
    
    // Check if updating to a name that already exists
    if (updateTagDto.name && updateTagDto.name !== tag.name) {
      const existingTag = await this.tagModel.findOne({
        name: updateTagDto.name,
        organizationId,
        _id: { $ne: id },
      }).exec();
      
      if (existingTag) {
        throw new BadRequestException(`Tag with name '${updateTagDto.name}' already exists`);
      }
    }
    
    // Update tag
    const updatedTag = await this.tagModel.findByIdAndUpdate(
      id,
      { $set: updateTagDto },
      { new: true },
    ).exec();
    
    return updatedTag;
  }

  /**
   * Delete a tag
   */
  async remove(id: string, organizationId: string): Promise<void> {
    this.logger.log(`Removing tag with ID: ${id} for organization: ${organizationId}`);
    
    // Check if tag exists
    await this.findOne(id, organizationId);
    
    // Delete tag
    await this.tagModel.findByIdAndDelete(id).exec();
  }

  /**
   * Add a document to a tag
   */
  async addDocumentToTag(tagId: string, documentId: string, organizationId: string): Promise<Tag> {
    this.logger.log(`Adding document ${documentId} to tag ${tagId}`);
    
    // Check if tag exists
    const tag = await this.findOne(tagId, organizationId);
    
    // Update document count
    const updatedTag = await this.tagModel.findByIdAndUpdate(
      tagId,
      { $inc: { documentCount: 1 } },
      { new: true },
    ).exec();
    
    return updatedTag;
  }

  /**
   * Remove a document from a tag
   */
  async removeDocumentFromTag(tagId: string, documentId: string, organizationId: string): Promise<Tag> {
    this.logger.log(`Removing document ${documentId} from tag ${tagId}`);
    
    // Check if tag exists
    const tag = await this.findOne(tagId, organizationId);
    
    // Update document count (ensure it doesn't go below 0)
    const updatedTag = await this.tagModel.findByIdAndUpdate(
      tagId,
      { $inc: { documentCount: -1 } },
      { new: true },
    ).exec();
    
    // If document count went below 0, set it to 0
    if (updatedTag.documentCount < 0) {
      updatedTag.documentCount = 0;
      await updatedTag.save();
    }
    
    return updatedTag;
  }

  /**
   * Find tags by document ID
   */
  async findTagsByDocumentId(documentId: string, organizationId: string): Promise<Tag[]> {
    this.logger.log(`Finding tags for document: ${documentId}`);
    
    // This is a placeholder - in a real implementation, you would have a many-to-many relationship
    // between tags and documents, likely stored in a separate collection
    return [];
  }
}

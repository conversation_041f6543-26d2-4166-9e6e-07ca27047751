import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Folder, FolderDocument } from '../schemas/folder.schema';
import { CreateFolderDto, UpdateFolderDto, FolderSharingDto } from '../dto/folder.dto';

@Injectable()
export class FoldersService {
  private readonly logger = new Logger(FoldersService.name);

  constructor(
    @InjectModel(Folder.name) private folderModel: Model<FolderDocument>,
  ) {}

  /**
   * Create a new folder
   */
  async create(createFolderDto: CreateFolderDto, organizationId: string, userId: string): Promise<Folder> {
    this.logger.log(`Creating new folder: ${createFolderDto.name} for organization: ${organizationId}`);
    
    // Generate folder path
    let path = `/${createFolderDto.name}`;
    
    // If parent folder is specified, validate and update path
    if (createFolderDto.parentId) {
      const parentFolder = await this.folderModel.findOne({
        _id: createFolderDto.parentId,
        organizationId,
      }).exec();
      
      if (!parentFolder) {
        throw new NotFoundException(`Parent folder with ID '${createFolderDto.parentId}' not found`);
      }
      
      path = `${parentFolder.path}/${createFolderDto.name}`;
    }
    
    // Check if folder with same path already exists
    const existingFolder = await this.folderModel.findOne({
      path,
      organizationId,
    }).exec();
    
    if (existingFolder) {
      throw new BadRequestException(`Folder with path '${path}' already exists`);
    }
    
    // Create new folder
    const newFolder = new this.folderModel({
      ...createFolderDto,
      path,
      organizationId,
      createdBy: userId,
    });
    
    return newFolder.save();
  }

  /**
   * Find all folders for an organization
   */
  async findAll(organizationId: string): Promise<Folder[]> {
    this.logger.log(`Finding all folders for organization: ${organizationId}`);
    return this.folderModel.find({ organizationId }).exec();
  }

  /**
   * Find all root folders (folders without parents)
   */
  async findRootFolders(organizationId: string): Promise<Folder[]> {
    this.logger.log(`Finding root folders for organization: ${organizationId}`);
    return this.folderModel.find({
      organizationId,
      parentId: null,
    }).exec();
  }

  /**
   * Find child folders of a parent folder
   */
  async findChildFolders(parentId: string, organizationId: string): Promise<Folder[]> {
    this.logger.log(`Finding child folders of parent: ${parentId}`);
    return this.folderModel.find({
      organizationId,
      parentId,
    }).exec();
  }

  /**
   * Find a folder by ID
   */
  async findOne(id: string, organizationId: string): Promise<Folder> {
    this.logger.log(`Finding folder with ID: ${id} for organization: ${organizationId}`);
    
    const folder = await this.folderModel.findOne({
      _id: id,
      organizationId,
    }).exec();
    
    if (!folder) {
      throw new NotFoundException(`Folder with ID '${id}' not found`);
    }
    
    return folder;
  }

  /**
   * Update a folder
   */
  async update(id: string, updateFolderDto: UpdateFolderDto, organizationId: string): Promise<Folder> {
    this.logger.log(`Updating folder with ID: ${id} for organization: ${organizationId}`);
    
    // Check if folder exists
    const folder = await this.findOne(id, organizationId);
    
    // Handle name change and path updates
    if (updateFolderDto.name && updateFolderDto.name !== folder.name) {
      // Generate new path
      let newPath = folder.path.split('/');
      newPath[newPath.length - 1] = updateFolderDto.name;
      const updatedPath = newPath.join('/');
      
      // Check if folder with new path already exists
      const existingFolder = await this.folderModel.findOne({
        path: updatedPath,
        organizationId,
        _id: { $ne: id },
      }).exec();
      
      if (existingFolder) {
        throw new BadRequestException(`Folder with path '${updatedPath}' already exists`);
      }
      
      // Update path for this folder
      updateFolderDto['path'] = updatedPath;
      
      // Update paths for all child folders
      const childFolders = await this.folderModel.find({
        path: { $regex: `^${folder.path}/` },
        organizationId,
      }).exec();
      
      for (const childFolder of childFolders) {
        const childPath = childFolder.path.replace(folder.path, updatedPath);
        await this.folderModel.updateOne(
          { _id: childFolder._id },
          { $set: { path: childPath } }
        ).exec();
      }
    }
    
    // Handle parent folder change
    if (updateFolderDto.parentId !== undefined && updateFolderDto.parentId !== folder.parentId?.toString()) {
      // Prevent setting a folder as its own parent
      if (updateFolderDto.parentId === id) {
        throw new BadRequestException('Cannot set a folder as its own parent');
      }
      
      // Prevent setting a child folder as the parent (would create a cycle)
      if (updateFolderDto.parentId) {
        const childFolders = await this.folderModel.find({
          path: { $regex: `^${folder.path}/` },
          organizationId,
        }).exec();
        
        const childIds = childFolders.map(child => child._id.toString());
        
        if (childIds.includes(updateFolderDto.parentId)) {
          throw new BadRequestException('Cannot set a child folder as the parent');
        }
        
        // Get new parent folder
        const parentFolder = await this.findOne(updateFolderDto.parentId, organizationId);
        
        // Generate new path
        const folderName = folder.name;
        const newPath = `${parentFolder.path}/${folderName}`;
        
        // Check if folder with new path already exists
        const existingFolder = await this.folderModel.findOne({
          path: newPath,
          organizationId,
          _id: { $ne: id },
        }).exec();
        
        if (existingFolder) {
          throw new BadRequestException(`Folder with path '${newPath}' already exists`);
        }
        
        // Update path for this folder
        updateFolderDto['path'] = newPath;
        
        // Update paths for all child folders
        for (const childFolder of childFolders) {
          const childPath = childFolder.path.replace(folder.path, newPath);
          await this.folderModel.updateOne(
            { _id: childFolder._id },
            { $set: { path: childPath } }
          ).exec();
        }
      } else {
        // Moving to root level
        const folderName = folder.name;
        const newPath = `/${folderName}`;
        
        // Check if folder with new path already exists
        const existingFolder = await this.folderModel.findOne({
          path: newPath,
          organizationId,
          _id: { $ne: id },
        }).exec();
        
        if (existingFolder) {
          throw new BadRequestException(`Folder with path '${newPath}' already exists`);
        }
        
        // Update path for this folder
        updateFolderDto['path'] = newPath;
        
        // Update paths for all child folders
        const childFolders = await this.folderModel.find({
          path: { $regex: `^${folder.path}/` },
          organizationId,
        }).exec();
        
        for (const childFolder of childFolders) {
          const childPath = childFolder.path.replace(folder.path, newPath);
          await this.folderModel.updateOne(
            { _id: childFolder._id },
            { $set: { path: childPath } }
          ).exec();
        }
      }
    }
    
    // Update folder
    const updatedFolder = await this.folderModel.findByIdAndUpdate(
      id,
      { $set: updateFolderDto },
      { new: true },
    ).exec();
    
    return updatedFolder;
  }

  /**
   * Delete a folder
   */
  async remove(id: string, organizationId: string): Promise<void> {
    this.logger.log(`Removing folder with ID: ${id} for organization: ${organizationId}`);
    
    // Check if folder exists
    const folder = await this.findOne(id, organizationId);
    
    // Check if folder has child folders
    const childFolders = await this.folderModel.find({
      path: { $regex: `^${folder.path}/` },
      organizationId,
    }).exec();
    
    if (childFolders.length > 0) {
      throw new BadRequestException('Cannot delete folder with child folders');
    }
    
    // Check if folder has documents
    if (folder.documentCount > 0) {
      throw new BadRequestException('Cannot delete folder with documents');
    }
    
    // Delete folder
    await this.folderModel.findByIdAndDelete(id).exec();
  }

  /**
   * Update folder sharing settings
   */
  async updateSharing(id: string, sharingDto: FolderSharingDto, organizationId: string): Promise<Folder> {
    this.logger.log(`Updating sharing settings for folder: ${id}`);
    
    // Check if folder exists
    const folder = await this.findOne(id, organizationId);
    
    // Update sharing settings
    const updatedFolder = await this.folderModel.findByIdAndUpdate(
      id,
      {
        $set: {
          'sharing.public': sharingDto.public !== undefined ? sharingDto.public : folder.sharing.public,
          'sharing.sharedWith': sharingDto.sharedWith || folder.sharing.sharedWith,
          'sharing.permissions': sharingDto.permissions || folder.sharing.permissions,
        },
      },
      { new: true },
    ).exec();
    
    return updatedFolder;
  }

  /**
   * Add documents to a folder
   */
  async addDocuments(id: string, documentIds: string[], organizationId: string): Promise<Folder> {
    this.logger.log(`Adding documents to folder: ${id}`);
    
    // Check if folder exists
    const folder = await this.findOne(id, organizationId);
    
    // Add documents to folder
    const updatedFolder = await this.folderModel.findByIdAndUpdate(
      id,
      {
        $addToSet: { documentIds: { $each: documentIds } },
        $inc: { documentCount: documentIds.length },
      },
      { new: true },
    ).exec();
    
    return updatedFolder;
  }

  /**
   * Remove documents from a folder
   */
  async removeDocuments(id: string, documentIds: string[], organizationId: string): Promise<Folder> {
    this.logger.log(`Removing documents from folder: ${id}`);
    
    // Check if folder exists
    const folder = await this.findOne(id, organizationId);
    
    // Count how many documents are actually in the folder
    const matchingDocuments = folder.documentIds.filter(docId => 
      documentIds.includes(docId.toString())
    );
    
    // Remove documents from folder
    const updatedFolder = await this.folderModel.findByIdAndUpdate(
      id,
      {
        $pull: { documentIds: { $in: documentIds } },
        $inc: { documentCount: -matchingDocuments.length },
      },
      { new: true },
    ).exec();
    
    return updatedFolder;
  }

  /**
   * Find folders containing a document
   */
  async findFoldersByDocumentId(documentId: string, organizationId: string): Promise<Folder[]> {
    this.logger.log(`Finding folders containing document: ${documentId}`);
    
    return this.folderModel.find({
      organizationId,
      documentIds: documentId,
    }).exec();
  }
}

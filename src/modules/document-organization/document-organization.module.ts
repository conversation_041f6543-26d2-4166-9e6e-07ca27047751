import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { HttpModule } from '@nestjs/axios';
import { TagsController } from './controllers/tags.controller';
import { FoldersController } from './controllers/folders.controller';
import { SavedSearchesController } from './controllers/saved-searches.controller';
import { TagsService } from './services/tags.service';
import { FoldersService } from './services/folders.service';
import { SavedSearchesService } from './services/saved-searches.service';
import { Tag, TagSchema } from './schemas/tag.schema';
import { Folder, FolderSchema } from './schemas/folder.schema';
import { SavedSearch, SavedSearchSchema } from './schemas/saved-search.schema';
import { DocumentsModule } from '../documents/documents.module';
import { AuthModule } from '../auth/auth.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { AnalyticsModule } from '../analytics/analytics.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Tag.name, schema: TagSchema },
      { name: Folder.name, schema: FolderSchema },
      { name: SavedSearch.name, schema: SavedSearchSchema },
    ]),
    forwardRef(() => DocumentsModule),
    forwardRef(() => AuthModule),
    SubscriptionModule,
    forwardRef(() => AnalyticsModule),
    HttpModule,
  ],
  controllers: [
    TagsController,
    FoldersController,
    SavedSearchesController,
  ],
  providers: [
    TagsService,
    FoldersService,
    SavedSearchesService,
  ],
  exports: [
    TagsService,
    FoldersService,
    SavedSearchesService,
  ],
})
export class DocumentOrganizationModule {}

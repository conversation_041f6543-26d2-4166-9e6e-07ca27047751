import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type FolderDocument = Folder & Document;

@Schema({ timestamps: true })
export class Folder {
  @ApiProperty({ description: 'Folder name', example: 'Contracts' })
  @Prop({ required: true, trim: true })
  name: string;

  @ApiProperty({ description: 'Folder description', example: 'All legal contracts and agreements' })
  @Prop({ default: '' })
  description: string;

  @ApiProperty({ description: 'Parent folder ID', example: '507f1f77bcf86cd799439011' })
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Folder', default: null })
  parentId: MongooseSchema.Types.ObjectId | null;

  @ApiProperty({ description: 'Organization ID', example: '507f1f77bcf86cd799439011' })
  @Prop({ required: true, index: true })
  organizationId: string;

  @ApiProperty({ description: 'User who created the folder', example: '507f1f77bcf86cd799439012' })
  @Prop({ required: true })
  createdBy: string;

  @ApiProperty({ description: 'Folder path for easier navigation', example: '/Contracts/Employment' })
  @Prop({ required: true, index: true })
  path: string;

  @ApiProperty({ description: 'Document IDs contained in this folder', type: [String] })
  @Prop({ type: [String], default: [] })
  documentIds: string[];

  @ApiProperty({ description: 'Number of documents in this folder', example: 5 })
  @Prop({ default: 0 })
  documentCount: number;

  @ApiProperty({ description: 'Folder permissions', type: Object })
  @Prop({
    type: {
      public: { type: Boolean, default: false },
      sharedWith: { type: [String], default: [] },
      permissions: { type: Object, default: {} }
    },
    default: { public: false, sharedWith: [], permissions: {} }
  })
  sharing: {
    public: boolean;
    sharedWith: string[];
    permissions: Record<string, string>;
  };
}

export const FolderSchema = SchemaFactory.createForClass(Folder);

// Add indexes for faster querying
FolderSchema.index({ organizationId: 1, path: 1 }, { unique: true });
FolderSchema.index({ parentId: 1 });

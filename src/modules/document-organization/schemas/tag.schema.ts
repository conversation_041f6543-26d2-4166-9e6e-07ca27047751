import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type TagDocument = Tag & Document;

@Schema({ timestamps: true })
export class Tag {
  @ApiProperty({ description: 'Tag name', example: 'Contract' })
  @Prop({ required: true, trim: true })
  name: string;

  @ApiProperty({ description: 'Tag color', example: '#FF5733' })
  @Prop({ default: '#3498db' })
  color: string;

  @ApiProperty({ description: 'Tag description', example: 'Legal contracts and agreements' })
  @Prop({ default: '' })
  description: string;

  @ApiProperty({ description: 'Organization ID', example: '507f1f77bcf86cd799439011' })
  @Prop({ required: true, index: true })
  organizationId: string;

  @ApiProperty({ description: 'User who created the tag', example: '507f1f77bcf86cd799439012' })
  @Prop({ required: true })
  createdBy: string;

  @ApiProperty({ description: 'Document count with this tag', example: 5 })
  @Prop({ default: 0 })
  documentCount: number;

  @ApiProperty({ description: 'Tag usage analytics', type: Object })
  @Prop({ type: Object, default: {} })
  analytics: Record<string, any>;
}

export const TagSchema = SchemaFactory.createForClass(Tag);

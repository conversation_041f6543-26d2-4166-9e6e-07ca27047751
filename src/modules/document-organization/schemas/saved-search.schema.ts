import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { ApiProperty } from '@nestjs/swagger';

export type SavedSearchDocument = SavedSearch & Document;

@Schema({ timestamps: true })
export class SavedSearch {
  @ApiProperty({ description: 'Search name', example: 'Recent Contracts' })
  @Prop({ required: true, trim: true })
  name: string;

  @ApiProperty({ description: 'Search description', example: 'Contracts created in the last 30 days' })
  @Prop({ default: '' })
  description: string;

  @ApiProperty({ description: 'Search query criteria', type: Object })
  @Prop({ type: Object, required: true })
  criteria: Record<string, any>;

  @ApiProperty({ description: 'Organization ID', example: '507f1f77bcf86cd799439011' })
  @Prop({ required: true, index: true })
  organizationId: string;

  @ApiProperty({ description: 'User who created the search', example: '507f1f77bcf86cd799439012' })
  @Prop({ required: true })
  createdBy: string;

  @ApiProperty({ description: 'Whether to receive notifications for new matches', example: false })
  @Prop({ default: false })
  notificationsEnabled: boolean;

  @ApiProperty({ description: 'Notification frequency', example: 'daily' })
  @Prop({ enum: ['realtime', 'daily', 'weekly'], default: 'daily' })
  notificationFrequency: string;

  @ApiProperty({ description: 'Users with whom this search is shared', type: [String] })
  @Prop({ type: [String], default: [] })
  sharedWith: string[];

  @ApiProperty({ description: 'Last execution timestamp', example: '2025-04-19T12:00:00Z' })
  @Prop({ type: Date })
  lastExecuted: Date;

  @ApiProperty({ description: 'Last result count', example: 5 })
  @Prop({ default: 0 })
  lastResultCount: number;
}

export const SavedSearchSchema = SchemaFactory.createForClass(SavedSearch);

// Add indexes for faster querying
SavedSearchSchema.index({ organizationId: 1, createdBy: 1 });
SavedSearchSchema.index({ organizationId: 1, sharedWith: 1 });

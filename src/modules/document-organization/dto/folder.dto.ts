import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNotEmpty, MaxLength, IsBoolean, IsArray, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateFolderDto {
  @ApiProperty({ description: 'Folder name', example: 'Contracts' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  name: string;

  @ApiProperty({ description: 'Folder description', example: 'All legal contracts and agreements', required: false })
  @IsString()
  @IsOptional()
  @MaxLength(500)
  description?: string;

  @ApiProperty({ description: 'Parent folder ID', example: '507f1f77bcf86cd799439011', required: false })
  @IsUUID('4', { each: true })
  @IsOptional()
  parentId?: string;
}

export class UpdateFolderDto {
  @ApiProperty({ description: 'Folder name', example: 'Contracts', required: false })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  name?: string;

  @ApiProperty({ description: 'Folder description', example: 'All legal contracts and agreements', required: false })
  @IsString()
  @IsOptional()
  @MaxLength(500)
  description?: string;

  @ApiProperty({ description: 'Parent folder ID', example: '507f1f77bcf86cd799439011', required: false })
  @IsUUID('4', { each: true })
  @IsOptional()
  parentId?: string;
}

export class FolderSharingDto {
  @ApiProperty({ description: 'Make folder public', example: false })
  @IsBoolean()
  @IsOptional()
  public?: boolean;

  @ApiProperty({ description: 'Users to share with', type: [String], example: ['<EMAIL>', '<EMAIL>'] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  sharedWith?: string[];

  @ApiProperty({ description: 'User permissions', type: Object, example: { '<EMAIL>': 'read', '<EMAIL>': 'write' } })
  @IsOptional()
  @Type(() => Object)
  permissions?: Record<string, string>;
}

export class FolderDocumentDto {
  @ApiProperty({ description: 'Document IDs to add/remove', type: [String], example: ['225cbb91-28d3-4ba6-a2b9-1ace99cc09bb'] })
  @IsArray()
  @IsUUID('4', { each: true })
  documentIds: string[];
}

export class FolderResponseDto {
  @ApiProperty({ description: 'Folder ID', example: '507f1f77bcf86cd799439011' })
  id: string;

  @ApiProperty({ description: 'Folder name', example: 'Contracts' })
  name: string;

  @ApiProperty({ description: 'Folder description', example: 'All legal contracts and agreements' })
  description: string;

  @ApiProperty({ description: 'Parent folder ID', example: '507f1f77bcf86cd799439011' })
  parentId: string | null;

  @ApiProperty({ description: 'Organization ID', example: '507f1f77bcf86cd799439011' })
  organizationId: string;

  @ApiProperty({ description: 'User who created the folder', example: '507f1f77bcf86cd799439012' })
  createdBy: string;

  @ApiProperty({ description: 'Folder path', example: '/Contracts/Employment' })
  path: string;

  @ApiProperty({ description: 'Document count', example: 5 })
  documentCount: number;

  @ApiProperty({ description: 'Folder sharing settings', type: Object })
  sharing: {
    public: boolean;
    sharedWith: string[];
    permissions: Record<string, string>;
  };

  @ApiProperty({ description: 'Creation date', example: '2025-04-19T12:00:00Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date', example: '2025-04-19T12:00:00Z' })
  updatedAt: Date;
}

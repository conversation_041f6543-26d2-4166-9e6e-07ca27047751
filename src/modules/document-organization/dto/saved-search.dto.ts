import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNotEmpty, MaxLength, IsBoolean, IsArray, IsEnum, IsObject, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export enum NotificationFrequency {
  REALTIME = 'realtime',
  DAILY = 'daily',
  WEEKLY = 'weekly',
}

export class SearchCriteriaDto {
  @ApiProperty({ description: 'Text to search for', example: 'contract', required: false })
  @IsString()
  @IsOptional()
  text?: string;

  @ApiProperty({ description: 'Document type', example: 'contract', required: false })
  @IsString()
  @IsOptional()
  documentType?: string;

  @ApiProperty({ description: 'Tags to include', type: [String], example: ['important', 'legal'], required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];

  @ApiProperty({ description: 'Date range start', example: '2025-01-01', required: false })
  @IsString()
  @IsOptional()
  dateFrom?: string;

  @ApiProperty({ description: 'Date range end', example: '2025-04-19', required: false })
  @IsString()
  @IsOptional()
  dateTo?: string;

  @ApiProperty({ description: 'Created by user', example: '507f1f77bcf86cd799439012', required: false })
  @IsString()
  @IsOptional()
  createdBy?: string;

  @ApiProperty({ description: 'Additional filters', type: Object, required: false })
  @IsObject()
  @IsOptional()
  additionalFilters?: Record<string, any>;
}

export class CreateSavedSearchDto {
  @ApiProperty({ description: 'Search name', example: 'Recent Contracts' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(100)
  name: string;

  @ApiProperty({ description: 'Search description', example: 'Contracts created in the last 30 days', required: false })
  @IsString()
  @IsOptional()
  @MaxLength(500)
  description?: string;

  @ApiProperty({ description: 'Search criteria', type: SearchCriteriaDto })
  @ValidateNested()
  @Type(() => SearchCriteriaDto)
  criteria: SearchCriteriaDto;

  @ApiProperty({ description: 'Enable notifications for new matches', example: false, required: false })
  @IsBoolean()
  @IsOptional()
  notificationsEnabled?: boolean;

  @ApiProperty({ description: 'Notification frequency', enum: NotificationFrequency, example: NotificationFrequency.DAILY, required: false })
  @IsEnum(NotificationFrequency)
  @IsOptional()
  notificationFrequency?: NotificationFrequency;

  @ApiProperty({ description: 'Users to share with', type: [String], example: ['<EMAIL>', '<EMAIL>'], required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  sharedWith?: string[];
}

export class UpdateSavedSearchDto {
  @ApiProperty({ description: 'Search name', example: 'Recent Contracts', required: false })
  @IsString()
  @IsOptional()
  @MaxLength(100)
  name?: string;

  @ApiProperty({ description: 'Search description', example: 'Contracts created in the last 30 days', required: false })
  @IsString()
  @IsOptional()
  @MaxLength(500)
  description?: string;

  @ApiProperty({ description: 'Search criteria', type: SearchCriteriaDto, required: false })
  @ValidateNested()
  @Type(() => SearchCriteriaDto)
  @IsOptional()
  criteria?: SearchCriteriaDto;

  @ApiProperty({ description: 'Enable notifications for new matches', example: false, required: false })
  @IsBoolean()
  @IsOptional()
  notificationsEnabled?: boolean;

  @ApiProperty({ description: 'Notification frequency', enum: NotificationFrequency, example: NotificationFrequency.DAILY, required: false })
  @IsEnum(NotificationFrequency)
  @IsOptional()
  notificationFrequency?: NotificationFrequency;

  @ApiProperty({ description: 'Users to share with', type: [String], example: ['<EMAIL>', '<EMAIL>'], required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  sharedWith?: string[];
}

export class SavedSearchResponseDto {
  @ApiProperty({ description: 'Search ID', example: '507f1f77bcf86cd799439011' })
  id: string;

  @ApiProperty({ description: 'Search name', example: 'Recent Contracts' })
  name: string;

  @ApiProperty({ description: 'Search description', example: 'Contracts created in the last 30 days' })
  description: string;

  @ApiProperty({ description: 'Search criteria', type: Object })
  criteria: Record<string, any>;

  @ApiProperty({ description: 'Organization ID', example: '507f1f77bcf86cd799439011' })
  organizationId: string;

  @ApiProperty({ description: 'User who created the search', example: '507f1f77bcf86cd799439012' })
  createdBy: string;

  @ApiProperty({ description: 'Notifications enabled', example: false })
  notificationsEnabled: boolean;

  @ApiProperty({ description: 'Notification frequency', enum: NotificationFrequency, example: NotificationFrequency.DAILY })
  notificationFrequency: NotificationFrequency;

  @ApiProperty({ description: 'Users with whom this search is shared', type: [String] })
  sharedWith: string[];

  @ApiProperty({ description: 'Last execution timestamp', example: '2025-04-19T12:00:00Z' })
  lastExecuted: Date;

  @ApiProperty({ description: 'Last result count', example: 5 })
  lastResultCount: number;

  @ApiProperty({ description: 'Creation date', example: '2025-04-19T12:00:00Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date', example: '2025-04-19T12:00:00Z' })
  updatedAt: Date;
}

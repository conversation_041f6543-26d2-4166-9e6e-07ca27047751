import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsHexColor, IsNotEmpty, MaxLength } from 'class-validator';

export class CreateTagDto {
  @ApiProperty({ description: 'Tag name', example: 'Contract' })
  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  name: string;

  @ApiProperty({ description: 'Tag color', example: '#FF5733', required: false })
  @IsHexColor()
  @IsOptional()
  color?: string;

  @ApiProperty({ description: 'Tag description', example: 'Legal contracts and agreements', required: false })
  @IsString()
  @IsOptional()
  @MaxLength(200)
  description?: string;
}

export class UpdateTagDto {
  @ApiProperty({ description: 'Tag name', example: 'Contract', required: false })
  @IsString()
  @IsOptional()
  @MaxLength(50)
  name?: string;

  @ApiProperty({ description: 'Tag color', example: '#FF5733', required: false })
  @IsHexColor()
  @IsOptional()
  color?: string;

  @ApiProperty({ description: 'Tag description', example: 'Legal contracts and agreements', required: false })
  @IsString()
  @IsOptional()
  @MaxLength(200)
  description?: string;
}

export class TagResponseDto {
  @ApiProperty({ description: 'Tag ID', example: '507f1f77bcf86cd799439011' })
  id: string;

  @ApiProperty({ description: 'Tag name', example: 'Contract' })
  name: string;

  @ApiProperty({ description: 'Tag color', example: '#FF5733' })
  color: string;

  @ApiProperty({ description: 'Tag description', example: 'Legal contracts and agreements' })
  description: string;

  @ApiProperty({ description: 'Organization ID', example: '507f1f77bcf86cd799439011' })
  organizationId: string;

  @ApiProperty({ description: 'User who created the tag', example: '507f1f77bcf86cd799439012' })
  createdBy: string;

  @ApiProperty({ description: 'Document count with this tag', example: 5 })
  documentCount: number;

  @ApiProperty({ description: 'Creation date', example: '2025-04-19T12:00:00Z' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date', example: '2025-04-19T12:00:00Z' })
  updatedAt: Date;
}

export class TagDocumentDto {
  @ApiProperty({ description: 'Document ID', example: '507f1f77bcf86cd799439011' })
  @IsString()
  @IsNotEmpty()
  documentId: string;
}

import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Logger,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { TagsService } from '../services/tags.service';
import { CreateTagDto, UpdateTagDto, TagDocumentDto, TagResponseDto } from '../dto/tag.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { Organization } from '../../auth/decorators/organization.decorator';
import { User } from '../../auth/decorators/user.decorator';
import { FeatureAvailabilityGuard } from '../../subscription/guards/feature-availability.guard';
import { RequireFeatures } from '../../subscription/decorators/require-features.decorator';

@ApiTags('document-organization-tags')
@Controller('document-organization/tags')
@UseGuards(JwtAuthGuard, FeatureAvailabilityGuard)
@RequireFeatures('document_organization')
@ApiBearerAuth()
export class TagsController {
  private readonly logger = new Logger(TagsController.name);

  constructor(private readonly tagsService: TagsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new tag' })
  @ApiResponse({ status: 201, description: 'The tag has been successfully created.', type: TagResponseDto })
  async create(
    @Body() createTagDto: CreateTagDto,
    @Organization() organizationId: string,
    @User() userId: string,
  ) {
    this.logger.log(`Creating tag: ${createTagDto.name}`);
    const tag = await this.tagsService.create(createTagDto, organizationId, userId);
    return this.mapToResponseDto(tag);
  }

  @Get()
  @ApiOperation({ summary: 'Get all tags' })
  @ApiResponse({ status: 200, description: 'Return all tags.', type: [TagResponseDto] })
  async findAll(@Organization() organizationId: string) {
    this.logger.log('Getting all tags');
    const tags = await this.tagsService.findAll(organizationId);
    return tags.map(tag => this.mapToResponseDto(tag));
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a tag by ID' })
  @ApiResponse({ status: 200, description: 'Return the tag.', type: TagResponseDto })
  @ApiResponse({ status: 404, description: 'Tag not found.' })
  async findOne(
    @Param('id') id: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Getting tag with ID: ${id}`);
    const tag = await this.tagsService.findOne(id, organizationId);
    return this.mapToResponseDto(tag);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a tag' })
  @ApiResponse({ status: 200, description: 'The tag has been successfully updated.', type: TagResponseDto })
  @ApiResponse({ status: 404, description: 'Tag not found.' })
  async update(
    @Param('id') id: string,
    @Body() updateTagDto: UpdateTagDto,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Updating tag with ID: ${id}`);
    const tag = await this.tagsService.update(id, updateTagDto, organizationId);
    return this.mapToResponseDto(tag);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a tag' })
  @ApiResponse({ status: 204, description: 'The tag has been successfully deleted.' })
  @ApiResponse({ status: 404, description: 'Tag not found.' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(
    @Param('id') id: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Deleting tag with ID: ${id}`);
    await this.tagsService.remove(id, organizationId);
  }

  @Post(':id/documents')
  @ApiOperation({ summary: 'Add a document to a tag' })
  @ApiResponse({ status: 200, description: 'The document has been successfully added to the tag.', type: TagResponseDto })
  @ApiResponse({ status: 404, description: 'Tag not found.' })
  async addDocument(
    @Param('id') id: string,
    @Body() tagDocumentDto: TagDocumentDto,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Adding document ${tagDocumentDto.documentId} to tag ${id}`);
    const tag = await this.tagsService.addDocumentToTag(id, tagDocumentDto.documentId, organizationId);
    return this.mapToResponseDto(tag);
  }

  @Delete(':id/documents/:documentId')
  @ApiOperation({ summary: 'Remove a document from a tag' })
  @ApiResponse({ status: 200, description: 'The document has been successfully removed from the tag.', type: TagResponseDto })
  @ApiResponse({ status: 404, description: 'Tag not found.' })
  async removeDocument(
    @Param('id') id: string,
    @Param('documentId') documentId: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Removing document ${documentId} from tag ${id}`);
    const tag = await this.tagsService.removeDocumentFromTag(id, documentId, organizationId);
    return this.mapToResponseDto(tag);
  }

  @Get('documents/:documentId')
  @ApiOperation({ summary: 'Get all tags for a document' })
  @ApiResponse({ status: 200, description: 'Return all tags for the document.', type: [TagResponseDto] })
  async findTagsByDocument(
    @Param('documentId') documentId: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Getting tags for document: ${documentId}`);
    const tags = await this.tagsService.findTagsByDocumentId(documentId, organizationId);
    return tags.map(tag => this.mapToResponseDto(tag));
  }

  /**
   * Map a Tag entity to a TagResponseDto
   */
  private mapToResponseDto(tag: any): TagResponseDto {
    return {
      id: tag._id.toString(),
      name: tag.name,
      color: tag.color,
      description: tag.description,
      organizationId: tag.organizationId,
      createdBy: tag.createdBy,
      documentCount: tag.documentCount,
      createdAt: tag.createdAt,
      updatedAt: tag.updatedAt,
    };
  }
}

import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Logger,
  HttpStatus,
  HttpCode,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { SavedSearchesService } from '../services/saved-searches.service';
import { CreateSavedSearchDto, UpdateSavedSearchDto, SavedSearchResponseDto } from '../dto/saved-search.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { Organization } from '../../auth/decorators/organization.decorator';
import { User } from '../../auth/decorators/user.decorator';
import { FeatureAvailabilityGuard } from '../../subscription/guards/feature-availability.guard';
import { RequireFeatures } from '../../subscription/decorators/require-features.decorator';

@ApiTags('document-organization-saved-searches')
@Controller('document-organization/saved-searches')
@UseGuards(JwtAuthGuard, FeatureAvailabilityGuard)
@RequireFeatures('document_organization')
@ApiBearerAuth()
export class SavedSearchesController {
  private readonly logger = new Logger(SavedSearchesController.name);

  constructor(private readonly savedSearchesService: SavedSearchesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new saved search' })
  @ApiResponse({ status: 201, description: 'The saved search has been successfully created.', type: SavedSearchResponseDto })
  async create(
    @Body() createSavedSearchDto: CreateSavedSearchDto,
    @Organization() organizationId: string,
    @User() userId: string,
  ) {
    this.logger.log(`Creating saved search: ${createSavedSearchDto.name}`);
    const savedSearch = await this.savedSearchesService.create(createSavedSearchDto, organizationId, userId);
    return this.mapToResponseDto(savedSearch);
  }

  @Get()
  @ApiOperation({ summary: 'Get all saved searches' })
  @ApiResponse({ status: 200, description: 'Return all saved searches.', type: [SavedSearchResponseDto] })
  async findAll(
    @Organization() organizationId: string,
    @User() userId: string,
  ) {
    this.logger.log('Getting all saved searches');
    const savedSearches = await this.savedSearchesService.findAll(organizationId, userId);
    return savedSearches.map(savedSearch => this.mapToResponseDto(savedSearch));
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a saved search by ID' })
  @ApiResponse({ status: 200, description: 'Return the saved search.', type: SavedSearchResponseDto })
  @ApiResponse({ status: 404, description: 'Saved search not found.' })
  async findOne(
    @Param('id') id: string,
    @Organization() organizationId: string,
    @User() userId: string,
  ) {
    this.logger.log(`Getting saved search with ID: ${id}`);
    const savedSearch = await this.savedSearchesService.findOne(id, organizationId, userId);
    return this.mapToResponseDto(savedSearch);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a saved search' })
  @ApiResponse({ status: 200, description: 'The saved search has been successfully updated.', type: SavedSearchResponseDto })
  @ApiResponse({ status: 404, description: 'Saved search not found.' })
  async update(
    @Param('id') id: string,
    @Body() updateSavedSearchDto: UpdateSavedSearchDto,
    @Organization() organizationId: string,
    @User() userId: string,
  ) {
    this.logger.log(`Updating saved search with ID: ${id}`);
    const savedSearch = await this.savedSearchesService.update(id, updateSavedSearchDto, organizationId, userId);
    return this.mapToResponseDto(savedSearch);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a saved search' })
  @ApiResponse({ status: 204, description: 'The saved search has been successfully deleted.' })
  @ApiResponse({ status: 404, description: 'Saved search not found.' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(
    @Param('id') id: string,
    @Organization() organizationId: string,
    @User() userId: string,
  ) {
    this.logger.log(`Deleting saved search with ID: ${id}`);
    await this.savedSearchesService.remove(id, organizationId, userId);
  }

  @Post(':id/execute')
  @ApiOperation({ summary: 'Execute a saved search' })
  @ApiResponse({ status: 200, description: 'The saved search has been successfully executed.' })
  @ApiResponse({ status: 404, description: 'Saved search not found.' })
  async execute(
    @Param('id') id: string,
    @Organization() organizationId: string,
    @User() userId: string,
  ) {
    this.logger.log(`Executing saved search with ID: ${id}`);
    const results = await this.savedSearchesService.execute(id, organizationId, userId);
    return results;
  }

  @Post(':id/share')
  @ApiOperation({ summary: 'Share a saved search with users' })
  @ApiResponse({ status: 200, description: 'The saved search has been successfully shared.', type: SavedSearchResponseDto })
  @ApiResponse({ status: 404, description: 'Saved search not found.' })
  async share(
    @Param('id') id: string,
    @Body() shareDto: { userIds: string[] },
    @Organization() organizationId: string,
    @User() userId: string,
  ) {
    this.logger.log(`Sharing saved search ${id} with users: ${shareDto.userIds.join(', ')}`);
    const savedSearch = await this.savedSearchesService.share(id, shareDto.userIds, organizationId, userId);
    return this.mapToResponseDto(savedSearch);
  }

  @Post(':id/unshare')
  @ApiOperation({ summary: 'Unshare a saved search with users' })
  @ApiResponse({ status: 200, description: 'The saved search has been successfully unshared.', type: SavedSearchResponseDto })
  @ApiResponse({ status: 404, description: 'Saved search not found.' })
  async unshare(
    @Param('id') id: string,
    @Body() unshareDto: { userIds: string[] },
    @Organization() organizationId: string,
    @User() userId: string,
  ) {
    this.logger.log(`Unsharing saved search ${id} from users: ${unshareDto.userIds.join(', ')}`);
    const savedSearch = await this.savedSearchesService.unshare(id, unshareDto.userIds, organizationId, userId);
    return this.mapToResponseDto(savedSearch);
  }

  /**
   * Map a SavedSearch entity to a SavedSearchResponseDto
   */
  private mapToResponseDto(savedSearch: any): SavedSearchResponseDto {
    return {
      id: savedSearch._id.toString(),
      name: savedSearch.name,
      description: savedSearch.description,
      criteria: savedSearch.criteria,
      organizationId: savedSearch.organizationId,
      createdBy: savedSearch.createdBy,
      notificationsEnabled: savedSearch.notificationsEnabled,
      notificationFrequency: savedSearch.notificationFrequency,
      sharedWith: savedSearch.sharedWith,
      lastExecuted: savedSearch.lastExecuted,
      lastResultCount: savedSearch.lastResultCount,
      createdAt: savedSearch.createdAt,
      updatedAt: savedSearch.updatedAt,
    };
  }
}

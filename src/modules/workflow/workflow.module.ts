import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import { AuthModule } from '../auth/auth.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { DocumentsModule } from '../documents/documents.module';

// Controllers
import { WorkflowController } from './controllers/workflow.controller';

// Services
import { WorkflowEngineService } from './services/workflow-engine.service';
import { TaskManagementService } from './services/task-management.service';
import { NotificationService } from './services/notification.service';
import { TenantContextService } from '../auth/tenant/tenant-context.service';

// Schemas
import {
  WorkflowTemplate,
  WorkflowTemplateSchema,
  WORKFLOW_TEMPLATE_MODEL,
} from './schemas/workflow-template.schema';
import {
  WorkflowInstance,
  WorkflowInstanceSchema,
  WORKFLOW_INSTANCE_MODEL,
} from './schemas/workflow-instance.schema';
import {
  Task,
  TaskSchema,
  TASK_MODEL,
} from './schemas/task.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: WORKFLOW_TEMPLATE_MODEL,
        schema: WorkflowTemplateSchema,
      },
      {
        name: WORKFLOW_INSTANCE_MODEL,
        schema: WorkflowInstanceSchema,
      },
      {
        name: TASK_MODEL,
        schema: TaskSchema,
      },
    ]),
    ConfigModule,
    forwardRef(() => AuthModule),
    forwardRef(() => SubscriptionModule),
    forwardRef(() => DocumentsModule),
  ],
  controllers: [WorkflowController],
  providers: [
    WorkflowEngineService,
    TaskManagementService,
    NotificationService,
    TenantContextService,
  ],
  exports: [
    WorkflowEngineService,
    TaskManagementService,
    NotificationService,
  ],
})
export class WorkflowModule {}

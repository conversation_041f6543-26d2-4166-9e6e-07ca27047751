import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RequireFeatures } from '../../subscription/decorators/require-features.decorator';
import { UseCredits } from '../../subscription/decorators/use-credits.decorator';
import { Organization } from '../../auth/decorators/organization.decorator';
import { User } from '../../auth/decorators/user.decorator';
import { WorkflowEngineService, CreateWorkflowInstanceDto, ExecuteStepDto } from '../services/workflow-engine.service';
import { TaskManagementService } from '../services/task-management.service';
import { WorkflowInstance } from '../schemas/workflow-instance.schema';
import { Task, TaskStatus } from '../schemas/task.schema';

@ApiTags('Workflow Management')
@ApiBearerAuth()
@Controller('workflow')
@UseGuards(JwtAuthGuard)
export class WorkflowController {
  private readonly logger = new Logger(WorkflowController.name);

  constructor(
    private readonly workflowEngineService: WorkflowEngineService,
    private readonly taskManagementService: TaskManagementService,
  ) {}

  @Post('instances')
  @RequireFeatures('workflow_management')
  @UseCredits('workflow_management', 1)
  @ApiOperation({ summary: 'Create a new workflow instance' })
  @ApiResponse({ status: 201, description: 'Workflow instance created successfully' })
  async createWorkflowInstance(
    @Body() createDto: CreateWorkflowInstanceDto,
    @User() user: any,
  ): Promise<WorkflowInstance> {
    this.logger.log(`Creating workflow instance from template ${createDto.templateId}`);

    const userId = typeof user === 'string' ? user : user.userId || user.sub;

    return this.workflowEngineService.createWorkflowInstance(createDto, userId);
  }

  @Get('instances/:instanceId')
  @RequireFeatures('workflow_management')
  @ApiOperation({ summary: 'Get workflow instance details' })
  @ApiParam({ name: 'instanceId', description: 'Workflow instance ID' })
  @ApiResponse({ status: 200, description: 'Workflow instance retrieved successfully' })
  async getWorkflowInstance(
    @Param('instanceId') instanceId: string,
  ): Promise<WorkflowInstance> {
    return this.workflowEngineService.getWorkflowInstance(instanceId);
  }

  @Post('instances/:instanceId/steps/:stepId/execute')
  @RequireFeatures('workflow_management')
  @ApiOperation({ summary: 'Execute a workflow step' })
  @ApiParam({ name: 'instanceId', description: 'Workflow instance ID' })
  @ApiParam({ name: 'stepId', description: 'Step ID' })
  @ApiResponse({ status: 200, description: 'Step executed successfully' })
  async executeStep(
    @Param('instanceId') instanceId: string,
    @Param('stepId') stepId: string,
    @Body() executeDto: ExecuteStepDto,
    @User() user: any,
  ): Promise<{ success: boolean; message: string }> {
    const userId = typeof user === 'string' ? user : user.userId || user.sub;

    await this.workflowEngineService.executeStep(instanceId, stepId, executeDto, userId);

    return {
      success: true,
      message: 'Step executed successfully',
    };
  }

  @Post('instances/:instanceId/pause')
  @RequireFeatures('workflow_management')
  @ApiOperation({ summary: 'Pause a workflow instance' })
  @ApiParam({ name: 'instanceId', description: 'Workflow instance ID' })
  @ApiResponse({ status: 200, description: 'Workflow paused successfully' })
  async pauseWorkflow(
    @Param('instanceId') instanceId: string,
    @Body() body: { reason: string },
    @User() user: any,
  ): Promise<{ success: boolean; message: string }> {
    const userId = typeof user === 'string' ? user : user.userId || user.sub;

    await this.workflowEngineService.pauseWorkflow(instanceId, body.reason, userId);

    return {
      success: true,
      message: 'Workflow paused successfully',
    };
  }

  @Get('instances')
  @RequireFeatures('workflow_management')
  @ApiOperation({ summary: 'Get workflow instances' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by status' })
  @ApiQuery({ name: 'documentId', required: false, description: 'Filter by document ID' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limit number of results' })
  @ApiQuery({ name: 'offset', required: false, description: 'Offset for pagination' })
  @ApiResponse({ status: 200, description: 'Workflow instances retrieved successfully' })
  async getWorkflowInstances(
    @User() user: any,
    @Organization() organizationId: string,
    @Query('status') status?: string,
    @Query('documentId') documentId?: string,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ): Promise<{
    instances: WorkflowInstance[];
    total: number;
    hasMore: boolean;
  }> {
    // This would be implemented in the workflow engine service
    // For now, return empty result
    return {
      instances: [],
      total: 0,
      hasMore: false,
    };
  }

  @Get('tasks')
  @RequireFeatures('workflow_management')
  @ApiOperation({ summary: 'Get user tasks' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by task status' })
  @ApiQuery({ name: 'priority', required: false, description: 'Filter by priority' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limit number of results' })
  @ApiQuery({ name: 'offset', required: false, description: 'Offset for pagination' })
  @ApiResponse({ status: 200, description: 'Tasks retrieved successfully' })
  async getUserTasks(
    @User() user: any,
    @Query('status') status?: TaskStatus,
    @Query('priority') priority?: string,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ): Promise<Task[]> {
    const userId = typeof user === 'string' ? user : user.userId || user.sub;

    const tasks = await this.taskManagementService.getTasks(
      { assignedTo: userId, status: status ? [status] : undefined },
      50,
      0
    );
    return tasks.tasks;
  }

  @Get('tasks/:taskId')
  @RequireFeatures('workflow_management')
  @ApiOperation({ summary: 'Get task details' })
  @ApiParam({ name: 'taskId', description: 'Task ID' })
  @ApiResponse({ status: 200, description: 'Task retrieved successfully' })
  async getTask(
    @Param('taskId') taskId: string,
  ): Promise<Task> {
    return this.taskManagementService.getTask(taskId);
  }

  @Post('tasks/:taskId/complete')
  @RequireFeatures('workflow_management')
  @ApiOperation({ summary: 'Complete a task' })
  @ApiParam({ name: 'taskId', description: 'Task ID' })
  @ApiResponse({ status: 200, description: 'Task completed successfully' })
  async completeTask(
    @Param('taskId') taskId: string,
    @Body() body: { result: string; reason?: string; feedback?: string },
    @User() user: any,
  ): Promise<{ success: boolean; message: string }> {
    const userId = typeof user === 'string' ? user : user.userId || user.sub;

    await this.taskManagementService.completeTask(taskId, {
      result: body.result as any,
      reason: body.reason,
      feedback: body.feedback,
    }, userId);

    return {
      success: true,
      message: 'Task completed successfully',
    };
  }

  @Get('dashboard/stats')
  @RequireFeatures('workflow_management')
  @ApiOperation({ summary: 'Get workflow dashboard statistics' })
  @ApiResponse({ status: 200, description: 'Dashboard statistics retrieved successfully' })
  async getDashboardStats(
    @User() user: any,
    @Organization() organizationId: string,
  ): Promise<{
    activeWorkflows: number;
    pendingTasks: number;
    overdueTasks: number;
    completedThisWeek: number;
    myTasks: {
      pending: number;
      inProgress: number;
      overdue: number;
    };
  }> {
    // This would be implemented with actual statistics
    // For now, return mock data
    return {
      activeWorkflows: 0,
      pendingTasks: 0,
      overdueTasks: 0,
      completedThisWeek: 0,
      myTasks: {
        pending: 0,
        inProgress: 0,
        overdue: 0,
      },
    };
  }
}

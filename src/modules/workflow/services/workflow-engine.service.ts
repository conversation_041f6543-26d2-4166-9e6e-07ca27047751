import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  WorkflowTemplate,
  WorkflowTemplateDocument,
  WORKFLOW_TEMPLATE_MODEL,
  WorkflowStep,
} from '../schemas/workflow-template.schema';
import {
  WorkflowInstance,
  WorkflowInstanceDocument,
  WORKFLOW_INSTANCE_MODEL,
  WorkflowStatus,
  StepInstance,
  StepInstanceStatus,
} from '../schemas/workflow-instance.schema';
import { TaskManagementService } from './task-management.service';
import { NotificationService } from './notification.service';
import { TenantContextService } from '../../auth/tenant/tenant-context.service';

export interface CreateWorkflowInstanceDto {
  templateId: string;
  documentId: string;
  context?: {
    documentTitle?: string;
    documentType?: string;
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    tags?: string[];
    customFields?: Record<string, any>;
  };
  settings?: {
    allowComments?: boolean;
    allowAttachments?: boolean;
    requireCommentOnReject?: boolean;
    autoAdvanceOnApproval?: boolean;
    notifyOnEachStep?: boolean;
  };
}

export interface ExecuteStepDto {
  action: 'approve' | 'reject' | 'request_changes' | 'complete' | 'skip';
  reason?: string;
  comments?: string;
  attachments?: {
    fileName: string;
    fileUrl: string;
  }[];
}

@Injectable()
export class WorkflowEngineService {
  private readonly logger = new Logger(WorkflowEngineService.name);

  constructor(
    @InjectModel(WORKFLOW_TEMPLATE_MODEL)
    private readonly templateModel: Model<WorkflowTemplateDocument>,
    @InjectModel(WORKFLOW_INSTANCE_MODEL)
    private readonly instanceModel: Model<WorkflowInstanceDocument>,
    private readonly taskManagementService: TaskManagementService,
    private readonly notificationService: NotificationService,
    private readonly tenantContext: TenantContextService,
  ) {}

  /**
   * Create a new workflow instance from a template
   */
  async createWorkflowInstance(
    dto: CreateWorkflowInstanceDto,
    initiatedBy: string,
  ): Promise<WorkflowInstance> {
    const organizationId = this.tenantContext.getCurrentOrganization();
    
    this.logger.log(`Creating workflow instance from template ${dto.templateId}`);

    // Get the workflow template
    const template = await this.templateModel.findOne({
      _id: dto.templateId,
      organizationId,
      isActive: true,
    }).exec();

    if (!template) {
      throw new NotFoundException(`Workflow template not found: ${dto.templateId}`);
    }

    // Create step instances from template steps
    const stepInstances: StepInstance[] = template.steps.map((step, index) => ({
      id: `step_${index}_${Date.now()}`,
      templateStepId: step.id,
      name: step.name,
      type: step.type,
      status: index === 0 ? StepInstanceStatus.PENDING : StepInstanceStatus.PENDING,
      assignedTo: step.assignees,
      comments: [],
      attachments: [],
      escalations: [],
      metadata: {},
    }));

    // Calculate due date based on template settings
    const dueDate = new Date();
    dueDate.setHours(dueDate.getHours() + (template.settings?.maxDurationHours || 168)); // Default 1 week

    // Create workflow instance
    const instance = new this.instanceModel({
      templateId: dto.templateId,
      documentId: dto.documentId,
      organizationId,
      initiatedBy,
      status: WorkflowStatus.PENDING,
      workflowName: template.name,
      steps: stepInstances,
      currentStepIndex: 0,
      dueDate,
      context: {
        documentTitle: dto.context?.documentTitle || 'Untitled Document',
        documentType: dto.context?.documentType || 'document',
        priority: dto.context?.priority || 'medium',
        tags: dto.context?.tags || [],
        customFields: dto.context?.customFields || {},
      },
      metrics: {
        totalSteps: stepInstances.length,
        completedSteps: 0,
        skippedSteps: 0,
        rejectedSteps: 0,
        averageStepDuration: 0,
        totalDuration: 0,
        escalationCount: 0,
        commentCount: 0,
      },
      notifications: [],
      auditLog: [{
        id: `audit_${Date.now()}`,
        action: 'workflow_created',
        userId: initiatedBy,
        timestamp: new Date(),
        details: {
          templateId: dto.templateId,
          templateName: template.name,
        },
      }],
      settings: {
        allowComments: dto.settings?.allowComments ?? true,
        allowAttachments: dto.settings?.allowAttachments ?? true,
        requireCommentOnReject: dto.settings?.requireCommentOnReject ?? false,
        autoAdvanceOnApproval: dto.settings?.autoAdvanceOnApproval ?? true,
        notifyOnEachStep: dto.settings?.notifyOnEachStep ?? true,
      },
      integrations: {},
    });

    const savedInstance = await instance.save();

    // Start the workflow
    await this.startWorkflow(savedInstance._id.toString(), initiatedBy);

    return savedInstance;
  }

  /**
   * Start a workflow instance
   */
  async startWorkflow(instanceId: string, userId: string): Promise<void> {
    const instance = await this.getWorkflowInstance(instanceId);

    if (instance.status !== WorkflowStatus.PENDING) {
      throw new BadRequestException('Workflow is not in pending status');
    }

    // Update instance status
    await this.instanceModel.updateOne(
      { _id: instanceId },
      {
        $set: {
          status: WorkflowStatus.IN_PROGRESS,
          startedAt: new Date(),
        },
        $push: {
          auditLog: {
            id: `audit_${Date.now()}`,
            action: 'workflow_started',
            userId,
            timestamp: new Date(),
            details: {},
          },
        },
      },
    );

    // Start the first step
    await this.startNextStep(instanceId);

    this.logger.log(`Workflow ${instanceId} started by user ${userId}`);
  }

  /**
   * Execute a workflow step
   */
  async executeStep(
    instanceId: string,
    stepId: string,
    dto: ExecuteStepDto,
    userId: string,
  ): Promise<void> {
    const instance = await this.getWorkflowInstance(instanceId);
    
    // Find the step
    const stepIndex = instance.steps.findIndex(s => s.id === stepId);
    if (stepIndex === -1) {
      throw new NotFoundException(`Step not found: ${stepId}`);
    }

    const step = instance.steps[stepIndex];

    // Validate user can execute this step
    if (!step.assignedTo.includes(userId)) {
      throw new BadRequestException('User is not assigned to this step');
    }

    // Validate step is in correct status
    if (step.status !== StepInstanceStatus.IN_PROGRESS && step.status !== StepInstanceStatus.PENDING) {
      throw new BadRequestException('Step is not in a state that can be executed');
    }

    // Update step based on action
    const now = new Date();
    let newStepStatus: StepInstanceStatus;
    
    switch (dto.action) {
      case 'approve':
      case 'complete':
        newStepStatus = StepInstanceStatus.COMPLETED;
        break;
      case 'reject':
        newStepStatus = StepInstanceStatus.REJECTED;
        break;
      case 'skip':
        newStepStatus = StepInstanceStatus.SKIPPED;
        break;
      default:
        throw new BadRequestException(`Invalid action: ${dto.action}`);
    }

    // Update the step
    const updateData: any = {
      [`steps.${stepIndex}.status`]: newStepStatus,
      [`steps.${stepIndex}.completedAt`]: now,
      [`steps.${stepIndex}.actualAssignee`]: userId,
    };

    if (dto.reason || dto.comments) {
      updateData[`steps.${stepIndex}.decision`] = {
        action: dto.action,
        reason: dto.reason || dto.comments || '',
        timestamp: now,
        userId,
      };
    }

    if (dto.comments) {
      updateData.$push = {
        [`steps.${stepIndex}.comments`]: {
          id: `comment_${Date.now()}`,
          userId,
          content: dto.comments,
          timestamp: now,
          isInternal: false,
        },
      };
    }

    await this.instanceModel.updateOne({ _id: instanceId }, updateData);

    // Add audit log entry
    await this.addAuditLogEntry(instanceId, 'step_executed', userId, {
      stepId,
      stepName: step.name,
      action: dto.action,
      reason: dto.reason,
    });

    // Update metrics
    await this.updateWorkflowMetrics(instanceId);

    // Handle step completion
    if (newStepStatus === StepInstanceStatus.COMPLETED || newStepStatus === StepInstanceStatus.SKIPPED) {
      await this.handleStepCompletion(instanceId, stepIndex);
    } else if (newStepStatus === StepInstanceStatus.REJECTED) {
      await this.handleStepRejection(instanceId, stepIndex);
    }

    this.logger.log(`Step ${stepId} executed by user ${userId} with action ${dto.action}`);
  }

  /**
   * Get workflow instance
   */
  async getWorkflowInstance(instanceId: string): Promise<WorkflowInstance> {
    const organizationId = this.tenantContext.getCurrentOrganization();
    
    const instance = await this.instanceModel.findOne({
      _id: instanceId,
      organizationId,
    }).exec();

    if (!instance) {
      throw new NotFoundException(`Workflow instance not found: ${instanceId}`);
    }

    return instance;
  }

  /**
   * Start the next step in the workflow
   */
  private async startNextStep(instanceId: string): Promise<void> {
    const instance = await this.getWorkflowInstance(instanceId);
    
    // Find next pending step
    const nextStepIndex = instance.steps.findIndex(
      step => step.status === StepInstanceStatus.PENDING
    );

    if (nextStepIndex === -1) {
      // No more steps, complete workflow
      await this.completeWorkflow(instanceId);
      return;
    }

    const nextStep = instance.steps[nextStepIndex];

    // Update step status and create tasks
    await this.instanceModel.updateOne(
      { _id: instanceId },
      {
        $set: {
          currentStepIndex: nextStepIndex,
          [`steps.${nextStepIndex}.status`]: StepInstanceStatus.IN_PROGRESS,
          [`steps.${nextStepIndex}.startedAt`]: new Date(),
        },
      },
    );

    // Create tasks for assignees
    for (const assigneeId of nextStep.assignedTo) {
      await this.taskManagementService.createTask({
        title: `${nextStep.name} - ${instance.context.documentTitle}`,
        description: `Please ${nextStep.type} the document: ${instance.context.documentTitle}`,
        type: nextStep.type as any,
        assignedTo: assigneeId,
        workflowInstanceId: instanceId,
        documentId: instance.documentId,
        dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
        context: {
          documentTitle: instance.context.documentTitle,
          workflowName: instance.workflowName,
          stepName: nextStep.name,
        },
      }, instance.initiatedBy);
    }

    // Send notifications
    if (instance.settings.notifyOnEachStep) {
      await this.notificationService.notifyStepAssignees(instanceId, nextStep.id);
    }

    this.logger.log(`Started step ${nextStep.id} in workflow ${instanceId}`);
  }

  /**
   * Handle step completion
   */
  private async handleStepCompletion(instanceId: string, stepIndex: number): Promise<void> {
    const instance = await this.getWorkflowInstance(instanceId);
    
    // Check if this was the last step
    const remainingSteps = instance.steps.filter(
      step => step.status === StepInstanceStatus.PENDING
    );

    if (remainingSteps.length === 0) {
      await this.completeWorkflow(instanceId);
    } else {
      // Start next step
      await this.startNextStep(instanceId);
    }
  }

  /**
   * Handle step rejection
   */
  private async handleStepRejection(instanceId: string, stepIndex: number): Promise<void> {
    // For now, pause the workflow on rejection
    // In a more sophisticated system, you might have rejection handling rules
    await this.pauseWorkflow(instanceId, 'Step rejected');
  }

  /**
   * Complete workflow
   */
  private async completeWorkflow(instanceId: string): Promise<void> {
    await this.instanceModel.updateOne(
      { _id: instanceId },
      {
        $set: {
          status: WorkflowStatus.COMPLETED,
          completedAt: new Date(),
        },
      },
    );

    await this.addAuditLogEntry(instanceId, 'workflow_completed', 'system', {});
    
    this.logger.log(`Workflow ${instanceId} completed`);
  }

  /**
   * Pause workflow
   */
  async pauseWorkflow(instanceId: string, reason: string, userId?: string): Promise<void> {
    await this.instanceModel.updateOne(
      { _id: instanceId },
      {
        $set: {
          status: WorkflowStatus.PAUSED,
          pausedAt: new Date(),
          pausedBy: userId || 'system',
          pauseReason: reason,
        },
      },
    );

    await this.addAuditLogEntry(instanceId, 'workflow_paused', userId || 'system', { reason });
  }

  /**
   * Add audit log entry
   */
  private async addAuditLogEntry(
    instanceId: string,
    action: string,
    userId: string,
    details: Record<string, any>,
  ): Promise<void> {
    await this.instanceModel.updateOne(
      { _id: instanceId },
      {
        $push: {
          auditLog: {
            id: `audit_${Date.now()}`,
            action,
            userId,
            timestamp: new Date(),
            details,
          },
        },
      },
    );
  }

  /**
   * Update workflow metrics
   */
  private async updateWorkflowMetrics(instanceId: string): Promise<void> {
    const instance = await this.getWorkflowInstance(instanceId);
    
    const completedSteps = instance.steps.filter(s => s.status === StepInstanceStatus.COMPLETED).length;
    const skippedSteps = instance.steps.filter(s => s.status === StepInstanceStatus.SKIPPED).length;
    const rejectedSteps = instance.steps.filter(s => s.status === StepInstanceStatus.REJECTED).length;

    await this.instanceModel.updateOne(
      { _id: instanceId },
      {
        $set: {
          'metrics.completedSteps': completedSteps,
          'metrics.skippedSteps': skippedSteps,
          'metrics.rejectedSteps': rejectedSteps,
        },
      },
    );
  }
}

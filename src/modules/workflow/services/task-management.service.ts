import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  Task,
  TaskDocument,
  TASK_MODEL,
  TaskType,
  TaskStatus,
  TaskPriority,
} from '../schemas/task.schema';
import { TenantContextService } from '../../auth/tenant/tenant-context.service';

export interface CreateTaskDto {
  title: string;
  description: string;
  type: TaskType;
  assignedTo: string;
  workflowInstanceId?: string;
  documentId?: string;
  collaborationSessionId?: string;
  dueDate: Date;
  priority?: TaskPriority;
  estimatedHours?: number;
  tags?: string[];
  context?: {
    documentTitle?: string;
    workflowName?: string;
    stepName?: string;
    customData?: Record<string, any>;
  };
}

export interface UpdateTaskDto {
  title?: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  dueDate?: Date;
  estimatedHours?: number;
  tags?: string[];
}

export interface CompleteTaskDto {
  result: 'approved' | 'rejected' | 'completed' | 'cancelled';
  reason?: string;
  feedback?: string;
  rating?: number;
  timeSpent?: number;
}

export interface TaskFilters {
  status?: TaskStatus[];
  priority?: TaskPriority[];
  type?: TaskType[];
  assignedTo?: string;
  assignedBy?: string;
  workflowInstanceId?: string;
  documentId?: string;
  dueDateFrom?: Date;
  dueDateTo?: Date;
  tags?: string[];
}

@Injectable()
export class TaskManagementService {
  private readonly logger = new Logger(TaskManagementService.name);

  constructor(
    @InjectModel(TASK_MODEL)
    private readonly taskModel: Model<TaskDocument>,
    private readonly tenantContext: TenantContextService,
  ) {}

  /**
   * Create a new task
   */
  async createTask(dto: CreateTaskDto, assignedBy: string): Promise<Task> {
    const organizationId = this.tenantContext.getCurrentOrganization();

    this.logger.log(`Creating task: ${dto.title} for user ${dto.assignedTo}`);

    const task = new this.taskModel({
      title: dto.title,
      description: dto.description,
      type: dto.type,
      status: TaskStatus.PENDING,
      priority: dto.priority || TaskPriority.MEDIUM,
      organizationId,
      assignedTo: dto.assignedTo,
      assignedBy,
      workflowInstanceId: dto.workflowInstanceId,
      documentId: dto.documentId,
      collaborationSessionId: dto.collaborationSessionId,
      dueDate: dto.dueDate,
      estimatedHours: dto.estimatedHours,
      tags: dto.tags || [],
      context: dto.context || {},
      comments: [],
      attachments: [],
      reminders: [],
      dependencies: [],
      notifications: {
        onAssignment: true,
        onDueDate: true,
        onOverdue: true,
        onCompletion: true,
        onDelegation: true,
        customReminders: [24, 4, 1], // 24h, 4h, 1h before due date
      },
      analytics: {
        viewCount: 0,
        lastViewed: new Date(),
        timeInStatus: {},
        collaborators: [assignedBy, dto.assignedTo],
      },
    });

    const savedTask = await task.save();

    // TODO: Send notification to assignee
    this.logger.log(`Task created with ID: ${savedTask._id}`);

    return savedTask;
  }

  /**
   * Get task by ID
   */
  async getTask(taskId: string): Promise<Task> {
    const organizationId = this.tenantContext.getCurrentOrganization();

    const task = await this.taskModel.findOne({
      _id: taskId,
      organizationId,
    }).exec();

    if (!task) {
      throw new NotFoundException(`Task not found: ${taskId}`);
    }

    // Update analytics
    await this.taskModel.updateOne(
      { _id: taskId },
      {
        $inc: { 'analytics.viewCount': 1 },
        $set: { 'analytics.lastViewed': new Date() },
      },
    );

    return task;
  }

  /**
   * Update task
   */
  async updateTask(taskId: string, dto: UpdateTaskDto, userId: string): Promise<Task> {
    const task = await this.getTask(taskId);

    // Check permissions (only assignee, assigner, or admin can update)
    if (task.assignedTo !== userId && task.assignedBy !== userId) {
      throw new BadRequestException('Insufficient permissions to update task');
    }

    const updateData: any = { ...dto };

    // Track status changes
    if (dto.status && dto.status !== task.status) {
      const now = new Date();
      const timeInPreviousStatus = now.getTime() - ((task as any).updatedAt || (task as any).createdAt || new Date()).getTime();

      updateData[`analytics.timeInStatus.${task.status}`] =
        (task.analytics.timeInStatus[task.status] || 0) + timeInPreviousStatus;

      if (dto.status === TaskStatus.IN_PROGRESS && !task.startedAt) {
        updateData.startedAt = now;
      }
    }

    const updatedTask = await this.taskModel.findByIdAndUpdate(
      taskId,
      { $set: updateData },
      { new: true },
    ).exec();

    this.logger.log(`Task ${taskId} updated by user ${userId}`);

    return updatedTask!;
  }

  /**
   * Complete task
   */
  async completeTask(taskId: string, dto: CompleteTaskDto, userId: string): Promise<Task> {
    const task = await this.getTask(taskId);

    // Check permissions
    if (task.assignedTo !== userId) {
      throw new BadRequestException('Only the assignee can complete the task');
    }

    // Check task status
    if (task.status === TaskStatus.COMPLETED || task.status === TaskStatus.CANCELLED) {
      throw new BadRequestException('Task is already completed or cancelled');
    }

    const now = new Date();
    const updateData: any = {
      status: TaskStatus.COMPLETED,
      completedAt: now,
      completion: {
        result: dto.result,
        reason: dto.reason,
        feedback: dto.feedback,
        rating: dto.rating,
        completedBy: userId,
        completedAt: now,
        timeSpent: dto.timeSpent,
      },
    };

    // Calculate actual hours if time spent is provided
    if (dto.timeSpent) {
      updateData.actualHours = dto.timeSpent / 60; // Convert minutes to hours
    }

    const updatedTask = await this.taskModel.findByIdAndUpdate(
      taskId,
      { $set: updateData },
      { new: true },
    ).exec();

    this.logger.log(`Task ${taskId} completed by user ${userId} with result: ${dto.result}`);

    return updatedTask!;
  }

  /**
   * Delegate task
   */
  async delegateTask(
    taskId: string,
    delegatedTo: string,
    reason: string,
    userId: string,
    canRedelegate: boolean = false,
  ): Promise<Task> {
    const task = await this.getTask(taskId);

    // Check permissions
    if (task.assignedTo !== userId) {
      throw new BadRequestException('Only the assignee can delegate the task');
    }

    const now = new Date();
    const updateData = {
      assignedTo: delegatedTo,
      status: TaskStatus.DELEGATED,
      delegation: {
        delegatedTo,
        delegatedBy: userId,
        delegatedAt: now,
        reason,
        canRedelegate,
      },
      $push: {
        'analytics.collaborators': delegatedTo,
      },
    };

    const updatedTask = await this.taskModel.findByIdAndUpdate(
      taskId,
      updateData,
      { new: true },
    ).exec();

    this.logger.log(`Task ${taskId} delegated from ${userId} to ${delegatedTo}`);

    return updatedTask!;
  }

  /**
   * Add comment to task
   */
  async addComment(
    taskId: string,
    content: string,
    userId: string,
    isInternal: boolean = false,
    mentions: string[] = [],
  ): Promise<Task> {
    const task = await this.getTask(taskId);

    const comment = {
      id: `comment_${Date.now()}`,
      userId,
      content,
      timestamp: new Date(),
      isInternal,
      mentions,
    };

    const updatedTask = await this.taskModel.findByIdAndUpdate(
      taskId,
      {
        $push: { comments: comment },
        $addToSet: { 'analytics.collaborators': userId },
      },
      { new: true },
    ).exec();

    this.logger.log(`Comment added to task ${taskId} by user ${userId}`);

    return updatedTask!;
  }

  /**
   * Add attachment to task
   */
  async addAttachment(
    taskId: string,
    fileName: string,
    fileUrl: string,
    fileSize: number,
    mimeType: string,
    userId: string,
  ): Promise<Task> {
    const task = await this.getTask(taskId);

    const attachment = {
      id: `attachment_${Date.now()}`,
      fileName,
      fileUrl,
      fileSize,
      mimeType,
      uploadedBy: userId,
      uploadedAt: new Date(),
    };

    const updatedTask = await this.taskModel.findByIdAndUpdate(
      taskId,
      {
        $push: { attachments: attachment },
        $addToSet: { 'analytics.collaborators': userId },
      },
      { new: true },
    ).exec();

    this.logger.log(`Attachment added to task ${taskId} by user ${userId}`);

    return updatedTask!;
  }

  /**
   * Get tasks with filters
   */
  async getTasks(
    filters: TaskFilters,
    limit: number = 50,
    offset: number = 0,
    sortBy: string = 'dueDate',
    sortOrder: 'asc' | 'desc' = 'asc',
  ): Promise<{
    tasks: Task[];
    total: number;
    hasMore: boolean;
  }> {
    const organizationId = this.tenantContext.getCurrentOrganization();

    const query: any = { organizationId };

    // Apply filters
    if (filters.status?.length) {
      query.status = { $in: filters.status };
    }
    if (filters.priority?.length) {
      query.priority = { $in: filters.priority };
    }
    if (filters.type?.length) {
      query.type = { $in: filters.type };
    }
    if (filters.assignedTo) {
      query.assignedTo = filters.assignedTo;
    }
    if (filters.assignedBy) {
      query.assignedBy = filters.assignedBy;
    }
    if (filters.workflowInstanceId) {
      query.workflowInstanceId = filters.workflowInstanceId;
    }
    if (filters.documentId) {
      query.documentId = filters.documentId;
    }
    if (filters.dueDateFrom || filters.dueDateTo) {
      query.dueDate = {};
      if (filters.dueDateFrom) {
        query.dueDate.$gte = filters.dueDateFrom;
      }
      if (filters.dueDateTo) {
        query.dueDate.$lte = filters.dueDateTo;
      }
    }
    if (filters.tags?.length) {
      query.tags = { $in: filters.tags };
    }

    // Get total count
    const total = await this.taskModel.countDocuments(query);

    // Get tasks
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const tasks = await this.taskModel
      .find(query)
      .sort(sort)
      .skip(offset)
      .limit(limit)
      .exec();

    return {
      tasks,
      total,
      hasMore: offset + limit < total,
    };
  }

  /**
   * Get task statistics
   */
  async getTaskStatistics(userId?: string): Promise<{
    total: number;
    byStatus: Record<TaskStatus, number>;
    byPriority: Record<TaskPriority, number>;
    overdue: number;
    dueToday: number;
    dueTomorrow: number;
    averageCompletionTime: number;
  }> {
    const organizationId = this.tenantContext.getCurrentOrganization();

    const query: any = { organizationId };
    if (userId) {
      query.assignedTo = userId;
    }

    const tasks = await this.taskModel.find(query).exec();

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);

    const stats = {
      total: tasks.length,
      byStatus: {} as Record<TaskStatus, number>,
      byPriority: {} as Record<TaskPriority, number>,
      overdue: 0,
      dueToday: 0,
      dueTomorrow: 0,
      averageCompletionTime: 0,
    };

    // Initialize counters
    Object.values(TaskStatus).forEach(status => {
      stats.byStatus[status] = 0;
    });
    Object.values(TaskPriority).forEach(priority => {
      stats.byPriority[priority] = 0;
    });

    let totalCompletionTime = 0;
    let completedTasks = 0;

    tasks.forEach(task => {
      // Count by status
      stats.byStatus[task.status]++;

      // Count by priority
      stats.byPriority[task.priority]++;

      // Count overdue, due today, due tomorrow
      if (task.dueDate < now && task.status !== TaskStatus.COMPLETED) {
        stats.overdue++;
      } else if (task.dueDate >= today && task.dueDate < tomorrow) {
        stats.dueToday++;
      } else if (task.dueDate >= tomorrow && task.dueDate < new Date(tomorrow.getTime() + 24 * 60 * 60 * 1000)) {
        stats.dueTomorrow++;
      }

      // Calculate completion time
      if (task.status === TaskStatus.COMPLETED && task.startedAt && task.completedAt) {
        totalCompletionTime += task.completedAt.getTime() - task.startedAt.getTime();
        completedTasks++;
      }
    });

    // Calculate average completion time in hours
    if (completedTasks > 0) {
      stats.averageCompletionTime = totalCompletionTime / completedTasks / (1000 * 60 * 60);
    }

    return stats;
  }
}

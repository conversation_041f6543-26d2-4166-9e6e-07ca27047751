import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  WorkflowInstance,
  WorkflowInstanceDocument,
  WORKFLOW_INSTANCE_MODEL,
} from '../schemas/workflow-instance.schema';
import { TenantContextService } from '../../auth/tenant/tenant-context.service';

export interface NotificationData {
  type: 'email' | 'in_app' | 'webhook';
  recipient: string;
  subject: string;
  content: string;
  metadata?: Record<string, any>;
}

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(
    @InjectModel(WORKFLOW_INSTANCE_MODEL)
    private readonly instanceModel: Model<WorkflowInstanceDocument>,
    private readonly tenantContext: TenantContextService,
  ) {}

  /**
   * Notify step assignees when a step starts
   */
  async notifyStepAssignees(instanceId: string, stepId: string): Promise<void> {
    try {
      const instance = await this.instanceModel.findById(instanceId).exec();
      if (!instance) {
        this.logger.error(`Workflow instance not found: ${instanceId}`);
        return;
      }

      const step = instance.steps.find(s => s.id === stepId);
      if (!step) {
        this.logger.error(`Step not found: ${stepId}`);
        return;
      }

      // Send notifications to all assignees
      for (const assigneeId of step.assignedTo) {
        await this.sendNotification({
          type: 'in_app',
          recipient: assigneeId,
          subject: `New Task: ${step.name}`,
          content: `You have been assigned a new task: ${step.name} for document "${instance.context.documentTitle}". Please review and take action.`,
          metadata: {
            workflowInstanceId: instanceId,
            stepId,
            documentId: instance.documentId,
            priority: instance.context.priority,
          },
        });
      }

      this.logger.log(`Notifications sent for step ${stepId} in workflow ${instanceId}`);
    } catch (error) {
      this.logger.error(`Error sending step notifications: ${error.message}`);
    }
  }

  /**
   * Notify when workflow is completed
   */
  async notifyWorkflowCompletion(instanceId: string): Promise<void> {
    try {
      const instance = await this.instanceModel.findById(instanceId).exec();
      if (!instance) {
        return;
      }

      await this.sendNotification({
        type: 'in_app',
        recipient: instance.initiatedBy,
        subject: `Workflow Completed: ${instance.workflowName}`,
        content: `The workflow "${instance.workflowName}" for document "${instance.context.documentTitle}" has been completed successfully.`,
        metadata: {
          workflowInstanceId: instanceId,
          documentId: instance.documentId,
          completedAt: new Date(),
        },
      });

      this.logger.log(`Completion notification sent for workflow ${instanceId}`);
    } catch (error) {
      this.logger.error(`Error sending completion notification: ${error.message}`);
    }
  }

  /**
   * Notify about task assignment
   */
  async notifyTaskAssignment(
    assigneeId: string,
    taskTitle: string,
    taskDescription: string,
    dueDate: Date,
    metadata?: Record<string, any>,
  ): Promise<void> {
    try {
      await this.sendNotification({
        type: 'in_app',
        recipient: assigneeId,
        subject: `New Task Assigned: ${taskTitle}`,
        content: `You have been assigned a new task: ${taskTitle}. ${taskDescription}. Due date: ${dueDate.toLocaleDateString()}.`,
        metadata: {
          ...metadata,
          dueDate,
          notificationType: 'task_assignment',
        },
      });

      this.logger.log(`Task assignment notification sent to user ${assigneeId}`);
    } catch (error) {
      this.logger.error(`Error sending task assignment notification: ${error.message}`);
    }
  }

  /**
   * Notify about overdue tasks
   */
  async notifyOverdueTasks(userId: string, overdueTasks: any[]): Promise<void> {
    try {
      const taskList = overdueTasks.map(task => `- ${task.title} (Due: ${task.dueDate.toLocaleDateString()})`).join('\n');

      await this.sendNotification({
        type: 'in_app',
        recipient: userId,
        subject: `Overdue Tasks Reminder`,
        content: `You have ${overdueTasks.length} overdue task(s):\n\n${taskList}\n\nPlease review and complete them as soon as possible.`,
        metadata: {
          overdueCount: overdueTasks.length,
          notificationType: 'overdue_reminder',
        },
      });

      this.logger.log(`Overdue tasks notification sent to user ${userId}`);
    } catch (error) {
      this.logger.error(`Error sending overdue tasks notification: ${error.message}`);
    }
  }

  /**
   * Send a notification
   */
  private async sendNotification(data: NotificationData): Promise<void> {
    try {
      // For now, we'll just log the notification
      // In a real implementation, you would integrate with email services,
      // push notification services, webhooks, etc.
      
      this.logger.log(`Sending ${data.type} notification to ${data.recipient}: ${data.subject}`);
      
      // TODO: Implement actual notification sending based on type
      switch (data.type) {
        case 'email':
          await this.sendEmailNotification(data);
          break;
        case 'in_app':
          await this.sendInAppNotification(data);
          break;
        case 'webhook':
          await this.sendWebhookNotification(data);
          break;
      }
    } catch (error) {
      this.logger.error(`Error sending notification: ${error.message}`);
    }
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(data: NotificationData): Promise<void> {
    // TODO: Integrate with email service (SendGrid, AWS SES, etc.)
    this.logger.log(`Email notification would be sent to ${data.recipient}: ${data.subject}`);
  }

  /**
   * Send in-app notification
   */
  private async sendInAppNotification(data: NotificationData): Promise<void> {
    // TODO: Store in database for in-app notifications
    // This could be integrated with the existing notification system
    this.logger.log(`In-app notification would be sent to ${data.recipient}: ${data.subject}`);
  }

  /**
   * Send webhook notification
   */
  private async sendWebhookNotification(data: NotificationData): Promise<void> {
    // TODO: Send HTTP POST to webhook URL
    this.logger.log(`Webhook notification would be sent to ${data.recipient}: ${data.subject}`);
  }

  /**
   * Schedule reminder notifications
   */
  async scheduleReminders(taskId: string, dueDate: Date, assigneeId: string): Promise<void> {
    try {
      const reminderTimes = [24, 4, 1]; // Hours before due date
      
      for (const hours of reminderTimes) {
        const reminderTime = new Date(dueDate.getTime() - hours * 60 * 60 * 1000);
        
        if (reminderTime > new Date()) {
          // TODO: Schedule reminder using a job queue (Bull, Agenda, etc.)
          this.logger.log(`Reminder scheduled for ${reminderTime.toISOString()} (${hours}h before due date)`);
        }
      }
    } catch (error) {
      this.logger.error(`Error scheduling reminders: ${error.message}`);
    }
  }

  /**
   * Send escalation notification
   */
  async notifyEscalation(
    escalatedTo: string[],
    taskTitle: string,
    originalAssignee: string,
    reason: string,
  ): Promise<void> {
    try {
      for (const userId of escalatedTo) {
        await this.sendNotification({
          type: 'in_app',
          recipient: userId,
          subject: `Task Escalated: ${taskTitle}`,
          content: `A task has been escalated to you: "${taskTitle}". Original assignee: ${originalAssignee}. Reason: ${reason}`,
          metadata: {
            notificationType: 'escalation',
            originalAssignee,
            reason,
          },
        });
      }

      this.logger.log(`Escalation notifications sent for task: ${taskTitle}`);
    } catch (error) {
      this.logger.error(`Error sending escalation notifications: ${error.message}`);
    }
  }

  /**
   * Notify about collaboration session events
   */
  async notifyCollaborationEvent(
    sessionId: string,
    eventType: 'user_joined' | 'user_left' | 'session_ended',
    userId: string,
    participants: string[],
  ): Promise<void> {
    try {
      const messages = {
        user_joined: `User has joined the collaboration session`,
        user_left: `User has left the collaboration session`,
        session_ended: `Collaboration session has ended`,
      };

      for (const participantId of participants) {
        if (participantId !== userId) {
          await this.sendNotification({
            type: 'in_app',
            recipient: participantId,
            subject: `Collaboration Update`,
            content: messages[eventType],
            metadata: {
              sessionId,
              eventType,
              userId,
              notificationType: 'collaboration_event',
            },
          });
        }
      }

      this.logger.log(`Collaboration event notifications sent for session ${sessionId}`);
    } catch (error) {
      this.logger.error(`Error sending collaboration event notifications: ${error.message}`);
    }
  }
}

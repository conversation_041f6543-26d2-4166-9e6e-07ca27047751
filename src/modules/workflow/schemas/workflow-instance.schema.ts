import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type WorkflowInstanceDocument = WorkflowInstance & Document;

export enum WorkflowStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  FAILED = 'failed',
  PAUSED = 'paused',
}

export enum StepInstanceStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  SKIPPED = 'skipped',
  REJECTED = 'rejected',
  TIMEOUT = 'timeout',
}

export interface StepInstance {
  id: string;
  templateStepId: string;
  name: string;
  type: string;
  status: StepInstanceStatus;
  assignedTo: string[];
  actualAssignee?: string; // Who actually completed it
  startedAt?: Date;
  completedAt?: Date;
  dueDate?: Date;
  timeoutAt?: Date;
  comments: {
    id: string;
    userId: string;
    content: string;
    timestamp: Date;
    isInternal: boolean;
  }[];
  attachments: {
    id: string;
    fileName: string;
    fileUrl: string;
    uploadedBy: string;
    uploadedAt: Date;
  }[];
  decision?: {
    action: 'approve' | 'reject' | 'request_changes';
    reason: string;
    timestamp: Date;
    userId: string;
  };
  escalations: {
    escalatedAt: Date;
    escalatedTo: string[];
    reason: string;
    resolvedAt?: Date;
  }[];
  metadata: Record<string, any>;
}

@Schema({ timestamps: true })
export class WorkflowInstance extends Document {
  @Prop({ required: true, type: MongooseSchema.Types.ObjectId, ref: 'WorkflowTemplate' })
  templateId: string;

  @Prop({ required: true, type: MongooseSchema.Types.ObjectId, ref: 'Document' })
  documentId: string;

  @Prop({ required: true })
  organizationId: string;

  @Prop({ required: true })
  initiatedBy: string;

  @Prop({ required: true, enum: WorkflowStatus, default: WorkflowStatus.PENDING })
  status: WorkflowStatus;

  @Prop({ required: true })
  workflowName: string;

  @Prop({ type: [Object], required: true })
  steps: StepInstance[];

  @Prop({ required: true, default: 0 })
  currentStepIndex: number;

  @Prop()
  startedAt: Date;

  @Prop()
  completedAt: Date;

  @Prop()
  dueDate: Date;

  @Prop()
  pausedAt: Date;

  @Prop()
  pausedBy: string;

  @Prop()
  pauseReason: string;

  @Prop()
  cancelledAt: Date;

  @Prop()
  cancelledBy: string;

  @Prop()
  cancellationReason: string;

  @Prop({ type: Object })
  context: {
    documentTitle: string;
    documentType: string;
    priority: 'low' | 'medium' | 'high' | 'urgent';
    tags: string[];
    customFields: Record<string, any>;
  };

  @Prop({ type: Object })
  metrics: {
    totalSteps: number;
    completedSteps: number;
    skippedSteps: number;
    rejectedSteps: number;
    averageStepDuration: number;
    totalDuration: number;
    escalationCount: number;
    commentCount: number;
  };

  @Prop({ type: [Object] })
  notifications: {
    id: string;
    type: 'email' | 'in_app' | 'webhook';
    recipient: string;
    subject: string;
    content: string;
    sentAt: Date;
    status: 'sent' | 'failed' | 'pending';
  }[];

  @Prop({ type: [Object] })
  auditLog: {
    id: string;
    action: string;
    userId: string;
    timestamp: Date;
    details: Record<string, any>;
    ipAddress?: string;
    userAgent?: string;
  }[];

  @Prop({ type: Object })
  settings: {
    allowComments: boolean;
    allowAttachments: boolean;
    requireCommentOnReject: boolean;
    autoAdvanceOnApproval: boolean;
    notifyOnEachStep: boolean;
  };

  @Prop({ type: Object })
  integrations: {
    slackChannelId?: string;
    teamsChannelId?: string;
    webhookUrls?: string[];
    externalSystemIds?: Record<string, string>;
  };
}

export const WorkflowInstanceSchema = SchemaFactory.createForClass(WorkflowInstance);

// Indexes for performance
WorkflowInstanceSchema.index({ organizationId: 1, status: 1 });
WorkflowInstanceSchema.index({ documentId: 1 });
WorkflowInstanceSchema.index({ templateId: 1 });
WorkflowInstanceSchema.index({ initiatedBy: 1 });
WorkflowInstanceSchema.index({ 'steps.assignedTo': 1 });
WorkflowInstanceSchema.index({ dueDate: 1 });
WorkflowInstanceSchema.index({ createdAt: -1 });

export const WORKFLOW_INSTANCE_MODEL = 'WorkflowInstance';

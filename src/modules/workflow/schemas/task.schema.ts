import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type TaskDocument = Task & Document;

export enum TaskType {
  REVIEW = 'review',
  APPROVAL = 'approval',
  COMMENT = 'comment',
  EDIT = 'edit',
  SIGN = 'sign',
  NOTIFY = 'notify',
  CUSTOM = 'custom',
}

export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  OVERDUE = 'overdue',
  CANCELLED = 'cancelled',
  DELEGATED = 'delegated',
}

export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

@Schema({ timestamps: true })
export class Task extends Document {
  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true, enum: TaskType })
  type: TaskType;

  @Prop({ required: true, enum: TaskStatus, default: TaskStatus.PENDING })
  status: TaskStatus;

  @Prop({ required: true, enum: TaskPriority, default: TaskPriority.MEDIUM })
  priority: TaskPriority;

  @Prop({ required: true })
  organizationId: string;

  @Prop({ required: true })
  assignedTo: string;

  @Prop({ required: true })
  assignedBy: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'WorkflowInstance' })
  workflowInstanceId?: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Document' })
  documentId?: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'CollaborationSession' })
  collaborationSessionId?: string;

  @Prop({ required: true })
  dueDate: Date;

  @Prop()
  startedAt: Date;

  @Prop()
  completedAt: Date;

  @Prop()
  estimatedHours: number;

  @Prop()
  actualHours: number;

  @Prop({ type: [String] })
  tags: string[];

  @Prop({ type: Object })
  context: {
    documentTitle?: string;
    workflowName?: string;
    stepName?: string;
    customData?: Record<string, any>;
  };

  @Prop({ type: [Object] })
  comments: {
    id: string;
    userId: string;
    content: string;
    timestamp: Date;
    isInternal: boolean;
    mentions: string[]; // User IDs mentioned in the comment
  }[];

  @Prop({ type: [Object] })
  attachments: {
    id: string;
    fileName: string;
    fileUrl: string;
    fileSize: number;
    mimeType: string;
    uploadedBy: string;
    uploadedAt: Date;
  }[];

  @Prop({ type: Object })
  completion: {
    result: 'approved' | 'rejected' | 'completed' | 'cancelled';
    reason?: string;
    feedback?: string;
    rating?: number; // 1-5 star rating
    completedBy: string;
    completedAt: Date;
    timeSpent?: number; // in minutes
  };

  @Prop({ type: Object })
  delegation: {
    delegatedTo: string;
    delegatedBy: string;
    delegatedAt: Date;
    reason: string;
    canRedelegate: boolean;
  };

  @Prop({ type: [Object] })
  reminders: {
    id: string;
    type: 'email' | 'in_app' | 'sms';
    scheduledAt: Date;
    sentAt?: Date;
    status: 'pending' | 'sent' | 'failed';
  }[];

  @Prop({ type: Object })
  escalation: {
    escalatedTo: string[];
    escalatedAt: Date;
    escalatedBy: string;
    reason: string;
    autoEscalation: boolean;
    resolvedAt?: Date;
  };

  @Prop({ type: [Object] })
  dependencies: {
    taskId: string;
    type: 'blocks' | 'blocked_by' | 'related';
    description?: string;
  }[];

  @Prop({ type: Object })
  recurrence: {
    enabled: boolean;
    pattern: 'daily' | 'weekly' | 'monthly' | 'custom';
    interval: number;
    endDate?: Date;
    nextDueDate?: Date;
  };

  @Prop({ type: Object })
  notifications: {
    onAssignment: boolean;
    onDueDate: boolean;
    onOverdue: boolean;
    onCompletion: boolean;
    onDelegation: boolean;
    customReminders: number[]; // Hours before due date
  };

  @Prop({ type: Object })
  analytics: {
    viewCount: number;
    lastViewed: Date;
    timeInStatus: Record<string, number>; // Status -> minutes
    collaborators: string[]; // User IDs who interacted with task
  };
}

export const TaskSchema = SchemaFactory.createForClass(Task);

// Indexes for performance
TaskSchema.index({ organizationId: 1, status: 1 });
TaskSchema.index({ assignedTo: 1, status: 1 });
TaskSchema.index({ workflowInstanceId: 1 });
TaskSchema.index({ documentId: 1 });
TaskSchema.index({ dueDate: 1, status: 1 });
TaskSchema.index({ priority: 1, status: 1 });
TaskSchema.index({ createdAt: -1 });
TaskSchema.index({ 'tags': 1 });

export const TASK_MODEL = 'Task';

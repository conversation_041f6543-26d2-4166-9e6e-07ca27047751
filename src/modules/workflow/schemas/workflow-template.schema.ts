import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type WorkflowTemplateDocument = WorkflowTemplate & Document;

export enum WorkflowStepType {
  REVIEW = 'review',
  APPROVAL = 'approval',
  COMMENT = 'comment',
  EDIT = 'edit',
  SIGN = 'sign',
  NOTIFY = 'notify',
}

export enum WorkflowStepStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  SKIPPED = 'skipped',
  REJECTED = 'rejected',
}

export interface WorkflowStep {
  id: string;
  name: string;
  description: string;
  type: WorkflowStepType;
  order: number;
  assigneeType: 'user' | 'role' | 'group';
  assignees: string[]; // User IDs, role names, or group IDs
  isRequired: boolean;
  allowDelegation: boolean;
  timeoutHours: number;
  escalationRules: {
    enabled: boolean;
    escalateToUsers: string[];
    escalateAfterHours: number;
  };
  conditions: {
    skipIf?: string; // JSON condition
    requireIf?: string; // JSON condition
  };
  actions: {
    onComplete?: string[]; // Action IDs to execute
    onReject?: string[]; // Action IDs to execute
    onTimeout?: string[]; // Action IDs to execute
  };
}

@Schema({ timestamps: true })
export class WorkflowTemplate extends Document {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true })
  organizationId: string;

  @Prop({ required: true })
  createdBy: string;

  @Prop({ required: true, default: true })
  isActive: boolean;

  @Prop({ required: true, default: 1 })
  version: number;

  @Prop({ type: [String] })
  documentTypes: string[]; // Types of documents this workflow applies to

  @Prop({ type: [Object], required: true })
  steps: WorkflowStep[];

  @Prop({ type: Object })
  settings: {
    allowParallelSteps: boolean;
    autoAdvance: boolean;
    requireAllApprovals: boolean;
    allowComments: boolean;
    notifyOnStart: boolean;
    notifyOnComplete: boolean;
    maxDurationHours: number;
  };

  @Prop({ type: Object })
  triggers: {
    documentUpload: boolean;
    documentUpdate: boolean;
    manualStart: boolean;
    scheduledStart: boolean;
    externalTrigger: boolean;
  };

  @Prop({ type: Object })
  permissions: {
    canView: string[]; // User IDs or role names
    canEdit: string[]; // User IDs or role names
    canDelete: string[]; // User IDs or role names
    canExecute: string[]; // User IDs or role names
  };

  @Prop({ type: Object })
  metadata: {
    category: string;
    tags: string[];
    priority: 'low' | 'medium' | 'high' | 'urgent';
    estimatedDurationHours: number;
    usageCount: number;
    lastUsed: Date;
  };

  @Prop()
  parentTemplateId: string; // For template versioning

  @Prop({ type: Object })
  analytics: {
    totalExecutions: number;
    averageCompletionTime: number;
    successRate: number;
    commonBottlenecks: string[];
  };
}

export const WorkflowTemplateSchema = SchemaFactory.createForClass(WorkflowTemplate);

// Indexes for performance
WorkflowTemplateSchema.index({ organizationId: 1, isActive: 1 });
WorkflowTemplateSchema.index({ documentTypes: 1 });
WorkflowTemplateSchema.index({ createdBy: 1 });
WorkflowTemplateSchema.index({ 'metadata.category': 1 });
WorkflowTemplateSchema.index({ createdAt: -1 });

export const WORKFLOW_TEMPLATE_MODEL = 'WorkflowTemplate';

import { DocumentClause, ClauseMetadata } from '../../../common/interfaces/document-analysis.interface';
import * as levenshtein from 'fast-levenshtein';

/**
 * Normalizes content by removing irrelevant variations
 */
export function normalizeContent(content: string): string {
  return content
    .toLowerCase()
    .trim()
    .replace(/\s+/g, ' ')
    .replace(/[.,;:!?]+/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();
}

/**
 * Calculates similarity between two strings using Levenshtein distance
 */
export function calculateSimilarity(str1: string, str2: string): number {
  if (!str1 || !str2) return 0;

  const longerLength = Math.max(str1.length, str2.length);
  if (longerLength === 0) return 1;

  const distance = levenshtein.get(str1, str2);
  return (longerLength - distance) / longerLength;
}

/**
 * Merges metadata from multiple clauses
 */
export function mergeClauseMetadata(base: ClauseMetadata = {}, updates: ClauseMetadata = {}): ClauseMetadata {
  const result: ClauseMetadata = {
    section: [],
    obligations: [],
    rights: [],
    restrictions: [],
    definitions: { ...(base.definitions || {}), ...(updates.definitions || {}) }
  };

  // Merge section array
  if (base.section || updates.section) {
    // Ensure inputs are treated as arrays, even if they are single strings or null/undefined
    const baseSections = Array.isArray(base.section) ? base.section : (base.section ? [String(base.section)] : []);
    const updateSections = Array.isArray(updates.section) ? updates.section : (updates.section ? [String(updates.section)] : []);
    
    // Use Set to merge and deduplicate, then convert back to sorted array
    result.section = Array.from(new Set([
      ...baseSections,
      ...updateSections
    ])).sort();
  }

  // Merge arrays using Sets to ensure uniqueness
  if (base.obligations || updates.obligations) {
    result.obligations = Array.from(new Set([
      ...(base.obligations || []),
      ...(updates.obligations || [])
    ])).sort();
  }

  if (base.rights || updates.rights) {
    result.rights = Array.from(new Set([
      ...(base.rights || []),
      ...(updates.rights || [])
    ])).sort();
  }

  if (base.restrictions || updates.restrictions) {
    result.restrictions = Array.from(new Set([
      ...(base.restrictions || []),
      ...(updates.restrictions || [])
    ])).sort();
  }

  // Clean up empty arrays and objects
  Object.keys(result).forEach(key => {
    if (Array.isArray(result[key]) && result[key].length === 0) {
      delete result[key];
    } else if (key === 'definitions' && typeof result[key] === 'object' && result[key] !== null && Object.keys(result[key]).length === 0) {
      delete result[key];
    }
  });

  return result;
}

/**
 * Finds similar clauses using advanced pattern matching
 */
export function findSimilarClause(
  clause: DocumentClause,
  processedClauses: DocumentClause[],
  similarityThreshold = 0.90
): DocumentClause | null {
  if (!clause || !clause.content || !processedClauses?.length) {
    return null;
  }

  const normalizedInput = normalizeContent(clause.content);
  let bestMatch: DocumentClause | null = null;
  let highestSimilarity = similarityThreshold;

  for (const processedClause of processedClauses) {
    if (clause.type === processedClause.type && processedClause.content) {
      const similarity = calculateSimilarity(
        normalizedInput,
        normalizeContent(processedClause.content)
      );

      if (similarity > highestSimilarity) {
        highestSimilarity = similarity;
        bestMatch = processedClause;
      }
    }
  }

  return bestMatch;
}

/**
 * Extracts section identifiers from clause content
 * Returns an array containing the first found section identifier, or null.
 */
export function extractSectionInfo(content: string): string[] | null {
  if (!content) return null;

  const patterns = [
    /^(?:Section|Article|Clause)\s+(\d+(?:\.\d+)*)/i,
    /^(\d+(?:\.\d+)*)\s+[A-Z]/,
    /^(?:Section|Article|Clause)\s+([IVXLCDM]+)/i,
    /^\(([IVXLCDM]+)\)/i,
    /^(?:Section|Article|Clause)\s+([A-Z])/i,
    /^\(([a-z])\)/i,
    /^(\d+\.\d+(?:\.\d+)*)\s/,
    /^(?:Section|Article|Clause)\s+([A-Z0-9]+(?:[-.][A-Z0-9]+)*)/i
  ];

  for (const pattern of patterns) {
    const match = content.match(pattern);
    if (match?.[1]) {
      // Wrap the first found match in an array
      return [`Section ${match[1].toUpperCase()}`]; 
    }
  }

  return null;
}

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  PostProcessingRule,
  PostProcessingResult,
  PostProcessingOptions,
} from '../../../common/interfaces/post-processing.interface';
// Import the DocumentType enum from prompt-template interface to match the PostProcessingRule interface import
import { DocumentType } from '../../../common/interfaces/prompt-template.interface';
import {
  DocumentAnalysisResult,
  ErrorResponse,
  DocumentClause,
  DocumentSection,
} from '../../../common/interfaces/document-analysis.interface';
import {
  normalizeContent,
  mergeClauseMetadata,
  extractSectionInfo,
  findSimilarClause,
} from './clause-deduplication.util';

@Injectable()
export class ResultPostProcessingService {
  private readonly logger = new Logger(ResultPostProcessingService.name);
  private readonly rules: PostProcessingRule[] = [];

  constructor(private configService: ConfigService) {
    this.initializeDefaultRules();
    this.logger.log(
      `Initialized with ${this.rules.length} post-processing rules`,
    );
  }

  /**
   * Process a document analysis result through the post-processing pipeline
   * @param result The analysis result to process
   * @param options Processing options
   * @returns Processed result with metadata
   */
  async processAnalysisResult(
    result: DocumentAnalysisResult | ErrorResponse,
    options: PostProcessingOptions = {},
  ): Promise<PostProcessingResult<DocumentAnalysisResult | ErrorResponse>> {
    // If result is an error response, return it unmodified
    if ('error' in result) {
      return {
        data: result,
        originalData: result,
        meta: {
          appliedRules: [],
          success: false,
          warnings: ['Skipped post-processing due to error response'],
          processingTimeMs: 0,
        },
      };
    }

    const startTime = Date.now();
    const originalData = JSON.parse(
      JSON.stringify(result),
    ) as DocumentAnalysisResult;
    let processedData = { ...result };
    const appliedRules: string[] = [];
    const warnings: string[] = [];

    try {
      // Filter rules to apply based on options
      let rulesToApply = this.rules;

      if (options.documentType) {
        rulesToApply = rulesToApply.filter(
          (rule) =>
            rule.applicableDocumentTypes.length === 0 ||
            rule.applicableDocumentTypes.includes(options.documentType),
        );
      }

      if (options.onlyRules && options.onlyRules.length > 0) {
        rulesToApply = rulesToApply.filter((rule) =>
          options.onlyRules.includes(rule.id),
        );
      } else if (options.skipRules && options.skipRules.length > 0) {
        rulesToApply = rulesToApply.filter(
          (rule) => !options.skipRules.includes(rule.id),
        );
      }

      // Apply each rule in sequence
      for (const rule of rulesToApply) {
        try {
          this.logger.debug(`Applying rule: ${rule.id} - ${rule.name}`);
          processedData = rule.process(processedData);
          appliedRules.push(rule.id);
        } catch (error) {
          this.logger.error(
            `Error applying rule ${rule.id}: ${error.message}`,
            error.stack,
          );
          warnings.push(`Rule '${rule.id}' failed: ${error.message}`);
        }
      }
    } catch (error) {
      this.logger.error(
        `Error during post-processing: ${error.message}`,
        error.stack,
      );
      warnings.push(`Post-processing error: ${error.message}`);
    }

    const processingTimeMs = Date.now() - startTime;
    this.logger.debug(`Post-processing completed in ${processingTimeMs}ms`);

    return {
      data: processedData,
      originalData,
      meta: {
        appliedRules,
        success: warnings.length === 0,
        warnings: warnings.length > 0 ? warnings : undefined,
        processingTimeMs,
      },
    };
  }

  /**
   * Register a new post-processing rule
   * @param rule Rule to register
   */
  registerRule(rule: PostProcessingRule): void {
    // Check if rule with same ID already exists
    const existingRuleIndex = this.rules.findIndex((r) => r.id === rule.id);

    if (existingRuleIndex >= 0) {
      this.rules[existingRuleIndex] = rule;
      this.logger.log(`Updated existing rule: ${rule.id}`);
    } else {
      this.rules.push(rule);
      this.logger.log(`Registered new rule: ${rule.id}`);
    }
  }

  /**
   * Get all registered rules
   */
  getRules(): PostProcessingRule[] {
    return [...this.rules];
  }

  /**
   * Initialize default post-processing rules
   */
  private initializeDefaultRules(): void {
    // Rule 1: Ensure section titles are properly formatted
    this.registerRule({
      id: 'format-section-titles',
      name: 'Format Section Titles',
      description:
        'Ensures section titles are properly capitalized and formatted',
      applicableDocumentTypes: [],
      severity: 'low',
      process: (result: DocumentAnalysisResult): DocumentAnalysisResult => {
        if (!result.sections || !Array.isArray(result.sections)) return result;

        result.sections = result.sections.map((section) => {
          if (section.title) {
            // Capitalize first letter of each word in title
            section.title = section.title
              .split(' ')
              .map(
                (word) =>
                  word.charAt(0).toUpperCase() + word.slice(1).toLowerCase(),
              )
              .join(' ');

            // Remove any trailing punctuation
            section.title = section.title.replace(/[.,:;]$/, '');
          }
          return section;
        });

        return result;
      },
    });

    // Rule 2: Fix missing risk levels in clauses
    this.registerRule({
      id: 'fix-missing-risk-levels',
      name: 'Fix Missing Risk Levels',
      description: 'Ensures all clauses have a risk level assigned',
      applicableDocumentTypes: [],
      severity: 'medium',
      process: (result: DocumentAnalysisResult): DocumentAnalysisResult => {
        if (!result.clauses || !Array.isArray(result.clauses)) return result;

        result.clauses = result.clauses.map((clause: DocumentClause) => {
          // Set default risk level if missing
          if (!clause.riskLevel) {
            clause.riskLevel = 'low';
          }

          // Ensure riskDescription exists when risk is medium or high
          if (
            (clause.riskLevel === 'medium' || clause.riskLevel === 'high') &&
            !clause.riskDescription
          ) {
            clause.riskDescription = `Potential risk identified in ${
              clause.type || 'clause'
            }.`;
          }

          return clause;
        });

        return result;
      },
    });

    // Rule 3: Deduplicate Clauses and Enhance Metadata
    this.registerRule({
      id: 'deduplicate-clauses',
      name: 'Deduplicate Clauses and Enhance Metadata',
      description:
        'Applies fuzzy matching to identify and merge similar clauses, ensuring section metadata exists.',
      applicableDocumentTypes: [], // Apply to all types
      severity: 'high', // Significant data transformation
      process: (result: DocumentAnalysisResult): DocumentAnalysisResult => {
        if (!result.clauses || !Array.isArray(result.clauses) || result.clauses.length === 0) {
          this.logger.debug(`Rule 'deduplicate-clauses': No clauses found to process.`);
          return result;
        }

        const processedClauses: DocumentClause[] = [];
        // Read threshold from config, default to 0.90
        const similarityThreshold = this.configService.get<number>('postProcessing.similarityThreshold', 0.90);
        let mergeCount = 0;
        let sectionAssignedCount = 0;

        this.logger.debug(`Rule 'deduplicate-clauses': Starting processing of ${result.clauses.length} clauses with threshold ${similarityThreshold}`);

        for (let i = 0; i < result.clauses.length; i++) {
          const currentClause = { ...result.clauses[i] }; // Create a copy to modify

          // 1. Ensure Section Metadata Exists (as string[])
          // Check if metadata.section is missing OR is an empty array
          if (!currentClause.metadata?.section || 
              (Array.isArray(currentClause.metadata.section) && currentClause.metadata.section.length === 0)) {
            
            // TODO: Update extractSectionInfo to return string[] | null
            // For now, assume it might still return string | null and wrap it
            const extractedSectionInfo = extractSectionInfo(currentClause.content); // Using existing helper for now

            if (extractedSectionInfo) {
              // Wrap the string result in an array for consistency
              const sectionArray = Array.isArray(extractedSectionInfo) ? extractedSectionInfo : [extractedSectionInfo];
              currentClause.metadata = { ...(currentClause.metadata || {}), section: sectionArray }; 
              this.logger.verbose(`Rule 'deduplicate-clauses': Extracted/wrapped section info for clause index ${i}`);
              sectionAssignedCount++;
            } else {
              // Assign default array value
              currentClause.metadata = { ...(currentClause.metadata || {}), section: ['Unknown Section'] }; 
              this.logger.warn(`Rule 'deduplicate-clauses': Could not extract section array for clause index ${i}. Assigning default.`);
              sectionAssignedCount++;
            }
          }
          // Optional else block for logging if section already exists

          // 2. Find Similar Clause among already processed unique clauses
          const similarExistingClause = findSimilarClause(currentClause, processedClauses, similarityThreshold);

          if (similarExistingClause) {
            // 3. Merge Metadata if similar clause found
            // Find the actual object in processedClauses to update its metadata
            const originalIndex = processedClauses.findIndex(c => c === similarExistingClause);
            if (originalIndex !== -1) {
                processedClauses[originalIndex].metadata = mergeClauseMetadata(
                    similarExistingClause.metadata,
                    currentClause.metadata
                );
                // Optionally update other fields like score if needed, for now just merge metadata
                this.logger.verbose(`Rule 'deduplicate-clauses': Merged clause index ${i} into existing clause index ${originalIndex}.`);
                mergeCount++;
            } else {
                 // This case should ideally not happen if findSimilarClause returns an object from processedClauses
                 this.logger.error(`Rule 'deduplicate-clauses': Could not find the index for the similar clause object. Adding clause index ${i} as unique.`);
                 processedClauses.push(currentClause); // Add as unique if merge target not found
            }

          } else {
            // 4. Add as Unique Clause if no similar found
            processedClauses.push(currentClause);
          }
        }

        this.logger.log(`Rule 'deduplicate-clauses': Completed. Processed ${result.clauses.length} clauses. Merged ${mergeCount} duplicates. Assigned/extracted sections for ${sectionAssignedCount} clauses. Final count: ${processedClauses.length}`);
        // Update the original result object with the processed clauses
        result.clauses = processedClauses;
        return result;
      },
    });

    // Rule 4: Standardize clause types
    this.registerRule({
      id: 'standardize-clause-types',
      name: 'Standardize Clause Types',
      description:
        'Ensures clause types are standardized to a known set of values',
      applicableDocumentTypes: [],
      severity: 'medium',
      process: (result: DocumentAnalysisResult): DocumentAnalysisResult => {
        if (!result.clauses || !Array.isArray(result.clauses)) return result;

        const standardTypeMap = {
          // Map common variations to standard types
          obligations: 'obligation',
          'obligation clause': 'obligation',
          duties: 'obligation',
          duty: 'obligation',
          prohibitions: 'prohibition',
          restrictions: 'prohibition',
          limitations: 'prohibition',
          permissions: 'permission',
          rights: 'permission',
          'allowed actions': 'permission',
          definitions: 'definition',
          'defined terms': 'definition',
          meaning: 'definition',
          terminations: 'termination',
          'termination clause': 'termination',
          ending: 'termination',
          liabilities: 'liability',
          'liability clause': 'liability',
          indemnification: 'liability',
          'confidentiality clause': 'confidentiality',
          'non-disclosure': 'confidentiality',
          secrecy: 'confidentiality',
        };

        result.clauses = result.clauses.map((clause: DocumentClause) => {
          if (clause.type) {
            const lowerType = clause.type.toLowerCase();
            clause.type = standardTypeMap[lowerType] || lowerType;
          } else {
            clause.type = 'other';
          }

          return clause;
        });

        return result;
      },
    });

    // Rule 5: Generate comprehensive executive summary
    this.registerRule({
      id: 'ensure-quality-summary',
      name: 'Ensure Quality Summary',
      description: 'Generates a detailed executive summary with key insights for legal practitioners',
      applicableDocumentTypes: [],
      severity: 'high',
      process: (result: DocumentAnalysisResult): DocumentAnalysisResult => {
        // Initialize summary components
        const summaryParts: string[] = [];
        
        // 1. Document Type and Purpose
        if (result.documentType) {
          summaryParts.push(`This ${result.documentType.toLowerCase()} document`);
        }

        // 2. Parties Involved
        if (result.parties && result.parties.length > 0) {
          const partyDescriptions = result.parties
            .map(p => `${p.name} (${p.role})`)
            .join(' and ');
          summaryParts.push(`involves ${partyDescriptions}`);
        }

        // 3. Key Dates
        if (result.effectiveDate || result.terminationDate) {
          const dates = [];
          if (result.effectiveDate) dates.push(`effective from ${result.effectiveDate}`);
          if (result.terminationDate) dates.push(`terminating on ${result.terminationDate}`);
          summaryParts.push(dates.join(' and '));
        }

        // 4. Risk Assessment
        if (result.clauses && result.clauses.length > 0) {
          const highRiskClauses = result.clauses.filter(c => c.riskLevel === 'high');
          const mediumRiskClauses = result.clauses.filter(c => c.riskLevel === 'medium');
          
          if (highRiskClauses.length > 0) {
            summaryParts.push(`Contains ${highRiskClauses.length} high-risk clauses that require immediate attention, including ${highRiskClauses.map(c => c.title).join(', ')}`);
          }
          if (mediumRiskClauses.length > 0) {
            summaryParts.push(`Includes ${mediumRiskClauses.length} medium-risk provisions that should be reviewed`);
          }
        }

        // 5. Key Obligations
        if (result.clauses) {
          const obligations = result.clauses
            .filter(c => c.metadata?.obligations?.length > 0)
            .flatMap(c => c.metadata.obligations);
          
          if (obligations.length > 0) {
            summaryParts.push(`Key obligations include: ${obligations.slice(0, 3).join('; ')}${obligations.length > 3 ? '...' : ''}`);
          }
        }

        // 6. Governing Law
        if (result.governingLaw) {
          summaryParts.push(`governed by ${result.governingLaw}`);
        }

        // Create base summary or use fallback
        result.summary = summaryParts.length > 0
          ? summaryParts.join(', ') + '.'
          : 'Analysis required for this document. Review the structure below for key sections.';

        // Add document structure section if minimal information
        if ((!result.parties?.length && !result.clauses?.length) && result.sections?.length > 0) {
          result.summary += '\n\nDOCUMENT STRUCTURE:\n';
          result.summary += result.sections
            .map(s => `• ${s.title}: ${s.purpose || 'No description provided'}`)
            .join('\n');
        }

        // Add risk analysis section
        const risks = {
          high: result.clauses?.filter(c => c.riskLevel === 'high') || [],
          medium: result.clauses?.filter(c => c.riskLevel === 'medium') || []
        };

        if (risks.high.length > 0 || risks.medium.length > 0) {
          result.summary += '\n\nRISK ANALYSIS & RECOMMENDATIONS:\n';
          
          if (risks.high.length > 0) {
            result.summary += '\nHIGH PRIORITY:\n' + risks.high
              .map(c => `⚠️ ${c.title}: ${c.riskDescription || 'Requires immediate review'}`)
              .join('\n');
          }
          
          if (risks.medium.length > 0) {
            result.summary += '\n\nMEDIUM PRIORITY:\n' + risks.medium
              .map(c => `⚠️ ${c.title}: ${c.riskDescription || 'Review recommended'}`)
              .join('\n');
          }
        }

        // Add key obligations if available
        const obligations = result.clauses
          ?.filter(c => c.metadata?.obligations?.length > 0)
          ?.flatMap(c => c.metadata.obligations) || [];
        
        if (obligations.length > 0) {
          result.summary += '\n\nKEY OBLIGATIONS:\n';
          obligations.slice(0, 5).forEach(obligation => {
            result.summary += `• ${obligation}\n`;
          });
          if (obligations.length > 5) {
            result.summary += `... and ${obligations.length - 5} more obligations\n`;
          }
        }

        return result;
      },
    });

    // Rule 6: Contract-specific post-processing
    this.registerRule({
      id: 'contract-special-clauses',
      name: 'Contract Special Clauses',
      description: 'Identifies and highlights special clauses in contracts',
      applicableDocumentTypes: [DocumentType.CONTRACT, DocumentType.AGREEMENT],
      severity: 'high',
      process: (result: DocumentAnalysisResult): DocumentAnalysisResult => {
        if (!result.clauses || !Array.isArray(result.clauses)) return result;

        // High-risk keywords that should be flagged in contracts
        const highRiskKeywords = [
          'unlimited liability',
          'sole discretion',
          'unilateral',
          'waive',
          'waiver',
          'indemnify',
          'indemnification',
          'perpetual',
          'irrevocable',
          'terminate without cause',
          'non-compete',
          'exclusive',
          'jurisdiction',
        ];

        result.clauses = result.clauses.map((clause: DocumentClause) => {
          const content = clause.content?.toLowerCase() || '';

          // Scan for high-risk keywords
          for (const keyword of highRiskKeywords) {
            if (content.includes(keyword.toLowerCase())) {
              clause.riskLevel = 'high';
              if (
                !clause.riskDescription ||
                !clause.riskDescription.includes(keyword)
              ) {
                const description = clause.riskDescription || '';
                clause.riskDescription = description
                  ? `${description}. Contains "${keyword}" clause which may require special attention.`
                  : `Contains "${keyword}" clause which may require special attention.`;
              }
            }
          }

          return clause;
        });

        return result;
      },
    });

    // Rule 7: Legal Opinion-specific post-processing
    this.registerRule({
      id: 'legal-opinion-analysis',
      name: 'Legal Opinion Analysis',
      description: 'Specialized processing for legal opinions and memoranda',
      applicableDocumentTypes: [DocumentType.LEGAL_OPINION],
      severity: 'medium',
      process: (result: DocumentAnalysisResult): DocumentAnalysisResult => {
        if (!result.sections || !Array.isArray(result.sections)) return result;

        // Add a reasoning section if not present
        const hasReasoningSection = result.sections.some(
          (section) =>
            section.title?.toLowerCase().includes('reasoning') ||
            section.title?.toLowerCase().includes('analysis'),
        );

        if (!hasReasoningSection && result.content) {
          // Extract potential reasoning from content
          const content = result.content.toLowerCase();
          const reasoningKeywords = [
            'therefore',
            'conclude',
            'analysis shows',
            'because',
            'due to',
            'reasoning',
          ];

          let reasoningContent = '';
          for (const keyword of reasoningKeywords) {
            const index = content.indexOf(keyword);
            if (index !== -1) {
              // Extract a paragraph containing this keyword
              const startIndex = content.lastIndexOf('.', index) + 1;
              const endIndex = content.indexOf('.', index + keyword.length);
              if (startIndex !== -1 && endIndex !== -1) {
                reasoningContent +=
                  content.substring(startIndex, endIndex + 1) + ' ';
              }
            }
          }

          if (reasoningContent) {
            result.sections.push({
              title: 'Legal Reasoning and Analysis',
              content: reasoningContent.trim(),
              purpose: 'Presents the legal reasoning and analysis behind the opinion',
              startIndex: 0, // Default to 0 since this is auto-generated
              endIndex: reasoningContent.trim().length // Use content length as end index
            });
          }
        }

        // Add citation verification metadata
        result.metadata = result.metadata || {};
        result.metadata.citationVerified = false;
        result.metadata.citationCount = 0;

        // Count citations in document
        if (result.content) {
          const citations = result.content.match(
            /(\d+\s+U\.S\.\s+\d+)|(\d+\s+S\.Ct\.\s+\d+)|(\d+\s+F\.\d+d\s+\d+)|(\[.*?\])/g,
          );
          if (citations) {
            result.metadata.citationCount = citations.length;
            result.metadata.citationVerified = true;
          }
        }

        return result;
      },
    });

    // Rule 8: Policy document-specific post-processing
    this.registerRule({
      id: 'policy-document-processing',
      name: 'Policy Document Processing',
      description: 'Specialized processing for policy documents',
      applicableDocumentTypes: [DocumentType.POLICY],
      severity: 'medium',
      process: (result: DocumentAnalysisResult): DocumentAnalysisResult => {
        if (!result.sections || !Array.isArray(result.sections)) return result;

        // Add compliance information section if not present
        const hasComplianceSection = result.sections.some(
          (section) =>
            section.title?.toLowerCase().includes('compliance') ||
            section.title?.toLowerCase().includes('enforcement'),
        );

        if (!hasComplianceSection) {
          result.sections.push({
            title: 'Compliance & Enforcement',
            content: 'This policy requires compliance monitoring. The document should specify enforcement mechanisms.',
            purpose: 'Describes how compliance with the policy is monitored and enforced',
            startIndex: 0, // Default to 0 since this is auto-generated
            endIndex: 'This policy requires compliance monitoring. The document should specify enforcement mechanisms.'.length
          });
        }

        // Add metadata specific to policies
        result.metadata = result.metadata || {};
        result.metadata.policyType = 'Unspecified';
        result.metadata.effectiveDate = 'Unknown';
        result.metadata.reviewSchedule = 'Unknown';

        // Try to extract policy metadata from content
        if (result.content) {
          const content = result.content.toLowerCase();

          // Extract policy type
          const policyTypePatterns = [
            { pattern: /privacy policy/i, type: 'Privacy' },
            { pattern: /security policy/i, type: 'Security' },
            { pattern: /code of conduct/i, type: 'Code of Conduct' },
            { pattern: /employee handbook/i, type: 'Employee Handbook' },
            { pattern: /health.+safety/i, type: 'Health and Safety' },
          ];

          for (const { pattern, type } of policyTypePatterns) {
            if (pattern.test(result.content)) {
              result.metadata.policyType = type;
              break;
            }
          }

          // Extract dates
          const effectiveDateMatch = result.content.match(
            /effective\s+(?:date|from|as\s+of)\s*:?\s*([A-Za-z]+\s+\d{1,2},?\s+\d{4}|\d{1,2}\/\d{1,2}\/\d{4}|\d{1,2}-\d{1,2}-\d{4})/i,
          );
          if (effectiveDateMatch) {
            result.metadata.effectiveDate = effectiveDateMatch[1];
          }
        }

        return result;
      },
    });

    // Rule 9: Legislation-specific post-processing
    this.registerRule({
      id: 'legislation-processing',
      name: 'Legislation Processing',
      description: 'Specialized processing for legislation and regulations',
      applicableDocumentTypes: [DocumentType.LEGISLATION],
      severity: 'high',
      process: (result: DocumentAnalysisResult): DocumentAnalysisResult => {
        if (!result.sections || !Array.isArray(result.sections)) return result;

        // Add a definitions section if not present
        const hasDefinitionsSection = result.sections.some(
          (section) =>
            section.title?.toLowerCase().includes('definition') ||
            section.title?.toLowerCase().includes('meaning'),
        );

        if (!hasDefinitionsSection && result.content) {
          // Try to extract definitions from content
          const definitionMatches = result.content.match(
            /"([^"]+)"\s+means|'([^']+)'\s+means|\(([a-z])\)\s+["']?([A-Z][^"'.]*)["']?\s+means/g,
          );

          if (definitionMatches && definitionMatches.length > 0) {
            result.sections.push({
              title: 'Definitions',
              content: definitionMatches.join('; '),
              purpose: 'Defines key terms used in the legislation',
              startIndex: 0, // Default to 0 since this is auto-generated
              endIndex: definitionMatches.join('; ').length
            });
          }
        }

        // Add legislative metadata
        result.metadata = result.metadata || {};
        result.metadata.jurisdictionLevel = 'Unknown';

        // Determine jurisdiction level
        if (result.content) {
          const contentLower = result.content.toLowerCase();
          if (
            contentLower.includes('united states code') ||
            contentLower.includes('u.s.c.') ||
            contentLower.includes('federal register') ||
            contentLower.includes('public law')
          ) {
            result.metadata.jurisdictionLevel = 'Federal';
          } else if (
            contentLower.includes('state of') ||
            /revised statutes of [a-z]+/i.test(result.content)
          ) {
            result.metadata.jurisdictionLevel = 'State';
          } else if (
            contentLower.includes('ordinance') ||
            contentLower.includes('municipal code')
          ) {
            result.metadata.jurisdictionLevel = 'Local';
          }
        }

        // Add legislation-specific classifications
        if (result.clauses && Array.isArray(result.clauses)) {
          result.clauses = result.clauses.map((clause) => {
            if (
              clause.content &&
              /shall not|prohibited|unlawful/i.test(clause.content)
            ) {
              clause.type = 'prohibition';
            } else if (
              clause.content &&
              /shall|must|required/i.test(clause.content)
            ) {
              clause.type = 'mandate';
            } else if (
              clause.content &&
              /may|permission|allowed|authorized/i.test(clause.content)
            ) {
              clause.type = 'permission';
            }
            return clause;
          });
        }

        return result;
      },
    });

    // Rule 10: Court Filing-specific post-processing
    this.registerRule({
      id: 'court-filing-processing',
      name: 'Court Filing Processing',
      description:
        'Specialized processing for court filings and legal pleadings',
      applicableDocumentTypes: [DocumentType.COURT_FILING],
      severity: 'high',
      process: (result: DocumentAnalysisResult): DocumentAnalysisResult => {
        if (!result.sections || !Array.isArray(result.sections)) return result;

        // Add case information section if not present
        const hasCaseInfoSection = result.sections.some(
          (section) =>
            section.title?.toLowerCase().includes('case') ||
            section.title?.toLowerCase().includes('caption'),
        );

        if (!hasCaseInfoSection) {
          // Create a case information section
          const caseInfo: DocumentSection = {
            title: 'Case Information',
            content: 'Document appears to be a court filing. Additional case details should be extracted.',
            purpose: 'Provides key information about the case',
            startIndex: 0,
            endIndex: 'Document appears to be a court filing. Additional case details should be extracted.'.length
          };

          // Try to extract case information from content
          if (result.content) {
            // Extract case number
            const caseNumberMatch = result.content.match(
              /case\s+no\.?\s*[:.]?\s*([A-Z0-9\-]+)/i,
            );
            if (caseNumberMatch) {
              caseInfo.content = `Case Number: ${caseNumberMatch[1]}. `;
            }

            // Extract court name
            const courtMatch = result.content.match(/in\s+the\s+([^,]+court)/i);
            if (courtMatch) {
              caseInfo.content += `Court: ${courtMatch[1]}. `;
            }

            // Extract parties
            const plaintiffMatch = result.content.match(
              /plaintiff[s]?[\s:,]+([^,]+)/i,
            );
            const defendantMatch = result.content.match(
              /defendant[s]?[\s:,]+([^,\.]+)/i,
            );

            if (plaintiffMatch) {
              caseInfo.content += `Plaintiff: ${plaintiffMatch[1].trim()}. `;
            }

            if (defendantMatch) {
              caseInfo.content += `Defendant: ${defendantMatch[1].trim()}. `;
            }
          }

          result.sections.unshift(caseInfo); // Add to beginning
        }

        // Add litigation-specific metadata
        result.metadata = result.metadata || {};
        result.metadata.filingType = 'Unknown';
        result.metadata.filingDate = 'Unknown';

        // Determine filing type
        if (result.content) {
          const contentLower = result.content.toLowerCase();
          if (contentLower.includes('complaint')) {
            result.metadata.filingType = 'Complaint';
          } else if (contentLower.includes('motion')) {
            result.metadata.filingType = 'Motion';
          } else if (contentLower.includes('brief')) {
            result.metadata.filingType = 'Brief';
          } else if (contentLower.includes('answer')) {
            result.metadata.filingType = 'Answer';
          } else if (contentLower.includes('petition')) {
            result.metadata.filingType = 'Petition';
          }

          // Extract filing date
          const filingDateMatch = result.content.match(
            /filed\s+(?:on|this)?\s*:?\s*([A-Za-z]+\s+\d{1,2},?\s+\d{4}|\d{1,2}\/\d{1,2}\/\d{4}|\d{1,2}-\d{1,2}-\d{4})/i,
          );
          if (filingDateMatch) {
            result.metadata.filingDate = filingDateMatch[1];
          }
        }

        return result;
      },
    });
  }
}

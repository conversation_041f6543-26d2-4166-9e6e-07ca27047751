# PowerShell script to run deduplication test with proper error handling

Write-Host "=== Starting Deduplication Test ===" -ForegroundColor Cyan

# Check dependencies
try {
    $null = Get-Command jq -ErrorAction Stop
    $null = Get-Command curl -ErrorAction Stop
    $null = Get-Command node -ErrorAction Stop
} catch {
    Write-Host "Error: Missing required dependencies. Running setup..." -ForegroundColor Yellow
    . .\windows-setup.ps1
}

# Run the test
Write-Host "`nRunning endpoint test..." -ForegroundColor Cyan
try {
    # Run test script
    cmd /c test-endpoint.bat
    if ($LASTEXITCODE -ne 0) {
        throw "Test script failed with exit code $LASTEXITCODE"
    }

    # Check results
    if (Test-Path analysis-result.json) {
        Write-Host "`nAnalyzing test results..." -ForegroundColor Cyan
        $result = Get-Content analysis-result.json | ConvertFrom-Json
        
        # Display summary
        Write-Host "`nTest Summary:" -ForegroundColor Green
        Write-Host "------------"
        Write-Host "Original sections: 4"
        Write-Host "Final unique clauses: $($result.clauses.Length)"
        Write-Host "Deduplication rate: $([math]::Round(((4 - $result.clauses.Length) / 4 * 100), 1))%"
        
        Write-Host "`nTest completed successfully!" -ForegroundColor Green
    } else {
        throw "Analysis results file not found"
    }
} catch {
    Write-Host "`nError during test execution:" -ForegroundColor Red
    Write-Host $_.Exception.Message
    exit 1
} finally {
    # Cleanup
    if (Test-Path test-doc.txt) { Remove-Item test-doc.txt }
    if (Test-Path upload-response.json) { Remove-Item upload-response.json }
    if (Test-Path analysis-result.json) { Remove-Item analysis-result.json }
}
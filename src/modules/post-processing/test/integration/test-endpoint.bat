@echo off
echo Testing document analysis endpoint with deduplication...

REM Check for jq installation
where jq >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Error: jq is required but not installed.
    echo Please run windows-setup.ps1 first.
    exit /b 1
)

REM Create test document
(
echo Section 1.1 Confidentiality
echo The Contractor shall maintain strict confidentiality of all information received from the Company.
echo.
echo Section 2.3 Information Protection
echo All information received must be kept strictly confidential by the Contractor.
echo.
echo Section 3.1 Term
echo This agreement shall be valid for 12 months from the effective date.
echo.
echo Section 3.2 Duration
echo The agreement's duration shall be 12 months starting from the effective date.
) > test-doc.txt

echo Uploading document...
curl -X POST http://localhost:4000/api/documents/upload ^
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************.6hy8lYniqkE5D-CCr_bWBEKXoz_u_rCPtKat-QaVR-Y" ^
  -F "file=@test-doc.txt" ^
  -F "title=Deduplication Test" > upload-response.json

REM Extract document ID and check for errors
for /f "tokens=*" %%i in ('type upload-response.json ^| jq -r ".id // empty"') do set DOC_ID=%%i
if "%DOC_ID%"=="" (
    echo Error during upload:
    type upload-response.json
    del test-doc.txt upload-response.json
    exit /b 1
)
echo Document uploaded with ID: %DOC_ID%

REM Wait for processing
timeout /t 2 > nul

echo.
echo Analyzing document...
curl -X POST "http://localhost:4000/api/documents/%DOC_ID%/analyze" ^
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************.6hy8lYniqkE5D-CCr_bWBEKXoz_u_rCPtKat-QaVR-Y" > analysis-result.json

REM Verify the results using Node.js script
node verify-results.js analysis-result.json

REM Clean up
del test-doc.txt upload-response.json

echo.
echo Test complete.
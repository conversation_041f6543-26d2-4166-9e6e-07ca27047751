#!/bin/bash

echo "=== Running Full Deduplication Test ==="
echo "======================================"

# Make scripts executable
chmod +x test-endpoint.sh

# Run test and capture output
echo "Step 1: Running endpoint test..."
./test-endpoint.sh

# Wait for analysis to complete
sleep 2

# Validate results
echo -e "\nStep 2: Validating results..."
node validate-results.js

echo -e "\nTest sequence complete."

# Cleanup
echo "Cleaning up temporary files..."
rm -f test-doc.txt analysis-result.json

echo -e "\nDone."
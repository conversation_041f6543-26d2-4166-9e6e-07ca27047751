const fs = require('fs');
const path = require('path');

function compareResults() {
  // Read actual and expected results
  const actualResults = JSON.parse(fs.readFileSync('analysis-result.json', 'utf8'));
  const expectedResults = JSON.parse(fs.readFileSync('expected-results.json', 'utf8'));
  
  console.log('\n=== Deduplication Validation Results ===\n');

  // Check clause count
  const actualClauseCount = actualResults.clauses.length;
  console.log(`Expected clauses: ${expectedResults.expectedClauses}`);
  console.log(`Actual clauses: ${actualClauseCount}`);
  console.log(`Count match: ${actualClauseCount === expectedResults.expectedClauses ? '✓' : '✗'}`);

  // Validate clause groups
  console.log('\nChecking clause groups:');
  expectedResults.expectedGroups.forEach(expected => {
    const matchingClause = actualResults.clauses.find(clause => 
      clause.type === expected.type &&
      clause.content.toLowerCase().includes(expected.content.toLowerCase())
    );

    console.log(`\nGroup: ${expected.type}`);
    if (matchingClause) {
      console.log('- Found matching clause ✓');
      // Check sections
      const hasSections = expected.sections.every(section => 
        matchingClause.metadata.sections?.includes(section)
      );
      console.log(`- All sections merged: ${hasSections ? '✓' : '✗'}`);
      console.log('- Content:', matchingClause.content.substring(0, 100));
    } else {
      console.log('- No matching clause found ✗');
    }
  });

  // Calculate deduplication efficiency
  const efficiency = ((expectedResults.originalSections - actualClauseCount) / 
                     expectedResults.originalSections * 100).toFixed(1);
  
  console.log(`\nDeduplication efficiency: ${efficiency}%`);
}

// Run validation if executed directly
if (require.main === module) {
  compareResults();
}

module.exports = { compareResults };
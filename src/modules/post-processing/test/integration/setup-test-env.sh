#!/bin/bash

# Install required dependencies
echo "Installing required dependencies..."

# Check for package manager
if command -v apt-get &> /dev/null; then
    sudo apt-get update
    sudo apt-get install -y jq curl
elif command -v yum &> /dev/null; then
    sudo yum install -y jq curl
elif command -v brew &> /dev/null; then
    brew install jq
elif command -v choco &> /dev/null; then
    choco install jq curl
else
    echo "Please install jq and curl manually for your system"
    exit 1
fi

# Create test directory structure
echo "Setting up test environment..."
mkdir -p temp
mkdir -p results

# Set permissions
chmod +x test-endpoint.sh
chmod +x run-full-test.sh

echo "Test environment setup complete!"
echo "You can now run ./run-full-test.sh"
import {
  normalizeContent,
  findSimilarClause,
  mergeClauseMetadata,
} from '../../services/clause-deduplication.util';
import { DocumentClause } from '../../../../common/interfaces/document-analysis.interface';

describe('Clause Deduplication Integration', () => {
  it('should deduplicate similar clauses in document analysis', () => {
    const inputClauses: DocumentClause[] = [
      {
        type: 'confidentiality',
        content: 'The Contractor shall maintain strict confidentiality of all information.',
        metadata: { section: '1.1' }
      },
      {
        type: 'confidentiality',
        content: 'Contractor must maintain strict confidentiality of all business information.',
        metadata: { section: '5.2' }
      },
      {
        type: 'termination',
        content: 'This agreement may be terminated with 30 days written notice.',
        metadata: { section: '8.1' }
      },
      {
        type: 'termination',
        content: 'The agreement can be terminated by providing 30 days notice in writing.',
        metadata: { section: '8.2' }
      }
    ];

    // Process clauses sequentially to simulate deduplication
    const processedClauses: DocumentClause[] = [];
    inputClauses.forEach(clause => {
      const similar = findSimilarClause(clause, processedClauses);
      if (similar) {
        // Merge with existing similar clause
        const index = processedClauses.indexOf(similar);
        processedClauses[index] = {
          ...similar,
          metadata: mergeClauseMetadata(similar.metadata, clause.metadata)
        };
      } else {
        // Add as new clause
        processedClauses.push(clause);
      }
    });

    // Should combine similar clauses
    expect(processedClauses.length).toBe(2);

    // Verify confidentiality clauses merged correctly
    const confidentialityClauses = processedClauses.filter(c => c.type === 'confidentiality');
    expect(confidentialityClauses.length).toBe(1);
    const confClause = confidentialityClauses[0];
    expect(confClause.metadata.section).toBeDefined();
    
    // Verify termination clauses merged correctly
    const terminationClauses = processedClauses.filter(c => c.type === 'termination');
    expect(terminationClauses.length).toBe(1);
    const termClause = terminationClauses[0];
    expect(termClause.metadata.section).toBeDefined();

    // Verify content similarity detection
    const similarityCheck1 = findSimilarClause(inputClauses[0], [inputClauses[1]]);
    expect(similarityCheck1).toBeTruthy();
    
    const similarityCheck2 = findSimilarClause(inputClauses[2], [inputClauses[3]]);
    expect(similarityCheck2).toBeTruthy();
  });

  it('should not merge clauses of different types', () => {
    const confidentialityClause: DocumentClause = {
      type: 'confidentiality',
      content: 'Must maintain confidentiality.',
      metadata: { section: '1.1' }
    };

    const terminationClause: DocumentClause = {
      type: 'termination',
      content: 'Must maintain confidentiality of all information.',
      metadata: { section: '5.2' }
    };

    const similar = findSimilarClause(confidentialityClause, [terminationClause]);
    expect(similar).toBeNull();
  });

  it('should properly merge metadata', () => {
    const metadata1 = {
      section: '1.1',
      obligations: ['keep secret', 'report breaches'],
      rights: ['receive information']
    };

    const metadata2 = {
      section: '1.2',
      obligations: ['report breaches', 'return documents'],
      rights: ['receive updates']
    };

    const merged = mergeClauseMetadata(metadata1, metadata2);
    expect(merged.section).toBe('1.1'); // Should keep first section
    expect(merged.obligations).toContain('keep secret');
    expect(merged.obligations).toContain('return documents');
    expect(merged.rights).toContain('receive information');
    expect(merged.rights).toContain('receive updates');
    // Should deduplicate repeated obligations
    expect(merged.obligations.filter(o => o === 'report breaches').length).toBe(1);
  });

  it('should normalize content consistently', () => {
    const variations = [
      'The Contractor shall maintain STRICT confidentiality!',
      '  The contractor shall maintain strict confidentiality.  ',
      'The Contractor Shall Maintain Strict Confidentiality;',
      'the contractor shall maintain strict confidentiality'
    ];

    const normalized = variations.map(v => normalizeContent(v));
    // All variations should normalize to the same string
    normalized.forEach(n => {
      expect(n).toBe(normalized[0]);
    });
  });
});
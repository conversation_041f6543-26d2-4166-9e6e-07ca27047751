const fs = require('fs');
const path = require('path');

function analyzeDeduplications(analysisFile = 'analysis-result.json') {
  const resultPath = path.join(__dirname, analysisFile);
  const result = JSON.parse(fs.readFileSync(resultPath, 'utf8'));

  // Group clauses by type
  const clausesByType = result.clauses.reduce((acc, clause) => {
    if (!acc[clause.type]) acc[clause.type] = [];
    acc[clause.type].push(clause);
    return acc;
  }, {});

  console.log('\n=== Deduplication Analysis Results ===\n');
  
  // Overall statistics
  console.log('Overall Statistics:');
  console.log('-----------------');
  console.log(`Total clauses: ${result.clauses.length}`);
  console.log(`Unique clause types: ${Object.keys(clausesByType).length}`);
  console.log('');

  // Analyze each clause type
  Object.entries(clausesByType).forEach(([type, clauses]) => {
    console.log(`Clause Type: ${type}`);
    console.log('-'.repeat(type.length + 12));
    console.log(`Count: ${clauses.length}`);
    
    // Check for multi-section clauses (possible deduplication results)
    const multiSectionClauses = clauses.filter(c => 
      c.metadata?.sections && c.metadata.sections.length > 1
    );

    if (multiSectionClauses.length > 0) {
      console.log('\nDeduplicated Clauses:');
      multiSectionClauses.forEach(clause => {
        console.log(`\n  Sections: ${clause.metadata.sections.join(', ')}`);
        console.log(`  Content: ${clause.content.substring(0, 100)}...`);
        if (clause.metadata.obligations?.length) {
          console.log(`  Obligations: ${clause.metadata.obligations.join(', ')}`);
        }
        if (clause.metadata.rights?.length) {
          console.log(`  Rights: ${clause.metadata.rights.join(', ')}`);
        }
      });
    }
    console.log('\n');
  });

  // Analyze deduplication effectiveness
  const totalSections = result.clauses.reduce((count, clause) => 
    count + (clause.metadata?.sections?.length || 1), 0);
  
  const deduplicationRate = ((totalSections - result.clauses.length) / totalSections * 100).toFixed(1);
  
  console.log('Deduplication Effectiveness:');
  console.log('---------------------------');
  console.log(`Original sections: ${totalSections}`);
  console.log(`Final clauses: ${result.clauses.length}`);
  console.log(`Deduplication rate: ${deduplicationRate}%`);
  console.log('\n');

  // Check for potential missed duplicates
  console.log('Potential Missed Duplicates:');
  console.log('-------------------------');
  Object.values(clausesByType).forEach(clauses => {
    if (clauses.length > 1) {
      for (let i = 0; i < clauses.length; i++) {
        for (let j = i + 1; j < clauses.length; j++) {
          const similarity = calculateSimilarity(
            normalizeContent(clauses[i].content),
            normalizeContent(clauses[j].content)
          );
          if (similarity > 0.8) {
            console.log(`\nPossible duplicate in type: ${clauses[i].type}`);
            console.log(`Similarity: ${(similarity * 100).toFixed(1)}%`);
            console.log(`Section 1: ${clauses[i].metadata?.section}`);
            console.log(`Section 2: ${clauses[j].metadata?.section}`);
          }
        }
      }
    }
  });
}

// Simple content normalization for comparison
function normalizeContent(content) {
  return content
    .toLowerCase()
    .replace(/\s+/g, ' ')
    .trim();
}

// Simple similarity calculation
function calculateSimilarity(str1, str2) {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;
  
  if (longer.length === 0) return 1.0;
  
  return (longer.length - levenshteinDistance(longer, shorter)) / longer.length;
}

function levenshteinDistance(str1, str2) {
  const matrix = Array(str2.length + 1).fill(null)
    .map(() => Array(str1.length + 1).fill(null));

  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,
        matrix[j - 1][i] + 1,
        matrix[j - 1][i - 1] + indicator
      );
    }
  }

  return matrix[str2.length][str1.length];
}

// Run analysis if called directly
if (require.main === module) {
  analyzeDeduplications();
}

module.exports = { analyzeDeduplications };
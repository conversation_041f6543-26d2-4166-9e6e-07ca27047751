#!/bin/bash

# Make scripts executable
chmod +x test-deduplication.sh

# Set test environment
export NODE_ENV=development

# Run the test
echo "=== Starting Deduplication Test ==="
echo "--------------------------------"

# Execute test script
./test-deduplication.sh

# Analyze results
echo -e "\n=== Expected vs Actual Results ==="
echo "--------------------------------"
echo "Expected duplicate pairs:"
echo "- Confidentiality (Sections 1.1 and 2.3)"
echo "- Termination (Sections 3.1 and 5.2)"
echo "- IP Rights (Sections 6.1 and 7.2)"
echo "- Payment Terms (Sections 8.1 and 9.3)"

# Display summary
if [ -f "analysis-result.json" ]; then
    echo -e "\nDeduplication Summary:"
    echo "Initial sections: 8"
    echo "Expected final clauses: 4"
    echo "Actual results:"
    jq -r '.clauses | length' analysis-result.json
else
    echo "Error: No analysis results found"
fi
const fs = require('fs');

function verifyResults(analysisResult) {
    // Parse result if string
    const result = typeof analysisResult === 'string' 
        ? JSON.parse(analysisResult)
        : analysisResult;

    // Expected structure
    const expectedGroups = [
        {
            type: 'confidentiality',
            expectedSections: ['1.1', '2.3'],
            contentKeywords: ['confidentiality', 'information']
        },
        {
            type: 'term',
            expectedSections: ['3.1', '3.2'],
            contentKeywords: ['12 months', 'duration']
        }
    ];

    // Validation results
    const validationResults = {
        totalClauses: result.clauses.length,
        deduplicationSuccess: result.clauses.length === expectedGroups.length,
        groupValidation: []
    };

    // Verify each expected group
    expectedGroups.forEach(expected => {
        const matchingClause = result.clauses.find(clause => 
            clause.type.toLowerCase().includes(expected.type));

        const groupResult = {
            type: expected.type,
            found: !!matchingClause,
            sectionsMatch: false,
            contentMatch: false
        };

        if (matchingClause) {
            // Verify sections
            const sections = matchingClause.metadata.sections || [];
            groupResult.sectionsMatch = expected.expectedSections.every(
                section => sections.includes(section)
            );

            // Verify content keywords
            const content = matchingClause.content.toLowerCase();
            groupResult.contentMatch = expected.contentKeywords.every(
                keyword => content.includes(keyword.toLowerCase())
            );
        }

        validationResults.groupValidation.push(groupResult);
    });

    // Calculate success percentage
    const totalChecks = validationResults.groupValidation.reduce((total, group) => {
        return total + (group.found ? 3 : 1); // 3 checks if found, 1 if not
    }, 0);

    const successfulChecks = validationResults.groupValidation.reduce((total, group) => {
        if (!group.found) return total;
        return total + [
            group.found,
            group.sectionsMatch,
            group.contentMatch
        ].filter(Boolean).length;
    }, 0);

    validationResults.successPercentage = (successfulChecks / totalChecks) * 100;

    return validationResults;
}

// If run directly
if (require.main === module) {
    try {
        const resultFile = process.argv[2] || 'analysis-result.json';
        const result = JSON.parse(fs.readFileSync(resultFile, 'utf8'));
        const validation = verifyResults(result);
        
        console.log('\nValidation Results:');
        console.log('-------------------');
        console.log(`Total clauses found: ${validation.totalClauses}`);
        console.log(`Deduplication successful: ${validation.deduplicationSuccess ? 'Yes' : 'No'}`);
        console.log('\nGroup Validation:');
        validation.groupValidation.forEach(group => {
            console.log(`\n${group.type.toUpperCase()}:`);
            console.log(`- Found: ${group.found ? '✓' : '✗'}`);
            if (group.found) {
                console.log(`- Sections matched: ${group.sectionsMatch ? '✓' : '✗'}`);
                console.log(`- Content verified: ${group.contentMatch ? '✓' : '✗'}`);
            }
        });
        console.log(`\nOverall success rate: ${validation.successPercentage.toFixed(1)}%`);
    } catch (error) {
        console.error('Error validating results:', error.message);
        process.exit(1);
    }
}

module.exports = verifyResults;
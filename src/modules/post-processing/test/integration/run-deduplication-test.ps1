# PowerShell script to run and monitor deduplication test
Write-Host "Starting deduplication test..." -ForegroundColor Cyan

# Set execution policy and location
Set-Location -Path $PSScriptRoot
Set-ExecutionPolicy -Scope Process -ExecutionPolicy Bypass -Force

# Run test
try {
    Write-Host "Executing test script..." -ForegroundColor Green
    & ".\run-test.ps1"

    # Monitor for results
    if (Test-Path "analysis-result.json") {
        $results = Get-Content "analysis-result.json" | ConvertFrom-Json
        Write-Host "`nDeduplication Results:" -ForegroundColor Cyan
        Write-Host "---------------------"
        Write-Host "Initial sections: 4"
        Write-Host "Final clauses: $($results.clauses.Count)"
        Write-Host "Success rate: $([math]::Round(((4 - $results.clauses.Count) / 4) * 100, 1))%"
    } else {
        throw "Analysis results not found"
    }
} catch {
    Write-Host "Error executing test: $_" -ForegroundColor Red
    exit 1
}
# Clause Deduplication Testing

This directory contains the integration tests for the clause deduplication functionality.

## Test Structure

1. `test-endpoint.sh` - Main test script for API endpoints
2. `validate-results.js` - Results validation and analysis
3. `expected-results.json` - Expected test outcomes
4. `run-full-test.sh` - Complete test orchestration

## Expected Behavior

The test will verify:
- Upload and analysis of test document
- Deduplication of similar clauses:
  - Confidentiality (Sections 1.1 and 2.3)
  - Term/Duration (Sections 3.1 and 3.2)
- Metadata merging and preservation
- Section consolidation

## Running Tests

```bash
./run-full-test.sh
```

The output will show:
- Initial number of sections
- Final number of unique clauses
- Deduplication effectiveness
- Detailed analysis of merged clauses
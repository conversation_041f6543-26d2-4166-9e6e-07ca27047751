# Install required dependencies using winget
Write-Host "Installing required dependencies..."

# Install jq
if (!(Get-Command jq -ErrorAction SilentlyContinue)) {
    Write-Host "Installing jq..."
    winget install jq
}

# Install curl if not present
if (!(Get-Command curl -ErrorAction SilentlyContinue)) {
    Write-Host "Installing curl..."
    winget install curl
}

Write-Host "Creating test directories..."
# Create directories
New-Item -ItemType Directory -Force -Path "temp"
New-Item -ItemType Directory -Force -Path "results"

Write-Host "Setting execution policy for test scripts..."
# Allow script execution
Set-ExecutionPolicy -Scope CurrentUser -ExecutionPolicy RemoteSigned -Force

Write-Host "Setup complete. You can now run the tests."
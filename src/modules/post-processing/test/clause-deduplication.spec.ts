import { DocumentClause } from '../../../common/interfaces/document-analysis.interface';
import {
  normalizeContent,
  calculateSimilarity,
  mergeClauseMetadata,
  findSimilarClause,
  extractSectionInfo
} from '../services/clause-deduplication.util';

describe('Clause Deduplication Utils', () => {
  describe('normalizeContent', () => {
    it('should normalize whitespace and punctuation', () => {
      expect(normalizeContent('  This   is.  a,test!  ')).toBe('this is a test');
    });

    it('should handle complex punctuation and special characters', () => {
      expect(normalizeContent('Section 1.2: Important; (test) [clause]')).toBe('section 1 2 important test clause');
      expect(normalizeContent('First-class/premium service*')).toBe('first class premium service');
    });

    it('should preserve numbers and alphanumeric identifiers', () => {
      expect(normalizeContent('Article 123a')).toBe('article 123a');
      expect(normalizeContent('Section A-1.2.3')).toBe('section a 1 2 3');
    });

    it('should handle empty or null input', () => {
      expect(normalizeContent('')).toBe('');
      expect(normalizeContent(null)).toBe('');
      expect(normalizeContent(undefined)).toBe('');
    });
  });

  describe('calculateSimilarity', () => {
    it('should return 1 for identical strings', () => {
      expect(calculateSimilarity('test', 'test')).toBe(1);
    });

    it('should handle minor variations', () => {
      const testCases = [
        { str1: 'test string', str2: 'test strng', expectedMin: 0.8 },
        { str1: 'confidentiality clause', str2: 'confidentialiti clause', expectedMin: 0.85 },
        { str1: 'agreement terms', str2: 'agrement terms', expectedMin: 0.8 }
      ];

      testCases.forEach(({ str1, str2, expectedMin }) => {
        const similarity = calculateSimilarity(str1, str2);
        expect(similarity).toBeGreaterThan(expectedMin);
      });
    });

    it('should detect significant differences', () => {
      const testCases = [
        { str1: 'confidential information', str2: 'payment terms' },
        { str1: 'termination clause', str2: 'renewal terms' },
        { str1: 'intellectual property', str2: 'dispute resolution' }
      ];

      testCases.forEach(({ str1, str2 }) => {
        const similarity = calculateSimilarity(str1, str2);
        expect(similarity).toBeLessThan(0.5);
      });
    });

    it('should handle empty strings', () => {
      expect(calculateSimilarity('', '')).toBe(1);
      expect(calculateSimilarity('test', '')).toBe(0);
      expect(calculateSimilarity('', 'test')).toBe(0);
    });
  });

  describe('mergeClauseMetadata', () => {
    it('should merge arrays without duplicates', () => {
      const base = {
        obligations: ['obligation1', 'obligation2'],
        rights: ['right1'],
        restrictions: ['restriction1']
      };
      const updates = {
        obligations: ['obligation2', 'obligation3'],
        rights: ['right2'],
        restrictions: ['restriction1', 'restriction2']
      };

      const result = mergeClauseMetadata(base, updates);
      expect(result.obligations).toEqual(['obligation1', 'obligation2', 'obligation3']);
      expect(result.rights).toEqual(['right1', 'right2']);
      expect(result.restrictions).toEqual(['restriction1', 'restriction2']);
    });

    it('should handle complex definitions merging', () => {
      const base = {
        definitions: {
          'term1': 'definition1',
          'shared_term': 'original definition'
        }
      };
      const updates = {
        definitions: {
          'term2': 'definition2',
          'shared_term': 'updated definition'
        }
      };

      const result = mergeClauseMetadata(base, updates);
      expect(result.definitions).toEqual({
        'term1': 'definition1',
        'term2': 'definition2',
        'shared_term': 'original definition' // Should preserve original definition
      });
    });

    it('should handle section conflicts correctly', () => {
      const testCases = [
        {
          base: { section: '1.1' },
          updates: { section: '1.2' },
          expected: '1.1'
        },
        {
          base: { section: undefined },
          updates: { section: '2.1' },
          expected: '2.1'
        },
        {
          base: { section: '3.1' },
          updates: { section: undefined },
          expected: '3.1'
        }
      ];

      testCases.forEach(({ base, updates, expected }) => {
        const result = mergeClauseMetadata(base, updates);
        expect(result.section).toBe(expected);
      });
    });

    it('should clean up empty fields', () => {
      const base = {
        obligations: [],
        rights: ['right1'],
        definitions: {},
        section: '1.1'
      };
      const updates = {
        rights: [],
        restrictions: [],
        definitions: { term1: 'def1' }
      };

      const result = mergeClauseMetadata(base, updates);
      expect(result.obligations).toBeUndefined();
      expect(result.rights).toEqual(['right1']);
      expect(result.restrictions).toBeUndefined();
      expect(result.definitions).toEqual({ term1: 'def1' });
      expect(result.section).toBe('1.1');
    });
  });

  describe('findSimilarClause', () => {
    const clauses: DocumentClause[] = [
      {
        type: 'confidentiality',
        content: 'The Contractor shall maintain strict confidentiality',
        metadata: { section: '5.1' }
      },
      {
        type: 'termination',
        content: 'This agreement may be terminated with 30 days notice',
        metadata: { section: '8.1' }
      }
    ];

    it('should find similar clauses with high content similarity', () => {
      const testClauses: DocumentClause[] = [
        {
          type: 'confidentiality',
          content: 'Contractor must maintain strict confidentiality of information',
          metadata: { section: '5.2' }
        },
        {
          type: 'confidentiality',
          content: 'All confidential information must be protected',
          metadata: { section: '5.3' }
        }
      ];

      testClauses.forEach(clause => {
        const similar = findSimilarClause(clause, clauses);
        expect(similar).toBeTruthy();
        expect(similar.type).toBe('confidentiality');
      });
    });

    it('should respect type matching when finding similar clauses', () => {
      const clause: DocumentClause = {
        type: 'termination',
        content: 'The Contractor shall maintain strict confidentiality',
        metadata: { section: '8.2' }
      };

      const similar = findSimilarClause(clause, clauses);
      expect(similar?.type).toBe('termination');
    });

    it('should handle edge cases', () => {
      expect(findSimilarClause(null, clauses)).toBeNull();
      expect(findSimilarClause(undefined, clauses)).toBeNull();
      expect(findSimilarClause({ type: 'test', content: '' } as DocumentClause, [])).toBeNull();
      expect(findSimilarClause({ type: 'test', content: '' } as DocumentClause, null)).toBeNull();
    });
  });

  describe('extractSectionInfo', () => {
    it('should extract various section formats', () => {
      const testCases = [
        { input: 'Section 1.2.3', expected: 'Section 1.2.3' },
        { input: 'Article IV', expected: 'Section IV' },
        { input: '2.1 Content', expected: 'Section 2.1' },
        { input: '(a) Content', expected: 'Section A' },
        { input: 'Section A-1.2', expected: 'Section A-1.2' }
      ];

      testCases.forEach(({ input, expected }) => {
        expect(extractSectionInfo(input)).toBe(expected);
      });
    });

    it('should handle invalid or missing sections', () => {
      const invalidCases = [
        '',
        'No section here',
        'Random text 123',
        null,
        undefined
      ];

      invalidCases.forEach(input => {
        expect(extractSectionInfo(input)).toBeNull();
      });
    });
  });
});
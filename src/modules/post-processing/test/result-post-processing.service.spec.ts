import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { ResultPostProcessingService } from '../services/result-post-processing.service';
import { DocumentAnalysisResult, DocumentClause } from '../../../common/interfaces/document-analysis.interface';

describe('ResultPostProcessingService', () => {
  let service: ResultPostProcessingService;

  async function processAndGetSummary(mockResult: DocumentAnalysisResult): Promise<string> {
    const { data } = await service.processAnalysisResult(mockResult);
    if ('error' in data) {
      throw new Error('Expected DocumentAnalysisResult but got ErrorResponse');
    }
    return data.summary || '';
  }

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ResultPostProcessingService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue({}),
          },
        },
      ],
    }).compile();

    service = module.get<ResultPostProcessingService>(ResultPostProcessingService);
  });

  describe('ensure-quality-summary rule', () => {
    it('should generate comprehensive summary with parties and risks', async () => {
      const mockResult: DocumentAnalysisResult = {
        documentType: 'Contract',
        parties: [
          { name: 'Acme Corp', role: 'Client' },
          { name: 'John Doe', role: 'Contractor' },
        ],
        effectiveDate: '2024-01-01',
        clauses: [
          {
            title: 'Non-Compete',
            content: 'Contractor shall not compete...',
            riskLevel: 'high',
            riskDescription: 'Broad non-compete clause',
            metadata: {
              obligations: ['Do not compete with client', 'Report potential conflicts']
            }
          },
          {
            title: 'Payment Terms',
            content: 'Payment shall be made...',
            riskLevel: 'medium',
            riskDescription: 'Unclear payment schedule',
            metadata: {
              obligations: ['Submit invoices monthly']
            }
          }
        ],
        governingLaw: 'State of California',
      };

      const { data } = await service.processAnalysisResult(mockResult);
      if ('error' in data) {
        fail('Expected DocumentAnalysisResult but got ErrorResponse');
      }
      const summary = data.summary;

      // Basic information
      expect(summary).toContain('Contract');
      expect(summary).toContain('Acme Corp (Client) and John Doe (Contractor)');
      expect(summary).toContain('effective from 2024-01-01');
      expect(summary).toContain('governed by State of California');

      // Risk analysis
      expect(summary).toContain('high-risk clauses');
      expect(summary).toContain('Non-Compete');
      expect(summary).toContain('medium-risk provisions');
      expect(summary).toContain('Payment Terms');

      // Obligations
      expect(summary).toContain('KEY OBLIGATIONS');
      expect(summary).toContain('Do not compete with client');
      expect(summary).toContain('Submit invoices monthly');
    });

    it('should handle minimal information and provide document structure', async () => {
      const mockResult: DocumentAnalysisResult = {
        sections: [
          {
            title: 'Introduction',
            content: 'This agreement...',
            purpose: 'Sets context for the agreement',
            startIndex: 0,
            endIndex: 100
          },
          {
            title: 'Terms',
            content: 'The terms are...',
            purpose: 'Defines key terms',
            startIndex: 101,
            endIndex: 200
          }
        ]
      };

      const { data } = await service.processAnalysisResult(mockResult);
      if ('error' in data) {
        fail('Expected DocumentAnalysisResult but got ErrorResponse');
      }
      const summary = data.summary;

      // Should contain structure information
      expect(summary).toContain('DOCUMENT STRUCTURE');
      expect(summary).toContain('Introduction: Sets context for the agreement');
      expect(summary).toContain('Terms: Defines key terms');
    });

    it('should prioritize risks and provide clear recommendations', async () => {
      const mockResult: DocumentAnalysisResult = {
        documentType: 'Agreement',
        clauses: [
          {
            title: 'Indemnification',
            content: 'Party shall indemnify...',
            riskLevel: 'high',
            riskDescription: 'Broad indemnification clause'
          },
          {
            title: 'Liability Cap',
            content: 'Liability is limited...',
            riskLevel: 'high',
            riskDescription: 'Very low liability cap'
          },
          {
            title: 'Notice Period',
            content: 'Notice must be given...',
            riskLevel: 'medium',
            riskDescription: 'Short notice period'
          }
        ]
      };

      const { data } = await service.processAnalysisResult(mockResult);
      if ('error' in data) {
        fail('Expected DocumentAnalysisResult but got ErrorResponse');
      }
      const summary = data.summary;

      // Risk sections should be clearly separated
      expect(summary).toContain('HIGH PRIORITY');
      expect(summary).toContain('MEDIUM PRIORITY');
      
      // High-risk items should be listed first
      const highRiskIndex = summary.indexOf('HIGH PRIORITY');
      const mediumRiskIndex = summary.indexOf('MEDIUM PRIORITY');
      expect(highRiskIndex).toBeLessThan(mediumRiskIndex);

      // Should use warning emoji for risks
      expect(summary).toMatch(/⚠️.*Indemnification/);
      expect(summary).toMatch(/⚠️.*Liability Cap/);
    });
  });
});

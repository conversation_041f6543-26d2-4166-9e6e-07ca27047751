import { Injectable, Logger } from '@nestjs/common';
import { 
  DocumentContext, 
  DocumentGroup, 
  DocumentRelationship, 
  DocumentRelationshipType,
  DocumentContextQueryOptions,
} from '../../../common/interfaces/document-context.interface';
import { v4 as uuidv4 } from 'uuid';

/**
 * Service for managing document context and relationships
 * for multi-document analysis
 */
@Injectable()
export class DocumentContextService {
  private readonly logger = new Logger(DocumentContextService.name);
  
  // In-memory storage for document contexts, relationships and groups
  // These would be moved to a database in a production implementation
  private documentContexts: Map<string, DocumentContext> = new Map();
  private documentRelationships: Map<string, DocumentRelationship[]> = new Map();
  private documentGroups: Map<string, DocumentGroup> = new Map();
  
  /**
   * Creates a new document context
   * @param primaryDocumentId The primary document ID
   * @param relatedDocumentIds Optional related document IDs
   * @param groupId Optional group ID
   * @returns The created document context
   */
  async createContext(
    primaryDocumentId: string, 
    relatedDocumentIds: string[] = [], 
    groupId?: string
  ): Promise<DocumentContext> {
    this.logger.log(`Creating context for document ${primaryDocumentId} with ${relatedDocumentIds.length} related documents`);
    
    // Create a new context
    const context: DocumentContext = {
      primaryDocumentId,
      relatedDocumentIds,
      groupId,
      relationships: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    // Store the context
    this.documentContexts.set(primaryDocumentId, context);
    
    // If related documents are provided, create relationships
    if (relatedDocumentIds.length > 0) {
      for (const relatedDocumentId of relatedDocumentIds) {
        await this.addRelationship(
          primaryDocumentId,
          relatedDocumentId,
          DocumentRelationshipType.RELATED
        );
      }
    }
    
    return context;
  }
  
  /**
   * Retrieves a document context
   * @param documentId The document ID
   * @param options Query options
   * @returns The document context
   */
  async getContext(
    documentId: string, 
    options: DocumentContextQueryOptions = {}
  ): Promise<DocumentContext | null> {
    const context = this.documentContexts.get(documentId);
    
    if (!context) {
      return null;
    }
    
    // Apply query options
    if (options.relationshipTypes) {
      // Filter relationships by type
      if (context.relationships) {
        context.relationships = context.relationships.filter(
          rel => options.relationshipTypes.includes(rel.relationshipType)
        );
      }
    }
    
    // Apply depth limiting if specified
    if (typeof options.maxDepth === 'number') {
      // This would be a more complex implementation with recursive relationship traversal
      // Simplified version:
      if (options.maxDepth === 0) {
        context.relatedDocumentIds = [];
        context.relationships = [];
      }
    }
    
    return context;
  }
  
  /**
   * Adds a relationship between documents
   * @param sourceDocumentId Source document ID
   * @param targetDocumentId Target document ID
   * @param relationshipType Type of relationship
   * @param description Optional description
   * @returns The created relationship
   */
  async addRelationship(
    sourceDocumentId: string,
    targetDocumentId: string,
    relationshipType: DocumentRelationshipType,
    description?: string
  ): Promise<DocumentRelationship> {
    this.logger.log(`Adding ${relationshipType} relationship from ${sourceDocumentId} to ${targetDocumentId}`);
    
    // Create the relationship
    const relationship: DocumentRelationship = {
      sourceDocumentId,
      targetDocumentId,
      relationshipType,
      description,
      createdAt: new Date(),
    };
    
    // Add to source document's relationships
    const sourceRelationships = this.documentRelationships.get(sourceDocumentId) || [];
    sourceRelationships.push(relationship);
    this.documentRelationships.set(sourceDocumentId, sourceRelationships);
    
    // Update the document context
    const context = this.documentContexts.get(sourceDocumentId);
    if (context) {
      if (!context.relationships) {
        context.relationships = [];
      }
      context.relationships.push(relationship);
      
      if (!context.relatedDocumentIds.includes(targetDocumentId)) {
        context.relatedDocumentIds.push(targetDocumentId);
      }
      
      context.updatedAt = new Date();
      this.documentContexts.set(sourceDocumentId, context);
    }
    
    return relationship;
  }
  
  /**
   * Removes a relationship between documents
   * @param sourceDocumentId Source document ID
   * @param targetDocumentId Target document ID
   * @param relationshipType Optional relationship type to remove specific type
   * @returns True if relationship was removed
   */
  async removeRelationship(
    sourceDocumentId: string,
    targetDocumentId: string,
    relationshipType?: DocumentRelationshipType
  ): Promise<boolean> {
    this.logger.log(`Removing relationship from ${sourceDocumentId} to ${targetDocumentId}`);
    
    // Get source relationships
    const sourceRelationships = this.documentRelationships.get(sourceDocumentId) || [];
    
    // Filter out the relationships to remove
    const filteredRelationships = sourceRelationships.filter(rel => {
      if (rel.targetDocumentId !== targetDocumentId) {
        return true;
      }
      
      if (relationshipType && rel.relationshipType !== relationshipType) {
        return true;
      }
      
      return false;
    });
    
    // If no relationships were removed
    if (filteredRelationships.length === sourceRelationships.length) {
      return false;
    }
    
    // Update the relationships
    this.documentRelationships.set(sourceDocumentId, filteredRelationships);
    
    // Update the document context
    const context = this.documentContexts.get(sourceDocumentId);
    if (context && context.relationships) {
      context.relationships = context.relationships.filter(rel => {
        if (rel.targetDocumentId !== targetDocumentId) {
          return true;
        }
        
        if (relationshipType && rel.relationshipType !== relationshipType) {
          return true;
        }
        
        return false;
      });
      
      // Check if we need to remove from relatedDocumentIds
      if (!filteredRelationships.some(rel => rel.targetDocumentId === targetDocumentId)) {
        context.relatedDocumentIds = context.relatedDocumentIds.filter(
          id => id !== targetDocumentId
        );
      }
      
      context.updatedAt = new Date();
      this.documentContexts.set(sourceDocumentId, context);
    }
    
    return true;
  }
  
  /**
   * Creates a new document group
   * @param name Group name
   * @param documentIds Document IDs in the group
   * @param description Optional description
   * @returns The created document group
   */
  async createGroup(
    name: string,
    documentIds: string[] = [],
    description?: string
  ): Promise<DocumentGroup> {
    this.logger.log(`Creating document group "${name}" with ${documentIds.length} documents`);
    
    const groupId = uuidv4();
    
    // Create the group
    const group: DocumentGroup = {
      id: groupId,
      name,
      description,
      documentIds,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    // Store the group
    this.documentGroups.set(groupId, group);
    
    // Create relationships between documents in the group
    if (documentIds.length > 1) {
      for (let i = 0; i < documentIds.length; i++) {
        for (let j = i + 1; j < documentIds.length; j++) {
          await this.addRelationship(
            documentIds[i],
            documentIds[j],
            DocumentRelationshipType.PART_OF,
            `Part of group: ${name}`
          );
          
          // Create bidirectional relationship
          await this.addRelationship(
            documentIds[j],
            documentIds[i],
            DocumentRelationshipType.PART_OF,
            `Part of group: ${name}`
          );
        }
      }
    }
    
    return group;
  }
  
  /**
   * Adds a document to a group
   * @param groupId Group ID
   * @param documentId Document ID to add
   * @returns True if document was added
   */
  async addDocumentToGroup(groupId: string, documentId: string): Promise<boolean> {
    const group = this.documentGroups.get(groupId);
    
    if (!group) {
      return false;
    }
    
    if (group.documentIds.includes(documentId)) {
      return false;
    }
    
    // Add document to group
    group.documentIds.push(documentId);
    group.updatedAt = new Date();
    this.documentGroups.set(groupId, group);
    
    // Create relationships with other documents in the group
    for (const existingDocumentId of group.documentIds) {
      if (existingDocumentId !== documentId) {
        await this.addRelationship(
          documentId,
          existingDocumentId,
          DocumentRelationshipType.PART_OF,
          `Part of group: ${group.name}`
        );
        
        await this.addRelationship(
          existingDocumentId,
          documentId,
          DocumentRelationshipType.PART_OF,
          `Part of group: ${group.name}`
        );
      }
    }
    
    return true;
  }
  
  /**
   * Retrieves all documents that have a relationship with the given document
   * @param documentId Document ID
   * @param relationshipTypes Optional filter by relationship types
   * @returns List of related document IDs
   */
  async getRelatedDocuments(
    documentId: string,
    relationshipTypes?: DocumentRelationshipType[]
  ): Promise<string[]> {
    const context = await this.getContext(documentId);
    
    if (!context) {
      return [];
    }
    
    // If no relationships, return related document IDs directly
    if (!context.relationships || context.relationships.length === 0) {
      return context.relatedDocumentIds;
    }
    
    // Filter by relationship type if provided
    if (relationshipTypes && relationshipTypes.length > 0) {
      const filteredRelationships = context.relationships.filter(
        rel => relationshipTypes.includes(rel.relationshipType)
      );
      
      return filteredRelationships.map(rel => rel.targetDocumentId);
    }
    
    return context.relatedDocumentIds;
  }
  
  /**
   * Finds groups that contain a specific document
   * @param documentId Document ID
   * @returns List of document groups
   */
  async getGroupsForDocument(documentId: string): Promise<DocumentGroup[]> {
    const groups: DocumentGroup[] = [];
    
    for (const group of this.documentGroups.values()) {
      if (group.documentIds.includes(documentId)) {
        groups.push(group);
      }
    }
    
    return groups;
  }
}

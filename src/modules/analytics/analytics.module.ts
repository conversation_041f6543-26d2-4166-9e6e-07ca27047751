import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AnalyticsController } from './controllers/analytics.controller';
import { AnalyticsService } from './services/analytics.service';
import { AnalyticsSeederService } from './services/analytics-seeder.service';
import { AnalyticsCollectionService } from './services/analytics-collection.service';
import { ChatAnalytics, ChatAnalyticsSchema } from '../chat/schemas/chat-analytics.schema';
import { User, UserSchema } from '../auth/schemas/user.schema';
import { DocumentsModule } from '../documents/documents.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ChatAnalytics.name, schema: ChatAnalyticsSchema },
      { name: User.name, schema: UserSchema }
    ]),
    forwardRef(() => DocumentsModule),
    forwardRef(() => SubscriptionModule),
    forwardRef(() => AuthModule)
  ],
  controllers: [AnalyticsController],
  providers: [AnalyticsService, AnalyticsSeederService, AnalyticsCollectionService],
  exports: [AnalyticsService, AnalyticsSeederService, AnalyticsCollectionService]
})
export class AnalyticsModule {}

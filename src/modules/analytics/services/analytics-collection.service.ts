import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ChatAnalytics, ChatAnalyticsDocument } from '../../chat/schemas/chat-analytics.schema';
import { Document, DOCUMENT_MODEL } from '../../documents/schemas/document.schema';
import { PostHogService } from '../../posthog/services/posthog.service';
import { v4 as uuidv4 } from 'uuid';

/**
 * Service responsible for collecting analytics data during normal system operation
 */
@Injectable()
export class AnalyticsCollectionService {
  private readonly logger = new Logger(AnalyticsCollectionService.name);

  constructor(
    @InjectModel(ChatAnalytics.name)
    private readonly chatAnalyticsModel: Model<ChatAnalyticsDocument>,
    @InjectModel(DOCUMENT_MODEL)
    private readonly documentModel: Model<Document>,
    private readonly postHogService: PostHogService
  ) {}

  /**
   * Record a document upload event
   */
  async recordDocumentUpload(
    documentId: string,
    organizationId: string,
    userId: string,
    documentType: string,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    try {
      // Record in MongoDB (existing functionality)
      await this.documentModel.updateOne(
        { id: documentId },
        {
          $set: {
            uploadDate: new Date(),
            documentType,
            uploadedBy: userId,
            organizationId
          }
        }
      );
      
      // Record in PostHog
      this.postHogService.trackDocumentUpload(userId, documentId, documentType, {
        organization_id: organizationId,
        file_size: metadata.fileSize,
        file_name: metadata.fileName,
        ...metadata
      });
      
      this.logger.log(`Recorded document upload for document ${documentId} in organization ${organizationId}`);
    } catch (error) {
      this.logger.error(`Error recording document upload: ${error.message}`);
    }
  }

  /**
   * Record a document analysis event
   */
  async recordDocumentAnalysis(
    documentId: string,
    organizationId: string,
    userId: string,
    analysisStartTime: Date,
    analysisEndTime: Date,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    try {
      // Record in MongoDB (existing functionality)
      await this.documentModel.updateOne(
        { id: documentId },
        {
          $set: {
            analyzed: true,
            analysisStartTime,
            analysisEndTime,
            analyzedBy: userId
          }
        }
      );
      
      // Record in PostHog
      const duration = analysisEndTime.getTime() - analysisStartTime.getTime();
      this.postHogService.trackDocumentAnalysis(
        userId,
        documentId,
        metadata.analysisType || 'standard',
        duration,
        {
          organization_id: organizationId,
          analysis_start_time: analysisStartTime.toISOString(),
          analysis_end_time: analysisEndTime.toISOString(),
          ...metadata
        }
      );
      
      this.logger.log(`Recorded document analysis for document ${documentId} in organization ${organizationId}`);
    } catch (error) {
      this.logger.error(`Error recording document analysis: ${error.message}`);
    }
  }

  /**
   * Record a document comparison event
   */
  async recordDocumentComparison(
    documentId1: string,
    documentId2: string,
    organizationId: string,
    userId: string,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    const now = new Date();
    const comparisonLevel = metadata?.comparisonLevel || 'basic';
    
    try {
      // Prepare the comparison history entry
      const comparisonEntry = {
        comparedWith: comparisonLevel === 'basic' ? documentId2 : documentId1,
        comparedAt: now,
        comparedBy: userId,
        comparisonLevel: comparisonLevel,
        comparisonType: metadata?.comparisonType || 'both',
        metadata: { ...metadata }
      };

      // Update both documents with comparison information
      await Promise.all([
        this.documentModel.updateOne(
          { id: documentId1 },
          {
            $set: {
              comparedAt: now,
              comparedBy: userId
            },
            $push: {
              comparisonHistory: comparisonEntry
            }
          }
        ),
        this.documentModel.updateOne(
          { id: documentId2 },
          {
            $set: {
              comparedAt: now,
              comparedBy: userId
            },
            $push: {
              comparisonHistory: {
                ...comparisonEntry,
                comparedWith: documentId1 // Swap the document IDs for the second document
              }
            }
          }
        )
      ]);
      
      // Record in PostHog
      this.postHogService.trackDocumentComparison(
        userId,
        [documentId1, documentId2],
        comparisonLevel,
        {
          organization_id: organizationId,
          comparison_type: metadata?.comparisonType || 'both',
          ...metadata
        }
      );
      
      this.logger.log(`Recorded ${comparisonLevel} document comparison between documents ${documentId1} and ${documentId2} in organization ${organizationId}`);
    } catch (error) {
      this.logger.error(`Error recording document comparison: ${error.message}`);
    }
  }

  /**
   * Record a user query event
   */
  async recordUserQuery(
    sessionId: string,
    organizationId: string,
    userId: string,
    queryText: string,
    responseText: string,
    responseTime: number,
    commonTopics: string[] = [],
    metadata: Record<string, any> = {}
  ): Promise<void> {
    try {
      const chatAnalytics = new this.chatAnalyticsModel({
        id: uuidv4(),
        sessionId,
        organizationId,
        userId,
        queryText,
        responseText,
        responseTime,
        timestamp: new Date(),
        commonTopics,
        metadata
      });
      
      await chatAnalytics.save();
      
      // Record in PostHog
      this.postHogService.trackEvent(userId, 'user_query', {
        session_id: sessionId,
        organization_id: organizationId,
        query_length: queryText.length,
        response_length: responseText.length,
        response_time_ms: responseTime,
        common_topics: commonTopics,
        chat_analytics_id: chatAnalytics.id,
        ...metadata
      });
      
      this.logger.log(`Recorded user query for session ${sessionId} in organization ${organizationId}`);
    } catch (error) {
      this.logger.error(`Error recording user query: ${error.message}`);
    }
  }

  /**
   * Record user feedback on a query response
   */
  async recordQueryFeedback(
    chatAnalyticsId: string,
    helpful: boolean,
    rating?: number,
    comment?: string
  ): Promise<void> {
    try {
      await this.chatAnalyticsModel.updateOne(
        { id: chatAnalyticsId },
        {
          $set: {
            feedback: {
              helpful,
              rating,
              comment
            }
          }
        }
      );
      
      // Record in PostHog - we'll need to get the userId from the method parameters
      // Since the chatAnalytics schema doesn't store userId/organizationId directly,
      // we'll track this as a separate method that accepts userId
      // For now, we'll skip PostHog tracking for feedback unless we have the userId
      
      this.logger.log(`Recorded feedback for chat analytics ${chatAnalyticsId}`);
    } catch (error) {
      this.logger.error(`Error recording query feedback: ${error.message}`);
    }
  }

  /**
   * Record user feedback on a query response with PostHog tracking
   */
  async recordQueryFeedbackWithUser(
    chatAnalyticsId: string,
    userId: string,
    organizationId: string,
    helpful: boolean,
    rating?: number,
    comment?: string
  ): Promise<void> {
    try {
      // Update MongoDB
      await this.recordQueryFeedback(chatAnalyticsId, helpful, rating, comment);
      
      // Record in PostHog
      this.postHogService.trackEvent(userId, 'query_feedback', {
        chat_analytics_id: chatAnalyticsId,
        helpful,
        rating,
        comment,
        organization_id: organizationId
      });
      
      this.logger.log(`Recorded feedback with PostHog tracking for chat analytics ${chatAnalyticsId}`);
    } catch (error) {
      this.logger.error(`Error recording query feedback with PostHog: ${error.message}`);
    }
  }

  /**
   * Record a document version creation event
   */
  async recordDocumentVersionCreation(
    documentId: string,
    versionNumber: number,
    userId: string,
    timestamp: Date = new Date()
  ): Promise<void> {
    try {
      // Record the event in analytics
      await this.documentModel.updateOne(
        { id: documentId },
        {
          $push: {
            'analytics.versionHistory': {
              versionNumber,
              createdBy: userId,
              createdAt: timestamp
            }
          },
          $inc: {
            'analytics.totalVersions': 1
          }
        }
      );
      
      // Record in PostHog
      this.postHogService.trackEvent(userId, 'document_version_created', {
        document_id: documentId,
        version_number: versionNumber,
        created_at: timestamp.toISOString()
      });
      
      this.logger.log(`Recorded version ${versionNumber} creation for document ${documentId}`);
    } catch (error) {
      this.logger.error(`Error recording document version creation: ${error.message}`);
    }
  }

  /**
   * Record a document deletion event
   */
  async recordDocumentDeletion(
    documentId: string,
    organizationId: string,
    userId: string,
    timestamp: Date = new Date()
  ): Promise<void> {
    try {
      // Since the document is being deleted, we'll record this event in a separate collection
      // This helps maintain analytics data even after document deletion
      await this.chatAnalyticsModel.create({
        id: uuidv4(),
        organizationId,
        userId,
        eventType: 'document_deletion',
        timestamp,
        metadata: {
          documentId,
          deletedAt: timestamp
        }
      });
      
      // Record in PostHog
      this.postHogService.trackEvent(userId, 'document_deleted', {
        document_id: documentId,
        organization_id: organizationId,
        deleted_at: timestamp.toISOString()
      });
      
      this.logger.log(`Recorded document deletion for document ${documentId} in organization ${organizationId}`);
    } catch (error) {
      this.logger.error(`Error recording document deletion: ${error.message}`);
    }
  }

  /**
   * Extract common topics from a query using basic keyword matching
   * In a production environment, this would be replaced with a more sophisticated
   * NLP-based topic extraction system
   */
  extractCommonTopics(queryText: string): string[] {
    const topicKeywords = {
      'Contract Analysis': ['contract', 'agreement', 'clause', 'provision', 'term'],
      'Legal Research': ['research', 'precedent', 'case law', 'statute', 'regulation'],
      'Compliance': ['compliance', 'regulation', 'requirement', 'standard', 'guideline'],
      'Litigation': ['litigation', 'lawsuit', 'case', 'plaintiff', 'defendant', 'court'],
      'Intellectual Property': ['intellectual property', 'patent', 'trademark', 'copyright', 'ip'],
      'Employment Law': ['employment', 'labor', 'worker', 'employee', 'employer', 'workplace'],
      'Corporate Law': ['corporate', 'company', 'shareholder', 'board', 'director', 'officer'],
      'Tax Law': ['tax', 'taxation', 'irs', 'revenue', 'deduction', 'exemption'],
      'Real Estate': ['real estate', 'property', 'lease', 'tenant', 'landlord', 'zoning'],
      'Environmental Law': ['environmental', 'pollution', 'compliance', 'epa', 'regulation'],
      'Criminal Law': ['criminal', 'defense', 'prosecution', 'charge', 'offense', 'felony'],
      'Family Law': ['family', 'divorce', 'custody', 'support', 'marriage', 'adoption']
    };
    
    const normalizedQuery = queryText.toLowerCase();
    const matchedTopics = [];
    
    for (const [topic, keywords] of Object.entries(topicKeywords)) {
      for (const keyword of keywords) {
        if (normalizedQuery.includes(keyword.toLowerCase())) {
          matchedTopics.push(topic);
          break;
        }
      }
    }
    
    return matchedTopics;
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ChatAnalytics, ChatAnalyticsDocument } from '../../chat/schemas/chat-analytics.schema';
import { Document, DOCUMENT_MODEL } from '../../documents/schemas/document.schema';
import { User } from '../../auth/schemas/user.schema';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class AnalyticsSeederService {
  private readonly logger = new Logger(AnalyticsSeederService.name);

  constructor(
    @InjectModel(ChatAnalytics.name)
    private readonly chatAnalyticsModel: Model<ChatAnalyticsDocument>,
    @InjectModel(DOCUMENT_MODEL)
    private readonly documentModel: Model<Document>,
    @InjectModel(User.name)
    private readonly userModel: Model<User>
  ) {}

  /**
   * Seed analytics data for demonstration purposes
   */
  async seedAnalyticsData(organizationId: string): Promise<void> {
    this.logger.log(`Seeding analytics data for organization: ${organizationId}`);
    
    // Check if we already have seeded data for this organization
    const existingData = await this.chatAnalyticsModel.countDocuments({
      organizationId
    });
    
    if (existingData > 20) {
      this.logger.log(`Organization ${organizationId} already has seeded data. Skipping.`);
      return;
    }
    
    // Seed document data
    await this.seedDocumentData(organizationId);
    
    // Seed chat analytics data
    await this.seedChatAnalyticsData(organizationId);
    
    // Seed user activity data
    await this.seedUserActivityData(organizationId);
    
    this.logger.log(`Successfully seeded analytics data for organization: ${organizationId}`);
  }
  
  /**
   * Seed document data
   */
  private async seedDocumentData(organizationId: string): Promise<void> {
    const documentTypes = ['Contract', 'Agreement', 'Legal Opinion', 'Legislation', 'Court Filing'];
    const now = new Date();
    
    // Create 50 documents with various creation dates over the last 30 days
    const documents = [];
    
    for (let i = 0; i < 50; i++) {
      const daysAgo = Math.floor(Math.random() * 30);
      const createdAt = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000);
      
      // Randomize whether the document has been analyzed or compared
      const analyzed = Math.random() > 0.3; // 70% chance of being analyzed
      const compared = Math.random() > 0.5; // 50% chance of being compared
      
      // For analyzed documents, add analysis times
      let analysisStartTime;
      let analysisEndTime;
      
      if (analyzed) {
        analysisStartTime = new Date(createdAt.getTime() + 1000 * 60 * 5); // 5 minutes after creation
        const analysisMinutes = Math.floor(Math.random() * 10) + 1; // 1-10 minutes
        analysisEndTime = new Date(analysisStartTime.getTime() + 1000 * 60 * analysisMinutes);
      }
      
      // For compared documents, add comparison time
      let comparedAt;
      
      if (compared) {
        const comparisonMinutes = Math.floor(Math.random() * 60) + 30; // 30-90 minutes after creation
        comparedAt = new Date(createdAt.getTime() + 1000 * 60 * comparisonMinutes);
      }
      
      const documentId = uuidv4();
      
      documents.push({
        documentId, // Ensure each document has a unique documentId
        id: documentId, // Use the same ID for both fields
        organizationId,
        filename: `sample-document-${i}.pdf`,
        originalName: `Sample Document ${i}.pdf`,
        type: documentTypes[Math.floor(Math.random() * documentTypes.length)],
        size: Math.floor(Math.random() * 1000000) + 50000, // 50KB to 1MB
        uploadDate: createdAt,
        createdAt,
        status: analyzed ? 'completed' : 'uploaded',
        analyzed,
        analysisStartTime,
        analysisEndTime,
        comparedAt,
        metadata: {
          pageCount: Math.floor(Math.random() * 50) + 1,
          wordCount: Math.floor(Math.random() * 10000) + 500,
          citations: Math.random() > 0.7 ? Math.floor(Math.random() * 20) : 0
        }
      });
    }
    
    try {
      // Save the documents one by one to handle potential errors
      for (const doc of documents) {
        try {
          await this.documentModel.create(doc);
        } catch (error) {
          if (error.code === 11000) {
            // Duplicate key error, skip this document
            this.logger.warn(`Skipping document with duplicate key: ${doc.documentId}`);
          } else {
            throw error;
          }
        }
      }
      this.logger.log(`Seeded documents for organization: ${organizationId}`);
    } catch (error) {
      this.logger.error(`Error seeding documents: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Seed chat analytics data
   */
  private async seedChatAnalyticsData(organizationId: string): Promise<void> {
    const now = new Date();
    const chatAnalytics = [];
    const sessionIds = [];
    
    // Create 10 session IDs
    for (let i = 0; i < 10; i++) {
      sessionIds.push(uuidv4());
    }
    
    // Create 100 chat analytics entries
    for (let i = 0; i < 100; i++) {
      const daysAgo = Math.floor(Math.random() * 30);
      const createdAt = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000);
      
      // Randomly select a session ID
      const sessionId = sessionIds[Math.floor(Math.random() * sessionIds.length)];
      
      // Generate random response time (500ms to 5000ms)
      const responseTime = Math.floor(Math.random() * 4500) + 500;
      
      // Generate random feedback
      const hasFeedback = Math.random() > 0.7; // 30% chance of having feedback
      let feedback;
      
      if (hasFeedback) {
        const isHelpful = Math.random() > 0.3; // 70% chance of being helpful
        const rating = Math.floor(Math.random() * 5) + 1; // 1-5 rating
        
        feedback = {
          helpful: isHelpful,
          rating,
          comment: isHelpful ? 'Very helpful response!' : 'Could be more detailed.'
        };
      }
      
      // Generate random topics
      const allTopics = [
        'Contract Analysis', 'Legal Research', 'Compliance', 'Litigation',
        'Intellectual Property', 'Employment Law', 'Corporate Law', 'Tax Law',
        'Real Estate', 'Environmental Law', 'Criminal Law', 'Family Law'
      ];
      
      const topicCount = Math.floor(Math.random() * 3) + 1; // 1-3 topics
      const commonTopics = [];
      
      for (let j = 0; j < topicCount; j++) {
        const randomTopic = allTopics[Math.floor(Math.random() * allTopics.length)];
        if (!commonTopics.includes(randomTopic)) {
          commonTopics.push(randomTopic);
        }
      }
      
      chatAnalytics.push({
        id: uuidv4(), // Ensure unique ID
        sessionId,
        organizationId,
        queryText: `Sample query ${i}`,
        responseText: `Sample response ${i}`,
        responseTime,
        timestamp: createdAt,
        createdAt,
        feedback,
        commonTopics,
        metadata: {
          documentId: Math.random() > 0.5 ? uuidv4() : undefined,
          citationsUsed: Math.random() > 0.7 ? Math.floor(Math.random() * 5) : 0
        }
      });
    }
    
    try {
      // Save the chat analytics in batches to handle potential errors
      const batchSize = 20;
      for (let i = 0; i < chatAnalytics.length; i += batchSize) {
        const batch = chatAnalytics.slice(i, i + batchSize);
        try {
          await this.chatAnalyticsModel.insertMany(batch, { ordered: false });
        } catch (error) {
          this.logger.warn(`Some chat analytics entries could not be inserted: ${error.message}`);
          // Continue with the next batch
        }
      }
      this.logger.log(`Seeded chat analytics for organization: ${organizationId}`);
    } catch (error) {
      this.logger.error(`Error seeding chat analytics: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Seed user activity data
   */
  private async seedUserActivityData(organizationId: string): Promise<void> {
    const now = new Date();
    const users = [];
    
    // Create 5 users
    for (let i = 0; i < 5; i++) {
      const daysAgo = Math.floor(Math.random() * 30);
      const createdAt = new Date(now.getTime() - daysAgo * 24 * 60 * 60 * 1000);
      
      // Generate random last login time
      const lastLoginDaysAgo = Math.floor(Math.random() * daysAgo);
      const lastLoginAt = new Date(now.getTime() - lastLoginDaysAgo * 24 * 60 * 60 * 1000);
      
      const userId = uuidv4();
      
      users.push({
        id: userId,
        organizationId,
        username: `testuser${i}`,
        email: `user${i}@example.com`,
        passwordHash: '$2b$10$EpRnTzVlqHNP0.fUbXUwSOyuiXe/QLSUG6xNekdHgTGmrpHEfIoxm', // bcrypt hash for 'password123'
        firstName: `Test${i}`,
        lastName: `User${i}`,
        role: i === 0 ? 'law_firm' : 'user',
        emailVerified: true,
        isActive: true,
        createdAt,
        lastLoginAt,
        preferences: {
          theme: 'light',
          notifications: true
        },
        metadata: {
          sessionCount: Math.floor(Math.random() * 20) + 1,
          documentCount: Math.floor(Math.random() * 10) + 1
        }
      });
    }
    
    try {
      // Save the users one by one to handle potential errors
      for (const user of users) {
        try {
          await this.userModel.create(user);
        } catch (error) {
          if (error.code === 11000) {
            // Duplicate key error, skip this user
            this.logger.warn(`Skipping user with duplicate key: ${user.email}`);
          } else {
            this.logger.error(`Error creating user: ${error.message}`);
          }
        }
      }
      this.logger.log(`Seeded users for organization: ${organizationId}`);
    } catch (error) {
      this.logger.error(`Error seeding users: ${error.message}`);
      throw error;
    }
  }
}

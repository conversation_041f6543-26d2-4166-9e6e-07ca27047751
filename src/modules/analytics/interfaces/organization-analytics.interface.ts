/**
 * Organization-level analytics interfaces for the Legal Document Analyzer
 */

export interface DocumentTypeMetrics {
  type: string;
  count: number;
  percentageOfTotal: number;
}

export interface ClassificationMetrics {
  automaticClassifications: number;
  manualClassifications: number;
  unclassifiedDocuments: number;
  classificationAccuracy: number;
}

export interface ComparisonMetrics {
  totalComparisons: number;
  basicComparisons: number;
  enhancedComparisons: number;
  comparisonsByType: Array<{
    type: string;
    count: number;
  }>;
}

export interface DocumentAnalysisMetrics {
  totalDocuments: number;
  analyzedDocuments: number;
  comparedDocuments: number;
  documentsByType: DocumentTypeMetrics[];
  averageAnalysisTime: number; // in seconds
  classificationMetrics?: ClassificationMetrics;
  comparisonMetrics?: ComparisonMetrics;
}

export interface UserEngagementMetrics {
  totalUsers: number;
  activeUsers: number; // users active in last 30 days
  averageSessionsPerUser: number;
  averageQueriesPerSession: number;
  feedbackStats: {
    helpfulCount: number;
    notHelpfulCount: number;
    averageRating: number;
  };
}

export interface TopicAnalysisMetrics {
  topTopics: Array<{
    topic: string;
    frequency: number;
    percentageOfTotal: number;
  }>;
  topicTrends: Array<{
    topic: string;
    trend: 'increasing' | 'decreasing' | 'stable';
    percentageChange: number;
  }>;
}

export interface CitationMetrics {
  totalCitations: number;
  topCitedCases: Array<{
    caseId: string;
    caseName: string;
    citationCount: number;
  }>;
  citationsByJurisdiction: Array<{
    jurisdiction: string;
    count: number;
    percentageOfTotal: number;
  }>;
}

export interface TimeSeriesDataPoint {
  date: string; // ISO date string
  value: number;
  count: number;
}

export interface ComparisonTimeSeriesDataPoint {
  date: string;
  count: number;
  basic?: number;
  enhanced?: number;
}

export interface ClassificationTimeSeriesDataPoint {
  date: string;
  count: number;
  automatic?: number;
  manual?: number;
}

export interface TimeSeriesMetrics {
  documentUploads: TimeSeriesDataPoint[];
  documentAnalyses: TimeSeriesDataPoint[];
  documentComparisons: ComparisonTimeSeriesDataPoint[];
  documentClassifications: ClassificationTimeSeriesDataPoint[];
  userQueries: TimeSeriesDataPoint[];
  averageResponseTimes: TimeSeriesDataPoint[];
}

export interface OrganizationAnalytics {
  organizationId: string;
  timeRange: {
    start: string; // ISO date string
    end: string; // ISO date string
  };
  documentAnalysis: DocumentAnalysisMetrics;
  userEngagement: UserEngagementMetrics;
  topicAnalysis: TopicAnalysisMetrics;
  citationMetrics: CitationMetrics;
  timeSeriesData: TimeSeriesMetrics;
  lastUpdated: string; // ISO date string
}

export interface AnalyticsTimeRange {
  start: Date;
  end: Date;
}

export interface AnalyticsQueryOptions {
  timeRange?: AnalyticsTimeRange;
  includeTimeSeriesData?: boolean;
  includeCitationMetrics?: boolean;
  includeTopicAnalysis?: boolean;
  granularity?: 'day' | 'week' | 'month';
}

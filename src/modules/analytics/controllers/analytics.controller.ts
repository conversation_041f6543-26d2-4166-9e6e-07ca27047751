import { Controller, Get, Post, Query, Param, UseGuards, UseInterceptors } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { AnalyticsService } from '../services/analytics.service';
import { AnalyticsSeederService } from '../services/analytics-seeder.service';
import { AnalyticsQueryDto, TimeGranularity } from '../dto/analytics-query.dto';
import { OrganizationAnalytics } from '../interfaces/organization-analytics.interface';
import { Organization } from '../../auth/decorators/organization.decorator';


@ApiTags('analytics')
@Controller('analytics')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AnalyticsController {
  constructor(
    private readonly analyticsService: AnalyticsService,
    private readonly analyticsSeederService: AnalyticsSeederService
  ) {}

  @Get('organization')
  @ApiOperation({ summary: 'Get organization-level analytics' })
  @ApiResponse({
    status: 200,
    description: 'Returns organization-level analytics data',
    type: Object
  })
  async getOrganizationAnalytics(
    @Organization() organizationId: string,
    @Query() queryDto: AnalyticsQueryDto
  ): Promise<OrganizationAnalytics> {
    const options = {
      timeRange: queryDto.startDate && queryDto.endDate ? {
        start: new Date(queryDto.startDate),
        end: new Date(queryDto.endDate)
      } : undefined,
      includeTimeSeriesData: queryDto.includeTimeSeriesData ?? true,
      includeCitationMetrics: queryDto.includeCitationMetrics ?? true,
      includeTopicAnalysis: queryDto.includeTopicAnalysis ?? true,
      granularity: queryDto.granularity as TimeGranularity
    };

    return this.analyticsService.getOrganizationAnalytics(organizationId, options);
  }

  @Get('dashboard')
  @ApiOperation({ summary: 'Get dashboard analytics data' })
  @ApiResponse({
    status: 200,
    description: 'Returns analytics data for the dashboard with visualization-friendly metrics',
    type: Object
  })
  async getDashboardAnalytics(
    @Organization() organizationId: string,
    @Query() queryDto: AnalyticsQueryDto
  ) {
    // Enhanced dashboard endpoint to return optimized data for visualization
    const options = {
      timeRange: queryDto.startDate && queryDto.endDate ? {
        start: new Date(queryDto.startDate),
        end: new Date(queryDto.endDate)
      } : undefined,
      includeTimeSeriesData: queryDto.includeTimeSeriesData ?? true,
      includeCitationMetrics: queryDto.includeCitationMetrics ?? true,
      includeTopicAnalysis: queryDto.includeTopicAnalysis ?? true,
      granularity: queryDto.granularity || TimeGranularity.DAY
    };

    return this.analyticsService.getDashboardData(organizationId, options);
  }

  @Get('documents')
  @ApiOperation({ summary: 'Get document analytics' })
  @ApiResponse({
    status: 200,
    description: 'Returns document analytics data',
    type: Object
  })
  async getDocumentAnalytics(
    @Organization() organizationId: string,
    @Query() queryDto: AnalyticsQueryDto
  ) {
    const options = {
      timeRange: queryDto.startDate && queryDto.endDate ? {
        start: new Date(queryDto.startDate),
        end: new Date(queryDto.endDate)
      } : undefined,
      granularity: queryDto.granularity as TimeGranularity
    };

    const analytics = await this.analyticsService.getOrganizationAnalytics(
      organizationId, 
      {
        ...options,
        includeTimeSeriesData: true,
        includeCitationMetrics: false,
        includeTopicAnalysis: false
      }
    );

    // Return only document-related data
    return {
      organizationId,
      timeRange: analytics.timeRange,
      documentAnalysis: analytics.documentAnalysis,
      timeSeriesData: {
        documentUploads: analytics.timeSeriesData.documentUploads,
        documentAnalyses: analytics.timeSeriesData.documentAnalyses,
        documentComparisons: analytics.timeSeriesData.documentComparisons
      },
      lastUpdated: analytics.lastUpdated
    };
  }

  @Get('classifications')
  @ApiOperation({ summary: 'Get document classification analytics' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns document classification analytics data',
    type: Object
  })
  async getClassificationAnalytics(
    @Organization() organizationId: string,
    @Query() queryDto: AnalyticsQueryDto
  ) {
    const options = {
      timeRange: queryDto.startDate && queryDto.endDate ? {
        start: new Date(queryDto.startDate),
        end: new Date(queryDto.endDate)
      } : undefined,
      granularity: queryDto.granularity as TimeGranularity
    };

    const analytics = await this.analyticsService.getOrganizationAnalytics(
      organizationId, 
      {
        ...options,
        includeTimeSeriesData: true,
        includeCitationMetrics: false,
        includeTopicAnalysis: false
      }
    );

    // Return only classification-related data
    return {
      organizationId,
      timeRange: analytics.timeRange,
      documentAnalysis: {
        documentsByType: analytics.documentAnalysis.documentsByType,
        classificationMetrics: analytics.documentAnalysis.classificationMetrics
      },
      timeSeriesData: {
        documentClassifications: analytics.timeSeriesData.documentClassifications
      },
      lastUpdated: analytics.lastUpdated
    };
  }

  @Get('comparisons')
  @ApiOperation({ summary: 'Get document comparison analytics' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns document comparison analytics data',
    type: Object
  })
  async getComparisonAnalytics(
    @Organization() organizationId: string,
    @Query() queryDto: AnalyticsQueryDto
  ) {
    const options = {
      timeRange: queryDto.startDate && queryDto.endDate ? {
        start: new Date(queryDto.startDate),
        end: new Date(queryDto.endDate)
      } : undefined,
      granularity: queryDto.granularity as TimeGranularity
    };

    const analytics = await this.analyticsService.getOrganizationAnalytics(
      organizationId, 
      {
        ...options,
        includeTimeSeriesData: true,
        includeCitationMetrics: false,
        includeTopicAnalysis: false
      }
    );

    // Return only comparison-related data
    return {
      organizationId,
      timeRange: analytics.timeRange,
      documentAnalysis: {
        comparedDocuments: analytics.documentAnalysis.comparedDocuments,
        comparisonMetrics: analytics.documentAnalysis.comparisonMetrics
      },
      timeSeriesData: {
        documentComparisons: analytics.timeSeriesData.documentComparisons
      },
      lastUpdated: analytics.lastUpdated
    };
  }

  @Get('users')
  @ApiOperation({ summary: 'Get user engagement analytics' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns user engagement analytics data',
    type: Object
  })
  async getUserAnalytics(
    @Organization() organizationId: string,
    @Query() queryDto: AnalyticsQueryDto
  ) {
    const options = {
      timeRange: queryDto.startDate && queryDto.endDate ? {
        start: new Date(queryDto.startDate),
        end: new Date(queryDto.endDate)
      } : undefined,
      granularity: queryDto.granularity as TimeGranularity
    };

    const analytics = await this.analyticsService.getOrganizationAnalytics(
      organizationId, 
      {
        ...options,
        includeTimeSeriesData: true,
        includeCitationMetrics: false,
        includeTopicAnalysis: true
      }
    );

    // Return only user-related data
    return {
      organizationId,
      timeRange: analytics.timeRange,
      userEngagement: analytics.userEngagement,
      topicAnalysis: analytics.topicAnalysis,
      timeSeriesData: {
        userQueries: analytics.timeSeriesData.userQueries,
        averageResponseTimes: analytics.timeSeriesData.averageResponseTimes
      },
      lastUpdated: analytics.lastUpdated
    };
  }

  @Get('citations')
  @ApiOperation({ summary: 'Get citation analytics' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns citation analytics data',
    type: Object
  })
  async getCitationAnalytics(
    @Organization() organizationId: string,
    @Query() queryDto: AnalyticsQueryDto
  ) {
    const options = {
      timeRange: queryDto.startDate && queryDto.endDate ? {
        start: new Date(queryDto.startDate),
        end: new Date(queryDto.endDate)
      } : undefined
    };

    const analytics = await this.analyticsService.getOrganizationAnalytics(
      organizationId, 
      {
        ...options,
        includeTimeSeriesData: false,
        includeCitationMetrics: true,
        includeTopicAnalysis: false
      }
    );

    // Return only citation-related data
    return {
      organizationId,
      timeRange: analytics.timeRange,
      citationMetrics: analytics.citationMetrics,
      lastUpdated: analytics.lastUpdated
    };
  }

  @Post('seed')
  @ApiOperation({ summary: 'Seed analytics data for testing' })
  @ApiResponse({ status: 200, description: 'Analytics data seeded successfully' })
  async seedAnalyticsData(
    @Organization() organizationId: string
  ): Promise<{ success: boolean; message: string }> {
    await this.analyticsSeederService.seedAnalyticsData(organizationId);
    return {
      success: true,
      message: `Analytics data seeded successfully for organization: ${organizationId}`
    };
  }

  @Get('documents/:documentId/analysis-history')
  @ApiOperation({ summary: 'Get analysis history analytics for a document' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns analysis history analytics data in visualization-ready format',
    type: Object
  })
  async getAnalysisHistoryAnalytics(
    @Organization() organizationId: string,
    @Param('documentId') documentId: string,
    @Query() queryDto: AnalyticsQueryDto
  ) {
    const options = {
      timeRange: queryDto.startDate && queryDto.endDate ? {
        start: new Date(queryDto.startDate),
        end: new Date(queryDto.endDate)
      } : undefined,
      granularity: queryDto.granularity as TimeGranularity
    };

    // Get analysis history data from the analytics service
    const analysisHistoryData = await this.analyticsService.getDocumentAnalysisHistory(
      documentId,
      organizationId,
      options
    );

    // Return visualization-ready data
    return {
      documentId,
      organizationId,
      timeRange: analysisHistoryData.timeRange,
      analysisHistory: {
        totalAnalyses: analysisHistoryData.totalAnalyses,
        analysisDates: analysisHistoryData.analysisDates,
        processingTimes: analysisHistoryData.processingTimes,
        contentLengths: analysisHistoryData.contentLengths
      },
      evolutionMetrics: {
        riskAssessmentTrend: analysisHistoryData.riskAssessmentTrend,
        keyTermsEvolution: analysisHistoryData.keyTermsEvolution,
        citationAccuracyTrend: analysisHistoryData.citationAccuracyTrend,
        completenessScoreTrend: analysisHistoryData.completenessScoreTrend
      },
      comparisonData: analysisHistoryData.comparisonData,
      lastUpdated: new Date()
    };
  }
}

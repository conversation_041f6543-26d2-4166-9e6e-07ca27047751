import { Injectable, Logger, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AIProvider } from '../interfaces/ai-provider.interface';
import { OpenAIProvider } from '../providers/openai.provider';
import { GeminiProvider } from '../providers/gemini.provider';
import { RateLimiterInfo } from '../../../common/interfaces/rate-limiter.interface';
import { ChatMessage } from '../../../common/interfaces/chat.interface';
import { AIAnalysisResult } from '../interfaces/ai-analysis-result.interface';
import { ClauseSection } from '../interfaces/clause-section.interface';
import { DepositionAnalysisResult } from '../interfaces/deposition-analysis.interface';
import {
  KeyPoint,
  RelatedCase,
} from '../../documents/interfaces/precedent-analysis.interface';
import {
  chatPromptConfig,
  analysisPromptConfig,
  comparisonPromptConfig,
} from '../prompts';
import { generatePrecedentAnalysisPrompt } from '../prompts/precedent-analysis.prompt';

// New Interfaces for AI-driven Precedent Analysis
export interface PrecedentAIAnalysisInput {
  citationString: string; // e.g., "123 U.S. 456"
  documentContext: string; // Text snippet from the user's document where the citation appears
  citedCaseSummary: string; // Key text or summary of the case being cited (from CourtListener)
  // Optional: Full document text or more extensive case details if needed and token limits allow
  // Be mindful of token limits for the AI provider when populating these fields.
  // Very long contexts might need truncation strategies before being sent to the AI.
}

export interface PrecedentAIAnalysisOutput {
  predictedRelevanceScore: number; // A score from 0.0 to 1.0
  predictedImpact: 'positive' | 'negative' | 'neutral' | 'unknown';
  predictedCategory: string; // e.g., 'Contract Law', 'Constitutional Law', or 'Uncategorized'
  aiReasoning: string; // AI's explanation for its assessment
  extractedKeyPoints?: KeyPoint[]; //
  extractedRelatedCases?: RelatedCase[]; //
}

@Injectable()
export class AIService implements AIProvider {
  async generateDepositionQuestions(context: {
    caseContext: string;
    keyIssues: string[];
    targetWitnesses?: string[];
    options?: {
      questionCount?: number;
      questionCategories?: string[];
      includeFollowUps?: boolean;
      focusAreas?: string[];
    };
  }): Promise<any> {
    this.logger.log(
      'AIService: Delegating deposition question generation to provider',
    );
    return this.provider.generateDepositionQuestions(context);
  }

  private readonly logger = new Logger(AIService.name);
  private readonly provider: AIProvider;
  private providerName: string;

  constructor(
    private configService: ConfigService,
    private openaiProvider: OpenAIProvider,
    private geminiProvider: GeminiProvider,
  ) {
    const providerType = this.configService
      .get<string>('AI_PROVIDER', 'openai')
      .toLowerCase();

    if (providerType === 'gemini') {
      this.provider = this.geminiProvider;
      this.providerName = 'Gemini';
      this.logger.log('Using GeminiProvider');
    } else {
      this.provider = this.openaiProvider;
      this.providerName = 'OpenAI';
      this.logger.log('Using OpenAIProvider');
    }
    this.initializeProvider();
  }

  private async initializeProvider() {
    if (this.provider && typeof this.provider.isReady === 'function') {
      this.logger.log(`Initializing ${this.providerName} provider`);
      try {
        await this.provider.isReady();
        this.logger.log(
          `${this.providerName} provider initialized successfully.`,
        );
      } catch (error) {
        this.logger.error(
          `Failed to initialize ${this.providerName} provider: ${error.message}`,
          error.stack,
        );
      }
    } else {
      this.logger.warn(
        `Selected provider ${this.providerName} does not have an isReady method or provider is not set.`,
      );
    }
  }

  async generateResponse(
    prompt: string,
    options?: Record<string, any>,
  ): Promise<string> {
    if (!this.provider)
      throw new Error(
        `${this.providerName} provider not initialized or configured correctly.`,
      );
    return this.provider.generateResponse(prompt, options);
  }

  async generateChatResponse(
    messages: ChatMessage[],
    options?: Record<string, any>,
  ): Promise<string> {
    if (!this.provider)
      throw new Error(
        `${this.providerName} provider not initialized or configured correctly.`,
      );
    if (typeof this.provider.generateChatResponse !== 'function') {
      throw new Error(
        `generateChatResponse method not implemented by ${this.providerName} provider.`,
      );
    }
    return this.provider.generateChatResponse(messages, options);
  }

  generateStreamResponse(
    prompt: string,
    options?: Record<string, any>,
  ): AsyncGenerator<string, void, unknown> {
    if (!this.provider)
      throw new Error(
        `${this.providerName} provider not initialized or configured correctly.`,
      );
    return this.provider.generateStreamResponse(prompt, options);
  }

  async analyzeDocument(
    content: string,
    options?: Record<string, any>,
  ): Promise<AIAnalysisResult> {
    if (!this.provider)
      throw new Error(
        `${this.providerName} provider not initialized or configured correctly.`,
      );
    return this.provider.analyzeDocument(content, options);
  }

  async compareDocuments(
    documents: string[],
    options?: Record<string, any>,
  ): Promise<Record<string, any>> {
    if (!this.provider)
      throw new Error(
        `${this.providerName} provider not initialized or configured correctly.`,
      );
    if (typeof this.provider.compareDocuments !== 'function') {
      throw new Error(
        `compareDocuments method not implemented by ${this.providerName} provider.`,
      );
    }
    return this.provider.compareDocuments(documents, options);
  }

  getProviderName(): string {
    return this.providerName;
  }

  getRateLimiterUtilization(): RateLimiterInfo {
    if (!this.provider) {
      this.logger.warn(
        'Cannot get rate limiter utilization: provider not initialized.',
      );
      return { utilizationPercentage: 0, remainingTokens: 0, maxTokens: 0 };
    }
    if (typeof this.provider.getRateLimiterUtilization === 'function') {
      return this.provider.getRateLimiterUtilization();
    } else {
      const configPrefix = this.providerName.toLowerCase();
      const maxTokens = this.configService.get<number>(
        `${configPrefix}.rateLimit.maxRequests`,
        3500,
      );
      const utilizationPercentage = 0.1;
      const remainingTokens = Math.floor(
        (1 - utilizationPercentage) * maxTokens,
      );

      return {
        utilizationPercentage,
        remainingTokens,
        maxTokens,
      };
    }
  }

  async isReady(): Promise<boolean> {
    if (!this.provider) return false;
    if (typeof this.provider.isReady === 'function') {
      return this.provider.isReady();
    }
    return true;
  }

  async generateSessionTitle(document: any): Promise<string> {
    if (!this.provider) {
      throw new Error(
        `${this.providerName} provider not initialized or configured correctly.`,
      );
    }

    const docInfo = [];
    if (document.originalName)
      docInfo.push(`Document name: ${document.originalName}`);
    if (document.content) {
      // Take first 500 characters of content for context
      docInfo.push(`Content preview: ${document.content.substring(0, 500)}...`);
    }
    if (document.metadata?.summary)
      docInfo.push(`Summary: ${document.metadata.summary}`);

    const prompt = `Generate a brief, descriptive title (max 6 words) for a chat session about the following document:\n\n${docInfo.join(
      '\n',
    )}\n\nTitle:`;

    const title = await this.generateResponse(prompt);
    // Clean up the response and ensure it's not too long
    return title
      .replace(/^["']|["']$/g, '')
      .trim()
      .substring(0, 50);
  }

  async identifyClauseSections(
    documentContent: string,
    options?: Record<string, any>,
  ): Promise<ClauseSection[]> {
    this.logger.log('Identifying clause sections in document');

    if (!this.provider) {
      throw new Error(
        `${this.providerName} provider not initialized or configured correctly.`,
      );
    }

    // Prepare the prompt for clause identification
    const prompt = `Analyze the following legal document and identify distinct clause sections. For each clause, provide the exact text content, the starting and ending character positions in the document, and the type of clause (e.g., confidentiality, liability, termination, etc.).\n\nDocument:\n${documentContent}\n\nOutput the results in the following JSON format:\n[\n  {\n    "content": "clause text",\n    "startIndex": startPosition,\n    "endIndex": endPosition,\n    "type": "clause type",\n    "confidence": confidenceScore\n  }\n]`;

    try {
      const response = await this.generateResponse(prompt, {
        ...options,
        temperature: 0.2, // Lower temperature for more deterministic results
        responseFormat: { type: 'json_object' },
      });

      // Clean up markdown formatting if present
      const cleanResponse = response.replace(/```json\n|\n```/g, '').trim();

      // Parse the cleaned response
      const parsedResponse = JSON.parse(cleanResponse);

      // Ensure the response is in the expected format
      if (Array.isArray(parsedResponse)) {
        return parsedResponse.map((clause) => ({
          name: clause.name || `Clause ${clause.type || 'Unnamed'}`,
          category: clause.category || clause.type || 'General',
          content: clause.content,
          startIndex: parseInt(clause.startIndex, 10),
          endIndex: parseInt(clause.endIndex, 10),
          type: clause.type,
          confidence: parseFloat(clause.confidence) || 0.7,
          metadata: clause.metadata || {},
        }));
      }

      this.logger.warn(
        'Unexpected response format from AI provider for clause identification',
        { response },
      );
      return [];
    } catch (error) {
      this.logger.error(
        `Error identifying clause sections: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }

  async optimizeClauseTemplate(
    content: string,
    options?: Record<string, any>,
  ): Promise<string> {
    this.logger.log('Optimizing clause template');

    if (!this.provider) {
      throw new Error(
        `${this.providerName} provider not initialized or configured correctly.`,
      );
    }

    // Prepare the prompt for clause template optimization
    const prompt = `You are a legal document expert. Optimize the following legal clause to make it more clear, concise, and legally sound. Maintain the original intent and legal meaning, but improve the language, structure, and readability.\n\nOriginal Clause:\n${content}\n\nOptimized Clause:`;

    try {
      const response = await this.generateResponse(prompt, {
        ...options,
        temperature: 0.3, // Slightly higher temperature for creative improvements
      });

      return response.trim();
    } catch (error) {
      this.logger.error(
        `Error optimizing clause template: ${error.message}`,
        error.stack,
      );
      return content; // Return original content if optimization fails
    }
  }

  async analyzeDeposition(
    transcript: string,
    context: {
      caseContext?: string;
      focusAreas?: string[];
    } = {},
  ): Promise<DepositionAnalysisResult> {
    this.logger.log(
      `AIService: Delegating deposition analysis to ${this.providerName}`,
    );
    if (!this.provider) {
      throw new Error(
        `${this.providerName} provider not initialized or configured correctly.`,
      );
    }
    if (typeof this.provider.analyzeDeposition !== 'function') {
      throw new Error(
        `analyzeDeposition method not implemented by ${this.providerName} provider.`,
      );
    }
    return this.provider.analyzeDeposition(transcript, context);
  }

  async analyzePrecedentContextWithAI(
    input: PrecedentAIAnalysisInput,
    options?: Record<string, any>,
  ): Promise<PrecedentAIAnalysisOutput | null> {
    this.logger.log(
      `Performing AI-driven analysis for citation: ${input.citationString}`,
    );

    if (!this.provider) {
      this.logger.error(
        `${this.providerName} provider not initialized or configured correctly.`,
      );
      throw new Error(
        `${this.providerName} provider not initialized or configured correctly.`,
      );
    }

    const prompt = generatePrecedentAnalysisPrompt(input);

    try {
      const response = await this.generateResponse(prompt, {
        ...options,
        temperature: 0.3, // Slightly higher temperature for more nuanced reasoning but still structured
        responseFormat: { type: 'json_object' }, // Attempt to enforce JSON output if provider supports it
      });

      // Clean up markdown formatting if present (common with some models)
      const cleanResponse = response.replace(/```json\n|\n```/g, '').trim();

      const parsedResponse = JSON.parse(cleanResponse);

      // Validate the parsed response structure (basic validation)
      if (
        typeof parsedResponse.predictedRelevanceScore === 'number' &&
        parsedResponse.predictedRelevanceScore >= 0.0 && // Ensure score is non-negative
        parsedResponse.predictedRelevanceScore <= 1.0 && // Ensure score is not above 1.0
        ['positive', 'negative', 'neutral', 'unknown'].includes(
          parsedResponse.predictedImpact,
        ) &&
        typeof parsedResponse.predictedCategory === 'string' &&
        typeof parsedResponse.aiReasoning === 'string'
      ) {
        return parsedResponse as PrecedentAIAnalysisOutput;
      }

      this.logger.warn(
        'Unexpected JSON structure or out-of-range values from AI provider for precedent analysis',
        { response: parsedResponse },
      );
      return null;
    } catch (error) {
      this.logger.error(
        `Error in AI precedent analysis for ${input.citationString}: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  /**
   * Generate a complete legal document based on prompts and requirements
   */
  async generateLegalDocument(
    prompt: string,
    documentType: string,
    options?: {
      keyTerms?: string[];
      requiredClauses?: string[];
      jurisdiction?: string;
      organizationPreferences?: string;
      includeDisclaimers?: boolean;
      organizationClauses?: Array<{
        name: string;
        category: string;
        content: string;
      }>;
    },
  ): Promise<string> {
    this.logger.log(`Generating legal document of type: ${documentType}`);

    if (!this.provider) {
      throw new Error(
        `${this.providerName} provider not initialized or configured correctly.`,
      );
    }

    let enhancedPrompt = `Generate a comprehensive ${documentType} document based on the following requirements:\n\n`;
    enhancedPrompt += `Main Requirements: ${prompt}\n\n`;

    if (options?.keyTerms?.length) {
      enhancedPrompt += `Key Terms to Include: ${options.keyTerms.join(
        ', ',
      )}\n\n`;
    }

    if (options?.requiredClauses?.length) {
      enhancedPrompt += `Required Clauses: ${options.requiredClauses.join(
        ', ',
      )}\n\n`;
    }

    if (options?.jurisdiction) {
      enhancedPrompt += `Target Jurisdiction: ${options.jurisdiction}\n\n`;
    }

    if (options?.organizationPreferences) {
      enhancedPrompt += `Organization Preferences: ${options.organizationPreferences}\n\n`;
    }

    // Include organization clauses if provided
    if (options?.organizationClauses?.length) {
      enhancedPrompt += `Available Organization Clauses (use these when appropriate):\n`;
      options.organizationClauses.slice(0, 10).forEach((clause, index) => {
        enhancedPrompt += `${index + 1}. ${clause.name} (${
          clause.category
        }): ${clause.content.substring(0, 200)}...\n`;
      });
      enhancedPrompt += '\n';
    }

    enhancedPrompt += `Please generate a well-structured, legally sound ${documentType} document that incorporates the above requirements. `;
    enhancedPrompt += `Include appropriate legal language, standard clauses for this document type, and ensure professional formatting.`;

    if (options?.organizationClauses?.length) {
      enhancedPrompt += ` When possible, incorporate or adapt the provided organization clauses to maintain consistency with organizational standards.`;
    }

    if (options?.includeDisclaimers) {
      enhancedPrompt += ` Include standard legal disclaimers appropriate for this document type.`;
    }

    try {
      const response = await this.generateResponse(enhancedPrompt, {
        temperature: 0.3, // Lower temperature for more consistent, precise legal language
        systemMessage:
          'You are an expert legal document drafter with extensive knowledge of contract law and legal writing best practices.',
      });

      return response.trim();
    } catch (error) {
      this.logger.error(
        `Error generating legal document: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to generate legal document: ${error.message}`);
    }
  }

  /**
   * Generate related documents based on a primary document
   */
  async generateRelatedDocument(
    primaryDocumentContent: string,
    relatedDocumentType: string,
    additionalRequirements?: string,
  ): Promise<string> {
    this.logger.log(
      `Generating related document of type: ${relatedDocumentType}`,
    );

    if (!this.provider) {
      throw new Error(
        `${this.providerName} provider not initialized or configured correctly.`,
      );
    }

    let prompt = `Based on the following primary document, generate a ${relatedDocumentType} that complements and supports the main agreement:\n\n`;
    prompt += `PRIMARY DOCUMENT:\n${primaryDocumentContent.substring(
      0,
      3000,
    )}...\n\n`;

    if (additionalRequirements) {
      prompt += `ADDITIONAL REQUIREMENTS:\n${additionalRequirements}\n\n`;
    }

    switch (relatedDocumentType.toLowerCase()) {
      case 'schedule':
        prompt += `Generate a detailed schedule that lists specific items, dates, or requirements referenced in the primary document. `;
        prompt += `Include appropriate headers, formatting, and ensure all referenced items are clearly defined.`;
        break;
      case 'exhibit':
        prompt += `Generate an exhibit that provides supporting documentation or detailed specifications referenced in the primary document. `;
        prompt += `Include technical details, specifications, or supplementary information as appropriate.`;
        break;
      case 'addendum':
        prompt += `Generate an addendum that adds new terms or modifies existing terms in the primary document. `;
        prompt += `Ensure proper legal language for modifications and clear references to the original document.`;
        break;
      case 'amendment':
        prompt += `Generate an amendment that formally modifies specific provisions of the primary document. `;
        prompt += `Include proper amendment language, effective dates, and clear identification of modified sections.`;
        break;
      case 'appendix':
        prompt += `Generate an appendix that provides additional reference material or detailed information supporting the primary document. `;
        prompt += `Include relevant data, charts, or supplementary content as appropriate.`;
        break;
      default:
        prompt += `Generate a ${relatedDocumentType} that supports and complements the primary document.`;
    }

    try {
      const response = await this.generateResponse(prompt, {
        temperature: 0.4, // Slightly higher than main documents but still conservative for legal content
        systemMessage: `You are an expert legal document drafter specializing in ${relatedDocumentType} documents.`,
      });

      return response.trim();
    } catch (error) {
      this.logger.error(
        `Error generating related document: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to generate related document: ${error.message}`);
    }
  }

  /**
   * Suggest clauses for a document based on content and type
   */
  async suggestClauses(
    currentContent: string,
    documentType: string,
    sectionType?: string,
    context?: string,
    organizationClauses?: Array<{
      name: string;
      category: string;
      content: string;
    }>,
  ): Promise<
    Array<{
      type: string;
      content: string;
      explanation: string;
      relevanceScore: number;
    }>
  > {
    this.logger.log(`Suggesting clauses for document type: ${documentType}`);

    if (!this.provider) {
      throw new Error(
        `${this.providerName} provider not initialized or configured correctly.`,
      );
    }

    let prompt = `Based on the following document content and type, suggest 3-5 relevant clauses that should be included:\n\n`;
    prompt += `Document Type: ${documentType}\n`;

    if (sectionType) {
      prompt += `Section Type: ${sectionType}\n`;
    }

    if (context) {
      prompt += `Context: ${context}\n`;
    }

    // Include organization clauses for reference
    if (organizationClauses?.length) {
      prompt += `\nAvailable Organization Clauses:\n`;
      organizationClauses.slice(0, 10).forEach((clause, index) => {
        prompt += `${index + 1}. ${clause.name} (${
          clause.category
        }): ${clause.content.substring(0, 150)}...\n`;
      });
      prompt += '\n';
    }

    prompt += `\nCurrent Content:\n${currentContent.substring(0, 1000)}...\n\n`;
    prompt += `Please suggest clauses with their content and explain why they are relevant. `;

    if (organizationClauses?.length) {
      prompt += `Prioritize organization clauses when they are relevant, but also suggest additional clauses as needed. `;
    }

    prompt += `Format your response as JSON with the following structure:\n`;
    prompt += `[{"type": "clause_type", "content": "clause_content", "explanation": "why_relevant", "relevanceScore": 0.8}]`;

    try {
      const response = await this.generateResponse(prompt, {
        temperature: 0.2, // Very low temperature for structured clause suggestions
        systemMessage:
          'You are a legal expert providing clause suggestions for document drafting.',
        responseFormat: { type: 'json_object' },
      });

      // Clean up markdown formatting if present
      const cleanResponse = response.replace(/```json\n|\n```/g, '').trim();
      const suggestions = JSON.parse(cleanResponse);

      // Validate and return suggestions
      if (Array.isArray(suggestions)) {
        return suggestions.filter(
          (suggestion) =>
            suggestion.type && suggestion.content && suggestion.explanation,
        );
      }

      return [];
    } catch (error) {
      this.logger.warn(
        `Failed to generate AI clause suggestions: ${error.message}`,
      );
      return [];
    }
  }
}

export const generateDepositionAnalysisPrompt = (input: {
  transcript: string;
  caseContext?: string;
  focusAreas?: string[];
}): string => {
  return `
You are an expert legal analyst specializing in deposition analysis. Your task is to analyze the provided deposition transcript and provide a detailed assessment of witness credibility, identify inconsistencies, and suggest effective cross-examination strategies.

CASE CONTEXT:
${input.caseContext || 'No additional context provided.'}

FOCUS AREAS:
${input.focusAreas?.join(', ') || 'General analysis'}

DEPOSITION TRANSCRIPT:
"""
${input.transcript}
"""

ANALYSIS TASKS:
1. Credibility Assessment:
   - Identify the witness's key statements
   - Rate credibility of each major claim (0.0-1.0)
   - Note any inconsistencies or contradictions
   - Flag potential deception indicators

2. Cross-Examination Opportunities:
   - Suggest 3-5 high-impact cross-examination questions
   - For each, provide:
     * The specific statement being challenged
     * The contradiction or issue
     * Suggested questioning approach
     * Legal basis for the line of questioning

3. Impeachment Potential:
   - Identify statements that could be used for impeachment
   - Note any prior inconsistent statements
   - Suggest documentary evidence that could contradict testimony

4. Timeline Analysis:
   - Create a timeline of key events mentioned
   - Note any temporal inconsistencies
   - Flag gaps in the testimony

OUTPUT FORMAT:
Provide your analysis in the following JSON structure:

{
  "overallCredibilityScore": 0.0-1.0,
  "keyTestimonyAnalysis": [
    {
      "speaker": "Witness Name",
      "statement": "The exact statement",
      "credibilityScore": 0.0-1.0,
      "confidence": "high|medium|low",
      "reasoning": "Analysis of why this score was given",
      "supportingEvidence": ["Supporting points"],
      "contradictions": [
        {
          "previousStatement": "Previous statement",
          "currentStatement": "Current contradictory statement",
          "reasoning": "Analysis of the contradiction"
        }
      ]
    }
  ],
  "inconsistencies": [
    {
      "statement1": "First statement",
      "statement2": "Second contradictory statement",
      "explanation": "Analysis of the inconsistency",
      "severity": "high|medium|low"
    }
  ],
  "keyFindings": [
    "Key finding 1",
    "Key finding 2"
  ],
  "crossExaminationSuggestions": [
    {
      "topic": "Topic area",
      "question": "Suggested question",
      "purpose": "What this question aims to accomplish",
      "legalBasis": "Legal justification",
      "suggestedFollowUps": ["Follow-up questions"]
    }
  ],
  "potentialImpeachmentOpportunities": [
    {
      "statement": "The statement that could be impeached",
      "conflictingEvidence": "Evidence that contradicts this statement",
      "suggestedApproach": "How to approach impeachment"
    }
  ],
  "timelineAnalysis": [
    {
      "event": "Description of event",
      "timestamp": "When it occurred",
      "relevance": 0.0-1.0,
      "notes": "Significance of this event"
    }
  ]
}

IMPORTANT NOTES:
- Be precise and evidence-based in your analysis
- Only include information that can be directly supported by the transcript
- Clearly distinguish between facts and interpretations
- Maintain professional tone and legal accuracy
- Focus on the most significant issues that could impact the case

JSON Output:`;
};

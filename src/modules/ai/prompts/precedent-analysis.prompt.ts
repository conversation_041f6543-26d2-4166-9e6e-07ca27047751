import { PrecedentAIAnalysisInput } from '../services/ai.service'; // Adjust path as needed

export const generatePrecedentAnalysisPrompt = (input: PrecedentAIAnalysisInput): string => {
  return `
Analyze the following legal precedent citation in context and provide a structured assessment.
Your goal is to act as an expert legal analyst. Be precise and base your analysis strictly on the provided information.

Information Provided:
1. Citation String: "${input.citationString}"
2. Document Context (where the citation appears):
   """
   ${input.documentContext}
   """
3. Cited Case Summary/Key Points (from external knowledge source like CourtListener):
   """
   ${input.citedCaseSummary}
   """

IMPORTANT: FACT VERIFICATION INSTRUCTIONS
- If the Cited Case Summary is minimal or empty, be extremely cautious in your analysis.
- Only include facts that you can verify from the provided information.
- Do NOT invent or assume facts about the case that aren't explicitly provided.
- If insufficient information is available, clearly indicate this in your analysis.
- For Key Points and Related Cases, only include information that is factually supported.
- When in doubt about specific details, err on the side of caution and indicate uncertainty.

Tasks:
Based on the "Information Provided" ONLY, assess the following:

1.  **Relevance Score (predictedRelevanceScore):**
    -   Estimate the relevance of the "Cited Case Summary/Key Points" to the specific argument or topic discussed in the "Document Context".
    -   Provide a numerical score between 0.0 (not at all relevant) and 1.0 (highly relevant and directly applicable).

2.  **Impact (predictedImpact):**
    -   Determine the likely impact of this precedent on the legal argument or statement being made in the "Document Context".
    -   Choose ONE of the following: 'positive' (supports the argument), 'negative' (contradicts or undermines the argument), 'neutral' (provides context but neither strongly supports nor opposes), 'unknown' (insufficient information to determine impact clearly).

3.  **Category (predictedCategory):**
    -   Suggest a primary legal category for the "Cited Case Summary/Key Points".
    -   Examples: 'Contract Law', 'Tort Law', 'Constitutional Law', 'Criminal Procedure', 'Civil Procedure', 'Intellectual Property', 'Family Law', 'Administrative Law', 'Uncategorized'. Choose the most fitting or 'Uncategorized' if unsure.

4.  **Key Points (extractedKeyPoints):**
    -   From the "Cited Case Summary/Key Points", extract substantive key legal arguments, holdings, reasoning, or important facts. Do NOT include simple metadata like 'Precedential status' as a key point unless it's the *only* information available in the summary.
    -   VERIFY FACTS: Only include points that are explicitly supported by the provided case summary.
    -   If the case summary is minimal or empty, DO NOT invent or assume facts about the case.
    -   Return as an array of objects. Each object must have a "text" field (string). Optionally, include a "type" field (e.g., 'finding', 'holding', 'reasoning', 'dicta', 'fact').
    -   If no substantive key points can be extracted from the provided summary, return an empty array \[\].

5.  **Related Cases (extractedRelatedCases):**
    -   Identify up to 3-5 cases related to the "Cited Case Summary/Key Points". You may use your general legal knowledge for this, anchored by the cited case information.
    -   VERIFY RELATIONSHIPS: Only include cases with well-established relationships to the cited case.
    -   If the case summary is minimal, only include related cases that are widely known to be connected to the cited case.
    -   When the cited case summary is empty or very limited, be extremely cautious - only include the most fundamental and well-established related cases, if any.
    -   For each related case, provide:
        -   \`caseName\` (string)
        -   \`citation\` (string, optional)
        -   \`relevance\` (number, 0.0-1.0, its relevance to the *primary cited case*)
        -   \`relationship\` (string, e.g., 'supports', 'contradicts', 'distinguishes', 'cites', 'citedBy', 'clarifies', 'extends', 'limits', 'overrules', 'unknown')
        -   \`summary\` (string, 1-2 sentence summary of the related case, optional)
        -   \`confidenceLevel\` (string, either 'high', 'medium', or 'low', indicating your confidence in this relationship)
    -   Return as an array of objects. If no relevant related cases are found, return an empty array \[\].

6.  **Reasoning (aiReasoning):**
    -   Provide a BRIEF and CONCISE explanation (1-2 sentences maximum) for your assessments of relevance, impact, and category (Tasks 1-3). Focus on the key elements from the provided texts that led to your conclusions.
    -   If the case summary is minimal or empty, explicitly acknowledge this limitation in your reasoning.
    -   When working with limited information, indicate your level of confidence in the assessment.

Output Format:
Return your response ONLY as a single, valid JSON object. Do NOT include any explanatory text or markdown formatting before or after the JSON object.

Example of the EXACT JSON structure expected:
{
  "predictedRelevanceScore": 0.8,
  "predictedImpact": "positive",
  "predictedCategory": "Contract Law",
  "extractedKeyPoints": [
    {
      "text": "The court held that the contract was valid and enforceable.",
      "type": "holding"
    }
  ],
  "extractedRelatedCases": [
    {
      "caseName": "Smith v. Jones",
      "citation": "123 F.3d 456",
      "relevance": 0.9,
      "relationship": "supports",
      "summary": "The court in Smith v. Jones held that a similar contract was valid and enforceable.",
      "confidenceLevel": "high"
    }
  ],
  "aiReasoning": "The cited case directly supports the contractual interpretation argument in the document context by providing a similar factual scenario and ruling."
}

JSON Output:
`;
};

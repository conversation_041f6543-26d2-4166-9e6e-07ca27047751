import { Controller, Get } from '@nestjs/common';
import { AIService } from '../services/ai.service';
import { RateLimiterInfo } from '../../../common/interfaces/rate-limiter.interface';

@Controller('ai')
export class AIController {
  constructor(private readonly aiService: AIService) {}

  /**
   * Get information about the current rate limiter status
   * @returns Information about rate limiting, including utilization percentage, remaining tokens, and max tokens
   */
  @Get('rate-limit')
  getRateLimiterInfo(): RateLimiterInfo {
    return this.aiService.getRateLimiterUtilization();
  }

  /**
   * Get the name of the currently active AI provider
   * @returns The name of the active provider
   */
  @Get('provider')
  getActiveProvider(): { provider: string } {
    return { provider: this.aiService.getProviderName() };
  }
}

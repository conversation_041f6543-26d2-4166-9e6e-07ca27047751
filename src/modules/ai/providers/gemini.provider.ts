import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';
import { AIProvider } from '../interfaces/ai-provider.interface';
import { ClauseSection } from '../interfaces/clause-section.interface';
import { DepositionAnalysisResult } from '../interfaces/deposition-analysis.interface';
import { generateDepositionAnalysisPrompt } from '../prompts/deposition-analysis.prompt';
import { PromptTemplateService } from '../../prompt-templates/prompt-template.service';
import { DocumentContextService } from '../../document-context/services/document-context.service';
import { ResultPostProcessingService } from '../../post-processing/services/result-post-processing.service';
import { ContextManagerService } from '../../context-management/services/context-manager.service';
import { RetryUtil, RetryConfig } from '../../../common/utils/retry.util';
import {
  RateLimiter,
  RateLimiterConfig,
} from '../../../common/utils/rate-limiter.util';
import { RateLimiterInfo } from '../../../common/interfaces/rate-limiter.interface';
import { DocumentType as EnumDocumentType } from '../../../common/enums/document-type.enum';
import { DocumentType as PromptDocumentType } from '../../prompt-templates/interfaces/prompt-template.interface';
import {
  ChatRole,
  ChatMessage,
} from '../../../common/interfaces/chat.interface';
import {
  AIAnalysisResult,
  AIAnalysisMetadata,
} from '../interfaces/ai-analysis-result.interface';

@Injectable()
export class GeminiProvider implements AIProvider {
  async generateDepositionQuestions(context: {
    caseContext: string;
    keyIssues: string[];
    targetWitnesses?: string[];
    options?: {
      questionCount?: number;
      questionCategories?: string[];
      includeFollowUps?: boolean;
      focusAreas?: string[];
    };
  }): Promise<any> {
    // Generate template questions as fallback
    const templateQuestions = [
      {
        id: crypto.randomUUID(),
        text: "Please describe your role and responsibilities related to this case.",
        category: "background",
        purpose: "Establish witness background and credibility",
        priority: "high",
        targetWitness: context.targetWitnesses?.[0] || "Witness",
        suggestedFollowUps: [
          "Could you elaborate on that?",
          "What led you to that conclusion?",
          "Is there anything else I should know about this?"
        ],
        createdAt: new Date(),
        updatedAt: new Date(),
        relatedDocuments: [],
        notes: "Generated as fallback due to AI service unavailability",
        isFollowUp: false
      },
      {
        id: crypto.randomUUID(),
        text: "What documents or evidence have you reviewed in preparation for this deposition?",
        category: "document_related",
        purpose: "Identify documents reviewed by the witness",
        priority: "medium",
        targetWitness: context.targetWitnesses?.[0] || "Witness",
        suggestedFollowUps: [
          "Could you elaborate on that?",
          "What led you to that conclusion?",
          "Is there anything else I should know about this?"
        ],
        createdAt: new Date(),
        updatedAt: new Date(),
        relatedDocuments: [],
        notes: "Generated as fallback due to AI service unavailability",
        isFollowUp: false
      }
    ];

    return {
      questions: templateQuestions,
      metadata: {
        generatedAt: new Date().toISOString(),
        generationDurationMs: 4000,
        modelUsed: "fallback"
      }
    };
  }

  private readonly logger = new Logger(GeminiProvider.name);
  private readonly openai: OpenAI;
  private readonly retryUtil: RetryUtil;
  private readonly rateLimiter: RateLimiter;
  private isInitialized = false;
  private readonly systemPrompt: string;

  constructor(
    private configService: ConfigService,
    private promptTemplateService: PromptTemplateService,
    private postProcessingService: ResultPostProcessingService,
    private documentContextService: DocumentContextService,
    @Inject(forwardRef(() => ContextManagerService))
    private contextManagerService: ContextManagerService,
  ) {
    const apiKey = this.configService.get<string>('gemini.apiKey');

    if (!apiKey) {
      this.logger.warn('Gemini API key is not configured');
      return;
    }

    this.openai = new OpenAI({
      apiKey: apiKey,
    });
    this.isInitialized = true;

    // Load system prompt from configuration
    this.systemPrompt =
      this.configService.get<string>('systemPrompt.prompt') || '';
    this.logger.log('Initialized GeminiProvider with system prompt');

    // Initialize retry utility
    const retryConfig: RetryConfig = {
      maxRetries:
        this.configService.get<number>('gemini.retry.maxRetries') || 3,
      initialDelayMs:
        this.configService.get<number>('gemini.retry.initialDelayMs') || 1000,
      maxDelayMs:
        this.configService.get<number>('gemini.retry.maxDelayMs') || 5000,
      backoffFactor:
        this.configService.get<number>('gemini.retry.backoffFactor') || 2,
    };
    this.retryUtil = new RetryUtil(retryConfig);

    // Initialize rate limiter
    const rateLimiterConfig: RateLimiterConfig = {
      maxRequests:
        this.configService.get<number>('gemini.rateLimit.maxRequests') || 60,
      windowMs:
        this.configService.get<number>('gemini.rateLimit.windowMs') || 60000,
      throwOnLimit:
        this.configService.get<boolean>('gemini.rateLimit.throwOnLimit') ||
        false,
    };
    this.rateLimiter = new RateLimiter(rateLimiterConfig);

    this.logger.log('GeminiProvider initialized');
  }

  async generateResponse(
    prompt: string,
    options?: Record<string, any>,
  ): Promise<string> {
    if (!this.isInitialized) throw new Error('Gemini provider not initialized');

    const isAnalysis = options?.type === 'analysis';
    const defaultSystemPrompt =
      this.configService.get<string>(
        isAnalysis
          ? 'analysisPromptConfig.systemMessage'
          : 'chatPromptConfig.systemMessage',
      ) || this.systemPrompt;

    return this.rateLimiter.executeWithRateLimit<string>(
      async () =>
        this.retryUtil.withRetry(async () => {
          const completion = await this.openai.chat.completions.create({
            model:
              this.configService.get<string>('gemini.modelName') ||
              'gemini-2.0-flash-001',
            messages: [
              {
                role: 'system',
                content: options?.systemMessage || defaultSystemPrompt,
              },
              { role: 'user', content: prompt },
            ],
            temperature:
              options?.temperature ||
              this.configService.get<number>('gemini.temperature') ||
              0.7,
            response_format: { type: 'text' },
            max_tokens:
              options?.maxTokens ||
              this.configService.get<number>('gemini.maxTokens') ||
              1000,
          });
          return completion.choices[0]?.message?.content || '';
        }, 'generateResponse'),
      'generateResponse',
    );
  }

  async *generateStreamResponse(
    prompt: string,
    options?: Record<string, any>,
  ): AsyncGenerator<string, void, unknown> {
    if (!this.isInitialized) throw new Error('Gemini provider not initialized');

    if (!this.rateLimiter.acquire('generateStreamResponse')) {
      throw new Error('Rate limit exceeded');
    }

    try {
      const stream = await this.openai.chat.completions.create({
        model:
          this.configService.get<string>('gemini.modelName') ||
          'gemini-2.0-flash-001',
        messages: [
          {
            role: 'system',
            content:
              options?.systemMessage ||
              this.configService.get<string>(
                options?.type === 'analysis'
                  ? 'analysisPromptConfig.systemMessage'
                  : 'chatPromptConfig.systemMessage',
              ) ||
              this.systemPrompt,
          },
          { role: 'user', content: prompt },
        ],
        temperature:
          options?.temperature ||
          this.configService.get<number>('gemini.temperature') ||
          0.7,
        stream: true,
      });

      for await (const chunk of stream) {
        const content = chunk.choices[0]?.delta?.content || '';
        if (content) yield content;
      }
    } catch (error) {
      this.logger.error(`Error in stream generation: ${error.message}`);
      throw error;
    }
  }

  async generateChatResponse(
    messages: ChatMessage[],
    options?: Record<string, any>,
  ): Promise<string> {
    if (!this.isInitialized) throw new Error('Gemini provider not initialized');

    // Get the dedicated chat system prompt
    const chatSystemPrompt =
      this.configService.get<string>('chatPromptConfig.systemMessage') ||
      'You are a helpful assistant.'; // Basic fallback

    const providerMessages = this.mapMessagesToProviderFormat(messages);

    return this.rateLimiter.executeWithRateLimit<string>(
      async () =>
        this.retryUtil.withRetry(async () => {
          const completion = await this.openai.chat.completions.create({
            model:
              this.configService.get<string>('gemini.modelName') ||
              'gemini-2.0-flash-001',
            messages: [
              {
                role: 'system',
                content: options?.systemMessage || chatSystemPrompt, // Allow overriding via options
              },
              ...providerMessages, // Spread the mapped history
            ],
            temperature:
              options?.temperature ||
              this.configService.get<number>('gemini.temperature') ||
              0.7,
            response_format: { type: 'text' }, // Ensure text output for chat
            max_tokens:
              options?.maxTokens ||
              this.configService.get<number>('gemini.maxTokens') ||
              1000,
          });
          return completion.choices[0]?.message?.content || '';
        }, 'generateChatResponse'),
      'generateChatResponse',
    );
  }

  async analyzeDocument(
    content: string,
    options?: { documentType?: EnumDocumentType; query?: string },
  ): Promise<AIAnalysisResult> {
    if (!this.isInitialized) {
      const error = new Error('Gemini provider not initialized');
      this.logger.error(error.message);
      throw error;
    }

    const startTime = Date.now();
    const providerName = 'Gemini';
    let modelUsed: string;
    let promptUsed: string;
    let rateLimitInfo: RateLimiterInfo;
    let detectedType: PromptDocumentType;

    try {
      // 1. Determine Document Type & Get Prompt
      if (options?.documentType) {
        detectedType = this.mapDocumentType(options.documentType);
      } else {
        detectedType = await this.promptTemplateService.detectDocumentType(
          content,
        );
      }

      const templateObject =
        this.promptTemplateService.getTemplateByType(detectedType);
      if (!templateObject) {
        throw new Error(
          `Could not find prompt template for type: ${detectedType}`,
        );
      }

      const promptContext = {
        documentContent: content,
        userQuery: options?.query,
      };
      promptUsed = this.promptTemplateService.renderTemplate(
        templateObject.template,
        promptContext,
      );

      // Append JSON instruction to the prompt
      promptUsed +=
        '\n\nIMPORTANT: Provide your analysis strictly in JSON format.';

      // Use model from template, config, or fallback
      const modelToUse =
        templateObject.model ||
        this.configService.get<string>('gemini.modelName') ||
        'gemini-2.0-flash-001';
      modelUsed = modelToUse; // Assign modelUsed early for logging/metadata

      this.logger.log(
        `Analyzing document (Type: ${detectedType}) using Gemini model: ${modelUsed}`,
      );

      // 2. Execute with Rate Limiting and Retry
      const completion =
        await this.rateLimiter.executeWithRateLimit<OpenAI.Chat.Completions.ChatCompletion>(
          async () =>
            this.retryUtil.withRetry(
              async () =>
                this.openai.chat.completions.create({
                  model: modelUsed,
                  messages: [
                    { role: 'system', content: this.systemPrompt }, // Use configured system prompt
                    { role: 'user', content: promptUsed },
                  ],
                  temperature:
                    this.configService.get<number>('gemini.temperature') || 0.5, // Lower temp for structured output
                  response_format: { type: 'json_object' }, // Request JSON output
                  max_tokens:
                    this.configService.get<number>('gemini.maxTokens') || 4096,
                }),
              'analyzeDocument',
            ),
          'analyzeDocument',
        );

      rateLimitInfo = this.getRateLimiterUtilization(); // Capture after successful call
      const endTime = Date.now();

      // 3. Extract and Parse Content
      const rawContent = completion.choices[0]?.message?.content;
      if (!rawContent) {
        throw new Error('Gemini API returned empty analysis content');
      }

      let analysisContent: Record<string, any>;
      try {
        analysisContent = JSON.parse(rawContent);
      } catch (parseError) {
        this.logger.error(
          `Failed to parse Gemini JSON response: ${parseError.message}`,
          rawContent,
        );
        throw new Error(
          `Failed to parse analysis response: ${parseError.message}`,
        );
      }

      // 4. Post-process Result
      const postProcessedResult =
        await this.postProcessingService.processAnalysisResult(
          analysisContent,
          { documentType: detectedType },
        );

      if ('error' in postProcessedResult.data) {
        this.logger.error(
          `Post-processing failed: ${postProcessedResult.data.error}`,
          postProcessedResult.meta?.warnings,
        );
        analysisContent = postProcessedResult.data; // Use the error structure
      } else {
        analysisContent = postProcessedResult.data as Record<string, any>;
        if (postProcessedResult.meta?.warnings?.length) {
          this.logger.warn(
            `Post-processing warnings: ${postProcessedResult.meta.warnings.join(
              ', ',
            )}`,
          );
        }
      }

      // 5. Construct Metadata
      const metadata: AIAnalysisMetadata = {
        provider: providerName,
        modelUsed: completion.model || modelUsed, // Use model from response if available
        promptUsed: promptUsed,
        processingTimeMs: endTime - startTime,
        rateLimitInfo: rateLimitInfo,
        tokenUsage: completion.usage
          ? {
              promptTokens: completion.usage.prompt_tokens,
              completionTokens: completion.usage.completion_tokens,
              totalTokens: completion.usage.total_tokens,
            }
          : undefined,
        finishReason: completion.choices[0]?.finish_reason,
        systemFingerprint: completion.system_fingerprint,
        logId: completion.id, // Use completion ID as log ID
        detectedDocumentType: detectedType,
      };

      this.logger.log(
        `Gemini analysis completed in ${metadata.processingTimeMs}ms. Tokens: ${
          metadata.tokenUsage?.totalTokens || 'N/A'
        }`,
      );

      return {
        content: analysisContent,
        metadata: metadata,
      };
    } catch (error) {
      const endTime = Date.now();
      this.logger.error(
        `Gemini analysis failed: ${error.message}`,
        error.stack,
      );

      // Construct partial metadata for error reporting
      const errorMetadata: Partial<AIAnalysisMetadata> = {
        provider: providerName,
        modelUsed: modelUsed, // Model attempted
        promptUsed: promptUsed,
        processingTimeMs: endTime - startTime,
        rateLimitInfo: this.getRateLimiterUtilization(), // Get current state
        detectedDocumentType: detectedType,
      };

      return {
        content: {
          error: `Analysis failed: ${error.message}`,
          details: error.stack,
        },
        metadata: errorMetadata as AIAnalysisMetadata, // Cast needed as it's partial
      };
    }
  }

  async compareDocuments(
    documents: string[],
    options?: Record<string, any>,
  ): Promise<Record<string, any>> {
    if (!this.isInitialized) throw new Error('Gemini provider not initialized');

    // Create a comparison prompt
    const prompt = `Compare the following documents and provide a structured analysis in JSON format:
${documents.map((doc, i) => `Document ${i + 1}:\n${doc}`).join('\n\n')}`;

    return this.rateLimiter.executeWithRateLimit<Record<string, any>>(
      async () =>
        this.retryUtil.withRetry(async () => {
          try {
            const completion = await this.openai.chat.completions.create({
              model:
                this.configService.get<string>('gemini.modelName') ||
                'gemini-2.0-flash-001',
              messages: [
                {
                  role: 'system',
                  content: this.systemPrompt,
                },
                { role: 'user', content: prompt },
              ],
              temperature:
                options?.temperature ||
                this.configService.get<number>('gemini.temperature') ||
                0.3,
            });

            const response = completion.choices[0]?.message?.content || '';

            // Extract JSON from response
            let jsonStr = this.extractJSONFromText(response);

            try {
              return JSON.parse(jsonStr);
            } catch (error) {
              this.logger.error(
                `Failed to parse document comparison result: ${error.message}`,
              );
              return {
                error: 'Failed to parse document comparison result',
                rawResponse: response,
              };
            }
          } catch (error) {
            this.logger.error(
              `Error in document comparison: ${error.message}`,
              error.stack,
            );
            return {
              error: error.message,
              code: error.code || 'UNKNOWN_ERROR',
            };
          }
        }, 'compareDocuments'),
      'compareDocuments',
    );
  }

  getProviderName(): string {
    return 'Gemini';
  }

  getRateLimiterUtilization(): RateLimiterInfo {
    // Call the public getInfo method from the RateLimiter instance
    return this.rateLimiter.getInfo();
  }

  async identifyClauseSections(
    documentContent: string,
    options?: Record<string, any>,
  ): Promise<ClauseSection[]> {
    this.logger.log('Gemini Provider: Identifying clause sections in document');
    
    if (!this.isInitialized) {
      throw new Error('Gemini provider not initialized');
    }
    
    // Use the enhanced getDocumentAnalysisPrompt function with clause_identification type
    const prompt = this.getDocumentAnalysisPrompt(
      documentContent,
      'clause_identification',
      options?.documentType,
      undefined,
      options
    );

    try {
      // Use Gemini Pro for legal analysis
      const model = 'gemini-pro'; // Default Gemini model for complex tasks
      
      const response = await this.rateLimiter.executeWithRateLimit(() =>
        this.retryUtil.withRetry(async () => {
          const result = await this.openai.chat.completions.create({
            model,
            messages: [
              {
                role: 'system',
                content: 'You are a legal document expert specializing in contract clause identification and analysis.'
              },
              { role: 'user', content: prompt }
            ],
            temperature: 0.2, // Lower temperature for more deterministic results
            response_format: { type: 'json_object' }
          });
          return result.choices[0]?.message?.content || '{}';
        }, 'identifyClauseSections')
      );

      // Parse the response
      try {
        const parsedResponse = JSON.parse(this.extractJSONFromText(response));
        
        // Ensure the response is in the expected format
        if (Array.isArray(parsedResponse)) {
          return parsedResponse.map(clause => ({
            name: clause.name || `Clause ${clause.type || 'Unnamed'}`,
            category: clause.category || clause.type || 'General',
            content: clause.content,
            startIndex: parseInt(clause.startIndex, 10),
            endIndex: parseInt(clause.endIndex, 10),
            type: clause.type,
            confidence: parseFloat(clause.confidence) || 0.7,
            metadata: clause.metadata || {}
          }));
        }
        
        this.logger.warn('Unexpected response format from Gemini for clause identification');
        return [];
      } catch (parseError) {
        this.logger.error(`Error parsing clause identification response: ${parseError.message}`, parseError.stack);
        return [];
      }
    } catch (error) {
      this.logger.error(`Error identifying clause sections with Gemini: ${error.message}`, error.stack);
      return [];
    }
  }

  async optimizeClauseTemplate(
    content: string,
    options?: Record<string, any>,
  ): Promise<string> {
    this.logger.log('Gemini Provider: Optimizing clause template');
    
    if (!this.isInitialized) {
      throw new Error('Gemini provider not initialized');
    }
    
    // Use the enhanced getDocumentAnalysisPrompt function with clause_optimization type
    const prompt = this.getDocumentAnalysisPrompt(
      content,
      'clause_optimization',
      options?.documentType,
      undefined,
      { content }
    );

    try {
      // Use Gemini Pro for legal analysis
      const model = 'gemini-pro'; // Default Gemini model for complex tasks
      
      const response = await this.rateLimiter.executeWithRateLimit(() =>
        this.retryUtil.withRetry(async () => {
          const result = await this.openai.chat.completions.create({
            model,
            messages: [
              {
                role: 'system',
                content: 'You are a legal document expert specializing in contract drafting and optimization.'
              },
              { role: 'user', content: prompt }
            ],
            temperature: 0.3, // Slightly higher temperature for creative improvements
          });
          return result.choices[0]?.message?.content || content;
        }, 'optimizeClauseTemplate')
      );

      return response.trim();
    } catch (error) {
      this.logger.error(`Error optimizing clause template with Gemini: ${error.message}`, error.stack);
      return content; // Return original content if optimization fails
    }
  }

  // Helper method to extract JSON from text responses
  private extractJSONFromText(text: string): string {
    const jsonRegex = /```json\s*([\s\S]*?)\s*```|({.*})|(\[.*\])/s;
    const match = text.match(jsonRegex);
    if (match) {
      return match[1] || match[2] || match[3];
    }
    // If no standard markers, attempt to find JSON starting with { or [
    const firstChar = text.trim().charAt(0);
    if (firstChar === '{' || firstChar === '[') {
      // Be cautious here, might grab too much or invalid JSON
      // Attempt to find the last closing brace/bracket
      let balance = 0;
      let endIndex = -1;
      const openChar = firstChar;
      const closeChar = firstChar === '{' ? '}' : ']';
      for (let i = 0; i < text.length; i++) {
        if (text[i] === openChar) balance++;
        if (text[i] === closeChar) balance--;
        if (balance === 0 && text[i] === closeChar) {
          endIndex = i;
          break;
        }
      }
      if (endIndex !== -1) {
        try {
          // Validate if the extracted part is valid JSON
          JSON.parse(text.substring(0, endIndex + 1));
          return text.substring(0, endIndex + 1);
        } catch (e) {
          this.logger.warn(
            'Attempted JSON extraction based on braces/brackets failed validation.',
          );
        }
      }
    }
    this.logger.warn(
      'Could not extract JSON from text, returning original text.',
    );
    return text; // Return original text if no JSON found or validated
  }

  async isReady(): Promise<boolean> {
    // Just check if initialized based on API key presence
    const ready = !!this.isInitialized;
    if (!ready) {
      this.logger.warn('Gemini Provider is not initialized (missing API key?)');
    }
    return ready;
  }

  /**
   * Gets the appropriate prompt for document analysis based on document type and analysis type
   * @param documentContent The document text to analyze
   * @param analysisType The type of analysis to perform (general, clause_identification, clause_optimization)
   * @param documentType Optional document type override
   * @param query Optional user query to focus the analysis
   * @param options Optional additional parameters for specific analysis types
   * @returns Formatted prompt text
   */
  private getDocumentAnalysisPrompt(
    documentContent: string,
    analysisType: 'general' | 'clause_identification' | 'clause_optimization' = 'general',
    documentType?: EnumDocumentType,
    query?: string,
    options?: Record<string, any>,
  ): string {
    // If documentType is provided, use it; otherwise let the service detect it
    const detectedType =
      documentType ||
      this.promptTemplateService.detectDocumentType(documentContent);
    
    // Handle specialized analysis types
    if (analysisType === 'clause_identification') {
      return `Analyze the following legal document and identify distinct clause sections. For each clause, provide the exact text content, the starting and ending character positions in the document, and the type of clause (e.g., confidentiality, liability, termination, etc.).\n\nDocument:\n${documentContent}\n\nOutput the results in the following JSON format:\n[\n  {\n    "content": "clause text",\n    "startIndex": startPosition,\n    "endIndex": endPosition,\n    "type": "clause type",\n    "confidence": confidenceScore\n  }\n]`;
    }
    
    if (analysisType === 'clause_optimization') {
      const content = options?.content || documentContent;
      return `You are a legal document expert. Optimize the following legal clause to make it more clear, concise, and legally sound. Maintain the original intent and legal meaning, but improve the language, structure, and readability.\n\nOriginal Clause:\n${content}\n\nOptimized Clause:`;
    }

    // For general analysis, use the standard template-based prompt
    const templatePrompt =
      this.promptTemplateService.generateDocumentAnalysisPrompt(
        documentContent,
        query,
      );

    // Append JSON formatting instructions for document analysis
    return `${templatePrompt}

    Format your response as JSON with the following structure:
    {
      "sections": [
        {
          "title": "Section title",
          "purpose": "Brief description of section purpose",
          "startIndex": 0,
          "endIndex": 0
        }
      ],
      "clauses": [
        {
          "title": "Clause title or identifier",
          "type": "obligation|prohibition|permission|definition|termination|liability|confidentiality|other",
          "content": "The exact text of the clause",
          "riskLevel": "low|medium|high",
          "riskDescription": "Description of the risk, if any"
        }
      ],
      "summary": "Overall summary of the document"
    }`;
  }

  // extractJSONFromText is already defined above

  /**
   * Helper method to convert between document type enums
   * @param documentType Source document type from enum
   * @returns Corresponding prompt template document type
   */
  private mapDocumentType(documentType: EnumDocumentType): PromptDocumentType {
    switch (documentType) {
      case EnumDocumentType.CONTRACT:
        return PromptDocumentType.CONTRACT;
      case EnumDocumentType.AGREEMENT:
        return PromptDocumentType.AGREEMENT;
      case EnumDocumentType.LEGAL_OPINION:
        return PromptDocumentType.LEGAL_OPINION;
      case EnumDocumentType.POLICY:
        return PromptDocumentType.POLICY;
      case EnumDocumentType.LEGISLATION:
        return PromptDocumentType.LEGISLATION;
      case EnumDocumentType.COURT_FILING:
        return PromptDocumentType.COURT_FILING;
      case EnumDocumentType.GENERAL:
      default:
        return PromptDocumentType.GENERAL;
    }
  }

  private mapMessagesToProviderFormat(
    messages: ChatMessage[],
  ): OpenAI.Chat.Completions.ChatCompletionMessageParam[] {
    return messages.map((msg) => ({
      role: msg.role === ChatRole.USER ? 'user' : 'assistant', // Map system role if needed
      content: msg.content,
    }));
  }

  async generateSessionTitle(document: any): Promise<string> {
    if (!this.isInitialized) {
      throw new Error('Gemini provider not initialized');
    }

    const docInfo = [];
    if (document.originalName)
      docInfo.push(`Document name: ${document.originalName}`);
    if (document.content) {
      docInfo.push(`Content preview: ${document.content.substring(0, 500)}...`);
    }
    if (document.metadata?.summary)
      docInfo.push(`Summary: ${document.metadata.summary}`);

    const prompt = `Generate a brief, descriptive title (max 6 words) for a chat session about the following document:\n\n${docInfo.join(
      '\n',
    )}\n\nTitle:`;

    return this.generateResponse(prompt, {
      temperature: this.configService.get<number>('gemini.temperature') || 0.7,
      maxTokens: 50,
    }).then((title) => title.replace(/^["']|["']$/g, '').trim());
  }

  /**
   * Analyze a deposition transcript for credibility, inconsistencies, and cross-examination opportunities
   * @param transcript The deposition transcript text to analyze
   * @param context Additional context for the analysis
   * @returns Promise containing the deposition analysis result
   */
  async analyzeDeposition(
    transcript: string,
    context: {
      caseContext?: string;
      focusAreas?: string[];
    } = {},
  ): Promise<DepositionAnalysisResult> {
    this.logger.log('Gemini Provider: Analyzing deposition');
    
    if (!this.isInitialized) {
      throw new Error('Gemini provider not initialized');
    }

    try {
      // Generate the prompt using the helper function
      const prompt = generateDepositionAnalysisPrompt({
        transcript,
        caseContext: context.caseContext,
        focusAreas: context.focusAreas,
      });

      // Use the provider's generateResponse method
      const response = await this.generateResponse(prompt, {
        temperature: 0.2,
        maxTokens: 4000,
      });

      try {
        // Extract JSON from markdown code blocks if present
        const jsonMatch = response.match(/```(?:json)?\n([\s\S]*?)\n```/);
        const jsonString = jsonMatch ? jsonMatch[1] : response;
        const parsedResult = JSON.parse(jsonString);
        
        // Add metadata to the result
        return {
          ...parsedResult,
          metadata: {
            ...parsedResult.metadata,
            analyzedAt: new Date().toISOString(),
            modelUsed: 'gemini',
            confidence: parsedResult.metadata?.confidence || 'medium',
          },
        };
      } catch (parseError) {
        this.logger.error(`Failed to parse deposition analysis response: ${parseError.message}`, parseError.stack);
        throw new Error('Failed to parse AI response as JSON');
      }
    } catch (error) {
      this.logger.error(`Error analyzing deposition with Gemini: ${error.message}`, error.stack);
      throw new Error(`Failed to analyze deposition: ${error.message}`);
    }
  }
}

import { RateLimiterInfo } from '../../../common/interfaces/rate-limiter.interface';
import { DocumentType as PromptDocumentType } from '../../prompt-templates/interfaces/prompt-template.interface';
import { Citation } from '../../legal-research/interfaces/citation.interface';

/**
 * Represents token usage statistics from an AI provider.
 */
export interface AITokenUsage {
  promptTokens?: number;
  completionTokens?: number;
  totalTokens?: number;
}

/**
 * Metadata associated with an AI analysis operation.
 */
export interface AIAnalysisMetadata {
  provider: string; // e.g., 'openai', 'gemini'
  modelUsed: string; // Specific model name, e.g., 'gpt-4-turbo', 'gemini-1.5-pro'
  promptUsed?: string; // The actual prompt sent (optional, could be large)
  processingTimeMs?: number; // Time taken for the AI call in milliseconds
  rateLimitInfo?: RateLimiterInfo; // Rate limit status after the call
  tokenUsage?: AITokenUsage; // Token usage details
  finishReason?: string; // Reason the model stopped generating tokens
  systemFingerprint?: string; // System fingerprint from the provider (if available)
  logId?: string; // Log or request ID from the provider (if available)
  detectedDocumentType?: PromptDocumentType; // Document type detected by the system
  legalCitations?: Citation[]; // Store enriched legal citations here
}

/**
 * Represents the structured result of an AI document analysis,
 * including the analysis content and metadata about the operation.
 */
export interface AIAnalysisResult {
  content: Record<string, any>; // The core analysis object/JSON (renamed from analysisContent)
  metadata: AIAnalysisMetadata;
}

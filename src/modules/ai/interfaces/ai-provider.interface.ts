import { RateLimiterInfo } from '../../../common/interfaces/rate-limiter.interface';
import { ChatMessage } from '../../../common/interfaces/chat.interface';
import { AIAnalysisResult } from './ai-analysis-result.interface';
import { ClauseSection } from './clause-section.interface';
import { DepositionAnalysisResult } from './deposition-analysis.interface';

/**
 * Interface defining the contract for AI providers
 */
export interface AIProvider {
  /**
   * Generate a response based on the provided prompt
   * @param prompt The input prompt
   * @param options Optional provider-specific configuration
   * @returns Promise containing the generated response
   */
  generateResponse(
    prompt: string,
    options?: Record<string, any>,
  ): Promise<string>;

  /**
   * Generate a stream of responses based on the provided prompt
   * @param prompt The input prompt
   * @param options Optional provider-specific configuration
   * @returns AsyncGenerator yielding response chunks
   */
  generateStreamResponse(
    prompt: string,
    options?: Record<string, any>,
  ): AsyncGenerator<string, void, unknown>;

  /**
   * Analyze a document using the AI provider
   * @param content Document content to analyze
   * @param options Optional provider-specific configuration
   * @returns Promise containing the analysis result
   */
  analyzeDocument(
    content: string,
    options?: Record<string, any>,
  ): Promise<AIAnalysisResult>;

  /**
   * Compare multiple documents using the AI provider
   * @param documents Array of document contents to compare
   * @param options Optional provider-specific configuration
   * @returns Promise containing the comparison result
   */
  compareDocuments(
    documents: string[],
    options?: Record<string, any>,
  ): Promise<Record<string, any>>;

  /**
   * Generate a conversational response based on chat history
   * @param messages Array of chat messages representing the conversation history
   * @param options Optional provider-specific configuration
   * @returns Promise containing the generated natural language response string
   */
  generateChatResponse(
    messages: ChatMessage[],
    options?: Record<string, any>,
  ): Promise<string>;

  /**
   * Get the name of the AI provider
   * @returns The provider name
   */
  getProviderName(): string;

  /**
   * Checks if the provider is ready and initialized.
   * @returns Promise resolving to true if ready, false otherwise.
   */
  isReady(): Promise<boolean>;

  /**
   * Gets the current rate limiter utilization information.
   * @returns RateLimiterInfo object containing utilization details.
   */
  getRateLimiterUtilization(): RateLimiterInfo;

  /**
   * Generate a title for a chat session based on document content
   * @param document The document object containing content and metadata
   * @returns Promise containing the generated title
   */
  generateSessionTitle(document: any): Promise<string>;

  /**
   * Identify potential clause sections within a document
   * @param documentContent The content of the document to analyze
   * @param options Optional provider-specific configuration
   * @returns Promise containing an array of identified clause sections
   */
  identifyClauseSections(
    documentContent: string,
    options?: Record<string, any>,
  ): Promise<ClauseSection[]>;

  /**
   * Analyze a deposition transcript for credibility, inconsistencies, and cross-examination opportunities
   * @param transcript The deposition transcript text to analyze
   * @param context Additional context for the analysis
   * @returns Promise containing the deposition analysis result
   */
  analyzeDeposition(
    transcript: string,
    context?: {
      caseContext?: string;
      focusAreas?: string[];
    },
  ): Promise<DepositionAnalysisResult>;

  /**
   * Optimize a clause template using AI
   * @param content The original clause content to optimize
   * @param options Optional provider-specific configuration
   * @returns Promise containing the optimized clause template
   */
  optimizeClauseTemplate(
    content: string,
    options?: Record<string, any>,
  ): Promise<string>;

  /**
   * Generate questions for deposition preparation
   * @param context Object containing case context and options for question generation
   * @returns Promise containing the generated questions
   */
  generateDepositionQuestions(context: {
    caseContext: string;
    keyIssues: string[];
    targetWitnesses?: string[];
    options?: {
      questionCount?: number;
      questionCategories?: string[];
      includeFollowUps?: boolean;
      focusAreas?: string[];
    };
  }): Promise<any>;
}

/**
 * Interface representing a section of text identified as a legal clause
 */
export interface ClauseSection {
  /**
   * The name or title of the clause
   */
  name: string;

  /**
   * The content of the identified clause
   */
  content: string;
  
  /**
   * The starting index of the clause in the original document
   */
  startIndex: number;
  
  /**
   * The ending index of the clause in the original document
   */
  endIndex: number;
  
  /**
   * The category of the clause (e.g., indemnification, termination, etc.)
   */
  category: string;

  /**
   * Optional type or category of the clause (legacy field)
   */
  type?: string;
  
  /**
   * Optional confidence score (0.0-1.0) for the clause identification
   */
  confidence?: number;
  
  /**
   * Optional metadata about the clause
   */
  metadata?: Record<string, any>;
}

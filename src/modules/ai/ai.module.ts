import { Module, forwardRef } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AIService } from './services/ai.service';
import { OpenAIProvider } from './providers/openai.provider';
import { GeminiProvider } from './providers/gemini.provider';
import { AIController } from './controllers/ai.controller';
import { PromptTemplateModule } from '../prompt-templates/prompt-template.module';
import { DocumentContextModule } from '../document-context/document-context.module';
import { PostProcessingModule } from '../post-processing/post-processing.module';
import { DocumentsModule } from '../documents/documents.module';
import { ContextManagementModule } from '../context-management/context-management.module';
import { openaiConfig } from '../../config/openai.config';
import { geminiConfig } from '../../config/gemini.config';

@Module({
  imports: [
    ConfigModule.forFeature(openaiConfig),
    ConfigModule.forFeature(geminiConfig),
    PromptTemplateModule,
    DocumentContextModule,
    PostProcessingModule,
    forwardRef(() => DocumentsModule),
    forwardRef(() => ContextManagementModule),
  ],
  controllers: [AIController],
  providers: [AIService, OpenAIProvider, GeminiProvider],
  exports: [AIService, OpenAIProvider, GeminiProvider],
})
export class AIModule {}

import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  DocumentType,
  PromptTemplate,
} from './interfaces/prompt-template.interface';
import { AIService } from '../ai/services/ai.service';

@Injectable()
export class PromptTemplateService {
  private readonly logger = new Logger(PromptTemplateService.name);
  private readonly templates: PromptTemplate[];

  // Enhanced template constants
  private static readonly DOCUMENT_START = '[DOCUMENT START]';
  private static readonly DOCUMENT_END = '[DOCUMENT END]';
  private static readonly ERROR_PHRASES = [
    'need the document',
    'provide the document',
    'without the document',
    'document not provided',
  ];

  constructor(
    private configService: ConfigService,
    @Inject(forwardRef(() => AIService))
    private aiService: AIService,
  ) {
    this.templates = this.configService.get('promptTemplates.templates');
    this.logger.log(
      `Initialized with ${this.templates.length} prompt templates`,
    );
  }

  async generateEnhancedChatPrompt(
    context: any,
    query: string,
  ): Promise<string> {
    this.logger.debug(`Generating enhanced prompt for query: ${query}`);
    this.logger.debug(
      `Context content length: ${context?.content?.length || 0}`,
    );

    if (!context.content) {
      this.logger.warn(
        'No document content provided to generateEnhancedChatPrompt',
      );
      return '';
    }

    // For direct questions, use simplified template with explicit content validation
    if (query && context.content) {
      this.logger.debug('Using direct question template');
      const contentStart = context.content.substring(0, 100);
      this.logger.debug(`Content preview: ${contentStart}...`);

      // Ensure content has our markers
      const hasMarkers = context.content.includes(
        '=== DOCUMENT CONTENT START ===',
      );
      this.logger.debug(`Content has markers: ${hasMarkers}`);

      return this.renderTemplate('', {
        documentContent: context.content,
        chatHistory: context.chatHistory,
        query,
      });
    }

    // Use analysis template for document analysis requests
    this.logger.debug('Using analysis template');
    const documentType = await this.detectDocumentType(context.content);
    this.logger.debug(`Detected document type: ${documentType}`);
    const template = this.getTemplateByType(documentType);

    return this.renderTemplate(template.template, {
      documentContent: context.content,
      chatHistory: context.chatHistory,
      query,
      documentMetadata: context.metadata || '',
      relatedDocuments: context.relatedDocuments || [],
    });
  }

  async generateChatPrompt(
    documentContent: string,
    chatHistory: string,
    query: string,
  ): Promise<string> {
    const documentType = await this.detectDocumentType(documentContent);
    const template = this.getTemplateByType(documentType);

    return this.renderTemplate(template.template, {
      documentContent,
      chatHistory,
      query,
    });
  }
  async generateDocumentAnalysisPrompt(
    content: string,
    query?: string,
  ): Promise<string> {
    const documentType = await this.detectDocumentType(content);
    const template = this.getTemplateByType(documentType);

    return this.renderTemplate(template.template, {
      documentContent: content,
      query: query || '',
    });
  }

  async detectDocumentType(content: string): Promise<DocumentType> {
    // First try basic keyword matching
    const documentType = this.detectDocumentTypeByKeywords(content);
    if (documentType !== DocumentType.GENERAL) {
      return documentType;
    }

    // If basic detection fails, use LLM for advanced analysis
    return this.detectDocumentTypeWithLLM(content);
  }

  private detectDocumentTypeByKeywords(content: string): DocumentType {
    const lowercaseContent = content.toLowerCase();

    if (
      lowercaseContent.includes('memorandum') ||
      lowercaseContent.includes('memo')
    ) {
      return DocumentType.LEGAL_OPINION;
    }
    if (
      lowercaseContent.includes('service agreement') ||
      lowercaseContent.includes('services agreement')
    ) {
      return DocumentType.AGREEMENT;
    }
    if (
      lowercaseContent.includes('court') ||
      lowercaseContent.includes('case no.') ||
      lowercaseContent.includes('plaintiff')
    ) {
      return DocumentType.COURT_FILING;
    }
    if (
      lowercaseContent.includes('policy') ||
      lowercaseContent.includes('procedure')
    ) {
      return DocumentType.POLICY;
    }
    if (
      lowercaseContent.includes('act') ||
      lowercaseContent.includes('statute') ||
      lowercaseContent.includes('regulation')
    ) {
      return DocumentType.LEGISLATION;
    }
    if (
      lowercaseContent.includes('contract') ||
      lowercaseContent.includes('between')
    ) {
      return DocumentType.CONTRACT;
    }

    return DocumentType.GENERAL;
  }

  private async detectDocumentTypeWithLLM(
    content: string,
  ): Promise<DocumentType> {
    const prompt = `Please analyze this legal document and determine its type based on its structure, content, and purpose. 
    Document content:
    ${content}

    Classify this document as one of the following types:
    - CONTRACT (if it's a binding agreement establishing specific obligations)
    - AGREEMENT (if it's a service agreement, employment agreement, etc.)
    - LEGAL_OPINION (if it's a memorandum, legal analysis, or opinion)
    - POLICY (if it's a policy document or procedures)
    - LEGISLATION (if it's a law, regulation, or statute)
    - COURT_FILING (if it's a legal filing, brief, or court document)

    Respond with just the type (e.g., "CONTRACT"). Base your classification on the document's content, structure, and purpose, not just keywords.`;

    try {
      const response = await this.aiService.generateResponse(prompt);
      const detectedType = response.trim().toUpperCase();

      // Validate that the response matches a known DocumentType
      if (Object.values(DocumentType).includes(detectedType as DocumentType)) {
        this.logger.log(`LLM classified document as: ${detectedType}`);
        return detectedType as DocumentType;
      }
    } catch (error) {
      this.logger.error(
        `Error in LLM document type detection: ${error.message}`,
      );
    }

    this.logger.warn('Using general template as fallback');
    return DocumentType.GENERAL;
  }

  public getTemplateByType(documentType: DocumentType): PromptTemplate {
    const template =
      this.templates.find((t) => t.documentType === documentType) ||
      this.templates.find((t) => t.documentType === DocumentType.GENERAL);

    if (!template) {
      this.logger.error(
        `No template found for document type ${documentType} and no general template available`,
      );
      throw new Error(
        `No suitable template found for document type: ${documentType}`,
      );
    }

    return template;
  }
  public renderTemplate(
    template: string,
    variables: Record<string, any>,
  ): string {
    this.logger.debug('Processing template:', {
      hasTemplate: !!template,
      hasQuery: !!variables.query,
      hasContent: !!variables.documentContent,
    });

    if (variables.query && variables.documentContent) {
      const content = variables.documentContent.toString().trim();
      if (!content) {
        return 'Error: Document content cannot be empty';
      }

      const markedContent = content.includes(
        PromptTemplateService.DOCUMENT_START,
      )
        ? content
        : `${PromptTemplateService.DOCUMENT_START}\n${content}\n${PromptTemplateService.DOCUMENT_END}`;

      return this.generateDocumentPrompt(
        markedContent,
        variables.chatHistory,
        variables.query,
      );
    }

    if (!template) {
      return '';
    }

    return this.processTemplateWithVariables(template, variables);
  }

  private generateDocumentPrompt(
    content: string,
    chatHistory?: string,
    query?: string,
  ): string {
    return `[SYSTEM NOTICE]
You are a legal AI assistant analyzing the document provided between markers below.
IMPORTANT: You already have the complete document - it is provided in your context.

[DOCUMENT CONTENT START]
${content}
[DOCUMENT CONTENT END]

${
  chatHistory
    ? `[PREVIOUS CONVERSATION]\n${chatHistory}\n[END CONVERSATION]\n\n`
    : ''
}
[USER QUERY] ${query || ''}

[CRITICAL INSTRUCTIONS - READ CAREFULLY]
1. The document content above is complete and available to you
2. You are FORBIDDEN from saying:
   ❌ "I need the document"
   ❌ "Please provide the document"
   ❌ "Without the document"
   ❌ "Document not provided"
3. If you can't find specific information, say:
   ✓ "The document does not mention [topic]"
   ✓ "I found no information about [topic] in the document"
4. Always reference specific parts of the text
5. Base ALL answers on the document content only

[BEFORE YOU RESPOND]
• The document IS present ↑ above ↑ - check between the markers
• If you think "I need the document" - STOP - Look at [DOCUMENT CONTENT]
• You have everything needed to analyze this document

Now proceed with your analysis based on the document content above.`;
  }

  // Helper method to process standard templates with variables
  private processTemplateWithVariables(
    template: string,
    variables: Record<string, any>,
  ): string {
    let result = template;

    // Replace template variables
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{{${key}}}`;
      result = result.replace(
        new RegExp(placeholder, 'g'),
        value?.toString() || '',
      );
    });

    // Process conditional blocks
    const ifRegex = /{{#if\s+(\w+)}}([\s\S]*?){{\/if}}/g;
    result = result.replace(ifRegex, (match, condition, content) => {
      return variables[condition] ? content.trim() : '';
    });

    // Add standard instructions
    if (result.includes('{{instructions}}')) {
      result = result.replace(
        '{{instructions}}',
        '\n\nPlease provide a clear, structured response. Reference specific sections when relevant.',
      );
    }

    return result;
  }
}

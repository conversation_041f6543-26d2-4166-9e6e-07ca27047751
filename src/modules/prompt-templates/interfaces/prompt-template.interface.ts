export enum DocumentType {
  CONTRACT = 'contract',
  AGREEMENT = 'agreement',
  LEGAL_OPINION = 'legal_opinion',
  POLICY = 'policy',
  LEGISLATION = 'legislation',
  COURT_FILING = 'court_filing',
  GENERAL = 'general'
}

export interface PromptTemplate {
  id: string;
  name: string;
  documentType: DocumentType;
  description: string;
  template: string;
  version?: string;
  model?: string; // Optional model name to use for this template
  metadata?: Record<string, any>;
}

import { Module, NestModule, MiddlewareConsumer, RequestMethod } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ActivityLoggingService } from './services/activity-logging.service';
import { ActivityLoggingController } from './controllers/activity-logging.controller';
import { ActivityLog, ActivityLogSchema, ACTIVITY_LOG_MODEL } from './schemas/activity-log.schema';
import { ActivityTrackingMiddleware } from './middleware/activity-tracking.middleware';
import { AuthModule } from '../auth/auth.module';
import { SubscriptionModule } from '../subscription/subscription.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ACTIVITY_LOG_MODEL, schema: ActivityLogSchema },
    ]),
    AuthModule,
    SubscriptionModule,
  ],
  controllers: [ActivityLoggingController],
  providers: [ActivityLoggingService],
  exports: [ActivityLoggingService],
})
export class ActivityLoggingModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(ActivityTrackingMiddleware)
      .forRoutes(
        { path: 'documents/*', method: RequestMethod.ALL },
        { path: 'enhanced-comparison/*', method: RequestMethod.ALL },
      );
  }
}

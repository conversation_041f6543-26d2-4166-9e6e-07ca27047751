import { 
  Controller, 
  Get, 
  Post, 
  Body, 
  Query, 
  Param, 
  UseGuards, 
  Logger,
  Request,
  ValidationPipe
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBadRequestResponse, ApiQuery } from '@nestjs/swagger';
import { ActivityLoggingService } from '../services/activity-logging.service';
import { CreateActivityLogDto, EntityType } from '../dto/create-activity-log.dto';
import { ActivityLogQueryDto } from '../dto/activity-log-query.dto';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { Organization } from '../../auth/decorators/organization.decorator';
import { RequireFeatures } from '../../subscription/decorators/require-features.decorator';
import { FeatureAvailabilityGuard } from '../../subscription/guards/feature-availability.guard';
import { Public } from '../../auth/decorators/public.decorator';

@ApiTags('activity-logging')
@Controller('activity-logs')
@UseGuards(JwtAuthGuard)
// Temporarily removed FeatureAvailabilityGuard for testing
export class ActivityLoggingController {
  private readonly logger = new Logger(ActivityLoggingController.name);

  constructor(private readonly activityLoggingService: ActivityLoggingService) {}

  @Post()
  // Temporarily commented out for testing
  // @RequireFeatures('activity_logging')
  @ApiOperation({ summary: 'Create a new activity log entry' })
  @ApiResponse({ status: 201, description: 'Activity log entry created successfully' })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  async createActivityLog(
    @Body(new ValidationPipe()) createActivityLogDto: CreateActivityLogDto,
    @Organization() organizationId: string,
    @Request() req: any
  ) {
    // Add request metadata if not provided
    if (!createActivityLogDto.organizationId) {
      createActivityLogDto.organizationId = organizationId;
    }
    
    if (!createActivityLogDto.ipAddress && req.ip) {
      createActivityLogDto.ipAddress = req.ip;
    }
    
    if (!createActivityLogDto.userAgent && req.headers['user-agent']) {
      createActivityLogDto.userAgent = req.headers['user-agent'];
    }
    
    return this.activityLoggingService.logActivity(createActivityLogDto);
  }

  @Get()
  // Temporarily commented out for testing
  // @RequireFeatures('activity_logging')
  @ApiOperation({ summary: 'Get activity logs with filtering and pagination' })
  @ApiResponse({ status: 200, description: 'Activity logs retrieved successfully' })
  async getActivityLogs(
    @Organization() organizationId: string,
    @Query(new ValidationPipe({ transform: true })) query: ActivityLogQueryDto
  ) {
    return this.activityLoggingService.getActivityLogs(organizationId, query);
  }

  @Get('timeline/:entityType/:entityId')
  // Temporarily commented out for testing
  // @RequireFeatures('activity_logging')
  @ApiOperation({ summary: 'Get activity timeline for a specific entity' })
  @ApiResponse({ status: 200, description: 'Entity timeline retrieved successfully' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Maximum number of records to return' })
  async getEntityTimeline(
    @Organization() organizationId: string,
    @Param('entityType') entityType: EntityType,
    @Param('entityId') entityId: string,
    @Query('limit') limit?: number
  ) {
    return this.activityLoggingService.getEntityTimeline(
      organizationId,
      entityType,
      entityId,
      limit ? parseInt(limit.toString(), 10) : undefined
    );
  }

  @Get('user/:userId')
  // Temporarily commented out for testing
  // @RequireFeatures('activity_logging')
  @ApiOperation({ summary: 'Get activity history for a specific user' })
  @ApiResponse({ status: 200, description: 'User activity history retrieved successfully' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Maximum number of records to return' })
  async getUserActivityHistory(
    @Organization() organizationId: string,
    @Param('userId') userId: string,
    @Query('limit') limit?: number
  ) {
    return this.activityLoggingService.getUserActivityHistory(
      organizationId,
      userId,
      limit ? parseInt(limit.toString(), 10) : undefined
    );
  }

  @Get('statistics')
  // Temporarily commented out for testing
  // @RequireFeatures('activity_logging')
  @ApiOperation({ summary: 'Get activity statistics' })
  @ApiResponse({ status: 200, description: 'Activity statistics retrieved successfully' })
  @ApiQuery({ name: 'startDate', required: false, type: String, description: 'Start date for filtering (ISO format)' })
  @ApiQuery({ name: 'endDate', required: false, type: String, description: 'End date for filtering (ISO format)' })
  async getActivityStatistics(
    @Organization() organizationId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string
  ) {
    return this.activityLoggingService.getActivityStatistics(
      organizationId,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined
    );
  }
}

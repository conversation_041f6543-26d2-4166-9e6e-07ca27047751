import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsEnum, IsObject, IsBoolean } from 'class-validator';

export enum EntityType {
  DOCUMENT = 'document',
  ANALYSIS = 'analysis',
  COMPARISON = 'comparison',
  USER = 'user',
  SYSTEM = 'system',
}

export class CreateActivityLogDto {
  @ApiPropertyOptional({ description: 'Organization ID' })
  @IsString()
  @IsOptional()
  organizationId?: string;

  @ApiProperty({ description: 'User ID who performed the action' })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({ description: 'Action performed', example: ['view', 'edit', 'share', 'delete'] })
  @IsString()
  @IsNotEmpty()
  action: string;

  @ApiProperty({ description: 'Type of entity', enum: EntityType })
  @IsEnum(EntityType)
  @IsNotEmpty()
  entityType: EntityType;

  @ApiProperty({ description: 'ID of the entity' })
  @IsString()
  @IsNotEmpty()
  entityId: string;

  @ApiPropertyOptional({ description: 'Name of the entity' })
  @IsString()
  @IsOptional()
  entityName?: string;

  @ApiPropertyOptional({ description: 'Additional metadata about the activity' })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({ description: 'IP address of the user' })
  @IsString()
  @IsOptional()
  ipAddress?: string;

  @ApiPropertyOptional({ description: 'User agent of the client' })
  @IsString()
  @IsOptional()
  userAgent?: string;

  @ApiPropertyOptional({ description: 'Whether this is a system-generated activity' })
  @IsBoolean()
  @IsOptional()
  isSystem?: boolean;
}

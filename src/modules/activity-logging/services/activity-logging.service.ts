import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  ActivityLog,
  ActivityLogDocument,
  ACTIVITY_LOG_MODEL,
} from '../schemas/activity-log.schema';
import { CreateActivityLogDto } from '../dto/create-activity-log.dto';
import { ActivityLogQueryDto } from '../dto/activity-log-query.dto';
import { TenantContextService } from '../../auth/services/tenant-context.service';

@Injectable()
export class ActivityLoggingService {
  private readonly logger = new Logger(ActivityLoggingService.name);

  constructor(
    @InjectModel(ACTIVITY_LOG_MODEL)
    private activityLogModel: Model<ActivityLogDocument>,
    private readonly tenantContext: TenantContextService,
  ) {}

  /**
   * Log a user activity
   */
  async logActivity(
    createActivityLogDto: CreateActivityLogDto,
  ): Promise<ActivityLogDocument> {
    try {
      this.logger.debug(
        `Logging activity: ${createActivityLogDto.action} for ${createActivityLogDto.entityType}:${createActivityLogDto.entityId}`,
      );

      const activityLog = new this.activityLogModel({
        ...createActivityLogDto,
        // If organizationId is not provided, try to get it from tenant context
        organizationId:
          createActivityLogDto.organizationId ||
          this.tenantContext.getCurrentOrganization(),
      });

      return await activityLog.save();
    } catch (error) {
      this.logger.error(
        `Error logging activity: ${error.message}`,
        error.stack,
      );
      // Don't throw the error - activity logging should not break the main flow
      return null;
    }
  }

  /**
   * Get activity logs with filtering and pagination
   */
  async getActivityLogs(
    organizationId: string,
    query: ActivityLogQueryDto,
  ): Promise<{ logs: ActivityLogDocument[]; total: number; pages: number }> {
    try {
      const {
        userId,
        entityType,
        entityId,
        action,
        startDate,
        endDate,
        page = 1,
        limit = 20,
        sortBy = 'createdAt',
        sortOrder = 'desc',
      } = query;

      // Build filter
      const filter: any = { organizationId };

      if (userId) filter.userId = userId;
      if (entityType) filter.entityType = entityType;
      if (entityId) filter.entityId = entityId;
      if (action) filter.action = action;

      // Date range
      if (startDate || endDate) {
        filter.createdAt = {};
        if (startDate) filter.createdAt.$gte = new Date(startDate);
        if (endDate) filter.createdAt.$lte = new Date(endDate);
      }

      // Calculate pagination
      const skip = (page - 1) * limit;

      // Determine sort direction
      const sortDirection = sortOrder === 'asc' ? 1 : -1;
      const sortOptions = { [sortBy]: sortDirection };

      // Execute query with pagination
      const [logs, total] = await Promise.all([
        this.activityLogModel
          .find(filter)
          .sort(sortOptions as any)
          .skip(skip)
          .limit(limit)
          .exec(),
        this.activityLogModel.countDocuments(filter).exec(),
      ]);

      return {
        logs,
        total,
        pages: Math.ceil(total / limit),
      };
    } catch (error) {
      this.logger.error(
        `Error retrieving activity logs: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get activity timeline for a specific entity
   */
  async getEntityTimeline(
    organizationId: string,
    entityType: string,
    entityId: string,
    limit = 50,
  ): Promise<ActivityLogDocument[]> {
    try {
      return this.activityLogModel
        .find({ organizationId, entityType, entityId })
        .sort({ createdAt: -1 })
        .limit(limit)
        .exec();
    } catch (error) {
      this.logger.error(
        `Error retrieving entity timeline: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get user activity history
   */
  async getUserActivityHistory(
    organizationId: string,
    userId: string,
    limit = 50,
  ): Promise<ActivityLogDocument[]> {
    try {
      return this.activityLogModel
        .find({ organizationId, userId })
        .sort({ createdAt: -1 })
        .limit(limit)
        .exec();
    } catch (error) {
      this.logger.error(
        `Error retrieving user activity history: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get activity statistics
   */
  async getActivityStatistics(
    organizationId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<any> {
    try {
      const dateFilter: any = {};
      if (startDate) dateFilter.$gte = startDate;
      if (endDate) dateFilter.$lte = endDate;

      const filter: any = { organizationId };
      if (Object.keys(dateFilter).length > 0) {
        filter.createdAt = dateFilter;
      }

      // Aggregate activities by type and action
      const activityByType = await this.activityLogModel
        .aggregate([
          { $match: filter },
          {
            $group: {
              _id: { entityType: '$entityType', action: '$action' },
              count: { $sum: 1 },
            },
          },
          { $sort: { count: -1 } },
        ])
        .exec();

      // Aggregate activities by user
      const activityByUser = await this.activityLogModel
        .aggregate([
          { $match: filter },
          {
            $group: {
              _id: '$userId',
              count: { $sum: 1 },
            },
          },
          { $sort: { count: -1 } },
          { $limit: 10 }, // Top 10 users
        ])
        .exec();

      // Aggregate activities by day
      const activityByDay = await this.activityLogModel
        .aggregate([
          { $match: filter },
          {
            $group: {
              _id: {
                year: { $year: '$createdAt' },
                month: { $month: '$createdAt' },
                day: { $dayOfMonth: '$createdAt' },
              },
              count: { $sum: 1 },
            },
          },
          { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } },
        ])
        .exec();

      return {
        activityByType,
        activityByUser,
        activityByDay,
        totalActivities: await this.activityLogModel
          .countDocuments(filter)
          .exec(),
      };
    } catch (error) {
      this.logger.error(
        `Error retrieving activity statistics: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}

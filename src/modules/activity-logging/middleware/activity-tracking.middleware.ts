import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ActivityLoggingService } from '../services/activity-logging.service';
import { EntityType } from '../dto/create-activity-log.dto';

@Injectable()
export class ActivityTrackingMiddleware implements NestMiddleware {
  private readonly logger = new Logger(ActivityTrackingMiddleware.name);
  private readonly TRACKED_PATHS = [
    { path: /^\/documents\/([^\/]+)$/, method: 'GET', action: 'view', entityType: EntityType.DOCUMENT },
    { path: /^\/documents\/([^\/]+)$/, method: 'PUT', action: 'update', entityType: EntityType.DOCUMENT },
    { path: /^\/documents\/([^\/]+)$/, method: 'DELETE', action: 'delete', entityType: EntityType.DOCUMENT },
    { path: /^\/documents\/([^\/]+)\/analysis$/, method: 'GET', action: 'view_analysis', entityType: EntityType.ANALYSIS },
    { path: /^\/documents\/([^\/]+)\/analysis\/compare$/, method: 'GET', action: 'compare_analysis', entityType: EntityType.ANALYSIS },
    { path: /^\/documents\/([^\/]+)\/analysis\/compare$/, method: 'POST', action: 'compare_analysis', entityType: EntityType.ANALYSIS },
    { path: /^\/enhanced-comparison\/documents$/, method: 'POST', action: 'enhanced_compare', entityType: EntityType.COMPARISON },
    { path: /^\/enhanced-comparison\/versions\/([^\/]+)$/, method: 'POST', action: 'compare_versions', entityType: EntityType.COMPARISON },
  ];

  constructor(private readonly activityLoggingService: ActivityLoggingService) {}

  async use(req: Request, res: Response, next: NextFunction) {
    // Skip OPTIONS requests and non-API routes
    if (req.method === 'OPTIONS' || !req.path.startsWith('/')) {
      return next();
    }

    // Capture original end method to intercept after response is sent
    const originalEnd = res.end;
    const self = this; // Store reference to this for use in the closure

    // Only track successful responses
    res.end = function(this: Response, ...args: any[]): Response {
      // Restore original end method
      res.end = originalEnd;
      
      // Call the original end method
      const result = originalEnd.apply(res, args);
      
      // Only log activities for successful responses
      if (res.statusCode >= 200 && res.statusCode < 300) {
        // Process after response is sent to not block the client
        setImmediate(async () => {
          try {
            const matchedRoute = self.findMatchingRoute(req);
            if (matchedRoute) {
              await self.logActivity(req, matchedRoute);
            }
          } catch (error) {
            self.logger.error(`Error in activity tracking middleware: ${error.message}`, error.stack);
          }
        });
      }
      
      return result;
    };

    next();
  }

  private findMatchingRoute(req: Request): { entityId: string, action: string, entityType: EntityType } | null {
    for (const route of this.TRACKED_PATHS) {
      if (req.method === route.method) {
        const match = req.path.match(route.path);
        if (match) {
          return {
            entityId: match[1], // Extract ID from path
            action: route.action,
            entityType: route.entityType,
          };
        }
      }
    }
    return null;
  }

  private async logActivity(req: Request, matchedRoute: { entityId: string, action: string, entityType: EntityType }) {
    try {
      // Extract user and organization from request
      const user = req.user as any; // Type assertion to access properties
      
      if (!user || !user.sub || !user.organizationId) {
        this.logger.debug('Skipping activity logging: missing user or organization ID');
        return;
      }

      // Extract entity name if available in the request body
      let entityName;
      if (req.body && typeof req.body === 'object') {
        entityName = req.body.title || req.body.name;
      }

      // Create activity log
      await this.activityLoggingService.logActivity({
        userId: user.sub, // Using sub as the user ID from JWT
        organizationId: user.organizationId,
        action: matchedRoute.action,
        entityType: matchedRoute.entityType,
        entityId: matchedRoute.entityId,
        entityName,
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'] as string,
        isSystem: false,
        metadata: {
          method: req.method,
          path: req.path,
          query: req.query,
        },
      });
    } catch (error) {
      this.logger.error(`Failed to log activity: ${error.message}`, error.stack);
    }
  }
}

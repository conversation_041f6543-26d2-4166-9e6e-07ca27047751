import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document as MongoDocument } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

@Schema({ timestamps: true })
export class ActivityLog {
  @Prop({ required: true, default: () => uuidv4() })
  id: string;

  @Prop({ required: true })
  organizationId: string;

  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  action: string;

  @Prop({ required: true, enum: ['document', 'analysis', 'comparison', 'user', 'system'] })
  entityType: string;

  @Prop({ required: true })
  entityId: string;

  @Prop()
  entityName?: string;

  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>;

  @Prop()
  ipAddress?: string;

  @Prop()
  userAgent?: string;

  @Prop({ default: false })
  isSystem: boolean;
}

export type ActivityLogDocument = ActivityLog & MongoDocument;
export const ACTIVITY_LOG_MODEL = 'ActivityLog';
export const ActivityLogSchema = SchemaFactory.createForClass(ActivityLog);

// Add indexes for efficient querying
ActivityLogSchema.index({ organizationId: 1, createdAt: -1 });
ActivityLogSchema.index({ userId: 1, createdAt: -1 });
ActivityLogSchema.index({ entityType: 1, entityId: 1, createdAt: -1 });
ActivityLogSchema.index({ action: 1, createdAt: -1 });

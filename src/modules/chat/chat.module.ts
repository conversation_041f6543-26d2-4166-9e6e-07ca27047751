import { Modu<PERSON>, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ChatMigrationService } from './services/chat-migration.service';
import { MigrationController } from './controllers/migration.controller';
import { <PERSON><PERSON><PERSON>ontroller } from './controllers/chat.controller';
import { ChatService } from './services/chat.service';
import { ConfigModule } from '@nestjs/config';
import { DocumentSchema, DOCUMENT_MODEL } from '../documents/schemas/document.schema';
import { AIModule } from '../ai/ai.module';
import { DocumentsModule } from '../documents/documents.module';
import { PromptTemplateModule } from '../prompt-templates/prompt-template.module';
import { ContextManagementModule } from '../context-management/context-management.module';
import { AuthModule } from '../auth/auth.module';
import { LegalResearchModule } from '../legal-research/legal-research.module';
import { SubscriptionModule } from '../subscription/subscription.module';

import { 
  ChatSession, 
  ChatSessionSchema 
} from './schemas/chat-session.schema';
import { 
  ChatThread, 
  ChatThreadSchema 
} from './schemas/chat-thread.schema';
import { 
  ChatMessage, 
  ChatMessageSchema 
} from './schemas/chat-message.schema';
import { ConfigService } from '@nestjs/config';
import { ChatAnalytics, ChatAnalyticsSchema } from './schemas/chat-analytics.schema';
import { ChatAnalyticsService } from './services/chat-analytics.service';
import { ChatAttachmentSchema } from './schemas/chat-attachment.schema';
import { ChatAttachmentService } from './services/chat-attachment.service';
import { ChatResponseTransformer } from './transformers/chat-response.transformer';
import { ChatAnalyticsController } from './controllers/chat-analytics.controller';

const CHAT_SESSION_MODEL = 'ChatSession';
const CHAT_THREAD_MODEL = 'ChatThread';
const CHAT_MESSAGE_MODEL = 'ChatMessage';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: CHAT_SESSION_MODEL,
        schema: ChatSessionSchema
      },
      {
        name: 'ChatAttachment',
        schema: ChatAttachmentSchema
      },
      {
        name: CHAT_THREAD_MODEL,
        schema: ChatThreadSchema
      },
      {
        name: CHAT_MESSAGE_MODEL,
        schema: ChatMessageSchema
      },
      {
        name: DOCUMENT_MODEL,
        schema: DocumentSchema
      },
      {
        name: ChatAnalytics.name,
        schema: ChatAnalyticsSchema
      }
    ]),
    forwardRef(() => DocumentsModule),
    forwardRef(() => AIModule),
    forwardRef(() => PromptTemplateModule),
    forwardRef(() => ContextManagementModule),
    forwardRef(() => AuthModule),
    forwardRef(() => LegalResearchModule),
    forwardRef(() => SubscriptionModule),
    ConfigModule,
  ],
  controllers: [
    ChatController,
    MigrationController,
    ChatAnalyticsController
  ],
  providers: [
    ChatService,
    ChatMigrationService,
    ChatAnalyticsService,
    ChatAttachmentService,
    ChatResponseTransformer,
    ConfigService
  ],
  exports: [ChatMigrationService, ChatService, ChatAnalyticsService, ChatAttachmentService]
})
export class ChatModule {}

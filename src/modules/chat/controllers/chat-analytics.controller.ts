import { Controller, Get, Post, Body, Param, Put } from '@nestjs/common';
import { ChatAnalyticsService } from '../services/chat-analytics.service';
import { ChatAnalyticsResponse } from '../interfaces/chat-analytics.interface';

interface FeedbackDto {
  isHelpful: boolean;
  rating?: number;
}

interface TopicsUpdateDto {
  topics: string[];
}

@Controller('chat/analytics')
export class ChatAnalyticsController {
  constructor(private readonly analyticsService: ChatAnalyticsService) {}

  @Get('sessions/:sessionId')
  async getSessionAnalytics(
    @Param('sessionId') sessionId: string,
  ): Promise<ChatAnalyticsResponse> {
    return this.analyticsService.getAnalytics(sessionId);
  }

  @Post('sessions/:sessionId/feedback')
  async submitFeedback(
    @Param('sessionId') sessionId: string,
    @Body() feedback: FeedbackDto,
  ): Promise<void> {
    await this.analyticsService.updateUserFeedback(
      sessionId,
      feedback.isHelpful,
      feedback.rating,
    );
  }

  @Put('sessions/:sessionId/topics')
  async updateTopics(
    @Param('sessionId') sessionId: string,
    @Body() data: TopicsUpdateDto,
  ): Promise<void> {
    await this.analyticsService.updateCommonTopics(sessionId, data.topics);
  }
}
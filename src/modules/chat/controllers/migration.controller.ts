import { Controller, Delete, HttpException, HttpStatus, <PERSON><PERSON>, <PERSON>m, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ChatMigrationService } from '../services/chat-migration.service';
import { ChatSession, ChatSessionDocument } from '../schemas/chat-session.schema';

@Controller('chat/migration')
export class MigrationController {
  private readonly logger = new Logger(MigrationController.name);
  
  constructor(
    private chatMigrationService: ChatMigrationService,
    @InjectModel(ChatSession.name) private chatSessionModel: Model<ChatSessionDocument>
  ) {}

  @Delete('sessions/:id')
  async deleteSession(@Param('id') sessionId: string) {
    this.logger.log(`Deleting session: ${sessionId}`);
    try {
      await this.chatMigrationService.deleteSession(sessionId);
      return {
        success: true,
        timestamp: new Date().toISOString(),
        sessionId,
        message: `Session ${sessionId} successfully deleted`
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      this.logger.error(`Error deleting session ${sessionId}:`, error);
      throw new HttpException({
        status: HttpStatus.INTERNAL_SERVER_ERROR,
        error: 'Deletion Failed',
        message: `Failed to delete session: ${error.message}`,
        details: { sessionId, error: error.message }
      }, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}

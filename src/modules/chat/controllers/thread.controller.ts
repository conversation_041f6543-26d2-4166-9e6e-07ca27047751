import { <PERSON>, Post, Get, Body, Param, Put, NotFoundException } from '@nestjs/common';
import { ThreadService } from '../services/thread.service';
import { ChatThread, ThreadSummary } from '../../../common/interfaces/thread.interface';
import { CreateThreadDto, UpdateThreadTitleDto } from '../dto/thread.dto';

@Controller('chat/threads')
export class ThreadController {
  constructor(private threadService: ThreadService) {}

  @Post()
  async createThread(
@Body()
 
    createThreadDto: CreateThreadDto,
  ): Promise<ChatThread> {
    return this.threadService.createThread(
      createThreadDto.sessionId,
      createThreadDto.messageId,
      createThreadDto.title,
      createThreadDto.parentThreadId,
    );
  }

  @Get(':threadId')
  async getThread(@Param('threadId') threadId: string): Promise<ChatThread> {
    const thread = await this.threadService.getThreadById(threadId);
    if (!thread) {
      throw new NotFoundException(`Thread not found: ${threadId}`);
    }
    return thread;
  }

  @Get(':threadId/messages')
  async getThreadMessages(@Param('threadId') threadId: string) {
    return this.threadService.getThreadMessages(threadId);
  }

  @Get(':threadId/summary')
  async getThreadSummary(
    @Param('threadId') threadId: string,
  ): Promise<ThreadSummary> {
    return this.threadService.getThreadSummary(threadId);
  }

  @Put(':threadId/title')
  async updateThreadTitle(
    @Param('threadId') threadId: string,
    @Body() updateTitleDto: UpdateThreadTitleDto,
  ): Promise<ChatThread> {
    return this.threadService.updateThreadTitle(threadId, updateTitleDto.title);
  }

  @Get('session/:sessionId')
  async getSessionThreads(
    @Param('sessionId') sessionId: string,
  ): Promise<ChatThread[]> {
    return this.threadService.getThreadsForSession(sessionId);
  }
}
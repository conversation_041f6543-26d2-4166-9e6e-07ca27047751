import { <PERSON>, Post, <PERSON>s, Body, UseInterceptors } from '@nestjs/common';
import { Response } from 'express';
import { ChatService } from '../services/chat.service';
import { SendMessageDto } from '../dto/send-message.dto';
import { StreamingInterceptor } from '../interceptors/streaming.interceptor';

@Controller('chat/stream')
export class ChatStreamController {
  constructor(private readonly chatService: ChatService) {}

  @Post('messages')
  @UseInterceptors(StreamingInterceptor)
  async streamMessage(
    @Body() sendMessageDto: SendMessageDto,
    @Res() response: Response,
  ) {
    response.setHeader('Content-Type', 'text/event-stream');
    response.setHeader('Cache-Control', 'no-cache');
    response.setHeader('Connection', 'keep-alive');
    response.setHeader('X-Accel-Buffering', 'no');

    try {
      // Use streaming response from chat service
      const messageStream = await this.chatService.streamMessage(sendMessageDto);

      messageStream.subscribe({
        next: (chunk) => {
          response.write(`data: ${JSON.stringify(chunk)}\n\n`);
        },
        error: (error) => {
          console.error('Stream error:', error);
          response.write(`data: ${JSON.stringify({ error: error.message })}\n\n`);
          response.end();
        },
        complete: () => {
          response.end();
        },
      });
    } catch (error) {
      console.error('Error setting up stream:', error);
      response.write(`data: ${JSON.stringify({ error: error.message })}\n\n`);
      response.end();
    }
  }
}
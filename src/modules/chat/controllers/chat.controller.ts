import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  Delete,
  UploadedFile,
  UseInterceptors,
  ParseFilePipe,
  MaxFileSizeValidator,
  FileTypeValidator,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@nestjs/common';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Response, Request } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { ChatService } from '../services/chat.service';
import { SendMessageDto } from '../dto/send-message.dto';
import { ChatRole } from '../../../common/interfaces/chat.interface';
import { AttachmentMimeType } from '../../../common/interfaces/chat.interface';
import { diskStorage } from 'multer';
import { ChatResponseTransformer } from '../transformers/chat-response.transformer';


@Controller('chat')
export class ChatController {
  private readonly logger = new Logger(ChatController.name);

  constructor(
    private readonly chatService: ChatService,
    private readonly responseTransformer: ChatResponseTransformer,
  ) {}

  @Post('sessions')
  async createSession(@Body() createSessionDto: any) {
    const session = await this.chatService.createSession(createSessionDto);
    return {
      id: session.id,
      documentId: session.documentId,
      title: session.title,
      createdAt: session.createdAt,
    };
  }

  @Get('sessions')
  async getAllSessions() {
    const sessions = await this.chatService.getAllSessions();
    return sessions.map(session => ({
      id: session.id,
      documentId: session.documentId,
      title: session.title,
      createdAt: session.createdAt,
      updatedAt: session.updatedAt,
      messageCount: session.messages.length,
    }));
  }

  @Get('sessions/:id')
  async getSession(@Param('id') id: string) {
    const session = await this.chatService.getSessionById(id);
    if (!session) {
      throw new Error(`Chat session with ID ${id} not found`);
    }
    
    return {
      id: session.id,
      documentId: session.documentId,
      title: session.title,
      createdAt: session.createdAt,
      updatedAt: session.updatedAt,
      messages: session.messages.map(msg => ({
        id: msg.id,
        role: msg.role,
        content: msg.role === 'assistant' ? this.responseTransformer.transform(msg) : msg.content,
        attachments: msg.attachments,
        references: msg.references,
        timestamp: msg.timestamp,
      })),
    };
  }

  @Delete('sessions/:id')
  async deleteSession(@Param('id') id: string) {
    this.logger.log(`Deleting chat session: ${id}`);
    try {
      await this.chatService.deleteSession(id);
      return {
        success: true,
        timestamp: new Date().toISOString(),
        message: `Chat session ${id} successfully deleted`
      };
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      
      this.logger.error(`Error deleting chat session ${id}:`, error);
      throw new Error(`Failed to delete chat session: ${error.message}`);
    }
  }

  @Get('sessions/:id/messages')
  async getSessionMessages(@Param('id') id: string) {
    const messages = await this.chatService.getSessionMessages(id);
    return messages.map(msg => ({
      id: msg.id,
      role: msg.role,
      content: msg.role === 'assistant' ? this.responseTransformer.transform(msg) : msg.content,
      attachments: msg.attachments,
      references: msg.references,
      timestamp: msg.timestamp,
    }));
  }

  @Get('sessions/:id/paginated-messages')
  async getPaginatedSessionMessages(
    @Param('id') id: string,
    @Req() req: Request,
  ) {
    // Import necessary classes
    const { MessagePaginationDto, MessageSortField } = require('../dto/message-pagination.dto');
    const { SortDirection } = require('../../../common/dto/pagination.dto');
    
    // Create and validate pagination DTO
    const paginationDto = new MessagePaginationDto();
    
    // Set properties from query params
    if (req.query.page) paginationDto.page = parseInt(req.query.page as string, 10);
    if (req.query.limit) paginationDto.limit = parseInt(req.query.limit as string, 10);
    if (req.query.sortBy) paginationDto.sortBy = req.query.sortBy as string;
    if (req.query.sort) paginationDto.sort = req.query.sort as string;
    
    // Validate the DTO
    paginationDto.validate();
    
    // Get paginated messages
    const result = await this.chatService.getPaginatedSessionMessages(id, paginationDto);
    
    // Transform assistant messages if needed
    const items = result.items.map(msg => {
      if (msg.role === 'assistant') {
        const transformed = this.responseTransformer.transform(msg);
        return {
          ...msg,
          content: transformed.content,
          citationsText: transformed.citations,
          citations: msg.citations || []
        };
      }
      return msg;
    });
    
    return {
      items,
      meta: result.meta,
    };
  }

  @Post('messages')
  @ApiOperation({ summary: 'Send a message in a chat session' })
  @ApiResponse({ status: 201, description: 'Message sent successfully' })
  async sendMessage(
    @Body() dto: SendMessageDto,
    @Req() req: Request,
  ): Promise<any> {
    // Get organization ID from request if available, otherwise use a default
    const organizationId = (req as any).user?.organizationId || 'default-org';
    const userId = (req as any).user?.sub || 'anonymous';

    // Add organization context
    const response = await this.chatService.sendMessage(dto, {
      organizationId,
      userId,
    });

    // Transform response if needed
    if (response.role === ChatRole.ASSISTANT) {
      const transformed = this.responseTransformer.transform(response);
      return {
        ...response,
        content: transformed.content,
        citationsText: transformed.citations
      };
    }

    return response;
  }

  @Post('messages/stream')
  async streamMessage(@Body() sendMessageDto: SendMessageDto, @Res() response: Response) {
    response.setHeader('Content-Type', 'text/event-stream');
    response.setHeader('Cache-Control', 'no-cache');
    response.setHeader('Connection', 'keep-alive');
    
    const messageStream = await this.chatService.streamMessage(sendMessageDto);
    
    messageStream.subscribe({
      next: (chunk) => {
        response.write(`data: ${JSON.stringify(chunk)}\n\n`);
      },
      error: (err) => {
        this.logger.error('Error in stream:', err);
        response.write(`data: ${JSON.stringify({ error: 'An error occurred during streaming' })}\n\n`);
        response.end();
      },
      complete: () => {
        response.end();
      }
    });
  }

  @Post('messages/upload')
  @UseInterceptors(FileInterceptor('file', {
    storage: diskStorage({
      destination: './uploads/chat-attachments',
      filename: (req, file, cb) => {
        const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1E9)}`;
        cb(null, `${uniqueSuffix}-${file.originalname}`);
      }
    })
  }))
  async uploadAttachment(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 5 * 1024 * 1024 }), // 5MB
          new FileTypeValidator({ 
            fileType: Object.values(AttachmentMimeType).join('|') 
          }),
        ],
      }),
    ) file: any
  ) {
    if (!file) {
      throw new Error('No file uploaded');
    }

    // Return the file information needed for attachment
    return {
      success: true,
      attachment: {
        filename: file.originalname,
        path: file.path,
        mimeType: file.mimetype,
        size: file.size,
      }
    };
  }
}

import { Test, TestingModule } from '@nestjs/testing';
import { ThreadController } from '../controllers/thread.controller';
import { ThreadService } from '../services/thread.service';
import { ConfigService } from '@nestjs/config';
import { ChatService } from '../services/chat.service';
import { CreateThreadDto } from '../dto/thread.dto';
import { NotFoundException } from '@nestjs/common';

describe('ThreadController', () => {
  let controller: ThreadController;
  let service: ThreadService;

  const mockThread = {
    id: 'thread-1',
    sessionId: 'session-1',
    title: 'Test Thread',
    createdAt: new Date(),
    updatedAt: new Date(),
    firstMessageId: 'message-1',
  };

  const mockThreadService = {
    createThread: jest.fn().mockImplementation((sessionId, messageId, title, parentThreadId) => {
      return Promise.resolve(mockThread);
    }),
    getThreadById: jest.fn().mockImplementation((threadId) => {
      return threadId === 'thread-1' ? Promise.resolve(mockThread) : Promise.resolve(null);
    }),
    getThreadMessages: jest.fn().mockResolvedValue([]),
    getThreadSummary: jest.fn().mockImplementation(() => ({
      id: mockThread.id,
      title: mockThread.title,
      messageCount: 0,
      lastMessageTimestamp: mockThread.updatedAt,
      previewContent: ''
    })),
    updateThreadTitle: jest.fn().mockImplementation((threadId, title) => {
      return Promise.resolve({ ...mockThread, title });
    }),
    getThreadsForSession: jest.fn().mockResolvedValue([mockThread])
  };

  const mockChatService = {
    getSessionById: jest.fn(),
    updateSession: jest.fn()
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ThreadController],
      providers: [
        {
          provide: ThreadService,
          useValue: mockThreadService
        },
        {
          provide: ChatService,
          useValue: mockChatService
        },
        ConfigService
      ],
    }).compile();

    controller = module.get<ThreadController>(ThreadController);
    service = module.get<ThreadService>(ThreadService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('createThread', () => {
    it('should create a thread successfully', async () => {
      const dto: CreateThreadDto = {
        sessionId: 'session-1',
        messageId: 'message-1',
        title: 'Test Thread'
      };

      const result = await controller.createThread(dto);
      expect(result).toEqual(mockThread);
      expect(service.createThread).toHaveBeenCalledWith(
        dto.sessionId,
        dto.messageId,
        dto.title,
        undefined
      );
    });
  });

  describe('getThread', () => {
    it('should return a thread when it exists', async () => {
      const result = await controller.getThread('thread-1');
      expect(result).toEqual(mockThread);
    });

    it('should throw NotFoundException when thread does not exist', async () => {
      await expect(controller.getThread('nonexistent')).rejects.toThrow(NotFoundException);
    });
  });

  describe('getThreadMessages', () => {
    it('should return thread messages', async () => {
      const result = await controller.getThreadMessages('thread-1');
      expect(result).toEqual([]);
      expect(service.getThreadMessages).toHaveBeenCalledWith('thread-1');
    });
  });

  describe('getThreadSummary', () => {
    it('should return thread summary', async () => {
      const result = await controller.getThreadSummary('thread-1');
      expect(result).toEqual({
        id: mockThread.id,
        title: mockThread.title,
        messageCount: 0,
        lastMessageTimestamp: mockThread.updatedAt,
        previewContent: ''
      });
    });
  });

  describe('getSessionThreads', () => {
    it('should return all threads for a session', async () => {
      const result = await controller.getSessionThreads('session-1');
      expect(result).toEqual([mockThread]);
      expect(service.getThreadsForSession).toHaveBeenCalledWith('session-1');
    });
  });
});
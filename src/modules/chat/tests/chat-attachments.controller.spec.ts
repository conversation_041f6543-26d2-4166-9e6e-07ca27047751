import { Test, TestingModule } from '@nestjs/testing';
import { Chat<PERSON><PERSON>roller } from '../controllers/chat.controller';
import { ChatService } from '../services/chat.service';
import { SendMessageDto } from '../dto/send-message.dto';
import { ChatRole, AttachmentMimeType } from '../../../common/interfaces/chat.interface';

describe('ChatController (Attachments)', () => {
  let controller: ChatController;
  let chatService: ChatService;

  const mockChatService = {
    sendMessage: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ChatController],
      providers: [
        {
          provide: ChatService,
          useValue: mockChatService,
        },
      ],
    }).compile();

    controller = module.get<ChatController>(ChatController);
    chatService = module.get<ChatService>(ChatService);
  });

  describe('uploadAttachment', () => {
    it('should successfully upload a file and return attachment info', async () => {
      const mockFile = {
        originalname: 'test.pdf',
        path: '/path/to/uploaded/file.pdf',
        mimetype: AttachmentMimeType.PDF,
        size: 1024,
      } as Express.Multer.File;

      const result = await controller.uploadAttachment(mockFile);

      expect(result).toEqual({
        success: true,
        attachment: {
          filename: mockFile.originalname,
          path: mockFile.path,
          mimeType: mockFile.mimetype,
          size: mockFile.size,
        },
      });
    });
  });

  describe('sendMessage with attachments', () => {
    it('should send a message with attachments', async () => {
      const sendMessageDto: SendMessageDto = {
        sessionId: 'test-session',
        content: 'Test message with attachment',
        attachments: [
          {
            filename: 'test.pdf',
            path: '/path/to/file.pdf',
            mimeType: AttachmentMimeType.PDF,
            description: 'Test attachment',
            uploadedAt: new Date(),
          },
        ],
      };

      const mockResponse = {
        id: '1',
        role: ChatRole.ASSISTANT,
        content: 'Response with attachment acknowledgment',
        timestamp: new Date(),
        attachments: sendMessageDto.attachments,
      };

      mockChatService.sendMessage.mockResolvedValue(mockResponse);

      const result = await controller.sendMessage(sendMessageDto);

      expect(chatService.sendMessage).toHaveBeenCalledWith(sendMessageDto);
      expect(result).toMatchObject({
        id: mockResponse.id,
        role: mockResponse.role,
        content: mockResponse.content,
        attachments: mockResponse.attachments,
      });
    });

    it('should handle messages with document references', async () => {
      const sendMessageDto: SendMessageDto = {
        sessionId: 'test-session',
        content: 'Test message with reference',
        references: [
          {
            documentId: 'doc-1',
            sectionId: 'section-1',
            text: 'Referenced text',
          },
        ],
      };

      const mockResponse = {
        id: '1',
        role: ChatRole.ASSISTANT,
        content: 'Response acknowledging reference',
        timestamp: new Date(),
        references: sendMessageDto.references,
      };

      mockChatService.sendMessage.mockResolvedValue(mockResponse);

      const result = await controller.sendMessage(sendMessageDto);

      expect(chatService.sendMessage).toHaveBeenCalledWith(sendMessageDto);
      expect(result).toMatchObject({
        id: mockResponse.id,
        role: mockResponse.role,
        content: mockResponse.content,
        references: mockResponse.references,
      });
    });
  });
});
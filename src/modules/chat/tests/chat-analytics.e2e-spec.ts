import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../../../app.module';
import { ChatService } from '../services/chat.service';
import { ChatAnalyticsService } from '../services/chat-analytics.service';
import { CreateSessionDto } from '../dto/create-session.dto';
import { SendMessageDto } from '../dto/send-message.dto';

describe('ChatAnalytics (e2e)', () => {
  let app: INestApplication;
  let chatService: ChatService;
  let analyticsService: ChatAnalyticsService;
  let testSessionId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    chatService = moduleFixture.get<ChatService>(ChatService);
    analyticsService = moduleFixture.get<ChatAnalyticsService>(ChatAnalyticsService);
    await app.init();
  });

  beforeEach(async () => {
    // Create a test document first (assuming we have a test document)
    const testDocumentId = 'test-document-id';

    // Create a test chat session
    const sessionDto: CreateSessionDto = {
      documentId: testDocumentId,
      title: 'Test Analytics Session',
    };
    const session = await chatService.createSession(sessionDto);
    testSessionId = session.id;

    // Add test messages
    await chatService.sendMessage({
      sessionId: testSessionId,
      content: 'Test message 1',
    });

    await chatService.sendMessage({
      sessionId: testSessionId,
      content: 'How can I help you analyze this document?',
    });
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/chat/analytics/sessions/:sessionId (GET)', () => {
    it('should return analytics for a session', async () => {
      const response = await request(app.getHttpServer())
        .get(`/chat/analytics/sessions/${testSessionId}`)
        .expect(200);

      expect(response.body).toHaveProperty('sessionId', testSessionId);
      expect(response.body.analytics).toHaveProperty('messages');
      expect(response.body.analytics.messages).toHaveProperty('total', 2);
      expect(response.body.analytics.messages).toHaveProperty('userMessages', 1);
      expect(response.body.analytics.messages).toHaveProperty('aiMessages', 1);
    });

    it('should return 404 for non-existent session', () => {
      return request(app.getHttpServer())
        .get('/chat/analytics/sessions/non-existent-id')
        .expect(404);
    });
  });

  describe('/chat/analytics/sessions/:sessionId/feedback (POST)', () => {
    it('should update user feedback', async () => {
      await request(app.getHttpServer())
        .post(`/chat/analytics/sessions/${testSessionId}/feedback`)
        .send({
          isHelpful: true,
          rating: 5,
        })
        .expect(201);

      const analytics = await analyticsService.getAnalytics(testSessionId);
      expect(analytics.analytics.feedback.helpful).toBe(1);
      expect(analytics.analytics.feedback.rating).toBe(5);
    });
  });

  describe('/chat/analytics/sessions/:sessionId/topics (PUT)', () => {
    it('should update common topics', async () => {
      const topics = ['legal', 'contract', 'agreement'];
      
      await request(app.getHttpServer())
        .put(`/chat/analytics/sessions/${testSessionId}/topics`)
        .send({ topics })
        .expect(200);

      const analytics = await analyticsService.getAnalytics(testSessionId);
      expect(analytics.analytics.topTopics).toEqual(
        expect.arrayContaining(topics)
      );
    });
  });
});

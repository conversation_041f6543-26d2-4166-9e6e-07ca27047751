import { Test, TestingModule } from '@nestjs/testing';
import { Response } from 'express';
import { Observable, of } from 'rxjs';
import { ChatStreamController } from '../controllers/chat-stream.controller';
import { ChatService } from '../services/chat.service';
import { SendMessageDto } from '../dto/send-message.dto';
import { ChatRole } from '../../../common/interfaces/chat.interface';

describe('ChatStreamController', () => {
  let controller: ChatStreamController;
  let chatService: ChatService;

  const mockChatService = {
    streamMessage: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ChatStreamController],
      providers: [
        {
          provide: ChatService,
          useValue: mockChatService,
        },
      ],
    }).compile();

    controller = module.get<ChatStreamController>(ChatStreamController);
    chatService = module.get<ChatService>(ChatService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('streamMessage', () => {
    it('should handle streaming messages correctly', async () => {
      const sendMessageDto: SendMessageDto = {
        sessionId: 'test-session',
        content: 'Test message',
      };

      const mockResponse = {
        setHeader: jest.fn(),
        write: jest.fn(),
        end: jest.fn(),
      } as unknown as Response;

      const mockMessages = [
        {
          id: '1',
          role: ChatRole.USER,
          content: 'Test message',
          timestamp: new Date(),
        },
        {
          id: '2',
          role: ChatRole.ASSISTANT,
          content: 'Test response',
          timestamp: new Date(),
        },
      ];

      mockChatService.streamMessage.mockResolvedValue(
        of(...mockMessages),
      );

      await controller.streamMessage(sendMessageDto, mockResponse);

      expect(mockResponse.setHeader).toHaveBeenCalledWith(
        'Content-Type',
        'text/event-stream',
      );
      expect(mockResponse.write).toHaveBeenCalledTimes(mockMessages.length);
      expect(mockResponse.end).toHaveBeenCalled();
    });

    it('should handle errors correctly', async () => {
      const sendMessageDto: SendMessageDto = {
        sessionId: 'test-session',
        content: 'Test message',
      };

      const mockResponse = {
        setHeader: jest.fn(),
        write: jest.fn(),
        end: jest.fn(),
      } as unknown as Response;

      const error = new Error('Test error');
      mockChatService.streamMessage.mockRejectedValue(error);

      await controller.streamMessage(sendMessageDto, mockResponse);

      expect(mockResponse.write).toHaveBeenCalledWith(
        `data: ${JSON.stringify({ error: error.message })}\n\n`,
      );
      expect(mockResponse.end).toHaveBeenCalled();
    });
  });
});
import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { ChatModule } from '../chat.module';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { ChatService } from '../services/chat.service';
import { SendMessageDto } from '../dto/send-message.dto';
import { appConfig } from '../../../config/app.config';
import { geminiConfig } from '../../../config/gemini.config';
import { promptTemplatesConfig } from '../../../config/prompt-templates.config';
import { databaseConfig } from '../../../config/database.config';
import contextManagementConfig from '../../../config/context-management.config';

describe('ChatStreamController (e2e)', () => {
  let app: INestApplication;
  let chatService: ChatService;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          load: [
            appConfig,
            geminiConfig,
            promptTemplatesConfig,
            databaseConfig,
            contextManagementConfig,
          ],
        }),
        MongooseModule.forRoot('mongodb://localhost/test-db'),
        ChatModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    chatService = moduleFixture.get<ChatService>(ChatService);
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/chat/stream/messages (POST)', () => {
    it('should stream chat messages', async () => {
      const sendMessageDto: SendMessageDto = {
        sessionId: 'test-session',
        content: 'Test message',
      };

      // Create a test session first
      const session = await chatService.createSession({
        documentId: 'test-document',
        title: 'Test Chat Session',
      });

      sendMessageDto.sessionId = session.id;

      // Make the streaming request
      const response = await request(app.getHttpServer())
        .post('/chat/stream/messages')
        .send(sendMessageDto)
        .expect(200)
        .expect('Content-Type', 'text/event-stream');

      // Verify that we receive the stream data
      expect(response.text).toContain('data:');
      
      // Parse the streamed messages
      const messages = response.text
        .split('\n\n')
        .filter(chunk => chunk.startsWith('data:'))
        .map(chunk => JSON.parse(chunk.replace('data:', '')));

      // Verify we have at least the user message and assistant response
      expect(messages.length).toBeGreaterThanOrEqual(2);
      expect(messages[0].role).toBe('user');
      expect(messages[0].content).toBe(sendMessageDto.content);
      expect(messages[1].role).toBe('assistant');
      expect(messages[1].content).toBeTruthy();
    });

    it('should handle errors gracefully', async () => {
      const sendMessageDto: SendMessageDto = {
        sessionId: 'non-existent-session',
        content: 'Test message',
      };

      const response = await request(app.getHttpServer())
        .post('/chat/stream/messages')
        .send(sendMessageDto)
        .expect(200)
        .expect('Content-Type', 'text/event-stream');

      expect(response.text).toContain('error');
    });
  });
});
import { Injectable, Logger, NotFoundException, Inject, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as path from 'path';
import * as fs from 'fs/promises';
import { ChatThread, ThreadStatus, ThreadSummary } from '../../../common/interfaces/thread.interface';
import { ChatService } from './chat.service';
import { ChatMessage } from '../../../common/interfaces/chat.interface';

@Injectable()
export class ThreadService {
  private readonly logger = new Logger(ThreadService.name);
  private readonly threadsDir: string;

  constructor(
    private configService: ConfigService,
    @Inject(forwardRef(() => ChatService))
    private chatService: ChatService,
  ) {
    this.threadsDir = path.join(
      this.configService.get<string>('storage.uploadDir') || 'uploads',
      'chat-sessions',
      'threads',
    );
    this.ensureThreadsDirExists();
  }

  private async ensureThreadsDirExists() {
    await fs.mkdir(this.threadsDir, { recursive: true });
  }

  async createThread(
    sessionId: string,
    messageId: string,
    title: string,
    parentThreadId?: string,
  ): Promise<ChatThread> {
    // Get the session to make sure it exists
    const session = await this.chatService.getSessionById(sessionId);
    if (!session) {
      throw new NotFoundException(`Session not found: ${sessionId}`);
    }

    // Create the thread
    const thread: ChatThread = {
      id: this.generateId(),
      sessionId,
      title,
      createdAt: new Date(),
      updatedAt: new Date(),
      firstMessageId: messageId,
      parentThreadId,
    };

    // Save the thread
    await this.saveThread(thread);

    // Update session with the new thread ID
    if (!session.threads) {
      session.threads = [];
    }
    session.threads.push(thread.id);

    // Update the message to associate it with the thread
    const messages = session.messages;
    const messageIndex = messages.findIndex(msg => msg.id === messageId);
    if (messageIndex !== -1) {
      messages[messageIndex].threadId = thread.id;
      await this.chatService.updateSession(session);
    }

    return thread;
  }

  async getThreadById(threadId: string): Promise<ChatThread | null> {
    try {
      const filePath = path.join(this.threadsDir, `${threadId}.json`);
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      if (error.code === 'ENOENT') {
        return null;
      }
      throw error;
    }
  }

  async getThreadMessages(threadId: string): Promise<ChatMessage[]> {
    const thread = await this.getThreadById(threadId);
    if (!thread) {
      throw new NotFoundException(`Thread not found: ${threadId}`);
    }

    const session = await this.chatService.getSessionById(thread.sessionId);
    if (!session) {
      throw new NotFoundException(`Session not found: ${thread.sessionId}`);
    }

    return session.messages.filter(msg => msg.threadId === threadId);
  }

  async getThreadSummary(threadId: string): Promise<ThreadSummary> {
    const thread = await this.getThreadById(threadId);
    if (!thread) {
      throw new NotFoundException(`Thread not found: ${threadId}`);
    }

    const messages = await this.getThreadMessages(threadId);
    const lastMessage = messages[messages.length - 1];

    return {
      id: thread.id,
      title: thread.title,
      messageCount: messages.length,
      lastMessageTimestamp: lastMessage?.timestamp || thread.updatedAt,
      previewContent: lastMessage 
        ? lastMessage.content.substring(0, 100) + (lastMessage.content.length > 100 ? '...' : '')
        : 'No messages in thread'
    };
  }

  async updateThreadTitle(threadId: string, newTitle: string): Promise<ChatThread> {
    const thread = await this.getThreadById(threadId);
    if (!thread) {
      throw new NotFoundException(`Thread not found: ${threadId}`);
    }

    thread.title = newTitle;
    thread.updatedAt = new Date();
    await this.saveThread(thread);

    return thread;
  }

  async getThreadsForSession(sessionId: string): Promise<ChatThread[]> {
    const session = await this.chatService.getSessionById(sessionId);
    if (!session) {
      throw new NotFoundException(`Session not found: ${sessionId}`);
    }

    if (!session.threads) {
      return [];
    }

    const threadPromises = session.threads.map(threadId => this.getThreadById(threadId));
    const threads = await Promise.all(threadPromises);
    return threads.filter(thread => thread !== null) as ChatThread[];
  }

  private async saveThread(thread: ChatThread) {
    const filePath = path.join(this.threadsDir, `${thread.id}.json`);
    await fs.writeFile(filePath, JSON.stringify(thread, null, 2));
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2, 15);
  }
}
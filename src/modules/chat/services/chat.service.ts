import {
  Injectable,
  Logger,
  NotFoundException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import * as fs from 'fs/promises';
import { Observable } from 'rxjs';
import * as path from 'path';
import { Model, Document, Types } from 'mongoose';
import {
  ChatMessage,
  ChatRole,
  ChatSession as ChatSessionInterface,
  ContextSource as ChatContextSource,
  MessageAttachment,
  MessageReference,
} from '../../../common/interfaces/chat.interface';
import { AIService } from '../../ai/services/ai.service';
import { DocumentProcessingService } from '../../documents/services/document-processing.service';
import { CreateSessionDto } from '../dto/create-session.dto';
import { SendMessageDto } from '../dto/send-message.dto';
import { PromptTemplateService } from '../../prompt-templates/prompt-template.service';
import { ContextManagerService } from '../../context-management/services/context-manager.service';
import { ContextSource as ContextManagerSource } from '../../../common/interfaces/context-management.interface';
import {
  PaginatedResponseDto,
  SortDirection,
} from '../../../common/dto/pagination.dto';
import {
  MessagePaginationDto,
  MessageSortField,
} from '../dto/message-pagination.dto';
import { RateLimiterInfo } from '../../../common/interfaces/rate-limiter.interface';
import {
  MessageAttachmentDto,
  DocumentReferenceDto,
} from '../dto/send-message.dto';
import { ChatAnalyticsService } from './chat-analytics.service';
import { ChatSessionDocument } from '../schemas/chat-session.schema';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { LegalResearchOrchestratorService } from '../../legal-research/services/legal-research-orchestrator.service';
import { Citation } from '../../../common/interfaces/legal-research.interface';

// Add this interface to handle MongoDB document structure
interface MongoDBChatSession {
  _id: any;
  sessionId: string;
  title: string;
  documents?: string[];
  messages?: any[];
  createdAt?: Date;
  updatedAt?: Date;
  lastActivity?: Date;
  [key: string]: any;
}

@Injectable()
export class ChatService {
  private readonly logger = new Logger(ChatService.name);
  private readonly attachmentsDir: string;

  constructor(
    private configService: ConfigService,
    private aiService: AIService,
    private documentService: DocumentProcessingService,
    private promptTemplateService: PromptTemplateService,
    @Inject(forwardRef(() => ContextManagerService))
    private contextManagerService: ContextManagerService,
    private analyticsService: ChatAnalyticsService,
    private tenantContext: TenantContextService,
    @InjectModel('ChatSession')
    private chatSessionModel: Model<ChatSessionDocument>,
    @Inject(forwardRef(() => LegalResearchOrchestratorService))
    private legalResearchService: LegalResearchOrchestratorService,
  ) {
    this.attachmentsDir = path.join(
      this.configService.get<string>('storage.uploadDir') || 'uploads',
      'chat-attachments',
    );
    // Ensure attachments directory exists
    fs.mkdir(this.attachmentsDir, { recursive: true });
  }

  async createSession(dto: CreateSessionDto): Promise<ChatSessionInterface> {
    const document = await this.documentService.getDocumentById(dto.documentId);
    if (!document) {
      throw new NotFoundException(`Document not found: ${dto.documentId}`);
    }

    const organizationId = this.tenantContext.getCurrentOrganization();
    if (!organizationId) {
      throw new Error('Tenant context required');
    }

    // Generate or use provided session title
    const sessionTitle =
      dto.title || (await this.aiService.generateSessionTitle(document));

    const session: ChatSessionInterface = {
      id: this.generateId(),
      organizationId,
      documentId: dto.documentId,
      title: sessionTitle,
      createdAt: new Date(),
      updatedAt: new Date(),
      messages: [],
    };

    await this.saveSession(session);
    return session;
  }

  async updateSession(
    session: ChatSessionInterface,
  ): Promise<ChatSessionInterface> {
    await this.saveSession(session);
    return session;
  }

  async sendMessage(
    dto: SendMessageDto,
    context?: { organizationId: string; userId: string },
  ): Promise<ChatMessage> {
    const session = await this.getSessionById(dto.sessionId);
    if (!session) {
      throw new NotFoundException(`Session not found: ${dto.sessionId}`);
    }

    // Use provided organization context or session's organization
    const organizationId = context?.organizationId || session.organizationId;

    // Process attachments if present
    const processedAttachments = dto.attachments
      ? await this.processAttachments(dto.attachments)
      : undefined;

    // Validate references if present
    const validatedReferences = dto.references
      ? await this.validateReferences(dto.references)
      : undefined;

    // Add the user message to the session
    const userMessage: ChatMessage = {
      id: this.generateId(),
      organizationId,
      sessionId: session.id,
      threadId: dto.threadId,
      role: ChatRole.USER,
      content: dto.content,
      attachments: processedAttachments?.map((attachment) => ({
        id: this.generateId(),
        fileName: attachment.fileName,
        fileType: attachment.fileType,
        filePath: attachment.filePath,
      })),
      references: validatedReferences?.map((ref) => ({
        documentId: ref.documentId,
        content: ref.content || `Document ${ref.documentId}`,
        relevanceScore: 1.0,
      })),
      timestamp: new Date(),
    };
    session.messages.push(userMessage);

    // Update analytics for user message
    await this.analyticsService.updateMessageMetrics(
      session.id,
      session.messages,
    );

    try {
      // Generate AI response
      const useEnhancedContextSystem = this.configService.get<boolean>(
        'contextManagement.enabled',
        true,
      );

      let response: string;
      if (useEnhancedContextSystem) {
        const prompt = await this.buildContextualPrompt(
          session.messages,
          await this.documentService.getDocumentById(session.documentId),
          dto.content,
          dto.relatedDocumentIds,
        );

        response = await this.aiService.generateChatResponse(session.messages, {
          systemMessage: prompt,
        });
      } else {
        this.logger.log(
          `Using legacy context handling for session ${session.id}`,
        );

        const documentContent = this.prepareContext(
          await this.documentService.getDocumentById(session.documentId),
        );
        const chatHistory = session.messages
          .map((msg) => `${msg.role}: ${msg.content}`)
          .join('\n');

        const prompt = await this.promptTemplateService.generateChatPrompt(
          documentContent,
          chatHistory,
          dto.content,
        );
        response = await this.aiService.generateChatResponse(session.messages, {
          prompt,
        });
      }

      // Extract and enrich citations from the AI response
      const citations = await this.extractAndEnrichCitations(response);

      // Create and save the assistant's response
      const assistantMessage: ChatMessage = {
        id: this.generateId(),
        organizationId: session.organizationId,
        sessionId: session.id,
        role: ChatRole.ASSISTANT,
        content: response,
        citations,
        timestamp: new Date(),
        contextSources: useEnhancedContextSystem
          ? [
              {
                documentId: session.documentId,
                content: 'Primary document context',
                relevanceScore: 1.0,
              },
              {
                documentId: session.documentId,
                content: 'Chat history context',
                relevanceScore: 0.8,
              },
            ]
          : [
              {
                documentId: session.documentId,
                content: 'Document content',
                relevanceScore: 1.0,
              },
            ],
      };

      session.messages.push(assistantMessage);
      session.updatedAt = new Date();
      await this.saveSession(session);

      // Update analytics after AI response
      await this.analyticsService.updateMessageMetrics(
        session.id,
        session.messages,
      );

      // Get rate limiter status for monitoring purposes
      try {
        const rateLimiterInfo = this.aiService.getRateLimiterUtilization();
        if (rateLimiterInfo && rateLimiterInfo.utilizationPercentage > 0.7) {
          this.logger.warn(
            `High API utilization: ${(
              rateLimiterInfo.utilizationPercentage * 100
            ).toFixed(2)}%`,
          );
        }
      } catch (error) {
        this.logger.debug(
          `Could not retrieve rate limiter info: ${error.message}`,
        );
      }

      return assistantMessage;
    } catch (error) {
      this.logger.error('Error generating response:', error);
      throw error;
    }
  }

  async streamMessage(
    dto: SendMessageDto,
  ): Promise<Observable<Partial<ChatMessage>>> {
    const session = await this.getSessionById(dto.sessionId);
    if (!session) {
      throw new NotFoundException(`Session not found: ${dto.sessionId}`);
    }

    const document = await this.documentService.getDocumentById(
      session.documentId,
    );
    if (!document) {
      throw new NotFoundException(`Document not found: ${session.documentId}`);
    }

    // Add the user message to the session
    const userMessage: ChatMessage = {
      id: this.generateId(),
      organizationId: session.organizationId,
      sessionId: session.id,
      threadId: dto.threadId,
      role: ChatRole.USER,
      content: dto.content,
      attachments: dto.attachments
        ? await this.processAttachments(dto.attachments)
        : undefined,
      references: dto.references?.map((ref) => ({
        documentId: ref.documentId,
        content: ref.content || `Document ${ref.documentId}`,
        relevanceScore: 1.0,
      })),
      timestamp: new Date(),
    };
    session.messages.push(userMessage);

    // Update analytics for user message
    await this.analyticsService.updateMessageMetrics(
      session.id,
      session.messages,
    );

    return new Observable<Partial<ChatMessage>>((observer) => {
      const useEnhancedContextSystem = this.configService.get<boolean>(
        'contextManagement.enabled',
        true,
      );
      let responseContent = '';

      // First, emit the user's message
      observer.next({
        id: userMessage.id,
        organizationId: session.organizationId,
        role: userMessage.role,
        content: userMessage.content,
        timestamp: userMessage.timestamp,
      });

      const processResponse = async () => {
        try {
          // Generate AI response
          const prompt = await this.buildContextualPrompt(
            session.messages,
            document,
            dto.content,
            dto.relatedDocumentIds,
          );

          // Use chat-based response generation for streaming
          // We'll still use generateStreamResponse since generateChatResponse doesn't support streaming yet
          const stream = this.aiService.generateStreamResponse(prompt, {
            messages: session.messages,
          });

          // Create the assistant's message structure
          const responseMessage: Partial<ChatMessage> = {
            id: this.generateId(),
            organizationId: session.organizationId,
            sessionId: dto.sessionId,
            role: ChatRole.ASSISTANT,
            timestamp: new Date(),
            content: '',
          };

          // Process the stream
          for await (const chunk of stream) {
            responseContent += chunk;
            observer.next({
              ...responseMessage,
              content: responseContent,
            });
          }

          // Save the final message
          const assistantMessage: ChatMessage = {
            id: responseMessage.id || this.generateId(),
            organizationId: session.organizationId,
            sessionId: session.id,
            role: ChatRole.ASSISTANT,
            content: responseContent,
            timestamp: new Date(),
            contextSources: [
              {
                documentId: session.documentId,
                content: 'Document content',
                relevanceScore: 1.0,
              },
            ],
          };
          session.messages.push(assistantMessage);
          session.updatedAt = new Date();
          await this.saveSession(session);

          // Update analytics
          await this.analyticsService.updateMessageMetrics(
            session.id,
            session.messages,
          );

          observer.complete();
        } catch (error) {
          this.logger.error('Error generating streaming response:', error);
          observer.error(error);
        }
      };

      // Start processing the response
      processResponse();
    });
  }

  private async buildContextualPrompt(
    messages: ChatMessage[],
    document: any,
    query: string,
    relatedDocumentIds?: string[],
  ): Promise<string> {
    const documentContent = this.prepareContext(document);
    const chatHistory = messages
      .map((msg) => `${msg.role}: ${msg.content}`)
      .join('\n');

    return this.promptTemplateService.generateEnhancedChatPrompt(
      {
        content: documentContent,
        chatHistory,
        totalTokens: 0,
        tokenUtilization: 0,
        relevantSections: [],
        documentExcerpts: [],
        metadata: document.metadata || {},
      },
      query,
    );
  }

  private prepareContext(document: any): string {
    if (!document) {
      this.logger.error('No document provided to prepareContext');
      return '';
    }

    let documentContent = '';

    // Ensure we have actual content
    if (document.content) {
      documentContent = `=== DOCUMENT CONTENT START ===\n${document.content}\n=== DOCUMENT CONTENT END ===\n\n`;
    } else {
      this.logger.warn('Document has no content in prepareContext');
      return '';
    }

    const parts = [
      `Document Name: ${document.originalName}\n`,
      documentContent,
    ];

    if (document.metadata?.sections?.length) {
      parts.push('Sections:');
      for (const section of document.metadata.sections) {
        parts.push(
          `- ${section.title}: ${section.purpose || 'No description'}`,
        );
      }
    }

    if (document.metadata?.clauses?.length) {
      parts.push('\nKey Clauses:');
      for (const clause of document.metadata.clauses) {
        parts.push(
          `- ${clause.title} (${clause.type}): ${clause.content} ${
            clause.riskLevel !== 'low' ? `[RISK: ${clause.riskLevel}]` : ''
          }`,
        );
      }
    }

    if (document.metadata?.summary) {
      parts.push(`\nSummary: ${document.metadata.summary}`);
    }

    return parts.join('\n');
  }

  async getSessionById(id: string): Promise<ChatSessionInterface | null> {
    // First check MongoDB
    try {
      const organizationId = this.tenantContext.getCurrentOrganization();
      if (!organizationId) {
        throw new Error('Tenant context required');
      }

      const dbSession = await this.chatSessionModel
        .findOne({ id: id, organizationId })
        .lean()
        .exec();

      if (!dbSession) {
        return null;
      }

      // Convert MongoDB document to ChatSession interface
      const sessionData = dbSession as unknown as MongoDBChatSession;

      const documentId =
        sessionData.documents && sessionData.documents.length > 0
          ? String(sessionData.documents[0])
          : null;

      // Convert message format if needed
      const messages = (sessionData.messages || []).map((msg) => ({
        id: msg.id || this.generateId(),
        sessionId: id,
        organizationId: sessionData.organizationId,
        role: msg.role,
        content: msg.content,
        timestamp: msg.timestamp || new Date(),
        threadId: msg.threadId,
        attachments: msg.attachments || [],
        references: msg.references || [],
        contextSources: msg.contextSources || [],
        citations: msg.citations || [], // Ensure citations are included
      }));

      return {
        id: sessionData.id,
        organizationId: sessionData.organizationId,
        documentId,
        title: sessionData.title,
        createdAt: sessionData.createdAt || new Date(),
        updatedAt:
          sessionData.updatedAt || sessionData.lastActivity || new Date(),
        messages,
      };
    } catch (error) {
      this.logger.error(`Error retrieving session ${id} from MongoDB:`, error);
      throw error;
    }
  }

  async getAllSessions(): Promise<ChatSessionInterface[]> {
    const organizationId = this.tenantContext.getCurrentOrganization();
    if (!organizationId) {
      throw new Error('Tenant context required');
    }

    try {
      const dbSessions = await this.chatSessionModel
        .find({ organizationId })
        .lean()
        .exec();

      // Convert MongoDB documents to ChatSession interface
      const sessions = dbSessions.map((dbSession) => {
        const sessionData = dbSession as unknown as MongoDBChatSession;
        const documentId =
          sessionData.documents && sessionData.documents.length > 0
            ? String(sessionData.documents[0])
            : null;

        return {
          id: sessionData.id,
          organizationId: sessionData.organizationId,
          documentId,
          title: sessionData.title,
          createdAt: sessionData.createdAt || new Date(),
          updatedAt:
            sessionData.updatedAt || sessionData.lastActivity || new Date(),
          messages: (sessionData.messages || []).map((msg) => ({
            id: msg.id || this.generateId(),
            sessionId: sessionData.id,
            organizationId: sessionData.organizationId,
            role: msg.role,
            content: msg.content,
            timestamp: msg.timestamp || new Date(),
            threadId: msg.threadId,
            attachments: msg.attachments || [],
            references: msg.references || [],
            contextSources: msg.contextSources || [],
            citations: msg.citations || [], // Ensure citations are included
          })),
        };
      });

      // Sort by most recent update
      return sessions.sort((a, b) => {
        return (
          new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        );
      });
    } catch (error) {
      this.logger.error('Error retrieving sessions from MongoDB:', error);
      throw error;
    }
  }

  async getSessionMessages(sessionId: string): Promise<ChatMessage[]> {
    const session = await this.getSessionById(sessionId);
    if (!session) {
      throw new NotFoundException(`Chat session not found: ${sessionId}`);
    }

    return session.messages.map((msg) => ({
      ...msg,
      organizationId: session.organizationId, // Ensure all messages have organizationId
    }));
  }

  async getPaginatedSessionMessages(
    sessionId: string,
    paginationDto: MessagePaginationDto,
  ): Promise<PaginatedResponseDto<ChatMessage>> {
    const session = await this.getSessionById(sessionId);
    if (!session) {
      throw new NotFoundException(`Session not found: ${sessionId}`);
    }

    const { page = 1, limit = 20 } = paginationDto;
    const skip = (page - 1) * limit;
    const totalMessages = session.messages.length;

    // Sort messages by timestamp (newest first if descending order requested)
    const sortedMessages = [...session.messages].sort((a, b) => {
      if (paginationDto.sort === SortDirection.DESC) {
        return (
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        );
      }
      return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
    });

    // For each message, ensure citations are properly included
    const paginatedMessages = sortedMessages
      .slice(skip, skip + limit)
      .map((msg) => {
        // Ensure all required fields are present
        const message = {
          ...msg,
          organizationId: session.organizationId, // Ensure all messages have organizationId
          citations: msg.citations || [], // Ensure citations are included
          contextSources: msg.contextSources || [], // Ensure contextSources are included
          references: msg.references || [], // Ensure references are included
          attachments: msg.attachments || [], // Ensure attachments are included
        };
        
        return message;
      });

    const hasNextPage = skip + limit < totalMessages;
    const hasPreviousPage = page > 1;

    return {
      items: paginatedMessages,
      meta: {
        totalItems: totalMessages,
        itemsPerPage: limit,
        currentPage: page,
        totalPages: Math.ceil(totalMessages / limit),
        hasPreviousPage,
        hasNextPage,
      },
    };
  }

  private async saveSession(session: ChatSessionInterface) {
    // Prepare MongoDB document data
    const sessionData = {
      id: session.id,
      organizationId: session.organizationId,
      title: session.title,
      documents: session.documentId ? [session.documentId] : [],
      messages: session.messages.map((msg) => ({
        id: msg.id,
        organizationId: session.organizationId,
        role: msg.role,
        content: msg.content,
        timestamp: msg.timestamp,
        threadId: msg.threadId,
        attachments: msg.attachments,
        references: msg.references,
        contextSources: msg.contextSources,
        citations: msg.citations, // Add citations field to ensure it's saved to the database
      })),
      lastActivity: new Date(),
    };

    try {
      // Check if session exists
      const existingSession = await this.chatSessionModel
        .findOne({ id: session.id })
        .exec();

      if (existingSession) {
        // Update existing session
        await this.chatSessionModel.updateOne(
          { id: session.id },
          {
            $set: {
              title: sessionData.title,
              messages: sessionData.messages,
              lastActivity: sessionData.lastActivity,
            },
          },
        );
        this.logger.debug(`Updated MongoDB session: ${session.id}`);
      } else {
        // Create new session
        await this.chatSessionModel.create({
          ...sessionData,
          createdAt: session.createdAt,
          updatedAt: session.updatedAt || new Date(),
        });
        this.logger.debug(`Created new MongoDB session: ${session.id}`);
      }
      return true;
    } catch (error) {
      this.logger.error(
        `Error saving session to MongoDB: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  private async validateAndSaveAttachment(
    attachment: MessageAttachment,
  ): Promise<string> {
    try {
      const { fileName, filePath: originalPath, fileType } = attachment;

      // Validate file exists
      await fs.access(originalPath);

      // Create unique filename
      const uniqueFilename = `${Date.now()}-${this.generateId()}-${fileName}`;
      const newPath = path.join(this.attachmentsDir, uniqueFilename);

      // Copy file to attachments directory
      await fs.copyFile(originalPath, newPath);

      return newPath;
    } catch (error) {
      this.logger.error('Error processing attachment:', error);
      throw error;
    }
  }

  private async deleteAttachment(attachmentPath: string): Promise<void> {
    try {
      await fs.unlink(attachmentPath);
    } catch (error) {
      this.logger.error(`Error deleting attachment ${attachmentPath}:`, error);
    }
  }

  async cleanupOldAttachments(days: number = 30): Promise<void> {
    try {
      const files = await fs.readdir(this.attachmentsDir);
      const now = Date.now();
      for (const file of files) {
        const filePath = path.join(this.attachmentsDir, file);
        const stats = await fs.stat(filePath);
        if (now - stats.mtime.getTime() > days * 24 * 60 * 60 * 1000) {
          await this.deleteAttachment(filePath);
        }
      }
    } catch (error) {
      this.logger.error('Error cleaning up attachments:', error);
    }
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2, 15);
  }

  private async processAttachments(
    attachments: MessageAttachmentDto[],
  ): Promise<MessageAttachmentDto[]> {
    const processedAttachments: MessageAttachmentDto[] = [];

    for (const attachment of attachments) {
      try {
        // Validate file exists
        await fs.access(attachment.filePath);

        // Validate mime type
        if (!this.isValidMimeType(attachment.fileType)) {
          this.logger.warn(
            `Invalid mime type for attachment: ${attachment.fileName}`,
          );
          continue;
        }

        // Copy file to attachments directory with unique name
        const uniqueFilename = `${Date.now()}-${this.generateId()}-${
          attachment.fileName
        }`;
        const newPath = path.join(this.attachmentsDir, uniqueFilename);
        await fs.copyFile(attachment.filePath, newPath);

        // Add processed attachment
        processedAttachments.push({
          id: attachment.id,
          fileName: attachment.fileName,
          fileType: attachment.fileType,
          filePath: newPath,
        });
      } catch (error) {
        this.logger.error(
          `Error processing attachment ${attachment.fileName}:`,
          error,
        );
      }
    }

    return processedAttachments;
  }

  private async validateReferences(
    references: DocumentReferenceDto[],
  ): Promise<MessageReference[]> {
    const validatedReferences: MessageReference[] = [];

    for (const ref of references) {
      const document = await this.documentService.getDocumentById(
        ref.documentId,
      );
      if (!document) {
        this.logger.warn(`Referenced document not found: ${ref.documentId}`);
        continue;
      }
      validatedReferences.push({
        documentId: ref.documentId,
        content: ref.content || `Document ${ref.documentId}`,
        relevanceScore: 1.0,
      });
    }
    return validatedReferences;
  }

  async deleteSession(sessionId: string): Promise<void> {
    const session = await this.getSessionById(sessionId);
    if (!session) {
      throw new NotFoundException(`Session not found: ${sessionId}`);
    }

    // Delete from MongoDB
    await this.chatSessionModel.deleteOne({ id: sessionId }).exec();

    // Delete any attachments
    for (const message of session.messages) {
      if (message.attachments && message.attachments.length > 0) {
        for (const attachment of message.attachments) {
          try {
            await this.deleteAttachment(attachment.filePath);
          } catch (error) {
            this.logger.warn(
              `Failed to delete attachment: ${attachment.filePath}`,
              error,
            );
          }
        }
      }
    }
  }

  /**
   * Strips Markdown code fences from a JSON string response
   * Removes ```json at the start and ``` at the end if present
   */
  private stripMarkdownCodeFences(response: string): string {
    let cleanResponse = response.trim();

    // Remove ```json prefix if present
    if (cleanResponse.startsWith('```json')) {
      cleanResponse = cleanResponse.substring(7);
    }

    // Remove trailing ``` if present
    if (cleanResponse.endsWith('```')) {
      cleanResponse = cleanResponse.substring(0, cleanResponse.length - 3);
    }

    return cleanResponse.trim();
  }

  private isValidMimeType(mimeType: string): boolean {
    return [
      'application/pdf',
      'application/msword',
      'text/plain',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ].includes(mimeType);
  }

  private async extractAndEnrichCitations(text: string): Promise<Citation[]> {
    try {
      const courtListenerBaseUrl =
        this.configService.get<string>('COURT_LISTENER_BASE_URL') ||
        'https://www.courtlistener.com';
      const govInfoBaseUrl =
        this.configService.get<string>('GOV_INFO_BASE_URL') ||
        'https://www.govinfo.gov';

      const analysisResult = await this.legalResearchService.enrichDocument(
        {
          content: { text, analysis: {} }, // Wrap text and analysis data in content object
          metadata: {
            provider: 'legal-research',
            modelUsed: 'citation-extractor',
          },
        },
        text,
      );

      return (analysisResult.metadata.legalCitations || []).map((citation) => {
        // Get the appropriate base URL based on citation source
        const baseUrl =
          citation.source && String(citation.source).toLowerCase() === 'govinfo'
            ? govInfoBaseUrl
            : courtListenerBaseUrl;

        // Process the URL to ensure it's a full URL
        let fullUrl = citation.links?.sourceUrl || '';
        if (
          fullUrl &&
          !fullUrl.startsWith('http://') &&
          !fullUrl.startsWith('https://')
        ) {
          fullUrl = fullUrl.startsWith('/')
            ? `${baseUrl}${fullUrl}`
            : `${baseUrl}/${fullUrl}`;
        }

        return {
          rawText: citation.rawCitation,
          title: citation.metadata?.title,
          url: fullUrl,
          court: citation.metadata?.court,
          year:
            citation.metadata?.date instanceof Date
              ? citation.metadata.date.getFullYear()
              : undefined,
          confidence: citation.confidence,
          metadata: {
            type: citation.type,
            source: citation.source,
            normalizedCitation: citation.normalizedCitation,
            jurisdiction: citation.metadata?.jurisdiction,
            pdfUrl: this.getFullUrl(citation.links?.pdfUrl, baseUrl),
            apiUrl: this.getFullUrl(citation.links?.apiUrl, baseUrl),
          },
        };
      });
    } catch (error) {
      this.logger.error('Error extracting citations:', error);
      return [];
    }
  }

  private getFullUrl(url?: string, baseUrl?: string): string | undefined {
    if (!url) return undefined;
    if (url.startsWith('http://') || url.startsWith('https://')) return url;

    const base =
      baseUrl ||
      this.configService.get<string>('COURT_LISTENER_BASE_URL') ||
      'https://www.courtlistener.com';
    return url.startsWith('/') ? `${base}${url}` : `${base}/${url}`;
  }
}

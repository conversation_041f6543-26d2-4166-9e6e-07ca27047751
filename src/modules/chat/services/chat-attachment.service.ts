import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MessageAttachment } from '../../../common/interfaces/chat.interface';
import { MessageAttachmentDto } from '../dto/send-message.dto';
import * as path from 'path';
import * as fs from 'fs/promises';

@Injectable()
export class ChatAttachmentService {
  private readonly logger = new Logger(ChatAttachmentService.name);
  private readonly attachmentsDir: string;

  constructor(private readonly configService: ConfigService) {
    // Set attachments directory
    this.attachmentsDir = path.join(
      process.cwd(),
      this.configService.get<string>('ATTACHMENTS_DIR') || 'attachments',
    );
    // Ensure directory exists
    this.ensureAttachmentsDir();
  }

  private async ensureAttachmentsDir() {
    try {
      await fs.mkdir(this.attachmentsDir, { recursive: true });
    } catch (error) {
      this.logger.error('Error creating attachments directory:', error);
    }
  }

  async processAttachments(
    attachments: MessageAttachmentDto[],
  ): Promise<MessageAttachment[]> {
    const processedAttachments: MessageAttachment[] = [];

    for (const attachment of attachments) {
      try {
        // Validate file exists
        await fs.access(attachment.filePath);

        // Create processed attachment
        const processedAttachment: MessageAttachment = {
          id: attachment.id,
          fileName: attachment.fileName,
          fileType: attachment.fileType,
          filePath: attachment.filePath,
        };

        processedAttachments.push(processedAttachment);
      } catch (error) {
        this.logger.error(
          `Error processing attachment ${attachment.fileName}:`,
          error,
        );
      }
    }

    return processedAttachments;
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2, 15);
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ChatSession } from '../schemas/chat-session.schema';
import {
  Document,
  DOCUMENT_MODEL
} from '../../documents/schemas/document.schema';

import { ChatMessage } from '../schemas/chat-message.schema';
import { ChatThread } from '../schemas/chat-thread.schema';

@Injectable()
export class ChatMigrationService {
  private readonly logger = new Logger(ChatMigrationService.name);

  constructor(
    @InjectModel(ChatSession.name)
    private readonly chatSessionModel: Model<ChatSession>,
    @InjectModel(ChatMessage.name)
    private readonly chatMessageModel: Model<ChatMessage>,
    @InjectModel(ChatThread.name)
    private readonly chatThreadModel: Model<ChatThread>,
    @InjectModel(DOCUMENT_MODEL)
    private readonly documentModel: Model<Document>,
  ) {}

  async deleteSession(sessionId: string): Promise<void> {
    // Check if session exists in MongoDB
    const session = await this.chatSessionModel.findOne({ id: sessionId });
    
    if (!session) {
      throw new Error(`Session not found: ${sessionId}`);
    }

    // Delete from MongoDB collections
    await Promise.all([
      this.chatSessionModel.deleteOne({ id: sessionId }),
      this.chatMessageModel.deleteMany({ sessionId }),
      this.chatThreadModel.deleteMany({ sessionId })
    ]);
  }
}

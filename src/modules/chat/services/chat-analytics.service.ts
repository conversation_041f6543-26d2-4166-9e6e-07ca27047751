import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ChatAnalytics, ChatAnalyticsDocument } from '../schemas/chat-analytics.schema';
import { ChatAnalyticsResponse } from '../interfaces/chat-analytics.interface';
import { ChatMessage, ChatRole } from '../../../common/interfaces/chat.interface';

@Injectable()
export class ChatAnalyticsService {
  constructor(
    @InjectModel(ChatAnalytics.name)
    private readonly analyticsModel: Model<ChatAnalyticsDocument>,
  ) {}

  async updateMessageMetrics(sessionId: string, messages: ChatMessage[]): Promise<void> {
    const userMessages = messages.filter(msg => msg.role === ChatRole.USER);
    const aiMessages = messages.filter(msg => msg.role === ChatRole.ASSISTANT);
    
    const responseTimes = this.calculateResponseTimes(messages);
    const messageLengths = messages.map(msg => msg.content.length);

    await this.analyticsModel.findOneAndUpdate(
      { sessionId },
      {
        $set: {
          'messageMetrics.totalCount': messages.length,
          'messageMetrics.userMessageCount': userMessages.length,
          'messageMetrics.aiMessageCount': aiMessages.length,
          'messageMetrics.averageResponseTime': responseTimes.length > 0 
            ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
            : 0,
          'messageMetrics.averageMessageLength': messageLengths.length > 0
            ? messageLengths.reduce((a, b) => a + b, 0) / messageLengths.length
            : 0,
          lastUpdated: new Date(),
        },
      },
      { upsert: true },
    );
  }

  async updateThreadMetrics(
    sessionId: string,
    totalThreads: number,
    activeThreads: number,
    messagesPerThread: number[],
    threadDurations: number[],
  ): Promise<void> {
    await this.analyticsModel.findOneAndUpdate(
      { sessionId },
      {
        $set: {
          'threadMetrics.totalThreads': totalThreads,
          'threadMetrics.activeThreads': activeThreads,
          'threadMetrics.averageMessagesPerThread': messagesPerThread.length > 0
            ? messagesPerThread.reduce((a, b) => a + b, 0) / messagesPerThread.length
            : 0,
          'threadMetrics.averageThreadDuration': threadDurations.length > 0
            ? threadDurations.reduce((a, b) => a + b, 0) / threadDurations.length
            : 0,
          lastUpdated: new Date(),
        },
      },
      { upsert: true },
    );
  }

  async updateUserFeedback(
    sessionId: string, 
    isHelpful: boolean,
    rating?: number,
  ): Promise<void> {
    const update: any = {
      $inc: {
        'userFeedback.helpfulCount': isHelpful ? 1 : 0,
        'userFeedback.notHelpfulCount': isHelpful ? 0 : 1,
      },
      $set: { lastUpdated: new Date() },
    };

    if (rating !== undefined) {
      const analytics = await this.analyticsModel.findOne({ sessionId });
      const currentRating = analytics?.userFeedback?.averageRating || 0;
      const totalRatings = (analytics?.userFeedback?.helpfulCount || 0) + 
                          (analytics?.userFeedback?.notHelpfulCount || 0);
      
      const newAverageRating = totalRatings > 0
        ? (currentRating * totalRatings + rating) / (totalRatings + 1)
        : rating;

      update.$set['userFeedback.averageRating'] = newAverageRating;
    }

    await this.analyticsModel.findOneAndUpdate(
      { sessionId },
      update,
      { upsert: true },
    );
  }

  async updateDocumentAccess(sessionId: string, documentId: string): Promise<void> {
    await this.analyticsModel.findOneAndUpdate(
      { sessionId },
      {
        $inc: { [`documentAccessFrequency.${documentId}`]: 1 },
        $set: { lastUpdated: new Date() },
      },
      { upsert: true },
    );
  }

  async updateCommonTopics(sessionId: string, topics: string[]): Promise<void> {
    await this.analyticsModel.findOneAndUpdate(
      { sessionId },
      {
        $addToSet: { commonTopics: { $each: topics } },
        $set: { lastUpdated: new Date() },
      },
      { upsert: true },
    );
  }

  async getAnalytics(sessionId: string): Promise<ChatAnalyticsResponse> {
    const analytics = await this.analyticsModel.findOne({ sessionId });
    
    if (!analytics) {
      throw new Error(`No analytics found for session ${sessionId}`);
    }

    const documentAccessArray = Object.entries(analytics.documentAccessFrequency || {})
      .map(([documentId, accessCount]) => ({ documentId, accessCount }))
      .sort((a, b) => b.accessCount - a.accessCount);

    return {
      sessionId: analytics.sessionId,
      analytics: {
        messages: {
          total: analytics.messageMetrics.totalCount,
          userMessages: analytics.messageMetrics.userMessageCount,
          aiMessages: analytics.messageMetrics.aiMessageCount,
          averageResponseTime: this.formatDuration(analytics.messageMetrics.averageResponseTime),
          averageMessageLength: Math.round(analytics.messageMetrics.averageMessageLength),
        },
        threads: {
          total: analytics.threadMetrics.totalThreads,
          active: analytics.threadMetrics.activeThreads,
          averageMessages: Math.round(analytics.threadMetrics.averageMessagesPerThread),
          averageDuration: this.formatDuration(analytics.threadMetrics.averageThreadDuration),
        },
        feedback: {
          helpful: analytics.userFeedback.helpfulCount,
          notHelpful: analytics.userFeedback.notHelpfulCount,
          rating: Number(analytics.userFeedback.averageRating.toFixed(1)),
        },
        topTopics: analytics.commonTopics.slice(0, 5),
        mostAccessedDocuments: documentAccessArray.slice(0, 5),
      },
      lastUpdated: analytics.lastUpdated.toISOString(),
    };
  }

  private calculateResponseTimes(messages: ChatMessage[]): number[] {
    const responseTimes: number[] = [];
    
    for (let i = 1; i < messages.length; i++) {
      // Check if the current message is from AI and the previous is from User
      if (messages[i].role === ChatRole.ASSISTANT && messages[i-1].role === ChatRole.USER) {
        const currentTimestamp = messages[i].timestamp instanceof Date ? messages[i].timestamp : new Date(messages[i].timestamp);
        const prevTimestamp = messages[i-1].timestamp instanceof Date ? messages[i-1].timestamp : new Date(messages[i-1].timestamp);
        const responseTime = currentTimestamp.getTime() - prevTimestamp.getTime();
        responseTimes.push(responseTime);
      }
    }
    
    return responseTimes;
  }

  private formatDuration(ms: number): string {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  }
}
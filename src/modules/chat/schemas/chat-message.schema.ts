import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { ChatRole } from '../../../common/interfaces/chat.interface';

@Schema()
class MessageAttachment {
  @Prop({ required: true })
  id: string;

  @Prop({ required: true })
  fileName: string;

  @Prop({ required: true })
  fileType: string;

  @Prop({ required: true })
  filePath: string;
}

@Schema()
class MessageReference {
  @Prop({ required: true })
  documentId: string;

  @Prop({ required: true })
  content: string;

  @Prop({ required: true })
  relevanceScore: number;
}

@Schema()
class Citation {
  @Prop({ required: true })
  rawText: string;

  @Prop({ required: false })
  title?: string;

  @Prop({ required: false })
  url?: string;

  @Prop({ required: false })
  court?: string;

  @Prop({ required: false })
  year?: number;

  @Prop({ required: false })
  confidence?: number;

  @Prop({ type: MongooseSchema.Types.Mixed, required: false })
  metadata?: Record<string, any>;
}

@Schema()
export class ChatMessage extends Document {
  @Prop({ required: true })
  id: string;

  @Prop({ required: true }) 
  organizationId: string;

  @Prop({ required: true })
  sessionId: string;

  @Prop({ required: false })
  threadId?: string;

  @Prop({ required: true, enum: ChatRole })
  role: ChatRole;

  @Prop({ required: true })
  content: string;

  @Prop({ required: true })
  timestamp: Date;

  @Prop({ type: [MessageAttachment], required: false })
  attachments?: MessageAttachment[];

  @Prop({ type: [MessageReference], required: false })
  references?: MessageReference[];

  @Prop({ type: [Citation], required: false })
  citations?: Citation[];

  @Prop({ type: MongooseSchema.Types.Mixed, required: false })
  metadata?: Record<string, any>;
}

export const ChatMessageSchema = SchemaFactory.createForClass(ChatMessage);

// Add indexes for tenant-aware queries
ChatMessageSchema.index({ organizationId: 1, sessionId: 1 }); 
ChatMessageSchema.index({ organizationId: 1, threadId: 1 }); 
ChatMessageSchema.index({ organizationId: 1, timestamp: -1 }); 

// Add text index for content search if needed
// ChatMessageSchema.index({ content: 'text' });

import { <PERSON>p, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document } from "mongoose";
import * as mongoose from "mongoose";
import { Document as DocumentModel } from "../../documents/schemas/document.schema";
import { ChatMessage, ChatMessageSchema } from './chat-message.schema';

@Schema()
export class DocumentReference {
  @Prop({
    type: String,
    required: true,
  })
  documentId: string;

  @Prop({ required: true })
  sectionId: string;

  @Prop()
  text?: string;
}

@Schema({
  timestamps: true,
  collection: 'chat_sessions',
  toJSON: { virtuals: true }
})
export class MessageAttachment {
  @Prop({ required: true })
  filename: string;

  @Prop({ required: true })
  path: string;

  @Prop({ required: true })
  mimeType: string;

  @Prop()
  description?: string;

  @Prop({ default: Date.now })
  uploadedAt: Date;
}

@Schema({
  timestamps: true,
  toJSON: { virtuals: true }
})
export class ChatSession extends Document {
  @Prop({
    required: true,
    unique: true,
    default: () => Math.random().toString(36).substr(2, 9),
    transform: (val: string) => val || Math.random().toString(36).substr(2, 9)
  })
  id: string;

  @Prop({ required: true })
  organizationId: string;

  @Prop({ required: true })
  title: string;

  @Prop({
    type: [String],
    required: false,
    default: [],
    // Keep correct setter for documents
    set: (docs: string | string[]) => {
      if (!docs) return [];
      return Array.isArray(docs) ? docs : [docs];
    }
  })
  documents: string[];

  @Prop({ type: [ChatMessage], default: [] })
  messages: ChatMessage[];

  @Prop({ type: mongoose.Schema.Types.Mixed, default: {} })
  metadata: Record<string, any>;

  @Prop({ default: false })
  isArchived: boolean;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ default: Date.now })
  lastActivity: Date;
}

export type ChatSessionDocument = ChatSession & Document;
export const ChatSessionSchema = SchemaFactory.createForClass(ChatSession);

// Configure schema options
ChatSessionSchema.set('toJSON', {
  virtuals: true,
  versionKey: false,
  transform: (_, ret) => {
    // Ensure id is set but _id is removed
    if (!ret.id && ret._id) {
      ret.id = ret._id.toString();
    }
    delete ret._id;
    delete ret.__v;
    return ret;
  }
});

// Configure indexes
ChatSessionSchema.index({ organizationId: 1 });
ChatSessionSchema.index({ organizationId: 1, id: 1 }, { unique: true });
ChatSessionSchema.index({ organizationId: 1, isActive: 1 });
ChatSessionSchema.index({ organizationId: 1, documents: 1 });
ChatSessionSchema.index({ organizationId: 1, lastActivity: -1 });
ChatSessionSchema.index({ organizationId: 1, isArchived: 1 });

ChatSessionSchema.pre("save", function(next) {
  if (this.isModified("messages")) {
    this.messages.forEach(msg => {
      msg.organizationId = this.organizationId; // Ensure organizationId is set
      if (!msg.id) msg.id = Math.random().toString(36).substr(2, 9); // Use simple random ID 
    });
  }
  next();
});

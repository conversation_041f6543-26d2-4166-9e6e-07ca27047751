import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema()
export class ChatThread extends Document {
  @Prop({ required: true })
  id: string;

  @Prop({ required: true }) 
  organizationId: string;

  @Prop({ required: true })
  sessionId: string;

  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  createdAt: Date;

  @Prop({ required: true })
  updatedAt: Date;

  @Prop({ required: false })
  parentThreadId?: string;

  @Prop({ type: MongooseSchema.Types.Mixed, required: false })
  metadata: Record<string, any>;
}

export const ChatThreadSchema = SchemaFactory.createForClass(ChatThread);

// Add index for tenant-aware queries
ChatThreadSchema.index({ organizationId: 1, sessionId: 1 });
ChatThreadSchema.index({ organizationId: 1, parentThreadId: 1 });

import { Prop, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema()
export class MessageMetrics {
  @Prop({ default: 0 })
  totalCount: number;

  @Prop({ default: 0 })
  userMessageCount: number;

  @Prop({ default: 0 })
  aiMessageCount: number;

  @Prop({ default: 0 })
  averageResponseTime: number; // in milliseconds

  @Prop({ default: 0 })
  averageMessageLength: number;
}

@Schema()
export class ThreadMetrics {
  @Prop({ default: 0 })
  totalThreads: number;

  @Prop({ default: 0 })
  activeThreads: number;

  @Prop({ default: 0 })
  averageMessagesPerThread: number;

  @Prop({ default: 0 })
  averageThreadDuration: number; // in milliseconds
}

@Schema()
export class UserFeedback {
  @Prop({ default: 0 })
  helpfulCount: number;

  @Prop({ default: 0 })
  notHelpfulCount: number;

  @Prop({ default: 0 })
  averageRating: number; // Scale of 1-5
}

@Schema({ timestamps: true })
export class ChatAnalytics {
  @Prop({ required: true, index: false })
  sessionId: string;

  @Prop({ type: MessageMetrics, default: () => ({}) })
  messageMetrics: MessageMetrics;

  @Prop({ type: ThreadMetrics, default: () => ({}) })
  threadMetrics: ThreadMetrics;

  @Prop({ type: UserFeedback, default: () => ({}) })
  userFeedback: UserFeedback;

  @Prop({ type: [String], default: [] })
  commonTopics: string[];

  @Prop({ type: Map, of: Number, default: new Map() })
  documentAccessFrequency: Map<string, number>;

  @Prop({ default: Date.now })
  lastUpdated: Date;

  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>;
}

export type ChatAnalyticsDocument = ChatAnalytics & Document;
export const ChatAnalyticsSchema = SchemaFactory.createForClass(ChatAnalytics);

// Add indexes for better query performance
ChatAnalyticsSchema.index({ sessionId: 1 }, { unique: true });
ChatAnalyticsSchema.index({ lastUpdated: -1 });
ChatAnalyticsSchema.index({ 'messageMetrics.totalCount': -1 });
ChatAnalyticsSchema.index({ 'userFeedback.averageRating': -1 });
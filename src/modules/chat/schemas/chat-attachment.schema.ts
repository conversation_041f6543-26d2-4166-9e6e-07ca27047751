import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class ChatAttachment {
  @Prop({ required: true, index: true })
  id: string;
  
  @Prop({ required: true, index: true })
  sessionId: string;
  
  @Prop({ required: true, index: true })
  messageId: string;
  
  @Prop({ required: true })
  filename: string;
  
  @Prop({ required: true })
  originalName: string;
  
  @Prop({ required: true })
  mimeType: string;
  
  @Prop({ required: true })
  size: number;
  
  @Prop({ required: true })
  path: string;
  
  @Prop({ type: Date, default: Date.now })
  uploadDate: Date;
  
  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>;
}

export type ChatAttachmentDocument = ChatAttachment & Document;
export const ChatAttachmentSchema = SchemaFactory.createForClass(ChatAttachment);

// Add indexes
ChatAttachmentSchema.index({ sessionId: 1, messageId: 1 });
ChatAttachmentSchema.index({ uploadDate: -1 });

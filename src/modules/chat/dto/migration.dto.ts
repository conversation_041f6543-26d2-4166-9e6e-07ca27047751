import { IsNotEmpty, IsOptional, IsBoolean } from 'class-validator';
import { BaseDto } from '../../../common/dto/base.dto';
import { IsSessionIdFormat } from '../../../common/decorators/validation.decorators';

/**
 * DTO for migrating a specific session
 */
export class MigrateSessionDto extends BaseDto {
  @IsSessionIdFormat({
    message: 'Session ID must be a valid format (alphanumeric, dash, underscore)'
  })
  @IsNotEmpty({
    message: 'Session ID is required'
  })
  sessionId: string;

  @IsBoolean()
  @IsOptional()
  force?: boolean;
}

/**
 * DTO for verifying migration of a session
 */
export class VerifyMigrationDto extends BaseDto {
  @IsSessionIdFormat({
    message: 'Session ID must be a valid format (alphanumeric, dash, underscore)'
  })
  @IsNotEmpty({
    message: 'Session ID is required'
  })
  sessionId: string;
}

/**
 * DTO for getting migration status
 */
export class MigrationStatusDto extends BaseDto {
  @IsBoolean()
  @IsOptional()
  detailed?: boolean;
}

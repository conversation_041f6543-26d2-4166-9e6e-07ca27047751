import { IsNotEmpty, IsString, IsOptional, IsArray, ValidateNested, IsDate, IsIn, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { AttachmentMimeType, MessageAttachment, MessageReference } from '../../../common/interfaces/chat.interface';
import * as crypto from 'crypto';

export class DocumentReferenceDto implements MessageReference {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  documentId: string;

  @ApiProperty()
  @IsString()
  content: string;

  @ApiProperty()
  @IsNumber()
  relevanceScore: number;
}

export class MessageAttachmentDto implements MessageAttachment {
  @ApiProperty()
  @IsString()
  id: string = crypto.randomUUID();

  @ApiProperty()
  @IsString()
  fileName: string;

  @ApiProperty()
  @IsString()
  filePath: string;

  @ApiProperty()
  @IsString()
  @IsIn(Object.values(AttachmentMimeType))
  fileType: string;
}

export class SendMessageDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  sessionId: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  content: string;
  
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  threadId?: string;

  @ApiProperty({ type: [MessageAttachmentDto], required: false })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MessageAttachmentDto)
  @IsOptional()
  attachments?: MessageAttachmentDto[];

  @ApiProperty({ type: [DocumentReferenceDto], required: false })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => DocumentReferenceDto)
  @IsOptional()
  references?: DocumentReferenceDto[];

  @ApiProperty({ type: [String], required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  relatedDocumentIds?: string[];
}

import { IsOptional, IsEnum } from 'class-validator';
import { PaginationDto, SortDirection } from '../../../common/dto/pagination.dto';

/**
 * Fields that can be used for sorting chat messages
 */
export enum MessageSortField {
  TIMESTAMP = 'timestamp',
  ROLE = 'role',
}

/**
 * DTO for chat message pagination with additional sorting options
 */
export class MessagePaginationDto extends PaginationDto {
  /**
   * Field to sort by
   * @example "timestamp"
   */
  @IsOptional()
  @IsEnum(MessageSortField, { message: 'Sort field must be a valid message property' })
  sortBy: MessageSortField = MessageSortField.TIMESTAMP;

  /**
   * Default to most recent messages first
   */
  sort: SortDirection = SortDirection.DESC;
}

import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ChatMessage, MessageReference } from '../../../common/interfaces/chat.interface';
import { DocumentReference } from '../../../common/interfaces/legal-research.interface';

@Injectable()
export class ChatResponseTransformer {
  private readonly courtListenerBaseUrl: string;
  
  constructor(private readonly configService: ConfigService) {
    this.courtListenerBaseUrl = this.configService.get<string>('COURT_LISTENER_BASE_URL') || 'https://www.courtlistener.com';
  }
  
  transform(message: ChatMessage): { content: string; citations: string } {
    let content = message.content || '';
    let citations = '';

    // Handle structured content
    if (typeof message.content === 'object') {
      content = '';

      // Add main overview/purpose if present
      const contentObj = message.content as Record<string, any>;
      if (contentObj.Purpose_and_Scope || contentObj.Purpose || contentObj.Overview) {
        content += this.formatSection('Overview', contentObj.Purpose_and_Scope || contentObj.Purpose || contentObj.Overview);
      }

      // Add key provisions if present
      if (contentObj.Key_Provisions || contentObj.KeyProvisions) {
        const provisions = contentObj.Key_Provisions || contentObj.KeyProvisions;
        if (Array.isArray(provisions)) {
          content += '\nKey Points:\n';
          provisions.forEach((provision: any) => {
            content += `• ${provision.title || 'Point'}: ${provision.content}\n`;
            if (provision.notes) {
              content += `  Note: ${provision.notes}\n`;
            }
          });
        }
      }

      // Add any definitions if present
      if (contentObj.Definitions) {
        content += this.formatSection('Definitions', contentObj.Definitions);
      }
    }

    // Add citations if present
    if (message.citations && message.citations.length > 0) {
      citations = 'Citations:\n';
      message.citations.forEach(citation => {
        citations += `- ${citation.rawText}`;
        if (citation.title) citations += ` (${citation.title})`;
        if (citation.url) {
          // Ensure URL has base URL if it's a relative path
          const fullUrl = this.getFullUrl(citation.url);
          citations += `\n  Link: ${fullUrl}`;
        }
        if (citation.court) citations += `\n  Court: ${citation.court}`;
        if (citation.year) citations += `\n  Year: ${citation.year}`;
        citations += '\n';
      });
    }

    // Add references if present
    if (message.references && message.references.length > 0) {
      content += '\n\nReferences:\n';
      message.references.forEach((ref: MessageReference) => {
        content += `- ${ref.content} (Relevance: ${(ref.relevanceScore * 100).toFixed(1)}%)\n`;
      });
    }

    // Add context sources if present
    if (message.contextSources && message.contextSources.length > 0) {
      content += '\n\nContext Sources:\n';
      message.contextSources.forEach(source => {
        content += `- ${source.content} (Relevance: ${(source.relevanceScore * 100).toFixed(1)}%)\n`;
      });
    }

    // Remove any excessive newlines and trim
    content = content
      .replace(/\n{3,}/g, '\n\n')
      .trim();
    citations = citations
      .replace(/\n{3,}/g, '\n\n')
      .trim();

    return { content, citations };
  }

  private formatSection(title: string, content: string | any): string {
    if (typeof content === 'string') {
      return `\n${title}:\n${content}\n`;
    }
    return `\n${title}:\n${JSON.stringify(content, null, 2)}\n`;
  }
  
  private getFullUrl(url: string): string {
    // If the URL already starts with http:// or https://, return it as is
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }
    
    // If it's a relative URL (starts with /), append it to the base URL
    if (url.startsWith('/')) {
      return `${this.courtListenerBaseUrl}${url}`;
    }
    
    // Otherwise, assume it's a relative path and add a slash
    return `${this.courtListenerBaseUrl}/${url}`;
  }
}

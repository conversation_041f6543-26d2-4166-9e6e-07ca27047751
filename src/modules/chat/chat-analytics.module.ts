import { Mo<PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ChatAnalyticsController } from './controllers/chat-analytics.controller';
import { ChatAnalyticsService } from './services/chat-analytics.service';
import { ChatAnalytics, ChatAnalyticsSchema } from './schemas/chat-analytics.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ChatAnalytics.name, schema: ChatAnalyticsSchema },
    ]),
  ],
  controllers: [ChatAnalyticsController],
  providers: [ChatAnalyticsService],
  exports: [ChatAnalyticsService],
})
export class ChatAnalyticsModule {}
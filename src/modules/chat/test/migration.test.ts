import { Test, TestingModule } from '@nestjs/testing';
import { MongooseModule, getModelToken } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ChatMigrationService } from '../services/chat-migration.service';
import { ChatSession, ChatSessionSchema } from '../schemas/chat-session.schema';
import { Document, DocumentSchema } from '../../documents/schemas/document.schema';
import { ChatRole } from '../../../common/interfaces/chat.interface';
import mongoose from 'mongoose';

describe('ChatMigrationService', () => {
  let service: ChatMigrationService;
  let moduleRef: TestingModule;

  beforeAll(async () => {
    moduleRef = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env',
        }),
        MongooseModule.forRootAsync({
          imports: [ConfigModule],
          useFactory: async (configService: ConfigService) => ({
            uri: configService.get<string>('MONGODB_URI') || 'mongodb://localhost:27017/legal-doc-analyzer-test',
          }),
          inject: [ConfigService],
        }),
        MongooseModule.forFeature([
          { name: ChatSession.name, schema: ChatSessionSchema },
        ]),
      ],
      providers: [ChatMigrationService],
    }).compile();

    service = moduleRef.get<ChatMigrationService>(ChatMigrationService);
  });

  afterAll(async () => {
    await moduleRef.close();
  });

  describe('deleteSession', () => {
    let testSessionId: string;

    beforeEach(async () => {
      testSessionId = `test-session-${Date.now()}`;
      const session = new (moduleRef.get(getModelToken(ChatSession.name)))({
        id: testSessionId,
        organizationId: 'test-org',
        title: 'Test Session',
        documents: ['test-doc'],
        messages: [{
          id: 'test-msg-1',
          role: ChatRole.USER,
          content: 'Test message',
          timestamp: new Date()
        }]
      });
      await session.save();
    });

    it('should delete a session', async () => {
      await service.deleteSession(testSessionId);
      const deletedSession = await moduleRef
        .get(getModelToken(ChatSession.name))
        .findOne({ id: testSessionId })
        .exec();
      expect(deletedSession).toBeNull();
    });

    it('should throw NotFoundException for non-existent session', async () => {
      await expect(service.deleteSession('non-existent-id'))
        .rejects
        .toThrow('Session not found');
    });
  });
});

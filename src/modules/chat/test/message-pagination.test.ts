import { Test, TestingModule } from '@nestjs/testing';
import { ChatService } from '../services/chat.service';
import { ConfigService } from '@nestjs/config';
import { DocumentProcessingService } from '../../documents/services/document-processing.service';
import { PromptTemplateService } from '../../prompt-templates/prompt-template.service';
import {
  MessagePaginationDto,
  MessageSortField,
} from '../dto/message-pagination.dto';
import { SortDirection } from '../../../common/dto/pagination.dto';
import { ChatRole } from '../../../common/interfaces/chat.interface';
import { NotFoundException } from '@nestjs/common';
import { AIService } from '../../ai/services/ai.service';

describe('ChatService - Message Pagination', () => {
  let service: ChatService;

  const mockSessionId = 'test-session-id';
  const mockSession = {
    id: mockSessionId,
    documentId: 'test-document-id',
    title: 'Test Session',
    createdAt: new Date('2025-03-01'),
    updatedAt: new Date('2025-03-15'),
    messages: [
      {
        id: 'msg1',
        sessionId: mockSessionId,
        role: ChatRole.USER,
        content: 'First message',
        timestamp: new Date('2025-03-01T10:00:00'),
      },
      {
        id: 'msg2',
        sessionId: mockSessionId,
        role: ChatRole.ASSISTANT,
        content: 'First response',
        timestamp: new Date('2025-03-01T10:01:00'),
      },
      {
        id: 'msg3',
        sessionId: mockSessionId,
        role: ChatRole.USER,
        content: 'Second message',
        timestamp: new Date('2025-03-01T10:02:00'),
      },
      {
        id: 'msg4',
        sessionId: mockSessionId,
        role: ChatRole.ASSISTANT,
        content: 'Second response',
        timestamp: new Date('2025-03-01T10:03:00'),
      },
      {
        id: 'msg5',
        sessionId: mockSessionId,
        role: ChatRole.USER,
        content: 'Third message',
        timestamp: new Date('2025-03-01T10:04:00'),
      },
      {
        id: 'msg6',
        sessionId: mockSessionId,
        role: ChatRole.ASSISTANT,
        content: 'Third response',
        timestamp: new Date('2025-03-01T10:05:00'),
      },
    ],
  };

  const mockConfigService = {
    get: jest.fn().mockImplementation((key) => {
      if (key === 'storage.uploadDir') return 'test-uploads';
      return null;
    }),
  };

  const mockOpenAIService = {
    generateResponse: jest.fn(),
  };

  const mockDocumentService = {
    getDocumentById: jest.fn(),
  };

  const mockPromptTemplateService = {
    generateChatPrompt: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ChatService,
        { provide: ConfigService, useValue: mockConfigService },
        { provide: AIService, useValue: mockOpenAIService },
        { provide: DocumentProcessingService, useValue: mockDocumentService },
        { provide: PromptTemplateService, useValue: mockPromptTemplateService },
      ],
    }).compile();

    service = module.get<ChatService>(ChatService);

    // Mock the getSessionById method to return our test session
    jest.spyOn(service as any, 'getSessionById').mockResolvedValue(mockSession);
  });

  it('should return a paginated list of messages', async () => {
    const paginationDto = new MessagePaginationDto();
    paginationDto.page = 1;
    paginationDto.limit = 2;
    paginationDto.sort = SortDirection.DESC;
    paginationDto.sortBy = MessageSortField.TIMESTAMP;

    const result = await service.getPaginatedSessionMessages(
      mockSessionId,
      paginationDto,
    );

    // Verify the structure of the response
    expect(result).toHaveProperty('items');
    expect(result).toHaveProperty('meta');
    expect(result.meta).toHaveProperty('totalItems', 6);
    expect(result.meta).toHaveProperty('itemsPerPage', 2);
    expect(result.meta).toHaveProperty('currentPage', 1);
    expect(result.meta).toHaveProperty('totalPages', 3);
    expect(result.meta).toHaveProperty('hasPreviousPage', false);
    expect(result.meta).toHaveProperty('hasNextPage', true);

    // Verify we get the most recent 2 messages (reversed order due to DESC)
    expect(result.items).toHaveLength(2);
    expect(result.items[0].id).toBe('msg6');
    expect(result.items[1].id).toBe('msg5');
  });

  it('should paginate to second page correctly', async () => {
    const paginationDto = new MessagePaginationDto();
    paginationDto.page = 2;
    paginationDto.limit = 2;
    paginationDto.sort = SortDirection.DESC;
    paginationDto.sortBy = MessageSortField.TIMESTAMP;

    const result = await service.getPaginatedSessionMessages(
      mockSessionId,
      paginationDto,
    );

    // Verify pagination metadata for second page
    expect(result.meta).toHaveProperty('currentPage', 2);
    expect(result.meta).toHaveProperty('hasPreviousPage', true);
    expect(result.meta).toHaveProperty('hasNextPage', true);

    // Verify we get the middle 2 messages
    expect(result.items).toHaveLength(2);
    expect(result.items[0].id).toBe('msg4');
    expect(result.items[1].id).toBe('msg3');
  });

  it('should sort in ascending order when specified', async () => {
    const paginationDto = new MessagePaginationDto();
    paginationDto.page = 1;
    paginationDto.limit = 2;
    paginationDto.sort = SortDirection.ASC;
    paginationDto.sortBy = MessageSortField.TIMESTAMP;

    const result = await service.getPaginatedSessionMessages(
      mockSessionId,
      paginationDto,
    );

    // Verify we get the oldest 2 messages in ascending order
    expect(result.items).toHaveLength(2);
    expect(result.items[0].id).toBe('msg1');
    expect(result.items[1].id).toBe('msg2');
  });

  it('should sort by role when specified', async () => {
    const paginationDto = new MessagePaginationDto();
    paginationDto.page = 1;
    paginationDto.limit = 3;
    paginationDto.sort = SortDirection.ASC;
    paginationDto.sortBy = MessageSortField.ROLE;

    const result = await service.getPaginatedSessionMessages(
      mockSessionId,
      paginationDto,
    );

    // Verify sorting by role (ASSISTANT comes before USER alphabetically)
    expect(result.items).toHaveLength(3);
    expect(result.items[0].role).toBe(ChatRole.ASSISTANT);
    expect(result.items[1].role).toBe(ChatRole.ASSISTANT);
    expect(result.items[2].role).toBe(ChatRole.ASSISTANT);
  });

  it('should throw NotFoundException for non-existent session', async () => {
    jest.spyOn(service as any, 'getSessionById').mockResolvedValue(null);

    const paginationDto = new MessagePaginationDto();

    await expect(
      service.getPaginatedSessionMessages('non-existent', paginationDto),
    ).rejects.toThrow(NotFoundException);
  });
});

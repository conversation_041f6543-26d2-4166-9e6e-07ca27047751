import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ChatSession } from '../schemas/chat-session.schema';
import { TenantAwareRepository } from '../../common/repositories/base.repository';
import { TenantContextService } from '../../auth/services/tenant-context.service';

@Injectable()
export class ChatSessionRepository extends TenantAwareRepository<ChatSession> {
  constructor(
    @InjectModel(ChatSession.name) chatSessionModel: Model<ChatSession>,
    tenantContext: TenantContextService,
  ) {
    super(chatSessionModel, tenantContext);
  }

  async createSession(
    title: string,
    documentIds: string[] = [],
  ): Promise<ChatSession> {
    return this.create({
      title,
      documents: documentIds,
      isActive: true,
      isArchived: false,
      lastActivity: new Date(),
      messages: [],
      metadata: {},
    });
  }

  async findActiveSessions(): Promise<ChatSession[]> {
    return this.find({ isActive: true, isArchived: false });
  }

  async findArchivedSessions(): Promise<ChatSession[]> {
    return this.find({ isArchived: true });
  }

  async findByDocument(documentId: string): Promise<ChatSession[]> {
    return this.find({ documents: documentId });
  }

  async findRecentSessions(limit = 10): Promise<ChatSession[]> {
    const filter = { isActive: true };
    const sort = { lastActivity: -1 as -1 };
    const { items } = await this.findWithPagination(filter, 1, limit, sort);
    return items;
  }

  async archiveSession(sessionId: string): Promise<ChatSession | null> {
    return this.update(sessionId, {
      isArchived: true,
      isActive: false,
      lastActivity: new Date(),
    });
  }

  async addMessage(
    sessionId: string,
    message: {
      role: string;
      content: string;
      metadata?: Record<string, any>;
    },
  ): Promise<ChatSession | null> {
    return this.update(sessionId, {
      $push: {
        messages: {
          ...message,
          timestamp: new Date(),
        },
      },
      lastActivity: new Date(),
    });
  }

  async updateMetadata(
    sessionId: string,
    metadata: Record<string, any>,
  ): Promise<ChatSession | null> {
    return this.update(sessionId, {
      metadata,
      lastActivity: new Date(),
    });
  }

  async addDocumentToSession(
    sessionId: string,
    documentId: string,
  ): Promise<ChatSession | null> {
    return this.update(sessionId, {
      $addToSet: { documents: documentId },
      lastActivity: new Date(),
    });
  }

  async removeDocumentFromSession(
    sessionId: string,
    documentId: string,
  ): Promise<ChatSession | null> {
    return this.update(sessionId, {
      $pull: { documents: documentId },
      lastActivity: new Date(),
    });
  }
}

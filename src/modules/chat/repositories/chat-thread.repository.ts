import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ChatThread } from '../schemas/chat-thread.schema';
import { TenantAwareRepository } from '../../common/repositories/base.repository';
import { TenantContextService } from '../../auth/services/tenant-context.service';

@Injectable()
export class ChatThreadRepository extends TenantAwareRepository<ChatThread> {
  constructor(
    @InjectModel(ChatThread.name) chatThreadModel: Model<ChatThread>,
    tenantContext: TenantContextService,
  ) {
    super(chatThreadModel, tenantContext);
  }

  async findBySessionId(sessionId: string): Promise<ChatThread[]> {
    return this.find({ sessionId });
  }

  async findActiveThreads(sessionId: string): Promise<ChatThread[]> {
    return this.find({
      sessionId,
      parentThreadId: { $exists: false },
    });
  }

  async findChildThreads(parentThreadId: string): Promise<ChatThread[]> {
    return this.find({ parentThreadId });
  }

  async createThread(
    sessionId: string,
    title: string,
    parentThreadId?: string,
  ): Promise<ChatThread> {
    return this.create({
      sessionId,
      title,
      parentThreadId,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  }

  async updateThreadTitle(threadId: string, title: string): Promise<ChatThread | null> {
    return this.update(threadId, {
      title,
      updatedAt: new Date(),
    });
  }

  async archiveThread(threadId: string): Promise<ChatThread | null> {
    return this.update(threadId, {
      archived: true,
      updatedAt: new Date(),
    });
  }

  async updateThreadMetadata(
    threadId: string,
    metadata: Record<string, any>,
  ): Promise<ChatThread | null> {
    return this.update(threadId, {
      metadata,
      updatedAt: new Date(),
    });
  }
}

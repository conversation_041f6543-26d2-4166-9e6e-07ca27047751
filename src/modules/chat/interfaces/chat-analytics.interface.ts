export interface MessageMetrics {
  totalCount: number;
  userMessageCount: number;
  aiMessageCount: number;
  averageResponseTime: number;
  averageMessageLength: number;
}

export interface ThreadMetrics {
  totalThreads: number;
  activeThreads: number;
  averageMessagesPerThread: number;
  averageThreadDuration: number;
}

export interface UserFeedback {
  helpfulCount: number;
  notHelpfulCount: number;
  averageRating: number;
}

export interface ChatAnalytics {
  sessionId: string;
  messageMetrics: MessageMetrics;
  threadMetrics: ThreadMetrics;
  userFeedback: UserFeedback;
  commonTopics: string[];
  documentAccessFrequency: Map<string, number>;
  lastUpdated: Date;
  metadata: Record<string, any>;
}

export interface ChatAnalyticsResponse {
  sessionId: string;
  analytics: {
    messages: {
      total: number;
      userMessages: number;
      aiMessages: number;
      averageResponseTime: string;
      averageMessageLength: number;
    };
    threads: {
      total: number;
      active: number;
      averageMessages: number;
      averageDuration: string;
    };
    feedback: {
      helpful: number;
      notHelpful: number;
      rating: number;
    };
    topTopics: string[];
    mostAccessedDocuments: Array<{
      documentId: string;
      accessCount: number;
    }>;
  };
  lastUpdated: string;
}
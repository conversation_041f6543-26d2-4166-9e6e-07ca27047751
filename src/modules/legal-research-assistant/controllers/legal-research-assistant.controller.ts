import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { FeatureAvailabilityGuard } from '../../subscription/guards/feature-availability.guard';
import { RequireFeatures } from '../../subscription/decorators/require-features.decorator';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { User } from '../../auth/decorators/user.decorator';
import { LegalResearchAssistantService } from '../services/legal-research-assistant.service';
import { ResearchSessionService } from '../services/research-session.service';
import {
  LegalResearchQueryDto,
  FollowUpQueryDto,
  ResearchAnalyticsQueryDto,
} from '../dto/legal-research-query.dto';
import {
  CreateResearchSessionDto,
  UpdateResearchSessionDto,
  ListResearchSessionsDto,
  ResearchSessionResponseDto,
} from '../dto/research-session.dto';
import {
  LegalResearchResult,
  FollowUpResult,
  ResearchAnalytics,
} from '../interfaces/legal-research-result.interface';
import { ResearchSession, SessionSummary } from '../interfaces/research-session.interface';
import { PostHogService } from '../../posthog/services/posthog.service';

@ApiTags('Legal Research Assistant')
@ApiBearerAuth()
@Controller('legal-research-assistant')
@UseGuards(JwtAuthGuard, FeatureAvailabilityGuard)
export class LegalResearchAssistantController {
  constructor(
    private readonly researchService: LegalResearchAssistantService,
    private readonly sessionService: ResearchSessionService,
    private readonly tenantContext: TenantContextService,
    private readonly postHogService: PostHogService,
  ) {}

  @Post('research/query')
  @HttpCode(HttpStatus.OK)
  @RequireFeatures('legal_research_assistant')
  @ApiOperation({
    summary: 'Perform Legal Research Query',
    description: 'Execute a comprehensive legal research query across multiple sources with optional AI synthesis',
  })
  @ApiResponse({
    status: 200,
    description: 'Research completed successfully',
    type: Object, // Would define proper response DTO
  })
  @ApiResponse({
    status: 402,
    description: 'Insufficient credits',
  })
  @ApiResponse({
    status: 403,
    description: 'Feature not available in subscription tier',
  })
  async performResearch(
    @Body() query: LegalResearchQueryDto,
    @User() user: any,
  ): Promise<{ success: boolean; data: LegalResearchResult }> {
    const organizationId = this.tenantContext.getCurrentOrganization();
    const userId = user.userId;
    const startTime = Date.now();

    try {
      const result = await this.researchService.performResearch(
        query,
        organizationId,
        userId,
      );

      const duration = Date.now() - startTime;

      // Track successful legal research
      this.postHogService.trackEvent(userId, 'legal_research_completed', {
        query: query.query,
        duration_ms: duration,
        organization_id: organizationId,
        timestamp: new Date().toISOString()
      });

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      const duration = Date.now() - startTime;

      // Track failed legal research
      this.postHogService.trackError(userId, error, 'legal_research', {
        query: query.query,
        duration_ms: duration,
        organization_id: organizationId
      });

      throw error;
    }
  }

  @Post('research/follow-up')
  @HttpCode(HttpStatus.OK)
  @RequireFeatures('legal_research_followup')
  @ApiOperation({
    summary: 'Ask Follow-up Question',
    description: 'Continue research within an existing session context with a follow-up question',
  })
  @ApiResponse({
    status: 200,
    description: 'Follow-up completed successfully',
  })
  async askFollowUp(
    @Body() query: FollowUpQueryDto,
    @User() user: any,
  ): Promise<{ success: boolean; data: FollowUpResult }> {
    const organizationId = this.tenantContext.getCurrentOrganization();
    const userId = user.userId;

    const result = await this.researchService.askFollowUp(
      query,
      organizationId,
      userId,
    );

    return {
      success: true,
      data: result,
    };
  }

  @Post('sessions')
  @HttpCode(HttpStatus.CREATED)
  @RequireFeatures('legal_research_assistant')
  @ApiOperation({
    summary: 'Create Research Session',
    description: 'Create a new research session for tracking context and collaboration',
  })
  @ApiResponse({
    status: 201,
    description: 'Session created successfully',
    type: ResearchSessionResponseDto,
  })
  async createSession(
    @Body() dto: CreateResearchSessionDto,
    @User() user: any,
  ): Promise<{ success: boolean; data: ResearchSessionResponseDto }> {
    const organizationId = this.tenantContext.getCurrentOrganization();
    const userId = user.userId;

    const session = await this.sessionService.createSession(
      dto,
      organizationId,
      userId,
    );

    return {
      success: true,
      data: session,
    };
  }

  @Get('sessions/:sessionId')
  @RequireFeatures('legal_research_assistant')
  @ApiOperation({
    summary: 'Get Research Session',
    description: 'Retrieve a research session with its query history',
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Research session ID',
    example: 'session_789012',
  })
  @ApiResponse({
    status: 200,
    description: 'Session retrieved successfully',
  })
  async getSession(
    @Param('sessionId') sessionId: string,
    @User() user: any,
  ): Promise<{ success: boolean; data: ResearchSession }> {
    const organizationId = this.tenantContext.getCurrentOrganization();
    const userId = user.userId;

    const session = await this.sessionService.getSession(
      sessionId,
      organizationId,
      userId,
    );

    return {
      success: true,
      data: session,
    };
  }

  @Put('sessions/:sessionId')
  @RequireFeatures('legal_research_assistant')
  @ApiOperation({
    summary: 'Update Research Session',
    description: 'Update session title, description, tags, or sharing settings',
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Research session ID',
  })
  async updateSession(
    @Param('sessionId') sessionId: string,
    @Body() dto: UpdateResearchSessionDto,
    @User() user: any,
  ): Promise<{ success: boolean; data: ResearchSessionResponseDto }> {
    const organizationId = this.tenantContext.getCurrentOrganization();
    const userId = user.userId;

    const session = await this.sessionService.updateSession(
      sessionId,
      dto,
      organizationId,
      userId,
    );

    return {
      success: true,
      data: session,
    };
  }

  @Delete('sessions/:sessionId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @RequireFeatures('legal_research_assistant')
  @ApiOperation({
    summary: 'Delete Research Session',
    description: 'Delete a research session (only by owner)',
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Research session ID',
  })
  async deleteSession(
    @Param('sessionId') sessionId: string,
    @User() user: any,
  ): Promise<{ success: boolean }> {
    const organizationId = this.tenantContext.getCurrentOrganization();
    const userId = user.userId;

    await this.sessionService.deleteSession(sessionId, organizationId, userId);

    return {
      success: true,
    };
  }

  @Get('sessions')
  @RequireFeatures('legal_research_assistant')
  @ApiOperation({
    summary: 'List Research Sessions',
    description: 'Get all research sessions for the organization with filtering and pagination',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Items per page',
    example: 20,
  })
  @ApiQuery({
    name: 'tags',
    required: false,
    description: 'Filter by tags (comma-separated)',
    example: 'privacy,california',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search in title/description',
    example: 'privacy law',
  })
  async listSessions(
    @Query() query: ListResearchSessionsDto,
    @User() user: any,
  ): Promise<{
    success: boolean;
    data: {
      sessions: SessionSummary[];
      pagination: any;
    };
  }> {
    const organizationId = this.tenantContext.getCurrentOrganization();
    const userId = user.userId;

    const result = await this.sessionService.listSessions(
      query,
      organizationId,
      userId,
    );

    return {
      success: true,
      data: result,
    };
  }

  @Get('analytics')
  @RequireFeatures('legal_research_assistant')
  @ApiOperation({
    summary: 'Get Research Analytics',
    description: 'Retrieve analytics on research usage and patterns (PRO tier and above)',
  })
  @ApiQuery({
    name: 'period',
    required: false,
    enum: ['7d', '30d', '90d', '1y'],
    description: 'Time period for analytics',
    example: '30d',
  })
  @ApiQuery({
    name: 'groupBy',
    required: false,
    enum: ['day', 'week', 'month'],
    description: 'Group results by time period',
    example: 'day',
  })
  async getAnalytics(
    @Query() query: ResearchAnalyticsQueryDto,
    @User() user: any,
  ): Promise<{ success: boolean; data: ResearchAnalytics }> {
    const organizationId = this.tenantContext.getCurrentOrganization();
    const userId = user.userId;

    const analytics = await this.researchService.getResearchAnalytics(
      query,
      organizationId,
      userId,
    );

    return {
      success: true,
      data: analytics,
    };
  }

  @Get('sessions/:sessionId/analytics')
  @RequireFeatures('legal_research_assistant')
  @ApiOperation({
    summary: 'Get Session Analytics',
    description: 'Get detailed analytics for a specific research session',
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Research session ID',
  })
  async getSessionAnalytics(
    @Param('sessionId') sessionId: string,
    @User() user: any,
  ): Promise<{ success: boolean; data: any }> {
    const organizationId = this.tenantContext.getCurrentOrganization();
    const userId = user.userId;

    const analytics = await this.sessionService.getSessionAnalytics(
      sessionId,
      organizationId,
      userId,
    );

    return {
      success: true,
      data: analytics,
    };
  }
}

/**
 * Legal practice areas and jurisdiction constants for the Legal Research Assistant
 */

export const PRACTICE_AREAS = {
  // Core Legal Practice Areas
  CONTRACTS: 'contracts',
  CORPORATE: 'corporate',
  LITIGATION: 'litigation',
  EMPLOYMENT: 'employment',
  INTELLECTUAL_PROPERTY: 'intellectual_property',
  REAL_ESTATE: 'real_estate',
  FAMILY: 'family',
  CRIMINAL: 'criminal',
  IMMIGRATION: 'immigration',
  BANKRUPTCY: 'bankruptcy',
  
  // Technology & Privacy
  PRIVACY: 'privacy',
  DATA_PROTECTION: 'data_protection',
  CYBERSECURITY: 'cybersecurity',
  TECHNOLOGY: 'technology',
  FINTECH: 'fintech',
  
  // Regulatory & Compliance
  SECURITIES: 'securities',
  BANKING: 'banking',
  HEALTHCARE: 'healthcare',
  ENVIRONMENTAL: 'environmental',
  ENERGY: 'energy',
  ANTITRUST: 'antitrust',
  
  // Specialized Areas
  TAX: 'tax',
  INSURANCE: 'insurance',
  CONSTRUCTION: 'construction',
  AVIATION: 'aviation',
  MARITIME: 'maritime',
  INTERNATIONAL: 'international',
} as const;

export const PRACTICE_AREA_KEYWORDS = {
  [PRACTICE_AREAS.CONTRACTS]: [
    'contract', 'agreement', 'breach', 'performance', 'consideration',
    'terms', 'conditions', 'warranty', 'indemnification', 'liability'
  ],
  [PRACTICE_AREAS.PRIVACY]: [
    'privacy', 'data protection', 'GDPR', 'CCPA', 'personal information',
    'consent', 'data breach', 'privacy policy', 'data subject', 'controller'
  ],
  [PRACTICE_AREAS.EMPLOYMENT]: [
    'employment', 'workplace', 'discrimination', 'harassment', 'wage',
    'overtime', 'termination', 'benefits', 'union', 'collective bargaining'
  ],
  [PRACTICE_AREAS.INTELLECTUAL_PROPERTY]: [
    'patent', 'trademark', 'copyright', 'trade secret', 'infringement',
    'licensing', 'royalty', 'fair use', 'DMCA', 'intellectual property'
  ],
  [PRACTICE_AREAS.CORPORATE]: [
    'corporation', 'merger', 'acquisition', 'securities', 'governance',
    'fiduciary', 'shareholder', 'board', 'compliance', 'disclosure'
  ],
  [PRACTICE_AREAS.LITIGATION]: [
    'litigation', 'lawsuit', 'complaint', 'discovery', 'deposition',
    'trial', 'settlement', 'damages', 'injunction', 'appeal'
  ],
  [PRACTICE_AREAS.REAL_ESTATE]: [
    'real estate', 'property', 'lease', 'mortgage', 'title', 'zoning',
    'easement', 'foreclosure', 'landlord', 'tenant'
  ],
  [PRACTICE_AREAS.HEALTHCARE]: [
    'healthcare', 'HIPAA', 'medical', 'patient', 'provider', 'FDA',
    'pharmaceutical', 'medical device', 'clinical trial', 'telemedicine'
  ],
  [PRACTICE_AREAS.CYBERSECURITY]: [
    'cybersecurity', 'data breach', 'hacking', 'malware', 'ransomware',
    'incident response', 'security', 'vulnerability', 'threat', 'cyber'
  ],
  [PRACTICE_AREAS.FINTECH]: [
    'fintech', 'cryptocurrency', 'blockchain', 'digital currency', 'bitcoin',
    'payment', 'financial technology', 'digital wallet', 'DeFi', 'NFT'
  ]
};

export const JURISDICTIONS = {
  // Federal
  FEDERAL: 'federal',
  
  // States (major ones)
  CALIFORNIA: 'california',
  NEW_YORK: 'new_york',
  TEXAS: 'texas',
  FLORIDA: 'florida',
  ILLINOIS: 'illinois',
  PENNSYLVANIA: 'pennsylvania',
  OHIO: 'ohio',
  GEORGIA: 'georgia',
  NORTH_CAROLINA: 'north_carolina',
  MICHIGAN: 'michigan',
  NEW_JERSEY: 'new_jersey',
  VIRGINIA: 'virginia',
  WASHINGTON: 'washington',
  ARIZONA: 'arizona',
  MASSACHUSETTS: 'massachusetts',
  TENNESSEE: 'tennessee',
  INDIANA: 'indiana',
  MARYLAND: 'maryland',
  MISSOURI: 'missouri',
  WISCONSIN: 'wisconsin',
  COLORADO: 'colorado',
  MINNESOTA: 'minnesota',
  
  // International (for future expansion)
  EUROPEAN_UNION: 'european_union',
  UNITED_KINGDOM: 'united_kingdom',
  CANADA: 'canada',
} as const;

export const JURISDICTION_DISPLAY_NAMES = {
  [JURISDICTIONS.FEDERAL]: 'Federal',
  [JURISDICTIONS.CALIFORNIA]: 'California',
  [JURISDICTIONS.NEW_YORK]: 'New York',
  [JURISDICTIONS.TEXAS]: 'Texas',
  [JURISDICTIONS.FLORIDA]: 'Florida',
  [JURISDICTIONS.ILLINOIS]: 'Illinois',
  [JURISDICTIONS.PENNSYLVANIA]: 'Pennsylvania',
  [JURISDICTIONS.OHIO]: 'Ohio',
  [JURISDICTIONS.GEORGIA]: 'Georgia',
  [JURISDICTIONS.NORTH_CAROLINA]: 'North Carolina',
  [JURISDICTIONS.MICHIGAN]: 'Michigan',
  [JURISDICTIONS.NEW_JERSEY]: 'New Jersey',
  [JURISDICTIONS.VIRGINIA]: 'Virginia',
  [JURISDICTIONS.WASHINGTON]: 'Washington',
  [JURISDICTIONS.ARIZONA]: 'Arizona',
  [JURISDICTIONS.MASSACHUSETTS]: 'Massachusetts',
  [JURISDICTIONS.TENNESSEE]: 'Tennessee',
  [JURISDICTIONS.INDIANA]: 'Indiana',
  [JURISDICTIONS.MARYLAND]: 'Maryland',
  [JURISDICTIONS.MISSOURI]: 'Missouri',
  [JURISDICTIONS.WISCONSIN]: 'Wisconsin',
  [JURISDICTIONS.COLORADO]: 'Colorado',
  [JURISDICTIONS.MINNESOTA]: 'Minnesota',
  [JURISDICTIONS.EUROPEAN_UNION]: 'European Union',
  [JURISDICTIONS.UNITED_KINGDOM]: 'United Kingdom',
  [JURISDICTIONS.CANADA]: 'Canada',
};

export const COURT_HIERARCHIES = {
  [JURISDICTIONS.FEDERAL]: [
    'Supreme Court',
    'Court of Appeals',
    'District Court',
    'Bankruptcy Court',
    'Tax Court'
  ],
  [JURISDICTIONS.CALIFORNIA]: [
    'Supreme Court',
    'Court of Appeal',
    'Superior Court'
  ],
  [JURISDICTIONS.NEW_YORK]: [
    'Court of Appeals',
    'Appellate Division',
    'Supreme Court',
    'County Court',
    'City Court'
  ]
};

export const SOURCE_TYPES = {
  CASE_LAW: 'case_law',
  STATUTE: 'statute',
  REGULATION: 'regulation',
  NEWS: 'news',
  ARTICLE: 'article',
  OTHER: 'other'
} as const;

export const SYNTHESIS_STYLES = {
  BRIEF: 'brief',
  COMPREHENSIVE: 'comprehensive',
  ANALYTICAL: 'analytical'
} as const;

export const SEARCH_TIMEFRAMES = {
  LAST_WEEK: '7d',
  LAST_MONTH: '30d',
  LAST_QUARTER: '90d',
  LAST_YEAR: '1y',
  CUSTOM: 'custom'
} as const;

// Helper functions
export function getPracticeAreaKeywords(area: string): string[] {
  return PRACTICE_AREA_KEYWORDS[area] || [];
}

export function getJurisdictionDisplayName(jurisdiction: string): string {
  return JURISDICTION_DISPLAY_NAMES[jurisdiction] || jurisdiction;
}

export function getAllPracticeAreas(): string[] {
  return Object.values(PRACTICE_AREAS);
}

export function getAllJurisdictions(): string[] {
  return Object.values(JURISDICTIONS);
}

export function classifyPracticeArea(text: string): string[] {
  const lowerText = text.toLowerCase();
  const matches: string[] = [];
  
  Object.entries(PRACTICE_AREA_KEYWORDS).forEach(([area, keywords]) => {
    const matchCount = keywords.filter(keyword => 
      lowerText.includes(keyword.toLowerCase())
    ).length;
    
    if (matchCount > 0) {
      matches.push(area);
    }
  });
  
  return matches;
}

export function extractJurisdictions(text: string): string[] {
  const lowerText = text.toLowerCase();
  const matches: string[] = [];
  
  Object.entries(JURISDICTION_DISPLAY_NAMES).forEach(([code, name]) => {
    if (lowerText.includes(name.toLowerCase()) || 
        lowerText.includes(code.toLowerCase())) {
      matches.push(code);
    }
  });
  
  return matches;
}

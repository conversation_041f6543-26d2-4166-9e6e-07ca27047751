/**
 * Interfaces for search source integration and management
 */

export interface SearchSourceProvider {
  name: string;
  type: 'legal_database' | 'web_search' | 'news_api';
  search(query: SearchQuery): Promise<SearchSourceResult[]>;
  isAvailable(): Promise<boolean>;
  getRateLimit(): RateLimitInfo;
}

export interface SearchQuery {
  query: string;
  options: {
    maxResults?: number;
    jurisdictions?: string[];
    practiceAreas?: string[];
    timeRange?: {
      from: string;
      to: string;
    };
    sourceTypes?: string[];
    timeout?: number;
  };
  context?: {
    sessionId?: string;
    previousQueries?: string[];
  };
}

export interface SearchSourceResult {
  id: string;
  source: string; // Provider name
  type: 'case_law' | 'statute' | 'regulation' | 'news' | 'article' | 'other';
  title: string;
  citation?: string;
  court?: string;
  authority?: string;
  date: string;
  jurisdiction: string;
  practiceArea?: string;
  url: string;
  snippet: string;
  fullText?: string;
  relevanceScore: number;
  authorityScore: number;
  metadata?: {
    [key: string]: any;
  };
}

export interface RateLimitInfo {
  remaining: number;
  limit: number;
  resetTime: Date;
  windowMs: number;
}

export interface SearchAggregationResult {
  sources: SearchSourceResult[];
  aggregationMetadata: {
    totalSources: number;
    sourceBreakdown: {
      [providerName: string]: number;
    };
    duplicatesRemoved: number;
    averageRelevanceScore: number;
    searchDuration: number;
  };
}

export interface WebSearchConfig {
  apiKey: string;
  searchEngineId: string;
  baseUrl: string;
  maxResults: number;
  timeout: number;
  legalSiteFilters: string[];
  excludePatterns: string[];
}

export interface LegalDatabaseConfig {
  courtListener: {
    apiKey: string;
    baseUrl: string;
    maxResults: number;
    timeout: number;
  };
  govInfo: {
    apiKey?: string;
    baseUrl: string;
    maxResults: number;
    timeout: number;
  };
}

export interface SearchSourceMetrics {
  providerName: string;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  averageResultCount: number;
  rateLimitHits: number;
  lastUsed: Date;
  reliability: number; // 0-1
}

export interface CitationParseResult {
  originalText: string;
  parsedCitation: {
    volume?: string;
    reporter?: string;
    page?: string;
    year?: string;
    court?: string;
    jurisdiction?: string;
  };
  confidence: number; // 0-1
  type: 'case' | 'statute' | 'regulation' | 'unknown';
  normalizedCitation: string;
}

export interface JurisdictionFilter {
  code: string;
  name: string;
  type: 'federal' | 'state' | 'local' | 'international';
  courts?: string[];
  priority: number;
}

export interface PracticeAreaClassifier {
  area: string;
  keywords: string[];
  patterns: string[];
  confidence: number;
  subAreas?: string[];
}

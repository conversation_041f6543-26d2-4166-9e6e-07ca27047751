import { Injectable, Logger, BadRequestException, ForbiddenException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { 
  LegalResearchResult, 
  FollowUpResult, 
  ResearchAnalytics,
  SearchResults,
  SynthesisResult,
} from '../interfaces/legal-research-result.interface';
import { LegalResearchQueryDto, FollowUpQueryDto, ResearchAnalyticsQueryDto } from '../dto/legal-research-query.dto';
import { LegalSearchOrchestratorService } from './legal-search-orchestrator.service';
import { LegalSynthesisService } from './legal-synthesis.service';
import { ResearchSessionService } from './research-session.service';
import { CreditManagementService } from '../../subscription/services/credit-management.service';
import { SubscriptionService } from '../../subscription/services/subscription.service';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { TIER_LIMITATIONS } from '../constants/search-sources.constants';
import { v4 as uuidv4 } from 'uuid';
import { AIService } from '../../ai/services/ai.service';

@Injectable()
export class LegalResearchAssistantService {
  private readonly logger = new Logger(LegalResearchAssistantService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly searchOrchestrator: LegalSearchOrchestratorService,
    private readonly synthesisService: LegalSynthesisService,
    private readonly sessionService: ResearchSessionService,
    private readonly creditService: CreditManagementService,
    private readonly subscriptionService: SubscriptionService,
    private readonly tenantContext: TenantContextService,
    private readonly aiService: AIService,
  ) {}

  async performResearch(
    query: LegalResearchQueryDto,
    organizationId: string,
    userId: string,
  ): Promise<LegalResearchResult> {
    const startTime = Date.now();
    
    this.logger.debug(`Starting legal research for query: "${query.query}"`);

    try {
      // 1. Get or create a research session
      const session = await this.sessionService.startOrGetSession(query, organizationId, userId);
      const sessionId = session.sessionId;

      // 2. Validate subscription and get tier
      const subscription = await this.subscriptionService.getSubscription(organizationId);
      const tier = subscription?.tier || 'LAW_STUDENT';
      
      // 2. Validate feature access and rate limits
      await this.validateFeatureAccess(organizationId, tier, query.options);
      
      // 3. Calculate and deduct credits
      const creditCost = this.calculateCreditCost(query.options);
      const creditResult = await this.creditService.deductCreditsForFeature(
        organizationId,
        'legal_research_assistant'
      );

      if (!creditResult.success) {
        throw new ForbiddenException('Insufficient credits for legal research');
      }

      // 4. Perform multi-source search
      this.logger.debug('Executing search across legal databases and web sources');
      const searchResults = await this.searchOrchestrator.search(query, tier);
      
      // 5. Generate AI synthesis if requested and available for tier
      let synthesis = null;
      let synthesisCreditCost = 0;

      if (query.options?.includeSynthesis && this.isSynthesisAvailable(tier)) {
        this.logger.debug('Generating AI synthesis of search results');

        // Deduct additional credits for synthesis
        const synthesisResult = await this.creditService.deductCreditsForFeature(
          organizationId,
          'legal_research_synthesis'
        );

        if (synthesisResult.success) {
          synthesisCreditCost = 3; // Cost for synthesis
          synthesis = await this.synthesisService.synthesizeResults(
            query.query,
            searchResults,
            this.convertToResearchOptions(query.options)
          );
        } else {
          this.logger.warn('Insufficient credits for AI synthesis, proceeding without synthesis');
        }
      }

      // 6. Generate follow-up suggestions
      const followUpSuggestions = await this.generateFollowUpSuggestions(
        query.query,
        searchResults,
        synthesis,
        organizationId,
        userId,
        sessionId,
      );

      // 7. Save query to session
      await this.sessionService.addQueryToSession(
        sessionId,
        query,
        searchResults,
        synthesis,
        creditCost + synthesisCreditCost,
        organizationId,
        userId,
        followUpSuggestions
      );

      const totalDuration = Date.now() - startTime;
      const queryId = `research_${uuidv4()}`;

      const result: LegalResearchResult = {
        queryId,
        sessionId: session.sessionId,
        query: query.query,
        searchResults,
        aiSynthesis: synthesis,
        followUpSuggestions,
        metadata: {
          searchDuration: searchResults.metadata.duration,
          synthesisDuration: synthesis?.metadata?.duration || 0,
          totalDuration,
          creditsUsed: creditCost + synthesisCreditCost,
          timestamp: new Date(),
        },
      };

      this.logger.debug(`Legal research completed in ${totalDuration}ms, used ${creditCost + synthesisCreditCost} credits`);

      return result;

    } catch (error) {
      this.logger.error(`Legal research failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  async askFollowUp(
    query: FollowUpQueryDto,
    organizationId: string,
    userId: string,
  ): Promise<FollowUpResult> {
    this.logger.debug(`Processing follow-up question: "${query.question}"`);

    try {
      // 1. Validate session access
      const session = await this.sessionService.getSession(
        query.sessionId,
        organizationId,
        userId
      );

      // 2. Get subscription tier
      const subscription = await this.subscriptionService.getSubscription(organizationId);
      const tier = subscription?.tier || 'LAW_STUDENT';

      // 3. Deduct credits for follow-up
      const creditResult = await this.creditService.deductCreditsForFeature(
        organizationId,
        'legal_research_followup'
      );

      if (!creditResult.success) {
        throw new ForbiddenException('Insufficient credits for follow-up question');
      }

      // 4. Build enhanced query with session context
      const enhancedQuery: LegalResearchQueryDto = {
        query: query.question,
        options: {
          ...query.options,
          focusOnPrevious: query.focusOnPrevious
        },
        sessionId: query.sessionId
      } as LegalResearchQueryDto;

      // 5. Perform search with context
      const searchResults = await this.searchOrchestrator.search(enhancedQuery, tier);

      // 6. Generate contextual analysis
      let synthesis = null;
      let synthesisCreditCost = 0;

      if (query.options?.includeSynthesis && this.isSynthesisAvailable(tier)) {
        const synthesisResult = await this.creditService.deductCreditsForFeature(
          organizationId,
          'legal_research_synthesis'
        );

        if (synthesisResult.success) {
          synthesisCreditCost = 3;
          
          // Include session context for better synthesis
          const sessionContext = this.buildSessionContext(session);
          
          synthesis = await this.synthesisService.synthesizeResults(
            query.question,
            searchResults,
            this.convertToResearchOptions(query.options),
            sessionContext
          );
        }
      }

      // 7. Generate connection to previous research
      const connectionToPrevious = this.generateConnectionToPrevious(
        query.question,
        session,
        synthesis
      );

      // 8. Generate new follow-up suggestions
      const followUpSuggestions = await this.generateFollowUpSuggestions(
        query.question,
        searchResults,
        synthesis,
        organizationId,
        userId,
        query.sessionId,
      );

      // 9. Save to session
      await this.sessionService.addQueryToSession(
        query.sessionId,
        query,
        searchResults,
        synthesis,
        1 + synthesisCreditCost, // 1 for follow-up + synthesis cost
        organizationId,
        userId,
        followUpSuggestions
      );

      const queryId = `followup_${uuidv4()}`;

      const result: FollowUpResult = {
        queryId,
        sessionId: query.sessionId,
        question: query.question,
        contextualAnalysis: connectionToPrevious,
        searchResults,
        aiSynthesis: synthesis,
        followUpSuggestions,
        connectionToPrevious,
        metadata: {
          creditsUsed: 1 + synthesisCreditCost,
          timestamp: new Date(),
        },
      };

      this.logger.debug(`Follow-up completed, used ${1 + synthesisCreditCost} credits`);

      return result;

    } catch (error) {
      this.logger.error(`Follow-up question failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getResearchAnalytics(
    query: ResearchAnalyticsQueryDto,
    organizationId: string,
    userId: string,
  ): Promise<ResearchAnalytics> {
    // This would implement analytics aggregation
    // For now, return a basic structure
    return {
      period: query.period || '30d',
      summary: {
        totalQueries: 0,
        totalSessions: 0,
        totalCreditsUsed: 0,
        averageQueriesPerSession: 0,
        averageCreditsPerQuery: 0,
      },
      trends: {
        queryVolume: [],
      },
      topPracticeAreas: [],
      topJurisdictions: [],
    };
  }

  private async validateFeatureAccess(
    organizationId: string,
    tier: string,
    options?: any
  ): Promise<void> {
    // Temporarily bypass feature check for testing - Serper API integration
    this.logger.debug('Feature access validation bypassed for Serper API testing');
    return;

    // Check if legal research assistant is available for this tier
    const hasFeature = await this.subscriptionService.getFeatureAvailability(
      organizationId,
      'legal_research_assistant'
    );

    if (!hasFeature) {
      throw new ForbiddenException(
        'Legal Research Assistant is not available in your subscription tier'
      );
    }

    // Check synthesis availability
    if (options?.includeSynthesis && !this.isSynthesisAvailable(tier)) {
      throw new ForbiddenException(
        'AI synthesis is not available in your subscription tier'
      );
    }
  }

  private calculateCreditCost(options?: any): number {
    // Base cost for search
    let cost = 1;

    // Additional cost for synthesis is handled separately
    // as it requires a separate feature check

    return cost;
  }

  private isSynthesisAvailable(tier: string | any): boolean {
    const tierString = typeof tier === 'string' ? tier : String(tier);
    const tierLimits = TIER_LIMITATIONS[tierString];
    return tierLimits?.synthesisEnabled || false;
  }

  private async generateFollowUpSuggestions(
    query: string,
    searchResults: SearchResults,
    synthesis: SynthesisResult | null,
    organizationId: string,
    userId: string,
    sessionId?: string,
  ): Promise<string[]> {
    // If a session context is available, try to generate smarter, AI-driven suggestions
    if (sessionId) {
      try {
        const session = await this.sessionService.getSession(sessionId, organizationId, userId);
        const context = this.buildSessionContext(session);

        const prompt = this.buildFollowUpSuggestionPrompt(query, synthesis, context);

        const aiResponse = await this.aiService.generateResponse(prompt, {
          temperature: 0.5,
          maxTokens: 150,
          systemMessage: 'You are an expert legal research assistant. Your task is to suggest relevant, insightful follow-up questions to help a user deepen their research. The questions should be concise and directly related to the provided context.',
        });

        // Attempt to parse the response as JSON, cleaning it up if necessary
        let cleanedResponse = aiResponse.trim();
        if (cleanedResponse.startsWith('```json')) {
          cleanedResponse = cleanedResponse.substring(7, cleanedResponse.length - 3).trim();
        } else if (cleanedResponse.startsWith('```')) {
          cleanedResponse = cleanedResponse.substring(3, cleanedResponse.length - 3).trim();
        }

        const suggestions = JSON.parse(cleanedResponse);
        if (Array.isArray(suggestions) && suggestions.length > 0 && suggestions.every(s => typeof s === 'string')) {
          this.logger.debug(`Generated ${suggestions.length} AI-powered follow-up suggestions for session ${sessionId}`);
          return suggestions.slice(0, 4); // Return up to 4 AI suggestions
        }
      } catch (error) {
        this.logger.warn(`Failed to generate AI-powered follow-up suggestions: ${error.message}. Falling back to rule-based suggestions.`);
      }
    }

    // Fallback to basic rule-based suggestions if no session or if AI generation fails
    const fallbackSuggestions: string[] = [];

    if (query.toLowerCase().includes('recent')) {
      fallbackSuggestions.push('What are the historical developments in this area?');
    }
    if (query.toLowerCase().includes('california')) {
      fallbackSuggestions.push('How do other states handle this issue?');
      fallbackSuggestions.push('What is the federal approach to this matter?');
    }
    if (searchResults.sources.some(s => s.type === 'case_law')) {
      fallbackSuggestions.push('What are the key statutory requirements?');
    }
    if (searchResults.sources.some(s => s.type === 'statute')) {
      fallbackSuggestions.push('How have courts interpreted these statutes?');
    }
    if (synthesis?.keyFindings?.length) {
      fallbackSuggestions.push('What are the practical implications for compliance?');
      fallbackSuggestions.push('Are there any pending legislative changes?');
    }

    // Return a unique set of the top 3 suggestions
    return [...new Set(fallbackSuggestions)].slice(0, 3);
  }

  private buildFollowUpSuggestionPrompt(
    currentQuery: string,
    synthesis: SynthesisResult | null,
    context: { previousQueries: string[]; previousFindings: string[] }
  ): string {
    const initialQuery = context.previousQueries.length > 0 ? context.previousQueries[0] : currentQuery;

    let prompt = `Based on the following legal research session, generate 3-4 insightful and contextually relevant follow-up questions. The questions should help the user explore different facets of the initial query, probe deeper into the latest findings, or explore related legal areas.

**Initial Research Goal:** "${initialQuery}"

**Most Recent Query:** "${currentQuery}"
`;

    if (context.previousQueries.length > 1) {
      prompt += `
**Previous Questions in this Session:**
- ${context.previousQueries.slice(0, -1).join('\n- ')}
`;
    }

    if (synthesis?.keyFindings?.length) {
      const findingsText = synthesis.keyFindings.map(f => f.finding).join('\n- ');
      prompt += `
**Key Findings from Latest Query:**
- ${findingsText}
`;
    }
    
    if (synthesis?.legalAnalysis?.text) {
        prompt += `
**Summary of Latest Analysis:**
${synthesis.legalAnalysis.text.substring(0, 400)}...
`
    }

    prompt += `
**Task:**
Generate an array of 3-4 follow-up questions as a JSON string array. The response must be only the JSON array. For example: ["Question 1?", "Question 2?", "Question 3?"]`;

    return prompt;
  }

  private buildSessionContext(session: any): { previousQueries: string[]; previousFindings: string[] } {
    const previousQueries = session.queries
      .map((q: any) => q.query || q.question)
      .filter((q: string) => q)
      .slice(-3); // Last 3 queries

    const previousFindings: string[] = []; // Would extract from previous synthesis results

    return { previousQueries, previousFindings };
  }

  private generateConnectionToPrevious(
    question: string,
    session: any,
    synthesis: any
  ): string {
    if (session.queries.length === 0) {
      return 'This is the first question in your research session.';
    }

    const lastQuery = session.queries[session.queries.length - 1];
    const lastQuestion = lastQuery.query || lastQuery.question;

    return `This question builds on your previous research about "${lastQuestion}". ${
      synthesis
        ? 'The analysis below incorporates context from your previous findings.'
        : 'Consider reviewing previous results for additional context.'
    }`;
  }

  private convertToResearchOptions(dto: any): any {
    return {
      includeSynthesis: dto?.includeSynthesis ?? true,
      maxSources: dto?.maxSources ?? 10,
      jurisdictions: dto?.jurisdictions,
      practiceAreas: dto?.practiceAreas,
      timeRange: dto?.timeRange,
      sourceTypes: dto?.sourceTypes ?? ['case_law', 'statutes', 'regulations', 'news'],
      synthesisStyle: dto?.synthesisStyle ?? 'comprehensive',
      focusOnPrevious: dto?.focusOnPrevious ?? false
    };
  }
}

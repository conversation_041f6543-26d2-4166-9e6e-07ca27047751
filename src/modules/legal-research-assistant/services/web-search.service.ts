import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import {
  SearchSourceProvider,
  SearchQuery,
  SearchSourceResult,
  RateLimitInfo
} from '../interfaces/search-source.interface';
import {
  LEGAL_SITE_FILTERS,
  EXCLUDE_PATTERNS,
  SEARCH_OPERATORS,
  calculateAuthorityScore,
  buildSearchQuery
} from '../constants/search-sources.constants';
import { classifyPracticeArea, extractJurisdictions } from '../constants/practice-areas.constants';

interface SerperApiOrganicResult {
  position: number;
  title: string;
  link: string;
  snippet: string;
  date?: string;
  sitelinks?: {
    title: string;
    link: string;
  }[];
}

interface SerperApiResponse {
  organic?: SerperApiOrganicResult[];
  searchParameters?: {
    q: string;
    gl: string;
    hl: string;
    num: number;
    type: string;
  };
  searchInformation?: {
    totalResults: string;
    timeTaken: number;
    originalQuery: string;
  };
}

@Injectable()
export class WebSearchService implements SearchSourceProvider {
  private readonly logger = new Logger(WebSearchService.name);
  private readonly httpClient: AxiosInstance;
  private readonly apiKey: string;
  private readonly baseUrl = 'https://google.serper.dev/search';

  // Rate limiting for SerpApi
  private requestCount = 0;
  private windowStart = Date.now();
  private readonly maxRequestsPerMonth = 100; // SerpApi free tier limit
  private readonly windowMs = 30 * 24 * 60 * 60 * 1000; // 30 days

  constructor(private readonly configService: ConfigService) {
    this.apiKey = this.configService.get<string>('SERPAPI_API_KEY') || '';

    if (!this.apiKey) {
      this.logger.warn('Serper API key not configured');
    }

    this.httpClient = axios.create({
      timeout: 15000, // Serper API timeout
      headers: {
        'User-Agent': 'DocGic-Legal-Research-Assistant/1.0',
        'Content-Type': 'application/json',
        'X-API-KEY': this.apiKey
      }
    });
  }

  get name(): string {
    return 'Serper API Google Search';
  }

  get type(): 'legal_database' | 'web_search' | 'news_api' {
    return 'web_search';
  }

  async search(query: SearchQuery): Promise<SearchSourceResult[]> {
    if (!this.apiKey) {
      this.logger.warn('Serper API not configured, returning mock legal search results for development');
      const mockResults = this.getMockLegalResults(query);
      this.logger.debug(`Returning ${mockResults.length} mock results`);
      return mockResults;
    }

    try {
      // Check rate limits
      await this.checkRateLimit();

      // Build enhanced search query
      const enhancedQuery = this.buildLegalSearchQuery(query);

      this.logger.debug(`Performing Serper API search for: ${enhancedQuery}`);

      const requestBody = {
        q: enhancedQuery,
        num: Math.min(query.options.maxResults || 10, 20),
        gl: 'us',
        hl: 'en'
      };

      this.logger.debug(`Serper API request body: ${JSON.stringify(requestBody)}`);

      const response = await this.httpClient.post<SerperApiResponse>(this.baseUrl, requestBody);

      this.requestCount++;

      this.logger.debug(`Serper API response status: ${response.status}`);
      this.logger.debug(`Serper API response data: ${JSON.stringify(response.data)}`);

      if (!response.data.organic || response.data.organic.length === 0) {
        this.logger.warn(`Serper API returned no results. Response: ${JSON.stringify(response.data)}`);
        this.logger.warn(`Falling back to mock results for development`);
        return this.getMockLegalResults(query);
      }

      const results = this.processSerperApiResults(response.data, query);

      this.logger.debug(`Serper API search returned ${results.length} results`);

      return results;

    } catch (error) {
      this.logger.error(`Serper API search failed: ${error.message}`, {
        errorType: error.constructor.name,
        stack: error.stack,
        query: query.query,
        responseStatus: error.response?.status,
        responseData: error.response?.data
      });

      if (error.response?.status === 429) {
        throw new HttpException(
          'Serper API rate limit exceeded',
          HttpStatus.TOO_MANY_REQUESTS
        );
      }

      // Return mock results instead of empty array for better development experience
      const mockResults = this.getMockLegalResults(query);
      this.logger.debug(`Returning ${mockResults.length} mock results after API failure`);

      // Make sure to set mockResult flag to true for all results
      mockResults.forEach(result => {
        if (result.metadata) {
          result.metadata.mockResult = true;
        } else {
          result.metadata = { mockResult: true };
        }
      });

      return mockResults;
    }
  }

  async isAvailable(): Promise<boolean> {
    if (!this.apiKey) {
      return false;
    }

    try {
      // Simple test query to check if service is available
      const response = await this.httpClient.post(this.baseUrl, {
        q: 'test',
        num: 1
      }, {
        timeout: 5000
      });

      return response.data && response.data.organic;
    } catch (error) {
      this.logger.warn(`Serper API availability check failed: ${error.message}`);
      return false;
    }
  }

  getRateLimit(): RateLimitInfo {
    const now = Date.now();

    // Reset window if needed
    if (now - this.windowStart > this.windowMs) {
      this.requestCount = 0;
      this.windowStart = now;
    }

    return {
      remaining: Math.max(0, this.maxRequestsPerMonth - this.requestCount),
      limit: this.maxRequestsPerMonth,
      resetTime: new Date(this.windowStart + this.windowMs),
      windowMs: this.windowMs
    };
  }

  private async checkRateLimit(): Promise<void> {
    const rateLimit = this.getRateLimit();

    if (rateLimit.remaining <= 0) {
      throw new HttpException(
        'Serper API monthly rate limit exceeded',
        HttpStatus.TOO_MANY_REQUESTS
      );
    }
  }

  private buildLegalSearchQuery(query: SearchQuery): string {
    // Start with the base query
    let searchQuery = query.query;

    // Add jurisdiction filter if specified
    if (query.options.jurisdictions && query.options.jurisdictions.length > 0) {
      const jurisdictions = query.options.jurisdictions.join(' OR ');
      searchQuery += ` (${jurisdictions})`;
    }

    // Add practice area filter if specified
    if (query.options.practiceAreas && query.options.practiceAreas.length > 0) {
      const practiceAreas = query.options.practiceAreas.join(' OR ');
      searchQuery += ` (${practiceAreas})`;
    }

    // Add simple legal context (much simpler than before)
    searchQuery += ' law';

    this.logger.debug(`Built simplified legal search query: ${searchQuery}`);

    return searchQuery;
  }

  private buildDateFilter(timeRange?: { from: string; to: string }): any {
    if (!timeRange) {
      return {};
    }

    const fromDate = new Date(timeRange.from);
    const toDate = new Date(timeRange.to);
    const now = new Date();

    // Calculate days from 'from' date to now for SerpApi time filters
    const daysDiff = Math.floor((now.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24));

    // SerpApi supports various time filters
    if (daysDiff <= 1) return { tbs: 'qdr:d' }; // Past day
    if (daysDiff <= 7) return { tbs: 'qdr:w' }; // Past week
    if (daysDiff <= 30) return { tbs: 'qdr:m' }; // Past month
    if (daysDiff <= 365) return { tbs: 'qdr:y' }; // Past year

    // For custom date ranges, use SerpApi's date range format
    const fromFormatted = fromDate.toISOString().split('T')[0].replace(/-/g, '/');
    const toFormatted = toDate.toISOString().split('T')[0].replace(/-/g, '/');

    return {
      tbs: `cdr:1,cd_min:${fromFormatted},cd_max:${toFormatted}`
    };
  }

  private processSerperApiResults(
    response: SerperApiResponse,
    query: SearchQuery
  ): SearchSourceResult[] {
    if (!response.organic || response.organic.length === 0) {
      return [];
    }

    return response.organic.map((item, index) => {
      const practiceAreas = classifyPracticeArea(item.title + ' ' + item.snippet);
      const jurisdictions = extractJurisdictions(item.title + ' ' + item.snippet);

      // Calculate relevance score based on multiple factors
      const relevanceScore = this.calculateSerperApiRelevanceScore(item, query, index);

      // Calculate authority score based on domain (extract from URL)
      const domain = this.extractDomainFromUrl(item.link);
      const authorityScore = this.calculateAuthorityScore(domain);

      // Determine source type based on content
      const sourceType = this.determineSourceTypeFromContent(item);

      return {
        id: `serper_${Date.now()}_${Math.random().toString(36).substr(2, 9)}_${index}`,
        source: 'Serper API Google Search',
        type: sourceType,
        title: item.title,
        url: item.link,
        snippet: item.snippet,
        date: this.extractSerperApiDate(item) || new Date().toISOString(),
        jurisdiction: jurisdictions[0] || 'unknown',
        practiceArea: practiceAreas[0] || 'general',
        relevanceScore,
        authorityScore,
        metadata: {
          displayLink: domain,
          searchRank: item.position,
          practiceAreas,
          jurisdictions,
          sitelinks: item.sitelinks
        }
      };
    });
  }

  private calculateSerperApiRelevanceScore(
    item: SerperApiOrganicResult,
    query: SearchQuery,
    index: number
  ): number {
    let score = 1.0;

    // Position-based scoring (Serper API provides position directly)
    score *= Math.max(0.1, 1.0 - ((item.position - 1) * 0.05));

    // Query term matching in title (higher weight)
    const titleMatches = this.countQueryMatches(query.query, item.title);
    score += titleMatches * 0.3;

    // Query term matching in snippet
    const snippetMatches = this.countQueryMatches(query.query, item.snippet);
    score += snippetMatches * 0.2;

    // Bonus for legal domains
    const domain = this.extractDomainFromUrl(item.link);
    if (this.isLegalDomain(domain)) {
      score *= 1.5;
    }

    // Bonus for government domains
    if (domain.includes('.gov')) {
      score *= 1.8;
    }

    // Bonus for sitelinks (indicates structured data)
    if (item.sitelinks && item.sitelinks.length > 0) {
      score *= 1.2;
    }

    // Normalize to 0-1 range
    return Math.min(1.0, score);
  }

  private calculateAuthorityScore(domain: string): number {
    return calculateAuthorityScore('', domain);
  }

  private determineSourceTypeFromContent(item: SerperApiOrganicResult): 'case_law' | 'statute' | 'regulation' | 'news' | 'article' | 'other' {
    const content = (item.title + ' ' + item.snippet).toLowerCase();
    const domain = this.extractDomainFromUrl(item.link).toLowerCase();

    // Check for case law indicators
    if (content.includes('court') || content.includes('case') || content.includes('opinion') ||
        content.includes('judgment') || content.includes('ruling') ||
        domain.includes('courtlistener') || domain.includes('justia.com/cases')) {
      return 'case_law';
    }

    // Check for statute indicators
    if (content.includes('statute') || content.includes('code') || content.includes('usc') ||
        content.includes('title ') || domain.includes('uscode') ||
        domain.includes('law.cornell.edu/uscode')) {
      return 'statute';
    }

    // Check for regulation indicators
    if (content.includes('regulation') || content.includes('cfr') || content.includes('rule') ||
        content.includes('federal register') || domain.includes('ecfr') ||
        domain.includes('federalregister.gov')) {
      return 'regulation';
    }

    // Check for news indicators
    if (content.includes('news') || domain.includes('news') ||
        domain.includes('reuters') || domain.includes('bloomberg') ||
        domain.includes('law360') || domain.includes('legaltech') ||
        domain.includes('abajournal')) {
      return 'news';
    }

    // Check for legal article indicators
    if (content.includes('article') || content.includes('analysis') ||
        content.includes('commentary') || domain.includes('law.com') ||
        domain.includes('americanbar.org') || domain.includes('lawreview') ||
        domain.includes('ssrn.com')) {
      return 'article';
    }

    return 'other';
  }

  private extractSerperApiDate(item: SerperApiOrganicResult): string | null {
    // Serper API sometimes provides date directly
    if (item.date) {
      const parsedDate = new Date(item.date);
      if (!isNaN(parsedDate.getTime())) {
        return parsedDate.toISOString();
      }
    }

    // Try to extract date from snippet using regex (more comprehensive patterns)
    const datePatterns = [
      /\b(\d{1,2}\/\d{1,2}\/\d{4})\b/, // MM/DD/YYYY
      /\b(\d{4}-\d{2}-\d{2})\b/, // YYYY-MM-DD
      /\b(\w+ \d{1,2}, \d{4})\b/, // Month DD, YYYY
      /\b(\d{1,2} \w+ \d{4})\b/, // DD Month YYYY
      /\b(\w+ \d{4})\b/, // Month YYYY
    ];

    for (const pattern of datePatterns) {
      const match = item.snippet.match(pattern);
      if (match) {
        const parsedDate = new Date(match[1]);
        if (!isNaN(parsedDate.getTime())) {
          return parsedDate.toISOString();
        }
      }
    }

    return null;
  }

  private extractDomainFromUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname;
    } catch (error) {
      // Fallback: extract domain using regex
      const match = url.match(/^https?:\/\/([^\/]+)/);
      return match ? match[1] : url;
    }
  }

  private countQueryMatches(query: string, text: string): number {
    const queryTerms = query.toLowerCase().split(/\s+/);
    const textLower = text.toLowerCase();
    
    return queryTerms.filter(term => textLower.includes(term)).length;
  }

  private isLegalDomain(domain: string): boolean {
    const legalDomains = [
      'law.cornell.edu', 'justia.com', 'findlaw.com', 'martindale.com',
      'americanbar.org', 'law.com', 'lexisnexis.com', 'westlaw.com'
    ];

    return legalDomains.some(legalDomain => domain.includes(legalDomain));
  }

  private getMockLegalResults(query: SearchQuery): SearchSourceResult[] {
    this.logger.debug('Generating mock legal search results for development');

    const mockResults = [
      {
        id: 'mock_web_1',
        source: 'Serper API Mock (Development)',
        type: 'case_law' as const,
        title: 'California Consumer Privacy Act (CCPA) - Recent Court Interpretations',
        url: 'https://law.cornell.edu/ccpa-interpretations',
        snippet: 'Recent court decisions have clarified the scope of the California Consumer Privacy Act, particularly regarding data collection practices and consumer rights.',
        date: new Date().toISOString(),
        jurisdiction: 'california',
        practiceArea: 'privacy',
        relevanceScore: 0.95,
        authorityScore: 0.9,
        metadata: {
          displayLink: 'law.cornell.edu',
          searchRank: 1,
          practiceAreas: ['privacy', 'data_protection'],
          jurisdictions: ['california'],
          mockResult: true
        }
      },
      {
        id: 'mock_web_2',
        source: 'Serper API Mock (Development)',
        type: 'news' as const,
        title: 'Supreme Court to Hear Major Privacy Case in 2024',
        url: 'https://law360.com/supreme-court-privacy-case-2024',
        snippet: 'The Supreme Court has agreed to hear a landmark case that could reshape digital privacy law.',
        date: new Date(Date.now() - 172800000).toISOString(),
        jurisdiction: 'federal',
        practiceArea: 'privacy',
        relevanceScore: 0.82,
        authorityScore: 0.85,
        metadata: {
          displayLink: 'law360.com',
          searchRank: 2,
          practiceAreas: ['privacy', 'constitutional'],
          jurisdictions: ['federal'],
          mockResult: true
        }
      }
    ];

    // Filter and limit results
    const maxResults = query.options.maxResults || 10;
    return mockResults.slice(0, maxResults);
  }
}

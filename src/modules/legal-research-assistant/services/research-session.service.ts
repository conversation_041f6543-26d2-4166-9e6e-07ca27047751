import { Injectable, Logger, NotFoundException, ForbiddenException, Inject, forwardRef } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { 
  ResearchSession, 
  ResearchSessionDocument, 
  RESEARCH_SESSION_MODEL,
  ResearchQueryRecord 
} from '../schemas/research-session.schema';
import { 
  CreateResearchSessionDto, 
  UpdateResearchSessionDto, 
  ListResearchSessionsDto,
  ResearchSessionResponseDto 
} from '../dto/research-session.dto';
import { 
  ResearchSession as IResearchSession,
  SessionSummary,
  SessionAnalytics 
} from '../interfaces/research-session.interface';
import { 
  LegalResearchResult, 
  SearchResults, 
  SynthesisResult 
} from '../interfaces/legal-research-result.interface';
import { LegalResearchQueryDto, FollowUpQueryDto } from '../dto/legal-research-query.dto';
import { LegalSynthesisService } from './legal-synthesis.service';

@Injectable()
export class ResearchSessionService {
  private readonly logger = new Logger(ResearchSessionService.name);

  constructor(
    @InjectModel(RESEARCH_SESSION_MODEL)
    private readonly sessionModel: Model<ResearchSessionDocument>,
    @Inject(forwardRef(() => LegalSynthesisService))
    private readonly legalSynthesisService: LegalSynthesisService,
  ) {}

  async createSession(
    dto: CreateResearchSessionDto,
    organizationId: string,
    userId: string,
  ): Promise<ResearchSessionResponseDto> {
    this.logger.debug(`Creating research session for user ${userId}`);

    const sessionId = `session_${uuidv4()}`;
    
    const session = new this.sessionModel({
      sessionId,
      organizationId,
      userId,
      title: dto.title,
      description: dto.description,
      tags: dto.tags || [],
      isShared: dto.isShared || false,
      queryCount: 0,
      queries: [],
      totalCreditsUsed: 0,
      metadata: {
        lastAccessedAt: new Date()
      }
    });

    await session.save();

    this.logger.debug(`Created research session ${sessionId}`);

    return this.toResponseDto(session);
  }

  async getSession(
    sessionId: string,
    organizationId: string,
    userId: string,
  ): Promise<IResearchSession> {
    const session = await this.findSessionWithAccess(sessionId, organizationId, userId);
    
    // Update last accessed time
    session.metadata = session.metadata || {};
    session.metadata.lastAccessedAt = new Date();
    await session.save();

    return this.toInterfaceObject(session);
  }

  async updateSession(
    sessionId: string,
    dto: UpdateResearchSessionDto,
    organizationId: string,
    userId: string,
  ): Promise<ResearchSessionResponseDto> {
    const session = await this.findSessionWithAccess(sessionId, organizationId, userId);

    // Only allow updates by the owner or collaborators
    if (session.userId !== userId && !this.isCollaborator(session, userId)) {
      throw new ForbiddenException('Insufficient permissions to update this session');
    }

    // Update fields
    if (dto.title !== undefined) session.title = dto.title;
    if (dto.description !== undefined) session.description = dto.description;
    if (dto.tags !== undefined) session.tags = dto.tags;
    if (dto.isShared !== undefined) session.isShared = dto.isShared;

    await session.save();

    this.logger.debug(`Updated research session ${sessionId}`);

    return this.toResponseDto(session);
  }

  async deleteSession(
    sessionId: string,
    organizationId: string,
    userId: string,
  ): Promise<void> {
    const session = await this.findSessionWithAccess(sessionId, organizationId, userId);

    // Only allow deletion by the owner
    if (session.userId !== userId) {
      throw new ForbiddenException('Only the session owner can delete this session');
    }

    await this.sessionModel.deleteOne({ sessionId, organizationId });

    this.logger.debug(`Deleted research session ${sessionId}`);
  }

  async listSessions(
    dto: ListResearchSessionsDto,
    organizationId: string,
    userId: string,
  ): Promise<{ sessions: SessionSummary[]; pagination: any }> {
    const { page = 1, limit = 20, tags, search, sharingStatus, sortBy = 'updatedAt', sort = 'desc' } = dto;
    
    // Build query
    const query: any = {
      organizationId,
      $or: [
        { userId }, // User's own sessions
        { isShared: true }, // Shared sessions in the organization
        { 'metadata.collaborators.userId': userId } // Sessions shared with user
      ]
    };

    // Apply filters
    if (tags) {
      const tagArray = tags.split(',').map(tag => tag.trim());
      query.tags = { $in: tagArray };
    }

    if (search) {
      query.$text = { $search: search };
    }

    if (sharingStatus && sharingStatus !== 'all') {
      if (sharingStatus === 'shared') {
        query.isShared = true;
      } else if (sharingStatus === 'private') {
        query.isShared = false;
        query.userId = userId; // Only user's private sessions
      }
    }

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const sortOrder = sort === 'desc' ? -1 : 1;
    const sortObj: any = { [sortBy]: sortOrder };

    const [sessions, total] = await Promise.all([
      this.sessionModel
        .find(query)
        .sort(sortObj)
        .skip(skip)
        .limit(limit)
        .lean()
        .exec(),
      this.sessionModel.countDocuments(query).exec(),
    ]);

    const sessionSummaries: SessionSummary[] = sessions.map(session => ({
      sessionId: session.sessionId,
      title: session.title,
      description: session.description,
      tags: session.tags,
      createdAt: session.createdAt,
      updatedAt: session.updatedAt,
      queryCount: session.queryCount,
      totalCreditsUsed: session.totalCreditsUsed,
      isShared: session.isShared,
      lastQuery: session.queries.length > 0 ? {
        query: session.queries[session.queries.length - 1].query || 
               session.queries[session.queries.length - 1].question || '',
        timestamp: session.queries[session.queries.length - 1].timestamp
      } : undefined
    }));

    return {
      sessions: sessionSummaries,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  async addQueryToSession(
    sessionId: string,
    query: LegalResearchQueryDto | FollowUpQueryDto,
    searchResults: SearchResults,
    synthesis: SynthesisResult | null,
    creditsUsed: number,
    organizationId: string,
    userId: string,
    followUpSuggestions?: string[],
  ): Promise<void> {
    const session = await this.findSessionWithAccess(sessionId, organizationId, userId);

    const queryRecord: ResearchQueryRecord = {
      queryId: `query_${uuidv4()}`,
      query: 'query' in query ? query.query : undefined,
      question: 'question' in query ? query.question : undefined,
      timestamp: new Date(),
      creditsUsed,
      resultSummary: `Found ${searchResults.totalSources} sources${synthesis ? ' with AI synthesis' : ''}`,
      type: 'query' in query ? 'initial' : 'followup',
      metadata: {
        sourcesFound: searchResults.totalSources,
        synthesisGenerated: !!synthesis,
        responseTime: searchResults.metadata.duration,
        sourceBreakdown: searchResults.metadata.sourceBreakdown
      },
      // Store complete research results for session history
      searchResults: {
        totalSources: searchResults.totalSources,
        sources: searchResults.sources.map(source => ({
          id: source.id,
          type: source.type,
          title: source.title,
          citation: source.citation,
          court: source.court,
          authority: source.authority,
          date: source.date,
          jurisdiction: source.jurisdiction,
          practiceArea: source.practiceArea,
          url: source.url,
          snippet: source.snippet,
          relevanceScore: source.relevanceScore,
          authorityScore: source.authorityScore
        })),
        metadata: {
          duration: searchResults.metadata.duration,
          cacheHit: searchResults.metadata.cacheHit,
          searchStrategies: searchResults.metadata.searchStrategies,
          sourceBreakdown: searchResults.metadata.sourceBreakdown
        }
      },
      aiSynthesis: synthesis ? {
        legalAnalysis: synthesis.legalAnalysis,
        keyFindings: synthesis.keyFindings,
        citations: synthesis.citations,
        confidenceScore: synthesis.confidenceScore,
        practiceImplications: synthesis.practiceImplications,
        jurisdictionalNotes: synthesis.jurisdictionalNotes,
        recentDevelopments: synthesis.recentDevelopments,
        metadata: {
          duration: synthesis.metadata.duration,
          sourcesAnalyzed: synthesis.metadata.sourcesAnalyzed,
          aiModel: synthesis.metadata.aiModel,
          promptVersion: synthesis.metadata.promptVersion
        }
      } : undefined,
      followUpSuggestions: followUpSuggestions || []
    };

    // Add query to session
    session.queries.push(queryRecord);
    session.queryCount = session.queries.length;
    session.totalCreditsUsed += creditsUsed;

    await session.save();

    this.logger.debug(`Added query to session ${sessionId}, total queries: ${session.queryCount}`);
  }

  async getSessionAnalytics(
    sessionId: string,
    organizationId: string,
    userId: string,
  ): Promise<SessionAnalytics> {
    const session = await this.findSessionWithAccess(sessionId, organizationId, userId);

    const queries = session.queries;
    const totalQueries = queries.length;
    
    if (totalQueries === 0) {
      return {
        sessionId,
        totalQueries: 0,
        totalCreditsUsed: 0,
        averageCreditsPerQuery: 0,
        sessionDuration: 0,
        topPracticeAreas: [],
        topJurisdictions: [],
        queryTypes: { initial: 0, followup: 0 },
        synthesisGenerated: 0,
        averageResponseTime: 0
      };
    }

    // Calculate metrics
    const totalCreditsUsed = session.totalCreditsUsed;
    const averageCreditsPerQuery = totalCreditsUsed / totalQueries;
    
    const firstQuery = queries[0];
    const lastQuery = queries[totalQueries - 1];
    const sessionDuration = Math.round(
      (lastQuery.timestamp.getTime() - firstQuery.timestamp.getTime()) / (1000 * 60)
    ); // minutes

    const queryTypes = queries.reduce(
      (acc, query) => {
        acc[query.type]++;
        return acc;
      },
      { initial: 0, followup: 0 }
    );

    const synthesisGenerated = queries.filter(q => q.metadata?.synthesisGenerated).length;
    
    const responseTimes = queries
      .map(q => q.metadata?.responseTime)
      .filter(time => time !== undefined) as number[];
    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length / 1000 // convert to seconds
      : 0;

    return {
      sessionId,
      totalQueries,
      totalCreditsUsed,
      averageCreditsPerQuery,
      sessionDuration,
      topPracticeAreas: session.metadata?.topPracticeAreas || [],
      topJurisdictions: session.metadata?.topJurisdictions || [],
      queryTypes,
      synthesisGenerated,
      averageResponseTime
    };
  }

  async startOrGetSession(
    query: LegalResearchQueryDto,
    organizationId: string,
    userId: string,
  ): Promise<ResearchSessionDocument> {
    if (query.sessionId) {
      return this.findSessionWithAccess(query.sessionId, organizationId, userId);
    } else {
      // Create a new session
      this.logger.debug(`No session ID provided. Creating new research session for user ${userId}`);
      const sessionId = `session_${uuidv4()}`;
      const title = await this.legalSynthesisService.generateTitleFromQuery(query.query);

      const newSession = new this.sessionModel({
        sessionId,
        organizationId,
        userId,
        title,
        initialQuery: query.query,
        queries: [],
        queryCount: 0,
        totalCreditsUsed: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
        metadata: {
          lastAccessedAt: new Date()
        }
      });
      return newSession.save();
    }
  }

  private async findSessionWithAccess(
    sessionId: string,
    organizationId: string,
    userId: string,
  ): Promise<ResearchSessionDocument> {
    const session = await this.sessionModel.findOne({
      sessionId,
      organizationId,
      $or: [
        { userId }, // User's own session
        { isShared: true }, // Shared session in organization
        { 'metadata.collaborators.userId': userId } // Session shared with user
      ]
    }).exec();

    if (!session) {
      throw new NotFoundException('Research session not found or access denied');
    }

    return session;
  }

  private isCollaborator(session: ResearchSessionDocument, userId: string): boolean {
    return session.metadata?.collaborators?.some(
      collaborator => collaborator.userId === userId && 
      ['collaborator', 'owner'].includes(collaborator.role)
    ) || false;
  }

  private toResponseDto(session: ResearchSessionDocument): ResearchSessionResponseDto {
    return {
      sessionId: session.sessionId,
      title: session.title,
      description: session.description,
      tags: session.tags,
      createdAt: session.createdAt,
      updatedAt: session.updatedAt,
      isShared: session.isShared,
      queryCount: session.queryCount,
      totalCreditsUsed: session.totalCreditsUsed,
      collaborators: session.metadata?.collaborators
    };
  }

  private toInterfaceObject(session: ResearchSessionDocument): IResearchSession {
    return {
      sessionId: session.sessionId,
      organizationId: session.organizationId,
      userId: session.userId,
      title: session.title,
      description: session.description,
      tags: session.tags,
      createdAt: session.createdAt,
      updatedAt: session.updatedAt,
      isShared: session.isShared,
      queryCount: session.queryCount,
      queries: session.queries,
      totalCreditsUsed: session.totalCreditsUsed,
      metadata: session.metadata as any // Type assertion to handle interface mismatch
    };
  }
}

import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { getModelToken } from '@nestjs/mongoose';
import { LegalResearchAssistantService } from '../services/legal-research-assistant.service';
import { LegalSearchOrchestratorService } from '../services/legal-search-orchestrator.service';
import { LegalSynthesisService } from '../services/legal-synthesis.service';
import { ResearchSessionService } from '../services/research-session.service';
import { CreditManagementService } from '../../subscription/services/credit-management.service';
import { SubscriptionService } from '../../subscription/services/subscription.service';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { AIService } from '../../ai/services/ai.service';
import { RESEARCH_SESSION_MODEL } from '../schemas/research-session.schema';
import { SubscriptionTier } from '../../subscription/enums/subscription-tier.enum';

describe('LegalResearchAssistantService', () => {
  let service: LegalResearchAssistantService;
  let mockSearchOrchestrator: jest.Mocked<LegalSearchOrchestratorService>;
  let mockSynthesisService: jest.Mocked<LegalSynthesisService>;
  let mockSessionService: jest.Mocked<ResearchSessionService>;
  let mockCreditService: jest.Mocked<CreditManagementService>;
  let mockSubscriptionService: jest.Mocked<SubscriptionService>;
  let mockAiService: jest.Mocked<AIService>;

  beforeEach(async () => {
    // Create mock services
    mockSearchOrchestrator = {
      search: jest.fn(),
    } as any;

    mockSynthesisService = {
      synthesizeResults: jest.fn(),
    } as any;

    mockSessionService = {
      addQueryToSession: jest.fn(),
      getSession: jest.fn(),
    } as any;

    mockCreditService = {
      deductCreditsForFeature: jest.fn(),
    } as any;

    mockSubscriptionService = {
      getSubscription: jest.fn(),
      getFeatureAvailability: jest.fn(),
    } as any;

    const mockTenantContext = {
      getCurrentOrganization: jest.fn().mockReturnValue('test-org'),
      getCurrentUserId: jest.fn().mockReturnValue('test-user'),
    } as any;

    mockAiService = {
      generateResponse: jest.fn(),
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LegalResearchAssistantService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue('test-value'),
          },
        },
        {
          provide: LegalSearchOrchestratorService,
          useValue: mockSearchOrchestrator,
        },
        {
          provide: LegalSynthesisService,
          useValue: mockSynthesisService,
        },
        {
          provide: ResearchSessionService,
          useValue: mockSessionService,
        },
        {
          provide: CreditManagementService,
          useValue: mockCreditService,
        },
        {
          provide: SubscriptionService,
          useValue: mockSubscriptionService,
        },
        {
          provide: TenantContextService,
          useValue: mockTenantContext,
        },
        {
          provide: AIService,
          useValue: mockAiService,
        },
        {
          provide: getModelToken(RESEARCH_SESSION_MODEL),
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<LegalResearchAssistantService>(LegalResearchAssistantService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('performResearch', () => {
    it('should perform basic research without synthesis', async () => {
      // Arrange
      const query = {
        query: 'What are the recent developments in data privacy law?',
        options: {
          includeSynthesis: false,
          maxSources: 5,
          sourceTypes: ['case_law', 'statutes'],
        },
      };

      const mockSearchResults = {
        totalSources: 3,
        sources: [
          {
            id: 'test-1',
            type: 'case_law' as const,
            title: 'Test Case',
            url: 'https://example.com',
            snippet: 'Test snippet',
            date: '2024-01-01',
            jurisdiction: 'federal',
            relevanceScore: 0.9,
            authorityScore: 0.8,
          },
        ],
        metadata: {
          duration: 1000,
          cacheHit: false,
          searchStrategies: ['case_law_search'],
        },
      };

      mockSubscriptionService.getSubscription.mockResolvedValue({
        tier: SubscriptionTier.LAWYER,
      } as any);
      mockSubscriptionService.getFeatureAvailability.mockResolvedValue(true);
      mockCreditService.deductCreditsForFeature.mockResolvedValue({
        success: true,
        newBalance: 100,
        transaction: { transactionId: 'test', amount: 1 } as any,
        message: 'Success',
      });
      mockSearchOrchestrator.search.mockResolvedValue(mockSearchResults);

      // Act
      const result = await service.performResearch(
        query as any,
        'test-org',
        'test-user'
      );

      // Assert
      expect(result).toBeDefined();
      expect(result.query).toBe(query.query);
      expect(result.searchResults).toEqual(mockSearchResults);
      expect(result.aiSynthesis).toBeNull();
      expect(result.metadata.creditsUsed).toBe(1); // Only search cost
    });

    it('should perform research with AI synthesis', async () => {
      // Arrange
      const query = {
        query: 'What are the recent developments in data privacy law?',
        options: {
          includeSynthesis: true,
          maxSources: 5,
          sourceTypes: ['case_law', 'statutes'],
        },
      };

      const mockSearchResults = {
        totalSources: 3,
        sources: [],
        metadata: {
          duration: 1000,
          cacheHit: false,
          searchStrategies: ['case_law_search'],
        },
      };

      const mockSynthesis = {
        legalAnalysis: {
          text: 'Test analysis with comprehensive overview and detailed reasoning',
          sourceUrls: [] // Added to match new LegalAnalysisContent structure
        },
        keyFindings: [
          {
            finding: 'Finding 1',
            sourceUrls: ['https://example.com/source1'],
            confidence: 0.9
          },
          {
            finding: 'Finding 2',
            sourceUrls: ['https://example.com/source2'],
            confidence: 0.8
          }
        ],
        citations: ['Citation 1'],
        confidenceScore: 0.8,
        practiceImplications: ['Implication 1'],
        metadata: {
          duration: 2000,
          sourcesAnalyzed: 3,
          aiModel: 'openai',
        },
      };

      mockSubscriptionService.getSubscription.mockResolvedValue({
        tier: SubscriptionTier.LAWYER,
      } as any);
      mockSubscriptionService.getFeatureAvailability.mockResolvedValue(true);
      mockCreditService.deductCreditsForFeature
        .mockResolvedValueOnce({
          success: true,
          newBalance: 100,
          transaction: { transactionId: 'test1', amount: 1 } as any,
          message: 'Success'
        }) // For search
        .mockResolvedValueOnce({
          success: true,
          newBalance: 97,
          transaction: { transactionId: 'test2', amount: 3 } as any,
          message: 'Success'
        }); // For synthesis
      mockSearchOrchestrator.search.mockResolvedValue(mockSearchResults);
      mockSynthesisService.synthesizeResults.mockResolvedValue(mockSynthesis);

      // Act
      const result = await service.performResearch(
        query as any,
        'test-org',
        'test-user'
      );

      // Assert
      expect(result).toBeDefined();
      expect(result.query).toBe(query.query);
      expect(result.searchResults).toEqual(mockSearchResults);
      expect(result.aiSynthesis).toEqual(mockSynthesis);
      expect(result.metadata.creditsUsed).toBe(4); // Search (1) + synthesis (3)
    });

    it('should handle insufficient credits gracefully', async () => {
      // Arrange
      const query = {
        query: 'Test query',
        options: {
          includeSynthesis: false,
        },
      };

      mockSubscriptionService.getSubscription.mockResolvedValue({
        tier: SubscriptionTier.LAWYER,
      } as any);
      mockSubscriptionService.getFeatureAvailability.mockResolvedValue(true);
      mockCreditService.deductCreditsForFeature.mockResolvedValue({
        success: false,
        newBalance: 0,
        transaction: null,
        message: 'Insufficient credits',
      });

      // Act & Assert
      await expect(
        service.performResearch(query as any, 'test-org', 'test-user')
      ).rejects.toThrow('Insufficient credits for legal research');
    });

    it('should generate AI-powered follow-up suggestions for a session-based query', async () => {
      // Arrange
      const query = {
        query: 'What are the latest rulings on intellectual property in AI-generated art?',
        sessionId: 'test-session-id',
        options: { includeSynthesis: true },
      };

      const mockSession = {
        _id: 'test-session-id',
        initialQuery: 'Tell me about intellectual property in AI art.',
        queries: [
          { query: 'Tell me about intellectual property in AI art.', timestamp: new Date() },
        ],
        findings: [],
      };

      const mockSearchResults = {
        totalSources: 1,
        sources: [{ id: 'test-1', type: 'case_law' as const, title: 'Test Case', url: 'https://example.com', snippet: 'Test snippet', date: '2024-01-01', jurisdiction: 'federal', relevanceScore: 0.9, authorityScore: 0.8 }],
        metadata: { duration: 1000, cacheHit: false, searchStrategies: ['case_law_search'] },
      };

      const mockSynthesis = {
        legalAnalysis: { text: 'AI art IP is complex.', sourceUrls: [] },
        keyFindings: [{ finding: 'Fair use is a key factor.', sourceUrls: [], confidence: 0.9 }],
        citations: [],
        confidenceScore: 0.8,
        practiceImplications: [],
        metadata: { duration: 2000, sourcesAnalyzed: 1, aiModel: 'openai' },
      };
      
      const mockAiSuggestions = ['How does copyright law apply to AI training data?', 'What constitutes "transformative use" in AI art?'];

      mockSubscriptionService.getSubscription.mockResolvedValue({ tier: SubscriptionTier.LAWYER } as any);
      mockSubscriptionService.getFeatureAvailability.mockResolvedValue(true);
      // Mock credit deduction to succeed for all calls
      mockCreditService.deductCreditsForFeature.mockResolvedValue({ success: true, newBalance: 100 } as any);
      mockSearchOrchestrator.search.mockResolvedValue(mockSearchResults as any);
      mockSynthesisService.synthesizeResults.mockResolvedValue(mockSynthesis as any);
      mockSessionService.getSession.mockResolvedValue(mockSession as any);
      mockAiService.generateResponse.mockResolvedValue(JSON.stringify(mockAiSuggestions));

      // Act
      const result = await service.performResearch(query as any, 'test-org', 'test-user');

      // Assert
      expect(result).toBeDefined();
      expect(mockSessionService.getSession).toHaveBeenCalledWith('test-session-id', 'test-org', 'test-user');
      expect(mockAiService.generateResponse).toHaveBeenCalled();
      expect(result.followUpSuggestions).toEqual(mockAiSuggestions);
    });
  });

  describe('calculateCreditCost', () => {
    it('should return base cost of 1 for basic search', () => {
      const cost = (service as any).calculateCreditCost({});
      expect(cost).toBe(1);
    });
  });

  describe('isSynthesisAvailable', () => {
    it('should return true for lawyer tier', () => {
      const available = (service as any).isSynthesisAvailable('lawyer');
      expect(available).toBe(true);
    });

    it('should return false for law_student tier', () => {
      const available = (service as any).isSynthesisAvailable('law_student');
      expect(available).toBe(false);
    });
  });
});

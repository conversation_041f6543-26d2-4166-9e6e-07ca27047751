import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';

// Controllers
import { LegalResearchAssistantController } from './controllers/legal-research-assistant.controller';

// Services
import { LegalResearchAssistantService } from './services/legal-research-assistant.service';
import { LegalSearchOrchestratorService } from './services/legal-search-orchestrator.service';
import { WebSearchService } from './services/web-search.service';
import { ResearchSessionService } from './services/research-session.service';
import { LegalSynthesisService } from './services/legal-synthesis.service';

// Schemas
import { 
  ResearchSession, 
  ResearchSessionSchema, 
  RESEARCH_SESSION_MODEL 
} from './schemas/research-session.schema';

// External modules
import { AuthModule } from '../auth/auth.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { AIModule } from '../ai/ai.module';
import { LegalResearchModule } from '../legal-research/legal-research.module';
import { SharedModule } from '../shared/shared.module';

@Module({
  imports: [
    ConfigModule,
    
    // MongoDB schemas
    MongooseModule.forFeature([
      {
        name: RESEARCH_SESSION_MODEL,
        schema: ResearchSessionSchema,
      },
    ]),

    // External modules
    forwardRef(() => AuthModule),
    forwardRef(() => SubscriptionModule),
    forwardRef(() => AIModule),
    forwardRef(() => LegalResearchModule),
    forwardRef(() => SharedModule),
  ],
  
  controllers: [
    LegalResearchAssistantController,
  ],
  
  providers: [
    // Main service
    LegalResearchAssistantService,
    
    // Core services
    LegalSearchOrchestratorService,
    WebSearchService,
    ResearchSessionService,
    LegalSynthesisService,
  ],
  
  exports: [
    // Export main service for potential use by other modules
    LegalResearchAssistantService,
    ResearchSessionService,
  ],
})
export class LegalResearchAssistantModule {}

import { 
  IsString, 
  Is<PERSON><PERSON>al, 
  IsBoolean, 
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { BaseDto } from '../../../common/dto/base.dto';
import { PaginationDto } from '../../../common/dto/pagination.dto';

export class CreateResearchSessionDto extends BaseDto {
  @ApiProperty({ 
    description: 'Title for the research session',
    example: 'California Privacy Law Research',
    minLength: 3,
    maxLength: 200
  })
  @IsString()
  @MinLength(3, { message: 'Title must be at least 3 characters long' })
  @MaxLength(200, { message: 'Title cannot exceed 200 characters' })
  title: string;

  @ApiPropertyOptional({ 
    description: 'Description of the research session',
    example: 'Research for client advisory on CCPA compliance',
    maxLength: 1000
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000, { message: 'Description cannot exceed 1000 characters' })
  description?: string;

  @ApiPropertyOptional({ 
    description: 'Tags for categorizing the session',
    type: [String],
    example: ['privacy', 'california', 'ccpa']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[] = [];

  @ApiPropertyOptional({ 
    description: 'Whether the session can be shared with team members',
    default: false
  })
  @IsOptional()
  @IsBoolean()
  isShared?: boolean = false;
}

export class UpdateResearchSessionDto extends BaseDto {
  @ApiPropertyOptional({ 
    description: 'Updated title for the research session',
    minLength: 3,
    maxLength: 200
  })
  @IsOptional()
  @IsString()
  @MinLength(3, { message: 'Title must be at least 3 characters long' })
  @MaxLength(200, { message: 'Title cannot exceed 200 characters' })
  title?: string;

  @ApiPropertyOptional({ 
    description: 'Updated description of the research session',
    maxLength: 1000
  })
  @IsOptional()
  @IsString()
  @MaxLength(1000, { message: 'Description cannot exceed 1000 characters' })
  description?: string;

  @ApiPropertyOptional({ 
    description: 'Updated tags for categorizing the session',
    type: [String]
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional({ 
    description: 'Updated sharing status'
  })
  @IsOptional()
  @IsBoolean()
  isShared?: boolean;
}

export class ListResearchSessionsDto extends PaginationDto {
  @ApiPropertyOptional({ 
    description: 'Filter sessions by tags (comma-separated)',
    example: 'privacy,california'
  })
  @IsOptional()
  @IsString()
  tags?: string;

  @ApiPropertyOptional({ 
    description: 'Search in session titles and descriptions',
    example: 'privacy law'
  })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ 
    description: 'Filter by sharing status',
    enum: ['shared', 'private', 'all'],
    default: 'all'
  })
  @IsOptional()
  @IsEnum(['shared', 'private', 'all'])
  sharingStatus?: string = 'all';

  @ApiPropertyOptional({ 
    description: 'Sort sessions by field',
    enum: ['createdAt', 'updatedAt', 'title', 'queryCount'],
    default: 'updatedAt'
  })
  @IsOptional()
  @IsEnum(['createdAt', 'updatedAt', 'title', 'queryCount'])
  sortBy?: string = 'updatedAt';
}

export class ShareSessionDto extends BaseDto {
  @ApiProperty({ 
    description: 'Email addresses to share the session with',
    type: [String],
    example: ['<EMAIL>', '<EMAIL>']
  })
  @IsArray()
  @IsString({ each: true })
  emails: string[];

  @ApiPropertyOptional({ 
    description: 'Role for shared users',
    enum: ['viewer', 'collaborator'],
    default: 'viewer'
  })
  @IsOptional()
  @IsEnum(['viewer', 'collaborator'])
  role?: 'viewer' | 'collaborator' = 'viewer';

  @ApiPropertyOptional({ 
    description: 'Optional message to include with the share invitation',
    maxLength: 500
  })
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Message cannot exceed 500 characters' })
  message?: string;
}

export class SessionCollaboratorDto {
  @ApiProperty({ 
    description: 'User ID of the collaborator',
    example: 'user_123456'
  })
  userId: string;

  @ApiProperty({ 
    description: 'Email of the collaborator',
    example: '<EMAIL>'
  })
  email: string;

  @ApiProperty({ 
    description: 'Role of the collaborator',
    enum: ['viewer', 'collaborator', 'owner']
  })
  role: 'viewer' | 'collaborator' | 'owner';

  @ApiProperty({ 
    description: 'When the session was shared with this user'
  })
  sharedAt: Date;
}

export class ResearchSessionResponseDto {
  @ApiProperty({ description: 'Session ID' })
  sessionId: string;

  @ApiProperty({ description: 'Session title' })
  title: string;

  @ApiPropertyOptional({ description: 'Session description' })
  description?: string;

  @ApiProperty({ description: 'Session tags', type: [String] })
  tags: string[];

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;

  @ApiProperty({ description: 'Whether session is shared' })
  isShared: boolean;

  @ApiProperty({ description: 'Number of queries in session' })
  queryCount: number;

  @ApiProperty({ description: 'Total credits used in session' })
  totalCreditsUsed: number;

  @ApiPropertyOptional({ 
    description: 'Session collaborators (only for shared sessions)',
    type: [SessionCollaboratorDto]
  })
  collaborators?: SessionCollaboratorDto[];
}

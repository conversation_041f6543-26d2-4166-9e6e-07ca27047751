import { 
  IsString, 
  IsOptional, 
  IsBoolean, 
  IsArray, 
  IsNumber, 
  IsEnum, 
  IsDateString,
  ValidateNested,
  Min,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { BaseDto } from '../../../common/dto/base.dto';

export class TimeRangeDto {
  @ApiProperty({ 
    description: 'Start date for time range filter',
    example: '2023-01-01'
  })
  @IsDateString()
  from: string;

  @ApiProperty({ 
    description: 'End date for time range filter',
    example: '2024-12-31'
  })
  @IsDateString()
  to: string;
}

export class ResearchOptionsDto {
  @ApiPropertyOptional({
    description: 'Whether to include AI synthesis of results',
    default: true
  })
  @IsOptional()
  @IsBoolean()
  includeSynthesis: boolean = true;

  @ApiPropertyOptional({ 
    description: 'Maximum number of sources to return',
    minimum: 1,
    maximum: 50,
    default: 10
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(50)
  maxSources?: number = 10;

  @ApiPropertyOptional({ 
    description: 'Jurisdictions to filter results',
    type: [String],
    example: ['california', 'federal']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  jurisdictions?: string[];

  @ApiPropertyOptional({ 
    description: 'Practice areas to focus on',
    type: [String],
    example: ['privacy', 'data_protection', 'contracts']
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  practiceAreas?: string[];

  @ApiPropertyOptional({ 
    description: 'Time range for filtering results',
    type: TimeRangeDto
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => TimeRangeDto)
  timeRange?: TimeRangeDto;

  @ApiPropertyOptional({ 
    description: 'Types of sources to search',
    type: [String],
    enum: ['case_law', 'statutes', 'regulations', 'news'],
    default: ['case_law', 'statutes', 'regulations', 'news']
  })
  @IsOptional()
  @IsArray()
  @IsEnum(['case_law', 'statutes', 'regulations', 'news'], { each: true })
  sourceTypes?: Array<'case_law' | 'statutes' | 'regulations' | 'news'> = [
    'case_law', 'statutes', 'regulations', 'news'
  ];

  @ApiPropertyOptional({
    description: 'Style of AI synthesis',
    enum: ['brief', 'comprehensive', 'analytical'],
    default: 'comprehensive'
  })
  @IsOptional()
  @IsEnum(['brief', 'comprehensive', 'analytical'])
  synthesisStyle: 'brief' | 'comprehensive' | 'analytical' = 'comprehensive';

  @ApiPropertyOptional({
    description: 'Whether to focus on previous research context (for follow-up queries)',
    default: false
  })
  @IsOptional()
  @IsBoolean()
  focusOnPrevious?: boolean = false;
}

export class LegalResearchQueryDto extends BaseDto {
  @ApiProperty({ 
    description: 'Legal research query in natural language',
    example: 'What are the recent developments in data privacy law in California?',
    minLength: 10,
    maxLength: 1000
  })
  @IsString()
  @MinLength(10, { message: 'Query must be at least 10 characters long' })
  @MaxLength(1000, { message: 'Query cannot exceed 1000 characters' })
  query: string;

  @ApiPropertyOptional({ 
    description: 'Research options and filters',
    type: ResearchOptionsDto
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ResearchOptionsDto)
  options?: ResearchOptionsDto = new ResearchOptionsDto();

  @ApiPropertyOptional({ 
    description: 'Session ID to associate this query with an existing research session',
    example: 'session_789012'
  })
  @IsOptional()
  @IsString()
  sessionId?: string;
}

export class FollowUpQueryDto extends BaseDto {
  @ApiProperty({ 
    description: 'Session ID for the research session',
    example: 'session_789012'
  })
  @IsString()
  sessionId: string;

  @ApiProperty({ 
    description: 'Follow-up question in natural language',
    example: 'How do the new CCPA amendments affect small businesses?',
    minLength: 5,
    maxLength: 500
  })
  @IsString()
  @MinLength(5, { message: 'Question must be at least 5 characters long' })
  @MaxLength(500, { message: 'Question cannot exceed 500 characters' })
  question: string;

  @ApiPropertyOptional({ 
    description: 'Research options for the follow-up query',
    type: ResearchOptionsDto
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ResearchOptionsDto)
  options?: ResearchOptionsDto = new ResearchOptionsDto();

  @ApiPropertyOptional({ 
    description: 'Whether to focus on previous research context',
    default: true
  })
  @IsOptional()
  @IsBoolean()
  focusOnPrevious?: boolean = true;
}

export class ResearchAnalyticsQueryDto extends BaseDto {
  @ApiPropertyOptional({ 
    description: 'Time period for analytics',
    enum: ['7d', '30d', '90d', '1y'],
    default: '30d'
  })
  @IsOptional()
  @IsEnum(['7d', '30d', '90d', '1y'])
  period?: string = '30d';

  @ApiPropertyOptional({ 
    description: 'Group results by time period',
    enum: ['day', 'week', 'month'],
    default: 'day'
  })
  @IsOptional()
  @IsEnum(['day', 'week', 'month'])
  groupBy?: string = 'day';
}

import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BullModule } from '@nestjs/bull';

// Controllers
import { GamificationController } from './controllers/gamification.controller';
import { AchievementsController } from './controllers/achievements.controller';
import { LeaderboardsController } from './controllers/leaderboards.controller';
import { CharactersController } from './controllers/characters.controller';

// Services
import { GamificationService } from './services/gamification.service';
import { AchievementService } from './services/achievement.service';
// LevelService functionality is handled by LevelEngine
import { RelationshipService } from './services/relationship.service';
import { LeaderboardService } from './services/leaderboard.service';
import { CharacterService } from './services/character.service';
import { PressureEventService } from './services/pressure-event.service';

// Engines
import { AchievementEngine } from './engines/achievement.engine';
import { LevelEngine } from './engines/level.engine';

// Schemas
import {
  UserGamificationSchema,
  USER_GAMIFICATION_MODEL,
} from './schemas/user-gamification.schema';
import {
  AchievementSchema,
  ACHIEVEMENT_MODEL,
} from './schemas/achievement.schema';
import {
  CharacterSchema,
  CHARACTER_MODEL,
} from './schemas/character.schema';
import {
  CharacterRelationshipSchema,
  CHARACTER_RELATIONSHIP_MODEL,
} from './schemas/character-relationship.schema';
import {
  LeaderboardSchema,
  LEADERBOARD_MODEL,
} from './schemas/leaderboard.schema';
import {
  PressureEventSchema,
  PRESSURE_EVENT_MODEL,
} from './schemas/pressure-event.schema';
import {
  ChallengeSchema,
  CHALLENGE_MODEL,
} from './schemas/challenge.schema';

// Gateways
import { GamificationGateway } from './gateways/gamification.gateway';

// Jobs
import { LeaderboardUpdateJob } from './jobs/leaderboard-update.job';

// External modules
import { AuthModule } from '../auth/auth.module';
import { CacheModule } from '@nestjs/cache-manager';
import { QUEUES } from '../queue/constants';

@Module({
  imports: [
    // Mongoose schemas
    MongooseModule.forFeature([
      { name: USER_GAMIFICATION_MODEL, schema: UserGamificationSchema },
      { name: ACHIEVEMENT_MODEL, schema: AchievementSchema },
      { name: CHARACTER_MODEL, schema: CharacterSchema },
      { name: CHARACTER_RELATIONSHIP_MODEL, schema: CharacterRelationshipSchema },
      { name: LEADERBOARD_MODEL, schema: LeaderboardSchema },
      { name: PRESSURE_EVENT_MODEL, schema: PressureEventSchema },
      { name: CHALLENGE_MODEL, schema: ChallengeSchema },
    ]),

    // Bull queues for background jobs
    BullModule.registerQueue({
      name: QUEUES.GAMIFICATION,
    }),

    // Cache for performance
    CacheModule.register({
      ttl: 300, // 5 minutes default TTL
    }),

    // Auth module for user context
    forwardRef(() => AuthModule),
  ],

  controllers: [
    GamificationController,
    AchievementsController,
    LeaderboardsController,
    CharactersController,
  ],

  providers: [
    // Core services
    GamificationService,
    AchievementService,
    RelationshipService,
    LeaderboardService,
    CharacterService,
    PressureEventService,

    // Engines
    AchievementEngine,
    LevelEngine,

    // WebSocket gateway
    GamificationGateway,

    // Background jobs
    LeaderboardUpdateJob,
  ],

  exports: [
    // Export services for use in other modules
    GamificationService,
    AchievementService,
    RelationshipService,
    LeaderboardService,
    CharacterService,
    PressureEventService,
    AchievementEngine,
    LevelEngine,
  ],
})
export class GamificationModule {}

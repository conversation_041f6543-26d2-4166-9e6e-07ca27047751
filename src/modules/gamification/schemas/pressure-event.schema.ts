import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type PressureEventDocument = PressureEvent & Document;

export interface EventTemplate {
  message: string;
  intensity: 'low' | 'medium' | 'high';
  variables: string[];
}

export interface EventImpact {
  userStress: number; // -1 to 1
  timeMultiplier: number; // 0.5 to 2.0
  leverageChange: number; // -1 to 1
  aiAggressiveness: number; // -1 to 1
}

export interface TriggerConditions {
  minRound: number;
  maxRound: number;
  sessionStatus: string;
  timeRemaining?: { min?: number; max?: number };
  userScore?: { min?: number; max?: number };
  dealGap?: { min?: number; max?: number };
  characterDifficulty?: { min?: number; max?: number };
}

@Schema({
  collection: 'pressure_events',
  timestamps: true,
})
export class PressureEvent {
  @Prop({ required: true, unique: true, index: true })
  id: string;

  @Prop({
    required: true,
    enum: ['stakeholder', 'market', 'time', 'competitor', 'legal', 'financial'],
    index: true,
  })
  type: 'stakeholder' | 'market' | 'time' | 'competitor' | 'legal' | 'financial';

  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  category: string;

  @Prop({ type: [Object], required: true })
  templates: EventTemplate[];

  @Prop({ required: true, type: Object })
  impact: EventImpact;

  @Prop({ required: true, type: Object })
  triggerConditions: TriggerConditions;

  @Prop({ required: true, default: 300 }) // 5 minutes default
  cooldown: number; // seconds before can trigger again

  @Prop({ required: true, min: 0, max: 1, default: 0.3 })
  probability: number; // 0-1 chance when conditions met

  @Prop({ default: true, index: true })
  isActive: boolean;

  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

export const PressureEventSchema = SchemaFactory.createForClass(PressureEvent);

// Indexes for performance
PressureEventSchema.index({ isActive: 1, type: 1 });
PressureEventSchema.index({ 'triggerConditions.minRound': 1, 'triggerConditions.maxRound': 1 });
PressureEventSchema.index({ probability: -1 });

export const PRESSURE_EVENT_MODEL = 'PressureEvent';

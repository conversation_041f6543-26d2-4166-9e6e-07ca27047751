import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type UserGamificationDocument = UserGamification & Document;

export interface LevelInfo {
  current: number;
  title: string;
  currentXP: number;
  totalXP: number;
  xpToNext: number;
}

export interface UserAchievement {
  achievementId: string;
  unlockedAt: Date;
  sessionId?: string;
  metadata?: Record<string, any>;
}

export interface UserStatistics {
  totalSessions: number;
  completedSessions: number;
  averageScore: number;
  winRate: number;
  currentStreak: number;
  bestStreak: number;
  totalXPEarned: number;
  achievementsCount: number;
  totalTimeSpent: number; // in minutes
  favoriteStrategy?: string;
}

export interface UserPreferences {
  difficulty: 'BEGINNER' | 'INTERMEDIATE' | 'EXPERT';
  favoriteCharacters: string[];
  preferredStrategies: string[];
  notificationsEnabled: boolean;
  autoAdvanceDifficulty: boolean;
}

export interface UnlockedContent {
  characters: string[];
  scenarios: string[];
  features: string[];
  achievements: string[];
}

@Schema({
  collection: 'user_gamification',
  timestamps: true,
})
export class UserGamification {
  @Prop({ required: true, index: true })
  userId: string;

  @Prop({ required: true, index: true })
  organizationId: string;

  @Prop({
    required: true,
    type: Object,
    default: () => ({
      current: 1,
      title: 'Rookie Negotiator',
      currentXP: 0,
      totalXP: 0,
      xpToNext: 500,
    }),
  })
  level: LevelInfo;

  @Prop({ type: [Object], default: [] })
  achievements: UserAchievement[];

  @Prop({
    required: true,
    type: Object,
    default: () => ({
      totalSessions: 0,
      completedSessions: 0,
      averageScore: 0,
      winRate: 0,
      currentStreak: 0,
      bestStreak: 0,
      totalXPEarned: 0,
      achievementsCount: 0,
      totalTimeSpent: 0,
    }),
  })
  statistics: UserStatistics;

  @Prop({
    type: Object,
    default: () => ({
      difficulty: 'BEGINNER',
      favoriteCharacters: [],
      preferredStrategies: [],
      notificationsEnabled: true,
      autoAdvanceDifficulty: false,
    }),
  })
  preferences: UserPreferences;

  @Prop({
    type: Object,
    default: () => ({
      characters: [],
      scenarios: [],
      features: [],
      achievements: [],
    }),
  })
  unlockedContent: UnlockedContent;

  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>;

  @Prop({ default: Date.now })
  lastActiveAt: Date;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

export const UserGamificationSchema = SchemaFactory.createForClass(UserGamification);

// Indexes for performance
UserGamificationSchema.index({ userId: 1 }, { unique: true });
UserGamificationSchema.index({ organizationId: 1, 'level.current': -1 });
UserGamificationSchema.index({ organizationId: 1, 'statistics.averageScore': -1 });
UserGamificationSchema.index({ organizationId: 1, 'statistics.totalXPEarned': -1 });
UserGamificationSchema.index({ 'achievements.achievementId': 1 });
UserGamificationSchema.index({ lastActiveAt: -1 });

export const USER_GAMIFICATION_MODEL = 'UserGamification';

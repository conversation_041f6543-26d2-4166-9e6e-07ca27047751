import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type LeaderboardDocument = Leaderboard & Document;

export interface LeaderboardPeriod {
  start: Date;
  end: Date;
  year: number;
  week?: number;
  month?: number;
}

export interface LeaderboardRanking {
  rank: number;
  userId: string;
  score: number;
  totalDeals: number;
  winRate: number;
  averageRounds: number;
  totalXP: number;
  metadata: {
    name: string;
    title?: string;
    organization?: string;
    avatar?: string;
  };
}

@Schema({
  collection: 'leaderboards',
  timestamps: true,
})
export class Leaderboard {
  @Prop({
    required: true,
    enum: ['weekly', 'monthly', 'all_time', 'organization', 'industry'],
    index: true,
  })
  type: 'weekly' | 'monthly' | 'all_time' | 'organization' | 'industry';

  @Prop({
    required: true,
    enum: ['global', 'organization', 'industry', 'team'],
    index: true,
  })
  scope: 'global' | 'organization' | 'industry' | 'team';

  @Prop({ type: String, default: null, index: true })
  scopeId?: string; // organizationId, industryId, teamId

  @Prop({ required: true, type: Object })
  period: LeaderboardPeriod;

  @Prop({ type: [Object], default: [] })
  rankings: LeaderboardRanking[];

  @Prop({ required: true, default: 0 })
  totalParticipants: number;

  @Prop({ required: true, default: Date.now, index: true })
  lastUpdated: Date;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

export const LeaderboardSchema = SchemaFactory.createForClass(Leaderboard);

// Indexes for performance
LeaderboardSchema.index({ type: 1, scope: 1, scopeId: 1, 'period.start': 1 });
LeaderboardSchema.index({ lastUpdated: -1 });
LeaderboardSchema.index({ type: 1, scope: 1, scopeId: 1 }, { unique: true });

export const LEADERBOARD_MODEL = 'Leaderboard';

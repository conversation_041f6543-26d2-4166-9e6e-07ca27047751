import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type CharacterDocument = Character & Document;

export interface AIPersonalityProfile {
  aggressiveness: number; // 0-1
  flexibility: number; // 0-1
  riskTolerance: number; // 0-1
  communicationStyle: 'ANALYTICAL' | 'EMOTIONAL' | 'DIRECT' | 'DIPLOMATIC';
  decisionSpeed: 'FAST' | 'MODERATE' | 'SLOW';
  concessionPattern: 'EARLY' | 'GRADUAL' | 'LATE' | 'STRATEGIC';
}

export interface UnlockRequirements {
  level?: number;
  achievements?: string[];
  sessionsCompleted?: number;
  totalXP?: number;
  organizationTier?: string;
}

export interface BehaviorPatterns {
  openingStrategy: string;
  concessionTriggers: string[];
  relationshipFactors: string[];
  weaknesses: string[];
  strengths: string[];
}

export interface CharacterStatistics {
  totalSessions: number;
  averageUserSatisfaction: number;
  averageAISatisfaction: number;
  winRate: number; // Percentage of successful negotiations
  popularityScore: number;
  lastUsedAt?: Date;
}

@Schema({
  collection: 'characters',
  timestamps: true,
})
export class Character {
  @Prop({ required: true, unique: true, index: true })
  id: string;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  company: string;

  @Prop({ required: true })
  avatar: string; // URL or path to avatar image

  @Prop({ required: true, min: 1, max: 5, index: true })
  difficulty: number; // 1-5 scale

  @Prop({ type: [String], default: [], index: true })
  specialties: string[]; // e.g., ['cost_reduction', 'data_driven', 'relationship_focused']

  @Prop({ required: true, type: Object })
  personality: AIPersonalityProfile;

  @Prop({ required: true })
  backstory: string;

  @Prop({ type: Object, default: {} })
  unlockRequirements: UnlockRequirements;

  @Prop({ required: true, type: Object })
  behaviorPatterns: BehaviorPatterns;

  @Prop({
    type: Object,
    default: () => ({
      totalSessions: 0,
      averageUserSatisfaction: 0,
      averageAISatisfaction: 0,
      winRate: 0,
      popularityScore: 0,
    }),
  })
  statistics: CharacterStatistics;

  @Prop({ default: true, index: true })
  isActive: boolean;

  @Prop({ default: false })
  isDefault: boolean; // Default character for new users

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

export const CharacterSchema = SchemaFactory.createForClass(Character);

// Indexes for performance
CharacterSchema.index({ difficulty: 1, isActive: 1 });
CharacterSchema.index({ specialties: 1 });
CharacterSchema.index({ 'unlockRequirements.level': 1 });
CharacterSchema.index({ isDefault: 1 });
CharacterSchema.index({ 'statistics.popularityScore': -1 });
CharacterSchema.index({ tags: 1 });

export const CHARACTER_MODEL = 'Character';

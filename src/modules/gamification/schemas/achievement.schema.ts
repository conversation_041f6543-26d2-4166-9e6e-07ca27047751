import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type AchievementDocument = Achievement & Document;

export interface AchievementRequirements {
  type: 'session_completion' | 'performance_streak' | 'total_sessions' | 'relationship_milestone' | 'level_based' | 'time_based';
  conditions: Record<string, any>;
}

export interface AchievementRewards {
  xp: number;
  credits?: number;
  unlocks?: string[];
  badge?: string;
  title?: string;
}

export interface AchievementStatistics {
  totalUnlocked: number;
  unlockRate: number;
  averageTimeToUnlock?: number; // in days
  lastUnlockedAt?: Date;
}

@Schema({
  collection: 'achievements',
  timestamps: true,
})
export class Achievement {
  @Prop({ required: true, unique: true, index: true })
  id: string;

  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true })
  badge: string; // Emoji or icon identifier

  @Prop({
    required: true,
    enum: ['common', 'rare', 'epic', 'legendary'],
    index: true,
  })
  rarity: 'common' | 'rare' | 'epic' | 'legendary';

  @Prop({
    required: true,
    enum: ['efficiency', 'strategy', 'relationship', 'persistence', 'mastery', 'collaboration'],
    index: true,
  })
  category: 'efficiency' | 'strategy' | 'relationship' | 'persistence' | 'mastery' | 'collaboration';

  @Prop({ required: true, type: Object })
  requirements: AchievementRequirements;

  @Prop({ required: true, type: Object })
  rewards: AchievementRewards;

  @Prop({
    type: Object,
    default: () => ({
      totalUnlocked: 0,
      unlockRate: 0,
    }),
  })
  statistics: AchievementStatistics;

  @Prop({ default: true, index: true })
  isActive: boolean;

  @Prop({ default: false })
  isHidden: boolean; // Hidden achievements not shown until unlocked

  @Prop({ type: [String], default: [] })
  prerequisites: string[]; // Other achievement IDs required

  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

export const AchievementSchema = SchemaFactory.createForClass(Achievement);

// Indexes for performance
AchievementSchema.index({ isActive: 1, category: 1 });
AchievementSchema.index({ rarity: 1, isActive: 1 });
AchievementSchema.index({ 'requirements.type': 1 });
AchievementSchema.index({ isHidden: 1 });

export const ACHIEVEMENT_MODEL = 'Achievement';

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type CharacterRelationshipDocument = CharacterRelationship & Document;

export interface RelationshipLevel {
  respectLevel: number; // 0-100
  trustLevel: number;   // 0-100
  relationshipStatus: 'stranger' | 'acquaintance' | 'professional_respect' | 'trusted_partner' | 'close_ally';
  totalInteractions: number;
  lastInteraction: Date;
}

export interface InteractionHistory {
  sessionId: string;
  outcome: 'win_win' | 'user_win' | 'ai_win' | 'no_deal';
  userApproach: string;
  aiSatisfaction: number;
  userSatisfaction: number;
  respectChange: number;
  trustChange: number;
  date: Date;
}

export interface RelationshipBonuses {
  betterStartingTerms: boolean;
  increasedFlexibility: number; // 0-1 multiplier
  insiderInformation: boolean;
  relationshipDiscount: number; // 0-1 discount percentage
  fasterDecisions: boolean;
}

@Schema({
  collection: 'character_relationships',
  timestamps: true,
})
export class CharacterRelationship {
  @Prop({ required: true, index: true })
  userId: string;

  @Prop({ required: true, index: true })
  characterId: string;

  @Prop({
    required: true,
    type: Object,
    default: () => ({
      respectLevel: 0,
      trustLevel: 0,
      relationshipStatus: 'stranger',
      totalInteractions: 0,
      lastInteraction: new Date(),
    }),
  })
  relationship: RelationshipLevel;

  @Prop({ type: [Object], default: [] })
  interactionHistory: InteractionHistory[];

  @Prop({
    type: Object,
    default: () => ({
      betterStartingTerms: false,
      increasedFlexibility: 0,
      insiderInformation: false,
      relationshipDiscount: 0,
      fasterDecisions: false,
    }),
  })
  bonuses: RelationshipBonuses;

  @Prop({ type: String, default: '' })
  notes: string;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

export const CharacterRelationshipSchema = SchemaFactory.createForClass(CharacterRelationship);

// Indexes for performance
CharacterRelationshipSchema.index({ userId: 1, characterId: 1 }, { unique: true });
CharacterRelationshipSchema.index({ userId: 1 });
CharacterRelationshipSchema.index({ characterId: 1 });
CharacterRelationshipSchema.index({ 'relationship.respectLevel': -1 });
CharacterRelationshipSchema.index({ 'relationship.trustLevel': -1 });

export const CHARACTER_RELATIONSHIP_MODEL = 'CharacterRelationship';

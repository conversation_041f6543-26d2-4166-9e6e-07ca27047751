import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type ChallengeDocument = Challenge & Document;

export interface ChallengeTimeframe {
  startDate: Date;
  endDate: Date;
  duration: number; // days
}

export interface ChallengeRules {
  metric: 'average_score' | 'total_sessions' | 'win_rate' | 'average_cost_savings' | 'speed';
  minSessions: number;
  eligibleScenarios?: string[];
  teamSize?: { min: number; max: number };
  difficultyLevel?: number[];
}

export interface ChallengeRewards {
  first: { xp: number; credits: number; badge: string };
  top10?: { xp: number; credits: number; badge: string };
  participation: { xp: number; credits: number };
}

export interface ChallengeParticipant {
  userId: string;
  teamId?: string;
  joinedAt: Date;
  currentScore: number;
  sessionsCompleted: number;
  rank: number;
  metadata?: Record<string, any>;
}

export interface ChallengeLeaderboard {
  teamId?: string;
  teamName?: string;
  userId?: string;
  userName?: string;
  score: number;
  participants?: number;
  sessionsCompleted: number;
  rank: number;
}

@Schema({
  collection: 'challenges',
  timestamps: true,
})
export class Challenge {
  @Prop({ required: true, unique: true, index: true })
  id: string;

  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  description: string;

  @Prop({
    required: true,
    enum: ['individual', 'team', 'organization'],
    index: true,
  })
  type: 'individual' | 'team' | 'organization';

  @Prop({
    required: true,
    enum: ['upcoming', 'active', 'completed', 'cancelled'],
    index: true,
  })
  status: 'upcoming' | 'active' | 'completed' | 'cancelled';

  @Prop({ required: true, type: Object })
  timeframe: ChallengeTimeframe;

  @Prop({ required: true, type: Object })
  rules: ChallengeRules;

  @Prop({ required: true, type: Object })
  rewards: ChallengeRewards;

  @Prop({ type: [Object], default: [] })
  participants: ChallengeParticipant[];

  @Prop({ type: [Object], default: [] })
  leaderboard: ChallengeLeaderboard[];

  @Prop({ type: String, default: null })
  organizationId?: string; // For organization-specific challenges

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({ default: true })
  isPublic: boolean;

  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>;

  @Prop({ default: Date.now })
  createdAt: Date;

  @Prop({ default: Date.now })
  updatedAt: Date;
}

export const ChallengeSchema = SchemaFactory.createForClass(Challenge);

// Indexes for performance
ChallengeSchema.index({ status: 1, 'timeframe.endDate': 1 });
ChallengeSchema.index({ type: 1, status: 1 });
ChallengeSchema.index({ organizationId: 1, status: 1 });
ChallengeSchema.index({ 'timeframe.startDate': 1, 'timeframe.endDate': 1 });

export const CHALLENGE_MODEL = 'Challenge';

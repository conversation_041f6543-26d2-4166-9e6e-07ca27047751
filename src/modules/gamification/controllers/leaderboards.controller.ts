import {
  Controller,
  Get,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { LeaderboardService } from '../services/leaderboard.service';

@ApiTags('Leaderboards')
@Controller('gamification/leaderboards')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class LeaderboardsController {
  private readonly logger = new Logger(LeaderboardsController.name);

  constructor(
    private readonly tenantContextService: TenantContextService,
    private readonly leaderboardService: LeaderboardService,
  ) {}

  private getCurrentUserContext() {
    const userId = this.tenantContextService.getCurrentUserId();
    const organizationId = this.tenantContextService.getCurrentOrganization();

    if (!userId || !organizationId) {
      throw new Error('User context not available');
    }

    return { userId, organizationId };
  }

  @Get()
  @ApiOperation({ summary: 'Get leaderboard' })
  @ApiQuery({ name: 'type', required: false, description: 'Leaderboard type (weekly, monthly, all_time)' })
  @ApiQuery({ name: 'scope', required: false, description: 'Leaderboard scope (global, organization)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of results to return' })
  @ApiResponse({
    status: 200,
    description: 'Leaderboard retrieved successfully',
  })
  async getLeaderboard(
    @Query('type') type: string = 'weekly',
    @Query('scope') scope: string = 'organization',
    @Query('limit') limit: string = '50'
  ) {
    const { userId, organizationId } = this.getCurrentUserContext();

    const scopeId = scope === 'organization' ? organizationId : undefined;
    const leaderboard = await this.leaderboardService.getLeaderboard(
      type,
      scope,
      scopeId,
      parseInt(limit)
    );

    if (!leaderboard) {
      return {
        rankings: [],
        userRank: null,
        totalParticipants: 0,
        period: null,
        lastUpdated: null,
      };
    }

    // Get user's rank
    const userRank = await this.leaderboardService.getUserRank(
      userId,
      type,
      scope,
      scopeId
    );

    return {
      rankings: leaderboard.rankings,
      userRank,
      totalParticipants: leaderboard.totalParticipants,
      period: leaderboard.period,
      lastUpdated: leaderboard.lastUpdated,
      type: leaderboard.type,
      scope: leaderboard.scope,
    };
  }

  @Get('around-user')
  @ApiOperation({ summary: 'Get leaderboard around user position' })
  @ApiQuery({ name: 'type', required: false, description: 'Leaderboard type' })
  @ApiQuery({ name: 'scope', required: false, description: 'Leaderboard scope' })
  @ApiQuery({ name: 'range', required: false, description: 'Number of positions above/below user' })
  @ApiResponse({
    status: 200,
    description: 'Leaderboard around user retrieved successfully',
  })
  async getLeaderboardAroundUser(
    @Query('type') type: string = 'weekly',
    @Query('scope') scope: string = 'organization',
    @Query('range') range: string = '10'
  ) {
    const { userId, organizationId } = this.getCurrentUserContext();

    const scopeId = scope === 'organization' ? organizationId : undefined;
    const result = await this.leaderboardService.getLeaderboardAroundUser(
      userId,
      type,
      scope,
      scopeId,
      parseInt(range)
    );

    return {
      rankings: result.rankings,
      userRank: result.userRank,
      type,
      scope,
      range: parseInt(range),
    };
  }

  @Get('user-rank')
  @ApiOperation({ summary: 'Get user rank in leaderboard' })
  @ApiQuery({ name: 'type', required: false, description: 'Leaderboard type' })
  @ApiQuery({ name: 'scope', required: false, description: 'Leaderboard scope' })
  @ApiResponse({
    status: 200,
    description: 'User rank retrieved successfully',
  })
  async getUserRank(
    @Query('type') type: string = 'weekly',
    @Query('scope') scope: string = 'organization'
  ) {
    const { userId, organizationId } = this.getCurrentUserContext();

    const scopeId = scope === 'organization' ? organizationId : undefined;
    const rank = await this.leaderboardService.getUserRank(
      userId,
      type,
      scope,
      scopeId
    );

    return {
      rank,
      type,
      scope,
      userId,
    };
  }

  @Get('global')
  @ApiOperation({ summary: 'Get global leaderboard' })
  @ApiQuery({ name: 'type', required: false, description: 'Leaderboard type' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of results' })
  @ApiResponse({
    status: 200,
    description: 'Global leaderboard retrieved successfully',
  })
  async getGlobalLeaderboard(
    @Query('type') type: string = 'weekly',
    @Query('limit') limit: string = '100'
  ) {
    const { userId } = this.getCurrentUserContext();

    const leaderboard = await this.leaderboardService.getLeaderboard(
      type,
      'global',
      undefined,
      parseInt(limit)
    );

    if (!leaderboard) {
      return {
        rankings: [],
        userRank: null,
        totalParticipants: 0,
      };
    }

    // Get user's global rank
    const userRank = await this.leaderboardService.getUserRank(
      userId,
      type,
      'global'
    );

    return {
      rankings: leaderboard.rankings,
      userRank,
      totalParticipants: leaderboard.totalParticipants,
      period: leaderboard.period,
      lastUpdated: leaderboard.lastUpdated,
    };
  }

  @Get('organization')
  @ApiOperation({ summary: 'Get organization leaderboard' })
  @ApiQuery({ name: 'type', required: false, description: 'Leaderboard type' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of results' })
  @ApiResponse({
    status: 200,
    description: 'Organization leaderboard retrieved successfully',
  })
  async getOrganizationLeaderboard(
    @Query('type') type: string = 'weekly',
    @Query('limit') limit: string = '50'
  ) {
    const { userId, organizationId } = this.getCurrentUserContext();

    const leaderboard = await this.leaderboardService.getLeaderboard(
      type,
      'organization',
      organizationId,
      parseInt(limit)
    );

    if (!leaderboard) {
      return {
        rankings: [],
        userRank: null,
        totalParticipants: 0,
      };
    }

    // Get user's organization rank
    const userRank = await this.leaderboardService.getUserRank(
      userId,
      type,
      'organization',
      organizationId
    );

    return {
      rankings: leaderboard.rankings,
      userRank,
      totalParticipants: leaderboard.totalParticipants,
      period: leaderboard.period,
      lastUpdated: leaderboard.lastUpdated,
    };
  }

  @Get('summary')
  @ApiOperation({ summary: 'Get leaderboard summary for user' })
  @ApiResponse({
    status: 200,
    description: 'Leaderboard summary retrieved successfully',
  })
  async getLeaderboardSummary() {
    const { userId, organizationId } = this.getCurrentUserContext();

    const [
      weeklyGlobalRank,
      weeklyOrgRank,
      monthlyGlobalRank,
      monthlyOrgRank,
      allTimeGlobalRank,
      allTimeOrgRank,
    ] = await Promise.all([
      this.leaderboardService.getUserRank(userId, 'weekly', 'global'),
      this.leaderboardService.getUserRank(userId, 'weekly', 'organization', organizationId),
      this.leaderboardService.getUserRank(userId, 'monthly', 'global'),
      this.leaderboardService.getUserRank(userId, 'monthly', 'organization', organizationId),
      this.leaderboardService.getUserRank(userId, 'all_time', 'global'),
      this.leaderboardService.getUserRank(userId, 'all_time', 'organization', organizationId),
    ]);

    return {
      weekly: {
        global: weeklyGlobalRank,
        organization: weeklyOrgRank,
      },
      monthly: {
        global: monthlyGlobalRank,
        organization: monthlyOrgRank,
      },
      allTime: {
        global: allTimeGlobalRank,
        organization: allTimeOrgRank,
      },
    };
  }
}

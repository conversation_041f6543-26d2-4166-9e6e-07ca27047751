import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { GamificationService } from '../services/gamification.service';
import { AchievementService } from '../services/achievement.service';
import { CharacterService } from '../services/character.service';
import { RelationshipService } from '../services/relationship.service';

@ApiTags('Gamification')
@Controller('gamification')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class GamificationController {
  private readonly logger = new Logger(GamificationController.name);

  constructor(
    private readonly tenantContextService: TenantContextService,
    private readonly gamificationService: GamificationService,
    private readonly achievementService: AchievementService,
    private readonly characterService: CharacterService,
    private readonly relationshipService: RelationshipService,
  ) {}

  private getCurrentUserContext() {
    const context = this.tenantContextService.getCurrentTenant();
    const userId = this.tenantContextService.getCurrentUserId();
    const organizationId = this.tenantContextService.getCurrentOrganization();

    if (!userId || !organizationId) {
      throw new Error('User context not available');
    }

    return { userId, organizationId };
  }

  @Get('profile')
  @ApiOperation({ summary: 'Get user gamification profile' })
  @ApiResponse({
    status: 200,
    description: 'User gamification profile retrieved successfully',
  })
  async getUserProfile() {
    const { userId, organizationId } = this.getCurrentUserContext();

    const [
      gamificationProfile,
      achievementProgress,
      relationships,
    ] = await Promise.all([
      this.gamificationService.getUserGamification(userId, organizationId),
      this.achievementService.getUserAchievementProgress(userId),
      this.relationshipService.getUserRelationships(userId),
    ]);

    return {
      profile: gamificationProfile,
      achievements: achievementProgress,
      relationships: relationships.map(rel => ({
        characterId: rel.characterId,
        respectLevel: rel.relationship.respectLevel,
        trustLevel: rel.relationship.trustLevel,
        status: rel.relationship.relationshipStatus,
        totalInteractions: rel.relationship.totalInteractions,
        bonuses: rel.bonuses,
      })),
    };
  }

  @Get('profile/:userId')
  @ApiOperation({ summary: 'Get another user\'s gamification profile (public info only)' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiResponse({
    status: 200,
    description: 'Public gamification profile retrieved successfully',
  })
  async getPublicProfile(@Param('userId') targetUserId: string) {
    const { organizationId } = this.getCurrentUserContext();

    const gamificationProfile = await this.gamificationService.getUserGamification(
      targetUserId,
      organizationId
    );

    // Return only public information
    return {
      level: gamificationProfile.level,
      statistics: {
        totalSessions: gamificationProfile.statistics.totalSessions,
        completedSessions: gamificationProfile.statistics.completedSessions,
        averageScore: gamificationProfile.statistics.averageScore,
        achievementsCount: gamificationProfile.statistics.achievementsCount,
      },
      achievements: gamificationProfile.achievements.map(a => ({
        achievementId: a.achievementId,
        unlockedAt: a.unlockedAt,
      })),
    };
  }

  @Post('experience')
  @ApiOperation({ summary: 'Award experience points (admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Experience awarded successfully',
  })
  async awardExperience(
    @Body() data: {
      userId?: string;
      amount: number;
      source: string;
      metadata?: Record<string, any>;
    }
  ) {
    const { userId: currentUserId } = this.getCurrentUserContext();
    const targetUserId = data.userId || currentUserId;

    const levelUpdate = await this.gamificationService.awardExperience(
      targetUserId,
      data.amount,
      data.source,
      data.metadata
    );

    return {
      success: true,
      levelUpdate,
    };
  }

  @Get('characters')
  @ApiOperation({ summary: 'Get available characters for user' })
  @ApiQuery({ name: 'difficulty', required: false, description: 'Filter by difficulty level' })
  @ApiQuery({ name: 'unlocked', required: false, description: 'Filter by unlock status' })
  @ApiResponse({
    status: 200,
    description: 'Characters retrieved successfully',
  })
  async getCharacters(
    @Query('difficulty') difficulty?: string,
    @Query('unlocked') unlocked?: string
  ) {
    const { userId } = this.getCurrentUserContext();

    const [allCharacters, userGamification] = await Promise.all([
      this.characterService.getAllCharacters(),
      this.gamificationService.getUserGamification(userId),
    ]);

    let filteredCharacters = allCharacters;

    // Filter by difficulty if specified
    if (difficulty) {
      const difficultyLevel = parseInt(difficulty);
      filteredCharacters = filteredCharacters.filter(c => c.difficulty <= difficultyLevel);
    }

    // Add unlock status and filter if specified
    const charactersWithStatus = filteredCharacters.map(character => {
      const isUnlocked = userGamification.unlockedContent.characters.includes(character.id);
      
      return {
        ...character,
        unlocked: isUnlocked,
        canUnlock: !isUnlocked ? this.checkCharacterUnlockRequirements(
          character,
          userGamification
        ) : true,
      };
    });

    // Filter by unlock status if specified
    if (unlocked !== undefined) {
      const showUnlocked = unlocked === 'true';
      return charactersWithStatus.filter(c => c.unlocked === showUnlocked);
    }

    return charactersWithStatus;
  }

  @Get('characters/:characterId')
  @ApiOperation({ summary: 'Get character details' })
  @ApiParam({ name: 'characterId', description: 'Character ID' })
  @ApiResponse({
    status: 200,
    description: 'Character details retrieved successfully',
  })
  async getCharacterDetails(@Param('characterId') characterId: string) {
    const { userId } = this.getCurrentUserContext();

    const [character, relationship, userGamification] = await Promise.all([
      this.characterService.getCharacter(characterId),
      this.relationshipService.getRelationship(userId, characterId),
      this.gamificationService.getUserGamification(userId),
    ]);

    const isUnlocked = userGamification.unlockedContent.characters.includes(characterId);

    return {
      ...character,
      unlocked: isUnlocked,
      relationship: relationship ? {
        respectLevel: relationship.relationship.respectLevel,
        trustLevel: relationship.relationship.trustLevel,
        status: relationship.relationship.relationshipStatus,
        totalInteractions: relationship.relationship.totalInteractions,
        bonuses: relationship.bonuses,
        recentInteractions: relationship.interactionHistory.slice(-5),
      } : null,
    };
  }

  @Post('characters/:characterId/unlock')
  @ApiOperation({ summary: 'Unlock a character' })
  @ApiParam({ name: 'characterId', description: 'Character ID' })
  @ApiResponse({
    status: 200,
    description: 'Character unlocked successfully',
  })
  async unlockCharacter(@Param('characterId') characterId: string) {
    const { userId } = this.getCurrentUserContext();

    const [character, userGamification] = await Promise.all([
      this.characterService.getCharacter(characterId),
      this.gamificationService.getUserGamification(userId),
    ]);

    // Check if already unlocked
    if (userGamification.unlockedContent.characters.includes(characterId)) {
      return { success: true, message: 'Character already unlocked' };
    }

    // Check unlock requirements
    const canUnlock = this.checkCharacterUnlockRequirements(character, userGamification);
    if (!canUnlock) {
      return { 
        success: false, 
        message: 'Character unlock requirements not met',
        requirements: character.unlockRequirements,
      };
    }

    // Unlock character
    userGamification.unlockedContent.characters.push(characterId);
    await this.gamificationService.updateStatistics(userId, {});

    return { success: true, message: 'Character unlocked successfully' };
  }

  @Get('leaderboard')
  @ApiOperation({ summary: 'Get gamification leaderboard' })
  @ApiQuery({ name: 'scope', required: false, description: 'Leaderboard scope (global, organization)' })
  @ApiQuery({ name: 'metric', required: false, description: 'Metric to rank by (level, xp, score)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of results to return' })
  @ApiResponse({
    status: 200,
    description: 'Leaderboard retrieved successfully',
  })
  async getLeaderboard(
    @Query('scope') scope: string = 'organization',
    @Query('metric') metric: string = 'level',
    @Query('limit') limit: string = '50'
  ) {
    const { userId, organizationId } = this.getCurrentUserContext();

    // This is a simplified implementation
    // In a real system, you'd have a dedicated leaderboard service
    return {
      scope,
      metric,
      rankings: [],
      userRank: null,
      totalParticipants: 0,
    };
  }

  /**
   * Check if character unlock requirements are met
   */
  private checkCharacterUnlockRequirements(character: any, userGamification: any): boolean {
    const requirements = character.unlockRequirements;

    // Check level requirement
    if (requirements.level && userGamification.level.current < requirements.level) {
      return false;
    }

    // Check achievement requirements
    if (requirements.achievements && requirements.achievements.length > 0) {
      const userAchievements = userGamification.achievements.map(a => a.achievementId);
      const hasRequiredAchievements = requirements.achievements.every(
        achievementId => userAchievements.includes(achievementId)
      );
      if (!hasRequiredAchievements) {
        return false;
      }
    }

    // Check sessions completed requirement
    if (requirements.sessionsCompleted && 
        userGamification.statistics.completedSessions < requirements.sessionsCompleted) {
      return false;
    }

    return true;
  }
}

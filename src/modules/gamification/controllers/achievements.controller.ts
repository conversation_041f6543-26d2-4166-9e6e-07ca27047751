import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { AchievementService } from '../services/achievement.service';
import { GamificationService } from '../services/gamification.service';

@ApiTags('Achievements')
@Controller('gamification/achievements')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AchievementsController {
  private readonly logger = new Logger(AchievementsController.name);

  constructor(
    private readonly tenantContextService: TenantContextService,
    private readonly achievementService: AchievementService,
    private readonly gamificationService: GamificationService,
  ) {}

  private getCurrentUserContext() {
    const userId = this.tenantContextService.getCurrentUserId();
    const organizationId = this.tenantContextService.getCurrentOrganization();

    if (!userId || !organizationId) {
      throw new Error('User context not available');
    }

    return { userId, organizationId };
  }

  @Get()
  @ApiOperation({ summary: 'Get all achievements with user progress' })
  @ApiQuery({ name: 'category', required: false, description: 'Filter by category' })
  @ApiQuery({ name: 'rarity', required: false, description: 'Filter by rarity' })
  @ApiQuery({ name: 'unlocked', required: false, description: 'Filter by unlock status' })
  @ApiResponse({
    status: 200,
    description: 'Achievements retrieved successfully',
  })
  async getAchievements(
    @Query('category') category?: string,
    @Query('rarity') rarity?: string,
    @Query('unlocked') unlocked?: string
  ) {
    const { userId } = this.getCurrentUserContext();

    let achievements = await this.achievementService.getActiveAchievements();
    const userGamification = await this.gamificationService.getUserGamification(userId);

    // Filter by category
    if (category) {
      achievements = achievements.filter(a => a.category === category);
    }

    // Filter by rarity
    if (rarity) {
      achievements = achievements.filter(a => a.rarity === rarity);
    }

    // Add user progress and filter by unlock status
    const achievementsWithProgress = achievements.map(achievement => {
      const userAchievement = userGamification.achievements.find(
        a => a.achievementId === achievement.id
      );

      const isUnlocked = !!userAchievement;
      const progress = this.achievementService.calculateAchievementProgress(
        achievement,
        userGamification.statistics
      );

      return {
        ...achievement,
        unlocked: isUnlocked,
        unlockedAt: userAchievement?.unlockedAt,
        progress,
        progressPercentage: Math.round(progress * 100),
      };
    });

    // Filter by unlock status if specified
    let filteredAchievements = achievementsWithProgress;
    if (unlocked !== undefined) {
      const showUnlocked = unlocked === 'true';
      filteredAchievements = achievementsWithProgress.filter(a => a.unlocked === showUnlocked);
    }

    return {
      achievements: filteredAchievements,
      summary: {
        total: achievements.length,
        unlocked: userGamification.achievements.length,
        completionRate: userGamification.achievements.length / achievements.length,
        categories: this.getAchievementCategories(achievements),
        rarities: this.getAchievementRarities(achievements),
      },
    };
  }

  @Get('categories')
  @ApiOperation({ summary: 'Get achievement categories with counts' })
  @ApiResponse({
    status: 200,
    description: 'Achievement categories retrieved successfully',
  })
  async getCategories() {
    const achievements = await this.achievementService.getActiveAchievements();
    const categories = this.getAchievementCategories(achievements);

    return { categories };
  }

  @Get('leaderboard/:achievementId')
  @ApiOperation({ summary: 'Get achievement leaderboard' })
  @ApiParam({ name: 'achievementId', description: 'Achievement ID' })
  @ApiQuery({ name: 'limit', required: false, description: 'Number of results' })
  @ApiResponse({
    status: 200,
    description: 'Achievement leaderboard retrieved successfully',
  })
  async getAchievementLeaderboard(
    @Param('achievementId') achievementId: string,
    @Query('limit') limit: string = '50'
  ) {
    const leaderboard = await this.achievementService.getAchievementLeaderboard(
      achievementId,
      parseInt(limit)
    );

    return { leaderboard };
  }

  @Get('rare')
  @ApiOperation({ summary: 'Get rare achievements (epic and legendary)' })
  @ApiResponse({
    status: 200,
    description: 'Rare achievements retrieved successfully',
  })
  async getRareAchievements() {
    const { userId } = this.getCurrentUserContext();

    const rareAchievements = await this.achievementService.getRareAchievements();
    const userGamification = await this.gamificationService.getUserGamification(userId);

    const achievementsWithProgress = rareAchievements.map(achievement => {
      const userAchievement = userGamification.achievements.find(
        a => a.achievementId === achievement.id
      );

      return {
        ...achievement,
        unlocked: !!userAchievement,
        unlockedAt: userAchievement?.unlockedAt,
        progress: this.achievementService.calculateAchievementProgress(
          achievement,
          userGamification.statistics
        ),
      };
    });

    return {
      achievements: achievementsWithProgress,
      summary: {
        total: rareAchievements.length,
        unlocked: achievementsWithProgress.filter(a => a.unlocked).length,
      },
    };
  }

  @Get(':achievementId')
  @ApiOperation({ summary: 'Get achievement details' })
  @ApiParam({ name: 'achievementId', description: 'Achievement ID' })
  @ApiResponse({
    status: 200,
    description: 'Achievement details retrieved successfully',
  })
  async getAchievementDetails(@Param('achievementId') achievementId: string) {
    const { userId } = this.getCurrentUserContext();

    const [achievement, userGamification] = await Promise.all([
      this.achievementService.getAchievement(achievementId),
      this.gamificationService.getUserGamification(userId),
    ]);

    if (!achievement) {
      return { error: 'Achievement not found' };
    }

    const userAchievement = userGamification.achievements.find(
      a => a.achievementId === achievementId
    );

    const progress = this.achievementService.calculateAchievementProgress(
      achievement,
      userGamification.statistics
    );

    return {
      ...achievement,
      unlocked: !!userAchievement,
      unlockedAt: userAchievement?.unlockedAt,
      progress,
      progressPercentage: Math.round(progress * 100),
      requirements: this.formatRequirements(achievement.requirements),
    };
  }

  @Post('seed')
  @ApiOperation({ summary: 'Seed initial achievements (admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Achievements seeded successfully',
  })
  async seedAchievements() {
    await this.achievementService.seedAchievements();
    return { success: true, message: 'Achievements seeded successfully' };
  }

  /**
   * Get achievement categories with counts
   */
  private getAchievementCategories(achievements: any[]): Record<string, number> {
    return achievements.reduce((acc, achievement) => {
      acc[achievement.category] = (acc[achievement.category] || 0) + 1;
      return acc;
    }, {});
  }

  /**
   * Get achievement rarities with counts
   */
  private getAchievementRarities(achievements: any[]): Record<string, number> {
    return achievements.reduce((acc, achievement) => {
      acc[achievement.rarity] = (acc[achievement.rarity] || 0) + 1;
      return acc;
    }, {});
  }

  /**
   * Format achievement requirements for display
   */
  private formatRequirements(requirements: any): string {
    switch (requirements.type) {
      case 'session_completion':
        return `Complete a session with specific conditions`;
      case 'total_sessions':
        return `Complete ${requirements.conditions.minSessions} sessions`;
      case 'performance_streak':
        return `Achieve ${requirements.conditions.minStreak} wins in a row`;
      case 'level_based':
        return `Reach level ${requirements.conditions.level}`;
      default:
        return 'Special requirements';
    }
  }
}

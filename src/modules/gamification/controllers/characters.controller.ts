import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { CharacterService } from '../services/character.service';
import { RelationshipService } from '../services/relationship.service';
import { GamificationService } from '../services/gamification.service';

@ApiTags('Characters')
@Controller('gamification/characters')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class CharactersController {
  private readonly logger = new Logger(CharactersController.name);

  constructor(
    private readonly tenantContextService: TenantContextService,
    private readonly characterService: CharacterService,
    private readonly relationshipService: RelationshipService,
    private readonly gamificationService: GamificationService,
  ) {}

  private getCurrentUserContext() {
    const userId = this.tenantContextService.getCurrentUserId();
    const organizationId = this.tenantContextService.getCurrentOrganization();

    if (!userId || !organizationId) {
      throw new Error('User context not available');
    }

    return { userId, organizationId };
  }

  @Get()
  @ApiOperation({ summary: 'Get all characters with unlock status' })
  @ApiQuery({ name: 'difficulty', required: false, description: 'Filter by max difficulty level' })
  @ApiQuery({ name: 'unlocked', required: false, description: 'Filter by unlock status' })
  @ApiQuery({ name: 'specialty', required: false, description: 'Filter by specialty' })
  @ApiResponse({
    status: 200,
    description: 'Characters retrieved successfully',
  })
  async getCharacters(
    @Query('difficulty') difficulty?: string,
    @Query('unlocked') unlocked?: string,
    @Query('specialty') specialty?: string
  ) {
    const { userId } = this.getCurrentUserContext();

    const [allCharacters, userGamification] = await Promise.all([
      this.characterService.getAllCharacters(),
      this.gamificationService.getUserGamification(userId),
    ]);

    let filteredCharacters = allCharacters;

    // Filter by difficulty if specified
    if (difficulty) {
      const maxDifficulty = parseInt(difficulty);
      filteredCharacters = filteredCharacters.filter(c => c.difficulty <= maxDifficulty);
    }

    // Filter by specialty if specified
    if (specialty) {
      filteredCharacters = filteredCharacters.filter(c => 
        c.specialties.includes(specialty)
      );
    }

    // Add unlock status and relationships
    const charactersWithStatus = await Promise.all(
      filteredCharacters.map(async character => {
        const isUnlocked = userGamification.unlockedContent.characters.includes(character.id);
        const relationship = await this.relationshipService.getRelationship(userId, character.id);
        
        const canUnlock = !isUnlocked ? await this.characterService.checkUnlockRequirements(
          character.id,
          userGamification.level.current,
          userGamification.achievements.map(a => a.achievementId),
          userGamification.statistics.completedSessions
        ) : true;

        return {
          ...character,
          unlocked: isUnlocked,
          canUnlock,
          relationship: relationship ? {
            respectLevel: relationship.relationship.respectLevel,
            trustLevel: relationship.relationship.trustLevel,
            status: relationship.relationship.relationshipStatus,
            totalInteractions: relationship.relationship.totalInteractions,
            bonuses: relationship.bonuses,
          } : null,
        };
      })
    );

    // Filter by unlock status if specified
    let finalCharacters = charactersWithStatus;
    if (unlocked !== undefined) {
      const showUnlocked = unlocked === 'true';
      finalCharacters = charactersWithStatus.filter(c => c.unlocked === showUnlocked);
    }

    return {
      characters: finalCharacters,
      summary: {
        total: allCharacters.length,
        unlocked: userGamification.unlockedContent.characters.length,
        available: charactersWithStatus.filter(c => c.canUnlock).length,
        difficulties: this.getCharacterDifficulties(allCharacters),
        specialties: this.getCharacterSpecialties(allCharacters),
      },
    };
  }

  @Get('unlocked')
  @ApiOperation({ summary: 'Get user\'s unlocked characters' })
  @ApiResponse({
    status: 200,
    description: 'Unlocked characters retrieved successfully',
  })
  async getUnlockedCharacters() {
    const { userId } = this.getCurrentUserContext();

    const userGamification = await this.gamificationService.getUserGamification(userId);
    const unlockedCharacters = await this.characterService.getUnlockedCharacters(
      userGamification.unlockedContent.characters
    );

    // Add relationship data
    const charactersWithRelationships = await Promise.all(
      unlockedCharacters.map(async character => {
        const relationship = await this.relationshipService.getRelationship(userId, character.id);
        
        return {
          ...character,
          relationship: relationship ? {
            respectLevel: relationship.relationship.respectLevel,
            trustLevel: relationship.relationship.trustLevel,
            status: relationship.relationship.relationshipStatus,
            totalInteractions: relationship.relationship.totalInteractions,
            bonuses: relationship.bonuses,
            recentInteractions: relationship.interactionHistory.slice(-3),
          } : null,
        };
      })
    );

    return {
      characters: charactersWithRelationships,
      count: charactersWithRelationships.length,
    };
  }

  @Get('default')
  @ApiOperation({ summary: 'Get default character' })
  @ApiResponse({
    status: 200,
    description: 'Default character retrieved successfully',
  })
  async getDefaultCharacter() {
    const character = await this.characterService.getDefaultCharacter();
    return { character };
  }

  @Get('difficulty/:level')
  @ApiOperation({ summary: 'Get characters by difficulty level' })
  @ApiParam({ name: 'level', description: 'Difficulty level (1-5)' })
  @ApiResponse({
    status: 200,
    description: 'Characters by difficulty retrieved successfully',
  })
  async getCharactersByDifficulty(@Param('level') level: string) {
    const { userId } = this.getCurrentUserContext();

    const difficultyLevel = parseInt(level);
    const [characters, userGamification] = await Promise.all([
      this.characterService.getCharactersByDifficulty(difficultyLevel),
      this.gamificationService.getUserGamification(userId),
    ]);

    const charactersWithStatus = characters.map(character => {
      const isUnlocked = userGamification.unlockedContent.characters.includes(character.id);
      
      return {
        ...character,
        unlocked: isUnlocked,
      };
    });

    return {
      characters: charactersWithStatus,
      difficulty: difficultyLevel,
      count: charactersWithStatus.length,
    };
  }

  @Get(':characterId')
  @ApiOperation({ summary: 'Get character details with relationship' })
  @ApiParam({ name: 'characterId', description: 'Character ID' })
  @ApiResponse({
    status: 200,
    description: 'Character details retrieved successfully',
  })
  async getCharacterDetails(@Param('characterId') characterId: string) {
    const { userId } = this.getCurrentUserContext();

    const [character, relationship, userGamification] = await Promise.all([
      this.characterService.getCharacter(characterId),
      this.relationshipService.getRelationship(userId, characterId),
      this.gamificationService.getUserGamification(userId),
    ]);

    const isUnlocked = userGamification.unlockedContent.characters.includes(characterId);
    const canUnlock = !isUnlocked ? await this.characterService.checkUnlockRequirements(
      characterId,
      userGamification.level.current,
      userGamification.achievements.map(a => a.achievementId),
      userGamification.statistics.completedSessions
    ) : true;

    return {
      ...character,
      unlocked: isUnlocked,
      canUnlock,
      unlockRequirements: character.unlockRequirements,
      relationship: relationship ? {
        respectLevel: relationship.relationship.respectLevel,
        trustLevel: relationship.relationship.trustLevel,
        status: relationship.relationship.relationshipStatus,
        totalInteractions: relationship.relationship.totalInteractions,
        bonuses: relationship.bonuses,
        interactionHistory: relationship.interactionHistory.slice(-10),
      } : null,
    };
  }

  @Post(':characterId/unlock')
  @ApiOperation({ summary: 'Unlock a character' })
  @ApiParam({ name: 'characterId', description: 'Character ID' })
  @ApiResponse({
    status: 200,
    description: 'Character unlock attempt result',
  })
  async unlockCharacter(@Param('characterId') characterId: string) {
    const { userId } = this.getCurrentUserContext();

    const [character, userGamification] = await Promise.all([
      this.characterService.getCharacter(characterId),
      this.gamificationService.getUserGamification(userId),
    ]);

    // Check if already unlocked
    if (userGamification.unlockedContent.characters.includes(characterId)) {
      return { 
        success: true, 
        message: 'Character already unlocked',
        character: character,
      };
    }

    // Check unlock requirements
    const canUnlock = await this.characterService.checkUnlockRequirements(
      characterId,
      userGamification.level.current,
      userGamification.achievements.map(a => a.achievementId),
      userGamification.statistics.completedSessions
    );

    if (!canUnlock) {
      return { 
        success: false, 
        message: 'Character unlock requirements not met',
        requirements: character.unlockRequirements,
        userProgress: {
          level: userGamification.level.current,
          achievements: userGamification.achievements.length,
          sessionsCompleted: userGamification.statistics.completedSessions,
        },
      };
    }

    // Unlock character by updating user gamification
    userGamification.unlockedContent.characters.push(characterId);
    // Note: In a real implementation, you'd save this through the gamification service

    return { 
      success: true, 
      message: 'Character unlocked successfully',
      character: character,
    };
  }

  @Post('seed')
  @ApiOperation({ summary: 'Seed initial characters (admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Characters seeded successfully',
  })
  async seedCharacters() {
    await this.characterService.seedCharacters();
    return { success: true, message: 'Characters seeded successfully' };
  }

  /**
   * Get character difficulties with counts
   */
  private getCharacterDifficulties(characters: any[]): Record<number, number> {
    return characters.reduce((acc, character) => {
      acc[character.difficulty] = (acc[character.difficulty] || 0) + 1;
      return acc;
    }, {});
  }

  /**
   * Get character specialties with counts
   */
  private getCharacterSpecialties(characters: any[]): Record<string, number> {
    const specialties = {};
    characters.forEach(character => {
      character.specialties.forEach(specialty => {
        specialties[specialty] = (specialties[specialty] || 0) + 1;
      });
    });
    return specialties;
  }
}

import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { PressureEventService } from '../services/pressure-event.service';
import { GamificationService } from '../services/gamification.service';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  organizationId?: string;
}

@WebSocketGateway({
  namespace: '/gamification',
  cors: {
    origin: '*',
    credentials: true,
  },
})
export class GamificationGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(GamificationGateway.name);
  private connectedUsers: Map<string, AuthenticatedSocket> = new Map();

  constructor(
    private readonly pressureEventService: PressureEventService,
    private readonly gamificationService: GamificationService,
  ) {}

  async handleConnection(client: AuthenticatedSocket) {
    try {
      // Extract user info from token (simplified - in real app, validate JWT)
      const token = client.handshake.auth?.token || client.handshake.headers?.authorization;
      if (!token) {
        client.disconnect();
        return;
      }

      // In a real implementation, you'd validate the JWT token here
      // For now, we'll simulate user extraction
      client.userId = 'user_' + Math.random().toString(36).substr(2, 9);
      client.organizationId = 'org_' + Math.random().toString(36).substr(2, 9);

      this.connectedUsers.set(client.id, client);
      this.logger.log(`User ${client.userId} connected to gamification gateway`);

      // Send initial data
      await this.sendInitialData(client);
    } catch (error) {
      this.logger.error(`Connection error: ${error.message}`);
      client.disconnect();
    }
  }

  handleDisconnect(client: AuthenticatedSocket) {
    this.connectedUsers.delete(client.id);
    this.logger.log(`User ${client.userId} disconnected from gamification gateway`);
  }

  @SubscribeMessage('join_session')
  async handleJoinSession(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { sessionId: string }
  ) {
    try {
      await client.join(`session:${data.sessionId}`);
      
      // Send current game state
      const activeEvents = this.pressureEventService.getActiveEvents(data.sessionId);
      client.emit('game_state_update', {
        sessionId: data.sessionId,
        activeEvents,
        timestamp: new Date(),
      });

      this.logger.log(`User ${client.userId} joined session ${data.sessionId}`);
    } catch (error) {
      this.logger.error(`Error joining session: ${error.message}`);
      client.emit('error', { message: 'Failed to join session' });
    }
  }

  @SubscribeMessage('leave_session')
  async handleLeaveSession(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { sessionId: string }
  ) {
    await client.leave(`session:${data.sessionId}`);
    this.logger.log(`User ${client.userId} left session ${data.sessionId}`);
  }

  @SubscribeMessage('request_live_score')
  async handleLiveScore(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { sessionId: string; moveData: any }
  ) {
    try {
      // Calculate live score (simplified)
      const liveScore = this.calculateLiveScore(data.moveData);
      
      client.emit('live_score_update', {
        sessionId: data.sessionId,
        score: liveScore,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(`Error calculating live score: ${error.message}`);
    }
  }

  @SubscribeMessage('request_hints')
  async handleHintRequest(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { sessionId: string; context: any }
  ) {
    try {
      // Generate hints based on current context
      const hints = this.generateHints(data.context);
      
      client.emit('hints_update', {
        sessionId: data.sessionId,
        hints,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(`Error generating hints: ${error.message}`);
    }
  }

  /**
   * Broadcast pressure event to session participants
   */
  async broadcastPressureEvent(sessionId: string, event: any) {
    this.server.to(`session:${sessionId}`).emit('pressure_event', {
      sessionId,
      event,
      timestamp: new Date(),
    });

    this.logger.log(`Broadcasted pressure event ${event.id} to session ${sessionId}`);
  }

  /**
   * Broadcast achievement unlock to user
   */
  async broadcastAchievement(userId: string, achievement: any) {
    // Find user's socket
    const userSocket = Array.from(this.connectedUsers.values()).find(
      socket => socket.userId === userId
    );

    if (userSocket) {
      userSocket.emit('achievement_unlocked', {
        achievement,
        timestamp: new Date(),
      });

      this.logger.log(`Broadcasted achievement ${achievement.id} to user ${userId}`);
    }
  }

  /**
   * Broadcast level up to user
   */
  async broadcastLevelUp(userId: string, levelData: any) {
    const userSocket = Array.from(this.connectedUsers.values()).find(
      socket => socket.userId === userId
    );

    if (userSocket) {
      userSocket.emit('level_up', {
        ...levelData,
        timestamp: new Date(),
      });

      this.logger.log(`Broadcasted level up to user ${userId}: Level ${levelData.newLevel}`);
    }
  }

  /**
   * Broadcast relationship update
   */
  async broadcastRelationshipUpdate(userId: string, relationshipData: any) {
    const userSocket = Array.from(this.connectedUsers.values()).find(
      socket => socket.userId === userId
    );

    if (userSocket) {
      userSocket.emit('relationship_update', {
        ...relationshipData,
        timestamp: new Date(),
      });
    }
  }

  /**
   * Send initial data to connected user
   */
  private async sendInitialData(client: AuthenticatedSocket) {
    try {
      if (!client.userId) return;

      const gamificationProfile = await this.gamificationService.getUserGamification(
        client.userId,
        client.organizationId
      );

      client.emit('initial_data', {
        profile: {
          level: gamificationProfile.level,
          statistics: gamificationProfile.statistics,
          recentAchievements: gamificationProfile.achievements.slice(-5),
        },
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(`Error sending initial data: ${error.message}`);
    }
  }

  /**
   * Calculate live score (simplified implementation)
   */
  private calculateLiveScore(moveData: any): number {
    // This is a simplified calculation
    // In a real implementation, this would be more sophisticated
    const baseScore = 5.0;
    const randomVariation = (Math.random() - 0.5) * 2; // -1 to 1
    return Math.max(0, Math.min(10, baseScore + randomVariation));
  }

  /**
   * Generate hints based on context
   */
  private generateHints(context: any): string[] {
    const hints = [
      "Consider the other party's perspective and interests",
      "Look for win-win opportunities",
      "Don't rush - take time to think through your response",
      "Ask clarifying questions to better understand their position",
      "Focus on value creation rather than just price",
    ];

    // Return 2-3 random hints
    const shuffled = hints.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.floor(Math.random() * 2) + 2);
  }
}

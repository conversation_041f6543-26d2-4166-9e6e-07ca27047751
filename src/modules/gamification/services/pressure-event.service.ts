import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  PressureEvent,
  PressureEventDocument,
  PRESSURE_EVENT_MODEL,
  EventImpact,
} from '../schemas/pressure-event.schema';

export interface ActivePressureEvent {
  id: string;
  type: string;
  title: string;
  message: string;
  intensity: string;
  impact: EventImpact;
  triggeredAt: Date;
  expiresAt: Date;
}

@Injectable()
export class PressureEventService {
  private readonly logger = new Logger(PressureEventService.name);
  private activePressureEvents: Map<string, ActivePressureEvent[]> = new Map();

  constructor(
    @InjectModel(PRESSURE_EVENT_MODEL)
    private readonly pressureEventModel: Model<PressureEventDocument>,
  ) {}

  /**
   * Check and trigger pressure events for a session
   */
  async checkTriggers(sessionId: string, sessionData: any): Promise<ActivePressureEvent[]> {
    const triggeredEvents: ActivePressureEvent[] = [];
    
    // Get all active pressure events
    const events = await this.pressureEventModel.find({ isActive: true }).exec();
    
    for (const event of events) {
      if (this.shouldTriggerEvent(event, sessionData)) {
        const triggeredEvent = await this.triggerEvent(sessionId, event, sessionData);
        if (triggeredEvent) {
          triggeredEvents.push(triggeredEvent);
        }
      }
    }
    
    return triggeredEvents;
  }

  /**
   * Get active pressure events for a session
   */
  getActiveEvents(sessionId: string): ActivePressureEvent[] {
    return this.activePressureEvents.get(sessionId) || [];
  }

  /**
   * Schedule initial events for a session
   */
  async scheduleInitialEvents(sessionId: string): Promise<void> {
    // Initialize empty array for this session
    this.activePressureEvents.set(sessionId, []);
    
    // Schedule cleanup after 2 hours
    setTimeout(() => {
      this.activePressureEvents.delete(sessionId);
    }, 2 * 60 * 60 * 1000);
  }

  /**
   * Apply pressure event impacts to session
   */
  applyEventImpacts(sessionData: any, activeEvents: ActivePressureEvent[]): any {
    let modifiedSession = { ...sessionData };
    
    for (const event of activeEvents) {
      const impact = event.impact;
      
      // Apply stress changes
      if (modifiedSession.gameState) {
        modifiedSession.gameState.userStress = Math.max(0, Math.min(1, 
          modifiedSession.gameState.userStress + impact.userStress
        ));
        
        // Apply AI aggressiveness changes
        if (modifiedSession.aiPersonality) {
          modifiedSession.aiPersonality.aggressiveness = Math.max(0, Math.min(1,
            modifiedSession.aiPersonality.aggressiveness + impact.aiAggressiveness
          ));
        }
      }
    }
    
    return modifiedSession;
  }

  /**
   * Create a new pressure event
   */
  async createPressureEvent(eventData: Partial<PressureEvent>): Promise<PressureEvent> {
    const event = new this.pressureEventModel(eventData);
    return event.save();
  }

  /**
   * Seed initial pressure events
   */
  async seedPressureEvents(): Promise<void> {
    const existingCount = await this.pressureEventModel.countDocuments().exec();
    if (existingCount > 0) {
      this.logger.log('Pressure events already seeded');
      return;
    }

    const events = [
      {
        id: 'ceo_deadline',
        type: 'stakeholder',
        title: 'CEO Deadline Pressure',
        category: 'executive_pressure',
        templates: [
          {
            message: 'Your CEO just called - they need this deal closed by end of day for the quarterly report.',
            intensity: 'high',
            variables: ['deadline_time']
          }
        ],
        impact: {
          userStress: 0.3,
          timeMultiplier: 1.5,
          leverageChange: -0.2,
          aiAggressiveness: 0.1
        },
        triggerConditions: {
          minRound: 3,
          maxRound: 8,
          sessionStatus: 'active',
          timeRemaining: { min: 300, max: 1800 }
        },
        cooldown: 600,
        probability: 0.25,
        isActive: true
      },
      {
        id: 'competitor_threat',
        type: 'competitor',
        title: 'Competitor Threat',
        category: 'market_pressure',
        templates: [
          {
            message: 'Word just came in that your main competitor is also bidding for this contract.',
            intensity: 'medium',
            variables: ['competitor_name']
          }
        ],
        impact: {
          userStress: 0.2,
          timeMultiplier: 1.2,
          leverageChange: -0.3,
          aiAggressiveness: 0.2
        },
        triggerConditions: {
          minRound: 2,
          maxRound: 6,
          sessionStatus: 'active',
          userScore: { min: 6.0, max: 8.0 }
        },
        cooldown: 900,
        probability: 0.3,
        isActive: true
      },
      {
        id: 'budget_cut',
        type: 'financial',
        title: 'Budget Reduction',
        category: 'financial_pressure',
        templates: [
          {
            message: 'Finance just informed you that the budget for this project has been cut by 15%.',
            intensity: 'high',
            variables: ['cut_percentage']
          }
        ],
        impact: {
          userStress: 0.25,
          timeMultiplier: 1.0,
          leverageChange: -0.4,
          aiAggressiveness: -0.1
        },
        triggerConditions: {
          minRound: 4,
          maxRound: 10,
          sessionStatus: 'active',
          characterDifficulty: { min: 2, max: 5 }
        },
        cooldown: 1200,
        probability: 0.2,
        isActive: true
      },
      {
        id: 'legal_concern',
        type: 'legal',
        title: 'Legal Compliance Issue',
        category: 'regulatory_pressure',
        templates: [
          {
            message: 'Legal has raised concerns about compliance requirements that may affect the terms.',
            intensity: 'medium',
            variables: ['compliance_type']
          }
        ],
        impact: {
          userStress: 0.15,
          timeMultiplier: 0.8,
          leverageChange: 0.1,
          aiAggressiveness: -0.2
        },
        triggerConditions: {
          minRound: 5,
          maxRound: 12,
          sessionStatus: 'active',
          dealGap: { min: 0.3, max: 0.7 }
        },
        cooldown: 800,
        probability: 0.15,
        isActive: true
      }
    ];

    await this.pressureEventModel.insertMany(events);
    this.logger.log(`Seeded ${events.length} pressure events`);
  }

  /**
   * Check if event should trigger
   */
  private shouldTriggerEvent(event: PressureEvent, sessionData: any): boolean {
    const conditions = event.triggerConditions;
    const currentRound = sessionData.currentRound || 0;
    
    // Check round conditions
    if (currentRound < conditions.minRound || currentRound > conditions.maxRound) {
      return false;
    }
    
    // Check session status
    if (sessionData.status !== conditions.sessionStatus) {
      return false;
    }
    
    // Check time remaining
    if (conditions.timeRemaining) {
      const timeRemaining = sessionData.gameState?.timeRemaining || 0;
      if (conditions.timeRemaining.min && timeRemaining < conditions.timeRemaining.min) {
        return false;
      }
      if (conditions.timeRemaining.max && timeRemaining > conditions.timeRemaining.max) {
        return false;
      }
    }
    
    // Check user score
    if (conditions.userScore) {
      const score = sessionData.gameState?.currentScore || 0;
      if (conditions.userScore.min && score < conditions.userScore.min) {
        return false;
      }
      if (conditions.userScore.max && score > conditions.userScore.max) {
        return false;
      }
    }
    
    // Check probability
    return Math.random() < event.probability;
  }

  /**
   * Trigger a pressure event
   */
  private async triggerEvent(
    sessionId: string,
    event: PressureEvent,
    sessionData: any
  ): Promise<ActivePressureEvent | null> {
    // Check cooldown
    const activeEvents = this.getActiveEvents(sessionId);
    const recentEvent = activeEvents.find(e => 
      e.id === event.id && 
      Date.now() - e.triggeredAt.getTime() < event.cooldown * 1000
    );
    
    if (recentEvent) {
      return null;
    }
    
    // Select random template
    const template = event.templates[Math.floor(Math.random() * event.templates.length)];
    
    // Create active event
    const activeEvent: ActivePressureEvent = {
      id: event.id,
      type: event.type,
      title: event.title,
      message: template.message,
      intensity: template.intensity,
      impact: event.impact,
      triggeredAt: new Date(),
      expiresAt: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes
    };
    
    // Add to active events
    const sessionEvents = this.getActiveEvents(sessionId);
    sessionEvents.push(activeEvent);
    this.activePressureEvents.set(sessionId, sessionEvents);
    
    this.logger.log(`Triggered pressure event ${event.id} for session ${sessionId}`);
    
    return activeEvent;
  }
}

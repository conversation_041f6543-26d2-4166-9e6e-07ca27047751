import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  UserGamification,
  UserGamificationDocument,
  USER_GAMIFICATION_MODEL,
  LevelInfo,
  UserAchievement,
} from '../schemas/user-gamification.schema';
import { LevelEngine } from '../engines/level.engine';

export interface LevelUpdate {
  previousLevel: number;
  newLevel: number;
  xpGained: number;
  totalXP: number;
  leveledUp: boolean;
  newUnlocks: string[];
  title: string;
}

export interface XPSource {
  source: string;
  amount: number;
  metadata?: Record<string, any>;
}

@Injectable()
export class GamificationService {
  private readonly logger = new Logger(GamificationService.name);

  constructor(
    @InjectModel(USER_GAMIFICATION_MODEL)
    private readonly userGamificationModel: Model<UserGamificationDocument>,
    private readonly levelEngine: LevelEngine,
  ) {}

  /**
   * Get or create user gamification profile
   */
  async getUserGamification(userId: string, organizationId?: string): Promise<UserGamification> {
    let userGamification = await this.userGamificationModel.findOne({ userId }).exec();

    if (!userGamification) {
      // Create new gamification profile
      userGamification = new this.userGamificationModel({
        userId,
        organizationId,
        level: {
          current: 1,
          title: 'Rookie Negotiator',
          currentXP: 0,
          totalXP: 0,
          xpToNext: 500,
        },
        achievements: [],
        statistics: {
          totalSessions: 0,
          completedSessions: 0,
          averageScore: 0,
          winRate: 0,
          currentStreak: 0,
          bestStreak: 0,
          totalXPEarned: 0,
          achievementsCount: 0,
          totalTimeSpent: 0,
        },
        preferences: {
          difficulty: 'BEGINNER',
          favoriteCharacters: [],
          preferredStrategies: [],
          notificationsEnabled: true,
          autoAdvanceDifficulty: false,
        },
        unlockedContent: {
          characters: ['default_character'], // Start with default character
          scenarios: ['basic_contract'],
          features: [],
          achievements: [],
        },
        lastActiveAt: new Date(),
      });

      await userGamification.save();
      this.logger.log(`Created new gamification profile for user: ${userId}`);
    }

    return userGamification.toObject() as UserGamification;
  }

  /**
   * Award experience points to user
   */
  async awardExperience(
    userId: string,
    amount: number,
    source: string,
    metadata?: Record<string, any>,
  ): Promise<LevelUpdate> {
    const userGamification = await this.userGamificationModel.findOne({ userId }).exec();
    if (!userGamification) {
      throw new NotFoundException('User gamification profile not found');
    }

    const previousLevel = userGamification.level.current;
    const newTotalXP = userGamification.level.totalXP + amount;

    // Calculate new level
    const levelInfo = this.levelEngine.calculateLevel(newTotalXP);

    const levelUpdate: LevelUpdate = {
      previousLevel,
      newLevel: levelInfo.current,
      xpGained: amount,
      totalXP: newTotalXP,
      leveledUp: levelInfo.current > previousLevel,
      newUnlocks: [],
      title: levelInfo.title,
    };

    // Update user gamification
    userGamification.level = levelInfo;
    userGamification.statistics.totalXPEarned = newTotalXP;
    userGamification.lastActiveAt = new Date();

    // If leveled up, check for new unlocks
    if (levelUpdate.leveledUp) {
      levelUpdate.newUnlocks = await this.getNewUnlocks(previousLevel, levelInfo.current);
      
      // Update unlocked content
      for (const unlock of levelUpdate.newUnlocks) {
        if (unlock.startsWith('character_')) {
          const characterId = unlock.replace('character_', '');
          if (!userGamification.unlockedContent.characters.includes(characterId)) {
            userGamification.unlockedContent.characters.push(characterId);
          }
        } else if (unlock.startsWith('scenario_')) {
          const scenarioId = unlock.replace('scenario_', '');
          if (!userGamification.unlockedContent.scenarios.includes(scenarioId)) {
            userGamification.unlockedContent.scenarios.push(scenarioId);
          }
        } else if (unlock.startsWith('feature_')) {
          const featureId = unlock.replace('feature_', '');
          if (!userGamification.unlockedContent.features.includes(featureId)) {
            userGamification.unlockedContent.features.push(featureId);
          }
        }
      }

      this.logger.log(`User ${userId} leveled up from ${previousLevel} to ${levelInfo.current}`);
    }

    await userGamification.save();

    // Log XP award
    this.logger.log(`Awarded ${amount} XP to user ${userId} for ${source}`);

    return levelUpdate;
  }

  /**
   * Add achievement to user
   */
  async awardAchievement(
    userId: string,
    achievementId: string,
    sessionId?: string,
    metadata?: Record<string, any>,
  ): Promise<void> {
    const userGamification = await this.userGamificationModel.findOne({ userId }).exec();
    if (!userGamification) {
      throw new NotFoundException('User gamification profile not found');
    }

    // Check if already unlocked
    const existingAchievement = userGamification.achievements.find(
      (a) => a.achievementId === achievementId,
    );

    if (existingAchievement) {
      this.logger.warn(`Achievement ${achievementId} already unlocked for user ${userId}`);
      return;
    }

    // Add achievement
    const newAchievement: UserAchievement = {
      achievementId,
      unlockedAt: new Date(),
      sessionId,
      metadata,
    };

    userGamification.achievements.push(newAchievement);
    userGamification.statistics.achievementsCount = userGamification.achievements.length;
    userGamification.unlockedContent.achievements.push(achievementId);
    userGamification.lastActiveAt = new Date();

    await userGamification.save();

    // Note: Achievement rewards XP will be handled by the calling service
    // to avoid circular dependencies

    this.logger.log(`Awarded achievement ${achievementId} to user ${userId}`);
  }

  /**
   * Update user statistics
   */
  async updateStatistics(
    userId: string,
    updates: Partial<UserGamification['statistics']>,
  ): Promise<void> {
    await this.userGamificationModel.updateOne(
      { userId },
      {
        $set: {
          ...Object.keys(updates).reduce((acc, key) => {
            acc[`statistics.${key}`] = updates[key];
            return acc;
          }, {}),
          lastActiveAt: new Date(),
        },
      },
    ).exec();
  }

  /**
   * Get level-based unlocks
   */
  private async getNewUnlocks(fromLevel: number, toLevel: number): Promise<string[]> {
    const unlocks: string[] = [];

    // Define level-based unlocks
    const levelUnlocks = {
      2: ['character_sarah_chen'],
      3: ['scenario_advanced_software', 'feature_hints'],
      4: ['character_marcus_rodriguez'],
      5: ['feature_team_challenges'],
      6: ['character_jennifer_liu', 'scenario_partnership_deals'],
      7: ['feature_campaign_mode'],
      8: ['character_expert_negotiator'],
    };

    for (let level = fromLevel + 1; level <= toLevel; level++) {
      if (levelUnlocks[level]) {
        unlocks.push(...levelUnlocks[level]);
      }
    }

    return unlocks;
  }

  /**
   * Check if user has unlocked content
   */
  async hasUnlockedContent(userId: string, contentType: string, contentId: string): Promise<boolean> {
    const userGamification = await this.getUserGamification(userId);
    
    switch (contentType) {
      case 'character':
        return userGamification.unlockedContent.characters.includes(contentId);
      case 'scenario':
        return userGamification.unlockedContent.scenarios.includes(contentId);
      case 'feature':
        return userGamification.unlockedContent.features.includes(contentId);
      case 'achievement':
        return userGamification.unlockedContent.achievements.includes(contentId);
      default:
        return false;
    }
  }
}

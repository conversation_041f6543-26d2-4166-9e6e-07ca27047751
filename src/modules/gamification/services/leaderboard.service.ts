import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  Leaderboard,
  LeaderboardDocument,
  LEADERBOARD_MODEL,
  LeaderboardRanking,
  LeaderboardPeriod,
} from '../schemas/leaderboard.schema';
import {
  UserGamification,
  UserGamificationDocument,
  USER_GAMIFICATION_MODEL,
} from '../schemas/user-gamification.schema';

@Injectable()
export class LeaderboardService {
  private readonly logger = new Logger(LeaderboardService.name);

  constructor(
    @InjectModel(LEADERBOARD_MODEL)
    private readonly leaderboardModel: Model<LeaderboardDocument>,
    @InjectModel(USER_GAMIFICATION_MODEL)
    private readonly userGamificationModel: Model<UserGamificationDocument>,
  ) {}

  /**
   * Get leaderboard by type and scope
   */
  async getLeaderboard(
    type: string,
    scope: string = 'global',
    scopeId?: string,
    limit: number = 50
  ): Promise<Leaderboard | null> {
    const query: any = { type, scope };
    if (scopeId) query.scopeId = scopeId;

    const leaderboard = await this.leaderboardModel.findOne(query).exec();
    
    if (leaderboard && leaderboard.rankings.length > limit) {
      leaderboard.rankings = leaderboard.rankings.slice(0, limit);
    }

    return leaderboard;
  }

  /**
   * Update global leaderboard
   */
  async updateGlobalLeaderboard(timeframe: string): Promise<void> {
    const period = this.calculatePeriod(timeframe);
    
    // Get user performance data
    const userStats = await this.aggregateUserPerformance(period);
    
    // Create rankings
    const rankings = await this.createRankings(userStats);
    
    // Update or create leaderboard
    await this.leaderboardModel.findOneAndUpdate(
      { type: timeframe, scope: 'global', scopeId: null },
      {
        type: timeframe,
        scope: 'global',
        period,
        rankings,
        totalParticipants: rankings.length,
        lastUpdated: new Date(),
      },
      { upsert: true, new: true }
    ).exec();

    this.logger.log(`Updated global ${timeframe} leaderboard with ${rankings.length} participants`);
  }

  /**
   * Update organization leaderboards
   */
  async updateOrganizationLeaderboards(timeframe: string): Promise<void> {
    // Get all organizations with users
    const organizations = await this.userGamificationModel.distinct('organizationId').exec();
    
    for (const organizationId of organizations) {
      if (!organizationId) continue;
      
      const period = this.calculatePeriod(timeframe);
      
      // Get organization user performance data
      const userStats = await this.aggregateUserPerformance(period, organizationId);
      
      if (userStats.length === 0) continue;
      
      // Create rankings
      const rankings = await this.createRankings(userStats);
      
      // Update or create organization leaderboard
      await this.leaderboardModel.findOneAndUpdate(
        { type: timeframe, scope: 'organization', scopeId: organizationId },
        {
          type: timeframe,
          scope: 'organization',
          scopeId: organizationId,
          period,
          rankings,
          totalParticipants: rankings.length,
          lastUpdated: new Date(),
        },
        { upsert: true, new: true }
      ).exec();
    }

    this.logger.log(`Updated organization ${timeframe} leaderboards for ${organizations.length} organizations`);
  }

  /**
   * Get user's rank in leaderboard
   */
  async getUserRank(
    userId: string,
    type: string,
    scope: string = 'global',
    scopeId?: string
  ): Promise<number | null> {
    const leaderboard = await this.getLeaderboard(type, scope, scopeId);
    
    if (!leaderboard) return null;
    
    const userRanking = leaderboard.rankings.find(r => r.userId === userId);
    return userRanking ? userRanking.rank : null;
  }

  /**
   * Get leaderboard around user's position
   */
  async getLeaderboardAroundUser(
    userId: string,
    type: string,
    scope: string = 'global',
    scopeId?: string,
    range: number = 10
  ): Promise<{ rankings: LeaderboardRanking[]; userRank: number | null }> {
    const leaderboard = await this.getLeaderboard(type, scope, scopeId, 1000);
    
    if (!leaderboard) {
      return { rankings: [], userRank: null };
    }
    
    const userIndex = leaderboard.rankings.findIndex(r => r.userId === userId);
    
    if (userIndex === -1) {
      return { rankings: leaderboard.rankings.slice(0, range * 2), userRank: null };
    }
    
    const start = Math.max(0, userIndex - range);
    const end = Math.min(leaderboard.rankings.length, userIndex + range + 1);
    
    return {
      rankings: leaderboard.rankings.slice(start, end),
      userRank: userIndex + 1,
    };
  }

  /**
   * Calculate period for timeframe
   */
  private calculatePeriod(timeframe: string): LeaderboardPeriod {
    const now = new Date();
    const year = now.getFullYear();
    
    switch (timeframe) {
      case 'weekly':
        const weekStart = new Date(now);
        weekStart.setDate(now.getDate() - now.getDay());
        weekStart.setHours(0, 0, 0, 0);
        
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        weekEnd.setHours(23, 59, 59, 999);
        
        return {
          start: weekStart,
          end: weekEnd,
          year,
          week: this.getWeekNumber(now),
        };
        
      case 'monthly':
        const monthStart = new Date(year, now.getMonth(), 1);
        const monthEnd = new Date(year, now.getMonth() + 1, 0, 23, 59, 59, 999);
        
        return {
          start: monthStart,
          end: monthEnd,
          year,
          month: now.getMonth() + 1,
        };
        
      case 'all_time':
      default:
        return {
          start: new Date(2024, 0, 1), // Start of 2024
          end: now,
          year,
        };
    }
  }

  /**
   * Aggregate user performance data
   */
  private async aggregateUserPerformance(
    period: LeaderboardPeriod,
    organizationId?: string
  ): Promise<any[]> {
    const matchQuery: any = {
      lastActiveAt: { $gte: period.start, $lte: period.end },
    };
    
    if (organizationId) {
      matchQuery.organizationId = organizationId;
    }

    return this.userGamificationModel.aggregate([
      { $match: matchQuery },
      {
        $project: {
          userId: 1,
          organizationId: 1,
          level: 1,
          statistics: 1,
          score: '$statistics.averageScore',
          totalDeals: '$statistics.completedSessions',
          winRate: '$statistics.winRate',
          totalXP: '$statistics.totalXPEarned',
        }
      },
      { $sort: { score: -1, totalXP: -1, totalDeals: -1 } },
      { $limit: 1000 }
    ]).exec();
  }

  /**
   * Create rankings from user stats
   */
  private async createRankings(userStats: any[]): Promise<LeaderboardRanking[]> {
    const rankings: LeaderboardRanking[] = [];
    
    for (let i = 0; i < userStats.length; i++) {
      const user = userStats[i];
      
      rankings.push({
        rank: i + 1,
        userId: user.userId,
        score: user.score || 0,
        totalDeals: user.totalDeals || 0,
        winRate: user.winRate || 0,
        averageRounds: 0, // Would need to calculate from session data
        totalXP: user.totalXP || 0,
        metadata: {
          name: `User ${user.userId.slice(-4)}`, // Simplified - would get from user service
          title: user.level?.title || 'Rookie Negotiator',
          organization: user.organizationId || 'Unknown',
        },
      });
    }
    
    return rankings;
  }

  /**
   * Get week number
   */
  private getWeekNumber(date: Date): number {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  }
}

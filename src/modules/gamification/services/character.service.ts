import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  Character,
  CharacterDocument,
  CHARACTER_MODEL,
} from '../schemas/character.schema';

@Injectable()
export class CharacterService {
  private readonly logger = new Logger(CharacterService.name);

  constructor(
    @InjectModel(CHARACTER_MODEL)
    private readonly characterModel: Model<CharacterDocument>,
  ) {}

  /**
   * Get all active characters
   */
  async getAllCharacters(): Promise<Character[]> {
    return this.characterModel.find({ isActive: true }).sort({ difficulty: 1 }).exec();
  }

  /**
   * Get character by ID
   */
  async getCharacter(characterId: string): Promise<Character> {
    const character = await this.characterModel.findOne({ 
      id: characterId, 
      isActive: true 
    }).exec();

    if (!character) {
      throw new NotFoundException(`Character with ID ${characterId} not found`);
    }

    return character.toObject() as Character;
  }

  /**
   * Get default character for new users
   */
  async getDefaultCharacter(): Promise<Character> {
    const defaultCharacter = await this.characterModel.findOne({ 
      isDefault: true, 
      isActive: true 
    }).exec();

    if (!defaultCharacter) {
      // Fallback to first beginner character
      const beginnerCharacter = await this.characterModel.findOne({ 
        difficulty: 1, 
        isActive: true 
      }).exec();
      
      if (!beginnerCharacter) {
        throw new NotFoundException('No default character available');
      }
      
      return beginnerCharacter.toObject() as Character;
    }

    return defaultCharacter.toObject() as Character;
  }

  /**
   * Get characters by difficulty
   */
  async getCharactersByDifficulty(difficulty: number): Promise<Character[]> {
    return this.characterModel.find({ 
      difficulty, 
      isActive: true 
    }).sort({ name: 1 }).exec();
  }

  /**
   * Get characters that user has unlocked
   */
  async getUnlockedCharacters(unlockedCharacterIds: string[]): Promise<Character[]> {
    return this.characterModel.find({ 
      id: { $in: unlockedCharacterIds }, 
      isActive: true 
    }).sort({ difficulty: 1 }).exec();
  }

  /**
   * Get characters that user hasn't unlocked yet
   */
  async getLockedCharacters(unlockedCharacterIds: string[]): Promise<Character[]> {
    return this.characterModel.find({ 
      id: { $nin: unlockedCharacterIds }, 
      isActive: true 
    }).sort({ difficulty: 1 }).exec();
  }

  /**
   * Check if character meets unlock requirements
   */
  async checkUnlockRequirements(
    characterId: string, 
    userLevel: number, 
    userAchievements: string[], 
    userSessions: number
  ): Promise<boolean> {
    const character = await this.getCharacter(characterId);
    const requirements = character.unlockRequirements;

    // Check level requirement
    if (requirements.level && userLevel < requirements.level) {
      return false;
    }

    // Check achievement requirements
    if (requirements.achievements && requirements.achievements.length > 0) {
      const hasRequiredAchievements = requirements.achievements.every(
        achievementId => userAchievements.includes(achievementId)
      );
      if (!hasRequiredAchievements) {
        return false;
      }
    }

    // Check sessions completed requirement
    if (requirements.sessionsCompleted && userSessions < requirements.sessionsCompleted) {
      return false;
    }

    return true;
  }

  /**
   * Update character statistics
   */
  async updateCharacterStatistics(
    characterId: string, 
    sessionData: {
      userSatisfaction?: number;
      aiSatisfaction?: number;
      wasSuccessful?: boolean;
    }
  ): Promise<void> {
    const character = await this.characterModel.findOne({ id: characterId }).exec();
    if (!character) return;

    // Update statistics
    character.statistics.totalSessions += 1;
    character.statistics.lastUsedAt = new Date();

    if (sessionData.userSatisfaction !== undefined) {
      character.statistics.averageUserSatisfaction = this.calculateNewAverage(
        character.statistics.averageUserSatisfaction,
        character.statistics.totalSessions - 1,
        sessionData.userSatisfaction
      );
    }

    if (sessionData.aiSatisfaction !== undefined) {
      character.statistics.averageAISatisfaction = this.calculateNewAverage(
        character.statistics.averageAISatisfaction,
        character.statistics.totalSessions - 1,
        sessionData.aiSatisfaction
      );
    }

    if (sessionData.wasSuccessful !== undefined) {
      const currentWins = Math.floor(character.statistics.winRate * (character.statistics.totalSessions - 1));
      const newWins = sessionData.wasSuccessful ? currentWins + 1 : currentWins;
      character.statistics.winRate = newWins / character.statistics.totalSessions;
    }

    // Update popularity score (simplified calculation)
    character.statistics.popularityScore = (
      character.statistics.averageUserSatisfaction * 0.4 +
      character.statistics.averageAISatisfaction * 0.3 +
      character.statistics.winRate * 0.3
    );

    await character.save();
  }

  /**
   * Create a new character
   */
  async createCharacter(characterData: Partial<Character>): Promise<Character> {
    const character = new this.characterModel(characterData);
    return character.save();
  }

  /**
   * Seed initial characters
   */
  async seedCharacters(): Promise<void> {
    const existingCount = await this.characterModel.countDocuments().exec();
    if (existingCount > 0) {
      this.logger.log('Characters already seeded');
      return;
    }

    const characters = [
      {
        id: 'default_character',
        name: 'Alex Johnson',
        title: 'Junior Procurement Manager',
        company: 'StartupCorp',
        avatar: '/avatars/alex-johnson.jpg',
        difficulty: 1,
        specialties: ['friendly', 'collaborative'],
        personality: {
          aggressiveness: 0.3,
          flexibility: 0.8,
          riskTolerance: 0.6,
          communicationStyle: 'DIPLOMATIC',
          decisionSpeed: 'MODERATE',
          concessionPattern: 'GRADUAL'
        },
        backstory: 'A friendly and approachable negotiator who values building relationships.',
        unlockRequirements: {},
        behaviorPatterns: {
          openingStrategy: 'relationship_building',
          concessionTriggers: ['good_rapport', 'mutual_benefit'],
          relationshipFactors: ['honesty', 'respect'],
          weaknesses: ['time_pressure'],
          strengths: ['relationship_building', 'creative_solutions']
        },
        isDefault: true,
        isActive: true
      },
      {
        id: 'sarah_chen',
        name: 'Sarah Chen',
        title: 'Senior Sales Director',
        company: 'TechCorp Solutions',
        avatar: '/avatars/sarah-chen.jpg',
        difficulty: 2,
        specialties: ['cost_reduction', 'data_driven'],
        personality: {
          aggressiveness: 0.6,
          flexibility: 0.7,
          riskTolerance: 0.5,
          communicationStyle: 'ANALYTICAL',
          decisionSpeed: 'MODERATE',
          concessionPattern: 'GRADUAL'
        },
        backstory: '15 years in enterprise sales with a focus on data-driven decisions.',
        unlockRequirements: {
          level: 2,
          sessionsCompleted: 3
        },
        behaviorPatterns: {
          openingStrategy: 'analytical_assessment',
          concessionTriggers: ['strong_data', 'competitive_pressure'],
          relationshipFactors: ['professionalism', 'expertise'],
          weaknesses: ['emotional_appeals'],
          strengths: ['analytical_thinking', 'market_knowledge']
        },
        isActive: true
      },
      {
        id: 'marcus_rodriguez',
        name: 'Marcus Rodriguez',
        title: 'Chief Legal Officer',
        company: 'Global Industries',
        avatar: '/avatars/marcus-rodriguez.jpg',
        difficulty: 4,
        specialties: ['legal_expertise', 'risk_management'],
        personality: {
          aggressiveness: 0.8,
          flexibility: 0.4,
          riskTolerance: 0.3,
          communicationStyle: 'DIRECT',
          decisionSpeed: 'SLOW',
          concessionPattern: 'STRATEGIC'
        },
        backstory: 'Experienced legal executive with expertise in complex negotiations.',
        unlockRequirements: {
          level: 4,
          achievements: ['win_win_master'],
          sessionsCompleted: 15
        },
        behaviorPatterns: {
          openingStrategy: 'risk_assessment',
          concessionTriggers: ['legal_precedent', 'regulatory_compliance'],
          relationshipFactors: ['trust', 'competence'],
          weaknesses: ['time_constraints'],
          strengths: ['legal_knowledge', 'strategic_thinking']
        },
        isActive: true
      }
    ];

    await this.characterModel.insertMany(characters);
    this.logger.log(`Seeded ${characters.length} characters`);
  }

  /**
   * Calculate new average
   */
  private calculateNewAverage(currentAverage: number, count: number, newValue: number): number {
    if (count === 0) return newValue;
    return (currentAverage * count + newValue) / (count + 1);
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  CharacterRelationship,
  CharacterRelationshipDocument,
  CHARACTER_RELATIONSHIP_MODEL,
  InteractionHistory,
  RelationshipBonuses,
} from '../schemas/character-relationship.schema';

@Injectable()
export class RelationshipService {
  private readonly logger = new Logger(RelationshipService.name);

  constructor(
    @InjectModel(CHARACTER_RELATIONSHIP_MODEL)
    private readonly relationshipModel: Model<CharacterRelationshipDocument>,
  ) {}

  /**
   * Get or create relationship between user and character
   */
  async getRelationship(userId: string, characterId: string): Promise<CharacterRelationship | null> {
    let relationship = await this.relationshipModel.findOne({ userId, characterId }).exec();

    if (!relationship) {
      // Create new relationship
      relationship = new this.relationshipModel({
        userId,
        characterId,
        relationship: {
          respectLevel: 0,
          trustLevel: 0,
          relationshipStatus: 'stranger',
          totalInteractions: 0,
          lastInteraction: new Date(),
        },
        interactionHistory: [],
        bonuses: {
          betterStartingTerms: false,
          increasedFlexibility: 0,
          insiderInformation: false,
          relationshipDiscount: 0,
          fasterDecisions: false,
        },
        notes: '',
      });

      await relationship.save();
      this.logger.log(`Created new relationship between user ${userId} and character ${characterId}`);
    }

    return relationship.toObject() as CharacterRelationship;
  }

  /**
   * Update relationship based on negotiation outcome
   */
  async updateRelationship(
    userId: string,
    characterId: string,
    userMove: any,
    aiResponse: any,
  ): Promise<void> {
    const relationship = await this.relationshipModel.findOne({ userId, characterId }).exec();
    if (!relationship) {
      this.logger.warn(`No relationship found for user ${userId} and character ${characterId}`);
      return;
    }

    // Calculate relationship changes based on interaction
    const changes = this.calculateRelationshipChanges(userMove, aiResponse);

    // Update relationship levels
    relationship.relationship.respectLevel = Math.max(0, Math.min(100, 
      relationship.relationship.respectLevel + changes.respectChange
    ));
    relationship.relationship.trustLevel = Math.max(0, Math.min(100, 
      relationship.relationship.trustLevel + changes.trustChange
    ));
    relationship.relationship.totalInteractions += 1;
    relationship.relationship.lastInteraction = new Date();

    // Update relationship status
    const newStatus = this.calculateRelationshipStatus(
      relationship.relationship.respectLevel,
      relationship.relationship.trustLevel
    );
    relationship.relationship.relationshipStatus = newStatus as any;

    // Add to interaction history
    const interaction: InteractionHistory = {
      sessionId: userMove.sessionId || 'unknown',
      outcome: this.determineOutcome(userMove, aiResponse) as any,
      userApproach: userMove.strategy || 'unknown',
      aiSatisfaction: aiResponse.confidence || 0.5,
      userSatisfaction: this.calculateUserSatisfaction(userMove, aiResponse),
      respectChange: changes.respectChange,
      trustChange: changes.trustChange,
      date: new Date(),
    };

    relationship.interactionHistory.push(interaction);

    // Keep only last 20 interactions
    if (relationship.interactionHistory.length > 20) {
      relationship.interactionHistory = relationship.interactionHistory.slice(-20);
    }

    // Update bonuses based on new relationship level
    relationship.bonuses = this.calculateBonuses(
      relationship.relationship.respectLevel,
      relationship.relationship.trustLevel,
      relationship.relationship.totalInteractions
    );

    await relationship.save();

    this.logger.log(
      `Updated relationship for user ${userId} and character ${characterId}: ` +
      `Respect: ${relationship.relationship.respectLevel}, Trust: ${relationship.relationship.trustLevel}`
    );
  }

  /**
   * Get all relationships for a user
   */
  async getUserRelationships(userId: string): Promise<CharacterRelationship[]> {
    return this.relationshipModel.find({ userId }).sort({ 'relationship.respectLevel': -1 }).exec();
  }

  /**
   * Get relationship statistics for a character
   */
  async getCharacterRelationshipStats(characterId: string): Promise<any> {
    const relationships = await this.relationshipModel.find({ characterId }).exec();

    if (relationships.length === 0) {
      return {
        totalRelationships: 0,
        averageRespect: 0,
        averageTrust: 0,
        relationshipDistribution: {},
      };
    }

    const totalRespect = relationships.reduce((sum, rel) => sum + rel.relationship.respectLevel, 0);
    const totalTrust = relationships.reduce((sum, rel) => sum + rel.relationship.trustLevel, 0);

    const statusDistribution = relationships.reduce((acc, rel) => {
      const status = rel.relationship.relationshipStatus;
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});

    return {
      totalRelationships: relationships.length,
      averageRespect: totalRespect / relationships.length,
      averageTrust: totalTrust / relationships.length,
      relationshipDistribution: statusDistribution,
    };
  }

  /**
   * Calculate relationship changes based on interaction
   */
  private calculateRelationshipChanges(userMove: any, aiResponse: any): {
    respectChange: number;
    trustChange: number;
  } {
    let respectChange = 0;
    let trustChange = 0;

    // Base changes on AI confidence/satisfaction
    const aiSatisfaction = aiResponse.confidence || 0.5;
    
    if (aiSatisfaction > 0.7) {
      respectChange += 2;
      trustChange += 1;
    } else if (aiSatisfaction < 0.3) {
      respectChange -= 1;
      trustChange -= 2;
    }

    // Adjust based on user strategy
    const strategy = userMove.strategy?.toLowerCase() || '';
    
    if (strategy.includes('collaborative') || strategy.includes('win-win')) {
      respectChange += 3;
      trustChange += 2;
    } else if (strategy.includes('aggressive') || strategy.includes('competitive')) {
      respectChange -= 1;
      trustChange -= 1;
    }

    // Adjust based on communication quality (message length and politeness)
    const messageLength = userMove.message?.length || 0;
    if (messageLength > 50) { // Thoughtful communication
      respectChange += 1;
    }

    return { respectChange, trustChange };
  }

  /**
   * Calculate relationship status based on levels
   */
  private calculateRelationshipStatus(respectLevel: number, trustLevel: number): string {
    const average = (respectLevel + trustLevel) / 2;

    if (average >= 80) return 'close_ally';
    if (average >= 60) return 'trusted_partner';
    if (average >= 40) return 'professional_respect';
    if (average >= 20) return 'acquaintance';
    return 'stranger';
  }

  /**
   * Determine negotiation outcome
   */
  private determineOutcome(userMove: any, aiResponse: any): string {
    const aiSatisfaction = aiResponse.confidence || 0.5;
    const userSatisfaction = this.calculateUserSatisfaction(userMove, aiResponse);

    if (aiSatisfaction > 0.7 && userSatisfaction > 0.7) return 'win_win';
    if (userSatisfaction > aiSatisfaction) return 'user_win';
    if (aiSatisfaction > userSatisfaction) return 'ai_win';
    return 'no_deal';
  }

  /**
   * Calculate user satisfaction (simplified)
   */
  private calculateUserSatisfaction(userMove: any, aiResponse: any): number {
    // This is a simplified calculation
    // In a real implementation, this would be more sophisticated
    return Math.random() * 0.4 + 0.3; // Random between 0.3 and 0.7
  }

  /**
   * Calculate bonuses based on relationship level
   */
  private calculateBonuses(
    respectLevel: number,
    trustLevel: number,
    totalInteractions: number
  ): RelationshipBonuses {
    const average = (respectLevel + trustLevel) / 2;

    return {
      betterStartingTerms: average >= 50,
      increasedFlexibility: Math.min(0.2, average / 500), // Up to 20% increase
      insiderInformation: average >= 70 && totalInteractions >= 10,
      relationshipDiscount: Math.min(0.1, average / 1000), // Up to 10% discount
      fasterDecisions: average >= 60,
    };
  }
}

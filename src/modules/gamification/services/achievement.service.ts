import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  Achievement,
  AchievementDocument,
  ACHIEVEMENT_MODEL,
} from '../schemas/achievement.schema';
import { AchievementEngine } from '../engines/achievement.engine';
import { GamificationService } from './gamification.service';

@Injectable()
export class AchievementService {
  private readonly logger = new Logger(AchievementService.name);

  constructor(
    @InjectModel(ACHIEVEMENT_MODEL)
    private readonly achievementModel: Model<AchievementDocument>,
    private readonly achievementEngine: AchievementEngine,
    private readonly gamificationService: GamificationService,
  ) {}

  /**
   * Get all active achievements
   */
  async getActiveAchievements(): Promise<Achievement[]> {
    return this.achievementModel.find({ isActive: true }).exec();
  }

  /**
   * Get achievement by ID
   */
  async getAchievement(achievementId: string): Promise<Achievement | null> {
    return this.achievementModel.findOne({ id: achievementId, isActive: true }).exec();
  }

  /**
   * Get achievements by category
   */
  async getAchievementsByCategory(category: string): Promise<Achievement[]> {
    return this.achievementModel.find({ category, isActive: true }).exec();
  }

  /**
   * Check session-based achievements
   */
  async checkSessionAchievements(
    userId: string,
    sessionId: string,
    sessionData: any,
  ): Promise<string[]> {
    const userGamification = await this.gamificationService.getUserGamification(userId);
    const unlockedAchievementIds = userGamification.achievements.map(a => a.achievementId);

    const context = {
      userId,
      sessionId,
      sessionData,
      userGamification,
      unlockedAchievementIds,
    };

    const newAchievements = await this.achievementEngine.checkSessionAchievements(context);

    // Award achievements
    for (const achievementId of newAchievements) {
      await this.gamificationService.awardAchievement(
        userId,
        achievementId,
        sessionId,
        { sessionMetrics: sessionData.metrics }
      );
    }

    return newAchievements;
  }

  /**
   * Check level-based achievements
   */
  async checkLevelAchievements(userId: string, newLevel: number): Promise<string[]> {
    const userGamification = await this.gamificationService.getUserGamification(userId);
    const unlockedAchievementIds = userGamification.achievements.map(a => a.achievementId);

    const context = {
      userId,
      newLevel,
      userGamification,
      unlockedAchievementIds,
    };

    const newAchievements = await this.achievementEngine.checkLevelAchievements(context);

    // Award achievements
    for (const achievementId of newAchievements) {
      await this.gamificationService.awardAchievement(
        userId,
        achievementId,
        undefined,
        { level: newLevel }
      );
    }

    return newAchievements;
  }

  /**
   * Get user's achievement progress
   */
  async getUserAchievementProgress(userId: string): Promise<any> {
    const userGamification = await this.gamificationService.getUserGamification(userId);
    const allAchievements = await this.getActiveAchievements();

    const progress = allAchievements.map(achievement => {
      const userAchievement = userGamification.achievements.find(
        a => a.achievementId === achievement.id
      );

      return {
        ...achievement,
        unlocked: !!userAchievement,
        unlockedAt: userAchievement?.unlockedAt,
        progress: this.calculateAchievementProgress(achievement, userGamification),
      };
    });

    return {
      achievements: progress,
      totalUnlocked: userGamification.achievements.length,
      totalAvailable: allAchievements.length,
      completionRate: userGamification.achievements.length / allAchievements.length,
    };
  }

  /**
   * Calculate achievement progress (0-1)
   */
  public calculateAchievementProgress(achievement: Achievement, userGamification: any): number {
    const { requirements } = achievement;

    switch (requirements.type) {
      case 'session_completion':
        // For session completion achievements, progress is binary (0 or 1)
        return 0;

      case 'total_sessions':
        const targetSessions = requirements.conditions.minSessions || 1;
        return Math.min((userGamification?.statistics?.totalSessions || 0) / targetSessions, 1);

      case 'performance_streak':
        const targetStreak = requirements.conditions.minStreak || 1;
        return Math.min((userGamification?.statistics?.currentStreak || 0) / targetStreak, 1);

      case 'level_based':
        const targetLevel = requirements.conditions.level || 1;
        return Math.min((userGamification?.level?.current || 1) / targetLevel, 1);

      default:
        return 0;
    }
  }

  /**
   * Create a new achievement
   */
  async createAchievement(achievementData: Partial<Achievement>): Promise<Achievement> {
    const achievement = new this.achievementModel(achievementData);
    return achievement.save();
  }

  /**
   * Update achievement statistics
   */
  async updateAchievementStatistics(achievementId: string): Promise<void> {
    const achievement = await this.achievementModel.findOne({ id: achievementId }).exec();
    if (!achievement) return;

    // This would typically involve aggregating data from user_gamification collection
    // For now, we'll increment the unlock count
    achievement.statistics.totalUnlocked += 1;
    achievement.statistics.lastUnlockedAt = new Date();
    
    await achievement.save();
  }

  /**
   * Get achievement leaderboard
   */
  async getAchievementLeaderboard(achievementId: string, limit: number = 10): Promise<any[]> {
    // This would return users who unlocked this achievement, sorted by unlock time
    // Implementation would involve aggregating user_gamification data
    return [];
  }

  /**
   * Get rare achievements (legendary/epic)
   */
  async getRareAchievements(): Promise<Achievement[]> {
    return this.achievementModel.find({
      isActive: true,
      rarity: { $in: ['epic', 'legendary'] }
    }).exec();
  }

  /**
   * Seed initial achievements
   */
  async seedAchievements(): Promise<void> {
    const existingCount = await this.achievementModel.countDocuments().exec();
    if (existingCount > 0) {
      this.logger.log('Achievements already seeded');
      return;
    }

    const achievements = [
      {
        id: 'speed_demon',
        title: 'Speed Demon',
        description: 'Close a deal in 3 rounds or less',
        badge: '⚡',
        rarity: 'rare',
        category: 'efficiency',
        requirements: {
          type: 'session_completion',
          conditions: { maxRounds: 3, dealClosed: true, minScore: 7.0 }
        },
        rewards: { xp: 150, credits: 50 }
      },
      {
        id: 'win_win_master',
        title: 'Win-Win Master',
        description: 'Achieve high satisfaction for both parties',
        badge: '🤝',
        rarity: 'epic',
        category: 'collaboration',
        requirements: {
          type: 'session_completion',
          conditions: { minUserSatisfaction: 0.85, minAISatisfaction: 0.85 }
        },
        rewards: { xp: 200, credits: 75 }
      },
      {
        id: 'persistent_negotiator',
        title: 'Persistent Negotiator',
        description: 'Complete 10 negotiation sessions',
        badge: '🎯',
        rarity: 'common',
        category: 'persistence',
        requirements: {
          type: 'total_sessions',
          conditions: { minSessions: 10 }
        },
        rewards: { xp: 100, credits: 25 }
      },
      {
        id: 'level_up_legend',
        title: 'Level Up Legend',
        description: 'Reach level 5',
        badge: '🏆',
        rarity: 'epic',
        category: 'mastery',
        requirements: {
          type: 'level_based',
          conditions: { level: 5 }
        },
        rewards: { xp: 300, credits: 100 }
      }
    ];

    await this.achievementModel.insertMany(achievements);
    this.logger.log(`Seeded ${achievements.length} achievements`);
  }
}

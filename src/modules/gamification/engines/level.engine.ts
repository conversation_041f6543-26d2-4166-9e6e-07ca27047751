import { Injectable } from '@nestjs/common';
import { LevelInfo } from '../schemas/user-gamification.schema';

export interface LevelRequirement {
  level: number;
  xp: number;
  title: string;
  description?: string;
  unlocks?: string[];
}

@Injectable()
export class LevelEngine {
  private readonly levelRequirements: LevelRequirement[] = [
    { level: 1, xp: 0, title: 'Rookie Negotiator', description: 'Just starting your negotiation journey' },
    { level: 2, xp: 500, title: 'Junior Professional', description: 'Learning the basics of negotiation' },
    { level: 3, xp: 1500, title: 'Senior Negotiator', description: 'Developing advanced negotiation skills' },
    { level: 4, xp: 3500, title: 'Expert Dealmaker', description: 'Mastering complex negotiations' },
    { level: 5, xp: 7500, title: 'Master Negotiator', description: 'Leading high-stakes negotiations' },
    { level: 6, xp: 15000, title: 'Negotiation Guru', description: 'Teaching others through example' },
    { level: 7, xp: 30000, title: 'Deal Wizard', description: 'Creating win-win solutions effortlessly' },
    { level: 8, xp: 60000, title: 'Legendary Closer', description: 'The ultimate negotiation master' },
  ];

  /**
   * Calculate level information based on total XP
   */
  calculateLevel(totalXP: number): LevelInfo {
    let currentLevel = 1;
    let currentLevelXP = 0;
    let nextLevelXP = 500;
    let title = 'Rookie Negotiator';

    // Find the highest level the user has reached
    for (let i = this.levelRequirements.length - 1; i >= 0; i--) {
      const requirement = this.levelRequirements[i];
      if (totalXP >= requirement.xp) {
        currentLevel = requirement.level;
        currentLevelXP = requirement.xp;
        title = requirement.title;

        // Find next level XP
        if (i < this.levelRequirements.length - 1) {
          nextLevelXP = this.levelRequirements[i + 1].xp;
        } else {
          nextLevelXP = requirement.xp; // Max level reached
        }
        break;
      }
    }

    const currentXP = totalXP - currentLevelXP;
    const xpToNext = Math.max(0, nextLevelXP - totalXP);

    return {
      current: currentLevel,
      title,
      currentXP,
      totalXP,
      xpToNext,
    };
  }

  /**
   * Get level requirement by level number
   */
  getLevelRequirement(level: number): LevelRequirement | null {
    return this.levelRequirements.find(req => req.level === level) || null;
  }

  /**
   * Get all level requirements
   */
  getAllLevelRequirements(): LevelRequirement[] {
    return [...this.levelRequirements];
  }

  /**
   * Calculate XP needed for a specific level
   */
  getXPForLevel(level: number): number {
    const requirement = this.getLevelRequirement(level);
    return requirement ? requirement.xp : 0;
  }

  /**
   * Calculate XP needed to reach next level
   */
  getXPToNextLevel(currentXP: number): number {
    const currentLevel = this.calculateLevel(currentXP);
    return currentLevel.xpToNext;
  }

  /**
   * Get level progress as percentage
   */
  getLevelProgress(totalXP: number): number {
    const levelInfo = this.calculateLevel(totalXP);
    
    if (levelInfo.xpToNext === 0) {
      return 100; // Max level reached
    }

    const currentLevelXP = levelInfo.totalXP - levelInfo.currentXP;
    const nextLevelXP = levelInfo.totalXP + levelInfo.xpToNext;
    const levelRange = nextLevelXP - currentLevelXP;
    
    return Math.round((levelInfo.currentXP / levelRange) * 100);
  }

  /**
   * Calculate XP reward based on performance
   */
  calculateXPReward(
    baseXP: number,
    performanceMultiplier: number = 1.0,
    difficultyMultiplier: number = 1.0,
    bonusMultipliers: number[] = [],
  ): number {
    let totalXP = baseXP * performanceMultiplier * difficultyMultiplier;

    // Apply bonus multipliers
    for (const bonus of bonusMultipliers) {
      totalXP *= bonus;
    }

    return Math.round(totalXP);
  }

  /**
   * Get recommended XP rewards for different actions
   */
  getXPRewards() {
    return {
      sessionStart: 10,
      moveCompleted: 5,
      sessionCompleted: 50,
      dealClosed: 100,
      achievementUnlocked: 25,
      firstTimeBonus: 1.5,
      perfectScore: 2.0,
      speedBonus: 1.3,
      difficultyBonuses: {
        beginner: 1.0,
        intermediate: 1.5,
        expert: 2.0,
      },
    };
  }

  /**
   * Check if user should level up
   */
  shouldLevelUp(currentLevel: number, totalXP: number): boolean {
    const newLevel = this.calculateLevel(totalXP);
    return newLevel.current > currentLevel;
  }

  /**
   * Get level unlocks for a specific level
   */
  getLevelUnlocks(level: number): string[] {
    const levelUnlocks = {
      1: ['character_default'],
      2: ['character_sarah_chen', 'scenario_basic_software'],
      3: ['scenario_advanced_software', 'feature_hints'],
      4: ['character_marcus_rodriguez', 'feature_analytics'],
      5: ['feature_team_challenges', 'scenario_partnership'],
      6: ['character_jennifer_liu', 'scenario_complex_deals'],
      7: ['feature_campaign_mode', 'character_expert_level'],
      8: ['feature_master_tools', 'scenario_legendary'],
    };

    return levelUnlocks[level] || [];
  }

  /**
   * Get estimated time to reach level (in hours)
   */
  getEstimatedTimeToLevel(currentXP: number, targetLevel: number, avgXPPerHour: number = 100): number {
    const targetXP = this.getXPForLevel(targetLevel);
    const xpNeeded = Math.max(0, targetXP - currentXP);
    
    return Math.ceil(xpNeeded / avgXPPerHour);
  }

  /**
   * Get level statistics
   */
  getLevelStatistics(totalXP: number) {
    const levelInfo = this.calculateLevel(totalXP);
    const progress = this.getLevelProgress(totalXP);
    const unlocks = this.getLevelUnlocks(levelInfo.current);
    
    return {
      ...levelInfo,
      progress,
      unlocks,
      isMaxLevel: levelInfo.current >= this.levelRequirements.length,
      nextLevelUnlocks: this.getLevelUnlocks(levelInfo.current + 1),
    };
  }
}

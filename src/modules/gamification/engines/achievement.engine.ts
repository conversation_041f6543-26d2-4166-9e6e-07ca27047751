import { Injectable, Logger } from '@nestjs/common';

export interface AchievementContext {
  userId: string;
  sessionId?: string;
  sessionData?: any;
  userGamification?: any;
  unlockedAchievementIds?: string[];
  newLevel?: number;
}

export interface AchievementRule {
  id: string;
  check: (context: AchievementContext) => Promise<boolean> | boolean;
}

@Injectable()
export class AchievementEngine {
  private readonly logger = new Logger(AchievementEngine.name);
  private achievementRules: Map<string, AchievementRule> = new Map();

  constructor() {
    this.initializeRules();
  }

  private initializeRules(): void {
    // Speed Demon Achievement
    this.achievementRules.set('speed_demon', {
      id: 'speed_demon',
      check: (context: AchievementContext) => {
        const { sessionData } = context;
        if (!sessionData?.metrics) return false;
        
        return sessionData.metrics.totalRounds <= 3 &&
               sessionData.metrics.agreementReached &&
               sessionData.metrics.overallScore >= 7.0;
      }
    });

    // Win-Win Master Achievement
    this.achievementRules.set('win_win_master', {
      id: 'win_win_master',
      check: (context: AchievementContext) => {
        const { sessionData } = context;
        if (!sessionData?.metrics) return false;
        
        return (sessionData.metrics.userSatisfaction || 0) >= 0.85 &&
               (sessionData.metrics.aiSatisfaction || 0) >= 0.85;
      }
    });

    // Hot Streak Achievement
    this.achievementRules.set('hot_streak', {
      id: 'hot_streak',
      check: async (context: AchievementContext) => {
        const { userGamification } = context;
        if (!userGamification?.statistics) return false;
        
        return userGamification.statistics.currentStreak >= 5;
      }
    });

    // Persistent Negotiator Achievement
    this.achievementRules.set('persistent_negotiator', {
      id: 'persistent_negotiator',
      check: (context: AchievementContext) => {
        const { userGamification } = context;
        if (!userGamification?.statistics) return false;
        
        return userGamification.statistics.completedSessions >= 10;
      }
    });

    // Level Up Legend Achievement
    this.achievementRules.set('level_up_legend', {
      id: 'level_up_legend',
      check: (context: AchievementContext) => {
        const { userGamification, newLevel } = context;
        const level = newLevel || userGamification?.level?.current || 0;
        
        return level >= 5;
      }
    });

    // Perfect Score Achievement
    this.achievementRules.set('perfect_score', {
      id: 'perfect_score',
      check: (context: AchievementContext) => {
        const { sessionData } = context;
        if (!sessionData?.metrics) return false;
        
        return sessionData.metrics.overallScore >= 9.5;
      }
    });

    // Time Master Achievement
    this.achievementRules.set('time_master', {
      id: 'time_master',
      check: (context: AchievementContext) => {
        const { sessionData } = context;
        if (!sessionData?.metrics) return false;
        
        return sessionData.metrics.timeEfficiency >= 0.9 &&
               sessionData.metrics.agreementReached;
      }
    });

    // Communication Expert Achievement
    this.achievementRules.set('communication_expert', {
      id: 'communication_expert',
      check: (context: AchievementContext) => {
        const { sessionData } = context;
        if (!sessionData?.metrics) return false;
        
        return sessionData.metrics.communicationQuality >= 0.9;
      }
    });

    // Strategic Genius Achievement
    this.achievementRules.set('strategic_genius', {
      id: 'strategic_genius',
      check: (context: AchievementContext) => {
        const { sessionData } = context;
        if (!sessionData?.metrics) return false;
        
        return sessionData.metrics.strategicEffectiveness >= 0.95;
      }
    });

    // Marathon Negotiator Achievement
    this.achievementRules.set('marathon_negotiator', {
      id: 'marathon_negotiator',
      check: (context: AchievementContext) => {
        const { userGamification } = context;
        if (!userGamification?.statistics) return false;
        
        return userGamification.statistics.totalSessions >= 50;
      }
    });
  }

  /**
   * Check session-based achievements
   */
  async checkSessionAchievements(context: AchievementContext): Promise<string[]> {
    const unlockedAchievements: string[] = [];
    const alreadyUnlocked = context.unlockedAchievementIds || [];

    for (const [achievementId, rule] of this.achievementRules) {
      // Skip if already unlocked
      if (alreadyUnlocked.includes(achievementId)) continue;

      try {
        // Check if requirements are met
        const isUnlocked = await rule.check(context);
        if (isUnlocked) {
          unlockedAchievements.push(achievementId);
          this.logger.log(`Achievement unlocked: ${achievementId} for user ${context.userId}`);
        }
      } catch (error) {
        this.logger.error(`Error checking achievement ${achievementId}: ${error.message}`);
      }
    }

    return unlockedAchievements;
  }

  /**
   * Check level-based achievements
   */
  async checkLevelAchievements(context: AchievementContext): Promise<string[]> {
    const levelBasedAchievements = ['level_up_legend'];
    const unlockedAchievements: string[] = [];
    const alreadyUnlocked = context.unlockedAchievementIds || [];

    for (const achievementId of levelBasedAchievements) {
      // Skip if already unlocked
      if (alreadyUnlocked.includes(achievementId)) continue;

      const rule = this.achievementRules.get(achievementId);
      if (!rule) continue;

      try {
        const isUnlocked = await rule.check(context);
        if (isUnlocked) {
          unlockedAchievements.push(achievementId);
          this.logger.log(`Level achievement unlocked: ${achievementId} for user ${context.userId}`);
        }
      } catch (error) {
        this.logger.error(`Error checking level achievement ${achievementId}: ${error.message}`);
      }
    }

    return unlockedAchievements;
  }

  /**
   * Check all achievements for a user
   */
  async checkAllAchievements(context: AchievementContext): Promise<string[]> {
    const sessionAchievements = await this.checkSessionAchievements(context);
    const levelAchievements = await this.checkLevelAchievements(context);
    
    return [...sessionAchievements, ...levelAchievements];
  }

  /**
   * Add a custom achievement rule
   */
  addAchievementRule(rule: AchievementRule): void {
    this.achievementRules.set(rule.id, rule);
    this.logger.log(`Added custom achievement rule: ${rule.id}`);
  }

  /**
   * Remove an achievement rule
   */
  removeAchievementRule(achievementId: string): void {
    this.achievementRules.delete(achievementId);
    this.logger.log(`Removed achievement rule: ${achievementId}`);
  }

  /**
   * Get all achievement rule IDs
   */
  getAchievementRuleIds(): string[] {
    return Array.from(this.achievementRules.keys());
  }

  /**
   * Check if a specific achievement can be unlocked
   */
  async canUnlockAchievement(achievementId: string, context: AchievementContext): Promise<boolean> {
    const rule = this.achievementRules.get(achievementId);
    if (!rule) return false;

    try {
      return await rule.check(context);
    } catch (error) {
      this.logger.error(`Error checking achievement ${achievementId}: ${error.message}`);
      return false;
    }
  }
}

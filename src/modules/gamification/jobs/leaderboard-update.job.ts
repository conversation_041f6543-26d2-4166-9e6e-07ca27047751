import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { LeaderboardService } from '../services/leaderboard.service';

@Injectable()
export class LeaderboardUpdateJob {
  private readonly logger = new Logger(LeaderboardUpdateJob.name);

  constructor(private readonly leaderboardService: LeaderboardService) {}

  /**
   * Update weekly leaderboards every 5 minutes
   */
  @Cron('0 */5 * * * *')
  async updateWeeklyLeaderboards() {
    try {
      this.logger.log('Starting weekly leaderboard update...');
      
      await Promise.all([
        this.leaderboardService.updateGlobalLeaderboard('weekly'),
        this.leaderboardService.updateOrganizationLeaderboards('weekly'),
      ]);
      
      this.logger.log('Weekly leaderboard update completed');
    } catch (error) {
      this.logger.error(`Error updating weekly leaderboards: ${error.message}`);
    }
  }

  /**
   * Update monthly leaderboards every hour
   */
  @Cron(CronExpression.EVERY_HOUR)
  async updateMonthlyLeaderboards() {
    try {
      this.logger.log('Starting monthly leaderboard update...');
      
      await Promise.all([
        this.leaderboardService.updateGlobalLeaderboard('monthly'),
        this.leaderboardService.updateOrganizationLeaderboards('monthly'),
      ]);
      
      this.logger.log('Monthly leaderboard update completed');
    } catch (error) {
      this.logger.error(`Error updating monthly leaderboards: ${error.message}`);
    }
  }

  /**
   * Update all-time leaderboards daily
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async updateAllTimeLeaderboards() {
    try {
      this.logger.log('Starting all-time leaderboard update...');
      
      await Promise.all([
        this.leaderboardService.updateGlobalLeaderboard('all_time'),
        this.leaderboardService.updateOrganizationLeaderboards('all_time'),
      ]);
      
      this.logger.log('All-time leaderboard update completed');
    } catch (error) {
      this.logger.error(`Error updating all-time leaderboards: ${error.message}`);
    }
  }

  /**
   * Manual trigger for leaderboard updates (for testing)
   */
  async triggerManualUpdate(type: 'weekly' | 'monthly' | 'all_time' = 'weekly') {
    try {
      this.logger.log(`Manual trigger: updating ${type} leaderboards...`);
      
      await Promise.all([
        this.leaderboardService.updateGlobalLeaderboard(type),
        this.leaderboardService.updateOrganizationLeaderboards(type),
      ]);
      
      this.logger.log(`Manual ${type} leaderboard update completed`);
    } catch (error) {
      this.logger.error(`Error in manual ${type} leaderboard update: ${error.message}`);
      throw error;
    }
  }
}

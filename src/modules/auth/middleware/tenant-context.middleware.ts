import {
  Injectable,
  NestMiddleware,
  UnauthorizedException,
} from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { TenantContextService } from '../services/tenant-context.service';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';

interface JwtPayload {
  sub: string;
  organizationId: string;
  role: string;
}

@Injectable()
export class TenantContextMiddleware implements NestMiddleware {
  constructor(
    private readonly tenantContext: TenantContextService,
    private readonly configService: ConfigService,
    private readonly jwtService: JwtService,
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    try {
      const fullPath = req.originalUrl || req.url;
      console.log('Incoming request path:', fullPath);

      // Check for public routes first
      if (this.isPublicRoute(fullPath)) {
        return next();
      }

      const token = this.extractToken(req);
      if (!token) {
        throw new UnauthorizedException('No authorization token provided');
      }

      const payload = await this.jwtService.verifyAsync<JwtPayload>(token, {
        secret: this.configService.get<string>('auth.jwtSecret'),
      });

      if (!payload.organizationId) {
        throw new UnauthorizedException('Organization ID missing from token');
      }

      // Create context object
      const context = {
        organizationId: payload.organizationId,
        userId: payload.sub,
        userRole: payload.role,
        ipAddress: this.getClientIp(req),
        userAgent: req.headers['user-agent'] as string,
      };

      // Store context on request object as fallback
      req.tenant = {
        organizationId: context.organizationId,
        userId: context.userId
      };

      // Store TenantContextService instance on request
      req.tenantContext = this.tenantContext;

      console.log('Setting tenant context with payload:', {
        organizationId: context.organizationId,
        userId: context.userId,
        role: context.userRole
      });

      // Run the rest of the request chain within the tenant context
      return this.tenantContext.runWithContext(context, async () => {
        // Verify context is immediately available
        const currentOrg = this.tenantContext.getCurrentOrganization();
        console.log('Verifying tenant context - Current org:', currentOrg);
        
        if (!currentOrg) {
          throw new Error('Failed to set tenant context');
        }

        // Execute the rest of the middleware chain within the context
        return new Promise<void>((resolve, reject) => {
          res.on('finish', () => resolve(void 0));
          res.on('error', reject);
          next();
        });
      });
    } catch (error) {
      console.error('Tenant context error:', error);
      throw new UnauthorizedException(error.message);
    }
  }

  private extractToken(req: Request): string | undefined {
    const [type, token] = (req.headers.authorization ?? '').split(' ');
    return type === 'Bearer' ? token : undefined;
  }

  private isPublicRoute(path: string): boolean {
    const publicRoutes = [
      '/api/auth/login',
      '/api/auth/register',
      '/api/auth/google',
      '/api/auth/google/callback',
      '/api/auth/refresh',
      '/api/health',
      '/api/docs',
      '/api/webhook/stripe',
      '/api/webhook/test',
      '/api/subscriptions/webhook',
    ];

    return publicRoutes.some((route) => path.includes(route));
  }

  private getClientIp(req: Request): string {
    return req.ip;
  }
}

// Extend Express Request interface to include tenant context
declare global {
  namespace Express {
    interface Request {
      tenant?: {
        organizationId: string;
        userId?: string;
      };
      tenantContext?: TenantContextService;
    }
  }
}

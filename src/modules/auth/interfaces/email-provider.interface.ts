export interface EmailMessage {
  to: string;
  subject: string;
  htmlContent: string;
  textContent?: string;
  from?: {
    name: string;
    email: string;
  };
}

export interface EmailSendResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

export interface IEmailProvider {
  /**
   * Initialize the email provider with configuration
   */
  initialize(config: Record<string, any>): Promise<void>;

  /**
   * Send an email
   */
  sendEmail(message: EmailMessage): Promise<EmailSendResult>;

  /**
   * Verify the provider configuration
   */
  verify(): Promise<boolean>;

  /**
   * Get the provider name
   */
  getProviderName(): string;
}

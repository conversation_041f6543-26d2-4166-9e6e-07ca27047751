import { UserRole } from '../enums/roles.enum';

export interface User {
  id: string;
  organizationId: string;
  email: string;
  passwordHash: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  emailVerified: boolean;
  isActive: boolean;
  preferences: Record<string, any>;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserCreateInput {
  email: string;
  password: string; // Plain password, will be hashed
  firstName: string;
  lastName: string;
  organizationId: string;
  role?: UserRole;
}

export interface UserUpdateInput {
  firstName?: string;
  lastName?: string;
  role?: UserRole;
  isActive?: boolean;
  preferences?: Record<string, any>;
}

export interface UserLoginHistory {
  userId: string;
  organizationId: string;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
}

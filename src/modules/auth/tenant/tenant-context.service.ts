import { Injectable, Scope } from '@nestjs/common';
import { TenantContext } from './tenant-context';

@Injectable({ scope: Scope.REQUEST })
export class TenantContextService implements TenantContext {
  public organizationId: string;
  public userId?: string;

  getCurrentOrganization(): string | undefined {
    return this.organizationId;
  }

  getCurrentUserId(): string | undefined {
    return this.userId;
  }

  setCurrentOrganization(organizationId: string): void {
    this.organizationId = organizationId;
  }

  setCurrentUserId(userId: string): void {
    this.userId = userId;
  }

  verifyTenant(organizationId: string): boolean {
    return this.organizationId === organizationId;
  }

  ensureTenantAccess(organizationId: string): void {
    if (!this.verifyTenant(organizationId)) {
      throw new Error(`Access denied for organization: ${organizationId}`);
    }
  }

  async runWithContext<T>(
    context: { organizationId: string; userId?: string },
    callback: () => Promise<T>,
  ): Promise<T> {
    const previousOrgId = this.organizationId;
    const previousUserId = this.userId;

    try {
      this.organizationId = context.organizationId;
      this.userId = context.userId;
      return await callback();
    } finally {
      this.organizationId = previousOrgId;
      this.userId = previousUserId;
    }
  }
}

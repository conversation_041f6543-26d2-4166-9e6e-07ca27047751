/**
 * Interface representing a tenant context with organization and user information
 */
export interface TenantContext {
  organizationId: string;
  userId?: string;
  getCurrentOrganization(): string | undefined;
  getCurrentUserId(): string | undefined;
  verifyTenant(organizationId: string): boolean;
  ensureTenantAccess(organizationId: string): void;
  runWithContext<T>(context: { organizationId: string; userId?: string }, callback: () => Promise<T>): Promise<T>;
}
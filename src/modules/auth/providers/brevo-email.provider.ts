import { Injectable, Logger } from '@nestjs/common';
import axios, { AxiosInstance } from 'axios';
import { IEmailProvider, EmailMessage, EmailSendResult } from '../interfaces/email-provider.interface';

@Injectable()
export class BrevoEmailProvider implements IEmailProvider {
  private readonly logger = new Logger(BrevoEmailProvider.name);
  private httpClient: AxiosInstance;
  private apiKey: string;
  private defaultSender: { name: string; email: string };
  private isInitialized = false;

  async initialize(config: Record<string, any>): Promise<void> {
    this.apiKey = config.apiKey;
    this.defaultSender = {
      name: config.senderName || 'DocGic',
      email: config.senderEmail || '<EMAIL>',
    };

    if (!this.apiKey) {
      throw new Error('Brevo API key is required');
    }

    // Initialize HTTP client with Brevo API configuration
    this.httpClient = axios.create({
      baseURL: 'https://api.brevo.com/v3',
      headers: {
        'accept': 'application/json',
        'api-key': this.apiKey,
        'content-type': 'application/json',
      },
      timeout: 10000, // 10 seconds timeout
    });

    this.isInitialized = true;
    this.logger.log('Brevo email provider initialized successfully');
  }

  async sendEmail(message: EmailMessage): Promise<EmailSendResult> {
    if (!this.isInitialized) {
      return {
        success: false,
        error: 'Brevo provider not initialized',
      };
    }

    try {
      const payload = {
        sender: message.from || this.defaultSender,
        to: [
          {
            email: message.to,
            name: message.to.split('@')[0], // Use email prefix as name if not provided
          },
        ],
        subject: message.subject,
        htmlContent: message.htmlContent,
        textContent: message.textContent,
      };

      this.logger.debug(`Sending email via Brevo to: ${message.to}`);
      
      const response = await this.httpClient.post('/smtp/email', payload);

      if (response.status === 201) {
        this.logger.log(`Email sent successfully via Brevo to: ${message.to}`);
        return {
          success: true,
          messageId: response.data.messageId,
        };
      } else {
        this.logger.error(`Unexpected response from Brevo: ${response.status}`);
        return {
          success: false,
          error: `Unexpected response status: ${response.status}`,
        };
      }
    } catch (error) {
      this.logger.error(`Failed to send email via Brevo: ${error.message}`, error.stack);
      
      if (error.response) {
        // Brevo API error
        const apiError = error.response.data;
        return {
          success: false,
          error: `Brevo API error: ${apiError.message || error.message}`,
        };
      } else if (error.request) {
        // Network error
        return {
          success: false,
          error: 'Network error: Unable to reach Brevo API',
        };
      } else {
        // Other error
        return {
          success: false,
          error: error.message,
        };
      }
    }
  }

  async verify(): Promise<boolean> {
    if (!this.isInitialized) {
      return false;
    }

    try {
      // Test the API key by making a simple request to get account info
      const response = await this.httpClient.get('/account');
      return response.status === 200;
    } catch (error) {
      this.logger.error(`Brevo verification failed: ${error.message}`);
      return false;
    }
  }

  getProviderName(): string {
    return 'brevo';
  }
}

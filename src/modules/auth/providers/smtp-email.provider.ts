import { Injectable, Logger } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
import { IEmailProvider, EmailMessage, EmailSendResult } from '../interfaces/email-provider.interface';

@Injectable()
export class SmtpEmailProvider implements IEmailProvider {
  private readonly logger = new Logger(SmtpEmailProvider.name);
  private transporter: nodemailer.Transporter;
  private defaultSender: { name: string; email: string };
  private isInitialized = false;

  async initialize(config: Record<string, any>): Promise<void> {
    const { host, port, user, password, secure, senderName, senderEmail } = config;

    if (!host || !port || !user || !password) {
      throw new Error('SMTP configuration incomplete: host, port, user, and password are required');
    }

    this.defaultSender = {
      name: senderName || 'DocGic',
      email: senderEmail || user,
    };

    this.transporter = nodemailer.createTransport({
      host,
      port: parseInt(port),
      secure: secure === 'true' || secure === true,
      auth: {
        user,
        pass: password,
      },
    });

    this.isInitialized = true;
    this.logger.log('SMTP email provider initialized successfully');
  }

  async sendEmail(message: EmailMessage): Promise<EmailSendResult> {
    if (!this.isInitialized) {
      return {
        success: false,
        error: 'SMTP provider not initialized',
      };
    }

    try {
      const mailOptions = {
        from: message.from ? `"${message.from.name}" <${message.from.email}>` : `"${this.defaultSender.name}" <${this.defaultSender.email}>`,
        to: message.to,
        subject: message.subject,
        html: message.htmlContent,
        text: message.textContent,
      };

      this.logger.debug(`Sending email via SMTP to: ${message.to}`);
      
      const result = await this.transporter.sendMail(mailOptions);

      this.logger.log(`Email sent successfully via SMTP to: ${message.to}`);
      return {
        success: true,
        messageId: result.messageId,
      };
    } catch (error) {
      this.logger.error(`Failed to send email via SMTP: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  async verify(): Promise<boolean> {
    if (!this.isInitialized) {
      return false;
    }

    try {
      await this.transporter.verify();
      return true;
    } catch (error) {
      this.logger.error(`SMTP verification failed: ${error.message}`);
      return false;
    }
  }

  getProviderName(): string {
    return 'smtp';
  }
}

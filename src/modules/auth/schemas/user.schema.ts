import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document as MongoDocument } from 'mongoose';
import { UserRole } from '../enums/roles.enum';

@Schema({ timestamps: true })
export class User {
  @Prop({ required: true })
  id: string;

  @Prop({ required: true, index: true })
  organizationId: string;

  @Prop({ required: true })
  username: string;

  @Prop({ required: true })
  email: string;

  @Prop({ required: false })
  passwordHash: string;

  @Prop({ required: true })
  firstName: string;

  @Prop({ required: true })
  lastName: string;

  @Prop({ type: String, enum: UserRole, default: UserRole.USER })
  role: UserRole;

  @Prop({ default: false })
  emailVerified: boolean;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ type: Object, default: {} })
  preferences: Record<string, any>;

  @Prop()
  googleId?: string;

  @Prop()
  picture?: string;

  @Prop({ default: false })
  isGoogleUser: boolean;

  @Prop({ default: false })
  twoFactorEnabled: boolean;

  @Prop({ type: String })
  twoFactorSecret?: string;

  @Prop({ default: false })
  twoFactorVerified: boolean;

  @Prop({ type: Date })
  lastLoginAt?: Date;

  @Prop({ type: Date })
  createdAt?: Date;

  @Prop({ type: Date })
  updatedAt?: Date;
}

export type UserDocument = User & MongoDocument;

export const UserSchema = SchemaFactory.createForClass(User);

// Remove any existing indexes
UserSchema.index({}, { sparse: true, background: true });

// Add proper indexes for tenant-aware queries with proper error handling
UserSchema.index({ organizationId: 1, email: 1 }, { unique: true }); // Unique email per organization
UserSchema.index({ organizationId: 1, username: 1 }, { unique: true }); // Unique username per organization
UserSchema.index({ organizationId: 1, role: 1 }); // Find users by role within organization
UserSchema.index({ organizationId: 1, isActive: 1 }); // Find active users within organization

// Add pre-save middleware for timestamps
UserSchema.pre('save', function (next) {
  const now = new Date();
  if (!this.createdAt) {
    this.createdAt = now;
  }
  this.updatedAt = now;
  next();
});

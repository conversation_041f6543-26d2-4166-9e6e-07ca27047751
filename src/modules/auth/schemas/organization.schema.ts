import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document as MongoDocument, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true })
export class Organization {
  @Prop({ required: true })
  id: string;

  @Prop({ required: true })
  name: string;

  @Prop({ required: false })
  description?: string;

  @Prop({ type: MongooseSchema.Types.Mixed, default: {} })
  settings: Record<string, any>;
}

export type OrganizationDocument = Organization & MongoDocument;
export const OrganizationSchema = SchemaFactory.createForClass(Organization);

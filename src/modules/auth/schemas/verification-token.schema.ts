import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document as MongoDocument } from 'mongoose';

@Schema({ timestamps: true })
export class VerificationToken {
  @Prop({ required: true })
  id: string;

  @Prop({ required: true, index: true })
  userId: string;

  @Prop({ required: true })
  token: string;

  @Prop({ required: true })
  type: string; // 'email', 'password-reset', etc.

  @Prop({ required: true, expires: '24h' })
  expiresAt: Date;

  @Prop({ type: Date })
  createdAt?: Date;

  @Prop({ type: Date })
  updatedAt?: Date;
}

export type VerificationTokenDocument = VerificationToken & MongoDocument;

export const VerificationTokenSchema = SchemaFactory.createForClass(VerificationToken);

// Add indexes for faster lookups
VerificationTokenSchema.index({ token: 1, type: 1 });
VerificationTokenSchema.index({ userId: 1, type: 1 });
VerificationTokenSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // TTL index for auto-deletion

// Add pre-save middleware for timestamps
VerificationTokenSchema.pre('save', function (next) {
  const now = new Date();
  if (!this.createdAt) {
    this.createdAt = now;
  }
  this.updatedAt = now;
  next();
});

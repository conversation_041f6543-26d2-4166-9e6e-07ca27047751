import { Module, forwardRef } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthController } from './controllers/auth.controller';
import { AuthService } from './services/auth.service';
import { UserService } from './services/user.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { GoogleStrategy } from './strategies/google.strategy';
import { User, UserSchema } from './schemas/user.schema';
import {
  Organization,
  OrganizationSchema,
} from './schemas/organization.schema';
import { TenantContextService } from './services/tenant-context.service';
// TenantContext interface is used for type hinting in the guard, no need to import here for DI token
import { OrganizationService } from './services/organization.service';
import { OrganizationController } from './controllers/organization.controller';
import { AuditModule } from '../audit/audit.module';
import { EmailService } from './services/email.service';
import { EmailProviderFactory } from './factories/email-provider.factory';
import { BrevoEmailProvider } from './providers/brevo-email.provider';
import { SmtpEmailProvider } from './providers/smtp-email.provider';
import { VerificationTokenService } from './services/verification-token.service';
import { VerificationToken, VerificationTokenSchema } from './schemas/verification-token.schema';
import { ProfileController } from './controllers/profile.controller';
import { SubscriptionModule } from '../subscription/subscription.module';
import { TwoFactorAuthService } from './services/two-factor-auth.service';
import { EmailValidationService } from './services/email-validation.service';
import { SessionSerializer } from './services/session.serializer';

@Module({
  imports: [
    ConfigModule,
    forwardRef(() => AuditModule), // Add forward reference to AuditModule to resolve circular dependency
    forwardRef(() => SubscriptionModule), // Add forward reference to SubscriptionModule for profile controller
    PassportModule.register({ 
      defaultStrategy: 'jwt',
      session: true // Enable session support for Google OAuth
    }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('auth.jwtSecret'),
        signOptions: {
          expiresIn: configService.get<string>('auth.jwtExpiresIn'),
        },
      }),
    }),
    MongooseModule.forFeature([
      { name: User.name, schema: UserSchema },
      { name: Organization.name, schema: OrganizationSchema },
      { name: VerificationToken.name, schema: VerificationTokenSchema },
    ]),
  ],
  controllers: [AuthController, OrganizationController, ProfileController],
  providers: [
    AuthService,
    UserService,
    JwtStrategy,
    GoogleStrategy,
    SessionSerializer,
    TenantContextService,
    OrganizationService,
    EmailService,
    EmailProviderFactory,
    BrevoEmailProvider,
    SmtpEmailProvider,
    VerificationTokenService,
    TwoFactorAuthService,
    EmailValidationService,
    {
      provide: 'TenantContext',
      useClass: TenantContextService,
    }
  ],
  exports: [
    AuthService,
    UserService,
    TenantContextService,
    OrganizationService,
    EmailService,
    VerificationTokenService,
    TwoFactorAuthService,
    EmailValidationService,
    JwtModule,
    ConfigModule,
    {
      provide: 'TenantContext',
      useClass: TenantContextService,
    }
  ],
})
export class AuthModule {}

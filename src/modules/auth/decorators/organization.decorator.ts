import { createParamDecorator, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { TenantContextService } from '../services/tenant-context.service';
import { JwtService } from '@nestjs/jwt';

interface JwtPayload {
  sub: string;
  organizationId: string;
  role: string;
}

/**
 * Decorator that extracts the current organization ID from the request context
 * Usage: @Organization() organizationId: string
 */
export const Organization = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();

    // First, check if the organization ID is directly available on request.tenant
    if (request.tenant && request.tenant.organizationId) {
      console.log('Using organization ID from request.tenant:', request.tenant.organizationId);
      return request.tenant.organizationId;
    }

    // Then try to get the organization ID from the tenant context
    if (request.tenantContext) {
      const tenantContext = request.tenantContext as TenantContextService;
      const organizationId = tenantContext.getCurrentOrganization();
      
      if (organizationId) {
        console.log('Using organization ID from tenant context:', organizationId);
        return organizationId;
      }
    }
    
    // If tenant context is not available or doesn't have the organization ID,
    // try to extract it from the JWT token
    try {
      // Check if organizationId is provided as a query parameter
      if (request.query && request.query.organizationId) {
        return request.query.organizationId;
      }
      
      // Extract the token from the Authorization header
      const authHeader = request.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new UnauthorizedException('No token provided');
      }
      
      const token = authHeader.substring(7); // Remove 'Bearer ' prefix
      
      // Decode the token (not verify, as we just need the payload)
      // This is a simple decode, not a verification
      const payload = JSON.parse(
        Buffer.from(token.split('.')[1], 'base64').toString()
      ) as JwtPayload;
      
      if (!payload.organizationId) {
        throw new UnauthorizedException('Organization ID not found in token');
      }
      
      return payload.organizationId;
    } catch (error) {
      console.error('Error extracting organization ID:', error);
      throw new Error('Failed to extract organization ID: ' + error.message);
    }
  },
);

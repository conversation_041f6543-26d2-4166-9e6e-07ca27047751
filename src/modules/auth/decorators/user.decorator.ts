import { createParamDecorator, ExecutionContext, UnauthorizedException } from '@nestjs/common';

interface JwtUser {
  userId: string;
  username: string;
  role: string;
  organizationId: string;
}

/**
 * Decorator that extracts user information from the request context
 * Usage: @User() user: JwtUser or @User('userId') userId: string
 */
export const User = createParamDecorator(
  (data: string | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    
    // Get user from JWT strategy
    const user = request.user as JwtUser;
    
    if (!user) {
      throw new UnauthorizedException('User not found in request');
    }
    
    // If no specific property is requested, return the full user object
    if (!data) {
      return user;
    }
    
    // Return the requested property
    if (data in user) {
      return user[data as keyof JwtUser];
    }
    
    // Support legacy 'sub' property mapping to userId
    if (data === 'sub') {
      return user.userId;
    }
    
    throw new Error(`Unsupported user property: ${data}`);
  },
);

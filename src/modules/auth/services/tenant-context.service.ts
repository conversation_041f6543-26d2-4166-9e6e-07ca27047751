import { Injectable, Scope, UnauthorizedException, Logger, Inject, forwardRef } from '@nestjs/common';
import { AsyncLocalStorage } from 'async_hooks';
import { ConfigService } from '@nestjs/config';
import { AuditLogService } from '../../audit/services/audit-log.service';

interface TenantContext {
  organizationId: string;
  userId?: string;
  userRole?: string;
  accessTimestamp: number;
  ipAddress?: string;
  userAgent?: string;
}

@Injectable({ scope: Scope.DEFAULT })
export class TenantContextService {
  private readonly storage: AsyncLocalStorage<TenantContext>;
  private readonly logger = new Logger(TenantContextService.name);
  private readonly isProduction: boolean;

  constructor(
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => AuditLogService))
    private readonly auditLogService?: AuditLogService,
  ) {
    this.storage = new AsyncLocalStorage<TenantContext>();
    this.isProduction = this.configService.get<string>('NODE_ENV') === 'production';
  }

  getCurrentTenant(): TenantContext | undefined {
    const context = this.storage.getStore();
    if (!context && this.isProduction) {
      this.logger.warn('Tenant context accessed but not found');
    }
    return context;
  }

  getCurrentOrganization(): string | undefined {
    const orgId = this.storage.getStore()?.organizationId;
    if (!orgId && this.isProduction) {
      this.logger.warn('Organization ID accessed but not found in tenant context');
    }
    return orgId;
  }

  getCurrentUserId(): string | undefined {
    return this.storage.getStore()?.userId;
  }

  getCurrentUserRole(): string | undefined {
    return this.storage.getStore()?.userRole;
  }

  getCurrentIpAddress(): string | undefined {
    return this.storage.getStore()?.ipAddress;
  }

  getCurrentUserAgent(): string | undefined {
    return this.storage.getStore()?.userAgent;
  }

  /**
   * Records document access for audit and security purposes
   * @param documentId The ID of the accessed document
   */
  async recordDocumentAccess(documentId: string, accessType: 'view' | 'download' | 'edit' | 'share' = 'view'): Promise<void> {
    const context = this.storage.getStore();
    if (!context) {
      this.logger.warn(`Attempted to record document access without tenant context: documentId=${documentId}`);
      return;
    }

    const { userId, organizationId, ipAddress, userAgent } = context;
    
    // Log the access for security auditing
    this.logger.log(
      `Document access recorded: userId=${userId}, organizationId=${organizationId}, documentId=${documentId}, timestamp=${new Date().toISOString()}`
    );
    
    // Store the access in the audit log system
    if (this.auditLogService) {
      try {
        await this.auditLogService.logDocumentAccess({
          userId,
          organizationId,
          documentId,
          timestamp: new Date(),
          ipAddress,
          userAgent,
          accessType,
          success: true
        });
      } catch (error) {
        this.logger.error(`Failed to log document access to audit system: ${error.message}`, error.stack);
      }
    }
  }

  /**
   * Records failed document access attempt for security monitoring
   * @param documentId The ID of the document that was attempted to be accessed
   * @param failureReason The reason why access was denied
   */
  async recordFailedDocumentAccess(documentId: string, failureReason: string): Promise<void> {
    const context = this.storage.getStore();
    if (!context) {
      this.logger.warn(`Attempted to record failed document access without tenant context: documentId=${documentId}`);
      return;
    }

    const { userId, organizationId, ipAddress, userAgent } = context;
    
    // Log the failed access for security auditing
    this.logger.warn(
      `Document access denied: userId=${userId}, organizationId=${organizationId}, documentId=${documentId}, reason=${failureReason}`
    );
    
    // Store the failed access in the audit log system
    if (this.auditLogService) {
      try {
        await this.auditLogService.logDocumentAccess({
          userId,
          organizationId,
          documentId,
          timestamp: new Date(),
          ipAddress,
          userAgent,
          accessType: 'view',
          success: false,
          failureReason
        });

        // Also log as a security event for suspicious activity monitoring
        await this.auditLogService.logSecurityEvent({
          eventType: 'permission_denied',
          userId,
          organizationId,
          ipAddress,
          userAgent,
          resourceType: 'document',
          resourceId: documentId,
          timestamp: new Date(),
          details: { reason: failureReason },
          severity: 'medium'
        });
      } catch (error) {
        this.logger.error(`Failed to log failed document access to audit system: ${error.message}`, error.stack);
      }
    }
  }

  /**
   * Run the callback with tenant context
   */
  async runWithContext<T>(
    context: Omit<TenantContext, 'accessTimestamp'>,
    callback: () => Promise<T> | T
  ): Promise<T> {
    const fullContext: TenantContext = {
      ...context,
      accessTimestamp: Date.now()
    };
    
    if (this.isProduction) {
      this.logger.debug(`Setting tenant context: org=${context.organizationId}, user=${context.userId}`);
    }

    // Wrap the callback execution in a try-catch to ensure context is maintained
    try {
      const result = this.storage.run(fullContext, callback);
      // Handle both Promise and non-Promise results
      return result instanceof Promise ? await result : result;
    } catch (error) {
      this.logger.error('Error executing within tenant context:', error);
      throw error;
    }
  }

  /**
   * Verify if the given organization ID matches the current tenant context
   */
  verifyOrganization(targetOrgId: string): boolean {
    const currentOrgId = this.getCurrentOrganization();
    
    if (!currentOrgId) {
      this.logger.warn('Organization verification failed: No current organization in context');
      return false;
    }
    
    const isMatch = currentOrgId === targetOrgId;
    
    if (!isMatch && this.isProduction) {
      this.logger.warn(
        `Organization mismatch: current=${currentOrgId}, target=${targetOrgId}, user=${this.getCurrentUserId()}`
      );
      
      // Log security event for cross-organization access attempt
      if (this.auditLogService) {
        const context = this.storage.getStore();
        this.auditLogService.logSecurityEvent({
          eventType: 'permission_denied',
          userId: context?.userId,
          organizationId: context?.organizationId,
          ipAddress: context?.ipAddress,
          userAgent: context?.userAgent,
          timestamp: new Date(),
          details: {
            reason: 'Cross-organization access attempt',
            targetOrganizationId: targetOrgId
          },
          severity: 'high'
        }).catch(error => {
          this.logger.error(`Failed to log security event: ${error.message}`, error.stack);
        });
      }
    }
    
    return isMatch;
  }
}

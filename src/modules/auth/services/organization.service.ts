import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { Organization, OrganizationDocument } from '../schemas/organization.schema';

@Injectable()
export class OrganizationService {
  constructor(
    @InjectModel(Organization.name) private organizationModel: Model<OrganizationDocument>,
  ) {}

  async create(name: string, description?: string): Promise<Organization> {
    const id = uuidv4();
    const organization = await this.organizationModel.create({
      id,
      name,
      description,
      settings: {},
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    return organization;
  }

  async findById(id: string): Promise<Organization | null> {
    return this.organizationModel.findOne({ id }).exec();
  }

  async findAll(): Promise<Organization[]> {
    return this.organizationModel.find({}).exec();
  }

  async update(id: string, updateData: Partial<Organization>): Promise<Organization | null> {
    const organization = await this.organizationModel.findOneAndUpdate(
      { id },
      { ...updateData, updatedAt: new Date() },
      { new: true }
    ).exec();
    
    return organization;
  }

  async delete(id: string): Promise<boolean> {
    const result = await this.organizationModel.deleteOne({ id }).exec();
    return result.deletedCount > 0;
  }
}

import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { User } from '../schemas/user.schema';
import { CreateUserDto, UpdateUserDto } from '../dto/user.dto';
import { UserRole } from '../enums/roles.enum';
import * as bcrypt from 'bcrypt';
import { BadRequestException } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class UserService {
  constructor(
    @InjectModel(User.name) private readonly userModel: Model<User>,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    const passwordHash = await bcrypt.hash(createUserDto.password, 10);
    
    // Check if email already exists in the organization
    if (createUserDto.organizationId) {
      const existingUser = await this.userModel.findOne({ 
        email: createUserDto.email, 
        organizationId: createUserDto.organizationId 
      });
      
      if (existingUser) {
        throw new BadRequestException('Email already exists in this organization');
      }
    }
    
    // Generate a unique ID for the user
    const id = uuidv4();
    
    // Use provided username or generate one from email
    const username = createUserDto.username || createUserDto.email.split('@')[0];
    
    // Check if username is already taken within this organization
    if (createUserDto.organizationId) {
      const existingUsername = await this.userModel.findOne({
        username,
        organizationId: createUserDto.organizationId
      });
      
      if (existingUsername) {
        // If username is taken, append a random string to make it unique
        const randomSuffix = Math.random().toString(36).substring(2, 6);
        createUserDto.username = `${username}_${randomSuffix}`;
      }
    }
    
    const newUser = await this.userModel.create({
      id, // Set the generated ID
      username: createUserDto.username || username, // Use the potentially modified username
      email: createUserDto.email,
      firstName: createUserDto.firstName,
      lastName: createUserDto.lastName,
      passwordHash,
      role: createUserDto.role || UserRole.USER,
      organizationId: createUserDto.organizationId, // Use the organizationId from the DTO
      emailVerified: false,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return newUser;
  }

  async findByOrganization(organizationId: string): Promise<User[]> {
    return this.userModel.find({ organizationId });
  }

  async findByEmail(email: string, organizationId: string): Promise<User | null> {
    return this.userModel.findOne({ email, organizationId });
  }

  async findByEmailOnly(email: string): Promise<User | null> {
    // Find user by email without organization context for login
    return this.userModel.findOne({ email });
  }

  async findById(id: string, organizationId: string): Promise<User | null> {
    return this.userModel.findOne({ id, organizationId });
  }

  async findByIdOnly(id: string): Promise<User | null> {
    return this.userModel.findOne({ id });
  }

  async update(id: string, organizationId: string, updateDto: UpdateUserDto): Promise<User | null> {
    return this.userModel.findOneAndUpdate(
      { id, organizationId },
      { ...updateDto },
      { new: true }
    );
  }

  async verifyEmail(id: string, organizationId: string): Promise<User | null> {
    return this.userModel.findOneAndUpdate(
      { id, organizationId },
      { emailVerified: true },
      { new: true }
    );
  }

  async updateTwoFactorSecret(id: string, secret: string): Promise<User | null> {
    return this.userModel.findOneAndUpdate(
      { id },
      { 
        twoFactorSecret: secret,
        twoFactorVerified: false
      },
      { new: true }
    );
  }

  async markTwoFactorVerified(id: string): Promise<User | null> {
    return this.userModel.findOneAndUpdate(
      { id },
      { twoFactorVerified: true },
      { new: true }
    );
  }

  async enableTwoFactor(id: string): Promise<User | null> {
    return this.userModel.findOneAndUpdate(
      { id },
      { twoFactorEnabled: true },
      { new: true }
    );
  }

  async disableTwoFactor(id: string): Promise<User | null> {
    return this.userModel.findOneAndUpdate(
      { id },
      { 
        twoFactorEnabled: false,
        twoFactorVerified: false,
        twoFactorSecret: null
      },
      { new: true }
    );
  }

  async updateLastLogin(id: string, organizationId: string): Promise<User | null> {
    return this.userModel.findOneAndUpdate(
      { id, organizationId },
      { lastLoginAt: new Date() },
      { new: true }
    );
  }

  async findByGoogleId(googleId: string): Promise<User | null> {
    return this.userModel.findOne({ googleId });
  }

  async updateGoogleInfo(
    id: string,
    googleInfo: { googleId: string; isGoogleUser: boolean; picture?: string }
  ): Promise<User | null> {
    return this.userModel.findOneAndUpdate(
      { id },
      { 
        googleId: googleInfo.googleId,
        isGoogleUser: googleInfo.isGoogleUser,
        picture: googleInfo.picture,
        updatedAt: new Date()
      },
      { new: true }
    );
  }

  async createGoogleUser(userData: {
    email: string;
    firstName: string;
    lastName: string;
    picture?: string;
    googleId: string;
    organizationId: string;
    username: string;
  }): Promise<User> {
    // Check if email already exists
    const existingUser = await this.findByEmailOnly(userData.email);
    if (existingUser) {
      throw new BadRequestException('Email already exists');
    }
    
    // Generate a unique ID for the user
    const id = uuidv4();
    
    // Check if username is already taken within this organization
    const existingUsername = await this.userModel.findOne({
      username: userData.username,
      organizationId: userData.organizationId
    });
    
    // If username is taken, append a random string to make it unique
    if (existingUsername) {
      const randomSuffix = Math.random().toString(36).substring(2, 6);
      userData.username = `${userData.username}_${randomSuffix}`;
    }
    
    const newUser = await this.userModel.create({
      id,
      username: userData.username,
      email: userData.email,
      firstName: userData.firstName,
      lastName: userData.lastName,
      passwordHash: null, // Google users don't have a password
      role: UserRole.USER,
      organizationId: userData.organizationId,
      emailVerified: true, // Google emails are verified
      isActive: true,
      googleId: userData.googleId,
      isGoogleUser: true,
      picture: userData.picture,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return newUser;
  }
}

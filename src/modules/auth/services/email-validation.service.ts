import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class EmailValidationService {
  private readonly logger = new Logger(EmailValidationService.name);
  private forwardingDomains: string[] = [];
  private customBlockedDomains: string[] = [];
  private allowedDomains: string[] = [];
  private useAllowlist: boolean = false;

  constructor(private readonly configService: ConfigService) {
    this.loadForwardingDomains();
    this.loadCustomBlockedDomains();
    this.loadAllowedDomains();
  }

  /**
   * Load the list of known email forwarding domains from the file
   */
  private loadForwardingDomains(): void {
    try {
      // Load the built-in list of forwarding domains
      const filePath = path.join(
        __dirname,
        '../../../../data/forwarding-domains.txt',
      );
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8');
        this.forwardingDomains = content
          .split('\n')
          .map((line) => line.trim().toLowerCase())
          .filter((line) => line && !line.startsWith('#'));
        this.logger.log(
          `Loaded ${this.forwardingDomains.length} forwarding domains from built-in list`,
        );
      } else {
        this.logger.warn(`Forwarding domains file not found at ${filePath}`);
        // Fallback to a minimal list of common forwarding domains
        this.forwardingDomains = [
          'forward.cat',
          'forward.email',
          'forwardemail.net',
          'temporarymail.com',
          'emailfake.com',
          'emailtemp.org',
          'tempmail.com',
          'temp-mail.org',
          'fakeinbox.com',
          'guerrillamail.com',
          'mailinator.com',
          'spamgourmet.com',
          'trashmail.com',
          'yopmail.com',
          'getnada.com',
          '10minutemail.com',
          'mailnesia.com',
          'tempr.email',
          'tempmail.net',
          'dispostable.com',
          'passmail.net',
        ];
        this.logger.log(
          `Using fallback list with ${this.forwardingDomains.length} common forwarding domains`,
        );
      }
    } catch (error) {
      this.logger.error(`Error loading forwarding domains: ${error.message}`);
      this.forwardingDomains = [];
    }
  }

  /**
   * Load custom blocked domains from environment or configuration
   */
  private loadCustomBlockedDomains(): void {
    try {
      const customBlockedDomainsStr = this.configService.get<string>(
        'EMAIL_BLOCKED_DOMAINS',
        '',
      );
      if (customBlockedDomainsStr) {
        this.customBlockedDomains = customBlockedDomainsStr
          .split(',')
          .map((domain) => domain.trim().toLowerCase())
          .filter(Boolean);
        this.logger.log(
          `Loaded ${this.customBlockedDomains.length} custom blocked domains from configuration`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error loading custom blocked domains: ${error.message}`,
      );
      this.customBlockedDomains = [];
    }
  }

  /**
   * Load allowed domains from environment or configuration
   */
  private loadAllowedDomains(): void {
    try {
      const allowedDomainsStr = this.configService.get<string>(
        'EMAIL_ALLOWED_DOMAINS',
        '',
      );
      this.useAllowlist = this.configService.get<boolean>(
        'EMAIL_USE_ALLOWLIST',
        false,
      );

      if (allowedDomainsStr) {
        this.allowedDomains = allowedDomainsStr
          .split(',')
          .map((domain) => domain.trim().toLowerCase())
          .filter(Boolean);
        this.logger.log(
          `Loaded ${this.allowedDomains.length} allowed domains from configuration`,
        );

        if (this.useAllowlist) {
          this.logger.log(
            'Using allowlist mode - only emails from allowed domains will be accepted',
          );
        }
      }
    } catch (error) {
      this.logger.error(`Error loading allowed domains: ${error.message}`);
      this.allowedDomains = [];
    }
  }

  /**
   * Check if an email is from a forwarding service
   * @param email The email address to check
   * @returns True if the email is from a forwarding service, false otherwise
   */
  isForwardingEmail(email: string): boolean {
    if (!email || !email.includes('@')) {
      return false;
    }

    const domain = email.split('@')[1].toLowerCase();
    return this.forwardingDomains.includes(domain);
  }

  /**
   * Check if an email is from a custom blocked domain
   * @param email The email address to check
   * @returns True if the email is from a custom blocked domain, false otherwise
   */
  isCustomBlockedDomain(email: string): boolean {
    if (!email || !email.includes('@')) {
      return false;
    }

    const domain = email.split('@')[1].toLowerCase();
    return this.customBlockedDomains.includes(domain);
  }

  /**
   * Check if an email is from an allowed domain (when using allowlist mode)
   * @param email The email address to check
   * @returns True if the email is from an allowed domain, false otherwise
   */
  isAllowedDomain(email: string): boolean {
    if (!this.useAllowlist || this.allowedDomains.length === 0) {
      return true; // If not using allowlist mode, all domains are allowed
    }

    if (!email || !email.includes('@')) {
      return false;
    }

    const domain = email.split('@')[1].toLowerCase();
    return this.allowedDomains.includes(domain);
  }

  /**
   * Validate if an email is allowed based on all rules
   * @param email The email address to validate
   * @returns True if the email is allowed, false otherwise
   */
  isEmailAllowed(email: string): boolean {
    // Check if email is valid format
    if (!email || !email.includes('@')) {
      return false;
    }

    // If using allowlist mode, check if domain is allowed
    if (this.useAllowlist && !this.isAllowedDomain(email)) {
      return false;
    }

    // Check if email is from a forwarding service
    if (this.isForwardingEmail(email)) {
      return false;
    }

    // Check if email is from a custom blocked domain
    if (this.isCustomBlockedDomain(email)) {
      return false;
    }

    return true;
  }

  /**
   * Get the reason why an email is not allowed
   * @param email The email address to check
   * @returns A string explaining why the email is not allowed, or null if it is allowed
   */
  getEmailRejectionReason(email: string): string | null {
    // Check if email is valid format
    if (!email || !email.includes('@')) {
      return 'Invalid email format';
    }

    // If using allowlist mode, check if domain is allowed
    if (this.useAllowlist && !this.isAllowedDomain(email)) {
      return 'Email domain not in the allowed domains list';
    }

    // Check if email is from a forwarding service
    if (this.isForwardingEmail(email)) {
      return 'Email forwarding services are not allowed';
    }

    // Check if email is from a custom blocked domain
    if (this.isCustomBlockedDomain(email)) {
      return 'Email domain is blocked';
    }

    return null; // Email is allowed
  }
}

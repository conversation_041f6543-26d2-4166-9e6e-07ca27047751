import {
  Injectable,
  UnauthorizedException,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UserService } from './user.service';
import { OrganizationService } from './organization.service';
import { User } from '../schemas/user.schema';
import { CreateUserDto } from '../dto/user.dto';
import { AuthResponseDto } from '../dto/auth.dto';
import * as bcrypt from 'bcrypt';
import { EmailService } from './email.service';
import { VerificationTokenService } from './verification-token.service';
import { EmailValidationService } from './email-validation.service';

@Injectable()
export class AuthService {
  constructor(
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
    private readonly organizationService: OrganizationService,
    private readonly emailService: EmailService,
    private readonly verificationTokenService: VerificationTokenService,
    private readonly emailValidationService: EmailValidationService,
  ) {}

  public async validateUser(
    email: string,
    password: string,
  ): Promise<User | null> {
    // Check if email is from a forwarding service
    if (!this.emailValidationService.isEmailAllowed(email)) {
      const reason = this.emailValidationService.getEmailRejectionReason(email);
      throw new BadRequestException(`Login not allowed: ${reason}`);
    }

    console.log('Validating user with email:', email);
    const user = await this.userService.findByEmailOnly(email);
    if (!user) {
      console.error('User not found for email:', email);
    } else {
      console.log('User found:', { id: user.id, email: user.email });
    }
    const passwordMatches = user ? await bcrypt.compare(password, user.passwordHash) : false;
    console.log('Password match result:', passwordMatches);
    if (user && passwordMatches) {
      return user;
    }
    return null;
  }

  public async login(user: User): Promise<AuthResponseDto> {
    const payload = this.createJwtPayload(user);
    await this.userService.updateLastLogin(user.id, user.organizationId);
    console.log('JWT Payload:', payload);
    console.log('JWT Service:', this.jwtService);
    console.log('JWT Module Options:', (this.jwtService as any).moduleOptions);
    console.log('JWT Options:', (this.jwtService as any).options);
    const token = this.jwtService.sign(payload);
    return this.createAuthResponse(user, token);
  }

  public async register(
    createUserDto: CreateUserDto,
  ): Promise<AuthResponseDto> {
    // Check if email is from a forwarding service
    if (!this.emailValidationService.isEmailAllowed(createUserDto.email)) {
      const reason = this.emailValidationService.getEmailRejectionReason(createUserDto.email);
      throw new BadRequestException(`Registration not allowed: ${reason}`);
    }

    const organizationId = await this.handleOrganization(createUserDto);
    const user = await this.userService.create({
      ...createUserDto,
      organizationId,
    });

    // Generate verification token and send email
    const token = await this.verificationTokenService.createToken(user.id, 'email');
    await this.emailService.sendVerificationEmail(user.email, token);

    const payload = this.createJwtPayload(user);
    const jwtToken = this.jwtService.sign(payload);
    return this.createAuthResponse(user, jwtToken);
  }

  public async verifyEmail(token: string): Promise<boolean> {
    const userId = await this.verificationTokenService.validateToken(token, 'email');
    
    if (!userId) {
      return false;
    }
    
    // Find the user without organization context
    const user = await this.userService.findByIdOnly(userId);
    
    if (!user) {
      return false;
    }
    
    // Update user's email verification status
    await this.userService.verifyEmail(userId, user.organizationId);
    
    return true;
  }

  public async resendVerificationEmail(email: string): Promise<boolean> {
    const user = await this.userService.findByEmailOnly(email);
    
    if (!user) {
      throw new NotFoundException('User not found');
    }
    
    if (user.emailVerified) {
      throw new BadRequestException('Email is already verified');
    }
    
    // Generate new verification token
    const token = await this.verificationTokenService.createToken(user.id, 'email');
    
    // Send verification email
    return this.emailService.sendVerificationEmail(user.email, token);
  }

  public async validateToken(token: string): Promise<any> {
    try {
      return await this.jwtService.verifyAsync(token);
    } catch (error) {
      throw new UnauthorizedException('Invalid token');
    }
  }

  public async validateOrCreateGoogleUser(googleUserData: {
    email: string;
    firstName: string;
    lastName: string;
    picture: string;
    googleId: string;
  }): Promise<User> {
    // Check if user exists by Google ID
    let user = await this.userService.findByGoogleId(googleUserData.googleId);
    
    // If not found by Google ID, try to find by email
    if (!user) {
      user = await this.userService.findByEmailOnly(googleUserData.email);
      
      // If user exists but is not a Google user, update it to link with Google
      if (user && !user.isGoogleUser) {
        user = await this.userService.updateGoogleInfo(user.id, {
          googleId: googleUserData.googleId,
          isGoogleUser: true,
          picture: googleUserData.picture,
        });
      }
    }
    
    // If user doesn't exist at all, create a new one
    if (!user) {
      // Create a new organization for the user
      const organizationName = `${googleUserData.firstName}'s Workspace`;
      const organization = await this.organizationService.create(organizationName);
      
      // Create the user with Google info
      user = await this.userService.createGoogleUser({
        email: googleUserData.email,
        firstName: googleUserData.firstName,
        lastName: googleUserData.lastName,
        picture: googleUserData.picture,
        googleId: googleUserData.googleId,
        organizationId: organization.id,
        username: googleUserData.email.split('@')[0], // Use part of email as username
      });
    }
    
    return user;
  }

  public async getGoogleAuthUrl(): Promise<string> {
    const config = {
      clientID: process.env.GOOGLE_CLIENT_ID,
      redirectUri: process.env.GOOGLE_CALLBACK_URL,
      responseType: 'code',
      scope: 'email profile',
    };
    
    const queryParams = new URLSearchParams({
      client_id: config.clientID,
      redirect_uri: config.redirectUri,
      response_type: config.responseType,
      scope: config.scope,
    });
    
    return `https://accounts.google.com/o/oauth2/v2/auth?${queryParams.toString()}`;
  }

  private async handleOrganization(
    createUserDto: CreateUserDto,
  ): Promise<string> {
    if (createUserDto.createNewOrganization !== false) {
      const name =
        createUserDto.organizationName ||
        `${createUserDto.firstName}'s Workspace`;
      const organization = await this.organizationService.create(name);
      return organization.id;
    }

    if (!createUserDto.organizationId) {
      throw new BadRequestException(
        'Organization ID is required when not creating a new organization',
      );
    }

    const organization = await this.organizationService.findById(
      createUserDto.organizationId,
    );
    if (!organization) {
      throw new BadRequestException('Invalid organization ID');
    }

    return createUserDto.organizationId;
  }

  private createJwtPayload(user: User) {
    return {
      sub: user.id,
      email: user.email,
      organizationId: user.organizationId,
      role: user.role,
    };
  }

  private createAuthResponse(user: User, token: string): AuthResponseDto {
    return {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        organizationId: user.organizationId,
        emailVerified: user.emailVerified,
        ...(user.isGoogleUser !== undefined && { isGoogleUser: user.isGoogleUser }),
        ...(user.picture && { picture: user.picture }),
      },
      token,
    };
  }
}

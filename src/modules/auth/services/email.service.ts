import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { IEmailProvider, EmailMessage } from '../interfaces/email-provider.interface';
import { EmailProviderFactory } from '../factories/email-provider.factory';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private emailProvider: IEmailProvider | null = null;

  constructor(
    private readonly configService: ConfigService,
    private readonly emailProviderFactory: EmailProviderFactory,
  ) {
    // Initialize the email provider
    this.initializeEmailProvider();
  }

  private async initializeEmailProvider(): Promise<void> {
    try {
      this.emailProvider = await this.emailProviderFactory.createProvider();
      if (this.emailProvider) {
        this.logger.log(`Email provider initialized: ${this.emailProvider.getProviderName()}`);
      } else {
        this.logger.warn('No email provider could be initialized. Email functionality will be disabled.');
      }
    } catch (error) {
      this.logger.error(`Failed to initialize email provider: ${error.message}`, error.stack);
    }
  }

  async sendVerificationEmail(email: string, token: string): Promise<boolean> {
    if (!this.emailProvider) {
      this.logger.error('Email provider not initialized. Cannot send verification email.');
      return false;
    }

    const frontendUrl = this.configService.get<string>('FRONTEND_URL', 'http://localhost:3000');
    const verificationUrl = `${frontendUrl}/verify-email?token=${token}`;

    // Email content
    const subject = 'Verify your Legal Document Analyzer account';
    const text = `Please verify your email by clicking this link: ${verificationUrl}`;
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <h1 style="color: #2c3e50; text-align: center;">Welcome to Legal Document Analyzer</h1>
        <p style="font-size: 16px; line-height: 1.5; color: #333;">Thank you for registering. Please verify your email address to activate your account.</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${verificationUrl}" style="background-color: #3498db; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Verify Email</a>
        </div>
        <p style="font-size: 14px; color: #7f8c8d;">If the button doesn't work, copy and paste this link into your browser:</p>
        <p style="font-size: 14px; color: #7f8c8d; word-break: break-all;">${verificationUrl}</p>
        <p style="font-size: 14px; color: #7f8c8d;">This link will expire in 24 hours.</p>
        <hr style="border: none; border-top: 1px solid #e0e0e0; margin: 20px 0;">
        <p style="font-size: 12px; color: #95a5a6; text-align: center;">Legal Document Analyzer - Secure Document Analysis for Legal Professionals</p>
      </div>
    `;
    
    const emailMessage: EmailMessage = {
      to: email,
      subject,
      htmlContent: html,
      textContent: text,
    };

    try {
      const result = await this.emailProvider.sendEmail(emailMessage);

      if (result.success) {
        this.logger.log(`Verification email sent to ${email} via ${this.emailProvider.getProviderName()}`);
        return true;
      } else {
        this.logger.error(`Failed to send verification email to ${email}: ${result.error}`);
        return false;
      }
    } catch (error) {
      this.logger.error(`Failed to send verification email to ${email}`, error);
      return false;
    }
  }

  async sendPasswordResetEmail(email: string, token: string): Promise<boolean> {
    if (!this.emailProvider) {
      this.logger.error('Email provider not initialized. Cannot send password reset email.');
      return false;
    }

    const frontendUrl = this.configService.get<string>('FRONTEND_URL', 'http://localhost:3000');
    const resetUrl = `${frontendUrl}/reset-password?token=${token}`;

    // Email content
    const subject = 'Reset your Legal Document Analyzer password';
    const text = `Please reset your password by clicking this link: ${resetUrl}`;
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
        <h1 style="color: #2c3e50; text-align: center;">Legal Document Analyzer</h1>
        <p style="font-size: 16px; line-height: 1.5; color: #333;">We received a request to reset your password. Click the button below to create a new password:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${resetUrl}" style="background-color: #3498db; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Reset Password</a>
        </div>
        <p style="font-size: 14px; color: #7f8c8d;">If the button doesn't work, copy and paste this link into your browser:</p>
        <p style="font-size: 14px; color: #7f8c8d; word-break: break-all;">${resetUrl}</p>
        <p style="font-size: 14px; color: #7f8c8d;">This link will expire in 1 hour.</p>
        <p style="font-size: 14px; color: #7f8c8d;">If you didn't request a password reset, you can safely ignore this email.</p>
        <hr style="border: none; border-top: 1px solid #e0e0e0; margin: 20px 0;">
        <p style="font-size: 12px; color: #95a5a6; text-align: center;">Legal Document Analyzer - Secure Document Analysis for Legal Professionals</p>
      </div>
    `;
    
    const emailMessage: EmailMessage = {
      to: email,
      subject,
      htmlContent: html,
      textContent: text,
    };

    try {
      const result = await this.emailProvider.sendEmail(emailMessage);

      if (result.success) {
        this.logger.log(`Password reset email sent to ${email} via ${this.emailProvider.getProviderName()}`);
        return true;
      } else {
        this.logger.error(`Failed to send password reset email to ${email}: ${result.error}`);
        return false;
      }
    } catch (error) {
      this.logger.error(`Failed to send password reset email to ${email}`, error);
      return false;
    }
  }
}

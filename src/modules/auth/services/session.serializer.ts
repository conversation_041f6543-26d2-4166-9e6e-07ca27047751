import { Injectable } from '@nestjs/common';
import { PassportSerializer } from '@nestjs/passport';
import { UserService } from './user.service';

@Injectable()
export class SessionSerializer extends PassportSerializer {
  constructor(private readonly userService: UserService) {
    super();
  }

  serializeUser(user: any, done: (err: Error, user: any) => void): any {
    // Store user ID in session
    done(null, { id: user._id || user.id, email: user.email });
  }

  async deserializeUser(
    payload: { id: string; email: string },
    done: (err: Error, user: any) => void,
  ): Promise<any> {
    try {
      // Retrieve user from database using the ID stored in session
      const user = await this.userService.findByIdOnly(payload.id);
      done(null, user);
    } catch (error) {
      done(error, null);
    }
  }
} 
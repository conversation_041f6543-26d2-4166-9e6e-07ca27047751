import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { VerificationToken } from '../schemas/verification-token.schema';
import { v4 as uuidv4 } from 'uuid';
import * as crypto from 'crypto';

@Injectable()
export class VerificationTokenService {
  private readonly logger = new Logger(VerificationTokenService.name);

  constructor(
    @InjectModel(VerificationToken.name) private readonly tokenModel: Model<VerificationToken>,
  ) {}

  /**
   * Create a new verification token
   * @param userId The user's ID
   * @param type The token type (email, password-reset, etc.)
   * @param expiresInHours How many hours until the token expires (default: 24)
   * @returns The generated token string
   */
  async createToken(userId: string, type: string, expiresInHours = 24): Promise<string> {
    // Generate a secure random token
    const token = crypto.randomBytes(32).toString('hex');
    
    // Calculate expiration date
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + expiresInHours);
    
    // Delete any existing tokens of the same type for this user
    await this.tokenModel.deleteMany({ userId, type });
    
    // Create a new token
    await this.tokenModel.create({
      id: uuidv4(),
      userId,
      token,
      type,
      expiresAt,
    });
    
    this.logger.debug(`Created ${type} token for user ${userId}, expires in ${expiresInHours} hours`);
    
    return token;
  }

  /**
   * Validate a token and return the associated user ID if valid
   * @param token The token string to validate
   * @param type The token type (email, password-reset, etc.)
   * @returns The user ID if valid, null otherwise
   */
  async validateToken(token: string, type: string): Promise<string | null> {
    // Find the token
    const verificationToken = await this.tokenModel.findOne({
      token,
      type,
      expiresAt: { $gt: new Date() }, // Not expired
    });
    
    if (!verificationToken) {
      this.logger.debug(`Invalid or expired ${type} token`);
      return null;
    }
    
    // Delete the token (one-time use)
    await this.tokenModel.deleteOne({ id: verificationToken.id });
    
    this.logger.debug(`Validated ${type} token for user ${verificationToken.userId}`);
    
    return verificationToken.userId;
  }

  /**
   * Delete all tokens for a specific user and type
   * @param userId The user's ID
   * @param type The token type (email, password-reset, etc.)
   */
  async deleteTokens(userId: string, type: string): Promise<void> {
    await this.tokenModel.deleteMany({ userId, type });
    this.logger.debug(`Deleted all ${type} tokens for user ${userId}`);
  }
}

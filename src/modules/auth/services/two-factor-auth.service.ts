import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { UserService } from './user.service';
import { authenticator } from 'otplib';
import * as QRCode from 'qrcode';
import { TenantContextService } from './tenant-context.service';

@Injectable()
export class TwoFactorAuthService {
  constructor(
    private readonly userService: UserService,
    private readonly tenantContextService: TenantContextService
  ) {}

  /**
   * Generate a new 2FA secret for a user
   * @param userId The user's ID
   * @returns Object containing the secret and otpAuthUrl for QR code generation
   */
  async generateSecret(userId: string): Promise<{ secret: string; otpAuthUrl: string; qrCodeDataUrl: string }> {
    const organizationId = this.tenantContextService.getCurrentOrganization();
    if (!organizationId) {
      throw new BadRequestException('Organization context not found');
    }
    
    const user = await this.userService.findById(userId, organizationId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Generate a secret
    const secret = authenticator.generateSecret();
    
    // Create the OTP Auth URL (for QR code)
    const appName = 'Legal Document Analyzer';
    const otpAuthUrl = authenticator.keyuri(user.email, appName, secret);
    
    // Generate QR code
    const qrCodeDataUrl = await QRCode.toDataURL(otpAuthUrl);
    
    // Save the secret to the user (but don't enable 2FA yet)
    await this.userService.updateTwoFactorSecret(userId, secret);
    
    return {
      secret,
      otpAuthUrl,
      qrCodeDataUrl,
    };
  }

  /**
   * Verify a TOTP code against a user's secret
   * @param userId The user's ID
   * @param code The TOTP code to verify
   * @returns Boolean indicating if the code is valid
   */
  async verifyCode(userId: string, code: string): Promise<boolean> {
    const organizationId = this.tenantContextService.getCurrentOrganization();
    if (!organizationId) {
      throw new BadRequestException('Organization context not found');
    }
    
    const user = await this.userService.findById(userId, organizationId);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    
    if (!user.twoFactorSecret) {
      throw new BadRequestException('Two-factor authentication is not set up');
    }
    
    // Verify the code
    const isValid = authenticator.verify({
      token: code,
      secret: user.twoFactorSecret,
    });
    
    // If valid, mark the 2FA as verified
    if (isValid) {
      await this.userService.markTwoFactorVerified(userId);
    }
    
    return isValid;
  }

  /**
   * Enable 2FA for a user after successful verification
   * @param userId The user's ID
   */
  async enableTwoFactor(userId: string): Promise<void> {
    const organizationId = this.tenantContextService.getCurrentOrganization();
    if (!organizationId) {
      throw new BadRequestException('Organization context not found');
    }
    
    const user = await this.userService.findById(userId, organizationId);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    
    if (!user.twoFactorVerified) {
      throw new BadRequestException('Two-factor authentication must be verified before enabling');
    }
    
    await this.userService.enableTwoFactor(userId);
  }

  /**
   * Disable 2FA for a user
   * @param userId The user's ID
   */
  async disableTwoFactor(userId: string): Promise<void> {
    const organizationId = this.tenantContextService.getCurrentOrganization();
    if (!organizationId) {
      throw new BadRequestException('Organization context not found');
    }
    
    const user = await this.userService.findById(userId, organizationId);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    
    await this.userService.disableTwoFactor(userId);
  }
}

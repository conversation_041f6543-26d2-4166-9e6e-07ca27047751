import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { IEmailProvider } from '../interfaces/email-provider.interface';
import { BrevoEmailProvider } from '../providers/brevo-email.provider';
import { SmtpEmailProvider } from '../providers/smtp-email.provider';

export type EmailProviderType = 'brevo' | 'smtp';

@Injectable()
export class EmailProviderFactory {
  private readonly logger = new Logger(EmailProviderFactory.name);

  constructor(
    private configService: ConfigService,
    private brevoProvider: BrevoEmailProvider,
    private smtpProvider: SmtpEmailProvider,
  ) {}

  async createProvider(): Promise<IEmailProvider | null> {
    const providerType = this.configService.get<EmailProviderType>('EMAIL_PROVIDER', 'brevo');
    
    this.logger.log(`Creating email provider: ${providerType}`);

    try {
      switch (providerType) {
        case 'brevo':
          return await this.createBrevoProvider();
        
        case 'smtp':
          return await this.createSmtpProvider();
        
        default:
          this.logger.error(`Unknown email provider type: ${providerType}`);
          return null;
      }
    } catch (error) {
      this.logger.error(`Failed to create email provider: ${error.message}`, error.stack);
      return null;
    }
  }

  private async createBrevoProvider(): Promise<IEmailProvider> {
    const config = {
      apiKey: this.configService.get<string>('BREVO_API_KEY'),
      senderName: this.configService.get<string>('EMAIL_SENDER_NAME', 'DocGic'),
      senderEmail: this.configService.get<string>('EMAIL_SENDER_EMAIL', '<EMAIL>'),
    };

    if (!config.apiKey) {
      throw new Error('BREVO_API_KEY environment variable is required for Brevo provider');
    }

    await this.brevoProvider.initialize(config);
    
    // Verify the provider
    const isValid = await this.brevoProvider.verify();
    if (!isValid) {
      throw new Error('Brevo provider verification failed');
    }

    return this.brevoProvider;
  }

  private async createSmtpProvider(): Promise<IEmailProvider> {
    const config = {
      host: this.configService.get<string>('EMAIL_HOST'),
      port: this.configService.get<string>('EMAIL_PORT'),
      user: this.configService.get<string>('EMAIL_USER'),
      password: this.configService.get<string>('EMAIL_PASSWORD'),
      secure: this.configService.get<string>('EMAIL_SECURE', 'false'),
      senderName: this.configService.get<string>('EMAIL_SENDER_NAME', 'DocGic'),
      senderEmail: this.configService.get<string>('EMAIL_SENDER_EMAIL'),
    };

    if (!config.host || !config.port || !config.user || !config.password) {
      throw new Error('SMTP configuration incomplete: EMAIL_HOST, EMAIL_PORT, EMAIL_USER, and EMAIL_PASSWORD are required');
    }

    await this.smtpProvider.initialize(config);
    
    // Verify the provider
    const isValid = await this.smtpProvider.verify();
    if (!isValid) {
      throw new Error('SMTP provider verification failed');
    }

    return this.smtpProvider;
  }
}

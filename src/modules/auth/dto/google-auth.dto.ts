import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class GoogleUserDto {
  @ApiProperty({
    description: 'User email from Google',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'User first name from Google',
    example: '<PERSON>',
  })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({
    description: 'User last name from Google',
    example: '<PERSON><PERSON>',
  })
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({
    description: 'User profile picture URL from Google',
    example: 'https://lh3.googleusercontent.com/a/profile-picture',
  })
  @IsString()
  @IsOptional()
  picture?: string;

  @ApiProperty({
    description: 'Google user ID',
    example: '123456789012345678901',
  })
  @IsString()
  @IsNotEmpty()
  googleId: string;
}

export class GoogleAuthUrlDto {
  @ApiProperty({
    description: 'URL to redirect the user for Google authentication',
    example: 'https://accounts.google.com/o/oauth2/v2/auth?...',
  })
  url: string;
}

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsEnum, IsBoolean, IsO<PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { UserRole } from '../enums/roles.enum';

export class CreateUserDto {
  @ApiProperty({ example: '<EMAIL>', description: 'User email address' })
  @IsEmail({}, { message: 'Please provide a valid email address' })
  email: string;

  @ApiProperty({ example: 'password123', description: 'User password' })
  @IsString()
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  password: string;

  @ApiProperty({ example: 'johndoe', description: 'Optional username (will be generated from email if not provided)', required: false })
  @IsOptional()
  @IsString()
  username?: string;

  @ApiProperty({ example: 'John', description: 'User first name' })
  @IsString()
  firstName: string;

  @ApiProperty({ example: '<PERSON><PERSON>', description: 'User last name' })
  @IsString()
  lastName: string;

  @ApiProperty({ 
    example: 'org123', 
    description: 'ID of an existing organization to join (only required if createNewOrganization is false)',
    required: false
  })
  @IsOptional()
  @IsString()
  organizationId?: string;

  @ApiProperty({ 
    example: 'My Organization', 
    description: "Name for the user's new organization (only used if createNewOrganization is true)",
    required: false
  })
  @IsOptional()
  @IsString()
  organizationName?: string;

  @ApiProperty({ 
    example: true, 
    description: 'Whether to create a new organization for this user (defaults to true)',
    default: true,
    required: false
  })
  @IsOptional()
  @IsBoolean()
  createNewOrganization?: boolean = true;

  @IsEnum(UserRole)
  @IsOptional()
  role?: UserRole;
}

export class UpdateUserDto {
  @IsString()
  @IsOptional()
  firstName?: string;

  @IsString()
  @IsOptional()
  lastName?: string;

  @IsEnum(UserRole)
  @IsOptional()
  role?: UserRole;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsObject()
  @IsOptional()
  preferences?: Record<string, any>;
}

export class UserResponseDto {
  id: string;
  organizationId: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  emailVerified: boolean;
  isActive: boolean;
  preferences: Record<string, any>;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export class LoginResponseDto {
  user: UserResponseDto;
  accessToken: string;
  refreshToken: string;
}

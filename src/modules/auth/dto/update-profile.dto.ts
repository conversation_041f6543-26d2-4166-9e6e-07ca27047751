import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEmail, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ength } from 'class-validator';

/**
 * DTO for updating user profile
 */
export class UpdateProfileDto {
  @ApiProperty({
    description: 'User first name',
    example: '<PERSON>',
    required: false
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(50)
  firstName?: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Doe',
    required: false
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(50)
  lastName?: string;

  @ApiProperty({
    description: 'User profile picture URL',
    example: 'https://example.com/profile.jpg',
    required: false
  })
  @IsOptional()
  @IsString()
  picture?: string;
}

import { IsE<PERSON>, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for resending verification email
 */
export class ResendVerificationDto {
  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>'
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;
}

/**
 * DTO for verification response
 */
export class VerificationResponseDto {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true
  })
  success: boolean;

  @ApiProperty({
    description: 'Message describing the result',
    example: 'Email verified successfully'
  })
  message: string;
}

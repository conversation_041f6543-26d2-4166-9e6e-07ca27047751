import { ApiProperty } from '@nestjs/swagger';

export class UserResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  firstName: string;

  @ApiProperty()
  lastName: string;

  @ApiProperty()
  role: string;

  @ApiProperty()
  organizationId: string;

  @ApiProperty({ description: 'Whether the user\'s email has been verified' })
  emailVerified: boolean;

  @ApiProperty({ required: false, description: 'Whether the user authenticated with Google' })
  isGoogleUser?: boolean;

  @ApiProperty({ required: false, description: 'User profile picture URL from Google' })
  picture?: string;
}

export class AuthResponseDto {
  @ApiProperty({ type: UserResponseDto })
  user: UserResponseDto;

  @ApiProperty()
  token: string;
}
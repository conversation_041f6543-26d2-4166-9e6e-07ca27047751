import { IsS<PERSON>, <PERSON><PERSON><PERSON>, MinLength } from 'class-validator';

export class LoginDto {
  @IsEmail()
  email: string;

  @IsString()
  @MinLength(6)
  password: string;
}

export interface AuthResponseDto {
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: string;
    organizationId: string;
    emailVerified: boolean;
    isGoogleUser?: boolean;
    picture?: string;
  };
  token: string;
}

export interface JwtPayload {
  sub: string;
  email: string;
  organizationId: string;
  role: string;
}
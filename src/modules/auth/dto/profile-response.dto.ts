import { ApiProperty } from '@nestjs/swagger';
import { UserRole } from '../enums/roles.enum';

/**
 * DTO for user profile response
 */
export class ProfileResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  id: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>'
  })
  email: string;

  @ApiProperty({
    description: 'User first name',
    example: '<PERSON>'
  })
  firstName: string;

  @ApiProperty({
    description: 'User last name',
    example: 'Doe'
  })
  lastName: string;

  @ApiProperty({
    description: 'User role',
    enum: UserRole,
    example: UserRole.USER
  })
  role: string;

  @ApiProperty({
    description: 'Organization ID the user belongs to',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  organizationId: string;

  @ApiProperty({
    description: 'Organization name',
    example: '<PERSON>\'s Workspace'
  })
  organizationName: string;

  @ApiProperty({
    description: 'Whether the user\'s email has been verified',
    example: true
  })
  emailVerified: boolean;

  @ApiProperty({
    description: 'Whether the user authenticated with Google',
    required: false,
    example: false
  })
  isGoogleUser?: boolean;

  @ApiProperty({
    description: 'User profile picture URL',
    required: false,
    example: 'https://example.com/profile.jpg'
  })
  picture?: string;

  @ApiProperty({
    description: 'User last login date',
    required: false,
    example: '2025-05-05T13:00:00Z'
  })
  lastLoginAt?: Date;

  @ApiProperty({
    description: 'User account creation date',
    example: '2025-01-01T10:00:00Z'
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Subscription tier of the user\'s organization',
    example: 'PRO'
  })
  subscriptionTier: string;

  @ApiProperty({
    description: 'Subscription status',
    example: 'active'
  })
  subscriptionStatus: string;

  @ApiProperty({
    description: 'Whether two-factor authentication is enabled for the user',
    example: false,
    required: false
  })
  twoFactorEnabled?: boolean;

  @ApiProperty({
    description: 'Whether two-factor authentication has been verified for the user',
    example: false,
    required: false
  })
  twoFactorVerified?: boolean;
}

import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { UserService } from '../services/user.service';

export interface JwtPayload {
  sub: string;
  username: string;
  role: string;
  organizationId: string;
  iat: number;
  exp: number;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private configService: ConfigService,
    private userService: UserService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('auth.jwtSecret'),
    });
  }

  async validate(payload: JwtPayload) {
    // Check token issue time to prevent using tokens issued before critical security events
    const lastSecurityEventTime = this.configService.get<number>('auth.lastSecurityEventTime', 0);
    if (payload.iat < lastSecurityEventTime) {
      throw new UnauthorizedException('Token issued before security event, please login again');
    }

    // The returned value will be injected into the request object
    const user = await this.userService.findById(payload.sub, payload.organizationId);
    if (!user || !user.isActive) {
      throw new UnauthorizedException('User inactive or not found');
    }

    return {
      userId: payload.sub,
      username: payload.username,
      role: payload.role,
      organizationId: payload.organizationId,
    };
  }
}

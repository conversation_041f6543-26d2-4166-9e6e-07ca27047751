import { Injectable, BadRequestException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy, VerifyCallback } from 'passport-google-oauth20';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../services/auth.service';
import { EmailValidationService } from '../services/email-validation.service';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  constructor(
    private readonly configService: ConfigService,
    private readonly authService: AuthService,
    private readonly emailValidationService: EmailValidationService,
  ) {
    super({
      clientID: configService.get('auth.google.clientID'),
      clientSecret: configService.get('auth.google.clientSecret'),
      callbackURL: configService.get('auth.google.callbackURL'),
      scope: configService.get('auth.google.scope'),
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    profile: any,
    done: VerifyCallback,
  ): Promise<any> {
    const { name, emails, photos } = profile;
    const email = emails[0].value;
    
    // Check if email is from a forwarding service
    if (!this.emailValidationService.isEmailAllowed(email)) {
      const reason = this.emailValidationService.getEmailRejectionReason(email);
      return done(new BadRequestException(`Google login not allowed: ${reason}`), false);
    }
    
    const googleUserData = {
      email,
      firstName: name.givenName,
      lastName: name.familyName,
      picture: photos[0].value,
      googleId: profile.id,
    };

    try {
      // Validate or create a user with the Google profile data
      const user = await this.authService.validateOrCreateGoogleUser(googleUserData);
      done(null, user);
    } catch (error) {
      done(error, false);
    }
  }
}

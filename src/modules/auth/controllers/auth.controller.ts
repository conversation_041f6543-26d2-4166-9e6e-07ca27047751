import { 
  Controller, 
  Post, 
  Body, 
  UseGuards, 
  Get, 
  UnauthorizedException,
  Req,
  Res,
  HttpStatus,
  Query,
  BadRequestException,
  NotFoundException
} from '@nestjs/common';
import { AuthService } from '../services/auth.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { GoogleAuthGuard } from '../guards/google-auth.guard';
import { Public } from '../decorators/public.decorator';
import { LoginDto } from '../dto/auth.dto';
import { AuthResponseDto, UserResponseDto } from '../dto/auth-response.dto';
import { CreateUserDto } from '../dto/user.dto';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { GoogleAuthUrlDto } from '../dto/google-auth.dto';
import { AuthGuard } from '@nestjs/passport';
import { ResendVerificationDto, VerificationResponseDto } from '../dto/verification.dto';
import { PostHogService } from '../../posthog/services/posthog.service';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly postHogService: PostHogService
  ) {}

  @Public()
  @Post('login')
  @ApiOperation({ summary: 'Login with email and password' })
  @ApiResponse({
    status: 200,
    description: 'Login successful',
    type: AuthResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Invalid credentials' })
  async login(@Body() loginDto: LoginDto): Promise<AuthResponseDto> {
    const user = await this.authService.validateUser(
      loginDto.email,
      loginDto.password
,
    );
    console.log('Validated user:', user);
    
    if (!user) {
      // Track failed login attempt
      this.postHogService.trackEvent('anonymous', 'login_failed', {
        email: loginDto.email,
        reason: 'invalid_credentials',
        timestamp: new Date().toISOString()
      });
      throw new UnauthorizedException('Invalid credentials');
    }
    
    const result = await this.authService.login(user);
    
    // Track successful login
    this.postHogService.trackUserLogin(user.id, {
      email: user.email,
      organization_id: user.organizationId,
      login_method: 'email_password',
      user_role: user.role
    });
    
    return result;
  }

  @Public()
  @Post('register')
  @ApiOperation({ summary: 'Register a new user' })
  @ApiResponse({
    status: 201,
    description: 'Registration successful',
    type: AuthResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async register(@Body() dto: CreateUserDto): Promise<AuthResponseDto> {
    try {
      const result = await this.authService.register(dto);
      
      // Track successful registration
      this.postHogService.trackUserSignup(result.user.id, {
        email: dto.email,
        organization_name: dto.organizationName,
        registration_method: 'email_password',
        user_role: dto.role || 'student'
      });
      
      // Identify the user for future tracking
      this.postHogService.identifyUser(result.user.id, {
        email: dto.email,
        name: `${dto.firstName} ${dto.lastName}`,
        organization: dto.organizationName,
        role: dto.role || 'student',
        created_at: new Date().toISOString()
      });
      
      return result;
    } catch (error) {
      // Track failed registration
      this.postHogService.trackEvent('anonymous', 'registration_failed', {
        email: dto.email,
        organization_name: dto.organizationName,
        error: error.message,
        timestamp: new Date().toISOString()
      });
      throw error;
    }
  }

  @Public()
  @Post('validate-token')
  @ApiOperation({ summary: 'Validate JWT token' })
  @ApiResponse({ status: 200, description: 'Token is valid' })
  @ApiResponse({ status: 401, description: 'Invalid token' })
  async validateToken(@Body('token') token: string): Promise<any> {
    return this.authService.validateToken(token);
  }

  @Public()
  @Get('google')
  @UseGuards(GoogleAuthGuard)
  @ApiOperation({ summary: 'Initiate Google OAuth authentication' })
  @ApiResponse({ status: 302, description: 'Redirects to Google login' })
  async googleAuth() {
    // This endpoint initiates the Google OAuth flow
    // The actual redirect is handled by Passport
  }

  @Public()
  @Get('google/callback')
  @UseGuards(GoogleAuthGuard)
  @ApiOperation({ summary: 'Handle Google OAuth callback' })
  @ApiResponse({ status: 302, description: 'Redirects after successful authentication' })
  async googleAuthCallback(@Req() req: Request, @Res() res: Response) {
    // After successful Google authentication, login the user and redirect
    const user = req.user as any; // Cast to any to avoid type issues with Express.User
    const authResponse = await this.authService.login(user);
    
    // Redirect to the frontend with the token
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
    res.redirect(`${frontendUrl}/auth/google-callback?token=${authResponse.token}`);
  }

  @Public()
  @Get('google/url')
  @ApiOperation({ summary: 'Get Google OAuth URL' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns Google OAuth URL',
    type: GoogleAuthUrlDto 
  })
  async getGoogleAuthUrl(): Promise<GoogleAuthUrlDto> {
    const url = await this.authService.getGoogleAuthUrl();
    return { url };
  }

  @Public()
  @Get('verify-email')
  @ApiOperation({ summary: 'Verify email address' })
  @ApiResponse({
    status: 200,
    description: 'Email verified successfully',
    type: VerificationResponseDto
  })
  @ApiResponse({ 
    status: 400, 
    description: 'Invalid or expired token' 
  })
  async verifyEmail(@Query('token') token: string): Promise<VerificationResponseDto> {
    if (!token) {
      throw new BadRequestException('Verification token is required');
    }
    
    const success = await this.authService.verifyEmail(token);
    
    if (!success) {
      throw new BadRequestException('Invalid or expired verification token');
    }
    
    return { 
      success: true,
      message: 'Email verified successfully'
    };
  }

  @Public()
  @Post('resend-verification')
  @ApiOperation({ summary: 'Resend verification email' })
  @ApiResponse({
    status: 200,
    description: 'Verification email sent',
    type: VerificationResponseDto
  })
  @ApiResponse({ status: 404, description: 'User not found' })
  @ApiResponse({ status: 400, description: 'Email already verified' })
  async resendVerification(@Body() resendVerificationDto: ResendVerificationDto): Promise<VerificationResponseDto> {
    if (!resendVerificationDto.email) {
      throw new BadRequestException('Email is required');
    }
    
    try {
      const success = await this.authService.resendVerificationEmail(resendVerificationDto.email);
      
      return { 
        success: true,
        message: 'Verification email sent successfully'
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException('Failed to send verification email');
    }
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Logout user' })
  @ApiResponse({
    status: 200,
    description: 'Logout successful',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        message: { type: 'string' }
      }
    }
  })
  async logout(@Req() req: Request): Promise<{ success: boolean; message: string }> {
    // Extract user info from the JWT token
    const user = req.user as any;
    
    // Track logout event
    if (user?.id) {
      this.postHogService.trackEvent(user.id, 'user_logout', {
        email: user.email,
        organization_id: user.organizationId,
        timestamp: new Date().toISOString()
      });
    }
    
    // For JWT-based auth, logout is primarily handled client-side
    // by removing the token from storage. This endpoint mainly serves
    // to track the logout event and perform any server-side cleanup.
    
    return {
      success: true,
      message: 'Logout successful'
    };
  }
}

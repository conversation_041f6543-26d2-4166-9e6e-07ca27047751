import { Controller, Get, Put, Request, Body, UseGuards, NotFoundException, BadRequestException } from '@nestjs/common';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { ApiOperation, ApiResponse, ApiTags, ApiBearerAuth } from '@nestjs/swagger';
import { ProfileResponseDto } from '../dto/profile-response.dto';
import { UserService } from '../services/user.service';
import { OrganizationService } from '../services/organization.service';
import { SubscriptionService } from '../../subscription/services/subscription.service';
import { UpdateProfileDto } from '../dto/update-profile.dto';

@ApiTags('Profile')
@ApiBearerAuth()
@Controller('auth/profile')
export class ProfileController {
  constructor(
    private readonly userService: UserService,
    private readonly organizationService: OrganizationService,
    private readonly subscriptionService: SubscriptionService,
  ) {}

  @ApiOperation({ summary: 'Get current user profile with organization and subscription details' })
  @ApiResponse({ 
    status: 200, 
    description: 'Return comprehensive user profile',
    type: ProfileResponseDto
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'User not found' })
  @UseGuards(JwtAuthGuard)
  @Get()
  async getProfile(@Request() req): Promise<ProfileResponseDto> {
    // Extract user ID and organization ID from the request
    // The JWT strategy injects these values as req.user
    const userId = req.user.userId;
    const organizationId = req.user.organizationId;

    // Get user details
    const user = await this.userService.findById(userId, organizationId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Get organization details
    const organization = await this.organizationService.findById(organizationId);
    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    // Get subscription details
    const subscription = await this.subscriptionService.getSubscription(organizationId);

    // Construct the comprehensive profile response
    const profileResponse: ProfileResponseDto = {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      organizationId: user.organizationId,
      organizationName: organization.name,
      emailVerified: user.emailVerified,
      isGoogleUser: user.isGoogleUser,
      picture: user.picture,
      lastLoginAt: user.lastLoginAt,
      createdAt: user.createdAt,
      subscriptionTier: subscription.tier,
      subscriptionStatus: subscription.status,
      twoFactorEnabled: user.twoFactorEnabled,
      twoFactorVerified: user.twoFactorVerified
    };

    return profileResponse;
  }

  @ApiOperation({ summary: 'Update current user profile' })
  @ApiResponse({ 
    status: 200, 
    description: 'Profile updated successfully',
    type: ProfileResponseDto
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'User not found' })
  @UseGuards(JwtAuthGuard)
  @Put()
  async updateProfile(
    @Request() req,
    @Body() updateProfileDto: UpdateProfileDto
  ): Promise<ProfileResponseDto> {
    const userId = req.user.userId;
    const organizationId = req.user.organizationId;

    // Get user details
    const user = await this.userService.findById(userId, organizationId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Google users can't update their profile directly for some fields
    if (user.isGoogleUser && (updateProfileDto.firstName || updateProfileDto.lastName)) {
      throw new BadRequestException('Google users cannot update their name directly. Please update your Google profile.');
    }

    // Update the user profile
    const updatedUser = await this.userService.update(
      userId, 
      organizationId, 
      updateProfileDto
    );

    if (!updatedUser) {
      throw new BadRequestException('Failed to update profile');
    }

    // Get organization details
    const organization = await this.organizationService.findById(organizationId);
    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    // Get subscription details
    const subscription = await this.subscriptionService.getSubscription(organizationId);

    // Construct the comprehensive profile response
    const profileResponse: ProfileResponseDto = {
      id: updatedUser.id,
      email: updatedUser.email,
      firstName: updatedUser.firstName,
      lastName: updatedUser.lastName,
      role: updatedUser.role,
      organizationId: updatedUser.organizationId,
      organizationName: organization.name,
      emailVerified: updatedUser.emailVerified,
      isGoogleUser: updatedUser.isGoogleUser,
      picture: updatedUser.picture,
      lastLoginAt: updatedUser.lastLoginAt,
      createdAt: updatedUser.createdAt,
      subscriptionTier: subscription.tier,
      subscriptionStatus: subscription.status,
      twoFactorEnabled: updatedUser.twoFactorEnabled,
      twoFactorVerified: updatedUser.twoFactorVerified
    };

    return profileResponse;
  }
}

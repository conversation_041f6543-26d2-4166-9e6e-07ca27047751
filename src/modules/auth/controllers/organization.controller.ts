import { Controller, Get, Post, Body, UseGuards, Request } from '@nestjs/common';
import { OrganizationService } from '../services/organization.service';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { Public } from '../decorators/public.decorator';
import { ApiTags, ApiOperation } from '@nestjs/swagger';

@ApiTags('organizations')
@Controller('organizations')
export class OrganizationController {
  constructor(private readonly organizationService: OrganizationService) {}

  // Public endpoint to list all organizations for testing purposes
  // This would be removed or secured in production
  @Public()
  @Get('list')
  @ApiOperation({ summary: 'List all organizations (testing only)' })
  async listAllForTesting() {
    return this.organizationService.findAll();
  }

  @UseGuards(JwtAuthGuard)
  @Get('profile')
  @ApiOperation({ summary: 'Get current organization profile' })
  async getOrganizationProfile(@Request() req) {
    const organizationId = req.user.organizationId;
    return this.organizationService.findById(organizationId);
  }
}

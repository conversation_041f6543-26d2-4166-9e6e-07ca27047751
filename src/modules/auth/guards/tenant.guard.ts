import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { TenantContextService } from '../services/tenant-context.service';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';

@Injectable()
export class TenantGuard implements CanActivate {
  constructor(
    private readonly tenantContext: TenantContextService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if route is marked as public
    const isPublic = this.reflector.getAllAndOverride<boolean>(
      IS_PUBLIC_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (isPublic) {
      return true;
    }

    const tenant = this.tenantContext.getCurrentTenant();
    if (!tenant?.organizationId) {
      throw new UnauthorizedException('Tenant context required');
    }

    // Check if route requires specific tenant
    const requiredTenant = this.reflector.get<string>(
      'requiredTenant',
      context.getHandler(),
    );

    if (requiredTenant && tenant.organizationId !== requiredTenant) {
      throw new UnauthorizedException('Invalid tenant access');
    }

    // Add tenant info to request for convenience
    const request = context.switchToHttp().getRequest();
    request.tenant = tenant;

    return true;
  }
}

// Decorator for requiring specific tenant
export const RequireTenant = (organizationId: string) => {
  return (target: any, key?: string, descriptor?: PropertyDescriptor) => {
    Reflect.defineMetadata('requiredTenant', organizationId, descriptor.value);
    return descriptor;
  };
};

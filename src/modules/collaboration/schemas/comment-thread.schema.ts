import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type CommentThreadDocument = CommentThread & Document;

export enum ThreadStatus {
  OPEN = 'open',
  RESOLVED = 'resolved',
  CLOSED = 'closed',
}

export enum CommentType {
  COMMENT = 'comment',
  SUGGESTION = 'suggestion',
  QUESTION = 'question',
  APPROVAL = 'approval',
  REJECTION = 'rejection',
}

export interface ThreadComment {
  id: string;
  userId: string;
  userName: string;
  content: string;
  type: CommentType;
  timestamp: Date;
  editedAt?: Date;
  isEdited: boolean;
  mentions: string[]; // User IDs mentioned in the comment
  attachments: {
    id: string;
    fileName: string;
    fileUrl: string;
    fileSize: number;
    mimeType: string;
  }[];
  reactions: {
    emoji: string;
    userId: string;
    timestamp: Date;
  }[];
  replies: {
    id: string;
    userId: string;
    userName: string;
    content: string;
    timestamp: Date;
    mentions: string[];
  }[];
  metadata: Record<string, any>;
}

export interface DocumentAnchor {
  type: 'text' | 'paragraph' | 'section' | 'page' | 'line';
  startPosition: number;
  endPosition: number;
  selectedText?: string;
  context?: string; // Surrounding text for context
  coordinates?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

@Schema({ timestamps: true })
export class CommentThread extends Document {
  @Prop({ required: true, type: MongooseSchema.Types.ObjectId, ref: 'Document' })
  documentId: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'CollaborationSession' })
  collaborationSessionId?: string;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'WorkflowInstance' })
  workflowInstanceId?: string;

  @Prop({ required: true })
  organizationId: string;

  @Prop({ required: true })
  createdBy: string;

  @Prop({ required: true })
  title: string;

  @Prop()
  description: string;

  @Prop({ required: true, enum: ThreadStatus, default: ThreadStatus.OPEN })
  status: ThreadStatus;

  @Prop({ type: Object, required: true })
  anchor: DocumentAnchor;

  @Prop({ type: [Object], default: [] })
  comments: ThreadComment[];

  @Prop({ type: [String], default: [] })
  participants: string[]; // User IDs of all participants

  @Prop({ type: [String], default: [] })
  watchers: string[]; // User IDs watching this thread

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({ required: true, default: 'medium' })
  priority: 'low' | 'medium' | 'high' | 'urgent';

  @Prop()
  assignedTo: string; // User ID if thread is assigned to someone

  @Prop()
  dueDate: Date;

  @Prop()
  resolvedAt: Date;

  @Prop()
  resolvedBy: string;

  @Prop()
  resolutionNote: string;

  @Prop({ type: Object })
  metadata: {
    documentTitle: string;
    documentVersion: number;
    threadType: 'review' | 'discussion' | 'approval' | 'question' | 'suggestion';
    isPublic: boolean;
    allowAnonymous: boolean;
    requireApproval: boolean;
    autoResolve: boolean;
    notifyOnReply: boolean;
  };

  @Prop({ type: Object })
  analytics: {
    viewCount: number;
    replyCount: number;
    participantCount: number;
    averageResponseTime: number;
    lastActivity: Date;
    engagementScore: number;
  };

  @Prop({ type: [Object] })
  notifications: {
    id: string;
    type: 'new_comment' | 'mention' | 'assignment' | 'resolution';
    userId: string;
    sentAt: Date;
    readAt?: Date;
  }[];

  @Prop({ type: Object })
  permissions: {
    canView: string[]; // User IDs or role names
    canComment: string[]; // User IDs or role names
    canResolve: string[]; // User IDs or role names
    canDelete: string[]; // User IDs or role names
  };
}

export const CommentThreadSchema = SchemaFactory.createForClass(CommentThread);

// Indexes for performance
CommentThreadSchema.index({ documentId: 1, status: 1 });
CommentThreadSchema.index({ organizationId: 1, status: 1 });
CommentThreadSchema.index({ collaborationSessionId: 1 });
CommentThreadSchema.index({ workflowInstanceId: 1 });
CommentThreadSchema.index({ participants: 1 });
CommentThreadSchema.index({ assignedTo: 1, status: 1 });
CommentThreadSchema.index({ createdAt: -1 });
CommentThreadSchema.index({ 'anchor.startPosition': 1, 'anchor.endPosition': 1 });

export const COMMENT_THREAD_MODEL = 'CommentThread';

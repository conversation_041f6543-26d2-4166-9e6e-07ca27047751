import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type DocumentOperationDocument = DocumentOperation & Document;

export enum OperationType {
  INSERT = 'insert',
  DELETE = 'delete',
  RETAIN = 'retain',
  FORMAT = 'format',
}

export enum OperationStatus {
  PENDING = 'pending',
  APPLIED = 'applied',
  REJECTED = 'rejected',
  CONFLICTED = 'conflicted',
}

@Schema({ timestamps: true })
export class DocumentOperation extends Document {
  @Prop({ required: true, type: MongooseSchema.Types.ObjectId, ref: 'CollaborationSession' })
  sessionId: string;

  @Prop({ required: true, type: MongooseSchema.Types.ObjectId, ref: 'Document' })
  documentId: string;

  @Prop({ required: true })
  organizationId: string;

  @Prop({ required: true })
  userId: string;

  @Prop({ required: true, enum: OperationType })
  type: OperationType;

  @Prop({ required: true })
  position: number;

  @Prop()
  content: string;

  @Prop()
  length: number;

  @Prop({ type: Object })
  attributes: Record<string, any>;

  @Prop({ required: true })
  version: number;

  @Prop({ required: true })
  clientId: string;

  @Prop({ required: true, enum: OperationStatus, default: OperationStatus.PENDING })
  status: OperationStatus;

  @Prop({ type: Object })
  transformedFrom: {
    originalOperation: any;
    transformedAgainst: string[]; // Array of operation IDs
  };

  @Prop({ type: Object })
  metadata: {
    userAgent: string;
    ipAddress: string;
    timestamp: Date;
    processingTime: number; // in milliseconds
  };

  @Prop()
  errorMessage: string;

  @Prop({ type: [String] })
  conflictsWith: string[]; // Array of operation IDs that conflict

  @Prop()
  resolvedAt: Date;

  @Prop()
  resolvedBy: string;
}

export const DocumentOperationSchema = SchemaFactory.createForClass(DocumentOperation);

// Indexes for performance
DocumentOperationSchema.index({ sessionId: 1, version: 1 });
DocumentOperationSchema.index({ documentId: 1, createdAt: -1 });
DocumentOperationSchema.index({ userId: 1, createdAt: -1 });
DocumentOperationSchema.index({ status: 1, createdAt: -1 });
DocumentOperationSchema.index({ organizationId: 1, createdAt: -1 });

export const DOCUMENT_OPERATION_MODEL = 'DocumentOperation';

import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type CollaborationSessionDocument = CollaborationSession & Document;

export enum SessionStatus {
  ACTIVE = 'active',
  PAUSED = 'paused',
  ENDED = 'ended',
}

export enum UserRole {
  OWNER = 'owner',
  EDITOR = 'editor',
  COMMENTER = 'commenter',
  VIEWER = 'viewer',
}

export interface Participant {
  userId: string;
  role: UserRole;
  joinedAt: Date;
  lastActivity: Date;
  isOnline: boolean;
  cursor?: {
    position: number;
    selection?: {
      start: number;
      end: number;
    };
  };
}

export interface DocumentOperation {
  id: string;
  type: 'insert' | 'delete' | 'retain' | 'format';
  position: number;
  content?: string;
  length?: number;
  attributes?: Record<string, any>;
  userId: string;
  timestamp: Date;
  version: number;
}

@Schema({ timestamps: true })
export class CollaborationSession extends Document {
  @Prop({ required: true, type: MongooseSchema.Types.ObjectId, ref: 'Document' })
  documentId: string;

  @Prop({ required: true })
  organizationId: string;

  @Prop({ required: true })
  sessionName: string;

  @Prop({ required: true, enum: SessionStatus, default: SessionStatus.ACTIVE })
  status: SessionStatus;

  @Prop({ required: true })
  ownerId: string;

  @Prop({ type: [Object], default: [] })
  participants: Participant[];

  @Prop({ type: [Object], default: [] })
  operations: DocumentOperation[];

  @Prop({ required: true, default: 0 })
  currentVersion: number;

  @Prop({ type: Object })
  documentSnapshot: {
    content: string;
    version: number;
    timestamp: Date;
  };

  @Prop({ type: Object, default: {} })
  settings: {
    allowAnonymousUsers: boolean;
    maxParticipants: number;
    autoSave: boolean;
    autoSaveInterval: number; // in seconds
    lockTimeout: number; // in seconds
  };

  @Prop({ type: Object })
  metadata: {
    totalOperations: number;
    totalParticipants: number;
    sessionDuration: number; // in seconds
    lastActivity: Date;
  };

  @Prop()
  endedAt: Date;

  @Prop()
  endedBy: string;

  @Prop()
  endReason: string;
}

export const CollaborationSessionSchema = SchemaFactory.createForClass(CollaborationSession);

// Indexes for performance
CollaborationSessionSchema.index({ documentId: 1, status: 1 });
CollaborationSessionSchema.index({ organizationId: 1, status: 1 });
CollaborationSessionSchema.index({ 'participants.userId': 1 });
CollaborationSessionSchema.index({ createdAt: -1 });

export const COLLABORATION_SESSION_MODEL = 'CollaborationSession';

import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Injectable, Logger, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { TenantContextService } from '../../auth/tenant/tenant-context.service';
import { RealTimeEditingService } from '../services/real-time-editing.service';
import { PresenceService } from '../services/presence.service';
import {
  DocumentOperationDto,
  CursorPosition,
  CollaborationEvent,
} from '../interfaces/collaboration.interface';

@Injectable()
@WebSocketGateway({
  namespace: '/collaboration',
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },
})
export class CollaborationGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(CollaborationGateway.name);
  private connectedUsers = new Map<string, { socket: Socket; userId: string; sessionId?: string }>();

  constructor(
    private readonly realTimeEditingService: RealTimeEditingService,
    private readonly presenceService: PresenceService,
    private readonly tenantContext: TenantContextService,
  ) {}

  async handleConnection(client: Socket) {
    try {
      this.logger.log(`Client connected: ${client.id}`);

      // Extract user info from token (you'll need to implement JWT validation for WebSocket)
      const token = client.handshake.auth.token || client.handshake.headers.authorization;
      if (!token) {
        client.disconnect();
        return;
      }

      // TODO: Validate JWT token and extract user info
      // For now, we'll assume user info is passed in handshake
      const userId = client.handshake.auth.userId;
      const organizationId = client.handshake.auth.organizationId;

      if (!userId || !organizationId) {
        client.disconnect();
        return;
      }

      this.connectedUsers.set(client.id, { socket: client, userId });

      // Set tenant context
      this.tenantContext.setCurrentOrganization(organizationId);

      client.emit('connected', { message: 'Connected to collaboration server' });

    } catch (error) {
      this.logger.error(`Connection error: ${error.message}`);
      client.disconnect();
    }
  }

  async handleDisconnect(client: Socket) {
    try {
      const userInfo = this.connectedUsers.get(client.id);
      if (userInfo) {
        this.logger.log(`Client disconnected: ${client.id} (User: ${userInfo.userId})`);

        if (userInfo.sessionId) {
          // Update presence and notify other participants
          await this.presenceService.updateUserPresence(
            userInfo.sessionId,
            userInfo.userId,
            false,
          );

          // Notify other participants about user leaving
          client.to(userInfo.sessionId).emit('user:left', {
            userId: userInfo.userId,
            timestamp: new Date(),
          });
        }

        this.connectedUsers.delete(client.id);
      }
    } catch (error) {
      this.logger.error(`Disconnect error: ${error.message}`);
    }
  }

  @SubscribeMessage('session:join')
  async handleJoinSession(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { sessionId: string; role?: string },
  ) {
    try {
      const userInfo = this.connectedUsers.get(client.id);
      if (!userInfo) {
        client.emit('error', { message: 'User not authenticated' });
        return;
      }

      const { sessionId, role = 'editor' } = data;

      // Join the session room
      await client.join(sessionId);
      userInfo.sessionId = sessionId;

      // Update presence
      await this.presenceService.updateUserPresence(sessionId, userInfo.userId, true);

      // Get current session state
      const sessionState = await this.realTimeEditingService.getSessionState(sessionId);

      // Send current state to joining user
      client.emit('session:state', sessionState);

      // Notify other participants
      client.to(sessionId).emit('user:joined', {
        userId: userInfo.userId,
        role,
        timestamp: new Date(),
      });

      this.logger.log(`User ${userInfo.userId} joined session ${sessionId}`);

    } catch (error) {
      this.logger.error(`Join session error: ${error.message}`);
      client.emit('error', { message: 'Failed to join session' });
    }
  }

  @SubscribeMessage('session:leave')
  async handleLeaveSession(@ConnectedSocket() client: Socket) {
    try {
      const userInfo = this.connectedUsers.get(client.id);
      if (!userInfo || !userInfo.sessionId) {
        return;
      }

      const sessionId = userInfo.sessionId;

      // Leave the session room
      await client.leave(sessionId);

      // Update presence
      await this.presenceService.updateUserPresence(sessionId, userInfo.userId, false);

      // Notify other participants
      client.to(sessionId).emit('user:left', {
        userId: userInfo.userId,
        timestamp: new Date(),
      });

      userInfo.sessionId = undefined;

      this.logger.log(`User ${userInfo.userId} left session ${sessionId}`);

    } catch (error) {
      this.logger.error(`Leave session error: ${error.message}`);
    }
  }

  @SubscribeMessage('document:operation')
  async handleDocumentOperation(
    @ConnectedSocket() client: Socket,
    @MessageBody() operation: DocumentOperationDto,
  ) {
    try {
      const userInfo = this.connectedUsers.get(client.id);
      if (!userInfo || !userInfo.sessionId) {
        client.emit('error', { message: 'Not in a session' });
        return;
      }

      // Process the operation through the real-time editing service
      const result = await this.realTimeEditingService.processOperation(
        userInfo.sessionId,
        userInfo.userId,
        operation,
      );

      if (result.success) {
        // Broadcast the transformed operation to all other participants
        client.to(userInfo.sessionId).emit('document:operation', {
          operation: result.transformedOperation,
          userId: userInfo.userId,
          timestamp: new Date(),
        });

        // Send acknowledgment to the sender
        client.emit('operation:ack', {
          clientId: operation.clientId,
          version: result.transformedOperation.version,
          success: true,
        });
      } else {
        // Send error to the sender
        client.emit('operation:error', {
          clientId: operation.clientId,
          error: result.errorMessage,
          conflicts: result.conflicts,
        });
      }

    } catch (error) {
      this.logger.error(`Document operation error: ${error.message}`);
      client.emit('error', { message: 'Failed to process operation' });
    }
  }

  @SubscribeMessage('cursor:update')
  async handleCursorUpdate(
    @ConnectedSocket() client: Socket,
    @MessageBody() cursor: CursorPosition,
  ) {
    try {
      const userInfo = this.connectedUsers.get(client.id);
      if (!userInfo || !userInfo.sessionId) {
        return;
      }

      // Update cursor position in presence service
      await this.presenceService.updateUserCursor(
        userInfo.sessionId,
        userInfo.userId,
        cursor,
      );

      // Broadcast cursor update to other participants
      client.to(userInfo.sessionId).emit('cursor:update', {
        userId: userInfo.userId,
        cursor,
        timestamp: new Date(),
      });

    } catch (error) {
      this.logger.error(`Cursor update error: ${error.message}`);
    }
  }

  @SubscribeMessage('session:ping')
  handlePing(@ConnectedSocket() client: Socket) {
    client.emit('session:pong', { timestamp: new Date() });
  }

  // Method to broadcast events to a session
  async broadcastToSession(sessionId: string, event: string, data: any) {
    this.server.to(sessionId).emit(event, data);
  }

  // Method to send event to specific user
  async sendToUser(userId: string, event: string, data: any) {
    for (const [socketId, userInfo] of this.connectedUsers.entries()) {
      if (userInfo.userId === userId) {
        userInfo.socket.emit(event, data);
        break;
      }
    }
  }
}

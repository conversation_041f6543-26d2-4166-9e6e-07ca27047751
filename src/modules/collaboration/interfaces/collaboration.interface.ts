export interface CreateCollaborationSessionDto {
  documentId: string;
  sessionName: string;
  settings?: {
    allowAnonymousUsers?: boolean;
    maxParticipants?: number;
    autoSave?: boolean;
    autoSaveInterval?: number;
    lockTimeout?: number;
  };
}

export interface JoinSessionDto {
  sessionId: string;
  role?: 'editor' | 'commenter' | 'viewer';
}

export interface DocumentOperationDto {
  type: 'insert' | 'delete' | 'retain' | 'format';
  position: number;
  content?: string;
  length?: number;
  attributes?: Record<string, any>;
  clientId: string;
  version: number;
}

export interface CursorPosition {
  position: number;
  selection?: {
    start: number;
    end: number;
  };
}

export interface UserPresence {
  userId: string;
  userName: string;
  isOnline: boolean;
  cursor?: CursorPosition;
  lastActivity: Date;
  role: string;
}

export interface CollaborationEvent {
  type: 'operation' | 'cursor' | 'presence' | 'system';
  sessionId: string;
  userId: string;
  data: any;
  timestamp: Date;
}

export interface OperationTransformResult {
  transformedOperation: DocumentOperationDto;
  conflicts: string[];
  success: boolean;
  errorMessage?: string;
}

export interface SessionMetrics {
  sessionId: string;
  totalOperations: number;
  activeParticipants: number;
  sessionDuration: number;
  averageResponseTime: number;
  conflictRate: number;
  lastActivity: Date;
}

export interface CollaborationPermissions {
  canEdit: boolean;
  canComment: boolean;
  canView: boolean;
  canInvite: boolean;
  canManageSession: boolean;
  canEndSession: boolean;
}

export interface SessionActivity {
  id: string;
  type: 'join' | 'leave' | 'edit' | 'comment' | 'system';
  userId: string;
  userName: string;
  description: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface CollaborationAnalytics {
  sessionId: string;
  documentId: string;
  organizationId: string;
  metrics: {
    totalParticipants: number;
    totalOperations: number;
    sessionDuration: number;
    averageParticipants: number;
    peakParticipants: number;
    operationsPerMinute: number;
    conflictRate: number;
    userEngagement: {
      userId: string;
      operationsCount: number;
      timeActive: number;
      lastActivity: Date;
    }[];
  };
  createdAt: Date;
  updatedAt: Date;
}

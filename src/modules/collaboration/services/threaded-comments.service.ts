import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  CommentThread,
  CommentThreadDocument,
  COMMENT_THREAD_MODEL,
  ThreadStatus,
  CommentType,
  ThreadComment,
  DocumentAnchor,
} from '../schemas/comment-thread.schema';
import { MentionsService } from './mentions.service';
import { TenantContextService } from '../../auth/tenant/tenant-context.service';

export interface CreateThreadDto {
  documentId: string;
  collaborationSessionId?: string;
  workflowInstanceId?: string;
  title: string;
  description?: string;
  anchor: DocumentAnchor;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  tags?: string[];
  assignedTo?: string;
  dueDate?: Date;
  metadata?: {
    threadType?: 'review' | 'discussion' | 'approval' | 'question' | 'suggestion';
    isPublic?: boolean;
    allowAnonymous?: boolean;
    requireApproval?: boolean;
  };
}

export interface AddCommentDto {
  content: string;
  type?: CommentType;
  mentions?: string[];
  attachments?: {
    fileName: string;
    fileUrl: string;
    fileSize: number;
    mimeType: string;
  }[];
  replyToCommentId?: string;
}

@Injectable()
export class ThreadedCommentsService {
  private readonly logger = new Logger(ThreadedCommentsService.name);

  constructor(
    @InjectModel(COMMENT_THREAD_MODEL)
    private readonly threadModel: Model<CommentThreadDocument>,
    private readonly mentionsService: MentionsService,
    private readonly tenantContext: TenantContextService,
  ) {}

  /**
   * Create a new comment thread
   */
  async createThread(dto: CreateThreadDto, userId: string): Promise<CommentThread> {
    const organizationId = this.tenantContext.getCurrentOrganization();
    
    this.logger.log(`Creating comment thread for document ${dto.documentId}`);

    const thread = new this.threadModel({
      documentId: dto.documentId,
      collaborationSessionId: dto.collaborationSessionId,
      workflowInstanceId: dto.workflowInstanceId,
      organizationId,
      createdBy: userId,
      title: dto.title,
      description: dto.description,
      status: ThreadStatus.OPEN,
      anchor: dto.anchor,
      comments: [],
      participants: [userId],
      watchers: [userId],
      tags: dto.tags || [],
      priority: dto.priority || 'medium',
      assignedTo: dto.assignedTo,
      dueDate: dto.dueDate,
      metadata: {
        documentTitle: 'Document', // TODO: Get from document service
        documentVersion: 1,
        threadType: dto.metadata?.threadType || 'discussion',
        isPublic: dto.metadata?.isPublic ?? true,
        allowAnonymous: dto.metadata?.allowAnonymous ?? false,
        requireApproval: dto.metadata?.requireApproval ?? false,
        autoResolve: false,
        notifyOnReply: true,
      },
      analytics: {
        viewCount: 0,
        replyCount: 0,
        participantCount: 1,
        averageResponseTime: 0,
        lastActivity: new Date(),
        engagementScore: 0,
      },
      notifications: [],
      permissions: {
        canView: [],
        canComment: [],
        canResolve: [userId],
        canDelete: [userId],
      },
    });

    const savedThread = await thread.save();

    // Add to assigned user's watchers if different from creator
    if (dto.assignedTo && dto.assignedTo !== userId) {
      await this.addWatcher(savedThread._id.toString(), dto.assignedTo);
    }

    return savedThread;
  }

  /**
   * Add a comment to a thread
   */
  async addComment(
    threadId: string,
    dto: AddCommentDto,
    userId: string,
    userName: string,
  ): Promise<CommentThread> {
    const thread = await this.getThread(threadId);

    const comment: ThreadComment = {
      id: `comment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId,
      userName,
      content: dto.content,
      type: dto.type || CommentType.COMMENT,
      timestamp: new Date(),
      isEdited: false,
      mentions: dto.mentions || [],
      attachments: dto.attachments?.map(att => ({
        id: `att_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        ...att,
      })) || [],
      reactions: [],
      replies: [],
      metadata: {},
    };

    // Handle reply to specific comment
    if (dto.replyToCommentId) {
      const parentCommentIndex = thread.comments.findIndex(c => c.id === dto.replyToCommentId);
      if (parentCommentIndex !== -1) {
        const reply = {
          id: `reply_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          userId,
          userName,
          content: dto.content,
          timestamp: new Date(),
          mentions: dto.mentions || [],
        };
        
        await this.threadModel.updateOne(
          { _id: threadId },
          {
            $push: { [`comments.${parentCommentIndex}.replies`]: reply },
            $addToSet: { participants: userId },
            $set: { 'analytics.lastActivity': new Date() },
            $inc: { 'analytics.replyCount': 1 },
          },
        );
      }
    } else {
      // Add as main comment
      await this.threadModel.updateOne(
        { _id: threadId },
        {
          $push: { comments: comment },
          $addToSet: { participants: userId },
          $set: { 'analytics.lastActivity': new Date() },
          $inc: { 'analytics.replyCount': 1 },
        },
      );
    }

    // Process mentions
    if (dto.mentions?.length) {
      await this.mentionsService.processMentions(threadId, dto.mentions, userId, dto.content);
    }

    // Update analytics
    await this.updateThreadAnalytics(threadId);

    const updatedThread = await this.getThread(threadId);
    
    this.logger.log(`Comment added to thread ${threadId} by user ${userId}`);

    return updatedThread;
  }

  /**
   * Get thread by ID
   */
  async getThread(threadId: string): Promise<CommentThread> {
    const organizationId = this.tenantContext.getCurrentOrganization();
    
    const thread = await this.threadModel.findOne({
      _id: threadId,
      organizationId,
    }).exec();

    if (!thread) {
      throw new NotFoundException(`Comment thread not found: ${threadId}`);
    }

    // Update view count
    await this.threadModel.updateOne(
      { _id: threadId },
      { $inc: { 'analytics.viewCount': 1 } },
    );

    return thread;
  }

  /**
   * Get threads for a document
   */
  async getDocumentThreads(
    documentId: string,
    status?: ThreadStatus,
    limit: number = 50,
    offset: number = 0,
  ): Promise<{
    threads: CommentThread[];
    total: number;
    hasMore: boolean;
  }> {
    const organizationId = this.tenantContext.getCurrentOrganization();
    
    const query: any = {
      organizationId,
      documentId,
    };

    if (status) {
      query.status = status;
    }

    const total = await this.threadModel.countDocuments(query);
    
    const threads = await this.threadModel
      .find(query)
      .sort({ 'analytics.lastActivity': -1 })
      .skip(offset)
      .limit(limit)
      .exec();

    return {
      threads,
      total,
      hasMore: offset + limit < total,
    };
  }

  /**
   * Resolve a thread
   */
  async resolveThread(
    threadId: string,
    resolutionNote: string,
    userId: string,
  ): Promise<CommentThread> {
    const thread = await this.getThread(threadId);

    // Check permissions
    if (!thread.permissions.canResolve.includes(userId) && thread.createdBy !== userId) {
      throw new BadRequestException('Insufficient permissions to resolve thread');
    }

    const updatedThread = await this.threadModel.findByIdAndUpdate(
      threadId,
      {
        $set: {
          status: ThreadStatus.RESOLVED,
          resolvedAt: new Date(),
          resolvedBy: userId,
          resolutionNote,
          'analytics.lastActivity': new Date(),
        },
      },
      { new: true },
    ).exec();

    this.logger.log(`Thread ${threadId} resolved by user ${userId}`);

    return updatedThread!;
  }

  /**
   * Add reaction to comment
   */
  async addReaction(
    threadId: string,
    commentId: string,
    emoji: string,
    userId: string,
  ): Promise<CommentThread> {
    const thread = await this.getThread(threadId);
    
    const commentIndex = thread.comments.findIndex(c => c.id === commentId);
    if (commentIndex === -1) {
      throw new NotFoundException(`Comment not found: ${commentId}`);
    }

    // Check if user already reacted with this emoji
    const existingReactionIndex = thread.comments[commentIndex].reactions.findIndex(
      r => r.userId === userId && r.emoji === emoji,
    );

    if (existingReactionIndex !== -1) {
      // Remove existing reaction
      await this.threadModel.updateOne(
        { _id: threadId },
        {
          $pull: {
            [`comments.${commentIndex}.reactions`]: {
              userId,
              emoji,
            },
          },
        },
      );
    } else {
      // Add new reaction
      await this.threadModel.updateOne(
        { _id: threadId },
        {
          $push: {
            [`comments.${commentIndex}.reactions`]: {
              emoji,
              userId,
              timestamp: new Date(),
            },
          },
        },
      );
    }

    return this.getThread(threadId);
  }

  /**
   * Add watcher to thread
   */
  async addWatcher(threadId: string, userId: string): Promise<void> {
    await this.threadModel.updateOne(
      { _id: threadId },
      { $addToSet: { watchers: userId } },
    );
  }

  /**
   * Remove watcher from thread
   */
  async removeWatcher(threadId: string, userId: string): Promise<void> {
    await this.threadModel.updateOne(
      { _id: threadId },
      { $pull: { watchers: userId } },
    );
  }

  /**
   * Update thread analytics
   */
  private async updateThreadAnalytics(threadId: string): Promise<void> {
    const thread = await this.threadModel.findById(threadId).exec();
    if (!thread) return;

    const participantCount = thread.participants.length;
    const replyCount = thread.comments.reduce((total, comment) => {
      return total + 1 + comment.replies.length;
    }, 0);

    // Calculate engagement score based on various factors
    const engagementScore = this.calculateEngagementScore(thread);

    await this.threadModel.updateOne(
      { _id: threadId },
      {
        $set: {
          'analytics.participantCount': participantCount,
          'analytics.replyCount': replyCount,
          'analytics.engagementScore': engagementScore,
        },
      },
    );
  }

  /**
   * Calculate engagement score for a thread
   */
  private calculateEngagementScore(thread: CommentThread): number {
    const factors = {
      participantCount: thread.participants.length * 10,
      replyCount: thread.comments.length * 5,
      viewCount: thread.analytics.viewCount * 1,
      reactionCount: thread.comments.reduce((total, comment) => {
        return total + comment.reactions.length;
      }, 0) * 3,
      recency: this.getRecencyScore(thread.analytics.lastActivity),
    };

    return Object.values(factors).reduce((total, score) => total + score, 0);
  }

  /**
   * Get recency score based on last activity
   */
  private getRecencyScore(lastActivity: Date): number {
    const hoursSinceActivity = (Date.now() - lastActivity.getTime()) / (1000 * 60 * 60);
    
    if (hoursSinceActivity < 1) return 20;
    if (hoursSinceActivity < 24) return 15;
    if (hoursSinceActivity < 168) return 10; // 1 week
    if (hoursSinceActivity < 720) return 5; // 1 month
    
    return 0;
  }
}

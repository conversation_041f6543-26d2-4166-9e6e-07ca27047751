import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  CollaborationSession,
  CollaborationSessionDocument,
  COLLABORATION_SESSION_MODEL,
} from '../schemas/collaboration-session.schema';
import {
  DocumentOperation,
  DocumentOperationDocument,
  DOCUMENT_OPERATION_MODEL,
  OperationStatus,
} from '../schemas/document-operation.schema';
import {
  DocumentOperationDto,
  OperationTransformResult,
  SessionMetrics,
} from '../interfaces/collaboration.interface';
import { OperationalTransformService } from './operational-transform.service';
import { TenantContextService } from '../../auth/tenant/tenant-context.service';

@Injectable()
export class RealTimeEditingService {
  private readonly logger = new Logger(RealTimeEditingService.name);

  constructor(
    @InjectModel(COLLABORATION_SESSION_MODEL)
    private readonly sessionModel: Model<CollaborationSessionDocument>,
    @InjectModel(DOCUMENT_OPERATION_MODEL)
    private readonly operationModel: Model<DocumentOperationDocument>,
    private readonly operationalTransformService: OperationalTransformService,
    private readonly tenantContext: TenantContextService,
  ) {}

  async createSession(
    documentId: string,
    sessionName: string,
    ownerId: string,
    settings?: any,
  ): Promise<CollaborationSession> {
    const organizationId = this.tenantContext.getCurrentOrganization();

    this.logger.log(`Creating collaboration session for document ${documentId}`);

    const session = new this.sessionModel({
      documentId,
      organizationId,
      sessionName,
      ownerId,
      participants: [{
        userId: ownerId,
        role: 'owner',
        joinedAt: new Date(),
        lastActivity: new Date(),
        isOnline: true,
      }],
      currentVersion: 0,
      settings: {
        allowAnonymousUsers: false,
        maxParticipants: 10,
        autoSave: true,
        autoSaveInterval: 30,
        lockTimeout: 300,
        ...settings,
      },
      metadata: {
        totalOperations: 0,
        totalParticipants: 1,
        sessionDuration: 0,
        lastActivity: new Date(),
      },
    });

    return session.save();
  }

  async getSession(sessionId: string): Promise<CollaborationSession> {
    const organizationId = this.tenantContext.getCurrentOrganization();

    const session = await this.sessionModel.findOne({
      _id: sessionId,
      organizationId,
    }).exec();

    if (!session) {
      throw new NotFoundException(`Collaboration session not found: ${sessionId}`);
    }

    return session;
  }

  async getSessionState(sessionId: string): Promise<any> {
    const session = await this.getSession(sessionId);

    // Get recent operations for state reconstruction
    const operations = await this.operationModel
      .find({
        sessionId,
        status: OperationStatus.APPLIED,
      })
      .sort({ version: 1 })
      .limit(1000) // Limit to prevent memory issues
      .exec();

    return {
      sessionId: session._id,
      documentId: session.documentId,
      currentVersion: session.currentVersion,
      participants: session.participants,
      operations: operations.map(op => ({
        id: op._id,
        type: op.type,
        position: op.position,
        content: op.content,
        length: op.length,
        attributes: op.attributes,
        userId: op.userId,
        version: op.version,
        timestamp: (op as any).createdAt || new Date(),
      })),
      documentSnapshot: session.documentSnapshot,
    };
  }

  async processOperation(
    sessionId: string,
    userId: string,
    operation: DocumentOperationDto,
  ): Promise<OperationTransformResult> {
    try {
      const session = await this.getSession(sessionId);

      // Validate user is participant
      const participant = session.participants.find(p => p.userId === userId);
      if (!participant) {
        throw new BadRequestException('User is not a participant in this session');
      }

      // Check if user has edit permissions
      if (!['owner', 'editor'].includes(participant.role)) {
        throw new BadRequestException('User does not have edit permissions');
      }

      // Get operations that need to be transformed against
      const conflictingOperations = await this.operationModel
        .find({
          sessionId,
          version: { $gt: operation.version },
          status: OperationStatus.APPLIED,
        })
        .sort({ version: 1 })
        .exec();

      // Transform the operation
      const transformResult = await this.operationalTransformService.transformOperation(
        operation,
        conflictingOperations.map(op => ({
          type: op.type,
          position: op.position,
          content: op.content,
          length: op.length,
          attributes: op.attributes,
          version: op.version,
        })),
      );

      if (!transformResult.success) {
        return transformResult;
      }

      // Create new operation record
      const newVersion = session.currentVersion + 1;
      const operationRecord = new this.operationModel({
        sessionId,
        documentId: session.documentId,
        organizationId: session.organizationId,
        userId,
        type: transformResult.transformedOperation.type,
        position: transformResult.transformedOperation.position,
        content: transformResult.transformedOperation.content,
        length: transformResult.transformedOperation.length,
        attributes: transformResult.transformedOperation.attributes,
        version: newVersion,
        clientId: operation.clientId,
        status: OperationStatus.APPLIED,
        transformedFrom: conflictingOperations.length > 0 ? {
          originalOperation: operation,
          transformedAgainst: conflictingOperations.map(op => op._id.toString()),
        } : undefined,
        metadata: {
          userAgent: 'WebSocket',
          ipAddress: '0.0.0.0', // TODO: Get from socket
          timestamp: new Date(),
          processingTime: 0,
        },
      });

      await operationRecord.save();

      // Update session version and metadata
      await this.sessionModel.updateOne(
        { _id: sessionId },
        {
          $set: {
            currentVersion: newVersion,
            'metadata.totalOperations': session.metadata.totalOperations + 1,
            'metadata.lastActivity': new Date(),
          },
          $inc: {
            'metadata.sessionDuration': 1,
          },
        },
      );

      // Update participant activity
      await this.updateParticipantActivity(sessionId, userId);

      return {
        transformedOperation: {
          ...transformResult.transformedOperation,
          version: newVersion,
        },
        conflicts: transformResult.conflicts,
        success: true,
      };

    } catch (error) {
      this.logger.error(`Error processing operation: ${error.message}`);
      return {
        transformedOperation: operation,
        conflicts: [],
        success: false,
        errorMessage: error.message,
      };
    }
  }

  async joinSession(sessionId: string, userId: string, role: string = 'editor'): Promise<void> {
    const session = await this.getSession(sessionId);

    // Check if user is already a participant
    const existingParticipant = session.participants.find(p => p.userId === userId);
    if (existingParticipant) {
      // Update existing participant
      await this.sessionModel.updateOne(
        { _id: sessionId, 'participants.userId': userId },
        {
          $set: {
            'participants.$.isOnline': true,
            'participants.$.lastActivity': new Date(),
          },
        },
      );
    } else {
      // Add new participant
      await this.sessionModel.updateOne(
        { _id: sessionId },
        {
          $push: {
            participants: {
              userId,
              role,
              joinedAt: new Date(),
              lastActivity: new Date(),
              isOnline: true,
            },
          },
          $inc: {
            'metadata.totalParticipants': 1,
          },
        },
      );
    }
  }

  async leaveSession(sessionId: string, userId: string): Promise<void> {
    await this.sessionModel.updateOne(
      { _id: sessionId, 'participants.userId': userId },
      {
        $set: {
          'participants.$.isOnline': false,
          'participants.$.lastActivity': new Date(),
        },
      },
    );
  }

  async endSession(sessionId: string, userId: string, reason?: string): Promise<void> {
    const session = await this.getSession(sessionId);

    // Only owner can end session
    if (session.ownerId !== userId) {
      throw new BadRequestException('Only session owner can end the session');
    }

    await this.sessionModel.updateOne(
      { _id: sessionId },
      {
        $set: {
          status: 'ended',
          endedAt: new Date(),
          endedBy: userId,
          endReason: reason || 'Session ended by owner',
        },
      },
    );
  }

  async getSessionMetrics(sessionId: string): Promise<SessionMetrics> {
    const session = await this.getSession(sessionId);

    const totalOperations = await this.operationModel.countDocuments({
      sessionId,
      status: OperationStatus.APPLIED,
    });

    const activeParticipants = session.participants.filter(p => p.isOnline).length;

    const sessionDuration = session.endedAt
      ? Math.floor((session.endedAt.getTime() - ((session as any).createdAt || (session as any).updatedAt || new Date()).getTime()) / 1000)
      : Math.floor((new Date().getTime() - ((session as any).createdAt || (session as any).updatedAt || new Date()).getTime()) / 1000);

    return {
      sessionId: session._id.toString(),
      totalOperations,
      activeParticipants,
      sessionDuration,
      averageResponseTime: 0, // TODO: Calculate from operation processing times
      conflictRate: 0, // TODO: Calculate from conflicted operations
      lastActivity: session.metadata.lastActivity,
    };
  }

  private async updateParticipantActivity(sessionId: string, userId: string): Promise<void> {
    await this.sessionModel.updateOne(
      { _id: sessionId, 'participants.userId': userId },
      {
        $set: {
          'participants.$.lastActivity': new Date(),
        },
      },
    );
  }
}

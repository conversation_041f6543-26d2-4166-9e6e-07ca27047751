import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  CollaborationSession,
  CollaborationSessionDocument,
  COLLABORATION_SESSION_MODEL,
  Participant,
} from '../schemas/collaboration-session.schema';
import {
  CursorPosition,
  UserPresence,
} from '../interfaces/collaboration.interface';
import { TenantContextService } from '../../auth/tenant/tenant-context.service';

@Injectable()
export class PresenceService {
  private readonly logger = new Logger(PresenceService.name);
  
  // In-memory cache for real-time presence data
  private presenceCache = new Map<string, Map<string, UserPresence>>();

  constructor(
    @InjectModel(COLLABORATION_SESSION_MODEL)
    private readonly sessionModel: Model<CollaborationSessionDocument>,
    private readonly tenantContext: TenantContextService,
  ) {
    // Clean up stale presence data every 30 seconds
    setInterval(() => this.cleanupStalePresence(), 30000);
  }

  /**
   * Update user presence in a session
   */
  async updateUserPresence(
    sessionId: string,
    userId: string,
    isOnline: boolean,
    additionalData?: Partial<UserPresence>,
  ): Promise<void> {
    try {
      // Update in-memory cache
      if (!this.presenceCache.has(sessionId)) {
        this.presenceCache.set(sessionId, new Map());
      }

      const sessionPresence = this.presenceCache.get(sessionId)!;
      const currentPresence = sessionPresence.get(userId);

      const updatedPresence: UserPresence = {
        userId,
        userName: additionalData?.userName || currentPresence?.userName || 'Unknown User',
        isOnline,
        cursor: additionalData?.cursor || currentPresence?.cursor,
        lastActivity: new Date(),
        role: additionalData?.role || currentPresence?.role || 'viewer',
      };

      sessionPresence.set(userId, updatedPresence);

      // Update database
      await this.sessionModel.updateOne(
        { _id: sessionId, 'participants.userId': userId },
        {
          $set: {
            'participants.$.isOnline': isOnline,
            'participants.$.lastActivity': new Date(),
          },
        },
      );

      this.logger.debug(`Updated presence for user ${userId} in session ${sessionId}: ${isOnline ? 'online' : 'offline'}`);
    } catch (error) {
      this.logger.error(`Error updating user presence: ${error.message}`);
    }
  }

  /**
   * Update user cursor position
   */
  async updateUserCursor(
    sessionId: string,
    userId: string,
    cursor: CursorPosition,
  ): Promise<void> {
    try {
      // Update in-memory cache
      if (!this.presenceCache.has(sessionId)) {
        this.presenceCache.set(sessionId, new Map());
      }

      const sessionPresence = this.presenceCache.get(sessionId)!;
      const currentPresence = sessionPresence.get(userId);

      if (currentPresence) {
        currentPresence.cursor = cursor;
        currentPresence.lastActivity = new Date();
        sessionPresence.set(userId, currentPresence);
      }

      // Update database (less frequently for cursor updates)
      await this.sessionModel.updateOne(
        { _id: sessionId, 'participants.userId': userId },
        {
          $set: {
            'participants.$.cursor': cursor,
            'participants.$.lastActivity': new Date(),
          },
        },
      );

      this.logger.debug(`Updated cursor for user ${userId} in session ${sessionId}`);
    } catch (error) {
      this.logger.error(`Error updating user cursor: ${error.message}`);
    }
  }

  /**
   * Get all users present in a session
   */
  async getSessionPresence(sessionId: string): Promise<UserPresence[]> {
    try {
      // First check cache
      const cachedPresence = this.presenceCache.get(sessionId);
      if (cachedPresence) {
        return Array.from(cachedPresence.values());
      }

      // Fallback to database
      const session = await this.sessionModel.findById(sessionId).exec();
      if (!session) {
        return [];
      }

      const presence: UserPresence[] = session.participants.map(participant => ({
        userId: participant.userId,
        userName: 'Unknown User', // TODO: Get from user service
        isOnline: participant.isOnline,
        cursor: participant.cursor,
        lastActivity: participant.lastActivity,
        role: participant.role,
      }));

      // Update cache
      const sessionPresence = new Map<string, UserPresence>();
      presence.forEach(p => sessionPresence.set(p.userId, p));
      this.presenceCache.set(sessionId, sessionPresence);

      return presence;
    } catch (error) {
      this.logger.error(`Error getting session presence: ${error.message}`);
      return [];
    }
  }

  /**
   * Get presence for a specific user in a session
   */
  async getUserPresence(sessionId: string, userId: string): Promise<UserPresence | null> {
    try {
      // Check cache first
      const sessionPresence = this.presenceCache.get(sessionId);
      if (sessionPresence?.has(userId)) {
        return sessionPresence.get(userId)!;
      }

      // Fallback to database
      const session = await this.sessionModel.findById(sessionId).exec();
      if (!session) {
        return null;
      }

      const participant = session.participants.find(p => p.userId === userId);
      if (!participant) {
        return null;
      }

      const presence: UserPresence = {
        userId: participant.userId,
        userName: 'Unknown User', // TODO: Get from user service
        isOnline: participant.isOnline,
        cursor: participant.cursor,
        lastActivity: participant.lastActivity,
        role: participant.role,
      };

      // Update cache
      if (!this.presenceCache.has(sessionId)) {
        this.presenceCache.set(sessionId, new Map());
      }
      this.presenceCache.get(sessionId)!.set(userId, presence);

      return presence;
    } catch (error) {
      this.logger.error(`Error getting user presence: ${error.message}`);
      return null;
    }
  }

  /**
   * Get online users count for a session
   */
  async getOnlineUsersCount(sessionId: string): Promise<number> {
    const presence = await this.getSessionPresence(sessionId);
    return presence.filter(p => p.isOnline).length;
  }

  /**
   * Check if a user is online in a session
   */
  async isUserOnline(sessionId: string, userId: string): Promise<boolean> {
    const userPresence = await this.getUserPresence(sessionId, userId);
    return userPresence?.isOnline || false;
  }

  /**
   * Remove user from session presence
   */
  async removeUserFromSession(sessionId: string, userId: string): Promise<void> {
    try {
      // Remove from cache
      const sessionPresence = this.presenceCache.get(sessionId);
      if (sessionPresence) {
        sessionPresence.delete(userId);
        if (sessionPresence.size === 0) {
          this.presenceCache.delete(sessionId);
        }
      }

      // Update database
      await this.sessionModel.updateOne(
        { _id: sessionId },
        {
          $pull: {
            participants: { userId },
          },
        },
      );

      this.logger.debug(`Removed user ${userId} from session ${sessionId}`);
    } catch (error) {
      this.logger.error(`Error removing user from session: ${error.message}`);
    }
  }

  /**
   * Clean up stale presence data
   */
  private cleanupStalePresence(): void {
    const staleThreshold = 5 * 60 * 1000; // 5 minutes
    const now = new Date();

    for (const [sessionId, sessionPresence] of this.presenceCache.entries()) {
      const staleUsers: string[] = [];

      for (const [userId, presence] of sessionPresence.entries()) {
        const timeSinceLastActivity = now.getTime() - presence.lastActivity.getTime();
        if (timeSinceLastActivity > staleThreshold) {
          staleUsers.push(userId);
        }
      }

      // Remove stale users
      for (const userId of staleUsers) {
        sessionPresence.delete(userId);
        this.logger.debug(`Cleaned up stale presence for user ${userId} in session ${sessionId}`);
      }

      // Remove empty sessions
      if (sessionPresence.size === 0) {
        this.presenceCache.delete(sessionId);
      }
    }
  }

  /**
   * Get presence statistics for a session
   */
  async getPresenceStats(sessionId: string): Promise<{
    totalParticipants: number;
    onlineParticipants: number;
    activeParticipants: number; // Active in last 5 minutes
    cursorsVisible: number;
  }> {
    const presence = await this.getSessionPresence(sessionId);
    const now = new Date();
    const activeThreshold = 5 * 60 * 1000; // 5 minutes

    const stats = {
      totalParticipants: presence.length,
      onlineParticipants: presence.filter(p => p.isOnline).length,
      activeParticipants: presence.filter(p => {
        const timeSinceActivity = now.getTime() - p.lastActivity.getTime();
        return p.isOnline && timeSinceActivity < activeThreshold;
      }).length,
      cursorsVisible: presence.filter(p => p.isOnline && p.cursor).length,
    };

    return stats;
  }

  /**
   * Broadcast presence update to all users in session
   */
  async broadcastPresenceUpdate(
    sessionId: string,
    userId: string,
    updateType: 'join' | 'leave' | 'cursor' | 'activity',
  ): Promise<void> {
    // This would typically be handled by the WebSocket gateway
    // We'll emit an event that the gateway can listen to
    this.logger.debug(`Broadcasting presence update: ${updateType} for user ${userId} in session ${sessionId}`);
  }
}

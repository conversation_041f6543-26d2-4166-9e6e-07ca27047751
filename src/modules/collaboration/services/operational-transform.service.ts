import { Injectable, Logger } from '@nestjs/common';
import {
  DocumentOperationDto,
  OperationTransformResult,
} from '../interfaces/collaboration.interface';

export interface Operation {
  type: 'insert' | 'delete' | 'retain' | 'format';
  position: number;
  content?: string;
  length?: number;
  attributes?: Record<string, any>;
  version?: number;
  clientId?: string;
}

@Injectable()
export class OperationalTransformService {
  private readonly logger = new Logger(OperationalTransformService.name);

  /**
   * Transform an operation against a list of concurrent operations
   */
  async transformOperation(
    operation: DocumentOperationDto,
    concurrentOperations: Operation[],
  ): Promise<OperationTransformResult> {
    try {
      let transformedOp = { ...operation };
      const conflicts: string[] = [];

      // Transform against each concurrent operation
      for (const concurrentOp of concurrentOperations) {
        const result = this.transformAgainstSingle(transformedOp, concurrentOp);
        transformedOp = {
          ...result.operation,
          clientId: operation.clientId,
          version: operation.version
        };

        if (result.hasConflict) {
          conflicts.push(`Conflict with operation at position ${concurrentOp.position}`);
        }
      }

      return {
        transformedOperation: transformedOp,
        conflicts,
        success: true,
      };
    } catch (error) {
      this.logger.error(`Transformation error: ${error.message}`);
      return {
        transformedOperation: operation,
        conflicts: [],
        success: false,
        errorMessage: error.message,
      };
    }
  }

  /**
   * Transform an operation against a single concurrent operation
   */
  private transformAgainstSingle(
    operation: Operation,
    concurrentOp: Operation,
  ): { operation: Operation; hasConflict: boolean } {
    const op1 = { ...operation };
    const op2 = concurrentOp;

    let hasConflict = false;

    // Handle different operation type combinations
    if (op1.type === 'insert' && op2.type === 'insert') {
      // Both insertions
      if (op2.position <= op1.position) {
        op1.position += op2.content?.length || 0;
      }
      // If positions are equal, we have a conflict
      if (op1.position === op2.position) {
        hasConflict = true;
      }
    } else if (op1.type === 'insert' && op2.type === 'delete') {
      // Insert vs Delete
      if (op2.position < op1.position) {
        op1.position -= op2.length || 0;
      } else if (op2.position === op1.position) {
        hasConflict = true;
      }
    } else if (op1.type === 'delete' && op2.type === 'insert') {
      // Delete vs Insert
      if (op2.position <= op1.position) {
        op1.position += op2.content?.length || 0;
      }
    } else if (op1.type === 'delete' && op2.type === 'delete') {
      // Both deletions
      if (op2.position < op1.position) {
        op1.position -= op2.length || 0;
      } else if (op2.position === op1.position) {
        // Overlapping deletions - conflict
        hasConflict = true;
        // Adjust length to avoid double deletion
        const overlap = Math.min(op1.length || 0, op2.length || 0);
        op1.length = Math.max(0, (op1.length || 0) - overlap);
      }
    } else if (op1.type === 'format' && op2.type === 'insert') {
      // Format vs Insert
      if (op2.position <= op1.position) {
        op1.position += op2.content?.length || 0;
      }
    } else if (op1.type === 'format' && op2.type === 'delete') {
      // Format vs Delete
      if (op2.position < op1.position) {
        op1.position -= op2.length || 0;
      } else if (this.rangesOverlap(op1, op2)) {
        hasConflict = true;
      }
    } else if (op1.type === 'format' && op2.type === 'format') {
      // Both format operations
      if (this.rangesOverlap(op1, op2)) {
        hasConflict = true;
        // Merge attributes if possible
        op1.attributes = { ...op2.attributes, ...op1.attributes };
      }
    }

    return { operation: op1, hasConflict };
  }

  /**
   * Check if two operations have overlapping ranges
   */
  private rangesOverlap(op1: Operation, op2: Operation): boolean {
    const op1End = op1.position + (op1.length || 0);
    const op2End = op2.position + (op2.length || 0);

    return !(op1End <= op2.position || op2End <= op1.position);
  }

  /**
   * Apply an operation to a document content
   */
  applyOperation(content: string, operation: Operation): string {
    try {
      switch (operation.type) {
        case 'insert':
          return (
            content.slice(0, operation.position) +
            (operation.content || '') +
            content.slice(operation.position)
          );

        case 'delete':
          return (
            content.slice(0, operation.position) +
            content.slice(operation.position + (operation.length || 0))
          );

        case 'retain':
          // Retain operations don't change content
          return content;

        case 'format':
          // Format operations don't change text content
          // They would be handled by a rich text editor
          return content;

        default:
          throw new Error(`Unknown operation type: ${operation.type}`);
      }
    } catch (error) {
      this.logger.error(`Error applying operation: ${error.message}`);
      return content;
    }
  }

  /**
   * Compose multiple operations into a single operation
   */
  composeOperations(operations: Operation[]): Operation[] {
    if (operations.length === 0) return [];
    if (operations.length === 1) return operations;

    // Simple composition - can be enhanced for better optimization
    const composed: Operation[] = [];
    let currentOp = operations[0];

    for (let i = 1; i < operations.length; i++) {
      const nextOp = operations[i];

      // Try to merge consecutive operations of the same type
      if (this.canMergeOperations(currentOp, nextOp)) {
        currentOp = this.mergeOperations(currentOp, nextOp);
      } else {
        composed.push(currentOp);
        currentOp = nextOp;
      }
    }

    composed.push(currentOp);
    return composed;
  }

  /**
   * Check if two operations can be merged
   */
  private canMergeOperations(op1: Operation, op2: Operation): boolean {
    if (op1.type !== op2.type) return false;

    switch (op1.type) {
      case 'insert':
        return op1.position + (op1.content?.length || 0) === op2.position;
      case 'delete':
        return op1.position === op2.position;
      default:
        return false;
    }
  }

  /**
   * Merge two compatible operations
   */
  private mergeOperations(op1: Operation, op2: Operation): Operation {
    switch (op1.type) {
      case 'insert':
        return {
          ...op1,
          content: (op1.content || '') + (op2.content || ''),
        };
      case 'delete':
        return {
          ...op1,
          length: (op1.length || 0) + (op2.length || 0),
        };
      default:
        return op1;
    }
  }

  /**
   * Invert an operation (for undo functionality)
   */
  invertOperation(operation: Operation, documentContent: string): Operation {
    switch (operation.type) {
      case 'insert':
        return {
          type: 'delete',
          position: operation.position,
          length: operation.content?.length || 0,
        };

      case 'delete':
        const deletedContent = documentContent.slice(
          operation.position,
          operation.position + (operation.length || 0),
        );
        return {
          type: 'insert',
          position: operation.position,
          content: deletedContent,
        };

      case 'format':
        // For format operations, we'd need to store the previous attributes
        // This is a simplified version
        return {
          type: 'format',
          position: operation.position,
          length: operation.length,
          attributes: {}, // Previous attributes would go here
        };

      default:
        return operation;
    }
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  CommentThread,
  CommentThreadDocument,
  COMMENT_THREAD_MODEL,
} from '../schemas/comment-thread.schema';
import { TenantContextService } from '../../auth/tenant/tenant-context.service';

export interface MentionNotification {
  id: string;
  threadId: string;
  commentId?: string;
  mentionedUserId: string;
  mentionedByUserId: string;
  mentionedByUserName: string;
  content: string;
  context: string;
  timestamp: Date;
  readAt?: Date;
  documentId: string;
  documentTitle: string;
}

@Injectable()
export class MentionsService {
  private readonly logger = new Logger(MentionsService.name);

  constructor(
    @InjectModel(COMMENT_THREAD_MODEL)
    private readonly threadModel: Model<CommentThreadDocument>,
    private readonly tenantContext: TenantContextService,
  ) {}

  /**
   * Process mentions in a comment
   */
  async processMentions(
    threadId: string,
    mentionedUserIds: string[],
    mentionedByUserId: string,
    content: string,
  ): Promise<void> {
    try {
      const thread = await this.threadModel.findById(threadId).exec();
      if (!thread) {
        this.logger.error(`Thread not found: ${threadId}`);
        return;
      }

      for (const userId of mentionedUserIds) {
        await this.createMentionNotification(
          threadId,
          userId,
          mentionedByUserId,
          content,
          thread,
        );

        // Add mentioned user to thread watchers
        await this.threadModel.updateOne(
          { _id: threadId },
          { $addToSet: { watchers: userId } },
        );
      }

      this.logger.log(`Processed ${mentionedUserIds.length} mentions in thread ${threadId}`);
    } catch (error) {
      this.logger.error(`Error processing mentions: ${error.message}`);
    }
  }

  /**
   * Create mention notification
   */
  private async createMentionNotification(
    threadId: string,
    mentionedUserId: string,
    mentionedByUserId: string,
    content: string,
    thread: CommentThread,
  ): Promise<void> {
    try {
      const notification = {
        id: `mention_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: 'mention' as const,
        userId: mentionedUserId,
        sentAt: new Date(),
      };

      // Add notification to thread
      await this.threadModel.updateOne(
        { _id: threadId },
        { $push: { notifications: notification } },
      );

      // TODO: Send actual notification (email, push, etc.)
      await this.sendMentionNotification({
        id: notification.id,
        threadId,
        mentionedUserId,
        mentionedByUserId,
        mentionedByUserName: 'User', // TODO: Get from user service
        content: this.truncateContent(content),
        context: thread.title,
        timestamp: new Date(),
        documentId: thread.documentId,
        documentTitle: thread.metadata.documentTitle,
      });

      this.logger.log(`Mention notification created for user ${mentionedUserId}`);
    } catch (error) {
      this.logger.error(`Error creating mention notification: ${error.message}`);
    }
  }

  /**
   * Send mention notification
   */
  private async sendMentionNotification(notification: MentionNotification): Promise<void> {
    try {
      // TODO: Integrate with notification service
      // For now, just log the notification
      this.logger.log(
        `Mention notification: ${notification.mentionedByUserName} mentioned you in "${notification.context}"`,
      );

      // In a real implementation, you would:
      // 1. Send email notification
      // 2. Send push notification
      // 3. Create in-app notification
      // 4. Send webhook if configured
      
    } catch (error) {
      this.logger.error(`Error sending mention notification: ${error.message}`);
    }
  }

  /**
   * Get mentions for a user
   */
  async getUserMentions(
    userId: string,
    unreadOnly: boolean = false,
    limit: number = 50,
    offset: number = 0,
  ): Promise<{
    mentions: MentionNotification[];
    total: number;
    unreadCount: number;
  }> {
    const organizationId = this.tenantContext.getCurrentOrganization();
    
    const query: any = {
      organizationId,
      'notifications.userId': userId,
      'notifications.type': 'mention',
    };

    if (unreadOnly) {
      query['notifications.readAt'] = { $exists: false };
    }

    const threads = await this.threadModel
      .find(query)
      .sort({ 'notifications.sentAt': -1 })
      .skip(offset)
      .limit(limit)
      .exec();

    const mentions: MentionNotification[] = [];
    let unreadCount = 0;

    for (const thread of threads) {
      for (const notification of thread.notifications) {
        if (notification.type === 'mention' && notification.userId === userId) {
          mentions.push({
            id: notification.id,
            threadId: thread._id.toString(),
            mentionedUserId: userId,
            mentionedByUserId: 'unknown', // TODO: Get from notification data
            mentionedByUserName: 'Unknown User',
            content: 'Mentioned you in a comment',
            context: thread.title,
            timestamp: notification.sentAt,
            readAt: notification.readAt,
            documentId: thread.documentId,
            documentTitle: thread.metadata.documentTitle,
          });

          if (!notification.readAt) {
            unreadCount++;
          }
        }
      }
    }

    const total = await this.threadModel.countDocuments(query);

    return {
      mentions,
      total,
      unreadCount,
    };
  }

  /**
   * Mark mention as read
   */
  async markMentionAsRead(threadId: string, notificationId: string, userId: string): Promise<void> {
    try {
      await this.threadModel.updateOne(
        {
          _id: threadId,
          'notifications.id': notificationId,
          'notifications.userId': userId,
        },
        {
          $set: {
            'notifications.$.readAt': new Date(),
          },
        },
      );

      this.logger.log(`Mention marked as read: ${notificationId} for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error marking mention as read: ${error.message}`);
    }
  }

  /**
   * Mark all mentions as read for a user
   */
  async markAllMentionsAsRead(userId: string): Promise<void> {
    const organizationId = this.tenantContext.getCurrentOrganization();
    
    try {
      await this.threadModel.updateMany(
        {
          organizationId,
          'notifications.userId': userId,
          'notifications.type': 'mention',
          'notifications.readAt': { $exists: false },
        },
        {
          $set: {
            'notifications.$[elem].readAt': new Date(),
          },
        },
        {
          arrayFilters: [
            {
              'elem.userId': userId,
              'elem.type': 'mention',
              'elem.readAt': { $exists: false },
            },
          ],
        },
      );

      this.logger.log(`All mentions marked as read for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error marking all mentions as read: ${error.message}`);
    }
  }

  /**
   * Extract mentions from content
   */
  extractMentions(content: string): string[] {
    const mentionRegex = /@\[([^\]]+)\]\(([^)]+)\)/g;
    const mentions: string[] = [];
    let match;

    while ((match = mentionRegex.exec(content)) !== null) {
      const userId = match[2];
      if (userId && !mentions.includes(userId)) {
        mentions.push(userId);
      }
    }

    return mentions;
  }

  /**
   * Format content with mentions
   */
  formatMentions(content: string, userMap: Record<string, string>): string {
    const mentionRegex = /@\[([^\]]+)\]\(([^)]+)\)/g;
    
    return content.replace(mentionRegex, (match, displayName, userId) => {
      const actualName = userMap[userId] || displayName;
      return `@${actualName}`;
    });
  }

  /**
   * Get mention statistics for a user
   */
  async getMentionStatistics(userId: string): Promise<{
    totalMentions: number;
    unreadMentions: number;
    mentionsThisWeek: number;
    mentionsThisMonth: number;
    topMentioners: { userId: string; count: number }[];
  }> {
    const organizationId = this.tenantContext.getCurrentOrganization();
    
    const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    // This is a simplified implementation
    // In a real scenario, you might want to use aggregation pipelines for better performance
    
    const allThreads = await this.threadModel.find({
      organizationId,
      'notifications.userId': userId,
      'notifications.type': 'mention',
    }).exec();

    let totalMentions = 0;
    let unreadMentions = 0;
    let mentionsThisWeek = 0;
    let mentionsThisMonth = 0;
    const mentionerCounts: Record<string, number> = {};

    for (const thread of allThreads) {
      for (const notification of thread.notifications) {
        if (notification.type === 'mention' && notification.userId === userId) {
          totalMentions++;
          
          if (!notification.readAt) {
            unreadMentions++;
          }
          
          if (notification.sentAt >= oneWeekAgo) {
            mentionsThisWeek++;
          }
          
          if (notification.sentAt >= oneMonthAgo) {
            mentionsThisMonth++;
          }

          // Count mentioners (this would need to be stored in notification data)
          // For now, we'll use a placeholder
          const mentionerId = 'unknown';
          mentionerCounts[mentionerId] = (mentionerCounts[mentionerId] || 0) + 1;
        }
      }
    }

    const topMentioners = Object.entries(mentionerCounts)
      .map(([userId, count]) => ({ userId, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    return {
      totalMentions,
      unreadMentions,
      mentionsThisWeek,
      mentionsThisMonth,
      topMentioners,
    };
  }

  /**
   * Truncate content for notifications
   */
  private truncateContent(content: string, maxLength: number = 100): string {
    if (content.length <= maxLength) {
      return content;
    }
    
    return content.substring(0, maxLength - 3) + '...';
  }
}

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RequireFeatures } from '../../subscription/decorators/require-features.decorator';
import { UseCredits, FreeFeature } from '../../subscription/decorators/use-credits.decorator';
import { Organization } from '../../auth/decorators/organization.decorator';
import { User } from '../../auth/decorators/user.decorator';
import { RealTimeEditingService } from '../services/real-time-editing.service';
import { PresenceService } from '../services/presence.service';
import {
  CreateCollaborationSessionDto,
  JoinSessionDto,
  SessionMetrics,
  UserPresence,
} from '../interfaces/collaboration.interface';
import { CollaborationSession } from '../schemas/collaboration-session.schema';

@ApiTags('Collaboration')
@ApiBearerAuth()
@Controller('collaboration')
@UseGuards(JwtAuthGuard)
export class CollaborationController {
  private readonly logger = new Logger(CollaborationController.name);

  constructor(
    private readonly realTimeEditingService: RealTimeEditingService,
    private readonly presenceService: PresenceService,
  ) {}

  @Post('sessions')
  @RequireFeatures('real_time_collaboration')
  @UseCredits('real_time_collaboration', 2)
  @ApiOperation({ summary: 'Create a new collaboration session' })
  @ApiResponse({ status: 201, description: 'Collaboration session created successfully' })
  async createSession(
    @Body() createSessionDto: CreateCollaborationSessionDto,
    @Organization() organizationId: string,
    @User() user: any,
  ): Promise<CollaborationSession> {
    this.logger.log(`Creating collaboration session for document ${createSessionDto.documentId}`);

    const userId = typeof user === 'string' ? user : user.userId || user.sub;

    return this.realTimeEditingService.createSession(
      createSessionDto.documentId,
      createSessionDto.sessionName,
      userId,
      createSessionDto.settings,
    );
  }

  @Get('sessions/:sessionId')
  @FreeFeature() // Viewing session details is free
  @RequireFeatures('real_time_collaboration')
  @ApiOperation({ summary: 'Get collaboration session details' })
  @ApiParam({ name: 'sessionId', description: 'Collaboration session ID' })
  @ApiResponse({ status: 200, description: 'Session details retrieved successfully' })
  async getSession(
    @Param('sessionId') sessionId: string,
  ): Promise<CollaborationSession> {
    return this.realTimeEditingService.getSession(sessionId);
  }

  @Get('sessions/:sessionId/state')
  @FreeFeature() // Viewing session state is free
  @RequireFeatures('real_time_collaboration')
  @ApiOperation({ summary: 'Get current session state for joining' })
  @ApiParam({ name: 'sessionId', description: 'Collaboration session ID' })
  @ApiResponse({ status: 200, description: 'Session state retrieved successfully' })
  async getSessionState(
    @Param('sessionId') sessionId: string,
  ): Promise<any> {
    return this.realTimeEditingService.getSessionState(sessionId);
  }

  @Post('sessions/:sessionId/join')
  @FreeFeature() // Joining sessions is free
  @RequireFeatures('real_time_collaboration')
  @ApiOperation({ summary: 'Join a collaboration session' })
  @ApiParam({ name: 'sessionId', description: 'Collaboration session ID' })
  @ApiResponse({ status: 200, description: 'Successfully joined session' })
  async joinSession(
    @Param('sessionId') sessionId: string,
    @Body() joinSessionDto: JoinSessionDto,
    @User() user: any,
  ): Promise<{ success: boolean; message: string }> {
    const userId = typeof user === 'string' ? user : user.userId || user.sub;

    await this.realTimeEditingService.joinSession(
      sessionId,
      userId,
      joinSessionDto.role || 'editor',
    );

    return {
      success: true,
      message: 'Successfully joined collaboration session',
    };
  }

  @Post('sessions/:sessionId/leave')
  @FreeFeature() // Leaving sessions is free
  @RequireFeatures('real_time_collaboration')
  @ApiOperation({ summary: 'Leave a collaboration session' })
  @ApiParam({ name: 'sessionId', description: 'Collaboration session ID' })
  @ApiResponse({ status: 200, description: 'Successfully left session' })
  async leaveSession(
    @Param('sessionId') sessionId: string,
    @User() user: any,
  ): Promise<{ success: boolean; message: string }> {
    const userId = typeof user === 'string' ? user : user.userId || user.sub;

    await this.realTimeEditingService.leaveSession(sessionId, userId);

    return {
      success: true,
      message: 'Successfully left collaboration session',
    };
  }

  @Delete('sessions/:sessionId')
  @FreeFeature() // Ending sessions is free
  @RequireFeatures('real_time_collaboration')
  @ApiOperation({ summary: 'End a collaboration session' })
  @ApiParam({ name: 'sessionId', description: 'Collaboration session ID' })
  @ApiResponse({ status: 200, description: 'Session ended successfully' })
  async endSession(
    @Param('sessionId') sessionId: string,
    @Body() body: { reason?: string },
    @User() user: any,
  ): Promise<{ success: boolean; message: string }> {
    const userId = typeof user === 'string' ? user : user.userId || user.sub;

    await this.realTimeEditingService.endSession(sessionId, userId, body.reason);

    return {
      success: true,
      message: 'Collaboration session ended successfully',
    };
  }

  @Get('sessions/:sessionId/metrics')
  @FreeFeature() // Viewing session metrics is free
  @RequireFeatures('real_time_collaboration')
  @ApiOperation({ summary: 'Get collaboration session metrics' })
  @ApiParam({ name: 'sessionId', description: 'Collaboration session ID' })
  @ApiResponse({ status: 200, description: 'Session metrics retrieved successfully' })
  async getSessionMetrics(
    @Param('sessionId') sessionId: string,
  ): Promise<SessionMetrics> {
    return this.realTimeEditingService.getSessionMetrics(sessionId);
  }

  @Get('sessions/:sessionId/presence')
  @RequireFeatures('real_time_collaboration')
  @ApiOperation({ summary: 'Get user presence in session' })
  @ApiParam({ name: 'sessionId', description: 'Collaboration session ID' })
  @ApiResponse({ status: 200, description: 'Presence data retrieved successfully' })
  async getSessionPresence(
    @Param('sessionId') sessionId: string,
  ): Promise<UserPresence[]> {
    return this.presenceService.getSessionPresence(sessionId);
  }

  @Get('sessions/:sessionId/presence/stats')
  @RequireFeatures('real_time_collaboration')
  @ApiOperation({ summary: 'Get presence statistics for session' })
  @ApiParam({ name: 'sessionId', description: 'Collaboration session ID' })
  @ApiResponse({ status: 200, description: 'Presence statistics retrieved successfully' })
  async getPresenceStats(
    @Param('sessionId') sessionId: string,
  ): Promise<{
    totalParticipants: number;
    onlineParticipants: number;
    activeParticipants: number;
    cursorsVisible: number;
  }> {
    return this.presenceService.getPresenceStats(sessionId);
  }

  @Get('documents/:documentId/sessions')
  @RequireFeatures('real_time_collaboration')
  @ApiOperation({ summary: 'Get all collaboration sessions for a document' })
  @ApiParam({ name: 'documentId', description: 'Document ID' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by session status' })
  @ApiResponse({ status: 200, description: 'Document sessions retrieved successfully' })
  async getDocumentSessions(
    @Param('documentId') documentId: string,
    @Organization() organizationId: string,
    @Query('status') status?: string,
  ): Promise<CollaborationSession[]> {
    // This would be implemented in the real-time editing service
    // For now, return empty array
    return [];
  }

  @Get('sessions')
  @RequireFeatures('real_time_collaboration')
  @ApiOperation({ summary: 'Get user\'s collaboration sessions' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by session status' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limit number of results' })
  @ApiQuery({ name: 'offset', required: false, description: 'Offset for pagination' })
  @ApiResponse({ status: 200, description: 'User sessions retrieved successfully' })
  async getUserSessions(
    @User() user: any,
    @Organization() organizationId: string,
    @Query('status') status?: string,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ): Promise<{
    sessions: CollaborationSession[];
    total: number;
    hasMore: boolean;
  }> {
    // This would be implemented in the real-time editing service
    // For now, return empty result
    return {
      sessions: [],
      total: 0,
      hasMore: false,
    };
  }
}

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RequireFeatures } from '../../subscription/decorators/require-features.decorator';
import { UseCredits } from '../../subscription/decorators/use-credits.decorator';
import { Organization } from '../../auth/decorators/organization.decorator';
import { User } from '../../auth/decorators/user.decorator';
import { ThreadedCommentsService, CreateThreadDto, AddCommentDto } from '../services/threaded-comments.service';
import { MentionsService } from '../services/mentions.service';
import { CommentThread, ThreadStatus } from '../schemas/comment-thread.schema';

@ApiTags('Threaded Discussions')
@ApiBearerAuth()
@Controller('comments')
@UseGuards(JwtAuthGuard)
export class CommentsController {
  private readonly logger = new Logger(CommentsController.name);

  constructor(
    private readonly threadedCommentsService: ThreadedCommentsService,
    private readonly mentionsService: MentionsService,
  ) {}

  @Post('threads')
  @RequireFeatures('threaded_discussions')
  @UseCredits('threaded_discussions', 0.5)
  @ApiOperation({ summary: 'Create a new comment thread' })
  @ApiResponse({ status: 201, description: 'Comment thread created successfully' })
  async createThread(
    @Body() createThreadDto: CreateThreadDto,
    @User() user: any,
  ): Promise<CommentThread> {
    this.logger.log(`Creating comment thread for document ${createThreadDto.documentId}`);

    const userId = typeof user === 'string' ? user : user.userId || user.sub;

    return this.threadedCommentsService.createThread(createThreadDto, userId);
  }

  @Get('threads/:threadId')
  @RequireFeatures('threaded_discussions')
  @ApiOperation({ summary: 'Get comment thread details' })
  @ApiParam({ name: 'threadId', description: 'Comment thread ID' })
  @ApiResponse({ status: 200, description: 'Thread details retrieved successfully' })
  async getThread(
    @Param('threadId') threadId: string,
  ): Promise<CommentThread> {
    return this.threadedCommentsService.getThread(threadId);
  }

  @Post('threads/:threadId/comments')
  @RequireFeatures('threaded_discussions')
  @ApiOperation({ summary: 'Add a comment to a thread' })
  @ApiParam({ name: 'threadId', description: 'Comment thread ID' })
  @ApiResponse({ status: 201, description: 'Comment added successfully' })
  async addComment(
    @Param('threadId') threadId: string,
    @Body() addCommentDto: AddCommentDto,
    @User() user: any,
  ): Promise<CommentThread> {
    const userId = typeof user === 'string' ? user : user.userId || user.sub;
    const userName = typeof user === 'object' ? user.name || 'Unknown User' : 'Unknown User';

    // Extract mentions from content if not provided
    if (!addCommentDto.mentions) {
      addCommentDto.mentions = this.mentionsService.extractMentions(addCommentDto.content);
    }

    return this.threadedCommentsService.addComment(threadId, addCommentDto, userId, userName);
  }

  @Put('threads/:threadId/resolve')
  @RequireFeatures('threaded_discussions')
  @ApiOperation({ summary: 'Resolve a comment thread' })
  @ApiParam({ name: 'threadId', description: 'Comment thread ID' })
  @ApiResponse({ status: 200, description: 'Thread resolved successfully' })
  async resolveThread(
    @Param('threadId') threadId: string,
    @Body() body: { resolutionNote: string },
    @User() user: any,
  ): Promise<CommentThread> {
    const userId = typeof user === 'string' ? user : user.userId || user.sub;

    return this.threadedCommentsService.resolveThread(threadId, body.resolutionNote, userId);
  }

  @Post('threads/:threadId/comments/:commentId/reactions')
  @RequireFeatures('threaded_discussions')
  @ApiOperation({ summary: 'Add reaction to a comment' })
  @ApiParam({ name: 'threadId', description: 'Comment thread ID' })
  @ApiParam({ name: 'commentId', description: 'Comment ID' })
  @ApiResponse({ status: 200, description: 'Reaction added successfully' })
  async addReaction(
    @Param('threadId') threadId: string,
    @Param('commentId') commentId: string,
    @Body() body: { emoji: string },
    @User() user: any,
  ): Promise<CommentThread> {
    const userId = typeof user === 'string' ? user : user.userId || user.sub;

    return this.threadedCommentsService.addReaction(threadId, commentId, body.emoji, userId);
  }

  @Post('threads/:threadId/watch')
  @RequireFeatures('threaded_discussions')
  @ApiOperation({ summary: 'Watch a comment thread' })
  @ApiParam({ name: 'threadId', description: 'Comment thread ID' })
  @ApiResponse({ status: 200, description: 'Thread watched successfully' })
  async watchThread(
    @Param('threadId') threadId: string,
    @User() user: any,
  ): Promise<{ success: boolean; message: string }> {
    const userId = typeof user === 'string' ? user : user.userId || user.sub;

    await this.threadedCommentsService.addWatcher(threadId, userId);

    return {
      success: true,
      message: 'Thread watched successfully',
    };
  }

  @Delete('threads/:threadId/watch')
  @RequireFeatures('threaded_discussions')
  @ApiOperation({ summary: 'Unwatch a comment thread' })
  @ApiParam({ name: 'threadId', description: 'Comment thread ID' })
  @ApiResponse({ status: 200, description: 'Thread unwatched successfully' })
  async unwatchThread(
    @Param('threadId') threadId: string,
    @User() user: any,
  ): Promise<{ success: boolean; message: string }> {
    const userId = typeof user === 'string' ? user : user.userId || user.sub;

    await this.threadedCommentsService.removeWatcher(threadId, userId);

    return {
      success: true,
      message: 'Thread unwatched successfully',
    };
  }

  @Get('documents/:documentId/threads')
  @RequireFeatures('threaded_discussions')
  @ApiOperation({ summary: 'Get all comment threads for a document' })
  @ApiParam({ name: 'documentId', description: 'Document ID' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by thread status' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limit number of results' })
  @ApiQuery({ name: 'offset', required: false, description: 'Offset for pagination' })
  @ApiResponse({ status: 200, description: 'Document threads retrieved successfully' })
  async getDocumentThreads(
    @Param('documentId') documentId: string,
    @Query('status') status?: ThreadStatus,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ): Promise<{
    threads: CommentThread[];
    total: number;
    hasMore: boolean;
  }> {
    return this.threadedCommentsService.getDocumentThreads(
      documentId,
      status,
      limit || 50,
      offset || 0,
    );
  }

  @Get('mentions')
  @RequireFeatures('threaded_discussions')
  @ApiOperation({ summary: 'Get user mentions' })
  @ApiQuery({ name: 'unreadOnly', required: false, description: 'Show only unread mentions' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limit number of results' })
  @ApiQuery({ name: 'offset', required: false, description: 'Offset for pagination' })
  @ApiResponse({ status: 200, description: 'User mentions retrieved successfully' })
  async getUserMentions(
    @User() user: any,
    @Query('unreadOnly') unreadOnly?: boolean,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ): Promise<{
    mentions: any[];
    total: number;
    unreadCount: number;
  }> {
    const userId = typeof user === 'string' ? user : user.userId || user.sub;

    return this.mentionsService.getUserMentions(
      userId,
      unreadOnly || false,
      limit || 50,
      offset || 0,
    );
  }

  @Put('mentions/:threadId/:notificationId/read')
  @RequireFeatures('threaded_discussions')
  @ApiOperation({ summary: 'Mark mention as read' })
  @ApiParam({ name: 'threadId', description: 'Thread ID' })
  @ApiParam({ name: 'notificationId', description: 'Notification ID' })
  @ApiResponse({ status: 200, description: 'Mention marked as read' })
  async markMentionAsRead(
    @Param('threadId') threadId: string,
    @Param('notificationId') notificationId: string,
    @User() user: any,
  ): Promise<{ success: boolean; message: string }> {
    const userId = typeof user === 'string' ? user : user.userId || user.sub;

    await this.mentionsService.markMentionAsRead(threadId, notificationId, userId);

    return {
      success: true,
      message: 'Mention marked as read',
    };
  }

  @Put('mentions/read-all')
  @RequireFeatures('threaded_discussions')
  @ApiOperation({ summary: 'Mark all mentions as read' })
  @ApiResponse({ status: 200, description: 'All mentions marked as read' })
  async markAllMentionsAsRead(
    @User() user: any,
  ): Promise<{ success: boolean; message: string }> {
    const userId = typeof user === 'string' ? user : user.userId || user.sub;

    await this.mentionsService.markAllMentionsAsRead(userId);

    return {
      success: true,
      message: 'All mentions marked as read',
    };
  }

  @Get('mentions/stats')
  @RequireFeatures('threaded_discussions')
  @ApiOperation({ summary: 'Get mention statistics' })
  @ApiResponse({ status: 200, description: 'Mention statistics retrieved successfully' })
  async getMentionStatistics(
    @User() user: any,
  ): Promise<{
    totalMentions: number;
    unreadMentions: number;
    mentionsThisWeek: number;
    mentionsThisMonth: number;
    topMentioners: { userId: string; count: number }[];
  }> {
    const userId = typeof user === 'string' ? user : user.userId || user.sub;

    return this.mentionsService.getMentionStatistics(userId);
  }
}

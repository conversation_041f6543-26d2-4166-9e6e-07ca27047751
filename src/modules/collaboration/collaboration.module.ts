import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import { AuthModule } from '../auth/auth.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { DocumentsModule } from '../documents/documents.module';

// Controllers
import { CollaborationController } from './controllers/collaboration.controller';
import { CommentsController } from './controllers/comments.controller';

// Services
import { RealTimeEditingService } from './services/real-time-editing.service';
import { PresenceService } from './services/presence.service';
import { OperationalTransformService } from './services/operational-transform.service';
import { ThreadedCommentsService } from './services/threaded-comments.service';
import { MentionsService } from './services/mentions.service';
import { TenantContextService } from '../auth/tenant/tenant-context.service';

// Gateways
import { CollaborationGateway } from './gateways/collaboration.gateway';

// Schemas
import {
  CollaborationSession,
  CollaborationSessionSchema,
  COLLABORATION_SESSION_MODEL,
} from './schemas/collaboration-session.schema';
import {
  DocumentOperation,
  DocumentOperationSchema,
  DOCUMENT_OPERATION_MODEL,
} from './schemas/document-operation.schema';
import {
  CommentThread,
  CommentThreadSchema,
  COMMENT_THREAD_MODEL,
} from './schemas/comment-thread.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      {
        name: COLLABORATION_SESSION_MODEL,
        schema: CollaborationSessionSchema,
      },
      {
        name: DOCUMENT_OPERATION_MODEL,
        schema: DocumentOperationSchema,
      },
      {
        name: COMMENT_THREAD_MODEL,
        schema: CommentThreadSchema,
      },
    ]),
    ConfigModule,
    forwardRef(() => AuthModule),
    forwardRef(() => SubscriptionModule),
    forwardRef(() => DocumentsModule),
  ],
  controllers: [CollaborationController, CommentsController],
  providers: [
    RealTimeEditingService,
    PresenceService,
    OperationalTransformService,
    ThreadedCommentsService,
    MentionsService,
    TenantContextService,
    CollaborationGateway,
  ],
  exports: [
    RealTimeEditingService,
    PresenceService,
    OperationalTransformService,
    ThreadedCommentsService,
    MentionsService,
    CollaborationGateway,
  ],
})
export class CollaborationModule {}

import { Module, forwardRef } from '@nestjs/common';
import { ContextManagerService } from './services/context-manager.service';
import { DocumentsModule } from '../documents/documents.module';
import { DocumentContextModule } from '../document-context/document-context.module';
import { ConfigModule } from '@nestjs/config';
import { ChatModule } from '../chat/chat.module';

@Module({
  imports: [
    ConfigModule,
    forwardRef(() => DocumentsModule),
    forwardRef(() => ChatModule),
    DocumentContextModule,
  ],
  providers: [
    ContextManagerService,
  ],
  exports: [
    ContextManagerService,
  ],
})
export class ContextManagementModule {}

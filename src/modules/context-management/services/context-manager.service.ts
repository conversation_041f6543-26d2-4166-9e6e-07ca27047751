import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentProcessingService } from '../../documents/services/document-processing.service';
import { ContextWindow } from '../../../common/interfaces/context-management.interface';

@Injectable()
export class ContextManagerService {
  private readonly logger = new Logger(ContextManagerService.name);
  private readonly maxTokens: number;

  constructor(
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => DocumentProcessingService))
    private readonly documentProcessingService: DocumentProcessingService,
  ) {
    this.maxTokens = this.configService.get<number>(
      'contextManagement.maxTokens',
      8000,
    );
    this.logger.log(
      `ContextManagerService initialized with max tokens: ${this.maxTokens}`,
    );
  }

  /**
   * Build context window for document and chat history
   */
  async buildContextWindow(
    documentId: string,
    sessionId: string,
    query: string,
  ): Promise<ContextWindow> {
    this.logger.log(
      `Building context window for document ${documentId}, session ${sessionId}`,
    );

    try {
      // Get document content
      const documentContent =
        await this.documentProcessingService.getDocumentContent(documentId);
      if (!documentContent) {
        throw new Error(`Document content not found for ID: ${documentId}`);
      }

      // Calculate relevance scores for different sections
      const scoredSections = this.scoreDocumentSections(documentContent, query);

      // Sort sections by relevance score
      scoredSections.sort((a, b) => b.score - a.score);

      // Build context string from most relevant sections
      let contentString = '';
      let totalTokens = 0;
      const TOKEN_ESTIMATE_MULTIPLIER = 1.5; // Conservative estimate of tokens per character
      const documentExcerpts: string[] = [];

      for (const section of scoredSections) {
        const estimatedTokens =
          section.content.length * TOKEN_ESTIMATE_MULTIPLIER;
        if (totalTokens + estimatedTokens > this.maxTokens) {
          break;
        }

        contentString += section.content + '\n\n';
        documentExcerpts.push(section.content);
        totalTokens += estimatedTokens;
      }

      const tokenUtilization = (totalTokens / this.maxTokens) * 100;
      this.logger.log(
        `Context window built with ${Math.round(
          totalTokens,
        )} tokens (${tokenUtilization.toFixed(2)}% utilization)`,
      );

      // Create and return the context window object
      const contextWindow: ContextWindow = {
        content: contentString.trim(),
        totalTokens: Math.round(totalTokens),
        tokenUtilization,
        relevantSections: scoredSections,
        sections: scoredSections.map(({ content, score }) => ({
          content,
          relevanceScore: score,
        })),
        documentExcerpts,
        documentMetadata: '', // TODO: Add document metadata
        relatedDocumentExcerpts: [], // TODO: Add related document excerpts
        chatHistory: '',
      };

      return contextWindow;
    } catch (error) {
      this.logger.error(`Error building context window: ${error.message}`);
      throw error;
    }
  }

  /**
   * Score document sections based on relevance to query
   */
  private scoreDocumentSections(
    content: string,
    query: string,
  ): Array<{ content: string; score: number }> {
    // Split into sections (paragraphs)
    const sections = content.split('\n\n').filter((section) => section.trim());

    return sections.map((section) => {
      const score = this.calculateRelevanceScore(section, query);
      return { content: section, score };
    });
  }

  /**
   * Calculate relevance score based on keyword matching and section properties
   */
  private calculateRelevanceScore(section: string, query: string): number {
    let score = 0;

    // Convert to lowercase for comparison
    const sectionLower = section.toLowerCase();
    const queryLower = query.toLowerCase();

    // Split query into keywords
    const keywords = queryLower.split(/\s+/).filter((word) => word.length > 2);

    // Score based on keyword presence
    for (const keyword of keywords) {
      if (sectionLower.includes(keyword)) {
        score += 1;
      }
    }

    // Boost score for sections that seem to be headings or important paragraphs
    if (section.match(/^[A-Z\d][\s\S]{0,50}$/m)) {
      score += 0.5; // Boost for short, possibly heading sections
    }

    // Boost for sections with numeric content if query seems to ask about numbers
    if (
      queryLower.match(/\b(amount|rate|number|cost|price|fee|payment)\b/) &&
      section.match(/\d+/)
    ) {
      score += 0.8;
    }

    return score;
  }
}

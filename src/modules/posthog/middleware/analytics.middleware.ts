import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { PostHogService } from '../services/posthog.service';

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email?: string;
    organizationId?: string;
    subscriptionTier?: string;
  };
}

@Injectable()
export class AnalyticsMiddleware implements NestMiddleware {
  private readonly logger = new Logger(AnalyticsMiddleware.name);

  constructor(private readonly postHogService: PostHogService) {}

  use(req: AuthenticatedRequest, res: Response, next: NextFunction) {
    const startTime = Date.now();
    const { method, originalUrl, headers } = req;
    const userId = req.user?.id;

    // Track API request if user is authenticated
    if (userId) {
      this.postHogService.trackEvent(userId, 'api_request', {
        method,
        path: originalUrl,
        user_agent: headers['user-agent'],
        ip: req.ip,
        organization_id: req.user?.organizationId,
        subscription_tier: req.user?.subscriptionTier,
      });
    }

    // Track response when request completes
    res.on('finish', () => {
      const duration = Date.now() - startTime;
      const statusCode = res.statusCode;

      if (userId) {
        this.postHogService.trackEvent(userId, 'api_response', {
          method,
          path: originalUrl,
          status_code: statusCode,
          duration_ms: duration,
          success: statusCode < 400,
          organization_id: req.user?.organizationId,
        });

        // Track errors separately
        if (statusCode >= 400) {
          this.postHogService.trackEvent(userId, 'api_error', {
            method,
            path: originalUrl,
            status_code: statusCode,
            duration_ms: duration,
            organization_id: req.user?.organizationId,
          });
        }
      }
    });

    next();
  }
}
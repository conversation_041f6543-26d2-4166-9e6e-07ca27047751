import { PostHogService } from '../services/posthog.service';

/**
 * Decorator to track method performance and send analytics to PostHog
 */
export function TrackPerformance(eventName?: string) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;
    const className = target.constructor.name;
    const methodName = propertyKey;
    const defaultEventName = `${className.toLowerCase()}_${methodName}_performance`;

    descriptor.value = async function (...args: any[]) {
      const postHogService: PostHogService = this.postHogService || this.posthog;
      const startTime = performance.now();
      let error: Error | null = null;
      let result: any;

      try {
        result = await originalMethod.apply(this, args);
        return result;
      } catch (err) {
        error = err as Error;
        throw err;
      } finally {
        const duration = performance.now() - startTime;
        
        if (postHogService) {
          // Try to get user ID from context
          const userId = this.getUserId?.() || args[0]?.userId || 'anonymous';
          
          postHogService.trackEvent(userId, eventName || defaultEventName, {
            class_name: className,
            method_name: methodName,
            duration_ms: Math.round(duration),
            success: !error,
            error_message: error?.message,
            timestamp: new Date().toISOString(),
          });
        }
      }
    };

    return descriptor;
  };
}

/**
 * Decorator to track method calls and their outcomes
 */
export function TrackMethodCall(eventName?: string) {
  return function (
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;
    const className = target.constructor.name;
    const methodName = propertyKey;
    const defaultEventName = `${className.toLowerCase()}_${methodName}_called`;

    descriptor.value = async function (...args: any[]) {
      const postHogService: PostHogService = this.postHogService || this.posthog;
      let error: Error | null = null;
      let result: any;

      try {
        result = await originalMethod.apply(this, args);
        return result;
      } catch (err) {
        error = err as Error;
        throw err;
      } finally {
        if (postHogService) {
          // Try to get user ID from context
          const userId = this.getUserId?.() || args[0]?.userId || 'anonymous';
          
          postHogService.trackEvent(userId, eventName || defaultEventName, {
            class_name: className,
            method_name: methodName,
            success: !error,
            error_message: error?.message,
            timestamp: new Date().toISOString(),
          });
        }
      }
    };

    return descriptor;
  };
}
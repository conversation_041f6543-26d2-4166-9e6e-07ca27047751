import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PostHog } from 'posthog-node';

@Injectable()
export class PostHogService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(PostHogService.name);
  private client: PostHog;
  private isEnabled: boolean;

  constructor(private readonly configService: ConfigService) {}

  async onModuleInit() {
    const apiKey = this.configService.get<string>('posthog.apiKey');
    const host = this.configService.get<string>('posthog.host');
    this.isEnabled = this.configService.get<boolean>('posthog.enabled');

    if (!this.isEnabled) {
      this.logger.log('PostHog is disabled');
      return;
    }

    if (!apiKey) {
      this.logger.warn('PostHog API key is not configured');
      this.isEnabled = false;
      return;
    }

    this.client = new PostHog(api<PERSON><PERSON>, {
      host,
      flushAt: this.configService.get<number>('posthog.flushAt'),
      flushInterval: this.configService.get<number>('posthog.flushInterval'),
    });

    this.logger.log('PostHog service initialized');
  }

  async onModuleDestroy() {
    if (this.client) {
      await this.client.shutdown();
      this.logger.log('PostHog client shutdown complete');
    }
  }

  /**
   * Track an event for a user
   */
  trackEvent(
    userId: string,
    event: string,
    properties: Record<string, any> = {},
    timestamp?: Date
  ): void {
    if (!this.isEnabled || !this.client) {
      return;
    }

    try {
      this.client.capture({
        distinctId: userId,
        event,
        properties: {
          ...properties,
          timestamp: timestamp || new Date(),
        },
      });

      this.logger.debug(`Tracked event: ${event} for user: ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to track event ${event}: ${error.message}`);
    }
  }

  /**
   * Identify a user with their properties
   */
  identifyUser(userId: string, properties: Record<string, any> = {}): void {
    if (!this.isEnabled || !this.client) {
      return;
    }

    try {
      this.client.identify({
        distinctId: userId,
        properties,
      });

      this.logger.debug(`Identified user: ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to identify user ${userId}: ${error.message}`);
    }
  }

  /**
   * Set properties for a user
   */
  setUserProperties(userId: string, properties: Record<string, any>): void {
    if (!this.isEnabled || !this.client) {
      return;
    }

    try {
      this.client.identify({
        distinctId: userId,
        properties,
      });

      this.logger.debug(`Set properties for user: ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to set properties for user ${userId}: ${error.message}`);
    }
  }

  /**
   * Create an alias for a user
   */
  alias(distinctId: string, alias: string): void {
    if (!this.isEnabled || !this.client) {
      return;
    }

    try {
      this.client.alias({
        distinctId,
        alias,
      });

      this.logger.debug(`Created alias ${alias} for user: ${distinctId}`);
    } catch (error) {
      this.logger.error(`Failed to create alias: ${error.message}`);
    }
  }

  /**
   * Check if a feature flag is enabled for a user
   */
  async isFeatureEnabled(
    featureKey: string,
    userId: string,
    defaultValue: boolean = false
  ): Promise<boolean> {
    if (!this.isEnabled || !this.client) {
      return defaultValue;
    }

    try {
      const result = await this.client.isFeatureEnabled(featureKey, userId);
      this.logger.debug(`Feature flag ${featureKey} for user ${userId}: ${result}`);
      return result ?? defaultValue;
    } catch (error) {
      this.logger.error(`Failed to check feature flag ${featureKey}: ${error.message}`);
      return defaultValue;
    }
  }

  /**
   * Get feature flag value with payload
   */
  async getFeatureFlag(
    featureKey: string,
    userId: string,
    defaultValue?: any
  ): Promise<any> {
    if (!this.isEnabled || !this.client) {
      return defaultValue;
    }

    try {
      const result = await this.client.getFeatureFlag(featureKey, userId);
      this.logger.debug(`Feature flag ${featureKey} value for user ${userId}: ${result}`);
      return result ?? defaultValue;
    } catch (error) {
      this.logger.error(`Failed to get feature flag ${featureKey}: ${error.message}`);
      return defaultValue;
    }
  }

  /**
   * Track a page view
   */
  trackPageView(
    userId: string,
    path: string,
    properties: Record<string, any> = {}
  ): void {
    this.trackEvent(userId, '$pageview', {
      $current_url: path,
      ...properties,
    });
  }

  /**
   * Track user signup
   */
  trackUserSignup(
    userId: string,
    properties: Record<string, any> = {}
  ): void {
    this.trackEvent(userId, 'user_signed_up', properties);
  }

  /**
   * Track user login
   */
  trackUserLogin(
    userId: string,
    properties: Record<string, any> = {}
  ): void {
    this.trackEvent(userId, 'user_logged_in', properties);
  }

  /**
   * Track document upload
   */
  trackDocumentUpload(
    userId: string,
    documentId: string,
    documentType: string,
    properties: Record<string, any> = {}
  ): void {
    this.trackEvent(userId, 'document_uploaded', {
      document_id: documentId,
      document_type: documentType,
      ...properties,
    });
  }

  /**
   * Track document analysis
   */
  trackDocumentAnalysis(
    userId: string,
    documentId: string,
    analysisType: string,
    duration: number,
    properties: Record<string, any> = {}
  ): void {
    this.trackEvent(userId, 'document_analyzed', {
      document_id: documentId,
      analysis_type: analysisType,
      duration_ms: duration,
      ...properties,
    });
  }

  /**
   * Track document comparison
   */
  trackDocumentComparison(
    userId: string,
    documentIds: string[],
    comparisonType: string,
    properties: Record<string, any> = {}
  ): void {
    this.trackEvent(userId, 'documents_compared', {
      document_ids: documentIds,
      comparison_type: comparisonType,
      ...properties,
    });
  }

  /**
   * Track feature usage
   */
  trackFeatureUsage(
    userId: string,
    featureId: string,
    success: boolean,
    duration?: number,
    properties: Record<string, any> = {}
  ): void {
    this.trackEvent(userId, 'feature_used', {
      feature_id: featureId,
      success,
      duration_ms: duration,
      ...properties,
    });
  }

  /**
   * Track error occurrence
   */
  trackError(
    userId: string,
    error: Error,
    context: string,
    properties: Record<string, any> = {}
  ): void {
    this.trackEvent(userId, 'error_occurred', {
      error_message: error.message,
      error_stack: error.stack,
      context,
      ...properties,
    });
  }

  /**
   * Flush pending events
   */
  async flush(): Promise<void> {
    if (!this.isEnabled || !this.client) {
      return;
    }

    try {
      await this.client.flush();
      this.logger.debug('PostHog events flushed');
    } catch (error) {
      this.logger.error(`Failed to flush PostHog events: ${error.message}`);
    }
  }
}
import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { PostHogService } from '../services/posthog.service';

describe('PostHogService', () => {
  let service: PostHogService;
  let configService: ConfigService;

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PostHogService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<PostHogService>(PostHogService);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('onModuleInit', () => {
    it('should initialize PostHog client when enabled with API key', async () => {
      mockConfigService.get.mockImplementation((key: string) => {
        switch (key) {
          case 'posthog.enabled':
            return true;
          case 'posthog.apiKey':
            return 'test-api-key';
          case 'posthog.host':
            return 'https://app.posthog.com';
          case 'posthog.flushAt':
            return 20;
          case 'posthog.flushInterval':
            return 10000;
          default:
            return undefined;
        }
      });

      await service.onModuleInit();
      
      // Service should initialize without throwing
      expect(service).toBeDefined();
    });

    it('should not initialize PostHog client when disabled', async () => {
      mockConfigService.get.mockImplementation((key: string) => {
        switch (key) {
          case 'posthog.enabled':
            return false;
          default:
            return undefined;
        }
      });

      await service.onModuleInit();
      
      // Service should initialize without throwing
      expect(service).toBeDefined();
    });

    it('should not initialize PostHog client when API key is missing', async () => {
      mockConfigService.get.mockImplementation((key: string) => {
        switch (key) {
          case 'posthog.enabled':
            return true;
          case 'posthog.apiKey':
            return undefined;
          default:
            return undefined;
        }
      });

      await service.onModuleInit();
      
      // Service should initialize without throwing
      expect(service).toBeDefined();
    });
  });

  describe('trackEvent', () => {
    beforeEach(async () => {
      mockConfigService.get.mockImplementation((key: string) => {
        switch (key) {
          case 'posthog.enabled':
            return false; // Disabled for testing to avoid actual API calls
          default:
            return undefined;
        }
      });
      await service.onModuleInit();
    });

    it('should handle tracking when PostHog is disabled', () => {
      expect(() => {
        service.trackEvent('test-user', 'test-event', { test: 'data' });
      }).not.toThrow();
    });

    it('should handle user identification when PostHog is disabled', () => {
      expect(() => {
        service.identifyUser('test-user', { email: '<EMAIL>' });
      }).not.toThrow();
    });
  });

  describe('feature flags', () => {
    beforeEach(async () => {
      mockConfigService.get.mockImplementation((key: string) => {
        switch (key) {
          case 'posthog.enabled':
            return false; // Disabled for testing
          default:
            return undefined;
        }
      });
      await service.onModuleInit();
    });

    it('should return default value when PostHog is disabled', async () => {
      const result = await service.isFeatureEnabled('test-flag', 'test-user', true);
      expect(result).toBe(true);
    });

    it('should return default value when PostHog is disabled for getFeatureFlag', async () => {
      const result = await service.getFeatureFlag('test-flag', 'test-user', 'default');
      expect(result).toBe('default');
    });
  });

  describe('document tracking methods', () => {
    beforeEach(async () => {
      mockConfigService.get.mockImplementation((key: string) => {
        switch (key) {
          case 'posthog.enabled':
            return false; // Disabled for testing
          default:
            return undefined;
        }
      });
      await service.onModuleInit();
    });

    it('should handle document upload tracking', () => {
      expect(() => {
        service.trackDocumentUpload('user-1', 'doc-1', 'contract', {
          organization_id: 'org-1',
          file_size: 1024,
        });
      }).not.toThrow();
    });

    it('should handle document analysis tracking', () => {
      expect(() => {
        service.trackDocumentAnalysis('user-1', 'doc-1', 'standard', 5000, {
          organization_id: 'org-1',
        });
      }).not.toThrow();
    });

    it('should handle document comparison tracking', () => {
      expect(() => {
        service.trackDocumentComparison('user-1', ['doc-1', 'doc-2'], 'detailed', {
          organization_id: 'org-1',
        });
      }).not.toThrow();
    });
  });
});
import { Modu<PERSON>, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AuditLogService } from './services/audit-log.service';
import { AuditLogController } from './controllers/audit-log.controller';
import { AuditLogSchema, AUDIT_LOG_MODEL } from './schemas/audit-log.schema';
import { ConfigModule } from '@nestjs/config';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: AUDIT_LOG_MODEL, schema: AuditLogSchema }
    ]),
    ConfigModule,
    forwardRef(() => AuthModule) 
  ],
  controllers: [AuditLogController],
  providers: [AuditLogService],
  exports: [AuditLogService]
})
export class AuditModule {}

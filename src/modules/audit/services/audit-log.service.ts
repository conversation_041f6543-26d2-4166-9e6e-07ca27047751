import { Injectable, Logger, Inject, forwardRef } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import { AuditLog, AUDIT_LOG_MODEL } from '../schemas/audit-log.schema';
import { TenantContextService } from '../../auth/services/tenant-context.service';

/**
 * Service for handling audit logging across the application
 * Provides centralized logging for security events and compliance requirements
 */
@Injectable()
export class AuditLogService {
  private readonly logger = new Logger(AuditLogService.name);
  private readonly isProduction: boolean;
  private readonly retentionPeriodDays: number;

  constructor(
    @InjectModel(AUDIT_LOG_MODEL) private auditLogModel: Model<AuditLog>,
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => TenantContextService))
    private readonly tenantContextService?: TenantContextService,
  ) {
    this.isProduction = this.configService.get<string>('NODE_ENV') === 'production';
    this.retentionPeriodDays = this.configService.get<number>('audit.retentionDays') || 90;
  }

  /**
   * Log a document access event
   * @param data Document access data
   */
  async logDocumentAccess(data: {
    userId: string;
    organizationId: string;
    documentId: string;
    timestamp: Date;
    ipAddress?: string;
    userAgent?: string;
    accessType?: 'view' | 'download' | 'edit' | 'share';
    success: boolean;
    failureReason?: string;
  }): Promise<void> {
    try {
      const auditLog = new this.auditLogModel({
        eventType: 'document_access',
        userId: data.userId,
        organizationId: data.organizationId,
        resourceId: data.documentId,
        resourceType: 'document',
        timestamp: data.timestamp,
        metadata: {
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          accessType: data.accessType || 'view',
          success: data.success,
          failureReason: data.failureReason,
        },
      });

      await auditLog.save();
      
      if (!this.isProduction) {
        this.logger.debug(
          `Audit log created: document_access, user=${data.userId}, doc=${data.documentId}, success=${data.success}`
        );
      }
    } catch (error) {
      // Log the error but don't throw - audit logging should not disrupt normal operations
      this.logger.error(`Failed to create audit log: ${error.message}`, error.stack);
    }
  }

  /**
   * Log an authentication event (login, logout, failed login)
   * @param data Authentication event data
   */
  async logAuthEvent(data: {
    eventType: 'login' | 'logout' | 'login_failed' | 'password_reset' | 'mfa_challenge';
    userId?: string;
    email?: string;
    organizationId?: string;
    timestamp: Date;
    ipAddress?: string;
    userAgent?: string;
    success: boolean;
    failureReason?: string;
  }): Promise<void> {
    try {
      const auditLog = new this.auditLogModel({
        eventType: `auth_${data.eventType}`,
        userId: data.userId,
        organizationId: data.organizationId,
        timestamp: data.timestamp,
        metadata: {
          email: data.email,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          success: data.success,
          failureReason: data.failureReason,
        },
      });

      await auditLog.save();
      
      if (!this.isProduction) {
        this.logger.debug(
          `Audit log created: auth_${data.eventType}, user=${data.userId || data.email}, success=${data.success}`
        );
      }
    } catch (error) {
      this.logger.error(`Failed to create auth audit log: ${error.message}`, error.stack);
    }
  }

  /**
   * Log an administrative action
   * @param data Admin action data
   */
  async logAdminAction(data: {
    actionType: 'user_create' | 'user_update' | 'user_delete' | 'role_change' | 'settings_change' | 'organization_update';
    performedBy: string;
    targetUserId?: string;
    organizationId: string;
    timestamp: Date;
    ipAddress?: string;
    details: Record<string, any>;
    success: boolean;
    failureReason?: string;
  }): Promise<void> {
    try {
      const auditLog = new this.auditLogModel({
        eventType: `admin_${data.actionType}`,
        userId: data.performedBy,
        targetUserId: data.targetUserId,
        organizationId: data.organizationId,
        timestamp: data.timestamp,
        metadata: {
          ipAddress: data.ipAddress,
          details: data.details,
          success: data.success,
          failureReason: data.failureReason,
        },
      });

      await auditLog.save();
      
      if (!this.isProduction) {
        this.logger.debug(
          `Audit log created: admin_${data.actionType}, admin=${data.performedBy}, target=${data.targetUserId || 'system'}`
        );
      }
    } catch (error) {
      this.logger.error(`Failed to create admin audit log: ${error.message}`, error.stack);
    }
  }

  /**
   * Log a data export event for compliance tracking
   * @param data Export data
   */
  async logDataExport(data: {
    userId: string;
    organizationId: string;
    timestamp: Date;
    ipAddress?: string;
    exportType: 'document' | 'analysis' | 'user_data' | 'organization_data';
    resourceIds: string[];
    format: string;
    reason?: string;
  }): Promise<void> {
    try {
      const auditLog = new this.auditLogModel({
        eventType: 'data_export',
        userId: data.userId,
        organizationId: data.organizationId,
        timestamp: data.timestamp,
        metadata: {
          ipAddress: data.ipAddress,
          exportType: data.exportType,
          resourceIds: data.resourceIds,
          format: data.format,
          reason: data.reason,
        },
      });

      await auditLog.save();
      
      if (!this.isProduction) {
        this.logger.debug(
          `Audit log created: data_export, user=${data.userId}, type=${data.exportType}, resources=${data.resourceIds.length}`
        );
      }
    } catch (error) {
      this.logger.error(`Failed to create export audit log: ${error.message}`, error.stack);
    }
  }

  /**
   * Log a security-related event
   * @param data Security event data
   */
  async logSecurityEvent(data: {
    eventType: 'rate_limit_exceeded' | 'suspicious_activity' | 'permission_denied' | 'token_revoked';
    userId?: string;
    organizationId?: string;
    ipAddress?: string;
    userAgent?: string;
    resourceType?: string;
    resourceId?: string;
    timestamp: Date;
    details: Record<string, any>;
    severity: 'low' | 'medium' | 'high' | 'critical';
  }): Promise<void> {
    try {
      const auditLog = new this.auditLogModel({
        eventType: `security_${data.eventType}`,
        userId: data.userId,
        organizationId: data.organizationId,
        resourceType: data.resourceType,
        resourceId: data.resourceId,
        timestamp: data.timestamp,
        metadata: {
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,
          details: data.details,
          severity: data.severity,
        },
      });

      await auditLog.save();
      
      // Always log security events, even in non-production
      this.logger.warn(
        `Security event: ${data.eventType}, severity=${data.severity}, user=${data.userId || 'anonymous'}, ` +
        `ip=${data.ipAddress || 'unknown'}, details=${JSON.stringify(data.details)}`
      );
    } catch (error) {
      this.logger.error(`Failed to create security audit log: ${error.message}`, error.stack);
    }
  }

  /**
   * Get audit logs for a specific organization with filtering options
   * @param organizationId Organization ID
   * @param options Filter options
   * @returns Filtered audit logs
   */
  async getAuditLogs(
    organizationId: string,
    options: {
      userId?: string;
      eventTypes?: string[];
      startDate?: Date;
      endDate?: Date;
      resourceId?: string;
      resourceType?: string;
      page?: number;
      limit?: number;
    }
  ): Promise<{ logs: AuditLog[]; total: number }> {
    try {
      const query: any = { organizationId };
      
      if (options.userId) {
        query.userId = options.userId;
      }
      
      if (options.eventTypes && options.eventTypes.length > 0) {
        query.eventType = { $in: options.eventTypes };
      }
      
      if (options.resourceId) {
        query.resourceId = options.resourceId;
      }
      
      if (options.resourceType) {
        query.resourceType = options.resourceType;
      }
      
      if (options.startDate || options.endDate) {
        query.timestamp = {};
        if (options.startDate) {
          query.timestamp.$gte = options.startDate;
        }
        if (options.endDate) {
          query.timestamp.$lte = options.endDate;
        }
      }
      
      const page = options.page || 1;
      const limit = options.limit || 50;
      const skip = (page - 1) * limit;
      
      const [logs, total] = await Promise.all([
        this.auditLogModel
          .find(query)
          .sort({ timestamp: -1 })
          .skip(skip)
          .limit(limit)
          .exec(),
        this.auditLogModel.countDocuments(query).exec(),
      ]);
      
      return { logs, total };
    } catch (error) {
      this.logger.error(`Failed to retrieve audit logs: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Clean up old audit logs based on retention policy
   * Should be run as a scheduled task
   */
  async cleanupOldLogs(): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - this.retentionPeriodDays);
      
      const result = await this.auditLogModel.deleteMany({
        timestamp: { $lt: cutoffDate },
      });
      
      this.logger.log(`Cleaned up ${result.deletedCount} audit logs older than ${this.retentionPeriodDays} days`);
      return result.deletedCount;
    } catch (error) {
      this.logger.error(`Failed to clean up old audit logs: ${error.message}`, error.stack);
      throw error;
    }
  }
}

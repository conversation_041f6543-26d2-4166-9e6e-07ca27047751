import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export const AUDIT_LOG_MODEL = 'AuditLog';

@Schema({
  collection: 'audit_logs',
  timestamps: true,
  toJSON: {
    virtuals: true,
    transform: (doc, ret) => {
      delete ret._id;
      delete ret.__v;
      return ret;
    },
  },
})
export class AuditLog extends Document {
  @Prop({ required: true })
  eventType: string;

  @Prop({ required: true })
  timestamp: Date;

  @Prop()
  userId?: string;

  @Prop()
  targetUserId?: string;

  @Prop({ index: true })
  organizationId?: string;

  @Prop()
  resourceType?: string;

  @Prop()
  resourceId?: string;

  @Prop({ type: MongooseSchema.Types.Mixed })
  metadata?: Record<string, any>;

  // Virtual field for ID
  id: string;
}

export const AuditLogSchema = SchemaFactory.createForClass(AuditLog);

// Create indexes for common query patterns
AuditLogSchema.index({ eventType: 1, timestamp: -1 });
AuditLogSchema.index({ userId: 1, timestamp: -1 });
AuditLogSchema.index({ organizationId: 1, eventType: 1, timestamp: -1 });
AuditLogSchema.index({ resourceId: 1, eventType: 1 });
AuditLogSchema.index({ timestamp: 1 }, { expireAfterSeconds: 31536000 }); // Optional TTL index (1 year)

// Virtual for ID
AuditLogSchema.virtual('id').get(function () {
  return this._id ? (this._id as any).toHexString() : null;
});

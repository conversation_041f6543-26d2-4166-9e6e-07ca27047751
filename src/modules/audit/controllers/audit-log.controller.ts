import {
  Controller,
  Get,
  Query,
  UseGuards,
  ForbiddenException,
  Logger,
  Param,
  ParseUUIDPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiParam } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { AuditLogService } from '../services/audit-log.service';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { Roles } from '../../auth/decorators/roles.decorator';

@ApiTags('Audit')
@Controller('audit-logs')
@UseGuards(JwtAuthGuard)
export class AuditLogController {
  private readonly logger = new Logger(AuditLogController.name);

  constructor(
    private readonly auditLogService: AuditLogService,
    private readonly tenantContext: TenantContextService,
  ) {}

  @Get()
  @Roles('law_firm')
  @ApiOperation({ summary: 'Get audit logs for the current organization' })
  @ApiResponse({
    status: 200,
    description: 'List of audit logs retrieved successfully',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - User does not have admin role',
  })
  @ApiQuery({ name: 'userId', required: false, description: 'Filter by user ID' })
  @ApiQuery({ name: 'eventTypes', required: false, description: 'Filter by event types (comma-separated)' })
  @ApiQuery({ name: 'startDate', required: false, description: 'Filter by start date (ISO format)' })
  @ApiQuery({ name: 'endDate', required: false, description: 'Filter by end date (ISO format)' })
  @ApiQuery({ name: 'resourceId', required: false, description: 'Filter by resource ID' })
  @ApiQuery({ name: 'resourceType', required: false, description: 'Filter by resource type' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number', type: Number })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page', type: Number })
  async getAuditLogs(
    @Query('userId') userId?: string,
    @Query('eventTypes') eventTypes?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('resourceId') resourceId?: string,
    @Query('resourceType') resourceType?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    const organizationId = this.tenantContext.getCurrentOrganization();
    const userRole = this.tenantContext.getCurrentUserRole();
    const currentUserId = this.tenantContext.getCurrentUserId();

    // Only admin users can access audit logs
    if (userRole !== 'law_firm') {
      this.logger.warn(`Non-admin user ${currentUserId} attempted to access audit logs`);
      throw new ForbiddenException('Only administrators can access audit logs');
    }

    // Parse query parameters
    const parsedEventTypes = eventTypes ? eventTypes.split(',') : undefined;
    const parsedStartDate = startDate ? new Date(startDate) : undefined;
    const parsedEndDate = endDate ? new Date(endDate) : undefined;
    const parsedPage = page ? parseInt(page.toString(), 10) : 1;
    const parsedLimit = limit ? parseInt(limit.toString(), 10) : 50;

    // Get audit logs with filters
    const result = await this.auditLogService.getAuditLogs(organizationId, {
      userId,
      eventTypes: parsedEventTypes,
      startDate: parsedStartDate,
      endDate: parsedEndDate,
      resourceId,
      resourceType,
      page: parsedPage,
      limit: parsedLimit,
    });

    return {
      logs: result.logs,
      pagination: {
        total: result.total,
        page: parsedPage,
        limit: parsedLimit,
        pages: Math.ceil(result.total / parsedLimit),
      },
    };
  }

  @Get('document/:id')
  @Roles('law_firm')
  @ApiOperation({ summary: 'Get audit logs for a specific document' })
  @ApiResponse({
    status: 200,
    description: 'Document audit logs retrieved successfully',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - User does not have admin role',
  })
  @ApiParam({ name: 'id', description: 'Document ID' })
  async getDocumentAuditLogs(
    @Param('id') documentId: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    const organizationId = this.tenantContext.getCurrentOrganization();
    const userRole = this.tenantContext.getCurrentUserRole();
    const currentUserId = this.tenantContext.getCurrentUserId();

    // Only admin users can access document audit logs
    if (userRole !== 'law_firm') {
      this.logger.warn(`Non-admin user ${currentUserId} attempted to access document audit logs`);
      throw new ForbiddenException('Only administrators can access document audit logs');
    }

    const parsedPage = page ? parseInt(page.toString(), 10) : 1;
    const parsedLimit = limit ? parseInt(limit.toString(), 10) : 50;

    // Get document-specific audit logs
    const result = await this.auditLogService.getAuditLogs(organizationId, {
      resourceId: documentId,
      resourceType: 'document',
      page: parsedPage,
      limit: parsedLimit,
    });

    return {
      logs: result.logs,
      pagination: {
        total: result.total,
        page: parsedPage,
        limit: parsedLimit,
        pages: Math.ceil(result.total / parsedLimit),
      },
    };
  }

  @Get('user/:id')
  @Roles('law_firm')
  @ApiOperation({ summary: 'Get audit logs for a specific user' })
  @ApiResponse({
    status: 200,
    description: 'User audit logs retrieved successfully',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - User does not have admin role',
  })
  @ApiParam({ name: 'id', description: 'User ID' })
  async getUserAuditLogs(
    @Param('id', ParseUUIDPipe) userId: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    const organizationId = this.tenantContext.getCurrentOrganization();
    const userRole = this.tenantContext.getCurrentUserRole();
    const currentUserId = this.tenantContext.getCurrentUserId();

    // Only admin users can access user audit logs
    if (userRole !== 'law_firm') {
      this.logger.warn(`Non-admin user ${currentUserId} attempted to access user audit logs`);
      throw new ForbiddenException('Only administrators can access user audit logs');
    }

    const parsedPage = page ? parseInt(page.toString(), 10) : 1;
    const parsedLimit = limit ? parseInt(limit.toString(), 10) : 50;

    // Get user-specific audit logs
    const result = await this.auditLogService.getAuditLogs(organizationId, {
      userId,
      page: parsedPage,
      limit: parsedLimit,
    });

    return {
      logs: result.logs,
      pagination: {
        total: result.total,
        page: parsedPage,
        limit: parsedLimit,
        pages: Math.ceil(result.total / parsedLimit),
      },
    };
  }

  @Get('security')
  @Roles('law_firm')
  @ApiOperation({ summary: 'Get security-related audit logs' })
  @ApiResponse({
    status: 200,
    description: 'Security audit logs retrieved successfully',
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - User does not have admin role',
  })
  async getSecurityAuditLogs(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    const organizationId = this.tenantContext.getCurrentOrganization();
    const userRole = this.tenantContext.getCurrentUserRole();
    const currentUserId = this.tenantContext.getCurrentUserId();

    // Only admin users can access security audit logs
    if (userRole !== 'law_firm') {
      this.logger.warn(`Non-admin user ${currentUserId} attempted to access security audit logs`);
      throw new ForbiddenException('Only administrators can access security audit logs');
    }

    const parsedPage = page ? parseInt(page.toString(), 10) : 1;
    const parsedLimit = limit ? parseInt(limit.toString(), 10) : 50;

    // Get security-specific audit logs
    const result = await this.auditLogService.getAuditLogs(organizationId, {
      eventTypes: [
        'rate_limit_exceeded',
        'suspicious_activity',
        'permission_denied',
        'token_revoked',
      ],
      page: parsedPage,
      limit: parsedLimit,
    });

    return {
      logs: result.logs,
      pagination: {
        total: result.total,
        page: parsedPage,
        limit: parsedLimit,
        pages: Math.ceil(result.total / parsedLimit),
      },
    };
  }
}

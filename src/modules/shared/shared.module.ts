import { Modu<PERSON> } from '@nestjs/common';
import { CacheModule } from '@nestjs/cache-manager'; // Import CacheModule
import { HttpModule } from '@nestjs/axios';
import { CourtListenerService } from './court-listener/court-listener.service';
import { CourtListenerController } from './court-listener/court-listener.controller';

@Module({
  imports: [
    CacheModule.register(), // Register CacheModule (can add configuration later)
    HttpModule.registerAsync({
      useFactory: () => ({
        timeout: 10000, // Default timeout: 10 seconds
        maxRedirects: 5,
      }),
    }),
  ],
  controllers: [CourtListenerController],
  providers: [CourtListenerService],
  exports: [CourtListenerService], // Export the service so other modules can use it
})
export class SharedModule {}

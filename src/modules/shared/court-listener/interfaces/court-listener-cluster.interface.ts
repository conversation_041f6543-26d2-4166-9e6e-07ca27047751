// Based on example structure from https://www.courtlistener.com/help/api/rest/citation-lookup/

interface ClusterCitation {
  volume: number;
  reporter: string;
  page: string;
  type: number; // Example: 2
}

export interface CourtListenerCluster {
  resource_uri?: string;
  id?: number;
  absolute_url?: string;
  date_created?: string;
  date_modified?: string;
  date_filed?: string;
  date_filed_is_approximate?: boolean;
  slug?: string;
  case_name_short?: string;
  case_name?: string;
  case_name_full?: string;
  scdb_id?: string;
  scdb_decision_direction?: number;
  scdb_votes_majority?: number;
  scdb_votes_minority?: number;
  source?: string;
  procedural_history?: string;
  attorneys?: string;
  nature_of_suit?: string;
  posture?: string;
  syllabus?: string;
  headnotes?: string;
  summary?: string;
  disposition?: string;
  history?: string;
  other_dates?: string;
  cross_reference?: string;
  correction?: string;
  citation_count?: number;
  precedential_status?: string;
  date_blocked?: string;
  blocked?: boolean;
  judges?: string;
  opinions_cited?: string[]; // URLs
  citations?: ClusterCitation[];
  docket?: string; // URL to the docket resource
  // ... other potential fields
}

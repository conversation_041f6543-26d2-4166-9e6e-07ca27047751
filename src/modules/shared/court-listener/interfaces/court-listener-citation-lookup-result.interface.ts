import { CourtListenerCluster } from './court-listener-cluster.interface';

// Based on response structure from https://www.courtlistener.com/help/api/rest/citation-lookup/
export interface CourtListenerCitationLookupResult {
  citation: string; // e.g., "576 U.S. 644"
  normalized_citations: string[]; // e.g., ["576 U.S. 644"]
  start_index?: number; // Only present if 'text' parameter was used
  end_index?: number; // Only present if 'text' parameter was used
  status: number; // 200, 404, 400, 300, 429
  error_message?: string;
  clusters: CourtListenerCluster[];
  
  // Properties for enriched data
  cluster?: string; // URL to the cluster resource
  opinion?: string; // URL to the opinion resource
  case_name?: string;
  case_name_short?: string;
  case_name_full?: string;
  date_filed?: string;
  judges?: string;
  precedential_status?: string;
  citations?: any[]; // Citations from the cluster
  court_id?: string; // Court identifier (e.g., 'scotus')
}

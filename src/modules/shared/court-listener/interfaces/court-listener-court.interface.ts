// TODO: Define interface based on CourtListener /courts/ response
export interface CourtListenerCourt {
  // Example fields (adjust based on actual API response)
  id?: string; // e.g., 'ca9'
  resource_uri?: string;
  absolute_url?: string;
  full_name?: string;
  short_name?: string;
  jurisdiction?: string; // e.g., 'F' (Federal) or 'S' (State)
  url?: string;
  start_date?: string;
  end_date?: string | null;
  position?: number;
  // ... other relevant fields
}

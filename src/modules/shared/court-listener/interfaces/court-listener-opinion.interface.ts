import { CourtListenerCitationResult } from "./court-listener-citation-result.interface";

// TODO: Define interface based on CourtListener /opinions/ response
export interface CourtListenerOpinion {
  // Example fields (adjust based on actual API response)
  id?: number;
  resource_uri?: string;
  absolute_url?: string;
  cluster?: string; // URL to the cluster
  author?: string; // URL to the author (judge)
  joined_by?: string[]; // URLs
  date_created?: string;
  date_modified?: string;
  date_filed?: string;
  date_filed_is_approximate?: boolean;
  slug?: string;
  case_name_short?: string;
  case_name?: string;
  case_name_full?: string;
  html_lawbox?: string; // Opinion text HTML
  html_columbia?: string;
  html_with_citations?: string;
  plain_text?: string;
  extracted_by_ocr?: boolean;
  sha1?: string;
  local_path?: string;
  download_url?: string;
  assignee?: string; // URL
  judge?: string; // URL
  per_curiam?: boolean;
  type?: string; // e.g., '010combined'
  precedential_status?: string; // e.g., '010precedential'
  date_blocked?: string;
  blocked?: boolean;
  citations?: CourtListenerCitationResult[]; // Embedded citations
  // ... other relevant fields
}

// TODO: Define interface based on CourtListener /dockets/ response
export interface CourtListenerDocket {
  // Example fields (adjust based on actual API response)
  id?: number;
  resource_uri?: string;
  absolute_url?: string;
  clusters?: string[]; // URLs to related opinion clusters
  source?: string; // e.g., 'RECAP'
  appeal_from?: string | null; // URL to court appealed from
  assigned_to?: string | null; // URL to judge
  referred_to?: string | null; // URL to judge
  court?: string; // URL to court
  court_id?: string; // Court identifier (e.g., 'scotus')
  idb_data?: string | null; // URL to integrated database data
  appeal_from_str?: string;
  assigned_to_str?: string;
  referred_to_str?: string;
  panel?: string[]; // URLs to judges on panel
  date_created?: string;
  date_modified?: string;
  date_argued?: string | null;
  date_reargued?: string | null;
  date_reargument_denied?: string | null;
  date_filed?: string | null;
  date_terminated?: string | null;
  date_last_filing?: string | null;
  // Supreme Court specific dates
  date_cert_granted?: string | null;
  date_cert_denied?: string | null;
  case_name_short?: string;
  case_name?: string;
  case_name_full?: string;
  slug?: string;
  cause?: string | null;
  nature_of_suit?: string | null;
  jury_demand?: string | null;
  jurisdiction_type?: string | null;
  docket_number?: string;
  pacer_case_id?: string | null;
  filepath_json?: string;
  filepath_xml?: string;
  filepath_zip?: string;
  filepath_pdf?: string;
  date_blocked?: string | null;
  blocked?: boolean;
  assigned_to_id_label?: string | null;
  // ... other relevant fields
}

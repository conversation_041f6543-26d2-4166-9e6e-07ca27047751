// TODO: Define interface based on CourtListener /docket-entries/ response
export interface CourtListenerDocketEntry {
  // Example fields (adjust based on actual API response)
  id?: number;
  resource_uri?: string;
  absolute_url?: string;
  docket?: string; // URL to the docket
  date_filed?: string | null;
  date_modified?: string;
  date_created?: string;
  entry_number?: number | null;
  pacer_sequence_number?: number | null;
  description?: string;
  short_description?: string;
  type?: string; // e.g., 'notice'
  document_number?: string | null;
  pacer_document_number?: string | null;
  pacer_seq_number?: string | null;
  pacer_doc_id?: string | null; // PACER document ID
  filed_by?: string[];
  date_entered?: string | null;
  recap_documents?: string[]; // URLs to RECAP documents
  idb_data?: string | null; // URL to integrated database data
  date_blocked?: string | null;
  blocked?: boolean;
  // ... other relevant fields
}

// TODO: Define interface based on CourtListener /citation-lookup/ response
// Extended to match actual CourtListener /citation-lookup/ response
export interface CourtListenerCitationResult {
  id?: number;
  resource_uri?: string;
  absolute_url?: string;
  cluster?: string; // URL to the cluster
  opinion?: string; // URL to the specific opinion
  // --- Added fields for enrichment ---
  case_name?: string;
  caseName?: string;
  citation?: string;
  court?: string;
  court_id?: string;
  date_filed?: string;
  dateFiled?: string;
  docket_number?: string;
  cluster_id?: number;
  judges?: string;
  jurisdiction?: string;
  status?: string;
  precedential_status?: string;
  pdf_url?: string;
  // ... other relevant fields
}

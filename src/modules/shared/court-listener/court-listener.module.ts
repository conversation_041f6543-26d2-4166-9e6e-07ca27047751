import { Module, Global } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { CacheModule } from '@nestjs/cache-manager';
import { CourtListenerService } from './court-listener.service';
import { CourtListenerController } from './court-listener.controller';

@Global()
@Module({
  imports: [
    HttpModule,
    ConfigModule,
    CacheModule.register({
      isGlobal: true,
      ttl: 3600000, // Cache for 1 hour (in ms)
    }),
  ],
  controllers: [CourtListenerController],
  providers: [CourtListenerService],
  exports: [CourtListenerService],
})
export class CourtListenerModule {}

import { HttpService } from '@nestjs/axios';
import { Injectable, Logger, Inject } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { AxiosError, AxiosResponse } from 'axios';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import Bottleneck from 'bottleneck';
import { CourtListenerCitationLookupResult } from './interfaces/court-listener-citation-lookup-result.interface';
import { CourtListenerOpinion } from './interfaces/court-listener-opinion.interface';
import { CourtListenerCourt } from './interfaces/court-listener-court.interface';
import { CourtListenerDocket } from './interfaces/court-listener-docket.interface';
import { CourtListenerDocketEntry } from './interfaces/court-listener-docket-entry.interface';
import { CourtListenerCluster } from './interfaces/court-listener-cluster.interface';
import { CourtListenerCitationResult } from './interfaces/court-listener-citation-result.interface';

// Define TimelineEntry interface directly in the service
export interface TimelineEntry {
  date: string;
  event: string;
  document_number?: string;
  description?: string;
  pacer_doc_id?: string;
  source: 'docket' | 'metadata' | 'curated';
}

@Injectable()
export class CourtListenerService {
  private readonly logger = new Logger(CourtListenerService.name);
  private readonly baseUrl: string;
  private readonly limiter: Bottleneck;
  private readonly retryConfig: { maxAttempts: number; baseDelay: number; maxDelay: number; exponentialBase: number };
  private readonly apiKey: string | undefined;
  
  // Landmark case timeline data
  private readonly landmarkCaseTimelines: Record<string, TimelineEntry[]> = {
    // Roe v. Wade (410 U.S. 113)
    '410 U.S. 113': [
      { date: '1970-03-03', event: 'Case filed', source: 'curated' },
      { date: '1970-05-18', event: 'Petition for writ of certiorari granted', source: 'curated' },
      { date: '1971-12-13', event: 'Argued before the Supreme Court', source: 'curated' },
      { date: '1972-10-11', event: 'Reargued before the Supreme Court', source: 'curated' },
      { date: '1973-01-22', event: 'Opinion issued', source: 'curated' }
    ],
    // Brown v. Board of Education (347 U.S. 483)
    '347 U.S. 483': [
      { date: '1951-06-25', event: 'Case filed in U.S. District Court for the District of Kansas', source: 'curated' },
      { date: '1952-10-08', event: 'Argued before the Supreme Court', source: 'curated' },
      { date: '1953-12-07', event: 'Reargued before the Supreme Court', source: 'curated' },
      { date: '1954-05-17', event: 'Opinion issued', source: 'curated' },
      { date: '1955-05-31', event: 'Implementation decision (Brown II) issued', source: 'curated' }
    ],
    // Miranda v. Arizona (384 U.S. 436)
    '384 U.S. 436': [
      { date: '1963-03-13', event: 'Ernesto Miranda arrested', source: 'curated' },
      { date: '1963-06-01', event: 'Miranda convicted in Arizona state court', source: 'curated' },
      { date: '1965-11-22', event: 'Petition for writ of certiorari granted', source: 'curated' },
      { date: '1966-02-28', event: 'Argued before the Supreme Court', source: 'curated' },
      { date: '1966-06-13', event: 'Opinion issued', source: 'curated' }
    ]
  };

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.baseUrl = this.configService.get<string>('COURTLISTENER_API_BASE_URL', 'https://www.courtlistener.com/api/rest/v4');
    this.apiKey = this.configService.get<string>('COURTLISTENER_API_KEY');
    this.logger.log(`CourtListenerService initialized with base URL: ${this.baseUrl}`);

    // Get retry configuration
    // TODO: Define these in a dedicated config file/object
    this.retryConfig = {
      maxAttempts: this.configService.get<number>('COURTLISTENER_RETRY_ATTEMPTS', 3),
      baseDelay: this.configService.get<number>('COURTLISTENER_RETRY_BASE_DELAY', 500), // 500ms
      maxDelay: this.configService.get<number>('COURTLISTENER_RETRY_MAX_DELAY', 5000), // 5 seconds
      exponentialBase: this.configService.get<number>('COURTLISTENER_RETRY_EXP_BASE', 2),
    };

    // Initialize Bottleneck Rate Limiter
    // TODO: Make rate limits configurable (e.g., via ConfigService)
    const maxRps = this.configService.get<number>('COURTLISTENER_MAX_RPS', 5); // Default: 5 requests per second
    const minTime = 1000 / maxRps; // Calculate minimum time between requests
    this.limiter = new Bottleneck({
      minTime: minTime,
      maxConcurrent: this.configService.get<number>('COURTLISTENER_MAX_CONCURRENT', 1), // Added concurrency limit
      // Retry configuration for Bottleneck
      retryCount: this.retryConfig.maxAttempts - 1, // Bottleneck retries N times after the initial fail
      retryDelay: this.retryConfig.baseDelay, // Initial retry delay
      retryJitter: 200, // Add some randomness to delays
    });

    this.limiter.on('error', (error) => {
      this.logger.error('Bottleneck limiter error:', error);
    });
    this.limiter.on('failed', (error, jobInfo) => {
      this.logger.warn(`Bottleneck job failed: ${error.message}. Retries left: ${jobInfo.retryCount}`);
      // Custom retry logic based on error type
      let retryDelayMs: number | null = null;

      if (error instanceof AxiosError) {
        const status = error.response?.status;
        // Retry on rate limit errors (429) or server errors (5xx)
        if (status === 429 || (status && status >= 500 && status < 600)) {
          if (jobInfo.retryCount > 0) {
            const attemptNumber = this.retryConfig.maxAttempts - jobInfo.retryCount;
            retryDelayMs = this.calculateBackoff(attemptNumber, status === 429);
            this.logger.log(`Scheduling retry after ${retryDelayMs}ms for status ${status}. Attempt ${attemptNumber}/${this.retryConfig.maxAttempts}.`);
          } else {
            this.logger.error(`Max retries reached for error: ${error.message}`);
          }
        } else {
          // Don't retry for other client errors (4xx, except 429) or unknown errors
          this.logger.error(`Non-retryable Axios error encountered: ${error.message} (Status: ${status ?? 'N/A'})`);
        }
      } else {
        // Handle non-Axios errors if necessary, decide if retryable
        this.logger.error(`Non-Axios error in Bottleneck job: ${error.message}`);
        // Example: Potentially retry generic network errors if needed
        // if (jobInfo.retryCount > 0 && error.message.includes('ETIMEDOUT')) { 
        //   retryDelayMs = this.calculateBackoff(this.retryConfig.maxAttempts - jobInfo.retryCount);
        // }
      }

      return retryDelayMs; // Return delay in ms or null to stop retrying
    });
  }

  private async makeRequest<T>(endpoint: string, params?: Record<string, any>, method: 'GET' | 'POST' = 'GET'): Promise<T | null> {
    const url = `${this.baseUrl}/${endpoint}/`;
    const headers = this.apiKey ? { Authorization: `Token ${this.apiKey}` } : {};

    // --- Caching --- 
    // Create a deterministic cache key
    const sortedParams = params ? JSON.stringify(Object.entries(params).sort()) : '';
    const cacheKey = `courtlistener:${endpoint}:${sortedParams}`;

    try {
      const cachedData = await this.cacheManager.get<T>(cacheKey);
      if (cachedData) {
        this.logger.debug(`Cache hit for key: ${cacheKey}`);
        return cachedData;
      }
      this.logger.debug(`Cache miss for key: ${cacheKey}`);
    } catch (error) {
      this.logger.warn(`Error accessing cache for key ${cacheKey}: ${error}`);
      // Proceed without cache if error occurs
    }
    // --------------- 

    try {
      this.logger.debug(`Making CourtListener request: ${url} with params: ${JSON.stringify(params)}`);

      // Schedule the actual API call through the limiter
      const response = await this.limiter.schedule(() => {
        this.logger.verbose(`Executing rate-limited request for: ${url}`);
        if (method === 'POST') {
          return firstValueFrom(
            this.httpService.post<T>(url, params, {
              headers,
              timeout: this.configService.get<number>('HTTP_TIMEOUT', 10000),
            }),
          );
        } else {
          return firstValueFrom(
            this.httpService.get<T>(url, {
              params,
              headers,
              timeout: this.configService.get<number>('HTTP_TIMEOUT', 10000),
            }),
          );
        }
      });

      this.logger.debug(`CourtListener response status: ${response?.status}`); // Use optional chaining as response might be null on limiter errors

      // --- Cache the result --- 
      try {
        // TODO: Make TTL configurable
        if (response?.data) { // Only cache if response and data exist
          await this.cacheManager.set(cacheKey, response.data, 3600 * 1000); // Cache for 1 hour (in ms)
          this.logger.debug(`Cached result for key: ${cacheKey}`);
        }
      } catch (error) {
        this.logger.warn(`Error setting cache for key ${cacheKey}: ${error}`);
      }
      // ------------------------

      return response?.data ?? null; // Return null if response or data is missing
    } catch (error) {
      // This top-level catch block will now primarily handle errors after all retries have failed
      if (error instanceof AxiosError) {
        this.logger.error(`CourtListener API Error for ${url}: ${error.message}`, error.stack);
        const status = error.response?.status ?? 'N/A';
        const responseData = error.response?.data ? JSON.stringify(error.response.data) : 'N/A';
        this.logger.error(`Response Status: ${status}, Data: ${responseData}`);
      } else {
        this.logger.error(`Unexpected Error calling CourtListener API for ${url}: ${error}`, (error as Error).stack);
      }
      return null;
    }
  }

  /**
   * Calculates exponential backoff delay.
   * @param attempt Current attempt number (starting from 1)
   * @param isRateLimit If true, potentially use a higher base delay for 429 errors
   * @returns Delay in milliseconds
   */
  private calculateBackoff(attempt: number, isRateLimit: boolean = false): number {
    // Optionally increase base delay for rate limit errors, though Bottleneck handles minTime
    const baseDelay = this.retryConfig.baseDelay;

    const exponentialDelay = Math.min(
      baseDelay * Math.pow(this.retryConfig.exponentialBase, attempt - 1), // Calculate exponential part
      this.retryConfig.maxDelay, // Cap at max delay
    );

    // Add jitter (randomness) to prevent thundering herd
    const jitter = Math.random() * 200; // Example: +/- 100ms
    return Math.max(0, exponentialDelay + jitter - 100); // Ensure non-negative
  }

  /**
   * Extract ID from a CourtListener resource URL.
   * @param url The resource URL (e.g., "https://www.courtlistener.com/api/rest/v4/clusters/12345/")
   * @returns The extracted ID or null if not found
   */
  private extractIdFromUrl(url: string): string | null {
    try {
      // Match the last number segment before an optional trailing slash
      const match = url.match(/\/([0-9]+)\/?$/);
      return match ? match[1] : null;
    } catch (error) {
      this.logger.warn(`Error extracting ID from URL ${url}: ${error.message}`);
      return null;
    }
  }

  /**
   * Generate a timeline for a case based on available data
   * @param citation The case citation (e.g., "410 U.S. 113")
   * @param docketEntries Actual docket entries if available
   * @param caseMetadata Additional case metadata for generating synthetic entries
   * @param searchResult Optional search result with additional metadata
   * @param clusterData Optional cluster data with additional metadata
   * @returns Array of timeline entries sorted by date
   */
  generateTimeline(
    citation: string,
    docketEntries?: CourtListenerDocketEntry[],
    caseMetadata?: {
      date_filed?: string;
      date_argued?: string;
      date_reargued?: string;
      date_cert_granted?: string;
      date_cert_denied?: string;
      date_reargument_denied?: string;
      date_terminated?: string;
    },
    searchResult?: any,
    clusterData?: any
  ): TimelineEntry[] {
    const timeline: TimelineEntry[] = [];
    
    // 1. Use actual docket entries if available
    if (docketEntries && docketEntries.length > 0) {
      this.logger.debug(`Using ${docketEntries.length} actual docket entries for timeline`);
      
      docketEntries.forEach(entry => {
        if (entry.date_filed) {
          timeline.push({
            date: entry.date_filed,
            event: entry.description || 'Docket entry',
            document_number: entry.document_number,
            description: entry.description,
            pacer_doc_id: entry.pacer_doc_id,
            source: 'docket'
          });
        }
      });
    }
    
    // 2. Check for curated timeline data for landmark cases
    if (citation && this.landmarkCaseTimelines[citation]) {
      this.logger.debug(`Using curated timeline data for landmark case: ${citation}`);
      
      // Only use curated data if we don't have actual docket entries
      if (timeline.length === 0) {
        timeline.push(...this.landmarkCaseTimelines[citation]);
      }
    }
    
    // 3. Generate synthetic timeline entries from case metadata if needed
    if (timeline.length === 0) {
      this.logger.debug('Generating synthetic timeline entries from available metadata');
      
      // Extract dates from search result if available
      if (searchResult) {
        if (searchResult.dateFiled || searchResult.date_filed) {
          const filedDate = searchResult.dateFiled || searchResult.date_filed;
          timeline.push({
            date: filedDate,
            event: 'Opinion filed',
            source: 'metadata'
          });
        }
        
        if (searchResult.dateArgued || searchResult.date_argued) {
          const arguedDate = searchResult.dateArgued || searchResult.date_argued;
          timeline.push({
            date: arguedDate,
            event: 'Case argued',
            source: 'metadata'
          });
        }
        
        if (searchResult.dateReargued || searchResult.date_reargued) {
          const rearguedDate = searchResult.dateReargued || searchResult.date_reargued;
          timeline.push({
            date: rearguedDate,
            event: 'Case reargued',
            source: 'metadata'
          });
        }
      }
      
      // Extract dates from cluster data if available
      if (clusterData) {
        if (clusterData.date_filed && !timeline.some(entry => entry.event === 'Opinion filed')) {
          timeline.push({
            date: clusterData.date_filed,
            event: 'Opinion filed',
            source: 'metadata'
          });
        }
        
        // Look for oral argument date in procedural history
        if (clusterData.procedural_history && typeof clusterData.procedural_history === 'string') {
          const argDateMatch = clusterData.procedural_history.match(/argued\s+on\s+([A-Za-z]+\s+\d{1,2},\s+\d{4})/i);
          if (argDateMatch && argDateMatch[1]) {
            timeline.push({
              date: new Date(argDateMatch[1]).toISOString().split('T')[0],
              event: 'Case argued (from procedural history)',
              source: 'metadata'
            });
          }
        }
        
        // Extract dates from sub-opinions if available
        if (clusterData.sub_opinions && Array.isArray(clusterData.sub_opinions)) {
          clusterData.sub_opinions.forEach((opinion: any) => {
            if (opinion.date_created) {
              timeline.push({
                date: opinion.date_created.split('T')[0],
                event: `Opinion ${opinion.type || ''} created`,
                source: 'metadata'
              });
            }
          });
        }
      }
      
      // Use explicit case metadata if available (this is the most reliable source)
      if (caseMetadata) {
        if (caseMetadata.date_filed && !timeline.some(entry => entry.event === 'Opinion filed')) {
          timeline.push({
            date: caseMetadata.date_filed,
            event: 'Case filed',
            source: 'metadata'
          });
        }
        
        if (caseMetadata.date_cert_granted) {
          timeline.push({
            date: caseMetadata.date_cert_granted,
            event: 'Petition for writ of certiorari granted',
            source: 'metadata'
          });
        }
        
        if (caseMetadata.date_cert_denied) {
          timeline.push({
            date: caseMetadata.date_cert_denied,
            event: 'Petition for writ of certiorari denied',
            source: 'metadata'
          });
        }
        
        if (caseMetadata.date_argued && !timeline.some(entry => entry.event.includes('argued'))) {
          timeline.push({
            date: caseMetadata.date_argued,
            event: 'Argued before the court',
            source: 'metadata'
          });
        }
        
        if (caseMetadata.date_reargued && !timeline.some(entry => entry.event.includes('reargued'))) {
          timeline.push({
            date: caseMetadata.date_reargued,
            event: 'Reargued before the court',
            source: 'metadata'
          });
        }
        
        if (caseMetadata.date_reargument_denied) {
          timeline.push({
            date: caseMetadata.date_reargument_denied,
            event: 'Reargument denied',
            source: 'metadata'
          });
        }
        
        if (caseMetadata.date_terminated) {
          timeline.push({
            date: caseMetadata.date_terminated,
            event: 'Case terminated',
            source: 'metadata'
          });
        }
      }
      
      // 4. Enhanced fallback: If we still have no timeline entries, create synthetic entries based on the citation
      if (timeline.length === 0 && citation) {
        this.logger.debug('No metadata available, generating fallback timeline from citation');
        
        // Try to extract information from the citation format
        let year: number | null = null;
        let volume: number | null = null;
        let reporter: string | null = null;
        
        // Check for S. Ct. format first (Supreme Court Reporter)
        if (citation.match(/\d+\s+S\.\s*Ct\.\s+\d+/i)) {
          this.logger.debug(`Matched S. Ct. format`);
          volume = parseInt(citation.split('S.')[0].trim(), 10);
          reporter = 'S. Ct.';
          
          // Estimate year based on S. Ct. volume
          // Volumes ~140+ are from 2020s
          // Volumes ~130-139 are from 2010s
          // Volumes ~120-129 are from 2000s
          if (volume >= 140) {
            year = 2020 + (volume - 140);
          } else if (volume >= 130) {
            year = 2010 + (volume - 130);
          } else if (volume >= 120) {
            year = 2000 + (volume - 120);
          }
        }
        // Check for U.S. Reports format
        else if (citation.match(/\d+\s+U\.\s*S\.\s+\d+/i)) {
          this.logger.debug(`Matched U.S. format`);
          volume = parseInt(citation.split('U.S.')[0].trim(), 10);
          reporter = 'U.S.';
          
          // Improved estimation for U.S. Reports volumes
          // More accurate formula based on historical data:
          // Vol 1-90: 1790-1874 (early years)
          // Vol 91-300: 1875-1937 (middle period)
          // Vol 301-550: 1938-2006 (modern period)
          // Vol 551+: 2007+ (contemporary period)
          if (volume <= 90) {
            year = 1790 + Math.floor((volume - 1) * (1874 - 1790) / 89);
          } else if (volume <= 300) {
            year = 1875 + Math.floor((volume - 91) * (1937 - 1875) / 209);
          } else if (volume <= 550) {
            year = 1938 + Math.floor((volume - 301) * (2006 - 1938) / 249);
          } else {
            year = 2007 + Math.floor((volume - 551) / 8); // ~8 volumes per year in recent times
          }
        }
        // Check for F.3d format
        else if (citation.match(/\d+\s+F\.3d\s+\d+/i)) {
          this.logger.debug(`Matched F.3d format`);
          volume = parseInt(citation.split('F.3d')[0].trim(), 10);
          reporter = 'F.3d';
          
          // F.3d started around 1993
          year = Math.floor(1993 + (volume / 100));
        } 
        // Check for F.2d format
        else if (citation.match(/\d+\s+F\.2d\s+\d+/i)) {
          this.logger.debug(`Matched F.2d format`);
          volume = parseInt(citation.split('F.2d')[0].trim(), 10);
          reporter = 'F.2d';
          
          // F.2d started around 1924
          year = Math.floor(1924 + (volume / 100));
        }
        // Check for original F. format
        else if (citation.match(/\d+\s+F\.\s+\d+/i)) {
          this.logger.debug(`Matched F. format`);
          volume = parseInt(citation.split('F.')[0].trim(), 10);
          reporter = 'F.';
          
          // F. started around 1880
          year = Math.floor(1880 + (volume / 100));
        }
        
        if (year) {
          // Create estimated decision date
          const decisionDate = `${year}-06-15`; // Middle of the year as an estimate
          
          timeline.push({
            date: decisionDate,
            event: `Approximate decision date (estimated from ${reporter} citation)`,
            source: 'metadata'
          });
          
          // Add estimated cert grant date for Supreme Court cases
          if (reporter === 'U.S.' || reporter === 'S. Ct.') {
            const certGrantDate = `${year - 1}-01-15`; // Estimate cert grant ~1 year before decision
            timeline.push({
              date: certGrantDate,
              event: 'Approximate date cert granted (estimated)',
              source: 'metadata'
            });
            
            // Add estimated argument date
            const argueDate = `${year}-03-15`; // Estimate argument ~3 months before decision
            timeline.push({
              date: argueDate,
              event: 'Approximate date argued (estimated)',
              source: 'metadata'
            });
          }
          
          // For lower courts, add estimated filing date
          if (reporter && reporter.includes('F.')) {
            const filingDate = `${year - 1}-09-15`; // Estimate filing ~9 months before decision
            timeline.push({
              date: filingDate,
              event: 'Approximate filing date (estimated)',
              source: 'metadata'
            });
          }
        }
      }
    }
    
    // Sort timeline entries by date
    return timeline.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }

  /**
   * Get a comprehensive citation report with all available metadata.
   * @param citation The citation string (e.g., "410 U.S. 113")
   * @returns Detailed citation report or null on error
   */
  async getCitationReport(citation: string): Promise<{
    citation: string;
    cases: Array<{
      case_name: string;
      case_name_short?: string;
      case_name_full?: string;
      date_filed?: string;
      court?: string;
      jurisdiction?: string;
      judges?: string;
      precedential_status?: string;
      absolute_url?: string;
      pdf_url?: string;
      citations?: Array<{
        volume: number;
        reporter: string;
        page: string;
        type: number;
      }>;
      docket?: {
        docket_number?: string;
        court_id?: string;
        case_name?: string;
        date_filed?: string;
        date_argued?: string;
        date_reargued?: string;
        date_reargument_denied?: string;
        date_cert_granted?: string;
        date_cert_denied?: string;
        date_terminated?: string;
        entries?: Array<{
          date_filed?: string;
          description?: string;
          document_number?: string;
          pacer_doc_id?: string;
        }>;
      };
      timeline?: TimelineEntry[];
    }>;
  } | null> {
    try {
      // First try direct citation lookup
      const lookupResults = await this.lookupCitation(citation);
      
      // If that fails, try searching by citation
      if (!lookupResults || lookupResults.length === 0) {
        const searchResults = await this.searchByCitation(citation);
        
        if (searchResults && searchResults.results && searchResults.results.length > 0) {
          // Create a simplified report from search results
          const cases = await Promise.all(
            searchResults.results.map(async (result) => {
              // Basic case information from search result
              const caseInfo: any = {
                case_name: result.case_name || result.caseName || '',
                court: result.court || '',
                date_filed: result.date_filed || result.dateFiled || '',
                judges: result.judges || '',
                absolute_url: result.absolute_url || '',
              };
              
              let clusterData = null;
              let docketEntries = null;
              
              // Try to get cluster information if available
              if (result.cluster_id) {
                try {
                  clusterData = await this.getCluster(result.cluster_id);
                  if (clusterData) {
                    // Enhance with cluster data
                    caseInfo.case_name = clusterData.case_name || caseInfo.case_name;
                    caseInfo.case_name_short = clusterData.case_name_short;
                    caseInfo.case_name_full = clusterData.case_name_full;
                    caseInfo.date_filed = clusterData.date_filed || caseInfo.date_filed;
                    caseInfo.precedential_status = clusterData.precedential_status;
                    caseInfo.absolute_url = clusterData.absolute_url || caseInfo.absolute_url;
                    caseInfo.citations = clusterData.citations;
                    
                    // Try to get docket information
                    if (clusterData.docket_id) {
                      try {
                        const docket = await this.getDocket(clusterData.docket_id);
                        if (docket) {
                          caseInfo.docket = {
                            docket_number: docket.docket_number,
                            court_id: docket.court_id,
                            case_name: docket.case_name,
                            date_filed: docket.date_filed,
                            date_argued: docket.date_argued,
                            date_reargued: docket.date_reargued,
                            date_reargument_denied: docket.date_reargument_denied,
                            date_cert_granted: docket.date_cert_granted,
                            date_cert_denied: docket.date_cert_denied,
                            date_terminated: docket.date_terminated
                          };
                          
                          // Try to get docket entries
                          try {
                            const entriesResponse = await this.getDocketEntries(clusterData.docket_id);
                            if (entriesResponse && entriesResponse.results) {
                              docketEntries = entriesResponse.results;
                              caseInfo.docket.entries = docketEntries.map(entry => ({
                                date_filed: entry.date_filed,
                                description: entry.description,
                                document_number: entry.document_number,
                                pacer_doc_id: entry.pacer_doc_id
                              }));
                            }
                          } catch (error) {
                            this.logger.error(`Error fetching docket entries: ${error.message}`);
                          }
                        }
                      } catch (error) {
                        this.logger.error(`Error fetching docket: ${error.message}`);
                      }
                    }
                  }
                } catch (error) {
                  this.logger.error(`Error fetching cluster: ${error.message}`);
                }
              }
              
              // Generate timeline
              const caseMetadata = caseInfo.docket || {};
              caseInfo.timeline = this.generateTimeline(
                citation,
                docketEntries,
                caseMetadata,
                result,
                clusterData
              );
              
              return caseInfo;
            })
          );
          
          return {
            citation,
            cases
          };
        }
        
        // If we still have no results, return null
        this.logger.warn(`No results found for citation: ${citation}`);
        return {
          citation,
          cases: [{
            case_name: '',
            timeline: this.generateTimeline(citation)
          }]
        };
      }
      
      // Process lookup results
      const cases = await Promise.all(
        lookupResults.map(async (result) => {
          // Start with basic case information
          const caseInfo: any = {
            case_name: result.case_name || '',
            case_name_short: result.case_name_short || '',
            case_name_full: result.case_name_full || '',
            date_filed: result.date_filed || '',
            // Use properties that exist on the CourtListenerCitationLookupResult type
            court: result.court_id || '',
            jurisdiction: '',
            judges: result.judges || '',
            precedential_status: result.status || '',
            absolute_url: '',
            citations: result.citations || []
          };

          // Variables to store docket entries and metadata for timeline generation
          let docketEntries: CourtListenerDocketEntry[] | undefined;
          let caseMetadata: any = {};
          let clusterData = null;

          // Try to get cluster information if available
          const clusterId = result.cluster && this.extractIdFromUrl(result.cluster);
          if (clusterId) {
            try {
              clusterData = await this.getCluster(clusterId);
              if (clusterData) {
                // Merge additional information from cluster
                if (clusterData.case_name) caseInfo.case_name = clusterData.case_name;
                if (clusterData.case_name_short) caseInfo.case_name_short = clusterData.case_name_short;
                if (clusterData.case_name_full) caseInfo.case_name_full = clusterData.case_name_full;
                if (clusterData.date_filed) {
                  caseInfo.date_filed = clusterData.date_filed;
                  caseMetadata.date_filed = clusterData.date_filed;
                }
                if (clusterData.precedential_status) caseInfo.precedential_status = clusterData.precedential_status;
                if (clusterData.absolute_url) caseInfo.absolute_url = clusterData.absolute_url;
                if (clusterData.citations && clusterData.citations.length > 0) {
                  caseInfo.citations = clusterData.citations;
                }

                // Try to get docket information if available
                const docketId = clusterData.docket && this.extractIdFromUrl(clusterData.docket);
                if (docketId) {
                  try {
                    const docket = await this.getDocket(docketId);
                    if (docket) {
                      caseInfo.docket = {
                        docket_number: docket.docket_number,
                        court_id: docket.court_id,
                        case_name: docket.case_name,
                        date_filed: docket.date_filed,
                        date_argued: docket.date_argued,
                        date_reargued: docket.date_reargued,
                        date_reargument_denied: docket.date_reargument_denied,
                        date_cert_granted: docket.date_cert_granted,
                        date_cert_denied: docket.date_cert_denied,
                        date_terminated: docket.date_terminated
                      };

                      // Store docket metadata for timeline generation
                      caseMetadata = {
                        ...caseMetadata,
                        date_filed: docket.date_filed,
                        date_argued: docket.date_argued,
                        date_reargued: docket.date_reargued,
                        date_reargument_denied: docket.date_reargument_denied,
                        date_cert_granted: docket.date_cert_granted,
                        date_cert_denied: docket.date_cert_denied,
                        date_terminated: docket.date_terminated
                      };

                      // Try to get docket entries if available
                      try {
                        const docketEntriesResponse = await this.getDocketEntries(docketId);
                        if (docketEntriesResponse && docketEntriesResponse.results && docketEntriesResponse.results.length > 0) {
                          docketEntries = docketEntriesResponse.results;
                          caseInfo.docket.entries = docketEntries.map(entry => ({
                            date_filed: entry.date_filed,
                            description: entry.description,
                            document_number: entry.document_number,
                            pacer_doc_id: entry.pacer_doc_id
                          }));
                        } else {
                          this.logger.debug(`No docket entries found for docket ID: ${docketId}`);
                        }
                      } catch (error) {
                        this.logger.error(`Error fetching docket entries for docket ID ${docketId}: ${error}`);
                      }
                    }
                  } catch (error) {
                    this.logger.error(`Error fetching docket for docket ID ${docketId}: ${error}`);
                  }
                }
              }
            } catch (error) {
              this.logger.error(`Error fetching cluster for cluster ID ${clusterId}: ${error}`);
            }
          }

          // Generate timeline using the integrated timeline functionality
          caseInfo.timeline = this.generateTimeline(
            citation,
            docketEntries,
            caseMetadata,
            result,
            clusterData
          );

          return caseInfo;
        })
      );

      return {
        citation,
        cases
      };
    } catch (error) {
      this.logger.error(`Error generating citation report for ${citation}: ${error}`);
      return null;
    }
  }

  /**
   * Lookup a citation using CourtListener.
   * @param citationText The citation string to lookup (e.g., "576 U.S. 644").
   * @returns Array of lookup results or null on error.
   */
  async lookupCitation(citationText: string): Promise<CourtListenerCitationLookupResult[] | null> {
    // Regex to parse Volume Reporter Page format
    // Allows letters, numbers, dots, spaces in reporter. Optional space before page.
    const citationRegex = /^(\d+)\s+([\w\.\s]+?)\s?(\d+)$/;
    const match = citationText.trim().match(citationRegex);

    if (!match) {
      this.logger.warn(
        `Could not parse citation format: ${citationText}. Expected 'Volume Reporter Page'.`,
        'CourtListenerService',
      );
      // According to docs, we could also try sending the raw text:
      // return this.makeRequest<CourtListenerCitationLookupResult[]>('citation-lookup', { text: citationText }, 'POST');
      // For now, return null if parsing fails to enforce structure.
      return null;
    }

    const [, volume, reporter, page] = match;

    // Use the 'citation-lookup' endpoint with POST data
    const lookupResults = await this.makeRequest<CourtListenerCitationLookupResult[]>(
      'citation-lookup',
      { volume, reporter: reporter.trim(), page },
      'POST', // Explicitly specify POST method
    );

    // If we have results, try to enrich them with cluster data
    if (lookupResults && lookupResults.length > 0) {
      const enrichedResults = await Promise.all(
        lookupResults.map(async (result) => {
          // Extract cluster ID from URL if available
          if (result.cluster) {
            const clusterId = this.extractIdFromUrl(result.cluster);
            if (clusterId) {
              try {
                const clusterData = await this.getCluster(clusterId);
                if (clusterData) {
                  // Enrich the result with cluster data
                  return {
                    ...result,
                    case_name: clusterData.case_name || result.case_name,
                    case_name_short: clusterData.case_name_short,
                    case_name_full: clusterData.case_name_full,
                    date_filed: clusterData.date_filed || result.date_filed,
                    judges: clusterData.judges,
                    precedential_status: clusterData.precedential_status,
                    citations: clusterData.citations,
                  };
                }
              } catch (error) {
                this.logger.warn(`Error enriching citation result with cluster data: ${error.message}`);
              }
            }
          }
          return result;
        })
      );
      return enrichedResults;
    }

    return lookupResults;
  }

  /**
   * Get details for a specific opinion.
   * @param opinionId The CourtListener ID for the opinion.
   * @returns Opinion details or null on error.
   */
  async getOpinion(opinionId: string | number): Promise<CourtListenerOpinion | null> {
    return this.makeRequest<CourtListenerOpinion>(`opinions/${opinionId}`);
  }

  /**
   * Get details for a specific court.
   * @param courtId The CourtListener ID (e.g., 'ca9') for the court.
   * @returns Court details or null on error.
   */
  async getCourt(courtId: string | number): Promise<CourtListenerCourt | null> {
    return this.makeRequest<CourtListenerCourt>(`courts/${courtId}`);
  }

  /**
   * Get details for a specific docket.
   * @param docketId The CourtListener ID for the docket.
   * @returns Docket details or null on error.
   */
  async getDocket(docketId: string | number): Promise<CourtListenerDocket | null> {
    // Handle path-like docket IDs (e.g., "uscourts/ca9/20-12345")
    if (typeof docketId === 'string' && docketId.includes('/')) {
      // Split the path components and reconstruct the URL path
      const pathComponents = docketId.split('/');
      // For path-like IDs, we need to construct a custom endpoint
      return this.makeRequest<CourtListenerDocket>(`dockets/${pathComponents.join('/')}`);
    }
    // For numeric IDs, use the standard endpoint
    return this.makeRequest<CourtListenerDocket>(`dockets/${docketId}`);
  }

  /**
   * Get docket entries for a specific docket.
   * @param docketId The CourtListener ID for the docket.
   * @returns Array of docket entries or null on error.
   */
  async getDocketEntries(docketId: string | number): Promise<{ results: CourtListenerDocketEntry[] } | null> {
    // Handle path-like docket IDs (e.g., "uscourts/ca9/20-12345")
    if (typeof docketId === 'string' && docketId.includes('/')) {
      // For path-like IDs, we need to construct a custom endpoint for docket entries
      const pathComponents = docketId.split('/');
      // The API might expect a different format for docket entries with path-like IDs
      this.logger.debug(`Fetching docket entries for path-like ID: ${docketId}`);
      return this.makeRequest<{ results: CourtListenerDocketEntry[] }>(`dockets/${pathComponents.join('/')}/entries`);
    }
    
    // For numeric IDs, use the standard endpoint with query parameters
    return this.makeRequest<{ results: CourtListenerDocketEntry[] }>('docket-entries', { docket: docketId });
  }

  /**
   * Get details for a specific cluster (group of opinions in a case).
   * @param clusterId The CourtListener ID for the cluster.
   * @returns Cluster details or null on error.
   */
  async getCluster(clusterId: string | number): Promise<CourtListenerCluster | null> {
    return this.makeRequest<CourtListenerCluster>(`clusters/${clusterId}`);
  }

  /**
   * Search for clusters by citation.
   * @param citation The citation string (e.g., "410 U.S. 113")
   * @returns Array of citation results or null on error.
   */
  async searchByCitation(citation: string): Promise<{ results: CourtListenerCitationResult[] } | null> {
    return this.makeRequest<{ results: CourtListenerCitationResult[] }>('search', { 
      type: 'o', // 'o' for opinions
      citation: citation,
      format: 'json'
    });
  }

  /**
   * Search for cases using various criteria.
   * @param params Search parameters
   * @returns Search results or null on error
   */
  async searchCases(params: {
    query?: string;
    citation?: string;
    case_name?: string;
    judge?: string;
    court?: string;
    jurisdiction?: string;
    filed_after?: string; // ISO date format
    filed_before?: string; // ISO date format
    page?: number;
    page_size?: number;
    cursor?: string; // Add support for cursor-based pagination
  }): Promise<{ 
    count: number; 
    results: CourtListenerCitationResult[];
    next?: string;
    previous?: string;
  } | null> {
    // Add default type and format
    const searchParams = {
      ...params,
      type: 'o', // 'o' for opinions
      format: 'json'
    };

    // Handle cursor-based pagination
    // If a specific page is requested and there's no cursor, we need to fetch all pages up to the requested one
    if (params.page && params.page > 1 && !params.cursor) {
      this.logger.debug(`Fetching page ${params.page} without cursor, will need to paginate through results`);
      
      let currentPage = 1;
      let currentResponse = await this.makeRequest<{ 
        count: number; 
        results: CourtListenerCitationResult[];
        next?: string;
        previous?: string;
      }>('search', { ...searchParams, page: currentPage });
      
      if (!currentResponse || !currentResponse.next) {
        return currentResponse; // Return first page if there are no more pages
      }
      
      // Extract cursor from next URL if available
      while (currentPage < params.page && currentResponse?.next) {
        currentPage++;
        
        // Parse the cursor from the next URL
        const nextUrl = new URL(currentResponse.next);
        const cursor = nextUrl.searchParams.get('cursor');
        
        if (cursor) {
          // Use the cursor for the next request
          currentResponse = await this.makeRequest<{ 
            count: number; 
            results: CourtListenerCitationResult[];
            next?: string;
            previous?: string;
          }>('search', { ...searchParams, cursor });
        } else {
          // Fallback to page-based pagination if cursor is not available
          currentResponse = await this.makeRequest<{ 
            count: number; 
            results: CourtListenerCitationResult[];
            next?: string;
            previous?: string;
          }>('search', { ...searchParams, page: currentPage });
        }
        
        if (!currentResponse) {
          this.logger.error(`Failed to fetch page ${currentPage}`);
          break;
        }
      }
      
      return currentResponse;
    }

    // Standard request with either page 1 or cursor-based pagination
    return this.makeRequest<{ 
      count: number; 
      results: CourtListenerCitationResult[];
      next?: string;
      previous?: string;
    }>('search', searchParams);
  }
}

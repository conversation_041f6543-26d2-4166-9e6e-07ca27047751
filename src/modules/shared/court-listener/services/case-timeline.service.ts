import { Injectable, Logger } from '@nestjs/common';
import { CourtListenerDocketEntry } from '../interfaces/court-listener-docket-entry.interface';

export interface TimelineEntry {
  date: string;
  event: string;
  document_number?: string;
  description?: string;
  pacer_doc_id?: string;
  source: 'docket' | 'metadata' | 'curated';
}

@Injectable()
export class CaseTimelineService {
  private readonly logger = new Logger(CaseTimelineService.name);
  
  // Landmark case timeline data
  private readonly landmarkCaseTimelines: Record<string, TimelineEntry[]> = {
    // <PERSON> v<PERSON> (410 U.S. 113)
    '410 U.S. 113': [
      { date: '1970-03-03', event: 'Case filed', source: 'curated' },
      { date: '1970-05-18', event: 'Petition for writ of certiorari granted', source: 'curated' },
      { date: '1971-12-13', event: 'Argued before the Supreme Court', source: 'curated' },
      { date: '1972-10-11', event: 'Reargued before the Supreme Court', source: 'curated' },
      { date: '1973-01-22', event: 'Opinion issued', source: 'curated' }
    ],
    // <PERSON> v. Board of Education (347 U.S. 483)
    '347 U.S. 483': [
      { date: '1951-06-25', event: 'Case filed in U.S. District Court for the District of Kansas', source: 'curated' },
      { date: '1952-10-08', event: 'Argued before the Supreme Court', source: 'curated' },
      { date: '1953-12-07', event: 'Reargued before the Supreme Court', source: 'curated' },
      { date: '1954-05-17', event: 'Opinion issued', source: 'curated' },
      { date: '1955-05-31', event: 'Implementation decision (Brown II) issued', source: 'curated' }
    ],
    // Miranda v. Arizona (384 U.S. 436)
    '384 U.S. 436': [
      { date: '1963-03-13', event: 'Ernesto Miranda arrested', source: 'curated' },
      { date: '1963-06-01', event: 'Miranda convicted in Arizona state court', source: 'curated' },
      { date: '1965-11-22', event: 'Petition for writ of certiorari granted', source: 'curated' },
      { date: '1966-02-28', event: 'Argued before the Supreme Court', source: 'curated' },
      { date: '1966-06-13', event: 'Opinion issued', source: 'curated' }
    ]
  };

  constructor() {
    this.logger.log('CaseTimelineService initialized');
  }

  /**
   * Generate a timeline for a case based on available data
   * @param citation The case citation (e.g., "410 U.S. 113")
   * @param docketEntries Actual docket entries if available
   * @param caseMetadata Additional case metadata for generating synthetic entries
   * @returns Array of timeline entries sorted by date
   */
  generateTimeline(
    citation: string,
    docketEntries?: CourtListenerDocketEntry[],
    caseMetadata?: {
      date_filed?: string;
      date_argued?: string;
      date_reargued?: string;
      date_cert_granted?: string;
      date_cert_denied?: string;
      date_reargument_denied?: string;
      date_terminated?: string;
    }
  ): TimelineEntry[] {
    const timeline: TimelineEntry[] = [];
    
    // 1. Use actual docket entries if available
    if (docketEntries && docketEntries.length > 0) {
      this.logger.debug(`Using ${docketEntries.length} actual docket entries for timeline`);
      
      docketEntries.forEach(entry => {
        if (entry.date_filed) {
          timeline.push({
            date: entry.date_filed,
            event: entry.description || 'Docket entry',
            document_number: entry.document_number,
            description: entry.description,
            pacer_doc_id: entry.pacer_doc_id,
            source: 'docket'
          });
        }
      });
    }
    
    // 2. Check for curated timeline data for landmark cases
    if (citation && this.landmarkCaseTimelines[citation]) {
      this.logger.debug(`Using curated timeline data for landmark case: ${citation}`);
      
      // Only use curated data if we don't have actual docket entries
      if (timeline.length === 0) {
        timeline.push(...this.landmarkCaseTimelines[citation]);
      }
    }
    
    // 3. Generate synthetic timeline entries from case metadata if needed
    if (timeline.length === 0 && caseMetadata) {
      this.logger.debug('Generating synthetic timeline entries from case metadata');
      
      if (caseMetadata.date_filed) {
        timeline.push({
          date: caseMetadata.date_filed,
          event: 'Case filed',
          source: 'metadata'
        });
      }
      
      if (caseMetadata.date_cert_granted) {
        timeline.push({
          date: caseMetadata.date_cert_granted,
          event: 'Petition for writ of certiorari granted',
          source: 'metadata'
        });
      }
      
      if (caseMetadata.date_cert_denied) {
        timeline.push({
          date: caseMetadata.date_cert_denied,
          event: 'Petition for writ of certiorari denied',
          source: 'metadata'
        });
      }
      
      if (caseMetadata.date_argued) {
        timeline.push({
          date: caseMetadata.date_argued,
          event: 'Argued before the court',
          source: 'metadata'
        });
      }
      
      if (caseMetadata.date_reargued) {
        timeline.push({
          date: caseMetadata.date_reargued,
          event: 'Reargued before the court',
          source: 'metadata'
        });
      }
      
      if (caseMetadata.date_reargument_denied) {
        timeline.push({
          date: caseMetadata.date_reargument_denied,
          event: 'Reargument denied',
          source: 'metadata'
        });
      }
      
      if (caseMetadata.date_terminated) {
        timeline.push({
          date: caseMetadata.date_terminated,
          event: 'Case terminated',
          source: 'metadata'
        });
      }
    }
    
    // Sort timeline entries by date
    return timeline.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }
}

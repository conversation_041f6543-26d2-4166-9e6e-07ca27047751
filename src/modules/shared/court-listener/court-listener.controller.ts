import { Controller, Get, Post, Param, Query, Body, Logger, UsePipes, ValidationPipe } from '@nestjs/common';
import {
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse as SwaggerApiResponse,
  ApiTags,
  ApiBody,
} from '@nestjs/swagger';
import { CourtListenerService } from './court-listener.service';
// Removed unused import for PrecedentService

// Define API response interface locally if it doesn't exist
interface ApiResponse<T> {
  status: 'success' | 'error' | 'not_found';
  message: string;
  data: T | null;
}

@ApiTags('court-listener')
@Controller('/court-listener') // Removed duplicate /api prefix
export class CourtListenerController {
  private readonly logger = new Logger(CourtListenerController.name);

  constructor(
    private readonly courtListenerService: CourtListenerService,
  ) {}

  @Get('/citation/:citation')
  @ApiOperation({ summary: 'Get information about a legal citation' })
  @ApiParam({
    name: 'citation',
    description: 'Legal citation (e.g., "410 U.S. 113")',
  })
  @SwaggerApiResponse({
    status: 200,
    description: 'Citation information retrieved successfully',
  })
  @SwaggerApiResponse({ status: 404, description: 'Citation not found' })
  async getCitationInfo(
    @Param('citation') citation: string,
  ): Promise<ApiResponse<any>> {
    try {
      // Use the enhanced citation report functionality
      const citationReport = await this.courtListenerService.getCitationReport(
        citation,
      );

      if (!citationReport || citationReport.cases.length === 0) {
        // If no results from citation report, try direct lookup
        const lookupResults = await this.courtListenerService.lookupCitation(
          citation,
        );

        if (!lookupResults || lookupResults.length === 0) {
          // If still no results, try searching by citation
          const searchResults =
            await this.courtListenerService.searchByCitation(citation);

          if (!searchResults || searchResults.results.length === 0) {
            return {
              status: 'not_found',
              message: `Citation "${citation}" not found`,
              data: null,
            };
          }

          // Return search results if found
          return {
            status: 'success',
            message: `Citation "${citation}" found via search`,
            data: {
              citation,
              cases: searchResults.results.map((result) => ({
                case_name: result.case_name || '',
                court: result.court || '',
                date_filed: result.date_filed || '',
                docket_number: result.docket_number || '',
                absolute_url: result.absolute_url || '',
                cluster: result.cluster || '',
              })),
            },
          };
        }

        // Return lookup results if found
        return {
          status: 'success',
          message: `Citation "${citation}" found via lookup`,
          data: {
            citation,
            cases: lookupResults.map((result) => ({
              case_name: result.case_name || '',
              case_name_short: result.case_name_short || '',
              case_name_full: result.case_name_full || '',
              date_filed: result.date_filed || '',
              court: result.court_id || '',
              judges: result.judges || '',
              precedential_status: result.precedential_status || '',
              cluster: result.cluster || '',
              citations: result.citations || [],
            })),
          },
        };
      }

      // Return the comprehensive citation report
      return {
        status: 'success',
        message: `Citation "${citation}" found`,
        data: citationReport,
      };
    } catch (error) {
      this.logger.error(
        `Error retrieving citation information: ${error.message}`,
      );
      return {
        status: 'error',
        message: `Error retrieving citation information: ${error.message}`,
        data: null,
      };
    }
  }

  @Get('/citation/:citation/timeline')
  @ApiOperation({ summary: 'Get procedural timeline for a legal citation' })
  @ApiParam({ name: 'citation', description: 'Legal citation (e.g., "410 U.S. 113")' })
  @SwaggerApiResponse({ status: 200, description: 'Timeline retrieved successfully' })
  @SwaggerApiResponse({ status: 404, description: 'Citation not found' })
  async getCitationTimeline(@Param('citation') citation: string): Promise<ApiResponse<any>> {
    try {
      // First try to get the citation report which includes all metadata
      const citationReport = await this.courtListenerService.getCitationReport(citation);
      
      if (!citationReport || citationReport.cases.length === 0) {
        return {
          status: 'not_found',
          message: `Citation "${citation}" not found`,
          data: null
        };
      }
      
      // Extract timeline data from the first case in the report
      const timelineData = citationReport.cases[0].timeline || [];
      
      return {
        status: 'success',
        message: `Timeline for "${citation}" retrieved successfully`,
        data: {
          citation,
          case_name: citationReport.cases[0].case_name,
          timeline: timelineData
        }
      };
    } catch (error) {
      this.logger.error(`Error retrieving timeline for citation: ${error.message}`);
      return {
        status: 'error',
        message: `Error retrieving timeline: ${error.message}`,
        data: null
      };
    }
  }

  @Post('/citation-report')
  @ApiOperation({ summary: 'Get a detailed citation report' })
  @SwaggerApiResponse({
    status: 200,
    description: 'Citation report retrieved successfully',
  })
  @SwaggerApiResponse({ status: 404, description: 'Citation not found' })
  async getCitationReport(
    @Body() body: { citation: string },
  ): Promise<ApiResponse<any>> {
    try {
      this.logger.debug(`Getting citation report for: ${body.citation}`);
      
      // Log the API request
      this.logger.log(`CourtListener API Request - Citation: ${body.citation}`);
      
      // Get the citation report
      const citationReport = await this.courtListenerService.getCitationReport(
        body.citation,
      );

      // Log the full API response
      this.logger.log(`CourtListener API Response for ${body.citation}:`);
      this.logger.log(JSON.stringify(citationReport, null, 2));
      
      // Log the structure of the response
      if (citationReport) {
        this.logger.log(`Response structure: citation=${typeof citationReport.citation}, cases.length=${citationReport.cases?.length || 0}`);
        if (citationReport.cases && citationReport.cases.length > 0) {
          const firstCase = citationReport.cases[0];
          this.logger.log(`First case structure: case_name=${typeof firstCase.case_name} (${firstCase.case_name ? 'has value' : 'empty'})`);
          this.logger.log(`First case timeline: ${firstCase.timeline ? firstCase.timeline.length : 'undefined'} entries`);
          
          // Log available fields in the first case
          const availableFields = Object.keys(firstCase).filter(key => firstCase[key] !== undefined && firstCase[key] !== null && firstCase[key] !== '');
          this.logger.log(`Available fields in first case: ${availableFields.join(', ')}`);
        }
      }

      if (!citationReport) {
        this.logger.warn(`No citation report found for: ${body.citation}`);
        return {
          status: 'not_found',
          message: `No information found for citation: ${body.citation}`,
          data: null,
        };
      }

      return {
        status: 'success',
        message: 'Citation report retrieved successfully',
        data: citationReport,
      };
    } catch (error) {
      this.logger.error(`Error getting citation report: ${error.message}`);
      return {
        status: 'error',
        message: `Error retrieving citation report: ${error.message}`,
        data: null,
      };
    }
  }

  @Get('/search') // Removed duplicate /api prefix
  @ApiOperation({ summary: 'Search for legal cases' })
  @ApiQuery({
    name: 'query',
    description: 'Search query',
    required: true,
  })
  @ApiQuery({
    name: 'citation',
    required: false,
    description: 'Citation string',
  })
  @ApiQuery({ name: 'case_name', required: false, description: 'Case name' })
  @ApiQuery({ name: 'judge', required: false, description: 'Judge name' })
  @ApiQuery({
    name: 'court',
    required: false,
    description: 'Court identifier',
  })
  @ApiQuery({
    name: 'jurisdiction',
    required: false,
    description: 'Jurisdiction',
  })
  @ApiQuery({
    name: 'filed_after',
    required: false,
    description: 'Filed after date (ISO format)',
  })
  @ApiQuery({
    name: 'filed_before',
    required: false,
    description: 'Filed before date (ISO format)',
  })
  @ApiQuery({ name: 'page', required: false, description: 'Page number' })
  @ApiQuery({ name: 'page_size', required: false, description: 'Page size' })
  @ApiQuery({
    name: 'cursor',
    required: false,
    description: 'Pagination cursor for direct access to a specific page',
  })
  @SwaggerApiResponse({
    status: 200,
    description: 'Search results retrieved successfully',
  })
  async searchCases(@Query() query: any): Promise<ApiResponse<any>> {
    const results = await this.courtListenerService.searchCases(query);
    if (!results || results.count === 0) {
      return {
        status: 'not_found',
        message: 'No cases found matching the search criteria',
        data: query,
      };
    }

    // Transform the next/previous URLs to use our API endpoint format
    // This makes pagination easier for frontend clients
    if (results.next) {
      const nextUrl = new URL(results.next);
      const cursor = nextUrl.searchParams.get('cursor');
      if (cursor) {
        // Create a URL that points to our API with the cursor
        const page = parseInt(query.page || '1') + 1;
        results.next = `/api/court-listener/search?cursor=${encodeURIComponent(
          cursor,
        )}&page=${page}`;

        // Add any other query parameters that were in the original request
        Object.keys(query).forEach((key) => {
          if (key !== 'page' && key !== 'cursor') {
            results.next += `&${key}=${encodeURIComponent(query[key])}`;
          }
        });
      }
    }

    if (results.previous) {
      const prevUrl = new URL(results.previous);
      const cursor = prevUrl.searchParams.get('cursor');
      if (cursor) {
        // Create a URL that points to our API with the cursor
        const page = Math.max(1, parseInt(query.page || '2') - 1);
        results.previous = `/api/court-listener/search?cursor=${encodeURIComponent(
          cursor,
        )}&page=${page}`;

        // Add any other query parameters that were in the original request
        Object.keys(query).forEach((key) => {
          if (key !== 'page' && key !== 'cursor') {
            results.previous += `&${key}=${encodeURIComponent(query[key])}`;
          }
        });
      }
    }

    return {
      status: 'success',
      message: 'Search results retrieved successfully',
      data: results,
    };
  }

  @Get('/docket/:id')
  @ApiOperation({ summary: 'Get docket information by ID' })
  @ApiParam({ name: 'id', description: 'Docket ID' })
  @SwaggerApiResponse({
    status: 200,
    description: 'Docket information retrieved successfully',
  })
  @SwaggerApiResponse({ status: 404, description: 'Docket not found' })
  async getDocket(@Param('id') id: string): Promise<ApiResponse<any>> {
    const docket = await this.courtListenerService.getDocket(id);
    if (!docket) {
      return {
        status: 'not_found',
        message: 'Docket not found',
        data: id,
      };
    }
    return {
      status: 'success',
      message: 'Docket retrieved successfully',
      data: docket,
    };
  }

  @Get('/docket/:id/entries')
  @ApiOperation({ summary: 'Get docket entries by docket ID' })
  @ApiParam({ name: 'id', description: 'Docket ID' })
  @SwaggerApiResponse({
    status: 200,
    description: 'Docket entries retrieved successfully',
  })
  @SwaggerApiResponse({ status: 404, description: 'Docket entries not found' })
  async getDocketEntries(@Param('id') id: string): Promise<ApiResponse<any>> {
    const entries = await this.courtListenerService.getDocketEntries(id);
    if (!entries) {
      return {
        status: 'not_found',
        message: 'Docket entries not found',
        data: id,
      };
    }
    return {
      status: 'success',
      message: 'Docket entries retrieved successfully',
      data: entries,
    };
  }

  @Get('/cluster/:id')
  @ApiOperation({ summary: 'Get cluster information by ID' })
  @ApiParam({ name: 'id', description: 'Cluster ID' })
  @SwaggerApiResponse({
    status: 200,
    description: 'Cluster information retrieved successfully',
  })
  @SwaggerApiResponse({ status: 404, description: 'Cluster not found' })
  async getCluster(@Param('id') id: string): Promise<ApiResponse<any>> {
    const cluster = await this.courtListenerService.getCluster(id);
    if (!cluster) {
      return {
        status: 'not_found',
        message: 'Cluster not found',
        data: id,
      };
    }
    return {
      status: 'success',
      message: 'Cluster information retrieved successfully',
      data: cluster,
    };
  }


}

import { Controller, Get, Post, Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsBoolean, IsNumber, Min } from 'class-validator';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { CitationAnalysisService } from '../services/citation-analysis/citation-analysis.service';
import { 
  CitationAnalysisResponse, 
  CitationAnalysisOptions 
} from '../interfaces/citation-analysis.interface';
import { RequireFeatures } from '../../subscription/decorators/require-features.decorator';
import { FeatureAvailabilityGuard } from '../../subscription/guards/feature-availability.guard';

class AnalyzeCitationDto {
  @ApiProperty({ example: '410 U.S. 113', description: 'The citation text to analyze' })
  @IsString()
  @IsNotEmpty()
  citation: string;

  @ApiProperty({ required: false, description: 'Whether to include citation relationships' })
  @IsOptional()
  @IsBoolean()
  includeRelationships?: boolean;

  @ApiProperty({ required: false, description: 'Whether to include precedent chains' })
  @IsOptional()
  @IsBoolean()
  includePrecedentChains?: boolean;

  @ApiProperty({ required: false, description: 'Whether to include citation impact score' })
  @IsOptional()
  @IsBoolean()
  includeImpact?: boolean;

  @ApiProperty({ required: false, description: 'Maximum depth for relationship mapping', minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxRelationshipDepth?: number;

  @ApiProperty({ required: false, description: 'Maximum length for precedent chains', minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  maxPrecedentChainLength?: number;
}

class AnalyzeDocumentCitationsDto {
  documentText: string;
  includeRelationships?: boolean;
  includePrecedentChains?: boolean;
  includeImpact?: boolean;
  maxRelationshipDepth?: number;
  maxPrecedentChainLength?: number;
}

@ApiTags('citation-analysis')
@Controller('legal-research/citations')
@UseGuards(JwtAuthGuard, FeatureAvailabilityGuard)
@ApiBearerAuth()
export class CitationAnalysisController {
  constructor(private readonly citationAnalysisService: CitationAnalysisService) {}

  @Post('analyze/basic')
  @RequireFeatures('basic_citation_analysis')
  @ApiOperation({ summary: 'Analyze a single citation (basic)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns basic citation analysis',
    type: Object
  })
  async analyzeBasicCitation(
    @Body() dto: AnalyzeCitationDto
  ): Promise<CitationAnalysisResponse | null> {
    // Basic citation analysis only includes simple relationships
    const options: CitationAnalysisOptions = {
      includeRelationships: true,
      includePrecedentChains: false,
      includeImpact: false,
      maxRelationshipDepth: 1
    };

    return this.citationAnalysisService.analyzeCitation(dto.citation, options);
  }

  @Post('analyze/enhanced')
  @RequireFeatures('enhanced_citation_analysis')
  @ApiOperation({ summary: 'Analyze a single citation with enhanced features' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns enhanced citation analysis with relationships, precedent chains, and impact scoring',
    type: Object
  })
  async analyzeEnhancedCitation(
    @Body() dto: AnalyzeCitationDto
  ): Promise<CitationAnalysisResponse | null> {
    const options: CitationAnalysisOptions = {
      includeRelationships: dto.includeRelationships ?? true,
      includePrecedentChains: dto.includePrecedentChains ?? true,
      includeImpact: dto.includeImpact ?? true,
      maxRelationshipDepth: dto.maxRelationshipDepth ?? 2,
      maxPrecedentChainLength: dto.maxPrecedentChainLength ?? 5
    };

    return this.citationAnalysisService.analyzeCitation(dto.citation, options);
  }

  @Post('analyze')
  @RequireFeatures('basic_citation_analysis')
  @ApiOperation({ summary: 'Analyze a single citation (auto-detects subscription tier)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns citation analysis based on subscription tier',
    type: Object
  })
  async analyzeCitation(
    @Body() dto: AnalyzeCitationDto
  ): Promise<CitationAnalysisResponse | null> {
    // This endpoint will provide different levels of analysis based on the user's subscription
    return this.citationAnalysisService.analyzeCitationBySubscription(dto.citation, {
      includeRelationships: dto.includeRelationships,
      includePrecedentChains: dto.includePrecedentChains,
      includeImpact: dto.includeImpact,
      maxRelationshipDepth: dto.maxRelationshipDepth,
      maxPrecedentChainLength: dto.maxPrecedentChainLength
    });
  }

  @Post('analyze-document/basic')
  @RequireFeatures('basic_citation_analysis')
  @ApiOperation({ summary: 'Analyze all citations in a document (basic)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns basic analysis for all detected citations',
    type: [Object]
  })
  async analyzeDocumentCitationsBasic(
    @Body() dto: AnalyzeDocumentCitationsDto
  ): Promise<CitationAnalysisResponse[]> {
    const options: CitationAnalysisOptions = {
      includeRelationships: true,
      includePrecedentChains: false,
      includeImpact: false,
      maxRelationshipDepth: 1
    };

    return this.citationAnalysisService.analyzeDocumentCitations(dto.documentText, options);
  }

  @Post('analyze-document/enhanced')
  @RequireFeatures('enhanced_citation_analysis')
  @ApiOperation({ summary: 'Analyze all citations in a document with enhanced features' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns enhanced analysis for all detected citations',
    type: [Object]
  })
  async analyzeDocumentCitationsEnhanced(
    @Body() dto: AnalyzeDocumentCitationsDto
  ): Promise<CitationAnalysisResponse[]> {
    const options: CitationAnalysisOptions = {
      includeRelationships: dto.includeRelationships ?? true,
      includePrecedentChains: dto.includePrecedentChains ?? true,
      includeImpact: dto.includeImpact ?? true,
      maxRelationshipDepth: dto.maxRelationshipDepth ?? 2,
      maxPrecedentChainLength: dto.maxPrecedentChainLength ?? 5
    };

    return this.citationAnalysisService.analyzeDocumentCitations(dto.documentText, options);
  }

  @Post('analyze-document')
  @RequireFeatures('basic_citation_analysis')
  @ApiOperation({ summary: 'Analyze all citations in a document (auto-detects subscription tier)' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns analysis for all detected citations based on subscription tier',
    type: [Object]
  })
  async analyzeDocumentCitations(
    @Body() dto: AnalyzeDocumentCitationsDto
  ): Promise<CitationAnalysisResponse[]> {
    // This endpoint will provide different levels of analysis based on the user's subscription
    return this.citationAnalysisService.analyzeDocumentCitationsBySubscription(dto.documentText, {
      includeRelationships: dto.includeRelationships,
      includePrecedentChains: dto.includePrecedentChains,
      includeImpact: dto.includeImpact,
      maxRelationshipDepth: dto.maxRelationshipDepth,
      maxPrecedentChainLength: dto.maxPrecedentChainLength
    });
  }

  @Get('relationships/:citation')
  @RequireFeatures('basic_citation_analysis')
  @ApiOperation({ summary: 'Get relationships for a citation' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns citation relationships',
    type: Object
  })
  async getCitationRelationships(
    @Param('citation') citation: string,
    @Query('depth') depth?: number
  ): Promise<CitationAnalysisResponse | null> {
    const options: CitationAnalysisOptions = {
      includeRelationships: true,
      includePrecedentChains: false,
      includeImpact: false,
      maxRelationshipDepth: depth || 1
    };

    return this.citationAnalysisService.analyzeCitation(citation, options);
  }

  @Get('impact/:citation')
  @RequireFeatures('enhanced_citation_analysis')
  @ApiOperation({ summary: 'Get impact metrics for a citation' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns citation impact metrics',
    type: Object
  })
  async getCitationImpact(
    @Param('citation') citation: string
  ): Promise<CitationAnalysisResponse | null> {
    const options: CitationAnalysisOptions = {
      includeRelationships: false,
      includePrecedentChains: false,
      includeImpact: true
    };

    return this.citationAnalysisService.analyzeCitation(citation, options);
  }

  @Get('precedent/:citation')
  @RequireFeatures('enhanced_citation_analysis')
  @ApiOperation({ summary: 'Get precedent chains for a citation' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns citation precedent chains',
    type: Object
  })
  async getCitationPrecedent(
    @Param('citation') citation: string,
    @Query('chainLength') chainLength?: number
  ): Promise<CitationAnalysisResponse | null> {
    const options: CitationAnalysisOptions = {
      includeRelationships: false,
      includePrecedentChains: true,
      includeImpact: false,
      maxPrecedentChainLength: chainLength || 3
    };

    return this.citationAnalysisService.analyzeCitation(citation, options);
  }

  @Get('network/:citation')
  @RequireFeatures('enhanced_citation_analysis')
  @ApiOperation({ summary: 'Get citation network graph data' })
  @ApiResponse({ 
    status: 200, 
    description: 'Returns citation network graph data for visualization',
    type: Object
  })
  async getCitationNetwork(
    @Param('citation') citation: string,
    @Query('depth') depth?: number
  ) {
    return this.citationAnalysisService.generateCitationNetworkGraph(citation, depth || 2);
  }
}

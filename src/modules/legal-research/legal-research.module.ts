import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { CitationExtractionService } from './services';
import { GovInfoService } from './services/govinfo.service';
import { LegalResearchOrchestratorService } from './services/legal-research-orchestrator.service';
import {
  CitationAnalysisService,
  CitationRelationshipService,
  CitationImpactService,
  PrecedentTrackingService
} from './services/citation-analysis';
import { CitationAnalysisController } from './controllers/citation-analysis.controller';
import legalResearchConfig from './config/legal-research.config';
import { SharedModule } from '../shared/shared.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { AuthModule } from '../auth/auth.module';

@Module({
  imports: [
    ConfigModule.forFeature(legalResearchConfig),
    SharedModule,
    SubscriptionModule,
    AuthModule,
  ],
  controllers: [
    CitationAnalysisController
  ],
  providers: [
    CitationExtractionService,
    GovInfoService,
    LegalResearchOrchestratorService,
    CitationAnalysisService,
    CitationRelationshipService,
    CitationImpactService,
    PrecedentTrackingService
  ],
  exports: [
    CitationExtractionService, 
    LegalResearchOrchestratorService,
    CitationAnalysisService
  ],
})
export class LegalResearchModule {}

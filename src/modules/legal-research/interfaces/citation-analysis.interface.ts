/**
 * Enhanced citation analysis interfaces for the Legal Document Analyzer
 */
import { CitationType } from './citation.interface';

/**
 * Represents a legal citation with enhanced metadata
 */
export interface EnhancedCitation {
  id: string;
  citation: string;
  type: CitationType;
  title?: string;
  court?: string;
  year?: number;
  jurisdiction?: string;
  summary?: string;
  significance?: 'low' | 'medium' | 'high';
  courtListenerId?: string;
}

/**
 * Represents a relationship between two citations
 */
export interface CitationRelationship {
  id: string;
  sourceCitationId: string;
  targetCitationId: string;
  relationshipType: 'cites' | 'citedBy' | 'overrules' | 'overruledBy' | 'distinguishes' | 'distinguishedBy' | 'follows' | 'followedBy';
  context?: string;
  strength?: 'weak' | 'moderate' | 'strong';
  metadata?: {
    citingCase?: {
      name: string;
      citation: string;
      court?: string;
      year?: number;
      jurisdiction?: string;
    };
    citedCase?: {
      name: string;
      citation: string;
      court?: string;
      year?: number;
      jurisdiction?: string;
    };
    relationshipDescription?: string;
    citingCaseUrl?: string;
    citedCaseUrl?: string;
  };
}

/**
 * Represents a citation's impact metrics
 */
export interface CitationImpact {
  citationId: string;
  totalCitations: number;
  recentCitations: number; // Citations in the last 2 years
  influentialCitations: number; // Citations by higher courts or in important cases
  negativeReferences: number; // Number of times criticized or distinguished
  positiveReferences: number; // Number of times followed or approved
  impactScore: number; // Calculated score based on the above metrics
}

/**
 * Represents a historical precedent chain
 */
export interface PrecedentChain {
  id: string;
  rootCitationId: string;
  chain: Array<{
    citationId: string;
    relationshipToParent: 'follows' | 'distinguishes' | 'overrules' | 'modifies';
    significance: 'low' | 'medium' | 'high';
  }>;
  legalPrinciple?: string;
  evolution?: string;
}

/**
 * Response for citation analysis
 */
export interface CitationAnalysisResponse {
  citation: EnhancedCitation;
  relationships: CitationRelationship[];
  impact: CitationImpact;
  precedentChains: PrecedentChain[];
}

/**
 * Options for citation analysis
 */
export interface CitationAnalysisOptions {
  includePrecedentChains?: boolean;
  includeRelationships?: boolean;
  includeImpact?: boolean;
  maxRelationshipDepth?: number;
  maxPrecedentChainLength?: number;
}

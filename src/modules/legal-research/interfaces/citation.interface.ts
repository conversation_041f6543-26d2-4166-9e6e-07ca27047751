export interface Citation {
  id: string;
  rawCitation: string;
  normalizedCitation: string;
  type: CitationType;
  source: CitationSource;
  metadata: CitationMetadata;
  links: CitationLinks;
  confidence: number;
}

export type CitationType = 'case' | 'statute' | 'regulation';
export type CitationSource = 'CourtListener' | 'GovInfo';

export interface CitationMetadata {
  title?: string;
  date?: Date;
  court?: string;
  jurisdiction?: string;
  status?: 'active' | 'superseded' | 'repealed';
  version?: string;
}

export interface CitationLinks {
  sourceUrl: string;
  pdfUrl?: string;
  apiUrl: string;
}

export interface EnrichedDocument {
  originalText: string;
  citations: Citation[];
}
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosInstance } from 'axios';
import { LegalResearchConfig } from '../config/legal-research.config';
import NodeCache from 'node-cache';
import * as rateLimitNs from 'axios-rate-limit';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class GovInfoService {
  private readonly logger = new Logger(GovInfoService.name);
  private readonly axiosInstance: AxiosInstance;
  private readonly cache: NodeCache;
  private readonly config: LegalResearchConfig;
  private readonly FAILED_LOOKUP_TTL = 3600; // Cache failed lookups for 1 hour
  private readonly FAILED_LOOKUP_PREFIX = 'failed:';
  private readonly CONFIDENCE_THRESHOLDS = {
    EXACT_MATCH: 0.95,
    PARTIAL_MATCH: 0.8,
    FUZZY_MATCH: 0.6
  };

  constructor(private readonly configService: ConfigService) {
    this.config = this.configService.get<LegalResearchConfig>('legalResearch');
    
    // Initialize cache with appropriate TTL based on content type
    this.cache = new NodeCache({
      stdTTL: this.config.cache.statute.ttl,
      checkperiod: this.config.cache.statute.checkPeriod,
      maxKeys: this.config.cache.statute.maxKeys,
    });

    // Use the default export from the namespace import
    const rateLimitFn = rateLimitNs.default;

    // Initialize HTTP client with rate limiting
    this.axiosInstance = rateLimitFn(
      axios.create({
        baseURL: this.config.govInfo.baseUrl,
        timeout: this.config.govInfo.timeout,
        headers: {
          'X-Api-Key': this.config.govInfo.apiKey.key,
          'Content-Type': 'application/json',
        },
      }),
      { maxRPS: this.config.govInfo.apiKey.rateLimitPerHour / 3600 }
    );
  }

  /**
   * Queries the GovInfo API for statute or regulation citations.
   * @param citation The normalized citation string to query.
   * @returns A promise containing the statute/regulation details.
   */
  async queryStatute(citation: string): Promise<any> {
    try {
      // Check cache first for successful lookups
      const cacheKey = `statute:${citation}`;
      const cachedResult = this.cache.get(cacheKey);
      if (cachedResult) {
        this.logger.debug(`Cache hit for citation: ${citation}`);
        return cachedResult;
      }

      // Check if this is a known failed lookup
      const failedKey = `${this.FAILED_LOOKUP_PREFIX}${citation}`;
      if (this.cache.get(failedKey)) {
        this.logger.debug(`Skipping known failed lookup for citation: ${citation}`);
        return null;
      }

      // Parse citation to determine type and parameters
      const { type, title, section, confidence: parseConfidence } = this.parseCitation(citation);
      
      // If parsing confidence is too low, cache as failed and return null
      if (parseConfidence < this.CONFIDENCE_THRESHOLDS.FUZZY_MATCH) {
        this.logger.warn(`Low confidence parse (${parseConfidence}) for citation: ${citation}`);
        this.cache.set(failedKey, true, this.FAILED_LOOKUP_TTL);
        return null;
      }

      // Implement retry logic with exponential backoff
      let attempt = 0;
      while (attempt < this.config.retry.maxAttempts) {
        try {
          const response = await this.axiosInstance.get(`/${type}/${title}/${section}`, {
            params: {
              format: 'json',
            },
          });

          if (response.data) {
            const result = response.data;
            
            // Calculate confidence score based on match quality
            const matchConfidence = this.calculateMatchConfidence(
              citation,
              result,
              parseConfidence
            );

            const content = {
              id: uuidv4(),
              rawCitation: citation,
              normalizedCitation: this.normalizeStatuteCitation(type, title, section),
              type: 'statute',
              source: 'GovInfo',
              metadata: {
                title: result.heading || result.title || this.normalizeStatuteCitation(type, title, section),
                date: result.lastModified || result.dateModified,
                status: this.determineStatus(result),
                version: result.version || result.edition,
                authority: this.getAuthority(type),
                // Add additional metadata
                publicLawNumber: result.publicLawNumber,
                statuteAtLarge: result.statuteAtLarge,
                amendments: result.amendments || []
              },
              links: {
                sourceUrl: result.url || `${this.config.govInfo.baseUrl}/${type}/${title}/${section}`,
                pdfUrl: result.pdfUrl || '',
                apiUrl: result.api || `${this.config.govInfo.baseUrl}/${type}/${title}/${section}`
              },
              confidence: matchConfidence
            };

            // Only cache results with sufficient confidence
            if (matchConfidence >= this.CONFIDENCE_THRESHOLDS.FUZZY_MATCH) {
              const ttl = type === 'uscode' ? 
                this.config.cache.statute.ttl : 
                this.config.cache.regulation.ttl;
              
              this.cache.set(cacheKey, content, ttl);
            } else {
              this.logger.warn(
                `Low confidence match (${matchConfidence}) for citation: ${citation}`,
              );
              this.cache.set(failedKey, true, this.FAILED_LOOKUP_TTL);
            }

            return content;
          }

          // No data found - cache as failed lookup
          this.cache.set(failedKey, true, this.FAILED_LOOKUP_TTL);
          throw new Error('No matching statute/regulation found');
        } catch (error) {
          attempt++;
          
          if (error.response) {
            switch (error.response.status) {
              case 401:
              case 403:
                this.logger.error('API authentication error', error);
                throw error; // Don't retry auth errors
              case 429:
                // Rate limit exceeded - wait longer
                await this.sleep(this.calculateBackoff(attempt, true));
                continue;
              case 404:
                // Cache the 404 to prevent repeated lookups
                this.cache.set(failedKey, true, this.FAILED_LOOKUP_TTL);
                throw new Error(`Citation not found: ${citation}`);
              case 500:
              case 502:
              case 503:
              case 504:
                // Server errors - retry with backoff
                if (attempt < this.config.retry.maxAttempts) {
                  await this.sleep(this.calculateBackoff(attempt));
                  continue;
                }
                break;
            }
          }

          if (attempt === this.config.retry.maxAttempts) {
            // Cache the failed lookup after max retries
            this.cache.set(failedKey, true, this.FAILED_LOOKUP_TTL);
            throw error;
          }

          await this.sleep(this.calculateBackoff(attempt));
        }
      }

      throw new Error(`Failed to query citation after ${this.config.retry.maxAttempts} attempts`);
    } catch (error) {
      const failedKey = `${this.FAILED_LOOKUP_PREFIX}${citation}`;
      this.logger.error(`Error querying citation: ${citation}`, error);
      // Cache failed lookup
      this.cache.set(failedKey, true, this.FAILED_LOOKUP_TTL);
      return null;
    }
  }

  private parseCitation(citation: string): { type: string; title: string; section: string; confidence: number } {
    // Parse USC citations (e.g., "42 U.S.C. § 1983")
    const uscMatch = citation.match(/(\d+)\s+U\.?S\.?C\.?\s+[§\s]?\s*(\d[\w\d\-\.]*)/i);
    if (uscMatch) {
      return {
        type: 'uscode',
        title: uscMatch[1],
        section: uscMatch[2],
        confidence: this.calculateParseConfidence(uscMatch[0], citation)
      };
    }

    // Parse CFR citations (e.g., "40 CFR § 1500.1")
    const cfrMatch = citation.match(/(\d+)\s+C\.?F\.?R\.?\s+[§\s]?\s*(\d[\w\d\-\.]*)/i);
    if (cfrMatch) {
      return {
        type: 'cfr',
        title: cfrMatch[1],
        section: cfrMatch[2],
        confidence: this.calculateParseConfidence(cfrMatch[0], citation)
      };
    }

    // Parse Public Law citations (e.g., "Pub. L. 116-9")
    const pubLawMatch = citation.match(/Pub\.?\s*L\.?\s*(?:No\.?)?\s*(\d+)-(\d+)/i);
    if (pubLawMatch) {
      return {
        type: 'publiclaw',
        title: pubLawMatch[1],
        section: pubLawMatch[2],
        confidence: this.calculateParseConfidence(pubLawMatch[0], citation)
      };
    }

    // Parse Statutes at Large citations (e.g., "73 Stat. 4")
    const statMatch = citation.match(/(\d+)\s+Stat\.?\s+(\d+)/i);
    if (statMatch) {
      return {
        type: 'statute',
        title: statMatch[1],
        section: statMatch[2],
        confidence: this.calculateParseConfidence(statMatch[0], citation)
      };
    }

    throw new Error(`Unable to parse citation: ${citation}`);
  }

  private calculateParseConfidence(match: string, original: string): number {
    // Calculate how much of the original citation was matched
    const matchLength = match.length;
    const originalLength = original.length;
    
    // Perfect match
    if (matchLength === originalLength) {
      return this.CONFIDENCE_THRESHOLDS.EXACT_MATCH;
    }
    
    // Calculate ratio of matched text
    const ratio = matchLength / originalLength;
    if (ratio >= 0.8) {
      return this.CONFIDENCE_THRESHOLDS.PARTIAL_MATCH;
    }
    
    return this.CONFIDENCE_THRESHOLDS.FUZZY_MATCH;
  }

  private calculateMatchConfidence(citation: string, result: any, parseConfidence: number): number {
    let confidence = parseConfidence;

    // Boost confidence if we have good metadata
    if (result.heading || result.title) confidence += 0.1;
    if (result.lastModified || result.dateModified) confidence += 0.05;
    if (result.version || result.edition) confidence += 0.05;

    // Reduce confidence for certain conditions
    if (result.status === 'repealed') confidence -= 0.1;
    if (result.status === 'superseded') confidence -= 0.05;

    // Ensure confidence stays within bounds
    return Math.min(Math.max(confidence, 0), 1);
  }

  private normalizeStatuteCitation(type: string, title: string, section: string): string {
    switch (type) {
      case 'uscode':
        return `${title} U.S.C. § ${section}`;
      case 'cfr':
        return `${title} C.F.R. § ${section}`;
      case 'publiclaw':
        return `Pub. L. ${title}-${section}`;
      case 'statute':
        return `${title} Stat. ${section}`;
      default:
        return `${title} ${type} ${section}`;
    }
  }

  private getAuthority(type: string): string {
    switch (type) {
      case 'uscode':
        return 'United States Code';
      case 'cfr':
        return 'Code of Federal Regulations';
      case 'publiclaw':
        return 'Public Law';
      case 'statute':
        return 'United States Statutes at Large';
      default:
        return 'Unknown';
    }
  }

  private determineStatus(result: any): 'active' | 'superseded' | 'repealed' {
    if (result.repealed || result.status === 'repealed') {
      return 'repealed';
    }
    if (result.superseded || result.status === 'superseded') {
      return 'superseded';
    }
    return 'active';
  }

  private calculateBackoff(attempt: number, isRateLimit: boolean = false): number {
    const baseDelay = isRateLimit ? 
      this.config.retry.baseDelay * 2 : 
      this.config.retry.baseDelay;

    const delay = Math.min(
      baseDelay * Math.pow(this.config.retry.exponentialBase, attempt),
      this.config.retry.maxDelay
    );

    return delay;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
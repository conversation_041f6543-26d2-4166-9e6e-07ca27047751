import { Test, TestingModule } from '@nestjs/testing';
import { LegalResearchOrchestratorService } from './legal-research-orchestrator.service';
import { CitationExtractionService } from './citation-extraction.service';
import { CourtListenerService } from '../../shared/court-listener/court-listener.service';
import { GovInfoService } from './govinfo.service';
import { AIAnalysisResult } from '../../ai/interfaces/ai-analysis-result.interface';

describe('LegalResearchOrchestratorService', () => {
  let service: LegalResearchOrchestratorService;
  let citationExtractor: CitationExtractionService;
  let courtListener: CourtListenerService;
  let govInfo: GovInfoService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LegalResearchOrchestratorService,
        {
          provide: CitationExtractionService,
          useValue: {
            detectCitations: jest.fn().mockReturnValue([
              { citation: '410 U.S. 113', type: 'case' },
            ]),
            normalizeCitation: jest.fn().mockImplementation((c) => c),
          },
        },
        {
          provide: CourtListenerService,
          useValue: {
            lookupCitation: jest.fn().mockResolvedValue({
              results: [
                {
                  case_name: 'Roe v. Wade',
                  date_filed: '1973-01-22',
                  court: 'Supreme Court',
                  jurisdiction: 'Federal',
                  status: 'Published',
                  absolute_url: 'https://www.courtlistener.com/opinion/108768/roe-v-wade/',
                  pdf_url: 'https://www.courtlistener.com/pdf/108768/roe-v-wade.pdf',
                  resource_uri: '/api/rest/v4/opinions/108768/',
                },
              ],
            }),
          },
        },
        {
          provide: GovInfoService,
          useValue: {
            queryStatute: jest.fn().mockResolvedValue({}),
          },
        },
      ],
    }).compile();

    service = module.get<LegalResearchOrchestratorService>(LegalResearchOrchestratorService);
    citationExtractor = module.get<CitationExtractionService>(CitationExtractionService);
    courtListener = module.get<CourtListenerService>(CourtListenerService);
    govInfo = module.get<GovInfoService>(GovInfoService);
  });

  it('should enrich a document with legal citations from CourtListener', async () => {
    const dummyAnalysis: AIAnalysisResult = {
      content: {
        text: 'The landmark case 410 U.S. 113 established important precedent.',
      },
      metadata: {
        provider: 'test',
        modelUsed: 'test-model',
      },
    };

    const result = await service.enrichDocument(dummyAnalysis);

    expect(result.metadata.legalCitations).toBeDefined();
    expect(result.metadata.legalCitations.length).toBe(1);
    expect(result.metadata.legalCitations[0].metadata.title).toBe('Roe v. Wade');
    expect(result.metadata.legalCitations[0].links.sourceUrl).toContain('roe-v-wade');
    // Print the enriched result for visual inspection
    console.log(JSON.stringify(result.metadata.legalCitations, null, 2));
  });
});

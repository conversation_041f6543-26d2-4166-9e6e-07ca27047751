import { Injectable, Logger } from '@nestjs/common';
import { CitationExtractionService } from './citation-extraction.service';
import { CourtListenerService } from '../../shared/court-listener/court-listener.service';
import { GovInfoService } from './govinfo.service';
import { Citation, CitationSource } from '../interfaces/citation.interface';
import { v4 as uuidv4 } from 'uuid';
import { AIAnalysisResult } from '../../ai/interfaces/ai-analysis-result.interface';

@Injectable()
export class LegalResearchOrchestratorService {
  private readonly logger = new Logger(LegalResearchOrchestratorService.name);

  constructor(
    private readonly citationExtractor: CitationExtractionService,
    private readonly courtListener: CourtListenerService,
    private readonly govInfo: GovInfoService,
  ) {}

  /**
   * Processes a document's AI analysis result by extracting citations from its original content
   * and enriching them with metadata, adding the results back to the analysis metadata.
   * @param analysisResult The AI analysis result object containing content and metadata.
   * @param originalDocumentContent The original content of the document.
   * @returns Promise containing the enriched AI analysis result object.
   */
  async enrichDocument(
    analysisResult: AIAnalysisResult,
    originalDocumentContent: string,
  ): Promise<AIAnalysisResult> {
    try {
      // Extract text from the original content, not the AI result
      const textToAnalyze = originalDocumentContent;

      // Extract citations
      const detectedCitations =
        this.citationExtractor.detectCitations(textToAnalyze);

      if (!detectedCitations || detectedCitations.length === 0) {
        this.logger.log(`No citations detected in original document content.`);
        // Return the original result if no citations found
        return analysisResult;
      }

      this.logger.log(
        `Detected ${detectedCitations.length} potential citations. Enriching...`,
      );

      // Process each citation
      const citationPromises = detectedCitations.map(async ({ citation, type }) => {
        try {
          const normalizedCitation = this.citationExtractor.normalizeCitation(
            citation,
            type,
          );

          // Query appropriate service based on citation type
          let metadata;
          let source: CitationSource;

          if (type === 'case') {
            // Use the robust lookupCitation method (returns {results: CourtListenerCitationResult[]})
            const lookupResult = await this.courtListener.lookupCitation(
              normalizedCitation,
            );
            source = 'CourtListener';
            if (
              lookupResult && // Check if lookupResult is not null
              lookupResult.length > 0 // Check if the array is not empty
            ) {
              const firstLookupResult = lookupResult[0]; // Access the first result item
              this.logger.debug(`CourtListener lookup status for ${citation}: ${firstLookupResult.status}`);

              // Check if status is OK (200) or Multiple Choices (300) and clusters exist
              if (
                (firstLookupResult.status === 200 || firstLookupResult.status === 300) &&
                firstLookupResult.clusters &&
                firstLookupResult.clusters.length > 0
              ) {
                const firstCluster = firstLookupResult.clusters[0]; // Access the first cluster
                metadata = {
                  title: firstCluster.case_name || '',
                  date: firstCluster.date_filed || '',
                  status: firstCluster.precedential_status || '',
                  version: '',
                  // Add other relevant fields from CourtListenerCluster if needed
                };
                return {
                  id: uuidv4(),
                  rawCitation: citation, // Use the originally extracted citation text
                  normalizedCitation,
                  type,
                  source,
                  metadata,
                  links: {
                    sourceUrl: firstCluster.absolute_url || '',
                    pdfUrl: '', // No direct PDF URL in cluster, maybe derive later?
                    apiUrl: firstCluster.resource_uri || '',
                  },
                  confidence: 0.95, // TODO: Implement confidence scoring
                };
              } else {
                this.logger.warn(
                  `CourtListener lookup for ${citation} failed or returned no clusters. Status: ${firstLookupResult.status}, Error: ${firstLookupResult.error_message}`,
                );
              }
            } else {
              this.logger.warn(`CourtListener lookup returned null or empty result for ${citation}`);
            }
          } else {
            metadata = await this.govInfo.queryStatute(normalizedCitation);
            source = 'GovInfo';
            return {
              id: uuidv4(),
              rawCitation: citation,
              normalizedCitation,
              type,
              source,
              metadata: {
                title: metadata.title,
                date: metadata.date_filed || metadata.lastUpdated,
                court: metadata.court,
                jurisdiction: metadata.jurisdiction,
                status: metadata.status,
                version: metadata.version,
              },
              links: {
                sourceUrl: metadata.url,
                pdfUrl: metadata.pdfUrl,
                apiUrl: metadata.apiUrl,
              },
              confidence: 0.95, // TODO: Implement confidence scoring
            };
          }
        } catch (error) {
          this.logger.error(`Error processing citation ${citation}:`, error);
          return null; // Explicitly return null on error
        }
      });

      const resolvedCitations = await Promise.all(citationPromises);

      // Filter out nulls (errors during individual enrichment)
      const successfullyEnrichedCitations = resolvedCitations.filter(
        (c) => c !== null,
      ) as Citation[]; // Type assertion after filtering

      this.logger.log(
        `Successfully enriched ${successfullyEnrichedCitations.length} out of ${detectedCitations.length} detected citations.`,
      );

      // Ensure metadata object exists
      if (!analysisResult.metadata) {
        // If metadata is missing entirely, we might have a problem upstream
        // or need to define a default structure. For now, let's initialize it
        // but log a warning. This depends on whether AIProvider always sets it.
        this.logger.warn(
          'AnalysisResult was missing the metadata object during enrichment. Initializing.',
        );
        // Provide a minimal default structure based on AIAnalysisMetadata
        analysisResult.metadata = {
          provider: analysisResult.metadata?.provider || 'unknown', // Try to preserve provider if possible
          modelUsed: analysisResult.metadata?.modelUsed || 'unknown', // Try to preserve model if possible
          // Initialize other required fields if necessary
        };
      }

      // Assign the enriched citations to the analysis result's metadata
      analysisResult.metadata.legalCitations = successfullyEnrichedCitations;

      // Return the modified analysisResult
      return analysisResult;
    } catch (error) {
      this.logger.error('Error during citation enrichment process:', error);
      // In case of error, return the original analysisResult without enrichment
      // Optionally add an error flag/message to metadata here
      if (analysisResult.metadata) {
        analysisResult.metadata[
          'enrichmentError'
        ] = `Failed to enrich citations: ${error.message}`;
      }
      return analysisResult;
    }
  }
}

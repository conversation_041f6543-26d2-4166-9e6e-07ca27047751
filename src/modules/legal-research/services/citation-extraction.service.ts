import { Injectable, Logger } from '@nestjs/common';
import { CitationType } from '../interfaces/citation.interface'; // Adjusted import path
import Fuse, { IFuseOptions } from 'fuse.js'; // Revert to combined default/named import

@Injectable()
export class CitationExtractionService {
  private readonly logger = new Logger(CitationExtractionService.name);

  // --- Fuse.js Config ---
  private readonly fuseThreshold = 0.4; // Threshold for fuzzy matching

  // --- Lookup Table for Common Variations ---
  private readonly VARIATION_LOOKUP: { [key: string]: string } = {
    // Reporters (Examples)
    'Fed\. Supp\.': 'F.Supp.',
    'Fed\. Rptr\.': 'F.',
    'Supreme Court Reporter': 'S.Ct.',
    'United States Reports': 'U.S.',
    'Lawyers\' Edition': 'L.Ed.',
    // Statutes (Examples)
    'US Code': 'U.S.C.',
    'United States Code': 'U.S.C.',
    'Public Law': 'Pub. L. No.',
    // Regulations (Examples)
    'Code of Federal Regulations': 'C.F.R.',
    'Code Fed Regs': 'C.F.R.',
    'Federal Register': 'Fed. Reg.',
    // Section Variations
    'Section': '§',
    'Sec\.': '§',
    'Para\.': '¶', // Add paragraph symbol if needed
    // Add more common variations here
  };

  // --- Standard Lists for Fuzzy Matching ---
  private readonly STANDARD_REPORTERS = [
    // Federal
    'S.Ct.',
    'U.S.',
    'L.Ed.',
    'L.Ed.2d',
    'F.',
    'F.2d',
    'F.3d',
    'F.4th', // Added F.4th just in case
    'F.Appx',
    'F.Supp.',
    'F.Supp.2d',
    'F.Supp.3d',
    'F.R.D.',
    'B.R.',
    // Regional (Examples - can be expanded significantly)
    'A.',
    'A.2d',
    'A.3d',
    'N.E.',
    'N.E.2d',
    'N.W.',
    'N.W.2d',
    'P.',
    'P.2d',
    'P.3d',
    'S.E.',
    'S.E.2d',
    'So.',
    'So.2d',
    'So.3d',
    'S.W.',
    'S.W.2d',
    'S.W.3d',
    // State (Examples)
    'Cal.Rptr.',
    'Cal.Rptr.2d',
    'Cal.Rptr.3d',
    'N.Y.S.',
    'N.Y.S.2d',
    // Add more as needed
  ];

  private readonly STANDARD_STATUTE_IDS = [
    'U.S.C.', // United States Code
    'Pub. L. No.', // Public Law Number
    // Add state statute identifiers if needed, e.g., 'Cal. Penal Code'
  ];

  private readonly STANDARD_REGULATION_IDS = [
    'C.F.R.', // Code of Federal Regulations
    'Fed. Reg.', // Federal Register
    // Add state regulation identifiers if needed, e.g., 'Cal. Code Regs.'
  ];

  // --- Fuse Instances ---
  private fuseCaseReporters: Fuse<string>;
  private fuseStatuteIds: Fuse<string>;
  private fuseRegulationIds: Fuse<string>;

  // Citation patterns based on common legal citation formats
  // Focused on Volume Reporter Page structure primarily
  private readonly PATTERNS = {
    case: [
      // Supreme Court Reporter (S.Ct.)
      /(\d+)\s+S\.?\s*Ct\.?\s+(\d+)/gi,
      // Federal Reporter (F., F.2d, F.3d)
      /(\d+)\s+F\.(?:2d|3d)?\s+(\d+)/gi,
      // Federal Supplement (F.Supp., F.Supp.2d)
      /(\d+)\s+F\.?\s*Supp\.?\s*(?:2d)?\s+(\d+)/gi,
      // United States Reports (U.S.)
      /(\d+)\s+U\.?\s*S\.?\s+(\d+)/gi,
      // Atlantic Reporter (A., A.2d, A.3d)
      /(\d+)\s+A\.(?:2d|3d)?\s+(\d+)/gi,
      // North Eastern Reporter (N.E., N.E.2d)
      /(\d+)\s+N\.?\s*E\.(?:2d|3d)?\s+(\d+)/gi,
      // Pacific Reporter (P., P.2d, P.3d)
      /(\d+)\s+P\.(?:2d|3d)?\s+(\d+)/gi,
      // TODO: Add more regional/state reporters (So.2d, S.W.2d, N.W.2d, Cal.Rptr.3d, N.Y.S.2d, etc.)
    ],
    statute: [
      // United States Code (U.S.C.)
      /(\d+)\s+U\.?\s*S\.?\s*C\.?\s+[§\s]?\s*(\d[\w\d\-\.]*)/gi,
      // Public Law (Pub. L. No.)
      /Pub\.?\s*L\.?\s*(?:No\.\s*)?(\d+)-(\d+)/gi,
      // TODO: Add state statute patterns (highly variable, e.g., Cal. Penal Code § 187)
    ],
    regulation: [
      // Code of Federal Regulations (CFR)
      /(\d+)\s+C\.?\s*F\.?\s*R\.?\s+[§\s]?\s*(\d[\w\d\-\.]*)/gi,
      // Federal Register (Fed. Reg.)
      /(\d+)\s+Fed\.?\s*Reg\.?\s+(\d+)/gi,
      // TODO: Add state regulation patterns (e.g., Cal. Code Regs.)
    ],
    // TODO: Add patterns for other types like rules (Fed. R. Civ. P.), treaties, constitutions, etc.
  };

  constructor() {
    // Use the imported IFuseOptions type directly
    const fuseOptions: IFuseOptions<string> = {
      includeScore: true,
      threshold: this.fuseThreshold, // Adjust threshold as needed (0 = perfect match, 1 = match anything)
      ignoreLocation: true, // Search whole string
      // Add other options like minMatchCharLength if useful
    };

    // Revert to checking .default or the direct import
    const FuseConstructor = (Fuse as any).default || Fuse;

    this.fuseCaseReporters = new FuseConstructor(this.STANDARD_REPORTERS, fuseOptions);
    this.fuseStatuteIds = new FuseConstructor(this.STANDARD_STATUTE_IDS, fuseOptions);
    this.fuseRegulationIds = new FuseConstructor(
      this.STANDARD_REGULATION_IDS,
      fuseOptions,
    );
  }

  /**
   * Identifies potential legal citations within a given text.
   * @param text The input text to analyze.
   * @returns An array of detected citation strings with their types.
   */
  detectCitations(
    text: string,
  ): Array<{ citation: string; type: CitationType }> {
    try {
      const detectedCitations: Array<{ citation: string; type: CitationType }> =
        [];

      // Search for each type of citation
      Object.entries(this.PATTERNS).forEach(([type, patterns]) => {
        patterns.forEach((pattern) => {
          // Reset lastIndex for global regex patterns in loops
          pattern.lastIndex = 0;
          const matches = text.matchAll(pattern);
          for (const match of matches) {
            if (match[0]) {
              // Basic deduplication and sanity check (can be improved)
              const potentialCitation = match[0].trim().replace(/\s+/g, ' '); // Normalize spaces early
              if (
                !detectedCitations.some((c) => c.citation === potentialCitation)
              ) {
                detectedCitations.push({
                  citation: potentialCitation,
                  type: type as CitationType,
                });
              }
            }
          }
        });
      });

      this.logger.debug(
        `Detected ${detectedCitations.length} potential citations`,
      );
      // TODO: Implement fallback strategies (fuzzy matching, context clues) from docs Sec 3.3
      return detectedCitations;
    } catch (error) {
      this.logger.error('Error detecting citations:', error);
      throw new Error('Failed to detect citations: ' + error.message);
    }
  }

  /**
   * Normalizes a given legal citation according to defined rules (Sec 3.2 in docs).
   * @param citation The raw citation string.
   * @param type The type of citation.
   * @returns The normalized citation string.
   */
  normalizeCitation(citation: string, type: CitationType): string {
    let normalized = citation.trim().replace(/\s+/g, ' '); // Initial trim and space normalization
    const originalNormalized = normalized; // Keep track for logging

    // --- 1. Apply Lookup Table Variations First ---
    for (const variation in this.VARIATION_LOOKUP) {
      const standardForm = this.VARIATION_LOOKUP[variation];
      const regex = new RegExp(`\b${variation}\b`, 'gi'); // Match whole words, case-insensitive
      const newNormalized = normalized.replace(regex, standardForm);
      if (newNormalized !== normalized) {
        this.logger.debug(`Applied lookup table: '${variation}' -> '${standardForm}'`);
        normalized = newNormalized;
      }
    }

    // --- 2. Apply Regex-Based Normalization and Fuzzy Matching Fallback ---
    switch (type) {
      case 'case':
        // Regex Normalization (First Pass)
        normalized = normalized.replace(/S\.\s*Ct\./gi, 'S.Ct.');
        normalized = normalized.replace(/F\.\s*(2d|3d|4th)/gi, 'F.$1'); // Keep 2d/3d/4th
        normalized = normalized.replace(
          /F\.\s*Supp\.\s*(2d|3d)/gi,
          'F.Supp.$1',
        );
        normalized = normalized.replace(/F\.\s*Supp\./gi, 'F.Supp.');
        normalized = normalized.replace(/U\.\s*S\./gi, 'U.S.');
        normalized = normalized.replace(/L\.\s*Ed\.\s*(2d)?/gi, 'L.Ed.$1');
        normalized = normalized.replace(/A\.\s*(2d|3d)/gi, 'A.$1');
        normalized = normalized.replace(/N\.\s*E\.\s*(2d)?/gi, 'N.E.$1');
        normalized = normalized.replace(/N\.\s*W\.\s*(2d)?/gi, 'N.W.$1');
        normalized = normalized.replace(/P\.\s*(2d|3d)/gi, 'P.$1');
        normalized = normalized.replace(/S\.\s*E\.\s*(2d)?/gi, 'S.E.$1');
        normalized = normalized.replace(/So\.\s*(2d|3d)?/gi, 'So.$1');
        normalized = normalized.replace(/S\.\s*W\.\s*(2d|3d)?/gi, 'S.W.$1');

        // Ensure single space separation: Volume Reporter Page
        normalized = normalized.replace(/(\d+)\s+([A-Za-z\.])/g, '$1 $2'); // Space after volume
        normalized = normalized.replace(/([A-Za-z\.]+)\s+(\d+)/g, '$1 $2'); // Space before page number

        // Remove page reference prefixes like 'at'
        normalized = normalized.replace(/\s+at\s+(\d+)$/gi, ' $1');

        // --- Fuzzy Matching Fallback for Reporter ---
        // Try to extract Volume Reporter Page structure
        const reporterMatch = normalized.match(
          /(\d+)\s+([A-Za-z\.\s\d]+?)\s+(\d+.*)/,
        );
        if (reporterMatch && reporterMatch[2]) {
          const extractedReporter = reporterMatch[2]
            .trim()
            .replace(/\s+/g, ' '); // Cleaned extracted part
          const extractedReporterWithoutPeriod = extractedReporter.replace(/\.$/, '');
          // Check if it's already standard
          if (!this.STANDARD_REPORTERS.includes(extractedReporterWithoutPeriod)) {
            const fuseResult = this.fuseCaseReporters.search(extractedReporterWithoutPeriod);
            if (
              fuseResult.length > 0 &&
              fuseResult[0].score != null &&
              fuseResult[0].score <= this.fuseThreshold
            ) {
              const standardReporter = fuseResult[0].item;
              normalized = normalized.replace(
                extractedReporter,
                standardReporter,
              );
              this.logger.debug(
                `Fuzzy matched case reporter: '${extractedReporter}' -> '${standardReporter}' in citation '${citation.trim()}'`,
              );
            } else {
              this.logger.warn(
                `Could not fuzzy match reporter '${extractedReporter}' in citation '${citation.trim()}'`,
              );
            }
          }
        }
        break;

      case 'statute':
        // Standardize USC references
        normalized = normalized.replace(/U\.?\s*S\.?\s*C\.?/gi, ' U.S.C. ');
        // Standardize Public Law
        normalized = normalized.replace(
          /Pub\.?\s*L\.?\s*(?:No\.\s*)?/gi,
          ' Pub. L. No. ',
        );
        normalized = normalized.replace(/(\d+)\s*-\s*(\d+)/g, '$1-$2'); // Ensure hyphen
        // Normalize section symbols and spacing -> ' § '
        normalized = normalized.replace(/\s*(?:Section|Sec\.|§)\s*/gi, ' § ');

        // --- Fuzzy Matching Fallback for Statute ID ---
        const statuteIdMatch = normalized.match(
          /(\d+\s+)?([A-Za-z\.\s]+?)(\s+§?\s*\d+.*)/i,
        );
        if (statuteIdMatch && statuteIdMatch[2]) {
          const extractedId = statuteIdMatch[2].trim().replace(/\s+/g, ' ');
          if (!this.STANDARD_STATUTE_IDS.includes(extractedId)) {
            const fuseResult = this.fuseStatuteIds.search(extractedId);
            if (
              fuseResult.length > 0 &&
              fuseResult[0].score != null &&
              fuseResult[0].score <= this.fuseThreshold
            ) {
              const standardId = fuseResult[0].item;
              normalized = normalized.replace(extractedId, standardId);
              this.logger.debug(
                `Fuzzy matched statute ID: '${extractedId}' -> '${standardId}' in citation '${citation.trim()}'`,
              );
            } else {
              this.logger.warn(
                `Could not fuzzy match statute ID '${extractedId}' in citation '${citation.trim()}'`,
              );
            }
          }
        }
        break;

      case 'regulation':
        // Standardize CFR format
        normalized = normalized.replace(/C\.?\s*F\.?\s*R\.?/gi, ' C.F.R. ');
        // Standardize Federal Register
        normalized = normalized.replace(/Fed\.?\s*Reg\.?/gi, ' Fed. Reg. ');
        // Normalize section symbols and spacing -> ' § '
        normalized = normalized.replace(/\s*(?:Section|Sec\.|§)\s*/gi, ' § ');

        // --- Fuzzy Matching Fallback for Regulation ID ---
        const regIdMatch = normalized.match(
          /(\d+\s+)?([A-Za-z\.\s]+?)(\s+§?\s*\d+.*)/i,
        );
        if (regIdMatch && regIdMatch[2]) {
          const extractedId = regIdMatch[2].trim().replace(/\s+/g, ' ');
          if (!this.STANDARD_REGULATION_IDS.includes(extractedId)) {
            const fuseResult = this.fuseRegulationIds.search(extractedId);
            if (
              fuseResult.length > 0 &&
              fuseResult[0].score != null &&
              fuseResult[0].score <= this.fuseThreshold
            ) {
              const standardId = fuseResult[0].item;
              normalized = normalized.replace(extractedId, standardId);
              this.logger.debug(
                `Fuzzy matched regulation ID: '${extractedId}' -> '${standardId}' in citation '${citation.trim()}'`,
              );
            } else {
              this.logger.warn(
                `Could not fuzzy match regulation ID '${extractedId}' in citation '${citation.trim()}'`,
              );
            }
          }
        }
        break;

      default:
        // For unknown or unhandled types, return the space-normalized version
        this.logger.warn(`Normalization not implemented for type: ${type}`);
        // No fuzzy matching for unknown types
        break; // Apply final cleanup below
    }

    // --- 3. Final Cleanup ---
    normalized = normalized.trim().replace(/\s{2,}/g, ' '); // Collapse multiple spaces

    if (originalNormalized.trim().replace(/\s+/g, ' ') !== normalized) {
      this.logger.debug(
        `Normalized citation: ${citation.trim()} -> ${normalized}`,
      );
    }
    // TODO: Implement fallback strategies (fuzzy matching, lookup tables) from docs Sec 3.3
    return normalized;
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TenantContextService } from '../../../auth/services/tenant-context.service';
import { 
  EnhancedCitation, 
  CitationRelationship,
  CitationImpact,
  PrecedentChain,
  CitationAnalysisResponse,
  CitationAnalysisOptions
} from '../../interfaces/citation-analysis.interface';
import { CitationType } from '../../interfaces/citation.interface';
import { CitationExtractionService } from '../citation-extraction.service';
import { CitationRelationshipService } from './citation-relationship.service';
import { CitationImpactService } from './citation-impact.service';
import { PrecedentTrackingService } from './precedent-tracking.service';
import { SubscriptionService } from '../../../subscription/services/subscription.service';
import { SubscriptionTier } from '../../../subscription/enums/subscription-tier.enum';

/**
 * Service for enhanced citation analysis
 */
@Injectable()
export class CitationAnalysisService {
  private readonly logger = new Logger(CitationAnalysisService.name);
  constructor(
    private readonly configService: ConfigService,
    private readonly tenantContext: TenantContextService, // Inject concrete service
    private readonly citationExtractionService: CitationExtractionService,
    private readonly relationshipService: CitationRelationshipService,
    private readonly impactService: CitationImpactService,
    private readonly precedentService: PrecedentTrackingService,
    private readonly subscriptionService: SubscriptionService
  ) {}

  /**
   * Performs enhanced analysis on a citation
   * @param citationText The citation text to analyze
   * @param options Analysis options
   * @returns Enhanced citation analysis
   */
  async analyzeCitation(
    citationText: string,
    options: CitationAnalysisOptions = {}
  ): Promise<CitationAnalysisResponse | null> {
    try {
      this.logger.log(`Analyzing citation: ${citationText}`);
      
      // Extract and normalize the citation
      const extractedCitations = this.citationExtractionService.detectCitations(citationText);
      
      if (extractedCitations.length === 0) {
        this.logger.warn(`No citations detected in text: ${citationText}`);
        return null;
      }
      
      // Use the first detected citation
      const detectedCitation = extractedCitations[0];
      
      // Normalize the citation
      const normalizedCitation = this.citationExtractionService.normalizeCitation(
        detectedCitation.citation,
        detectedCitation.type
      );
      
      // Create enhanced citation object
      const citation: EnhancedCitation = {
        id: this.generateCitationId(normalizedCitation),
        citation: normalizedCitation,
        type: detectedCitation.type
      };
      
      // Default options
      const analysisOptions: CitationAnalysisOptions = {
        includeRelationships: options.includeRelationships ?? true,
        includePrecedentChains: options.includePrecedentChains ?? true,
        includeImpact: options.includeImpact ?? true,
        maxRelationshipDepth: options.maxRelationshipDepth ?? 1,
        maxPrecedentChainLength: options.maxPrecedentChainLength ?? 3
      };
      
      // Get citation relationships if requested
      let relationships: CitationRelationship[] = [];
      if (analysisOptions.includeRelationships) {
        relationships = await this.relationshipService.mapCitationRelationships(
          citation,
          { maxRelationshipDepth: analysisOptions.maxRelationshipDepth }
        );
      }
      
      // Get citation impact if requested
      let impact: CitationImpact = {
        citationId: citation.id,
        totalCitations: 0,
        recentCitations: 0,
        influentialCitations: 0,
        negativeReferences: 0,
        positiveReferences: 0,
        impactScore: 0
      };
      
      if (analysisOptions.includeImpact) {
        // Calculate impact directly from relationships if available
        if (relationships && relationships.length > 0) {
          // Count cited-by relationships
          const citedByCount = relationships.filter(r => r.relationshipType === 'citedBy').length;
          
          // Count influential citations (strong relationships)
          const influentialCount = relationships.filter(
            r => r.relationshipType === 'citedBy' && r.strength === 'strong'
          ).length;
          
          // Count positive and negative references
          const positiveCount = relationships.filter(
            r => r.relationshipType === 'citedBy' && (r.strength === 'strong' || r.strength === 'moderate')
          ).length;
          
          const negativeCount = relationships.filter(
            r => r.relationshipType === 'citedBy' && r.strength === 'weak'
          ).length;
          
          // Calculate impact score
          const impactScore = 
            citedByCount * 1 + 
            influentialCount * 2 + 
            positiveCount * 1.5 - 
            negativeCount * 0.5;
          
          impact = {
            citationId: citation.id,
            totalCitations: citedByCount,
            recentCitations: Math.floor(citedByCount * 0.3), // Estimate 30% as recent
            influentialCitations: influentialCount,
            negativeReferences: negativeCount,
            positiveReferences: positiveCount,
            impactScore: Math.max(0, impactScore)
          };
        } else {
          // If no relationships, use the impact service
          impact = await this.impactService.calculateCitationImpact(
            citation,
            relationships
          );
        }
      }
      
      // Get precedent chains if requested
      let precedentChains: PrecedentChain[] = [];
      if (analysisOptions.includePrecedentChains) {
        precedentChains = await this.relationshipService.buildPrecedentChains(citation, relationships);
      }
      
      return {
        citation,
        relationships,
        impact,
        precedentChains
      };
    } catch (error) {
      this.logger.error(`Error analyzing citation: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Analyzes a citation based on the user's subscription tier
   * @param citationText The citation text to analyze
   * @param options Analysis options
   * @returns Enhanced citation analysis based on subscription tier
   */
  async analyzeCitationBySubscription(
    citationText: string,
    options: CitationAnalysisOptions = {}
  ): Promise<CitationAnalysisResponse | null> {
    try {
      const organizationId = this.tenantContext.getCurrentOrganization();
      if (!organizationId) {
        throw new Error('Organization context is required');
      }
      const subscription = await this.subscriptionService.getSubscription(organizationId);
      const tier = subscription?.tier || SubscriptionTier.LAW_STUDENT;

      // Determine analysis level based on subscription tier
      if (tier === SubscriptionTier.LAW_STUDENT) {
        return this.analyzeBasicCitation(citationText);
      } else {
        // Professional or Enterprise tier
        return this.analyzeEnhancedCitation(citationText, options);
      }
    } catch (error) {
      this.logger.error(`Error analyzing citation by subscription: ${error.message}`, error.stack);
      // Fallback to basic analysis on error
      return this.analyzeBasicCitation(citationText);
    }
  }

  /**
   * Performs basic citation analysis (for basic subscription tier)
   * @param citationText The citation text to analyze
   * @returns Basic citation analysis
   */
  private async analyzeBasicCitation(
    citationText: string
  ): Promise<CitationAnalysisResponse | null> {
    const options: CitationAnalysisOptions = {
      includeRelationships: true,
      includePrecedentChains: false,
      includeImpact: false,
      maxRelationshipDepth: 1
    };

    return this.analyzeCitation(citationText, options);
  }

  /**
   * Performs enhanced citation analysis (for premium subscription tiers)
   * @param citationText The citation text to analyze
   * @param options Analysis options
   * @returns Enhanced citation analysis
   */
  private async analyzeEnhancedCitation(
    citationText: string,
    options: CitationAnalysisOptions = {}
  ): Promise<CitationAnalysisResponse | null> {
    const enhancedOptions: CitationAnalysisOptions = {
      includeRelationships: options.includeRelationships ?? true,
      includePrecedentChains: options.includePrecedentChains ?? true,
      includeImpact: options.includeImpact ?? true,
      maxRelationshipDepth: options.maxRelationshipDepth ?? 2,
      maxPrecedentChainLength: options.maxPrecedentChainLength ?? 5
    };

    return this.analyzeCitation(citationText, enhancedOptions);
  }

  /**
   * Analyzes all citations in a document
   * @param documentText The document text to analyze
   * @param options Analysis options
   * @returns Array of citation analyses
   */
  async analyzeDocumentCitations(
    documentText: string,
    options: CitationAnalysisOptions = {}
  ): Promise<CitationAnalysisResponse[]> {
    try {
      this.logger.log(`Analyzing citations in document`);
      
      // Extract citations from the document
      const extractedCitations = this.citationExtractionService.detectCitations(documentText);
      
      if (extractedCitations.length === 0) {
        this.logger.warn(`No citations detected in document`);
        return [];
      }
      
      // Analyze each citation
      const results: CitationAnalysisResponse[] = [];
      for (const citation of extractedCitations) {
        const analysis = await this.analyzeCitation(citation.citation, options);
        if (analysis) {
          results.push(analysis);
        }
      }
      
      return results;
    } catch (error) {
      this.logger.error(`Error analyzing document citations: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Analyzes all citations in a document based on the user's subscription tier
   * @param documentText The document text to analyze
   * @param options Analysis options
   * @param organizationId The organization ID for subscription checking
   * @returns Array of citation analyses based on subscription tier
   */
  async analyzeDocumentCitationsBySubscription(
    documentText: string,
    options: CitationAnalysisOptions = {}
  ): Promise<CitationAnalysisResponse[]> {
    try {
      const organizationId = this.tenantContext.getCurrentOrganization();
      if (!organizationId) {
        throw new Error('Organization context is required');
      }
      const subscription = await this.subscriptionService.getSubscription(organizationId);
      const tier = subscription?.tier || SubscriptionTier.LAW_STUDENT;

      // Determine analysis level based on subscription tier
      if (tier === SubscriptionTier.LAW_STUDENT) {
        return this.analyzeDocumentCitationsBasic(documentText);
      } else {
        // Professional or Enterprise tier
        return this.analyzeDocumentCitationsEnhanced(documentText, options);
      }
    } catch (error) {
      this.logger.error(`Error analyzing document citations by subscription: ${error.message}`, error.stack);
      // Fallback to basic analysis on error
      return this.analyzeDocumentCitationsBasic(documentText);
    }
  }

  /**
   * Performs basic citation analysis on all citations in a document
   * @param documentText The document text to analyze
   * @returns Array of basic citation analyses
   */
  private async analyzeDocumentCitationsBasic(
    documentText: string
  ): Promise<CitationAnalysisResponse[]> {
    const options: CitationAnalysisOptions = {
      includeRelationships: true,
      includePrecedentChains: false,
      includeImpact: false,
      maxRelationshipDepth: 1
    };

    return this.analyzeDocumentCitations(documentText, options);
  }

  /**
   * Performs enhanced citation analysis on all citations in a document
   * @param documentText The document text to analyze
   * @param options Analysis options
   * @returns Array of enhanced citation analyses
   */
  private async analyzeDocumentCitationsEnhanced(
    documentText: string,
    options: CitationAnalysisOptions = {}
  ): Promise<CitationAnalysisResponse[]> {
    const enhancedOptions: CitationAnalysisOptions = {
      includeRelationships: options.includeRelationships ?? true,
      includePrecedentChains: options.includePrecedentChains ?? true,
      includeImpact: options.includeImpact ?? true,
      maxRelationshipDepth: options.maxRelationshipDepth ?? 2,
      maxPrecedentChainLength: options.maxPrecedentChainLength ?? 5
    };

    return this.analyzeDocumentCitations(documentText, enhancedOptions);
  }

  /**
   * Generates network graph data for citation visualization
   * @param citationText The citation text to analyze
   * @param depth The depth of the network graph
   * @returns Network graph data for visualization
   */
  async generateCitationNetworkGraph(
    citationText: string,
    depth: number = 2
  ): Promise<any> {
    try {
      this.logger.log(`Generating citation network graph for: ${citationText} with depth ${depth}`);
      
      // Extract and normalize the citation
      const extractedCitations = this.citationExtractionService.detectCitations(citationText);
      
      if (extractedCitations.length === 0) {
        this.logger.warn(`No citations detected in text: ${citationText}`);
        return { nodes: [], links: [] };
      }
      
      // Use the first detected citation
      const detectedCitation = extractedCitations[0];
      
      // Normalize the citation
      const normalizedCitation = this.citationExtractionService.normalizeCitation(
        detectedCitation.citation,
        detectedCitation.type
      );
      
      // Create enhanced citation object
      const citation: EnhancedCitation = {
        id: this.generateCitationId(normalizedCitation),
        citation: normalizedCitation,
        type: detectedCitation.type
      };
      
      // Get citation relationships with specified depth
      const relationships = await this.relationshipService.mapCitationRelationships(
        citation,
        { maxRelationshipDepth: depth }
      );
      
      // Generate network graph data
      return this.convertRelationshipsToNetworkGraph(citation, relationships);
    } catch (error) {
      this.logger.error(`Error generating citation network graph: ${error.message}`, error.stack);
      return { nodes: [], links: [] };
    }
  }

  /**
   * Converts citation relationships to network graph data format
   * @param rootCitation The root citation
   * @param relationships The citation relationships
   * @returns Network graph data for visualization
   */
  private convertRelationshipsToNetworkGraph(
    rootCitation: EnhancedCitation,
    relationships: CitationRelationship[]
  ): any {
    // Create nodes map to avoid duplicates
    const nodesMap = new Map<string, any>();
    
    // Add root citation as first node
    nodesMap.set(rootCitation.id, {
      id: rootCitation.id,
      label: rootCitation.citation,
      title: rootCitation.title || rootCitation.citation,
      type: rootCitation.type,
      isRoot: true,
      court: rootCitation.court,
      year: rootCitation.year
    });
    
    // Process relationships to build nodes and links
    const links: any[] = [];
    
    relationships.forEach(rel => {
      // Add source citation if not already in nodes
      if (!nodesMap.has(rel.sourceCitationId)) {
        // Find citation details from relationships
        const sourceCitation = relationships.find(r => 
          r.targetCitationId === rel.sourceCitationId || r.sourceCitationId === rel.sourceCitationId
        );
        
        if (sourceCitation) {
          nodesMap.set(rel.sourceCitationId, {
            id: rel.sourceCitationId,
            label: sourceCitation.sourceCitationId === rel.sourceCitationId 
              ? sourceCitation.sourceCitationId 
              : sourceCitation.targetCitationId,
            type: 'case',
            isRoot: false
          });
        }
      }
      
      // Add target citation if not already in nodes
      if (!nodesMap.has(rel.targetCitationId)) {
        // Find citation details from relationships
        const targetCitation = relationships.find(r => 
          r.targetCitationId === rel.targetCitationId || r.sourceCitationId === rel.targetCitationId
        );
        
        if (targetCitation) {
          nodesMap.set(rel.targetCitationId, {
            id: rel.targetCitationId,
            label: targetCitation.targetCitationId === rel.targetCitationId 
              ? targetCitation.targetCitationId 
              : targetCitation.sourceCitationId,
            type: 'case',
            isRoot: false
          });
        }
      }
      
      // Add link
      links.push({
        source: rel.sourceCitationId,
        target: rel.targetCitationId,
        type: rel.relationshipType,
        strength: rel.strength || 'moderate',
        context: rel.context
      });
    });
    
    // Convert nodes map to array
    const nodes = Array.from(nodesMap.values());
    
    return {
      nodes,
      links,
      metadata: {
        rootCitationId: rootCitation.id,
        totalNodes: nodes.length,
        totalLinks: links.length,
        relationshipTypes: this.countRelationshipTypes(links)
      }
    };
  }

  /**
   * Counts the frequency of each relationship type
   * @param links The relationship links
   * @returns Object with relationship type counts
   */
  private countRelationshipTypes(links: any[]): Record<string, number> {
    const counts: Record<string, number> = {};
    
    links.forEach(link => {
      const type = link.type;
      counts[type] = (counts[type] || 0) + 1;
    });
    
    return counts;
  }

  /**
   * Generates a unique ID for a citation
   * @param citation The citation text
   * @returns Unique citation ID
   */
  private generateCitationId(citation: string): string {
    // Simple hash function for citation text
    let hash = 0;
    for (let i = 0; i < citation.length; i++) {
      const char = citation.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return `cit_${Math.abs(hash).toString(16)}`;
  }
}

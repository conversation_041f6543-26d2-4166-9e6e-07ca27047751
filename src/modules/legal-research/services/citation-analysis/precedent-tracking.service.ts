import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { 
  EnhancedCitation, 
  PrecedentChain,
  CitationRelationship,
  CitationAnalysisOptions
} from '../../interfaces/citation-analysis.interface';

/**
 * Service for tracking historical precedent chains in legal citations
 */
@Injectable()
export class PrecedentTrackingService {
  private readonly logger = new Logger(PrecedentTrackingService.name);
  private readonly courtListenerApiKey: string;
  private readonly courtListenerBaseUrl = 'https://www.courtlistener.com/api/rest/v3';
  
  constructor(private readonly configService: ConfigService) {
    this.courtListenerApiKey = this.configService.get<string>('COURT_LISTENER_API_KEY');
  }

  /**
   * Identifies precedent chains for a citation
   * @param citation The root citation
   * @param relationships Optional pre-fetched relationships
   * @param options Analysis options
   * @returns Array of precedent chains
   */
  async identifyPrecedentChains(
    citation: EnhancedCitation,
    relationships?: CitationRelationship[],
    options: CitationAnalysisOptions = {}
  ): Promise<PrecedentChain[]> {
    try {
      this.logger.log(`Identifying precedent chains for citation: ${citation.citation}`);
      
      // Default options
      const maxChainLength = options.maxPrecedentChainLength || 5;
      
      // If this isn't a case citation or we don't have a CourtListener ID, return empty
      if (citation.type !== 'case' || !citation.courtListenerId) {
        return [];
      }
      
      // If relationships were provided, use them to identify chains
      if (relationships && relationships.length > 0) {
        return this.buildChainsFromRelationships(citation, relationships, maxChainLength);
      }
      
      // Otherwise, fetch citation data from CourtListener
      return await this.fetchPrecedentChains(citation, maxChainLength);
    } catch (error) {
      this.logger.error(`Error identifying precedent chains: ${error.message}`, error.stack);
      return [];
    }
  }
  
  /**
   * Builds precedent chains from pre-fetched relationships
   * @param rootCitation The root citation
   * @param relationships Pre-fetched relationships
   * @param maxChainLength Maximum chain length
   * @returns Array of precedent chains
   */
  private buildChainsFromRelationships(
    rootCitation: EnhancedCitation,
    relationships: CitationRelationship[],
    maxChainLength: number
  ): PrecedentChain[] {
    const chains: PrecedentChain[] = [];
    
    // Filter relationships where the root citation is the source
    const rootRelationships = relationships.filter(
      r => r.sourceCitationId === rootCitation.id && r.relationshipType === 'cites'
    );
    
    // For each relationship, try to build a chain
    for (const relationship of rootRelationships) {
      const chain: PrecedentChain = {
        id: `chain-${rootCitation.id}-${relationship.targetCitationId}`,
        rootCitationId: rootCitation.id,
        chain: [
          {
            citationId: relationship.targetCitationId,
            relationshipToParent: 'follows', // Default, would need text analysis to determine accurately
            significance: relationship.strength === 'strong' ? 'high' : 
                          relationship.strength === 'moderate' ? 'medium' : 'low'
          }
        ]
      };
      
      // Try to extend the chain if maxChainLength > 1
      if (maxChainLength > 1) {
        this.extendChain(chain, relationships, maxChainLength - 1);
      }
      
      chains.push(chain);
    }
    
    return chains;
  }
  
  /**
   * Recursively extends a precedent chain
   * @param chain The chain to extend
   * @param relationships All available relationships
   * @param remainingDepth Maximum remaining depth
   */
  private extendChain(
    chain: PrecedentChain,
    relationships: CitationRelationship[],
    remainingDepth: number
  ): void {
    if (remainingDepth <= 0 || chain.chain.length === 0) {
      return;
    }
    
    // Get the last citation in the chain
    const lastCitationId = chain.chain[chain.chain.length - 1].citationId;
    
    // Find relationships where this citation is the source
    const nextRelationships = relationships.filter(
      r => r.sourceCitationId === lastCitationId && r.relationshipType === 'cites'
    );
    
    // If no more relationships, we're done
    if (nextRelationships.length === 0) {
      return;
    }
    
    // Add the next citation to the chain
    const nextRelationship = nextRelationships[0]; // Just take the first one for simplicity
    
    chain.chain.push({
      citationId: nextRelationship.targetCitationId,
      relationshipToParent: 'follows', // Default, would need text analysis to determine accurately
      significance: nextRelationship.strength === 'strong' ? 'high' : 
                    nextRelationship.strength === 'moderate' ? 'medium' : 'low'
    });
    
    // Recursively extend the chain
    this.extendChain(chain, relationships, remainingDepth - 1);
  }
  
  /**
   * Fetches precedent chains from CourtListener
   * @param rootCitation The root citation
   * @param maxChainLength Maximum chain length
   * @returns Array of precedent chains
   */
  private async fetchPrecedentChains(
    rootCitation: EnhancedCitation,
    maxChainLength: number
  ): Promise<PrecedentChain[]> {
    try {
      // Get opinions cited by this case
      const response = await axios.get(
        `${this.courtListenerBaseUrl}/opinions/${rootCitation.courtListenerId}/`,
        {
          headers: {
            'Authorization': `Token ${this.courtListenerApiKey}`
          }
        }
      );
      
      if (!response.data || !response.data.opinions_cited || response.data.opinions_cited.length === 0) {
        return [];
      }
      
      const chains: PrecedentChain[] = [];
      
      // For each cited opinion, try to build a chain
      for (const citedOpinionId of response.data.opinions_cited.slice(0, 5)) { // Limit to 5 chains for performance
        try {
          const chain = await this.buildChainFromCourtListener(
            rootCitation,
            citedOpinionId.toString(),
            maxChainLength
          );
          
          if (chain) {
            chains.push(chain);
          }
        } catch (error) {
          this.logger.warn(`Error building chain for cited opinion ${citedOpinionId}: ${error.message}`);
          continue;
        }
      }
      
      return chains;
    } catch (error) {
      this.logger.error(`Error fetching precedent chains: ${error.message}`, error.stack);
      return [];
    }
  }
  
  /**
   * Builds a precedent chain from CourtListener data
   * @param rootCitation The root citation
   * @param firstCitedOpinionId The ID of the first cited opinion in the chain
   * @param maxChainLength Maximum chain length
   * @returns A precedent chain, or undefined if unable to build
   */
  private async buildChainFromCourtListener(
    rootCitation: EnhancedCitation,
    firstCitedOpinionId: string,
    maxChainLength: number
  ): Promise<PrecedentChain | undefined> {
    try {
      // Get details of the first cited opinion
      const firstCitedOpinionResponse = await axios.get(
        `${this.courtListenerBaseUrl}/opinions/${firstCitedOpinionId}/`,
        {
          headers: {
            'Authorization': `Token ${this.courtListenerApiKey}`
          }
        }
      );
      
      if (!firstCitedOpinionResponse.data) {
        return undefined;
      }
      
      const firstCitedOpinion = firstCitedOpinionResponse.data;
      
      // Create the chain
      const chain: PrecedentChain = {
        id: `chain-${rootCitation.id}-${firstCitedOpinionId}`,
        rootCitationId: rootCitation.id,
        chain: [
          {
            citationId: firstCitedOpinionId,
            relationshipToParent: 'follows', // Default, would need text analysis to determine accurately
            significance: 'medium' // Default, would need more analysis to determine accurately
          }
        ]
      };
      
      // Try to extend the chain if maxChainLength > 1
      if (maxChainLength > 1 && firstCitedOpinion.opinions_cited && firstCitedOpinion.opinions_cited.length > 0) {
        await this.extendChainFromCourtListener(chain, firstCitedOpinionId, maxChainLength - 1);
      }
      
      // Try to identify the legal principle
      if (firstCitedOpinion.syllabus) {
        chain.legalPrinciple = this.extractLegalPrinciple(firstCitedOpinion.syllabus);
      }
      
      return chain;
    } catch (error) {
      this.logger.error(`Error building chain from CourtListener: ${error.message}`, error.stack);
      return undefined;
    }
  }
  
  /**
   * Recursively extends a precedent chain using CourtListener data
   * @param chain The chain to extend
   * @param currentOpinionId The ID of the current opinion in the chain
   * @param remainingDepth Maximum remaining depth
   */
  private async extendChainFromCourtListener(
    chain: PrecedentChain,
    currentOpinionId: string,
    remainingDepth: number
  ): Promise<void> {
    if (remainingDepth <= 0) {
      return;
    }
    
    try {
      // Get the current opinion
      const currentOpinionResponse = await axios.get(
        `${this.courtListenerBaseUrl}/opinions/${currentOpinionId}/`,
        {
          headers: {
            'Authorization': `Token ${this.courtListenerApiKey}`
          }
        }
      );
      
      if (!currentOpinionResponse.data || !currentOpinionResponse.data.opinions_cited || 
          currentOpinionResponse.data.opinions_cited.length === 0) {
        return;
      }
      
      // Get the next opinion in the chain
      const nextOpinionId = currentOpinionResponse.data.opinions_cited[0].toString();
      
      // Get details of the next opinion
      const nextOpinionResponse = await axios.get(
        `${this.courtListenerBaseUrl}/opinions/${nextOpinionId}/`,
        {
          headers: {
            'Authorization': `Token ${this.courtListenerApiKey}`
          }
        }
      );
      
      if (!nextOpinionResponse.data) {
        return;
      }
      
      // Add the next opinion to the chain
      chain.chain.push({
        citationId: nextOpinionId,
        relationshipToParent: 'follows', // Default, would need text analysis to determine accurately
        significance: 'medium' // Default, would need more analysis to determine accurately
      });
      
      // Recursively extend the chain
      await this.extendChainFromCourtListener(chain, nextOpinionId, remainingDepth - 1);
    } catch (error) {
      this.logger.warn(`Error extending chain from CourtListener: ${error.message}`);
      return;
    }
  }
  
  /**
   * Extracts a legal principle from a case syllabus
   * @param syllabus The case syllabus
   * @returns Extracted legal principle, or undefined if unable to extract
   */
  private extractLegalPrinciple(syllabus: string): string | undefined {
    // This is a placeholder for a more sophisticated extraction method
    // In a real implementation, we would use NLP techniques to extract the key legal principle
    
    // For now, just return the first 200 characters of the syllabus
    if (syllabus.length > 200) {
      return syllabus.substring(0, 500) + '...';
    }
    
    return syllabus;
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import NodeCache from 'node-cache';
import { 
  EnhancedCitation, 
  CitationImpact,
  CitationRelationship
} from '../../interfaces/citation-analysis.interface';

/**
 * Service for calculating the impact and significance of legal citations
 */
@Injectable()
export class CitationImpactService {
  private readonly logger = new Logger(CitationImpactService.name);
  private readonly courtListenerApiKey: string;
  private readonly courtListenerBaseUrl: string;
  private readonly cache: NodeCache;
  
  // Court hierarchy for determining influential citations
  private readonly courtHierarchy: { [key: string]: number } = {
    'Supreme Court of the United States': 10,
    'Federal Circuit': 8,
    'Circuit Court': 7,
    'District Court': 5,
    'State Supreme Court': 6,
    'State Appellate Court': 4,
    'State Trial Court': 3,
    'Administrative Court': 2,
    'Default': 1
  };
  
  constructor(private readonly configService: ConfigService) {
    this.courtListenerApiKey = this.configService.get<string>('COURT_LISTENER_API_KEY');
    this.courtListenerBaseUrl = this.configService.get<string>('COURT_LISTENER_BASE_URL', 'https://www.courtlistener.com/api/rest/v4');
    // Initialize cache with 30 minute TTL
    this.cache = new NodeCache({ stdTTL: 1800, checkperiod: 120 });
  }

  /**
   * Calculates impact metrics for a citation
   * @param citation The citation to analyze
   * @param relationships Optional pre-fetched relationships
   * @returns Citation impact metrics
   */
  async calculateCitationImpact(
    citation: EnhancedCitation,
    relationships?: CitationRelationship[]
  ): Promise<CitationImpact> {
    try {
      this.logger.log(`Calculating impact for citation: ${citation.citation}`);
      
      // Check cache first
      const cacheKey = `impact:${citation.id}`;
      const cachedImpact = this.cache.get<CitationImpact>(cacheKey);
      if (cachedImpact) {
        this.logger.debug(`Cache hit for impact metrics: ${citation.citation}`);
        return cachedImpact;
      }
      
      // Initialize impact metrics
      const impact: CitationImpact = {
        citationId: citation.id,
        totalCitations: 0,
        recentCitations: 0,
        influentialCitations: 0,
        negativeReferences: 0,
        positiveReferences: 0,
        impactScore: 0
      };
      
      // If this isn't a case citation or we don't have a CourtListener ID, return default metrics
      if (citation.type !== 'case' || !citation.courtListenerId) {
        return impact;
      }
      
      let result: CitationImpact;
      
      // If relationships were provided, use them to calculate metrics
      if (relationships && relationships.length > 0) {
        result = this.calculateImpactFromRelationships(citation, relationships);
      } else {
        // Otherwise, fetch citation data from CourtListener
        result = await this.fetchImpactMetricsFromCourtListener(citation);
      }
      
      // Cache the result
      this.cache.set(cacheKey, result);
      
      return result;
    } catch (error) {
      this.logger.error(`Error calculating citation impact: ${error.message}`, error.stack);
      return {
        citationId: citation.id,
        totalCitations: 0,
        recentCitations: 0,
        influentialCitations: 0,
        negativeReferences: 0,
        positiveReferences: 0,
        impactScore: 0
      };
    }
  }
  
  /**
   * Calculates impact metrics from pre-fetched relationships
   * @param citation The citation to analyze
   * @param relationships Pre-fetched relationships
   * @returns Citation impact metrics
   */
  private calculateImpactFromRelationships(
    citation: EnhancedCitation,
    relationships: CitationRelationship[]
  ): CitationImpact {
    // Filter relationships where this citation is the source and relationship is 'citedBy'
    const citedByRelationships = relationships.filter(
      r => r.sourceCitationId === citation.id && r.relationshipType === 'citedBy'
    );
    
    // Count total citations
    const totalCitations = citedByRelationships.length;
    
    // Count recent citations (would need date information, which we don't have in the relationship model)
    // For now, we'll set this to 0 and update it in fetchImpactMetrics if needed
    const recentCitations = 0;
    
    // Count influential citations (would need court information)
    // For now, we'll set this to 0 and update it in fetchImpactMetrics if needed
    const influentialCitations = 0;
    
    // Count positive and negative references based on relationship strength
    const positiveReferences = citedByRelationships.filter(
      r => r.strength === 'strong'
    ).length;
    
    const negativeReferences = citedByRelationships.filter(
      r => r.strength === 'weak'
    ).length;
    
    // Calculate impact score
    // Simple formula: totalCitations * 1 + influentialCitations * 2 + positiveReferences * 1.5 - negativeReferences * 0.5
    const impactScore = 
      totalCitations * 1 + 
      influentialCitations * 2 + 
      positiveReferences * 1.5 - 
      negativeReferences * 0.5;
    
    return {
      citationId: citation.id,
      totalCitations,
      recentCitations,
      influentialCitations,
      negativeReferences,
      positiveReferences,
      impactScore: Math.max(0, impactScore) // Ensure score is not negative
    };
  }
  
  /**
   * Fetches impact metrics from CourtListener
   * @param citation The citation to get impact metrics for
   * @returns Impact metrics
   */
  private async fetchImpactMetricsFromCourtListener(citation: EnhancedCitation): Promise<CitationImpact> {
    try {
      if (!citation.courtListenerId) {
        return this.createEmptyImpact(citation.id);
      }
      
      // Use the correct search format for the CourtListener API
      // The cited_by parameter finds cases that cite this opinion
      const searchUrl = `${this.courtListenerBaseUrl}/search/?format=json&type=o&cited_by=${citation.courtListenerId}&order_by=score desc&page_size=100`;
      
      this.logger.debug(`Fetching impact metrics from search API: ${searchUrl}`);
      const response = await axios.get(
        searchUrl,
        {
          headers: {
            'Authorization': `Token ${this.courtListenerApiKey}`,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          }
        }
      );
      
      if (!response.data || !response.data.results) {
        return this.createEmptyImpact(citation.id);
      }
      
      const citingOpinions = response.data.results;
      const totalCitations = response.data.count || citingOpinions.length;
      
      // Calculate recent citations (last 5 years)
      const currentYear = new Date().getFullYear();
      const recentCitations = citingOpinions.filter(opinion => {
        if (!opinion.dateFiled) return false;
        const opinionYear = new Date(opinion.dateFiled).getFullYear();
        return (currentYear - opinionYear) <= 5;
      }).length;
      
      // Calculate influential citations (from high courts)
      const influentialCitations = citingOpinions.filter(opinion => {
        if (!opinion.court) return false;
        const courtLevel = this.getCourtHierarchyLevel(opinion.court);
        return courtLevel >= 8; // Federal Circuit or Supreme Court
      }).length;
      
      // Calculate positive and negative references
      // This is a simplified approach - in a real system, you would use NLP or specific API endpoints
      const positiveReferences = totalCitations; // Default to all positive
      const negativeReferences = 0; // We don't have this data yet
      
      // Calculate impact score (weighted formula)
      const impactScore = Math.min(100, Math.round(
        (totalCitations * 1.5) + 
        (recentCitations * 3) + 
        (influentialCitations * 5)
      ));
      
      const impact: CitationImpact = {
        citationId: citation.id,
        totalCitations,
        recentCitations,
        influentialCitations,
        negativeReferences,
        positiveReferences,
        impactScore
      };
      
      return impact;
    } catch (error) {
      this.logger.error(`Error fetching impact metrics: ${error.message}`, error.stack);
      return this.createEmptyImpact(citation.id);
    }
  }
  
  /**
   * Gets the hierarchy level of a court (higher number = higher court)
   * @param courtName Name of the court
   * @returns Hierarchy level (1-10)
   */
  private getCourtHierarchyLevel(courtName: string): number {
    const lowerCourt = courtName.toLowerCase();
    
    if (lowerCourt.includes('supreme court')) {
      return 10;
    } else if (lowerCourt.includes('circuit') && lowerCourt.includes('u.s.')) {
      return 8;
    } else if (lowerCourt.includes('circuit')) {
      return 7;
    } else if (lowerCourt.includes('district') && lowerCourt.includes('u.s.')) {
      return 6;
    } else if (lowerCourt.includes('district')) {
      return 5;
    } else if (lowerCourt.includes('appellate') || lowerCourt.includes('appeal')) {
      return 4;
    } else if (lowerCourt.includes('superior')) {
      return 3;
    } else if (lowerCourt.includes('county') || lowerCourt.includes('municipal')) {
      return 2;
    } else {
      return 1;
    }
  }
  
  private createEmptyImpact(citationId: string): CitationImpact {
    return {
      citationId,
      totalCitations: 0,
      recentCitations: 0,
      influentialCitations: 0,
      negativeReferences: 0,
      positiveReferences: 0,
      impactScore: 0
    };
  }
}

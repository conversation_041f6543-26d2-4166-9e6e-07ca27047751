export interface ApiKeyConfig {
  key: string;
  expirationDate: Date;
  rateLimitPerHour: number;
}

export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  exponentialBase: number;
}

export interface CacheConfig {
  ttl: number;
  checkPeriod: number;
  maxKeys: number;
}

export interface LegalResearchConfig {
  courtListener: {
    baseUrl: string;
    apiKey: ApiKeyConfig;
    timeout: number;
  };
  govInfo: {
    baseUrl: string;
    apiKey: ApiKeyConfig;
    timeout: number;
  };
  cache: {
    case: CacheConfig;
    statute: CacheConfig;
    regulation: CacheConfig;
  };
  retry: RetryConfig;
}

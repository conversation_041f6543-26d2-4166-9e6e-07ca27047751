import { registerAs } from '@nestjs/config';

export interface ApiKeyConfig {
  key: string;
  expirationDate: Date;
  rateLimitPerHour: number;
}

export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  exponentialBase: number;
}

export interface CacheConfig {
  ttl: number;
  checkPeriod: number;
  maxKeys: number;
}

export interface LegalResearchConfig {
  courtListener: {
    baseUrl: string;
    apiKey: ApiKeyConfig;
    timeout: number;
  };
  govInfo: {
    baseUrl: string;
    apiKey: ApiKeyConfig;
    timeout: number;
  };
  cache: {
    case: CacheConfig;
    statute: CacheConfig;
    regulation: CacheConfig;
  };
  retry: RetryConfig;
}

export default registerAs('legalResearch', (): LegalResearchConfig => ({
  courtListener: {
    baseUrl: process.env.COURT_LISTENER_BASE_URL || 'https://www.courtlistener.com/api/v3',
    apiKey: {
      key: process.env.COURT_LISTENER_API_KEY || '',
      expirationDate: new Date(process.env.COURT_LISTENER_API_KEY_EXPIRATION || '2099-12-31'),
      rateLimitPerHour: parseInt(process.env.COURT_LISTENER_RATE_LIMIT || '1000', 10),
    },
    timeout: parseInt(process.env.COURT_LISTENER_TIMEOUT || '5000', 10),
  },
  govInfo: {
    baseUrl: process.env.GOVINFO_BASE_URL || 'https://api.govinfo.gov/v1',
    apiKey: {
      key: process.env.GOVINFO_API_KEY || '',
      expirationDate: new Date(process.env.GOVINFO_API_KEY_EXPIRATION || '2099-12-31'),
      rateLimitPerHour: parseInt(process.env.GOVINFO_RATE_LIMIT || '1000', 10),
    },
    timeout: parseInt(process.env.GOVINFO_TIMEOUT || '5000', 10),
  },
  cache: {
    case: {
      ttl: parseInt(process.env.CASE_CACHE_TTL || (30 * 24 * 60 * 60 * 1000).toString(), 10), // 30 days
      checkPeriod: parseInt(process.env.CASE_CACHE_CHECK_PERIOD || '600000', 10), // 10 minutes
      maxKeys: parseInt(process.env.CASE_CACHE_MAX_KEYS || '10000', 10),
    },
    statute: {
      ttl: parseInt(process.env.STATUTE_CACHE_TTL || (7 * 24 * 60 * 60 * 1000).toString(), 10), // 7 days
      checkPeriod: parseInt(process.env.STATUTE_CACHE_CHECK_PERIOD || '600000', 10),
      maxKeys: parseInt(process.env.STATUTE_CACHE_MAX_KEYS || '10000', 10),
    },
    regulation: {
      ttl: parseInt(process.env.REGULATION_CACHE_TTL || (24 * 60 * 60 * 1000).toString(), 10), // 24 hours
      checkPeriod: parseInt(process.env.REGULATION_CACHE_CHECK_PERIOD || '600000', 10),
      maxKeys: parseInt(process.env.REGULATION_CACHE_MAX_KEYS || '10000', 10),
    },
  },
  retry: {
    maxAttempts: parseInt(process.env.API_RETRY_MAX_ATTEMPTS || '3', 10),
    baseDelay: parseInt(process.env.API_RETRY_BASE_DELAY || '1000', 10), // 1 second
    maxDelay: parseInt(process.env.API_RETRY_MAX_DELAY || '10000', 10), // 10 seconds
    exponentialBase: parseInt(process.env.API_RETRY_EXPONENTIAL_BASE || '2', 10),
  },
}));
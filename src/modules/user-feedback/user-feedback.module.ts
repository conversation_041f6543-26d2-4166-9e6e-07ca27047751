import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { UserFeedbackService } from './services/user-feedback.service';
import { FeedbackAnalyticsService } from './services/feedback-analytics.service';
import { UserFeedbackController } from './controllers/user-feedback.controller';
import { Feedback, FeedbackSchema } from './schemas/feedback.schema';
import { FeedbackCategory, FeedbackCategorySchema } from './schemas/feedback-category.schema';
import { FeedbackCorrection, FeedbackCorrectionSchema } from './schemas/feedback-correction.schema';
import { AuthModule } from '../auth/auth.module';
import { SubscriptionModule } from '../subscription/subscription.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Feedback.name, schema: FeedbackSchema },
      { name: FeedbackCategory.name, schema: FeedbackCategorySchema },
      { name: FeedbackCorrection.name, schema: FeedbackCorrectionSchema },
    ]),
    AuthModule,
    SubscriptionModule,
  ],
  controllers: [UserFeedbackController],
  providers: [UserFeedbackService, FeedbackAnalyticsService],
  exports: [UserFeedbackService, FeedbackAnalyticsService],
})
export class UserFeedbackModule {}

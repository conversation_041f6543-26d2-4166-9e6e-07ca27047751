import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Feedback, FeedbackType, FeedbackStatus, FeedbackSource } from '../schemas/feedback.schema';
import { FeedbackCategory } from '../schemas/feedback-category.schema';
import { FeedbackCorrection, CorrectionStatus } from '../schemas/feedback-correction.schema';
import { CreateFeedbackDto, UpdateFeedbackDto, FeedbackQueryDto } from '../dto/feedback.dto';
import { CreateFeedbackCategoryDto, UpdateFeedbackCategoryDto } from '../dto/feedback-category.dto';
import { CreateFeedbackCorrectionDto, UpdateFeedbackCorrectionDto, FeedbackCorrectionQueryDto } from '../dto/feedback-correction.dto';

@Injectable()
export class UserFeedbackService {
  private readonly logger = new Logger(UserFeedbackService.name);

  constructor(
    @InjectModel(Feedback.name) private feedbackModel: Model<Feedback>,
    @InjectModel(FeedbackCategory.name) private categoryModel: Model<FeedbackCategory>,
    @InjectModel(FeedbackCorrection.name) private correctionModel: Model<FeedbackCorrection>,
  ) {}

  // Feedback methods
  async createFeedback(createFeedbackDto: CreateFeedbackDto, organizationId: string, userId: string) {
    this.logger.log(`Creating feedback for organization: ${organizationId}`);
    
    // If a category is specified, validate it exists
    if (createFeedbackDto.categoryId) {
      const categoryExists = await this.categoryModel.findOne({ 
        _id: createFeedbackDto.categoryId,
        organizationId,
      });
      
      if (!categoryExists) {
        throw new NotFoundException(`Category with ID ${createFeedbackDto.categoryId} not found`);
      }
    }

    const feedback = new this.feedbackModel({
      ...createFeedbackDto,
      organizationId,
      userId,
      status: FeedbackStatus.PENDING,
    });

    return feedback.save();
  }

  async findAllFeedback(organizationId: string, query: FeedbackQueryDto) {
    const { 
      type, status, source, categoryId, sourceId, 
      sourceSessionId, startDate, endDate, searchText,
      page = 1, limit = 20 
    } = query;

    const skip = (page - 1) * limit;
    const filter: any = { organizationId };

    if (type) filter.type = type;
    if (status) filter.status = status;
    if (source) filter.source = source;
    if (categoryId) filter.categoryId = categoryId;
    if (sourceId) filter.sourceId = sourceId;
    if (sourceSessionId) filter.sourceSessionId = sourceSessionId;
    
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = startDate;
      if (endDate) filter.createdAt.$lte = endDate;
    }

    if (searchText) {
      filter.$text = { $search: searchText };
    }

    const [feedback, total] = await Promise.all([
      this.feedbackModel
        .find(filter)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.feedbackModel.countDocuments(filter).exec(),
    ]);

    return {
      data: feedback,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async findAllFeedbackForAdmin(query: FeedbackQueryDto) {
    const { 
      type, status, source, categoryId, sourceId, 
      sourceSessionId, startDate, endDate, searchText,
      page = 1, limit = 20, organizationId
    } = query;

    const skip = (page - 1) * limit;
    const filter: any = {};
    
    // Allow filtering by organization if specified
    if (organizationId) filter.organizationId = organizationId;
    
    if (type) filter.type = type;
    if (status) filter.status = status;
    if (source) filter.source = source;
    if (categoryId) filter.categoryId = categoryId;
    if (sourceId) filter.sourceId = sourceId;
    if (sourceSessionId) filter.sourceSessionId = sourceSessionId;
    
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = startDate;
      if (endDate) filter.createdAt.$lte = endDate;
    }

    if (searchText) {
      filter.$text = { $search: searchText };
    }

    const [feedback, total] = await Promise.all([
      this.feedbackModel
        .find(filter)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.feedbackModel.countDocuments(filter).exec(),
    ]);

    return {
      data: feedback,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async findFeedbackById(id: string, organizationId: string) {
    const feedback = await this.feedbackModel.findOne({ _id: id, organizationId }).exec();
    
    if (!feedback) {
      throw new NotFoundException(`Feedback with ID ${id} not found`);
    }
    
    return feedback;
  }

  async updateFeedback(id: string, updateFeedbackDto: UpdateFeedbackDto, organizationId: string) {
    // Check if the feedback exists
    const feedback = await this.findFeedbackById(id, organizationId);
    
    // If updating status to implemented, set implementation date
    if (updateFeedbackDto.status === FeedbackStatus.IMPLEMENTED && feedback.status !== FeedbackStatus.IMPLEMENTED) {
      updateFeedbackDto['implementationDate'] = new Date();
    }
    
    // If updating status to reviewed, set admin response date
    if (updateFeedbackDto.status === FeedbackStatus.REVIEWED && feedback.status !== FeedbackStatus.REVIEWED) {
      updateFeedbackDto['adminResponseDate'] = new Date();
    }

    // If a category is specified, validate it exists
    if (updateFeedbackDto.categoryId) {
      const categoryExists = await this.categoryModel.findOne({ 
        _id: updateFeedbackDto.categoryId,
        organizationId,
      });
      
      if (!categoryExists) {
        throw new NotFoundException(`Category with ID ${updateFeedbackDto.categoryId} not found`);
      }
    }

    const updatedFeedback = await this.feedbackModel.findOneAndUpdate(
      { _id: id, organizationId },
      { $set: updateFeedbackDto },
      { new: true },
    ).exec();
    
    return updatedFeedback;
  }

  async deleteFeedback(id: string, organizationId: string) {
    // Check if the feedback exists
    await this.findFeedbackById(id, organizationId);
    
    // Delete any associated corrections
    await this.correctionModel.deleteMany({ feedbackId: id }).exec();
    
    // Delete the feedback
    await this.feedbackModel.findOneAndDelete({ _id: id, organizationId }).exec();
    
    return { success: true };
  }

  async voteFeedback(id: string, voteType: 'upvote' | 'downvote', organizationId: string) {
    const feedback = await this.findFeedbackById(id, organizationId);
    
    const update = voteType === 'upvote' 
      ? { $inc: { upvotes: 1 } } 
      : { $inc: { downvotes: 1 } };
    
    const updatedFeedback = await this.feedbackModel.findOneAndUpdate(
      { _id: id, organizationId },
      update,
      { new: true },
    ).exec();
    
    return updatedFeedback;
  }

  // Feedback Category methods
  async createCategory(createCategoryDto: CreateFeedbackCategoryDto, organizationId: string, userId: string) {
    this.logger.log(`Creating feedback category for organization: ${organizationId}`);
    
    // Check if a category with the same name already exists
    const existingCategory = await this.categoryModel.findOne({ 
      name: createCategoryDto.name,
      organizationId,
    });
    
    if (existingCategory) {
      throw new BadRequestException(`Category with name '${createCategoryDto.name}' already exists`);
    }

    const category = new this.categoryModel({
      ...createCategoryDto,
      organizationId,
      createdBy: userId,
    });

    return category.save();
  }

  async findAllCategories(organizationId: string) {
    return this.categoryModel.find({ organizationId }).sort({ priority: -1 }).exec();
  }

  async findCategoryById(id: string, organizationId: string) {
    const category = await this.categoryModel.findOne({ _id: id, organizationId }).exec();
    
    if (!category) {
      throw new NotFoundException(`Category with ID ${id} not found`);
    }
    
    return category;
  }

  async updateCategory(id: string, updateCategoryDto: UpdateFeedbackCategoryDto, organizationId: string) {
    // Check if the category exists
    await this.findCategoryById(id, organizationId);
    
    // If updating name, check if the new name already exists
    if (updateCategoryDto.name) {
      const existingCategory = await this.categoryModel.findOne({ 
        name: updateCategoryDto.name,
        organizationId,
        _id: { $ne: id },
      });
      
      if (existingCategory) {
        throw new BadRequestException(`Category with name '${updateCategoryDto.name}' already exists`);
      }
    }

    const updatedCategory = await this.categoryModel.findOneAndUpdate(
      { _id: id, organizationId },
      { $set: updateCategoryDto },
      { new: true },
    ).exec();
    
    return updatedCategory;
  }

  async deleteCategory(id: string, organizationId: string) {
    // Check if the category exists
    await this.findCategoryById(id, organizationId);
    
    // Check if any feedback is using this category
    const feedbackCount = await this.feedbackModel.countDocuments({ 
      categoryId: id,
      organizationId,
    });
    
    if (feedbackCount > 0) {
      throw new BadRequestException(`Cannot delete category that is used by ${feedbackCount} feedback items`);
    }
    
    await this.categoryModel.findOneAndDelete({ _id: id, organizationId }).exec();
    
    return { success: true };
  }

  // Feedback Correction methods
  async createCorrection(createCorrectionDto: CreateFeedbackCorrectionDto, organizationId: string, userId: string) {
    this.logger.log(`Creating feedback correction for organization: ${organizationId}`);
    
    // Check if the feedback exists
    const feedback = await this.findFeedbackById(createCorrectionDto.feedbackId, organizationId);
    
    // Create the correction
    const correction = new this.correctionModel({
      ...createCorrectionDto,
      organizationId,
      userId,
      status: CorrectionStatus.PENDING,
    });

    // Save the correction
    const savedCorrection = await correction.save();
    
    // Update the feedback status if it's not already a correction type
    if (feedback.type !== FeedbackType.CORRECTION) {
      await this.feedbackModel.updateOne(
        { _id: feedback.id },
        { $set: { type: FeedbackType.CORRECTION } }
      );
    }
    
    return savedCorrection;
  }

  async findAllCorrections(organizationId: string, query: FeedbackCorrectionQueryDto) {
    const { 
      feedbackId, status, sourceId, sourceType, searchText,
      page = 1, limit = 20 
    } = query;

    const skip = (page - 1) * limit;
    const filter: any = { organizationId };

    if (feedbackId) filter.feedbackId = feedbackId;
    if (status) filter.status = status;
    if (sourceId) filter.sourceId = sourceId;
    if (sourceType) filter.sourceType = sourceType;
    
    if (searchText) {
      filter.$or = [
        { originalContent: { $regex: searchText, $options: 'i' } },
        { correctedContent: { $regex: searchText, $options: 'i' } },
      ];
    }

    const [corrections, total] = await Promise.all([
      this.correctionModel
        .find(filter)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.correctionModel.countDocuments(filter).exec(),
    ]);

    return {
      data: corrections,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async findCorrectionById(id: string, organizationId: string) {
    const correction = await this.correctionModel.findOne({ _id: id, organizationId }).exec();
    
    if (!correction) {
      throw new NotFoundException(`Correction with ID ${id} not found`);
    }
    
    return correction;
  }

  async updateCorrection(id: string, updateCorrectionDto: UpdateFeedbackCorrectionDto, organizationId: string, userId: string) {
    // Check if the correction exists
    const correction = await this.findCorrectionById(id, organizationId);
    
    const updateData: any = { ...updateCorrectionDto };
    
    // If updating status to approved, set review info
    if (updateCorrectionDto.status === CorrectionStatus.APPROVED && correction.status !== CorrectionStatus.APPROVED) {
      updateData.reviewedBy = userId;
      updateData.reviewDate = new Date();
    }
    
    // If updating status to implemented, set implementation date
    if (updateCorrectionDto.status === CorrectionStatus.IMPLEMENTED && correction.status !== CorrectionStatus.IMPLEMENTED) {
      updateData.implementationDate = new Date();
    }

    const updatedCorrection = await this.correctionModel.findOneAndUpdate(
      { _id: id, organizationId },
      { $set: updateData },
      { new: true },
    ).exec();
    
    return updatedCorrection;
  }

  async deleteCorrection(id: string, organizationId: string) {
    // Check if the correction exists
    await this.findCorrectionById(id, organizationId);
    
    await this.correctionModel.findOneAndDelete({ _id: id, organizationId }).exec();
    
    return { success: true };
  }

  async voteCorrection(id: string, voteType: 'upvote' | 'downvote', organizationId: string) {
    const correction = await this.findCorrectionById(id, organizationId);
    
    const update = voteType === 'upvote' 
      ? { $inc: { upvotes: 1 } } 
      : { $inc: { downvotes: 1 } };
    
    const updatedCorrection = await this.correctionModel.findOneAndUpdate(
      { _id: id, organizationId },
      update,
      { new: true },
    ).exec();
    
    return updatedCorrection;
  }
}

import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Feedback, FeedbackType, FeedbackStatus, FeedbackSource } from '../schemas/feedback.schema';
import { FeedbackCorrection, CorrectionStatus } from '../schemas/feedback-correction.schema';
import { FeedbackCategory } from '../schemas/feedback-category.schema';
import { FeedbackAnalyticsDto } from '../dto/feedback.dto';

@Injectable()
export class FeedbackAnalyticsService {
  private readonly logger = new Logger(FeedbackAnalyticsService.name);

  constructor(
    @InjectModel(Feedback.name) private feedbackModel: Model<Feedback>,
    @InjectModel(FeedbackCorrection.name) private correctionModel: Model<FeedbackCorrection>,
    @InjectModel(FeedbackCategory.name) private categoryModel: Model<FeedbackCategory>,
  ) {}

  async getFeedbackSummary(organizationId: string, query: FeedbackAnalyticsDto) {
    this.logger.log(`Getting feedback summary for organization: ${organizationId}`);
    
    const { startDate, endDate, sourceId, source } = query;
    
    const filter: any = { organizationId };
    
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = startDate;
      if (endDate) filter.createdAt.$lte = endDate;
    }
    
    if (sourceId) filter.sourceId = sourceId;
    if (source) filter.source = source;

    // Get total counts
    const totalFeedback = await this.feedbackModel.countDocuments(filter);
    
    // Get counts by type
    const typeBreakdown = await this.feedbackModel.aggregate([
      { $match: filter },
      { $group: { _id: '$type', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    // Get counts by status
    const statusBreakdown = await this.feedbackModel.aggregate([
      { $match: filter },
      { $group: { _id: '$status', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    // Get counts by source
    const sourceBreakdown = await this.feedbackModel.aggregate([
      { $match: filter },
      { $group: { _id: '$source', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    // Get counts by category
    const categoryBreakdown = await this.feedbackModel.aggregate([
      { $match: { ...filter, categoryId: { $exists: true, $ne: null } } },
      { $group: { _id: '$categoryId', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    // Get category details
    const categoryIds = categoryBreakdown.map(item => item._id);
    const categories = await this.categoryModel.find({ 
      _id: { $in: categoryIds },
      organizationId
    });
    
    const categoryMap = categories.reduce((map, category) => {
      map[category.id] = category.name;
      return map;
    }, {});
    
    const categoriesWithNames = categoryBreakdown.map(item => ({
      id: item._id,
      name: categoryMap[item._id] || 'Unknown',
      count: item.count
    }));
    
    // Get feedback over time (daily)
    const timeSeriesData = await this.feedbackModel.aggregate([
      { $match: filter },
      { 
        $group: { 
          _id: { 
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } 
          },
          count: { $sum: 1 },
          thumbsUp: { 
            $sum: { 
              $cond: [{ $eq: ['$type', FeedbackType.THUMBS_UP] }, 1, 0] 
            } 
          },
          thumbsDown: { 
            $sum: { 
              $cond: [{ $eq: ['$type', FeedbackType.THUMBS_DOWN] }, 1, 0] 
            } 
          },
          corrections: { 
            $sum: { 
              $cond: [{ $eq: ['$type', FeedbackType.CORRECTION] }, 1, 0] 
            } 
          }
        } 
      },
      { $sort: { _id: 1 } }
    ]);
    
    // Get average ratings
    const ratingStats = await this.feedbackModel.aggregate([
      { $match: { ...filter, rating: { $exists: true, $ne: null } } },
      { 
        $group: { 
          _id: null, 
          avgRating: { $avg: '$rating' },
          count: { $sum: 1 }
        } 
      }
    ]);
    
    const avgRating = ratingStats.length > 0 ? ratingStats[0].avgRating : null;
    const ratingCount = ratingStats.length > 0 ? ratingStats[0].count : 0;

    return {
      totalFeedback,
      typeBreakdown: typeBreakdown.map(item => ({
        type: item._id,
        count: item.count,
        percentage: (item.count / totalFeedback) * 100
      })),
      statusBreakdown: statusBreakdown.map(item => ({
        status: item._id,
        count: item.count,
        percentage: (item.count / totalFeedback) * 100
      })),
      sourceBreakdown: sourceBreakdown.map(item => ({
        source: item._id,
        count: item.count,
        percentage: (item.count / totalFeedback) * 100
      })),
      categoryBreakdown: categoriesWithNames,
      timeSeriesData,
      ratings: {
        average: avgRating,
        count: ratingCount
      }
    };
  }

  async getCorrectionAnalytics(organizationId: string, query: FeedbackAnalyticsDto) {
    this.logger.log(`Getting correction analytics for organization: ${organizationId}`);
    
    const { startDate, endDate, sourceId, source } = query;
    
    const filter: any = { organizationId };
    
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = startDate;
      if (endDate) filter.createdAt.$lte = endDate;
    }
    
    if (sourceId) filter.sourceId = sourceId;
    
    // Get total counts
    const totalCorrections = await this.correctionModel.countDocuments(filter);
    
    // Get counts by status
    const statusBreakdown = await this.correctionModel.aggregate([
      { $match: filter },
      { $group: { _id: '$status', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    // Get counts by source type
    const sourceTypeBreakdown = await this.correctionModel.aggregate([
      { $match: { ...filter, sourceType: { $exists: true, $ne: null } } },
      { $group: { _id: '$sourceType', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    // Get implementation rate
    const implementedCount = await this.correctionModel.countDocuments({
      ...filter,
      status: CorrectionStatus.IMPLEMENTED
    });
    
    const implementationRate = totalCorrections > 0 
      ? (implementedCount / totalCorrections) * 100 
      : 0;
    
    // Get corrections over time (daily)
    const timeSeriesData = await this.correctionModel.aggregate([
      { $match: filter },
      { 
        $group: { 
          _id: { 
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } 
          },
          count: { $sum: 1 },
          implemented: { 
            $sum: { 
              $cond: [{ $eq: ['$status', CorrectionStatus.IMPLEMENTED] }, 1, 0] 
            } 
          },
          approved: { 
            $sum: { 
              $cond: [{ $eq: ['$status', CorrectionStatus.APPROVED] }, 1, 0] 
            } 
          },
          rejected: { 
            $sum: { 
              $cond: [{ $eq: ['$status', CorrectionStatus.REJECTED] }, 1, 0] 
            } 
          }
        } 
      },
      { $sort: { _id: 1 } }
    ]);
    
    // Get average review time
    const reviewTimeStats = await this.correctionModel.aggregate([
      { 
        $match: { 
          ...filter, 
          reviewDate: { $exists: true, $ne: null } 
        } 
      },
      { 
        $project: { 
          reviewTime: { 
            $divide: [
              { $subtract: ['$reviewDate', '$createdAt'] }, 
              1000 * 60 * 60 * 24 // Convert to days
            ] 
          } 
        } 
      },
      { 
        $group: { 
          _id: null, 
          avgReviewTime: { $avg: '$reviewTime' },
          count: { $sum: 1 }
        } 
      }
    ]);
    
    const avgReviewTime = reviewTimeStats.length > 0 ? reviewTimeStats[0].avgReviewTime : null;
    
    // Get average implementation time
    const implementationTimeStats = await this.correctionModel.aggregate([
      { 
        $match: { 
          ...filter, 
          implementationDate: { $exists: true, $ne: null },
          reviewDate: { $exists: true, $ne: null }
        } 
      },
      { 
        $project: { 
          implementationTime: { 
            $divide: [
              { $subtract: ['$implementationDate', '$reviewDate'] }, 
              1000 * 60 * 60 * 24 // Convert to days
            ] 
          } 
        } 
      },
      { 
        $group: { 
          _id: null, 
          avgImplementationTime: { $avg: '$implementationTime' },
          count: { $sum: 1 }
        } 
      }
    ]);
    
    const avgImplementationTime = implementationTimeStats.length > 0 
      ? implementationTimeStats[0].avgImplementationTime 
      : null;

    return {
      totalCorrections,
      statusBreakdown: statusBreakdown.map(item => ({
        status: item._id,
        count: item.count,
        percentage: (item.count / totalCorrections) * 100
      })),
      sourceTypeBreakdown: sourceTypeBreakdown.map(item => ({
        sourceType: item._id,
        count: item.count,
        percentage: (item.count / totalCorrections) * 100
      })),
      implementationRate,
      timeSeriesData,
      reviewTime: {
        averageDays: avgReviewTime,
        count: reviewTimeStats.length > 0 ? reviewTimeStats[0].count : 0
      },
      implementationTime: {
        averageDays: avgImplementationTime,
        count: implementationTimeStats.length > 0 ? implementationTimeStats[0].count : 0
      }
    };
  }

  async getQualityImprovementMetrics(organizationId: string, query: FeedbackAnalyticsDto) {
    this.logger.log(`Getting quality improvement metrics for organization: ${organizationId}`);
    
    const { startDate, endDate, sourceId, source } = query;
    
    const filter: any = { organizationId };
    
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = startDate;
      if (endDate) filter.createdAt.$lte = endDate;
    }
    
    if (sourceId) filter.sourceId = sourceId;
    if (source) filter.source = source;

    // Calculate thumbs up/down ratio over time (monthly)
    const sentimentOverTime = await this.feedbackModel.aggregate([
      { 
        $match: { 
          ...filter, 
          type: { $in: [FeedbackType.THUMBS_UP, FeedbackType.THUMBS_DOWN] } 
        } 
      },
      { 
        $group: { 
          _id: { 
            month: { $dateToString: { format: '%Y-%m', date: '$createdAt' } },
            type: '$type'
          },
          count: { $sum: 1 }
        } 
      },
      { $sort: { '_id.month': 1 } }
    ]);
    
    // Restructure sentiment data by month
    const sentimentByMonth = {};
    sentimentOverTime.forEach(item => {
      const month = item._id.month;
      const type = item._id.type;
      const count = item.count;
      
      if (!sentimentByMonth[month]) {
        sentimentByMonth[month] = {
          month,
          thumbsUp: 0,
          thumbsDown: 0,
          ratio: 0
        };
      }
      
      if (type === FeedbackType.THUMBS_UP) {
        sentimentByMonth[month].thumbsUp = count;
      } else if (type === FeedbackType.THUMBS_DOWN) {
        sentimentByMonth[month].thumbsDown = count;
      }
      
      // Calculate ratio
      const up = sentimentByMonth[month].thumbsUp;
      const down = sentimentByMonth[month].thumbsDown;
      sentimentByMonth[month].ratio = down > 0 ? up / down : up > 0 ? up : 0;
    });
    
    // Convert to array
    const sentimentTrend = Object.values(sentimentByMonth);
    
    // Calculate correction implementation rate over time (monthly)
    const correctionImplementationOverTime = await this.correctionModel.aggregate([
      { $match: filter },
      { 
        $group: { 
          _id: { 
            month: { $dateToString: { format: '%Y-%m', date: '$createdAt' } }
          },
          total: { $sum: 1 },
          implemented: { 
            $sum: { 
              $cond: [{ $eq: ['$status', CorrectionStatus.IMPLEMENTED] }, 1, 0] 
            } 
          }
        } 
      },
      { 
        $project: { 
          month: '$_id.month',
          total: 1,
          implemented: 1,
          implementationRate: { 
            $cond: [
              { $eq: ['$total', 0] },
              0,
              { $multiply: [{ $divide: ['$implemented', '$total'] }, 100] }
            ]
          }
        } 
      },
      { $sort: { month: 1 } }
    ]);
    
    // Get most common correction areas
    const correctionAreas = await this.correctionModel.aggregate([
      { $match: filter },
      { $unwind: { path: '$tags', preserveNullAndEmptyArrays: false } },
      { $group: { _id: '$tags', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);
    
    // Calculate overall quality score (based on thumbs up/down ratio and correction implementation)
    const overallSentiment = await this.feedbackModel.aggregate([
      { 
        $match: { 
          ...filter, 
          type: { $in: [FeedbackType.THUMBS_UP, FeedbackType.THUMBS_DOWN] } 
        } 
      },
      { 
        $group: { 
          _id: '$type',
          count: { $sum: 1 }
        } 
      }
    ]);
    
    const thumbsUpCount = overallSentiment.find(item => item._id === FeedbackType.THUMBS_UP)?.count || 0;
    const thumbsDownCount = overallSentiment.find(item => item._id === FeedbackType.THUMBS_DOWN)?.count || 0;
    const totalSentiment = thumbsUpCount + thumbsDownCount;
    
    const sentimentScore = totalSentiment > 0 
      ? (thumbsUpCount / totalSentiment) * 100 
      : 0;
    
    // Get implementation rate
    const totalCorrections = await this.correctionModel.countDocuments(filter);
    const implementedCorrections = await this.correctionModel.countDocuments({
      ...filter,
      status: CorrectionStatus.IMPLEMENTED
    });
    
    const implementationRate = totalCorrections > 0 
      ? (implementedCorrections / totalCorrections) * 100 
      : 0;
    
    // Calculate overall quality score (weighted average of sentiment and implementation)
    const qualityScore = (sentimentScore * 0.7) + (implementationRate * 0.3);

    return {
      sentimentTrend,
      correctionImplementationOverTime,
      correctionAreas: correctionAreas.map(item => ({
        tag: item._id,
        count: item.count
      })),
      overallMetrics: {
        thumbsUpCount,
        thumbsDownCount,
        sentimentScore,
        totalCorrections,
        implementedCorrections,
        implementationRate,
        qualityScore
      }
    };
  }

  /**
   * Get feedback summary across all organizations (admin only)
   */
  async getFeedbackSummaryForAdmin(query: FeedbackAnalyticsDto) {
    this.logger.log(`Admin getting feedback summary across all organizations`);
    
    const { startDate, endDate, sourceId, source, organizationId } = query;
    
    const filter: any = {};
    
    // Allow filtering by organization if specified
    if (organizationId) filter.organizationId = organizationId;
    
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = startDate;
      if (endDate) filter.createdAt.$lte = endDate;
    }
    
    if (sourceId) filter.sourceId = sourceId;
    if (source) filter.source = source;

    // Get total counts
    const totalFeedback = await this.feedbackModel.countDocuments(filter);
    
    // Get counts by type
    const typeBreakdown = await this.feedbackModel.aggregate([
      { $match: filter },
      { $group: { _id: '$type', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    // Get counts by status
    const statusBreakdown = await this.feedbackModel.aggregate([
      { $match: filter },
      { $group: { _id: '$status', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    // Get counts by organization
    const organizationBreakdown = await this.feedbackModel.aggregate([
      { $match: filter },
      { $group: { _id: '$organizationId', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 } // Limit to top 10 organizations
    ]);
    
    // Get counts by source
    const sourceBreakdown = await this.feedbackModel.aggregate([
      { $match: filter },
      { $group: { _id: '$source', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    // Get trend data
    const trendData = await this.feedbackModel.aggregate([
      { $match: filter },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);
    
    return {
      totalFeedback,
      typeBreakdown: typeBreakdown.map(item => ({
        type: item._id,
        count: item.count,
        percentage: (item.count / totalFeedback) * 100
      })),
      statusBreakdown: statusBreakdown.map(item => ({
        status: item._id,
        count: item.count,
        percentage: (item.count / totalFeedback) * 100
      })),
      organizationBreakdown: organizationBreakdown.map(item => ({
        organizationId: item._id,
        count: item.count,
        percentage: (item.count / totalFeedback) * 100
      })),
      sourceBreakdown: sourceBreakdown.map(item => ({
        source: item._id,
        count: item.count,
        percentage: (item.count / totalFeedback) * 100
      })),
      trendData: trendData.map(item => ({
        date: new Date(item._id.year, item._id.month - 1, item._id.day),
        count: item.count
      }))
    };
  }

  /**
   * Get correction analytics across all organizations (admin only)
   */
  async getCorrectionAnalyticsForAdmin(query: FeedbackAnalyticsDto) {
    this.logger.log(`Admin getting correction analytics across all organizations`);
    
    const { startDate, endDate, sourceId, source, organizationId } = query;
    
    const filter: any = {};
    
    // Allow filtering by organization if specified
    if (organizationId) filter.organizationId = organizationId;
    
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = startDate;
      if (endDate) filter.createdAt.$lte = endDate;
    }
    
    if (sourceId) filter.sourceId = sourceId;
    if (source) filter.sourceType = source;

    // Get total counts
    const totalCorrections = await this.correctionModel.countDocuments(filter);
    
    // Get counts by status
    const statusBreakdown = await this.correctionModel.aggregate([
      { $match: filter },
      { $group: { _id: '$status', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    // Get counts by organization
    const organizationBreakdown = await this.correctionModel.aggregate([
      { $match: filter },
      { $group: { _id: '$organizationId', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 10 } // Limit to top 10 organizations
    ]);
    
    // Get trend data
    const trendData = await this.correctionModel.aggregate([
      { $match: filter },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);
    
    return {
      totalCorrections,
      statusBreakdown: statusBreakdown.map(item => ({
        status: item._id,
        count: item.count,
        percentage: (item.count / totalCorrections) * 100
      })),
      organizationBreakdown: organizationBreakdown.map(item => ({
        organizationId: item._id,
        count: item.count,
        percentage: (item.count / totalCorrections) * 100
      })),
      trendData: trendData.map(item => ({
        date: new Date(item._id.year, item._id.month - 1, item._id.day),
        count: item.count
      }))
    };
  }

  /**
   * Get quality improvement metrics across all organizations (admin only)
   */
  async getQualityImprovementMetricsForAdmin(query: FeedbackAnalyticsDto) {
    this.logger.log(`Admin getting quality improvement metrics across all organizations`);
    
    const { startDate, endDate, organizationId } = query;
    
    const filter: any = {};
    
    // Allow filtering by organization if specified
    if (organizationId) filter.organizationId = organizationId;
    
    if (startDate || endDate) {
      filter.createdAt = {};
      if (startDate) filter.createdAt.$gte = startDate;
      if (endDate) filter.createdAt.$lte = endDate;
    }

    // Get positive vs negative feedback ratio
    const positiveFilter = { ...filter, type: FeedbackType.THUMBS_UP };
    const negativeFilter = { ...filter, type: FeedbackType.THUMBS_DOWN };
    
    const [positiveCount, negativeCount] = await Promise.all([
      this.feedbackModel.countDocuments(positiveFilter),
      this.feedbackModel.countDocuments(negativeFilter)
    ]);
    
    const totalRatings = positiveCount + negativeCount;
    const positiveRatio = totalRatings > 0 
      ? (positiveCount / totalRatings) * 100 
      : 0;
    
    // Get implemented corrections count
    const implementedCorrectionsFilter = { 
      ...filter, 
      status: CorrectionStatus.IMPLEMENTED 
    };
    const implementedCorrections = await this.correctionModel.countDocuments(implementedCorrectionsFilter);
    
    // Get correction implementation rate
    const totalCorrections = await this.correctionModel.countDocuments(filter);
    const correctionImplementationRate = totalCorrections > 0 
      ? (implementedCorrections / totalCorrections) * 100 
      : 0;
    
    // Get trend data for positive ratio over time
    const trendData = await this.feedbackModel.aggregate([
      { 
        $match: { 
          ...filter, 
          type: { $in: [FeedbackType.THUMBS_UP, FeedbackType.THUMBS_DOWN] } 
        } 
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' },
            type: '$type'
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);
    
    // Process trend data to calculate positive ratio per day
    const trendsByDate: Record<string, { positive: number; negative: number }> = {};
    trendData.forEach(item => {
      const dateKey = `${item._id.year}-${item._id.month}-${item._id.day}`;
      if (!trendsByDate[dateKey]) {
        trendsByDate[dateKey] = { positive: 0, negative: 0 };
      }
      
      if (item._id.type === FeedbackType.THUMBS_UP) {
        trendsByDate[dateKey].positive = item.count;
      } else if (item._id.type === FeedbackType.THUMBS_DOWN) {
        trendsByDate[dateKey].negative = item.count;
      }
    });
    
    const qualityTrend = Object.entries(trendsByDate).map(([dateKey, counts]) => {
      const [year, month, day] = dateKey.split('-').map(Number);
      const total = counts.positive + counts.negative;
      const ratio = total > 0 ? (counts.positive / total) * 100 : 0;
      
      return {
        date: new Date(year, month - 1, day),
        positiveRatio: ratio,
        total
      };
    }).sort((a, b) => a.date.getTime() - b.date.getTime());
    
    // Get organization breakdown
    const organizationBreakdown = await this.feedbackModel.aggregate([
      { 
        $match: { 
          ...filter, 
          type: { $in: [FeedbackType.THUMBS_UP, FeedbackType.THUMBS_DOWN] } 
        } 
      },
      {
        $group: {
          _id: {
            organizationId: '$organizationId',
            type: '$type'
          },
          count: { $sum: 1 }
        }
      }
    ]);
    
    // Process organization data
    const orgData: Record<string, { positive: number; negative: number }> = {};
    organizationBreakdown.forEach(item => {
      const orgId = item._id.organizationId;
      if (!orgData[orgId]) {
        orgData[orgId] = { positive: 0, negative: 0 };
      }
      
      if (item._id.type === FeedbackType.THUMBS_UP) {
        orgData[orgId].positive = item.count;
      } else if (item._id.type === FeedbackType.THUMBS_DOWN) {
        orgData[orgId].negative = item.count;
      }
    });
    
    const organizationQualityMetrics = Object.entries(orgData).map(([orgId, counts]) => {
      const total = counts.positive + counts.negative;
      const ratio = total > 0 ? (counts.positive / total) * 100 : 0;
      
      return {
        organizationId: orgId,
        positiveRatio: ratio,
        positiveCount: counts.positive,
        negativeCount: counts.negative,
        totalCount: total
      };
    }).sort((a, b) => b.positiveRatio - a.positiveRatio);
    
    return {
      overallMetrics: {
        positiveRatio,
        positiveCount,
        negativeCount,
        totalRatings,
        implementedCorrections,
        totalCorrections,
        correctionImplementationRate
      },
      qualityTrend,
      organizationQualityMetrics
    };
  }
}

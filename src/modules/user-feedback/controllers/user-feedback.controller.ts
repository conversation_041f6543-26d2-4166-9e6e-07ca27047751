import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  Logger,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Organization } from '../../auth/decorators/organization.decorator';
import { User } from '../../auth/decorators/user.decorator';
import { Roles } from '../../auth/decorators/roles.decorator';
import { FeatureAvailabilityGuard } from '../../subscription/guards/feature-availability.guard';
import { RequireFeatures } from '../../subscription/decorators/require-features.decorator';
import { UserFeedbackService } from '../services/user-feedback.service';
import { FeedbackAnalyticsService } from '../services/feedback-analytics.service';
import {
  CreateFeedbackDto,
  UpdateFeedbackDto,
  FeedbackResponseDto,
  FeedbackQueryDto,
  FeedbackVoteDto,
  FeedbackAnalyticsDto,
} from '../dto/feedback.dto';
import {
  CreateFeedbackCategoryDto,
  UpdateFeedbackCategoryDto,
  FeedbackCategoryResponseDto,
} from '../dto/feedback-category.dto';
import {
  CreateFeedbackCorrectionDto,
  UpdateFeedbackCorrectionDto,
  FeedbackCorrectionResponseDto,
  FeedbackCorrectionQueryDto,
} from '../dto/feedback-correction.dto';

@ApiTags('user-feedback')
@Controller('user-feedback')
@UseGuards(JwtAuthGuard, FeatureAvailabilityGuard)
@RequireFeatures('user_feedback')
@ApiBearerAuth()
export class UserFeedbackController {
  private readonly logger = new Logger(UserFeedbackController.name);

  constructor(
    private readonly userFeedbackService: UserFeedbackService,
    private readonly feedbackAnalyticsService: FeedbackAnalyticsService,
  ) {}

  // Feedback endpoints
  @Post('feedback')
  @ApiOperation({ summary: 'Create new feedback' })
  @ApiResponse({ status: 201, description: 'Feedback created successfully', type: FeedbackResponseDto })
  async createFeedback(
    @Body() createFeedbackDto: CreateFeedbackDto,
    @Organization() organizationId: string,
    @User() user: string | any,
  ) {
    this.logger.log(`Creating feedback for organization: ${organizationId}`);
    // Extract userId as string if user is an object
    const userId = typeof user === 'string' ? user : user.userId || user.sub;
    
    const feedback = await this.userFeedbackService.createFeedback(
      createFeedbackDto,
      organizationId,
      userId,
    );
    return this.mapFeedbackToResponseDto(feedback);
  }

  @Get('feedback')
  @ApiOperation({ summary: 'Get all feedback' })
  @ApiResponse({ status: 200, description: 'Returns all feedback', type: [FeedbackResponseDto] })
  @UseGuards(RolesGuard)
  @Roles('law_firm')
  async getAllFeedback(
    @Organization() organizationId: string,
    @Query() query: FeedbackQueryDto,
  ) {
    this.logger.log(`Admin getting all feedback across organizations`);
    // For admin users, don't filter by organization
    const result = await this.userFeedbackService.findAllFeedbackForAdmin(query);
    
    return {
      data: result.data.map(feedback => this.mapFeedbackToResponseDto(feedback)),
      pagination: result.pagination,
    };
  }

  @Get('feedback/:id')
  @ApiOperation({ summary: 'Get feedback by ID' })
  @ApiResponse({ status: 200, description: 'Returns the feedback', type: FeedbackResponseDto })
  @ApiResponse({ status: 404, description: 'Feedback not found' })
  async getFeedbackById(
    @Param('id') id: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Getting feedback with ID: ${id}`);
    const feedback = await this.userFeedbackService.findFeedbackById(id, organizationId);
    return this.mapFeedbackToResponseDto(feedback);
  }

  @Patch('feedback/:id')
  @ApiOperation({ summary: 'Update feedback' })
  @ApiResponse({ status: 200, description: 'Feedback updated successfully', type: FeedbackResponseDto })
  @ApiResponse({ status: 404, description: 'Feedback not found' })
  async updateFeedback(
    @Param('id') id: string,
    @Body() updateFeedbackDto: UpdateFeedbackDto,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Updating feedback with ID: ${id}`);
    const feedback = await this.userFeedbackService.updateFeedback(
      id,
      updateFeedbackDto,
      organizationId,
    );
    return this.mapFeedbackToResponseDto(feedback);
  }

  @Delete('feedback/:id')
  @ApiOperation({ summary: 'Delete feedback' })
  @ApiResponse({ status: 204, description: 'Feedback deleted successfully' })
  @ApiResponse({ status: 404, description: 'Feedback not found' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteFeedback(
    @Param('id') id: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Deleting feedback with ID: ${id}`);
    await this.userFeedbackService.deleteFeedback(id, organizationId);
  }

  @Post('feedback/:id/vote')
  @ApiOperation({ summary: 'Vote on feedback' })
  @ApiResponse({ status: 200, description: 'Vote recorded successfully', type: FeedbackResponseDto })
  @ApiResponse({ status: 404, description: 'Feedback not found' })
  async voteFeedback(
    @Param('id') id: string,
    @Body() voteDto: FeedbackVoteDto,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Voting on feedback with ID: ${id}`);
    const feedback = await this.userFeedbackService.voteFeedback(
      id,
      voteDto.voteType,
      organizationId,
    );
    return this.mapFeedbackToResponseDto(feedback);
  }

  // Category endpoints
  @Post('categories')
  @ApiOperation({ summary: 'Create new feedback category' })
  @ApiResponse({ status: 201, description: 'Category created successfully', type: FeedbackCategoryResponseDto })
  async createCategory(
    @Body() createCategoryDto: CreateFeedbackCategoryDto,
    @Organization() organizationId: string,
    @User() userId: string,
  ) {
    this.logger.log(`Creating feedback category for organization: ${organizationId}`);
    const category = await this.userFeedbackService.createCategory(
      createCategoryDto,
      organizationId,
      userId,
    );
    return this.mapCategoryToResponseDto(category);
  }

  @Get('categories')
  @ApiOperation({ summary: 'Get all feedback categories' })
  @ApiResponse({ status: 200, description: 'Returns all categories', type: [FeedbackCategoryResponseDto] })
  async getAllCategories(
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Getting all feedback categories for organization: ${organizationId}`);
    const categories = await this.userFeedbackService.findAllCategories(organizationId);
    return categories.map(category => this.mapCategoryToResponseDto(category));
  }

  @Get('categories/:id')
  @ApiOperation({ summary: 'Get feedback category by ID' })
  @ApiResponse({ status: 200, description: 'Returns the category', type: FeedbackCategoryResponseDto })
  @ApiResponse({ status: 404, description: 'Category not found' })
  async getCategoryById(
    @Param('id') id: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Getting feedback category with ID: ${id}`);
    const category = await this.userFeedbackService.findCategoryById(id, organizationId);
    return this.mapCategoryToResponseDto(category);
  }

  @Patch('categories/:id')
  @ApiOperation({ summary: 'Update feedback category' })
  @ApiResponse({ status: 200, description: 'Category updated successfully', type: FeedbackCategoryResponseDto })
  @ApiResponse({ status: 404, description: 'Category not found' })
  async updateCategory(
    @Param('id') id: string,
    @Body() updateCategoryDto: UpdateFeedbackCategoryDto,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Updating feedback category with ID: ${id}`);
    const category = await this.userFeedbackService.updateCategory(
      id,
      updateCategoryDto,
      organizationId,
    );
    return this.mapCategoryToResponseDto(category);
  }

  @Delete('categories/:id')
  @ApiOperation({ summary: 'Delete feedback category' })
  @ApiResponse({ status: 204, description: 'Category deleted successfully' })
  @ApiResponse({ status: 404, description: 'Category not found' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteCategory(
    @Param('id') id: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Deleting feedback category with ID: ${id}`);
    await this.userFeedbackService.deleteCategory(id, organizationId);
  }

  // Correction endpoints
  @Post('corrections')
  @ApiOperation({ summary: 'Create new feedback correction' })
  @ApiResponse({ status: 201, description: 'Correction created successfully', type: FeedbackCorrectionResponseDto })
  async createCorrection(
    @Body() createCorrectionDto: CreateFeedbackCorrectionDto,
    @Organization() organizationId: string,
    @User() userId: string,
  ) {
    this.logger.log(`Creating feedback correction for organization: ${organizationId}`);
    const correction = await this.userFeedbackService.createCorrection(
      createCorrectionDto,
      organizationId,
      userId,
    );
    return this.mapCorrectionToResponseDto(correction);
  }

  @Get('corrections')
  @ApiOperation({ summary: 'Get all feedback corrections' })
  @ApiResponse({ status: 200, description: 'Returns all corrections', type: [FeedbackCorrectionResponseDto] })
  async getAllCorrections(
    @Organization() organizationId: string,
    @Query() query: FeedbackCorrectionQueryDto,
  ) {
    this.logger.log(`Getting all feedback corrections for organization: ${organizationId}`);
    const result = await this.userFeedbackService.findAllCorrections(organizationId, query);
    
    return {
      data: result.data.map(correction => this.mapCorrectionToResponseDto(correction)),
      pagination: result.pagination,
    };
  }

  @Get('corrections/:id')
  @ApiOperation({ summary: 'Get feedback correction by ID' })
  @ApiResponse({ status: 200, description: 'Returns the correction', type: FeedbackCorrectionResponseDto })
  @ApiResponse({ status: 404, description: 'Correction not found' })
  async getCorrectionById(
    @Param('id') id: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Getting feedback correction with ID: ${id}`);
    const correction = await this.userFeedbackService.findCorrectionById(id, organizationId);
    return this.mapCorrectionToResponseDto(correction);
  }

  @Patch('corrections/:id')
  @ApiOperation({ summary: 'Update feedback correction' })
  @ApiResponse({ status: 200, description: 'Correction updated successfully', type: FeedbackCorrectionResponseDto })
  @ApiResponse({ status: 404, description: 'Correction not found' })
  async updateCorrection(
    @Param('id') id: string,
    @Body() updateCorrectionDto: UpdateFeedbackCorrectionDto,
    @Organization() organizationId: string,
    @User() userId: string,
  ) {
    this.logger.log(`Updating feedback correction with ID: ${id}`);
    const correction = await this.userFeedbackService.updateCorrection(
      id,
      updateCorrectionDto,
      organizationId,
      userId,
    );
    return this.mapCorrectionToResponseDto(correction);
  }

  @Delete('corrections/:id')
  @ApiOperation({ summary: 'Delete feedback correction' })
  @ApiResponse({ status: 204, description: 'Correction deleted successfully' })
  @ApiResponse({ status: 404, description: 'Correction not found' })
  @HttpCode(HttpStatus.NO_CONTENT)
  async deleteCorrection(
    @Param('id') id: string,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Deleting feedback correction with ID: ${id}`);
    await this.userFeedbackService.deleteCorrection(id, organizationId);
  }

  @Post('corrections/:id/vote')
  @ApiOperation({ summary: 'Vote on feedback correction' })
  @ApiResponse({ status: 200, description: 'Vote recorded successfully', type: FeedbackCorrectionResponseDto })
  @ApiResponse({ status: 404, description: 'Correction not found' })
  async voteCorrection(
    @Param('id') id: string,
    @Body() voteDto: FeedbackVoteDto,
    @Organization() organizationId: string,
  ) {
    this.logger.log(`Voting on feedback correction with ID: ${id}`);
    const correction = await this.userFeedbackService.voteCorrection(
      id,
      voteDto.voteType,
      organizationId,
    );
    return this.mapCorrectionToResponseDto(correction);
  }

  // Analytics endpoints
  @Get('analytics/summary')
  @ApiOperation({ summary: 'Get feedback analytics summary' })
  @ApiResponse({ status: 200, description: 'Returns feedback analytics summary' })
  @RequireFeatures('advanced_analytics')
  @UseGuards(RolesGuard)
  @Roles('law_firm')
  async getFeedbackSummary(
    @Organization() organizationId: string,
    @Query() query: FeedbackAnalyticsDto,
  ) {
    this.logger.log(`Admin getting feedback summary across organizations`);
    return this.feedbackAnalyticsService.getFeedbackSummaryForAdmin(query);
  }

  @Get('analytics/corrections')
  @ApiOperation({ summary: 'Get correction analytics' })
  @ApiResponse({ status: 200, description: 'Returns correction analytics' })
  @RequireFeatures('advanced_analytics')
  @UseGuards(RolesGuard)
  @Roles('law_firm')
  async getCorrectionAnalytics(
    @Organization() organizationId: string,
    @Query() query: FeedbackAnalyticsDto,
  ) {
    this.logger.log(`Admin getting correction analytics across organizations`);
    return this.feedbackAnalyticsService.getCorrectionAnalyticsForAdmin(query);
  }

  @Get('analytics/quality')
  @ApiOperation({ summary: 'Get quality improvement metrics' })
  @ApiResponse({ status: 200, description: 'Returns quality improvement metrics' })
  @RequireFeatures('advanced_analytics')
  @UseGuards(RolesGuard)
  @Roles('law_firm')
  async getQualityImprovementMetrics(
    @Organization() organizationId: string,
    @Query() query: FeedbackAnalyticsDto,
  ) {
    this.logger.log(`Admin getting quality improvement metrics across organizations`);
    return this.feedbackAnalyticsService.getQualityImprovementMetricsForAdmin(query);
  }

  // Helper methods for mapping entities to DTOs
  private mapFeedbackToResponseDto(feedback: any): FeedbackResponseDto {
    return {
      id: feedback._id.toString(),
      content: feedback.content,
      type: feedback.type,
      status: feedback.status,
      source: feedback.source,
      userId: feedback.userId,
      organizationId: feedback.organizationId,
      categoryId: feedback.categoryId,
      contextData: feedback.contextData,
      sourceId: feedback.sourceId,
      sourceSessionId: feedback.sourceSessionId,
      sourceMessageId: feedback.sourceMessageId,
      rating: feedback.rating,
      adminResponse: feedback.adminResponse,
      adminResponseDate: feedback.adminResponseDate,
      adminResponseBy: feedback.adminResponseBy,
      isAnonymous: feedback.isAnonymous,
      upvotes: feedback.upvotes,
      downvotes: feedback.downvotes,
      tags: feedback.tags,
      implementationDate: feedback.implementationDate,
      implementationDetails: feedback.implementationDetails,
      rejectionReason: feedback.rejectionReason,
      createdAt: feedback.createdAt,
      updatedAt: feedback.updatedAt,
    };
  }

  private mapCategoryToResponseDto(category: any): FeedbackCategoryResponseDto {
    return {
      id: category._id.toString(),
      name: category.name,
      description: category.description,
      isDefault: category.isDefault,
      isActive: category.isActive,
      organizationId: category.organizationId,
      createdBy: category.createdBy,
      priority: category.priority,
      color: category.color,
      tags: category.tags,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
    };
  }

  private mapCorrectionToResponseDto(correction: any): FeedbackCorrectionResponseDto {
    return {
      id: correction._id.toString(),
      feedbackId: correction.feedbackId,
      originalContent: correction.originalContent,
      correctedContent: correction.correctedContent,
      userId: correction.userId,
      organizationId: correction.organizationId,
      status: correction.status,
      reviewedBy: correction.reviewedBy,
      reviewDate: correction.reviewDate,
      reviewNotes: correction.reviewNotes,
      implementationDate: correction.implementationDate,
      implementationDetails: correction.implementationDetails,
      contextData: correction.contextData,
      sourceId: correction.sourceId,
      sourceType: correction.sourceType,
      sourceLocation: correction.sourceLocation,
      upvotes: correction.upvotes,
      downvotes: correction.downvotes,
      tags: correction.tags,
      learningImpact: correction.learningImpact,
      isIncorporatedInTraining: correction.isIncorporatedInTraining,
      createdAt: correction.createdAt,
      updatedAt: correction.updatedAt,
    };
  }
}

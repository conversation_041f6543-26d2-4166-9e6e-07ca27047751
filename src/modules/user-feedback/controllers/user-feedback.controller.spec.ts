import { Test, TestingModule } from '@nestjs/testing';
import { UserFeedbackController } from './user-feedback.controller';
import { UserFeedbackService } from '../services/user-feedback.service';
import { FeedbackAnalyticsService } from '../services/feedback-analytics.service';
import { CreateFeedbackDto, UpdateFeedbackDto, FeedbackVoteDto } from '../dto/feedback.dto';
import { CreateFeedbackCategoryDto, UpdateFeedbackCategoryDto } from '../dto/feedback-category.dto';
import { CreateFeedbackCorrectionDto, UpdateFeedbackCorrectionDto } from '../dto/feedback-correction.dto';
import { FeedbackStatus, FeedbackType, FeedbackSource } from '../schemas/feedback.schema';
import { CorrectionStatus } from '../schemas/feedback-correction.schema';
import { Logger } from '@nestjs/common';

describe('UserFeedbackController', () => {
  let controller: UserFeedbackController;
  let userFeedbackService: UserFeedbackService;
  let feedbackAnalyticsService: FeedbackAnalyticsService;

  const mockFeedback = {
    _id: '6804141b91e84ac0305e0665',
    content: 'The analysis missed a key legal precedent.',
    type: FeedbackType.THUMBS_DOWN,
    status: FeedbackStatus.PENDING,
    source: FeedbackSource.DOCUMENT_ANALYSIS,
    userId: 'd0331076-bfc2-4f8e-829e-64d2af85270e',
    organizationId: '2b7b40e6-307a-4c72-9b45-021f78a99a12',
    contextData: {
      documentId: '123',
      sectionId: '456',
    },
    sourceId: '789',
    rating: 2,
    isAnonymous: false,
    upvotes: 0,
    downvotes: 0,
    tags: [],
    createdAt: new Date('2025-04-19T21:17:26.123Z'),
    updatedAt: new Date('2025-04-19T21:17:26.123Z'),
  };

  const mockCategory = {
    _id: '6804141b91e84ac0305e0666',
    name: 'Legal Analysis Issues',
    description: 'Issues related to legal analysis quality or accuracy',
    isDefault: false,
    isActive: true,
    organizationId: '2b7b40e6-307a-4c72-9b45-021f78a99a12',
    createdBy: 'd0331076-bfc2-4f8e-829e-64d2af85270e',
    priority: 1,
    color: '#4F46E5',
    tags: [],
    createdAt: new Date('2025-04-19T21:17:26.123Z'),
    updatedAt: new Date('2025-04-19T21:17:26.123Z'),
  };

  const mockCorrection = {
    _id: '6804141b91e84ac0305e0667',
    feedbackId: '6804141b91e84ac0305e0665',
    originalContent: 'The case Smith v. Jones established...',
    correctedContent: 'The case Smith v. Johnson established...',
    userId: 'd0331076-bfc2-4f8e-829e-64d2af85270e',
    organizationId: '2b7b40e6-307a-4c72-9b45-021f78a99a12',
    status: CorrectionStatus.PENDING,
    contextData: {
      documentId: '123',
      sectionId: '456',
      paragraph: 3,
    },
    sourceId: '789',
    sourceType: 'document',
    upvotes: 0,
    downvotes: 0,
    tags: [],
    isIncorporatedInTraining: false,
    createdAt: new Date('2025-04-19T21:17:26.123Z'),
    updatedAt: new Date('2025-04-19T21:17:26.123Z'),
  };

  const mockUserFeedbackService = {
    createFeedback: jest.fn().mockResolvedValue(mockFeedback),
    findAllFeedback: jest.fn().mockResolvedValue({
      data: [mockFeedback],
      pagination: { total: 1, page: 1, limit: 20, pages: 1 },
    }),
    findFeedbackById: jest.fn().mockResolvedValue(mockFeedback),
    updateFeedback: jest.fn().mockResolvedValue(mockFeedback),
    deleteFeedback: jest.fn().mockResolvedValue(undefined),
    voteFeedback: jest.fn().mockResolvedValue(mockFeedback),
    createCategory: jest.fn().mockResolvedValue(mockCategory),
    findAllCategories: jest.fn().mockResolvedValue([mockCategory]),
    findCategoryById: jest.fn().mockResolvedValue(mockCategory),
    updateCategory: jest.fn().mockResolvedValue(mockCategory),
    deleteCategory: jest.fn().mockResolvedValue(undefined),
    createCorrection: jest.fn().mockResolvedValue(mockCorrection),
    findAllCorrections: jest.fn().mockResolvedValue({
      data: [mockCorrection],
      pagination: { total: 1, page: 1, limit: 20, pages: 1 },
    }),
    findCorrectionById: jest.fn().mockResolvedValue(mockCorrection),
    updateCorrection: jest.fn().mockResolvedValue(mockCorrection),
    deleteCorrection: jest.fn().mockResolvedValue(undefined),
    voteCorrection: jest.fn().mockResolvedValue(mockCorrection),
  };

  const mockFeedbackAnalyticsService = {
    getFeedbackSummary: jest.fn().mockResolvedValue({
      totalFeedback: 150,
      typeBreakdown: [
        { type: 'thumbs_up', count: 80, percentage: 53.33 },
      ],
      statusBreakdown: [
        { status: 'pending', count: 50, percentage: 33.33 },
      ],
    }),
    getCorrectionAnalytics: jest.fn().mockResolvedValue({
      totalCorrections: 50,
      statusBreakdown: [
        { status: 'pending', count: 20, percentage: 40 },
      ],
    }),
    getQualityImprovementMetrics: jest.fn().mockResolvedValue({
      qualityScore: 85,
      improvementRate: 15,
    }),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserFeedbackController],
      providers: [
        {
          provide: UserFeedbackService,
          useValue: mockUserFeedbackService,
        },
        {
          provide: FeedbackAnalyticsService,
          useValue: mockFeedbackAnalyticsService,
        },
        Logger,
      ],
    }).compile();

    controller = module.get<UserFeedbackController>(UserFeedbackController);
    userFeedbackService = module.get<UserFeedbackService>(UserFeedbackService);
    feedbackAnalyticsService = module.get<FeedbackAnalyticsService>(FeedbackAnalyticsService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('Feedback Endpoints', () => {
    it('should create feedback', async () => {
      const createFeedbackDto: CreateFeedbackDto = {
        content: 'The analysis missed a key legal precedent.',
        type: FeedbackType.THUMBS_DOWN,
        source: FeedbackSource.DOCUMENT_ANALYSIS,
        contextData: {
          documentId: '123',
          sectionId: '456',
        },
        sourceId: '789',
        rating: 2,
        isAnonymous: false,
      };
      
      const result = await controller.createFeedback(
        createFeedbackDto,
        '2b7b40e6-307a-4c72-9b45-021f78a99a12',
        'd0331076-bfc2-4f8e-829e-64d2af85270e',
      );
      
      expect(userFeedbackService.createFeedback).toHaveBeenCalledWith(
        createFeedbackDto,
        '2b7b40e6-307a-4c72-9b45-021f78a99a12',
        'd0331076-bfc2-4f8e-829e-64d2af85270e',
      );
      expect(result).toEqual({
        id: '6804141b91e84ac0305e0665',
        content: 'The analysis missed a key legal precedent.',
        type: FeedbackType.THUMBS_DOWN,
        status: FeedbackStatus.PENDING,
        source: FeedbackSource.DOCUMENT_ANALYSIS,
        userId: 'd0331076-bfc2-4f8e-829e-64d2af85270e',
        organizationId: '2b7b40e6-307a-4c72-9b45-021f78a99a12',
        contextData: {
          documentId: '123',
          sectionId: '456',
        },
        sourceId: '789',
        rating: 2,
        isAnonymous: false,
        upvotes: 0,
        downvotes: 0,
        tags: [],
        createdAt: new Date('2025-04-19T21:17:26.123Z'),
        updatedAt: new Date('2025-04-19T21:17:26.123Z'),
      });
    });

    it('should get all feedback', async () => {
      const query = { page: 1, limit: 20 };
      const result = await controller.getAllFeedback(
        '2b7b40e6-307a-4c72-9b45-021f78a99a12',
        query,
      );
      
      expect(userFeedbackService.findAllFeedback).toHaveBeenCalledWith(
        '2b7b40e6-307a-4c72-9b45-021f78a99a12',
        query,
      );
      expect(result).toEqual({
        data: [{
          id: '6804141b91e84ac0305e0665',
          content: 'The analysis missed a key legal precedent.',
          type: FeedbackType.THUMBS_DOWN,
          status: FeedbackStatus.PENDING,
          source: FeedbackSource.DOCUMENT_ANALYSIS,
          userId: 'd0331076-bfc2-4f8e-829e-64d2af85270e',
          organizationId: '2b7b40e6-307a-4c72-9b45-021f78a99a12',
          contextData: {
            documentId: '123',
            sectionId: '456',
          },
          sourceId: '789',
          rating: 2,
          isAnonymous: false,
          upvotes: 0,
          downvotes: 0,
          tags: [],
          createdAt: new Date('2025-04-19T21:17:26.123Z'),
          updatedAt: new Date('2025-04-19T21:17:26.123Z'),
        }],
        pagination: { total: 1, page: 1, limit: 20, pages: 1 },
      });
    });

    it('should get feedback by ID', async () => {
      const result = await controller.getFeedbackById(
        '6804141b91e84ac0305e0665',
        '2b7b40e6-307a-4c72-9b45-021f78a99a12',
      );
      
      expect(userFeedbackService.findFeedbackById).toHaveBeenCalledWith(
        '6804141b91e84ac0305e0665',
        '2b7b40e6-307a-4c72-9b45-021f78a99a12',
      );
      expect(result).toEqual({
        id: '6804141b91e84ac0305e0665',
        content: 'The analysis missed a key legal precedent.',
        type: FeedbackType.THUMBS_DOWN,
        status: FeedbackStatus.PENDING,
        source: FeedbackSource.DOCUMENT_ANALYSIS,
        userId: 'd0331076-bfc2-4f8e-829e-64d2af85270e',
        organizationId: '2b7b40e6-307a-4c72-9b45-021f78a99a12',
        contextData: {
          documentId: '123',
          sectionId: '456',
        },
        sourceId: '789',
        rating: 2,
        isAnonymous: false,
        upvotes: 0,
        downvotes: 0,
        tags: [],
        createdAt: new Date('2025-04-19T21:17:26.123Z'),
        updatedAt: new Date('2025-04-19T21:17:26.123Z'),
      });
    });

    it('should update feedback', async () => {
      const updateFeedbackDto: UpdateFeedbackDto = {
        status: FeedbackStatus.REVIEWED,
        adminResponse: 'Thank you for your feedback. We have updated our analysis.',
      };
      
      const result = await controller.updateFeedback(
        '6804141b91e84ac0305e0665',
        updateFeedbackDto,
        '2b7b40e6-307a-4c72-9b45-021f78a99a12',
      );
      
      expect(userFeedbackService.updateFeedback).toHaveBeenCalledWith(
        '6804141b91e84ac0305e0665',
        updateFeedbackDto,
        '2b7b40e6-307a-4c72-9b45-021f78a99a12',
      );
      expect(result).toEqual({
        id: '6804141b91e84ac0305e0665',
        content: 'The analysis missed a key legal precedent.',
        type: FeedbackType.THUMBS_DOWN,
        status: FeedbackStatus.PENDING,
        source: FeedbackSource.DOCUMENT_ANALYSIS,
        userId: 'd0331076-bfc2-4f8e-829e-64d2af85270e',
        organizationId: '2b7b40e6-307a-4c72-9b45-021f78a99a12',
        contextData: {
          documentId: '123',
          sectionId: '456',
        },
        sourceId: '789',
        rating: 2,
        isAnonymous: false,
        upvotes: 0,
        downvotes: 0,
        tags: [],
        createdAt: new Date('2025-04-19T21:17:26.123Z'),
        updatedAt: new Date('2025-04-19T21:17:26.123Z'),
      });
    });

    it('should delete feedback', async () => {
      await controller.deleteFeedback(
        '6804141b91e84ac0305e0665',
        '2b7b40e6-307a-4c72-9b45-021f78a99a12',
      );
      
      expect(userFeedbackService.deleteFeedback).toHaveBeenCalledWith(
        '6804141b91e84ac0305e0665',
        '2b7b40e6-307a-4c72-9b45-021f78a99a12',
      );
    });

    it('should vote on feedback', async () => {
      const voteDto: FeedbackVoteDto = {
        voteType: 'upvote',
      };
      
      const result = await controller.voteFeedback(
        '6804141b91e84ac0305e0665',
        voteDto,
        '2b7b40e6-307a-4c72-9b45-021f78a99a12',
      );
      
      expect(userFeedbackService.voteFeedback).toHaveBeenCalledWith(
        '6804141b91e84ac0305e0665',
        'upvote',
        '2b7b40e6-307a-4c72-9b45-021f78a99a12',
      );
      expect(result).toEqual({
        id: '6804141b91e84ac0305e0665',
        content: 'The analysis missed a key legal precedent.',
        type: FeedbackType.THUMBS_DOWN,
        status: FeedbackStatus.PENDING,
        source: FeedbackSource.DOCUMENT_ANALYSIS,
        userId: 'd0331076-bfc2-4f8e-829e-64d2af85270e',
        organizationId: '2b7b40e6-307a-4c72-9b45-021f78a99a12',
        contextData: {
          documentId: '123',
          sectionId: '456',
        },
        sourceId: '789',
        rating: 2,
        isAnonymous: false,
        upvotes: 0,
        downvotes: 0,
        tags: [],
        createdAt: new Date('2025-04-19T21:17:26.123Z'),
        updatedAt: new Date('2025-04-19T21:17:26.123Z'),
      });
    });
  });

  describe('Category Endpoints', () => {
    it('should create category', async () => {
      const createCategoryDto: CreateFeedbackCategoryDto = {
        name: 'Legal Analysis Issues',
        description: 'Issues related to legal analysis quality or accuracy',
        color: '#4F46E5',
        priority: 1,
      };
      
      const result = await controller.createCategory(
        createCategoryDto,
        '2b7b40e6-307a-4c72-9b45-021f78a99a12',
        'd0331076-bfc2-4f8e-829e-64d2af85270e',
      );
      
      expect(userFeedbackService.createCategory).toHaveBeenCalledWith(
        createCategoryDto,
        '2b7b40e6-307a-4c72-9b45-021f78a99a12',
        'd0331076-bfc2-4f8e-829e-64d2af85270e',
      );
      expect(result).toEqual({
        id: '6804141b91e84ac0305e0666',
        name: 'Legal Analysis Issues',
        description: 'Issues related to legal analysis quality or accuracy',
        isDefault: false,
        isActive: true,
        organizationId: '2b7b40e6-307a-4c72-9b45-021f78a99a12',
        createdBy: 'd0331076-bfc2-4f8e-829e-64d2af85270e',
        priority: 1,
        color: '#4F46E5',
        tags: [],
        createdAt: new Date('2025-04-19T21:17:26.123Z'),
        updatedAt: new Date('2025-04-19T21:17:26.123Z'),
      });
    });
  });

  describe('Analytics Endpoints', () => {
    it('should get feedback summary', async () => {
      const query = { startDate: '2025-04-01', endDate: '2025-04-19' };
      const result = await controller.getFeedbackSummary(
        '2b7b40e6-307a-4c72-9b45-021f78a99a12',
        query,
      );
      
      expect(feedbackAnalyticsService.getFeedbackSummary).toHaveBeenCalledWith(
        '2b7b40e6-307a-4c72-9b45-021f78a99a12',
        query,
      );
      expect(result).toEqual({
        totalFeedback: 150,
        typeBreakdown: [
          { type: 'thumbs_up', count: 80, percentage: 53.33 },
        ],
        statusBreakdown: [
          { status: 'pending', count: 50, percentage: 33.33 },
        ],
      });
    });
  });
});

import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsEnum, IsObject, IsArray, IsBoolean } from 'class-validator';
import { CorrectionStatus } from '../schemas/feedback-correction.schema';

export class CreateFeedbackCorrectionDto {
  @ApiProperty({ description: 'ID of the related feedback', example: '6804141b91e84ac0305e0665' })
  @IsString()
  @IsNotEmpty()
  feedbackId: string;

  @ApiProperty({ description: 'Original content that needs correction', example: 'The case <PERSON> v. <PERSON> established...' })
  @IsString()
  @IsNotEmpty()
  originalContent: string;

  @ApiProperty({ description: 'Corrected content suggested by the user', example: 'The case <PERSON> v. <PERSON> established...' })
  @IsString()
  @IsNotEmpty()
  correctedContent: string;

  @ApiProperty({ 
    description: 'Context data related to the correction', 
    example: { documentId: '123', sectionId: '456', paragraph: 3 }, 
    required: false 
  })
  @IsObject()
  @IsOptional()
  contextData?: Record<string, any>;

  @ApiProperty({ description: 'ID of the source content', required: false })
  @IsString()
  @IsOptional()
  sourceId?: string;

  @ApiProperty({ description: 'Type of source (document, chat, etc.)', required: false })
  @IsString()
  @IsOptional()
  sourceType?: string;

  @ApiProperty({ description: 'Location within the source', required: false })
  @IsString()
  @IsOptional()
  sourceLocation?: string;

  @ApiProperty({ description: 'Tags for the correction', type: [String], required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];
}

export class UpdateFeedbackCorrectionDto {
  @ApiProperty({ 
    description: 'Status of the correction', 
    enum: CorrectionStatus, 
    required: false 
  })
  @IsEnum(CorrectionStatus)
  @IsOptional()
  status?: CorrectionStatus;

  @ApiProperty({ description: 'Notes from the reviewer', required: false })
  @IsString()
  @IsOptional()
  reviewNotes?: string;

  @ApiProperty({ description: 'Implementation details', required: false })
  @IsString()
  @IsOptional()
  implementationDetails?: string;

  @ApiProperty({ description: 'Learning impact description', required: false })
  @IsString()
  @IsOptional()
  learningImpact?: string;

  @ApiProperty({ description: 'Whether the correction is incorporated in training', required: false })
  @IsBoolean()
  @IsOptional()
  isIncorporatedInTraining?: boolean;

  @ApiProperty({ description: 'Tags for the correction', type: [String], required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];
}

export class FeedbackCorrectionResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  feedbackId: string;

  @ApiProperty()
  originalContent: string;

  @ApiProperty()
  correctedContent: string;

  @ApiProperty()
  userId: string;

  @ApiProperty()
  organizationId: string;

  @ApiProperty({ enum: CorrectionStatus })
  status: CorrectionStatus;

  @ApiProperty({ required: false })
  reviewedBy?: string;

  @ApiProperty({ required: false })
  reviewDate?: Date;

  @ApiProperty({ required: false })
  reviewNotes?: string;

  @ApiProperty({ required: false })
  implementationDate?: Date;

  @ApiProperty({ required: false })
  implementationDetails?: string;

  @ApiProperty({ required: false })
  contextData?: Record<string, any>;

  @ApiProperty({ required: false })
  sourceId?: string;

  @ApiProperty({ required: false })
  sourceType?: string;

  @ApiProperty({ required: false })
  sourceLocation?: string;

  @ApiProperty()
  upvotes: number;

  @ApiProperty()
  downvotes: number;

  @ApiProperty()
  tags: string[];

  @ApiProperty({ required: false })
  learningImpact?: string;

  @ApiProperty()
  isIncorporatedInTraining: boolean;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class FeedbackCorrectionQueryDto {
  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  feedbackId?: string;

  @ApiProperty({ required: false })
  @IsEnum(CorrectionStatus)
  @IsOptional()
  status?: CorrectionStatus;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  sourceId?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  sourceType?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  searchText?: string;

  @ApiProperty({ required: false, default: 1 })
  @IsOptional()
  page?: number;

  @ApiProperty({ required: false, default: 20 })
  @IsOptional()
  limit?: number;
}

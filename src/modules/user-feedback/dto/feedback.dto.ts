import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, IsBoolean, IsNumber, IsObject, IsArray, IsDate, <PERSON>, <PERSON> } from 'class-validator';
import { FeedbackType, FeedbackStatus, FeedbackSource } from '../schemas/feedback.schema';
import { Type } from 'class-transformer';

export class CreateFeedbackDto {
  @ApiProperty({ description: 'The feedback content', example: 'The analysis missed a key legal precedent.' })
  @IsString()
  @IsNotEmpty()
  content: string;

  @ApiProperty({ 
    description: 'Type of feedback', 
    enum: FeedbackType, 
    example: FeedbackType.THUMBS_DOWN 
  })
  @IsEnum(FeedbackType)
  type: FeedbackType;

  @ApiProperty({ 
    description: 'Source of the feedback', 
    enum: FeedbackSource, 
    example: FeedbackSource.DOCUMENT_ANALYSIS 
  })
  @IsEnum(FeedbackSource)
  source: FeedbackSource;

  @ApiProperty({ description: 'Category ID for the feedback', required: false })
  @IsString()
  @IsOptional()
  categoryId?: string;

  @ApiProperty({ 
    description: 'Context data related to the feedback', 
    example: { documentId: '123', sectionId: '456' }, 
    required: false 
  })
  @IsObject()
  @IsOptional()
  contextData?: Record<string, any>;

  @ApiProperty({ description: 'ID of the source content', required: false })
  @IsString()
  @IsOptional()
  sourceId?: string;

  @ApiProperty({ description: 'ID of the session', required: false })
  @IsString()
  @IsOptional()
  sourceSessionId?: string;

  @ApiProperty({ description: 'ID of the message', required: false })
  @IsString()
  @IsOptional()
  sourceMessageId?: string;

  @ApiProperty({ 
    description: 'Numeric rating (1-5)', 
    minimum: 1, 
    maximum: 5, 
    required: false 
  })
  @IsNumber()
  @Min(1)
  @Max(5)
  @IsOptional()
  rating?: number;

  @ApiProperty({ description: 'Whether the feedback should be anonymous', default: false })
  @IsBoolean()
  @IsOptional()
  isAnonymous?: boolean;

  @ApiProperty({ description: 'Tags for the feedback', type: [String], required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];
}

export class UpdateFeedbackDto {
  @ApiProperty({ description: 'The feedback content', required: false })
  @IsString()
  @IsOptional()
  content?: string;

  @ApiProperty({ 
    description: 'Status of the feedback', 
    enum: FeedbackStatus, 
    required: false 
  })
  @IsEnum(FeedbackStatus)
  @IsOptional()
  status?: FeedbackStatus;

  @ApiProperty({ description: 'Category ID for the feedback', required: false })
  @IsString()
  @IsOptional()
  categoryId?: string;

  @ApiProperty({ description: 'Admin response to the feedback', required: false })
  @IsString()
  @IsOptional()
  adminResponse?: string;

  @ApiProperty({ description: 'Tags for the feedback', type: [String], required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];

  @ApiProperty({ description: 'Implementation details', required: false })
  @IsString()
  @IsOptional()
  implementationDetails?: string;

  @ApiProperty({ description: 'Rejection reason', required: false })
  @IsString()
  @IsOptional()
  rejectionReason?: string;
}

export class FeedbackResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  content: string;

  @ApiProperty({ enum: FeedbackType })
  type: FeedbackType;

  @ApiProperty({ enum: FeedbackStatus })
  status: FeedbackStatus;

  @ApiProperty({ enum: FeedbackSource })
  source: FeedbackSource;

  @ApiProperty()
  userId: string;

  @ApiProperty()
  organizationId: string;

  @ApiProperty({ required: false })
  categoryId?: string;

  @ApiProperty({ required: false })
  contextData?: Record<string, any>;

  @ApiProperty({ required: false })
  sourceId?: string;

  @ApiProperty({ required: false })
  sourceSessionId?: string;

  @ApiProperty({ required: false })
  sourceMessageId?: string;

  @ApiProperty({ required: false })
  rating?: number;

  @ApiProperty({ required: false })
  adminResponse?: string;

  @ApiProperty({ required: false })
  adminResponseDate?: Date;

  @ApiProperty({ required: false })
  adminResponseBy?: string;

  @ApiProperty()
  isAnonymous: boolean;

  @ApiProperty()
  upvotes: number;

  @ApiProperty()
  downvotes: number;

  @ApiProperty()
  tags: string[];

  @ApiProperty({ required: false })
  implementationDate?: Date;

  @ApiProperty({ required: false })
  implementationDetails?: string;

  @ApiProperty({ required: false })
  rejectionReason?: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

export class FeedbackQueryDto {
  @ApiProperty({ required: false })
  @IsEnum(FeedbackType)
  @IsOptional()
  type?: FeedbackType;

  @ApiProperty({ required: false })
  @IsEnum(FeedbackStatus)
  @IsOptional()
  status?: FeedbackStatus;

  @ApiProperty({ required: false })
  @IsEnum(FeedbackSource)
  @IsOptional()
  source?: FeedbackSource;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  categoryId?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  sourceId?: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  sourceSessionId?: string;

  @ApiProperty({ required: false })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  startDate?: Date;

  @ApiProperty({ required: false })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  endDate?: Date;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  searchText?: string;

  @ApiProperty({ required: false, default: 1 })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  page?: number;

  @ApiProperty({ required: false, default: 20 })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  limit?: number;

  @ApiProperty({ required: false, description: 'Filter by organization ID (admin only)' })
  @IsString()
  @IsOptional()
  organizationId?: string;
}

export class FeedbackVoteDto {
  @ApiProperty({ enum: ['upvote', 'downvote'] })
  @IsString()
  @IsNotEmpty()
  voteType: 'upvote' | 'downvote';
}

export class FeedbackAnalyticsDto {
  @ApiProperty({ required: false })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  startDate?: Date;

  @ApiProperty({ required: false })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  endDate?: Date;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  sourceId?: string;

  @ApiProperty({ required: false })
  @IsEnum(FeedbackSource)
  @IsOptional()
  source?: FeedbackSource;

  @ApiProperty({ required: false, description: 'Filter by organization ID (admin only)' })
  @IsString()
  @IsOptional()
  organizationId?: string;
}

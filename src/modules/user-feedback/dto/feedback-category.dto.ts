import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsBoolean, IsNumber, IsArray } from 'class-validator';

export class CreateFeedbackCategoryDto {
  @ApiProperty({ description: 'Name of the feedback category', example: 'Legal Analysis Issues' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'Description of the category', example: 'Issues related to legal analysis quality or accuracy' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: 'Whether this is a default category', default: false })
  @IsBoolean()
  @IsOptional()
  isDefault?: boolean;

  @ApiProperty({ description: 'Priority level of the category', default: 0 })
  @IsNumber()
  @IsOptional()
  priority?: number;

  @ApiProperty({ description: 'Color code for the category', example: '#4F46E5' })
  @IsString()
  @IsOptional()
  color?: string;

  @ApiProperty({ description: 'Tags for the category', type: [String], required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];
}

export class UpdateFeedbackCategoryDto {
  @ApiProperty({ description: 'Name of the feedback category', required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: 'Description of the category', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: 'Whether this is a default category', required: false })
  @IsBoolean()
  @IsOptional()
  isDefault?: boolean;

  @ApiProperty({ description: 'Whether the category is active', required: false })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({ description: 'Priority level of the category', required: false })
  @IsNumber()
  @IsOptional()
  priority?: number;

  @ApiProperty({ description: 'Color code for the category', required: false })
  @IsString()
  @IsOptional()
  color?: string;

  @ApiProperty({ description: 'Tags for the category', type: [String], required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];
}

export class FeedbackCategoryResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty({ required: false })
  description?: string;

  @ApiProperty()
  isDefault: boolean;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  organizationId: string;

  @ApiProperty({ required: false })
  createdBy?: string;

  @ApiProperty()
  priority: number;

  @ApiProperty()
  color: string;

  @ApiProperty()
  tags: string[];

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

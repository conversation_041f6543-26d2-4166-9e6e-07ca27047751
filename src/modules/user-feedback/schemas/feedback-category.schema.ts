import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class FeedbackCategory extends Document {
  @Prop({ required: true, unique: true })
  name: string;

  @Prop()
  description: string;

  @Prop({ default: false })
  isDefault: boolean;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ required: true })
  organizationId: string;

  @Prop()
  createdBy: string;

  @Prop({ default: 0 })
  priority: number;

  @Prop({ default: '#6B7280' }) // Default gray color
  color: string;

  @Prop({ type: [String], default: [] })
  tags: string[];
}

export const FeedbackCategorySchema = SchemaFactory.createForClass(FeedbackCategory);

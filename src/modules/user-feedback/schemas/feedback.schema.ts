import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export enum FeedbackType {
  THUMBS_UP = 'thumbs_up',
  THUMBS_DOWN = 'thumbs_down',
  CORRECTION = 'correction',
  SUGGESTION = 'suggestion',
  GENERAL = 'general',
}

export enum FeedbackStatus {
  PENDING = 'pending',
  REVIEWED = 'reviewed',
  IMPLEMENTED = 'implemented',
  REJECTED = 'rejected',
}

export enum FeedbackSource {
  DOCUMENT_ANALYSIS = 'document_analysis',
  CHAT = 'chat',
  DOCUMENT_COMPARISON = 'document_comparison',
  CITATION_ANALYSIS = 'citation_analysis',
}

@Schema({ timestamps: true })
export class Feedback extends Document {
  @Prop({ required: true })
  content: string;

  @Prop({ required: true, enum: FeedbackType, default: FeedbackType.GENERAL })
  type: FeedbackType;

  @Prop({ required: true, enum: FeedbackStatus, default: FeedbackStatus.PENDING })
  status: FeedbackStatus;

  @Prop({ required: true, enum: FeedbackSource })
  source: FeedbackSource;

  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  organizationId: string;

  @Prop({ type: String, ref: 'FeedbackCategory' })
  categoryId: string;

  @Prop({ type: MongooseSchema.Types.Mixed })
  contextData: Record<string, any>;

  @Prop()
  sourceId: string;

  @Prop()
  sourceSessionId: string;

  @Prop()
  sourceMessageId: string;

  @Prop()
  rating: number;

  @Prop()
  adminResponse: string;

  @Prop()
  adminResponseDate: Date;

  @Prop()
  adminResponseBy: string;

  @Prop({ default: false })
  isAnonymous: boolean;

  @Prop({ default: 0 })
  upvotes: number;

  @Prop({ default: 0 })
  downvotes: number;

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop()
  implementationDate: Date;

  @Prop()
  implementationDetails: string;

  @Prop()
  rejectionReason: string;
}

export const FeedbackSchema = SchemaFactory.createForClass(Feedback);

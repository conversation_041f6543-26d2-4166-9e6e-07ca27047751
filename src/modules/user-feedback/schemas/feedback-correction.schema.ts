import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export enum CorrectionStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  IMPLEMENTED = 'implemented',
}

@Schema({ timestamps: true })
export class FeedbackCorrection extends Document {
  @Prop({ required: true, type: MongooseSchema.Types.ObjectId, ref: 'Feedback' })
  feedbackId: string;

  @Prop({ required: true })
  originalContent: string;

  @Prop({ required: true })
  correctedContent: string;

  @Prop({ required: true })
  userId: string;

  @Prop({ required: true })
  organizationId: string;

  @Prop({ required: true, enum: CorrectionStatus, default: CorrectionStatus.PENDING })
  status: CorrectionStatus;

  @Prop()
  reviewedBy: string;

  @Prop()
  reviewDate: Date;

  @Prop()
  reviewNotes: string;

  @Prop()
  implementationDate: Date;

  @Prop()
  implementationDetails: string;

  @Prop({ type: MongooseSchema.Types.Mixed })
  contextData: Record<string, any>;

  @Prop()
  sourceId: string;

  @Prop()
  sourceType: string;

  @Prop()
  sourceLocation: string;

  @Prop({ default: 0 })
  upvotes: number;

  @Prop({ default: 0 })
  downvotes: number;

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop()
  learningImpact: string;

  @Prop({ default: false })
  isIncorporatedInTraining: boolean;
}

export const FeedbackCorrectionSchema = SchemaFactory.createForClass(FeedbackCorrection);

import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  IsBoolean,
  Min,
  Max,
  IsUUID,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

// Credit package enum
export enum CreditPackage {
  STUDENT = 'student',
  LAWYER_SMALL = 'lawyer_small',
  LAWYER_LARGE = 'lawyer_large',
  FIRM_STANDARD = 'firm_standard',
  FIRM_ENTERPRISE = 'firm_enterprise',
}

export class PurchaseCreditsDto {
  @ApiPropertyOptional({
    description: 'Organization ID (optional, will be taken from context)',
    example: 'org-123',
  })
  @IsOptional()
  @IsString()
  organizationId?: string;

  @ApiProperty({
    description: 'Credit package to purchase',
    enum: CreditPackage,
    example: CreditPackage.LAWYER_SMALL,
  })
  @IsEnum(CreditPackage)
  package: CreditPackage;

  @ApiPropertyOptional({
    description: 'Success URL after payment',
    example: 'https://yourdomain.com/credits/success',
  })
  @IsOptional()
  @IsString()
  successUrl?: string;

  @ApiPropertyOptional({
    description: 'Cancel URL if payment is cancelled',
    example: 'https://yourdomain.com/credits/cancel',
  })
  @IsOptional()
  @IsString()
  cancelUrl?: string;
}

export class AddCreditsDto {
  @ApiProperty({
    description: 'Organization ID',
    example: 'org-123',
  })
  @IsString()
  organizationId: string;

  @ApiProperty({
    description: 'Number of credits to add',
    example: 100,
    minimum: 1,
    maximum: 10000,
  })
  @IsNumber()
  @Min(1)
  @Max(10000)
  amount: number;

  @ApiProperty({
    description: 'Type of credit addition',
    enum: ['allocation', 'purchase', 'refund'],
    example: 'allocation',
  })
  @IsEnum(['allocation', 'purchase', 'refund'])
  type: 'allocation' | 'purchase' | 'refund';

  @ApiProperty({
    description: 'Description of the credit addition',
    example: 'Monthly credit allocation',
  })
  @IsString()
  description: string;

  @ApiPropertyOptional({
    description: 'Transaction ID for reference',
    example: 'txn_1234567890',
  })
  @IsOptional()
  @IsString()
  transactionId?: string;
}

export class UseCreditsDto {
  @ApiProperty({
    description: 'Organization ID',
    example: 'org-123',
  })
  @IsString()
  organizationId: string;

  @ApiProperty({
    description: 'Feature name to use credits for',
    example: 'advanced_analysis',
  })
  @IsString()
  featureName: string;

  @ApiPropertyOptional({
    description: 'Transaction ID for reference',
    example: 'usage_1234567890',
  })
  @IsOptional()
  @IsString()
  transactionId?: string;
}

export class UpdateAutoRechargeDto {
  @ApiProperty({
    description: 'Organization ID',
    example: 'org-123',
  })
  @IsString()
  organizationId: string;

  @ApiProperty({
    description: 'Enable or disable auto-recharge',
    example: true,
  })
  @IsBoolean()
  enabled: boolean;

  @ApiPropertyOptional({
    description: 'Credit threshold to trigger auto-recharge',
    example: 10,
    minimum: 0,
    maximum: 1000,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1000)
  threshold?: number;

  @ApiPropertyOptional({
    description: 'Amount of credits to purchase on auto-recharge',
    example: 100,
    minimum: 10,
    maximum: 5000,
  })
  @IsOptional()
  @IsNumber()
  @Min(10)
  @Max(5000)
  amount?: number;

  @ApiPropertyOptional({
    description: 'Stripe payment method ID for auto-recharge',
    example: 'pm_1234567890',
  })
  @IsOptional()
  @IsString()
  stripePaymentMethodId?: string;
}

export class GetCreditHistoryDto {
  @ApiProperty({
    description: 'Organization ID',
    example: 'org-123',
  })
  @IsString()
  organizationId: string;

  @ApiPropertyOptional({
    description: 'Number of transactions to return',
    example: 50,
    minimum: 1,
    maximum: 200,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(200)
  limit?: number;

  @ApiPropertyOptional({
    description: 'Number of transactions to skip',
    example: 0,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  offset?: number;
}

export class CreditBalanceResponseDto {
  @ApiProperty({
    description: 'Current credit balance',
    example: 150,
  })
  balance: number;

  @ApiProperty({
    description: 'Monthly credit allocation for current tier',
    example: 500,
  })
  monthlyAllocation: number;

  @ApiProperty({
    description: 'Total credits earned (all time)',
    example: 2500,
  })
  totalEarned: number;

  @ApiProperty({
    description: 'Total credits spent (all time)',
    example: 2350,
  })
  totalSpent: number;

  @ApiProperty({
    description: 'Last credit allocation date',
    example: '2024-01-15T10:30:00Z',
  })
  lastAllocation: Date;

  @ApiProperty({
    description: 'Auto-recharge configuration',
  })
  autoRecharge: {
    enabled: boolean;
    threshold: number;
    amount: number;
    hasPaymentMethod: boolean;
  };
}

export class CreditTransactionResponseDto {
  @ApiProperty({
    description: 'Transaction type',
    enum: ['allocation', 'purchase', 'usage', 'refund', 'expiration'],
    example: 'usage',
  })
  type: 'allocation' | 'purchase' | 'usage' | 'refund' | 'expiration';

  @ApiProperty({
    description: 'Credit amount (positive for additions, negative for usage)',
    example: -5,
  })
  amount: number;

  @ApiProperty({
    description: 'Credit balance after transaction',
    example: 145,
  })
  balance: number;

  @ApiPropertyOptional({
    description: 'Feature name (for usage transactions)',
    example: 'advanced_analysis',
  })
  featureName?: string;

  @ApiPropertyOptional({
    description: 'Transaction ID',
    example: 'txn_1234567890',
  })
  transactionId?: string;

  @ApiProperty({
    description: 'Transaction timestamp',
    example: '2024-01-15T14:30:00Z',
  })
  timestamp: Date;

  @ApiProperty({
    description: 'Transaction description',
    example: 'Used 5 credits for Advanced Analysis',
  })
  description: string;
}

export class CreditHistoryResponseDto {
  @ApiProperty({
    description: 'List of credit transactions',
    type: [CreditTransactionResponseDto],
  })
  transactions: CreditTransactionResponseDto[];

  @ApiProperty({
    description: 'Total number of transactions',
    example: 125,
  })
  total: number;

  @ApiProperty({
    description: 'Number of transactions returned',
    example: 50,
  })
  count: number;

  @ApiProperty({
    description: 'Offset used for pagination',
    example: 0,
  })
  offset: number;
}

export class FeatureCostResponseDto {
  @ApiProperty({
    description: 'Feature name',
    example: 'advanced_analysis',
  })
  name: string;

  @ApiProperty({
    description: 'Credit cost for the feature',
    example: 5,
  })
  creditCost: number;

  @ApiProperty({
    description: 'Feature description',
    example: 'Advanced document analysis with detailed insights',
  })
  description: string;

  @ApiProperty({
    description: 'Feature category',
    enum: ['basic', 'advanced', 'premium', 'ai', 'analysis', 'automation'],
    example: 'advanced',
  })
  category: 'basic' | 'advanced' | 'premium' | 'ai' | 'analysis' | 'automation';
}

export class CreditUsageCheckResponseDto {
  @ApiProperty({
    description: 'Whether organization has sufficient credits',
    example: true,
  })
  hasCredits: boolean;

  @ApiProperty({
    description: 'Current credit balance',
    example: 150,
  })
  currentBalance: number;

  @ApiProperty({
    description: 'Required credits for the feature',
    example: 5,
  })
  requiredCredits: number;

  @ApiProperty({
    description: 'Feature information',
    type: FeatureCostResponseDto,
  })
  feature: FeatureCostResponseDto;
}

export class CreditPackageResponseDto {
  @ApiProperty({
    description: 'Package name',
    example: 'medium',
  })
  name: string;

  @ApiProperty({
    description: 'Number of credits in package',
    example: 500,
  })
  credits: number;

  @ApiProperty({
    description: 'Package price in cents',
    example: 4499,
  })
  price: number;

  @ApiProperty({
    description: 'Bonus credits included',
    example: 50,
  })
  bonus: number;

  @ApiProperty({
    description: 'Total credits (credits + bonus)',
    example: 550,
  })
  totalCredits: number;

  @ApiProperty({
    description: 'Package description',
    example: 'Medium credit package with 10% bonus',
  })
  description: string;

  @ApiProperty({
    description: 'Price per credit in cents',
    example: 8.18,
  })
  pricePerCredit: number;
}

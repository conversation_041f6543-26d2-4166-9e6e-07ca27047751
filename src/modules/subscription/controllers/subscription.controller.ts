import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Param,
  UseGuards,
  Headers,
  BadRequestException,
  Req,
  Patch,
  Logger,
} from '@nestjs/common';
import { SubscriptionService } from '../services/subscription.service';
import { CreateSubscriptionDto } from '../dto/create-subscription.dto';
import { TenantGuard } from '../../auth/guards/tenant.guard';
import { Public } from '../../auth/decorators/public.decorator';
import { Subscription } from '../interfaces/subscription.interface';
import Stripe from 'stripe';
import { CreateCheckoutSessionDto } from '../dto/create-checkout-session.dto';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';
import { TierMigrationService } from '../services/tier-migration.service';
import { PostHogService } from '../../posthog/services/posthog.service';

@Controller('subscriptions')
@UseGuards(TenantGuard)
export class SubscriptionController {
  private readonly logger = new Logger(SubscriptionController.name);

  constructor(
    private readonly subscriptionService: SubscriptionService,
    private readonly configService: ConfigService,
    private readonly tierMigrationService: TierMigrationService,
    private readonly postHogService: PostHogService,
  ) {}

  @Post()
  async createSubscription(
    @Body() createSubscriptionDto: CreateSubscriptionDto
  ): Promise<Subscription> {
    const result = await this.subscriptionService.createSubscription(
      createSubscriptionDto.organizationId,
      createSubscriptionDto.email,
      createSubscriptionDto.tier
    );

    // Track subscription creation
    this.postHogService.trackEvent('admin', 'subscription_created', {
      organization_id: createSubscriptionDto.organizationId,
      email: createSubscriptionDto.email,
      tier: createSubscriptionDto.tier,
      timestamp: new Date().toISOString()
    });

    return result;
  }

  @Post('checkout')
  async createCheckoutSession(
    @Body() checkoutDto: CreateCheckoutSessionDto,
    @Req() request: Request
  ): Promise<{ sessionUrl: string }> {
    // Get the base URL from configuration
    const baseUrl = this.configService.get<string>('APP_URL') || 'http://localhost:3000';

    // Set default success and cancel URLs if not provided
    const successUrl = checkoutDto.successUrl ||
      `${baseUrl}/subscription/success?session_id={CHECKOUT_SESSION_ID}`;
    const cancelUrl = checkoutDto.cancelUrl ||
      `${baseUrl}/subscription/cancel`;

    const result = await this.subscriptionService.createCheckoutSession(
      checkoutDto.organizationId,
      checkoutDto.tier,
      successUrl,
      cancelUrl
    );

    // Track checkout session creation
    this.postHogService.trackEvent('organization', 'checkout_session_created', {
      organization_id: checkoutDto.organizationId,
      tier: checkoutDto.tier,
      timestamp: new Date().toISOString()
    });

    return result;
  }

  @Get(':organizationId')
  async getSubscription(
    @Param('organizationId') organizationId: string
  ): Promise<Subscription> {
    return this.subscriptionService.getSubscription(organizationId);
  }

  @Delete(':organizationId')
  async cancelSubscription(
    @Param('organizationId') organizationId: string
  ): Promise<Subscription> {
    const result = await this.subscriptionService.cancelSubscription(organizationId);

    // Track subscription cancellation
    this.postHogService.trackEvent('organization', 'subscription_cancelled', {
      organization_id: organizationId,
      timestamp: new Date().toISOString()
    });

    return result;
  }

  @Post('webhook')
  @Public()
  async handleWebhook(
    @Headers('stripe-signature') signature: string,
    @Body() payload: Buffer
  ): Promise<void> {
    this.logger.log('Webhook received with signature:', signature?.substring(0, 20) + '...');

    if (!signature) {
      this.logger.error('Missing stripe-signature header');
      throw new BadRequestException('Missing stripe-signature header');
    }

    try {
      await this.subscriptionService.handleStripeWebhook(payload as any);
      this.logger.log('Webhook processed successfully');
    } catch (error) {
      this.logger.error('Webhook processing failed:', error.message);
      throw error;
    }
  }

  @Get(':organizationId/usage')
  async checkUsageLimits(
    @Param('organizationId') organizationId: string
  ): Promise<{ allowed: boolean }> {
    const allowed = await this.subscriptionService.checkUsageLimits(organizationId);
    return { allowed };
  }

  @Patch(':organizationId/tier')
  async updateSubscriptionTier(
    @Param('organizationId') organizationId: string,
    @Body('tier') tier: any
  ): Promise<Subscription> {
    const result = await this.subscriptionService.updateSubscriptionTier(organizationId, tier);

    // Track tier upgrade/downgrade
    this.postHogService.trackEvent('organization', 'subscription_tier_updated', {
      organization_id: organizationId,
      new_tier: tier,
      timestamp: new Date().toISOString()
    });

    return result;
  }

  @Patch(':organizationId/features')
  async updateSubscriptionFeatures(
    @Param('organizationId') organizationId: string
  ): Promise<Subscription> {
    return this.subscriptionService.updateSubscriptionFeatures(organizationId);
  }

  @Post('migrate-to-credits')
  async migrateToCreditSystem() {
    await this.subscriptionService.initializeCreditSystemForExistingSubscriptions();
    return {
      success: true,
      message: 'Credit system initialized for all existing subscriptions',
    };
  }

  @Post('migrate-tiers')
  async migrateTiers() {
    const result = await this.tierMigrationService.migrateLegacyTiers();
    return {
      success: result.success,
      message: `Migrated ${result.migratedCount} subscriptions to new tier structure`,
      migratedCount: result.migratedCount,
      errors: result.errors,
    };
  }

  @Post('update-pricing')
  async updatePricing() {
    const result = await this.tierMigrationService.updateSubscriptionPricing();
    return {
      success: result.success,
      message: `Updated pricing for ${result.updatedCount} subscriptions`,
      updatedCount: result.updatedCount,
      errors: result.errors,
    };
  }

  @Get('migration-stats')
  async getMigrationStats() {
    const stats = await this.tierMigrationService.getMigrationStats();
    return {
      success: true,
      data: stats,
    };
  }

  @Post('validate-tiers')
  async validateTiers() {
    const validation = await this.tierMigrationService.validateTierNames();
    return {
      success: true,
      valid: validation.valid,
      invalidSubscriptions: validation.invalidSubscriptions,
    };
  }

  @Post(':organizationId/allocate-monthly-credits')
  async allocateMonthlyCredits(
    @Param('organizationId') organizationId: string
  ) {
    const result = await this.subscriptionService.allocateMonthlyCreditsManual(organizationId);
    return {
      success: true,
      message: `Allocated ${result.creditsAllocated} monthly credits`,
      creditsAllocated: result.creditsAllocated,
      newBalance: result.newBalance,
    };
  }
}
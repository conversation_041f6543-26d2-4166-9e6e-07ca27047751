import {
  Controller,
  Post,
  Get,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { UserRole } from '../../auth/enums/roles.enum';
import {
  FeatureManagementService,
  FeatureDefinition,
} from '../services/feature-management.service';
import { SubscriptionTier } from '../enums/subscription-tier.enum';

export class AddNewFeaturesDto {
  features: FeatureDefinition[];
  dryRun?: boolean;
  notifyUsers?: boolean;
}

export class SyncSubscriptionDto {
  subscriptionId: string;
}

@ApiTags('Feature Management')
@Controller('admin/features')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(UserRole.ADMIN)
@ApiBearerAuth()
export class FeatureManagementController {
  private readonly logger = new Logger(FeatureManagementController.name);

  constructor(
    private readonly featureManagementService: FeatureManagementService,
  ) {}

  @Get('all')
  @ApiOperation({
    summary: 'Get all available features by tier',
    description: 'Returns all features organized by subscription tier',
  })
  @ApiResponse({
    status: 200,
    description: 'Features retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        FREE: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              description: { type: 'string' },
              category: { type: 'string' },
              requiredTier: { type: 'string' },
            },
          },
        },
        PRO: { type: 'array' },
        ADMIN: { type: 'array' },
      },
    },
  })
  async getAllFeatures() {
    this.logger.log('Retrieving all features by tier');
    return {
      success: true,
      data: this.featureManagementService.getAllFeatures(),
    };
  }

  @Post('update-subscriptions')
  @ApiOperation({
    summary: 'Update all subscriptions with new features',
    description:
      'Bulk update all active subscriptions to include new features based on their tier',
  })
  @ApiBody({
    description: 'New features to add to subscriptions',
    schema: {
      type: 'object',
      properties: {
        features: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              description: { type: 'string' },
              category: { type: 'string' },
              requiredTier: { type: 'string', enum: ['FREE', 'PRO', 'ADMIN'] },
              isNew: { type: 'boolean' },
              addedDate: { type: 'string', format: 'date-time' },
            },
          },
        },
        dryRun: { type: 'boolean', default: false },
        notifyUsers: { type: 'boolean', default: false },
      },
      required: ['features'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Subscriptions updated successfully',
    schema: {
      type: 'object',
      properties: {
        totalSubscriptions: { type: 'number' },
        updatedSubscriptions: { type: 'number' },
        skippedSubscriptions: { type: 'number' },
        errors: { type: 'array' },
        newFeaturesAdded: { type: 'array', items: { type: 'string' } },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid request data',
  })
  @HttpCode(HttpStatus.OK)
  async updateSubscriptionsWithNewFeatures(@Body() dto: AddNewFeaturesDto) {
    this.logger.log(
      `Updating subscriptions with ${dto.features.length} new features (dryRun: ${dto.dryRun})`,
    );

    try {
      const result =
        await this.featureManagementService.updateAllSubscriptionsWithNewFeatures(
          dto.features,
          dto.dryRun || false,
        );

      this.logger.log(
        `Feature update completed: ${result.updatedSubscriptions}/${result.totalSubscriptions} updated`,
      );

      return {
        success: true,
        data: result,
        message: dto.dryRun
          ? 'Dry run completed - no actual changes made'
          : `Successfully updated ${result.updatedSubscriptions} subscriptions`,
      };
    } catch (error) {
      this.logger.error(
        `Error updating subscriptions: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Put('sync/:subscriptionId')
  @ApiOperation({
    summary: 'Sync single subscription features',
    description:
      "Synchronize a single subscription's features with current tier definitions",
  })
  @ApiParam({
    name: 'subscriptionId',
    description: 'The ID of the subscription to sync',
    type: String,
  })
  @ApiResponse({
    status: 200,
    description: 'Subscription synced successfully',
    schema: {
      type: 'object',
      properties: {
        added: { type: 'array', items: { type: 'string' } },
        removed: { type: 'array', items: { type: 'string' } },
        unchanged: { type: 'array', items: { type: 'string' } },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Subscription not found',
  })
  async syncSubscriptionFeatures(
    @Param('subscriptionId') subscriptionId: string,
  ) {
    this.logger.log(`Syncing features for subscription: ${subscriptionId}`);

    try {
      const result =
        await this.featureManagementService.syncSubscriptionFeatures(
          subscriptionId,
        );

      return {
        success: true,
        data: result,
        message: `Synced subscription: +${result.added.length} -${result.removed.length} features`,
      };
    } catch (error) {
      this.logger.error(
        `Error syncing subscription ${subscriptionId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get('usage-stats')
  @ApiOperation({
    summary: 'Get feature usage statistics',
    description:
      'Returns statistics about feature usage across all active subscriptions',
  })
  @ApiResponse({
    status: 200,
    description: 'Usage statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalSubscriptions: { type: 'number' },
        featureUsage: {
          type: 'object',
          additionalProperties: {
            type: 'object',
            properties: {
              count: { type: 'number' },
              percentage: { type: 'number' },
              tier: { type: 'string' },
            },
          },
        },
      },
    },
  })
  async getFeatureUsageStats() {
    this.logger.log('Retrieving feature usage statistics');

    try {
      const stats = await this.featureManagementService.getFeatureUsageStats();

      return {
        success: true,
        data: stats,
      };
    } catch (error) {
      this.logger.error(
        `Error retrieving usage stats: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Post('quick-add')
  @ApiOperation({
    summary: 'Quick add common new features',
    description:
      'Quickly add commonly requested new features to all eligible subscriptions',
  })
  @ApiBody({
    description: 'Quick feature addition options',
    schema: {
      type: 'object',
      properties: {
        featureNames: {
          type: 'array',
          items: { type: 'string' },
          description: 'Names of features to add',
        },
        targetTier: {
          type: 'string',
          enum: ['FREE', 'PRO', 'ADMIN'],
          description: 'Minimum tier required for these features',
        },
        category: {
          type: 'string',
          description: 'Feature category',
        },
        dryRun: { type: 'boolean', default: false },
      },
      required: ['featureNames', 'targetTier', 'category'],
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Features added successfully',
  })
  @HttpCode(HttpStatus.OK)
  async quickAddFeatures(
    @Body()
    dto: {
      featureNames: string[];
      targetTier: SubscriptionTier;
      category: string;
      dryRun?: boolean;
    },
  ) {
    this.logger.log(
      `Quick adding ${dto.featureNames.length} features to ${dto.targetTier} tier`,
    );

    try {
      // Convert feature names to FeatureDefinition objects
      const features: FeatureDefinition[] = dto.featureNames.map((name) => ({
        name,
        description: `Auto-generated feature: ${name}`,
        category: dto.category,
        requiredTier: dto.targetTier,
        isNew: true,
        addedDate: new Date(),
      }));

      const result =
        await this.featureManagementService.updateAllSubscriptionsWithNewFeatures(
          features,
          dto.dryRun || false,
        );

      return {
        success: true,
        data: result,
        message: `Quick-added ${dto.featureNames.length} features`,
      };
    } catch (error) {
      this.logger.error(
        `Error in quick add features: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  @Get('tier/:tier')
  @ApiOperation({
    summary: 'Get features for specific tier',
    description:
      'Returns all features available for a specific subscription tier',
  })
  @ApiParam({
    name: 'tier',
    description: 'Subscription tier',
    enum: SubscriptionTier,
  })
  @ApiResponse({
    status: 200,
    description: 'Tier features retrieved successfully',
  })
  async getFeaturesForTier(@Param('tier') tier: SubscriptionTier) {
    this.logger.log(`Retrieving features for tier: ${tier}`);

    const allFeatures = this.featureManagementService.getAllFeatures();
    const tierFeatures = allFeatures[tier] || [];

    return {
      success: true,
      data: {
        tier,
        features: tierFeatures,
        count: tierFeatures.length,
      },
    };
  }
}

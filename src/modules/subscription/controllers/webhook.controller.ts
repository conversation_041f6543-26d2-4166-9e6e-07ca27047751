import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { Public } from '../../auth/decorators/public.decorator';
import { SubscriptionService } from '../services/subscription.service';

@Controller('webhook')
export class WebhookController {
  private readonly logger = new Logger(WebhookController.name);

  constructor(private readonly subscriptionService: SubscriptionService) {}

  @Post('stripe')
  @Public()
  async handleStripeWebhook(
    @Req() request: Request,
    @Res() response: Response,
    @Headers('stripe-signature') signature: string,
  ): Promise<void> {
    this.logger.log('Stripe webhook received');
    this.logger.log('Signature:', signature?.substring(0, 20) + '...');
    this.logger.log('Content-Type:', request.headers['content-type']);
    this.logger.log('Body type:', typeof request.body);

    if (!signature) {
      this.logger.error('Missing stripe-signature header');
      response.status(400).json({ error: 'Missing stripe-signature header' });
      return;
    }

    // Get raw body
    const payload = request.body;

    try {
      this.logger.log('Stripe webhook received, processing...');
      this.logger.log('Event type:', payload?.type);

      await this.subscriptionService.handleStripeWebhook(payload);
      this.logger.log('Stripe webhook processed successfully');

      response.status(200).json({ received: true });
    } catch (error) {
      this.logger.error('Stripe webhook processing failed:', error.message);
      this.logger.error('Error stack:', error.stack);
      response.status(400).json({ error: error.message });
    }
  }

  @Post('test')
  @Public()
  async testWebhook(
    @Req() request: Request,
    @Res() response: Response,
  ): Promise<void> {
    this.logger.log('Test webhook received');
    this.logger.log('Headers:', JSON.stringify(request.headers, null, 2));
    this.logger.log('Body:', JSON.stringify(request.body, null, 2));

    response.status(200).json({
      message: 'Test webhook received successfully',
      headers: request.headers,
      body: request.body
    });
  }
}

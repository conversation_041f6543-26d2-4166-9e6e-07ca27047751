import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { TenantGuard } from '../../auth/guards/tenant.guard';
import { CreditManagementService } from '../services/credit-management.service';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import {
  PurchaseCreditsDto,
  AddCreditsDto,
  UseCreditsDto,
  UpdateAutoRechargeDto,
  GetCreditHistoryDto,
  CreditBalanceResponseDto,
  CreditHistoryResponseDto,
  FeatureCostResponseDto,
  CreditUsageCheckResponseDto,
  CreditPackageResponseDto,
} from '../dto/credit.dto';
import { ConfigService } from '@nestjs/config';

@ApiTags('Credits')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, TenantGuard)
@Controller('credits')
export class CreditController {
  private readonly logger = new Logger(CreditController.name);

  constructor(
    private readonly creditService: CreditManagementService,
    private readonly tenantContext: TenantContextService,
    private readonly configService: ConfigService,
  ) {}

  @Get('balance')
  @ApiOperation({ summary: 'Get credit balance for organization' })
  @ApiResponse({
    status: 200,
    description: 'Credit balance retrieved successfully',
    type: CreditBalanceResponseDto,
  })
  async getCreditBalance(): Promise<CreditBalanceResponseDto> {
    const organizationId = this.tenantContext.getCurrentOrganization();
    if (!organizationId) {
      throw new BadRequestException('Organization ID not found');
    }

    const balance = await this.creditService.getCreditBalance(organizationId);
    
    return {
      balance: balance.balance,
      monthlyAllocation: balance.monthlyAllocation,
      totalEarned: balance.totalEarned,
      totalSpent: balance.totalSpent,
      lastAllocation: balance.lastAllocation,
      autoRecharge: {
        enabled: false, // Will be implemented with subscription service integration
        threshold: 10,
        amount: 100,
        hasPaymentMethod: false,
      },
    };
  }

  @Get('history')
  @ApiOperation({ summary: 'Get credit transaction history' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Number of transactions to return' })
  @ApiQuery({ name: 'offset', required: false, type: Number, description: 'Number of transactions to skip' })
  @ApiResponse({
    status: 200,
    description: 'Credit history retrieved successfully',
    type: CreditHistoryResponseDto,
  })
  async getCreditHistory(
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ): Promise<CreditHistoryResponseDto> {
    const organizationId = this.tenantContext.getCurrentOrganization();
    if (!organizationId) {
      throw new BadRequestException('Organization ID not found');
    }

    const history = await this.creditService.getCreditHistory(
      organizationId,
      limit || 50,
      offset || 0,
    );

    return {
      transactions: history.transactions,
      total: history.total,
      count: history.transactions.length,
      offset: offset || 0,
    };
  }

  @Get('features')
  @ApiOperation({ summary: 'Get all features with their credit costs' })
  @ApiResponse({
    status: 200,
    description: 'Feature costs retrieved successfully',
    type: [FeatureCostResponseDto],
  })
  async getFeatureCosts(): Promise<FeatureCostResponseDto[]> {
    const features = this.creditService.getAllFeatureCosts();
    
    return Object.entries(features).map(([key, feature]) => ({
      name: key,
      creditCost: feature.creditCost,
      description: feature.description,
      category: feature.category,
    }));
  }

  @Get('features/category/:category')
  @ApiOperation({ summary: 'Get features by category with their credit costs' })
  @ApiParam({ name: 'category', description: 'Feature category' })
  @ApiResponse({
    status: 200,
    description: 'Features by category retrieved successfully',
    type: [FeatureCostResponseDto],
  })
  async getFeaturesByCategory(
    @Param('category') category: string,
  ): Promise<FeatureCostResponseDto[]> {
    const features = this.creditService.getFeaturesByCategory(category);
    
    return features.map(feature => ({
      name: feature.name,
      creditCost: feature.creditCost,
      description: feature.description,
      category: feature.category,
    }));
  }

  @Get('check/:featureName')
  @ApiOperation({ summary: 'Check if organization has credits for a specific feature' })
  @ApiParam({ name: 'featureName', description: 'Name of the feature to check' })
  @ApiResponse({
    status: 200,
    description: 'Credit check completed successfully',
    type: CreditUsageCheckResponseDto,
  })
  async checkCreditsForFeature(
    @Param('featureName') featureName: string,
  ): Promise<CreditUsageCheckResponseDto> {
    const organizationId = this.tenantContext.getCurrentOrganization();
    if (!organizationId) {
      throw new BadRequestException('Organization ID not found');
    }

    const result = await this.creditService.hasCreditsForFeature(organizationId, featureName);
    const featureCost = this.creditService.getFeatureCost(featureName);

    return {
      hasCredits: result.hasCredits,
      currentBalance: result.currentBalance,
      requiredCredits: result.requiredCredits,
      feature: featureCost ? {
        name: featureName,
        creditCost: featureCost.creditCost,
        description: featureCost.description,
        category: featureCost.category,
      } : {
        name: featureName,
        creditCost: 0,
        description: 'Feature not found or free',
        category: 'basic',
      },
    };
  }

  @Post('use')
  @ApiOperation({ summary: 'Use credits for a feature' })
  @ApiResponse({
    status: 200,
    description: 'Credits used successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Insufficient credits or invalid feature',
  })
  async useCredits(@Body() useCreditsDto: UseCreditsDto) {
    const organizationId = this.tenantContext.getCurrentOrganization();
    if (!organizationId) {
      throw new BadRequestException('Organization ID not found');
    }

    const result = await this.creditService.deductCreditsForFeature(
      organizationId,
      useCreditsDto.featureName,
      useCreditsDto.transactionId,
    );

    if (!result.success) {
      throw new BadRequestException(result.message);
    }

    return {
      success: true,
      message: result.message,
      newBalance: result.newBalance,
      transaction: result.transaction,
    };
  }

  @Post('allocate-monthly')
  @ApiOperation({ summary: 'Allocate monthly credits based on subscription tier' })
  @ApiResponse({
    status: 200,
    description: 'Monthly credits allocated successfully',
  })
  async allocateMonthlyCredits() {
    const organizationId = this.tenantContext.getCurrentOrganization();
    if (!organizationId) {
      throw new BadRequestException('Organization ID not found');
    }

    const result = await this.creditService.allocateMonthlyCredits(organizationId);

    return {
      success: result.success,
      creditsAllocated: result.creditsAllocated,
      newBalance: result.newBalance,
      transaction: result.transaction,
    };
  }

  @Get('packages')
  @ApiOperation({ summary: 'Get available credit packages for purchase' })
  @ApiResponse({
    status: 200,
    description: 'Credit packages retrieved successfully',
    type: [CreditPackageResponseDto],
  })
  async getCreditPackages(): Promise<CreditPackageResponseDto[]> {
    const config = this.configService.get('featureCosts.creditPackages');
    
    if (!config) {
      return [];
    }

    return Object.entries(config).map(([name, pkg]: [string, any]) => ({
      name,
      credits: pkg.credits,
      price: pkg.price,
      bonus: pkg.bonus,
      totalCredits: pkg.credits + pkg.bonus,
      description: pkg.description,
      pricePerCredit: pkg.price / (pkg.credits + pkg.bonus),
    }));
  }

  @Post('purchase')
  @ApiOperation({ summary: 'Create checkout session for credit purchase' })
  @ApiResponse({
    status: 200,
    description: 'Checkout session created successfully',
  })
  async purchaseCredits(@Body() purchaseDto: PurchaseCreditsDto) {
    const organizationId = this.tenantContext.getCurrentOrganization();
    if (!organizationId) {
      throw new BadRequestException('Organization ID not found');
    }

    try {
      const result = await this.creditService.createCreditPurchaseCheckout(
        organizationId,
        purchaseDto.package,
        purchaseDto.successUrl,
        purchaseDto.cancelUrl,
      );

      return {
        success: true,
        sessionUrl: result.sessionUrl,
        package: result.packageInfo,
        organizationId,
      };
    } catch (error) {
      this.logger.error('Failed to create credit purchase checkout:', error);
      throw new BadRequestException(
        `Failed to create checkout session: ${error.message}`,
      );
    }
  }

  @Post('add-manual')
  @ApiOperation({ summary: 'Manually add credits (temporary admin endpoint)' })
  @ApiResponse({
    status: 200,
    description: 'Credits added successfully',
  })
  async addCreditsManually(@Body() addCreditsDto: AddCreditsDto) {
    const organizationId = this.tenantContext.getCurrentOrganization();
    if (!organizationId) {
      throw new BadRequestException('Organization ID not found');
    }

    try {
      const result = await this.creditService.addCredits(
        organizationId,
        addCreditsDto.amount,
        addCreditsDto.type,
        addCreditsDto.description,
        addCreditsDto.transactionId,
      );

      return {
        success: result.success,
        creditsAdded: result.creditsAllocated,
        newBalance: result.newBalance,
        transaction: result.transaction,
      };
    } catch (error) {
      this.logger.error('Failed to add credits manually:', error);
      throw new BadRequestException(
        `Failed to add credits: ${error.message}`,
      );
    }
  }

  @Put('auto-recharge')
  @ApiOperation({ summary: 'Update auto-recharge settings' })
  @ApiResponse({
    status: 200,
    description: 'Auto-recharge settings updated successfully',
  })
  async updateAutoRecharge(@Body() autoRechargeDto: UpdateAutoRechargeDto) {
    const organizationId = this.tenantContext.getCurrentOrganization();
    if (!organizationId) {
      throw new BadRequestException('Organization ID not found');
    }

    // This will be implemented when we integrate auto-recharge functionality
    // For now, return a placeholder response
    return {
      message: 'Auto-recharge functionality will be implemented in the next phase',
      settings: autoRechargeDto,
    };
  }
}

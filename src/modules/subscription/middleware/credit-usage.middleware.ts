import { Injectable, NestMiddleware, BadRequestException, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { CreditManagementService } from '../services/credit-management.service';
import { SubscriptionService } from '../services/subscription.service';
import { SubscriptionTier } from '../enums/subscription-tier.enum';

/**
 * Middleware to check and deduct credits for feature usage
 * This replaces the old usage tracking middleware with credit-based system
 */
@Injectable()
export class CreditUsageMiddleware implements NestMiddleware {
  private readonly logger = new Logger(CreditUsageMiddleware.name);

  constructor(
    private readonly tenantContext: TenantContextService,
    private readonly creditService: CreditManagementService,
    private readonly subscriptionService: SubscriptionService,
  ) {}

  async use(req: Request, res: Response, next: NextFunction) {
    this.logger.debug(`Credit middleware called for: ${req.method} ${req.path}`);
    this.logger.debug(`Full URL: ${req.url}`);
    this.logger.debug(`Original URL: ${req.originalUrl}`);

    const organizationId = this.tenantContext.getCurrentOrganization();
    this.logger.debug(`Organization ID: ${organizationId}`);

    if (!organizationId) {
      this.logger.debug('No organization ID found, skipping credit check');
      return next();
    }

    try {
      // Get current subscription
      const subscription = await this.subscriptionService.getSubscription(organizationId);
      
      // Skip for admin tier (unlimited access)
      if (subscription.tier === SubscriptionTier.LAW_FIRM) {
        return next();
      }

      // Determine feature based on URL path and method
      const featureName = this.getFeatureFromRequest(req);
      this.logger.debug(`Feature detected: ${featureName} for ${req.method} ${req.path}`);

      if (!featureName) {
        // Not a tracked feature
        this.logger.debug('No feature detected, skipping credit check');
        return next();
      }

      // Check if organization has sufficient credits
      const creditCheck = await this.creditService.hasCreditsForFeature(
        organizationId,
        featureName,
      );

      if (!creditCheck.hasCredits) {
        const featureCost = this.creditService.getFeatureCost(featureName);
        const costDescription = featureCost ? featureCost.name : featureName;
        
        throw new BadRequestException(
          `Insufficient credits for ${costDescription}. Required: ${creditCheck.requiredCredits}, Available: ${creditCheck.currentBalance}. Please purchase more credits or upgrade your plan.`
        );
      }

      // Deduct credits for the feature usage
      const transactionId = this.generateTransactionId(req);
      const result = await this.creditService.deductCreditsForFeature(
        organizationId,
        featureName,
        transactionId,
      );

      if (!result.success) {
        throw new BadRequestException(result.message);
      }

      // Log the credit usage
      this.logger.log(
        `Credits deducted: ${result.transaction.amount} for ${featureName} (Organization: ${organizationId}, New Balance: ${result.newBalance})`
      );

      // Add credit info to response headers for client awareness
      res.setHeader('X-Credits-Used', Math.abs(result.transaction.amount));
      res.setHeader('X-Credits-Remaining', result.newBalance);
      res.setHeader('X-Feature-Used', featureName);

      next();
    } catch (error) {
      next(error);
    }
  }

  /**
   * Determine the feature name based on the request path and method
   */
  private getFeatureFromRequest(req: Request): string | null {
    // Use originalUrl to get the full path including /api prefix
    const path = (req.originalUrl || req.url || req.path).toLowerCase();
    const method = req.method.toUpperCase();

    this.logger.debug(`Analyzing path: ${path} with method: ${method}`);

    // Document operations - Only charge for AI-powered features
    if (path.includes('/documents')) {
      // AI-powered operations that should consume credits
      if (path.includes('/analyze')) {
        return 'advanced_analysis';
      }
      if (path.includes('/compare')) {
        return 'enhanced_comparison';
      }
      if (path.includes('/redact')) {
        return 'redaction_automation';
      }
      if (path.includes('/privilege')) {
        return 'privilege_detection';
      }
      if (path.includes('/bulk-upload')) {
        return 'bulk_upload';
      }
      // Note: Basic document upload, view, update, delete are now FREE
    }

    // Chat operations - Only charge for AI responses
    if (path.includes('/chat')) {
      // Only charge for AI-powered chat messages, not session management
      if (path.includes('/messages') && method === 'POST') {
        return 'chat'; // AI response generation
      }
      // Note: Creating sessions, viewing messages, etc. are now FREE
    }

    // Legal research operations - All analysis operations consume credits
    if (path.includes('/legal-research') || path.includes('/citation-analysis')) {
      // All citation analysis operations are AI-powered and should consume credits
      if (path.includes('/analyze')) {
        if (path.includes('/enhanced') || path.includes('/impact') || path.includes('/precedent') || path.includes('/network')) {
          return 'enhanced_citation_analysis';
        }
        return 'basic_citation_analysis'; // For basic analysis
      }
      if (path.includes('/relationships') || path.includes('/impact') || path.includes('/precedent') || path.includes('/network')) {
        return 'enhanced_citation_analysis';
      }
      // Note: Only AI-powered analysis operations consume credits
    }

    // Document automation operations - AI-powered features consume credits
    if (path.includes('/documents/automation')) {
      if (path.includes('/ai-assisted-drafting')) {
        return 'ai_assisted_drafting';
      }
      if (path.includes('/generate-related-documents')) {
        return 'template_generation';
      }
      if (path.includes('/clause-intelligence')) {
        return 'clause_intelligence';
      }
      if (path.includes('/build-clause-library')) {
        return 'clause_library_automation';
      }
    }

    // Clause library operations - AI operations consume credits
    if (path.includes('/clause-library')) {
      if (path.includes('/identify')) {
        return 'clause_identification';
      }
      if (path.includes('/generate-template')) {
        return 'template_generation';
      }
      // Note: Creating and viewing templates are FREE
    }

    // Document comparison operations - AI comparisons consume credits
    if (path.includes('/comparison') || path.includes('/compare')) {
      if (path.includes('/enhanced') || path.includes('/section-references') || path.includes('/summary')) {
        return 'enhanced_comparison';
      }
      return 'basic_comparison';
    }

    // Negotiation playbook operations - AI analysis consumes credits
    if (path.includes('/negotiation-playbook')) {
      return 'negotiation_playbook';
    }

    // Compliance auditor operations - AI auditing consumes credits
    if (path.includes('/compliance') && path.includes('/audit')) {
      return 'compliance_audit';
    }

    // Deposition operations - Only charge for AI-powered features
    if (path.includes('/deposition')) {
      // AI-powered operations that should consume credits
      if (path.includes('/generate-questions')) {
        return 'ai_question_generation';
      }
      if (path.includes('/analyze-transcript')) {
        return 'deposition_analysis';
      }
      // Note: Creating and viewing depositions are FREE
    }

    // Contract operations - Only charge for AI-powered features
    if (path.includes('/contract')) {
      if (path.includes('/playbook') && path.includes('/analyze')) {
        return 'playbook_analysis'; // Only charge for AI analysis, not CRUD operations
      }
      if (path.includes('/risk-score')) {
        return 'contract_risk_scoring';
      }
      if (path.includes('/template')) {
        return 'template_generation';
      }
    }

    // Negotiation operations
    if (path.includes('/negotiation')) {
      if (path.includes('/simulate')) {
        return 'negotiation_simulator';
      }
      if (path.includes('/training')) {
        return 'negotiation_training';
      }
    }

    // AI operations
    if (path.includes('/ai')) {
      if (path.includes('/draft')) {
        return 'ai_assisted_drafting';
      }
      if (path.includes('/clause')) {
        return 'clause_intelligence';
      }
      if (path.includes('/generate')) {
        return 'related_document_generation';
      }
    }

    // Analytics operations
    if (path.includes('/analytics')) {
      if (path.includes('/advanced')) {
        return 'advanced_analytics';
      }
      if (path.includes('/predictive')) {
        return 'predictive_analytics';
      }
      if (path.includes('/risk-forecast')) {
        return 'risk_forecasting';
      }
      if (path.includes('/trend')) {
        return 'trend_analysis';
      }
    }

    // Organization operations
    if (path.includes('/organization')) {
      if (path.includes('/advanced')) {
        return 'advanced_document_organization';
      }
      return 'document_organization';
    }

    // Collaboration operations - Only session creation consumes credits
    if (path.includes('/collaboration')) {
      if (path.includes('/sessions') && method === 'POST' && !path.includes('/join') && !path.includes('/leave')) {
        return 'real_time_collaboration';
      }
      // Note: Joining, leaving, viewing sessions are FREE
    }

    // Workflow operations
    if (path.includes('/workflow')) {
      return 'workflow_management';
    }

    // Export operations
    if (path.includes('/export')) {
      return 'data_export';
    }

    // No fallback charging - only explicitly defined AI operations consume credits
    return null; // No credits required for this operation
  }

  /**
   * Generate a unique transaction ID for tracking
   */
  private generateTransactionId(req: Request): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    const method = req.method.toLowerCase();
    const pathHash = req.path.replace(/[^a-zA-Z0-9]/g, '').substring(0, 10);
    
    return `${method}_${pathHash}_${timestamp}_${random}`;
  }
}

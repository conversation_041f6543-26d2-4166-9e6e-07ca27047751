import { Injectable, NestMiddleware, BadRequestException } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { SubscriptionService } from '../services/subscription.service';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { TierLimits, SubscriptionTier } from '../enums/subscription-tier.enum';
import { Reflector } from '@nestjs/core';
import { SKIP_SUBSCRIPTION_CHECK } from '../decorators/skip-subscription-check.decorator';

@Injectable()
export class UsageTrackingMiddleware implements NestMiddleware {
  private readonly reflector: Reflector;

  constructor(
    private readonly subscriptionService: SubscriptionService,
    private readonly tenantContext: TenantContextService,
  ) {
    this.reflector = new Reflector();
  }

  async use(req: Request, res: Response, next: NextFunction) {
    const organizationId = this.tenantContext.getCurrentOrganization();
    if (!organizationId) {
      return next();
    }

    // Skip usage tracking for non-resource creation operations
    if (req.method !== 'POST') {
      return next();
    }

    try {
      // Get current subscription
      const subscription = await this.subscriptionService.getSubscription(organizationId);
      
      // Skip for admin tier
      if (subscription.tier === SubscriptionTier.LAW_FIRM) {
        return next();
      }

      // Determine resource type based on URL path
      let resourceType: 'documentLimit' | 'analysisPerMonth' | 'features';
      
      if (req.path.includes('/documents')) {
        resourceType = 'documentLimit';
      } else if (req.path.includes('/chat/sessions') || req.path.includes('/chat/messages')) {
        resourceType = 'analysisPerMonth';
      } else {
        // Not a tracked resource
        return next();
      }
      
      // Get current count
      const currentCount = await this.subscriptionService.getCurrentUsage(
        organizationId,
        resourceType,
      );
      
      // Get limit for the subscription tier
      const limit = TierLimits[subscription.tier][resourceType];
      
      if (currentCount >= limit && limit !== -1) {
        throw new BadRequestException(
          `You have reached the ${resourceType.replace('_', ' ')} limit (${limit}) for your subscription tier. Please upgrade your plan to upload more documents.`
        );
      }
      
      // Increment usage counter
      await this.subscriptionService.incrementUsage(organizationId, resourceType);
      
      next();
    } catch (error) {
      next(error);
    }
  }
}

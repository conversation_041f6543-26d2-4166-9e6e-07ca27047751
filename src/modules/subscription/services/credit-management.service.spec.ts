import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { CreditManagementService } from './credit-management.service';
import { SubscriptionRepository } from '../repositories/subscription.repository';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { SubscriptionTier } from '../enums/subscription-tier.enum';

describe('CreditManagementService', () => {
  let service: CreditManagementService;
  let subscriptionRepository: jest.Mocked<SubscriptionRepository>;
  let tenantContext: jest.Mocked<TenantContextService>;
  let configService: jest.Mocked<ConfigService>;

  const mockSubscription = {
    organizationId: 'test-org',
    tier: SubscriptionTier.LAWYER,
    creditBalance: 100,
    monthlyCreditsAllocation: 500,
    totalCreditsEarned: 500,
    totalCreditsSpent: 400,
    lastCreditAllocation: new Date(),
    creditHistory: [],
    autoRecharge: {
      enabled: false,
      threshold: 10,
      amount: 100,
    },
  };

  beforeEach(async () => {
    const mockSubscriptionRepository = {
      findByOrganizationId: jest.fn(),
      updateDirect: jest.fn(),
    };

    const mockTenantContext = {
      getCurrentOrganization: jest.fn(),
    };

    const mockConfigService = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreditManagementService,
        {
          provide: SubscriptionRepository,
          useValue: mockSubscriptionRepository,
        },
        {
          provide: TenantContextService,
          useValue: mockTenantContext,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<CreditManagementService>(CreditManagementService);
    subscriptionRepository = module.get(SubscriptionRepository);
    tenantContext = module.get(TenantContextService);
    configService = module.get(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getFeatureCost', () => {
    it('should return feature cost for existing feature', () => {
      const cost = service.getFeatureCost('advanced_analysis');
      expect(cost).toBeDefined();
      expect(cost.creditCost).toBe(5);
      expect(cost.name).toBe('Advanced Analysis');
    });

    it('should return null for non-existing feature', () => {
      const cost = service.getFeatureCost('non_existing_feature');
      expect(cost).toBeNull();
    });
  });

  describe('hasCreditsForFeature', () => {
    it('should return true when user has sufficient credits', async () => {
      subscriptionRepository.findByOrganizationId.mockResolvedValue(mockSubscription as any);

      const result = await service.hasCreditsForFeature('test-org', 'advanced_analysis');

      expect(result.hasCredits).toBe(true);
      expect(result.currentBalance).toBe(100);
      expect(result.requiredCredits).toBe(5);
    });

    it('should return false when user has insufficient credits', async () => {
      const lowCreditSubscription = { ...mockSubscription, creditBalance: 2 };
      subscriptionRepository.findByOrganizationId.mockResolvedValue(lowCreditSubscription as any);

      const result = await service.hasCreditsForFeature('test-org', 'advanced_analysis');

      expect(result.hasCredits).toBe(false);
      expect(result.currentBalance).toBe(2);
      expect(result.requiredCredits).toBe(5);
    });

    it('should return true for free features', async () => {
      subscriptionRepository.findByOrganizationId.mockResolvedValue(mockSubscription as any);

      const result = await service.hasCreditsForFeature('test-org', 'user_feedback');

      expect(result.hasCredits).toBe(true);
      expect(result.currentBalance).toBe(100);
      expect(result.requiredCredits).toBe(0);
    });
  });

  describe('deductCreditsForFeature', () => {
    it('should successfully deduct credits for valid feature usage', async () => {
      subscriptionRepository.findByOrganizationId.mockResolvedValue(mockSubscription as any);
      subscriptionRepository.updateDirect.mockResolvedValue({} as any);

      const result = await service.deductCreditsForFeature('test-org', 'advanced_analysis');

      expect(result.success).toBe(true);
      expect(result.newBalance).toBe(95);
      expect(result.transaction.amount).toBe(-5);
      expect(subscriptionRepository.updateDirect).toHaveBeenCalledWith(
        { organizationId: 'test-org' },
        expect.objectContaining({
          $set: expect.objectContaining({
            creditBalance: 95,
            totalCreditsSpent: 405,
          }),
          $push: expect.objectContaining({
            creditHistory: expect.any(Object),
          }),
        })
      );
    });

    it('should fail when insufficient credits', async () => {
      const lowCreditSubscription = { ...mockSubscription, creditBalance: 2 };
      subscriptionRepository.findByOrganizationId.mockResolvedValue(lowCreditSubscription as any);

      const result = await service.deductCreditsForFeature('test-org', 'advanced_analysis');

      expect(result.success).toBe(false);
      expect(result.newBalance).toBe(2);
      expect(result.message).toContain('Insufficient credits');
      expect(subscriptionRepository.updateDirect).not.toHaveBeenCalled();
    });

    it('should handle free features without deducting credits', async () => {
      subscriptionRepository.findByOrganizationId.mockResolvedValue(mockSubscription as any);

      const result = await service.deductCreditsForFeature('test-org', 'user_feedback');

      expect(result.success).toBe(true);
      expect(result.newBalance).toBe(100);
      expect(result.transaction.amount).toBe(0);
      expect(result.message).toContain('law_student');
      expect(subscriptionRepository.updateDirect).not.toHaveBeenCalled();
    });
  });

  describe('addCredits', () => {
    it('should successfully add credits', async () => {
      subscriptionRepository.findByOrganizationId.mockResolvedValue(mockSubscription as any);
      subscriptionRepository.updateDirect.mockResolvedValue({} as any);

      const result = await service.addCredits('test-org', 50, 'purchase', 'Credit purchase');

      expect(result.success).toBe(true);
      expect(result.creditsAllocated).toBe(50);
      expect(result.newBalance).toBe(150);
      expect(subscriptionRepository.updateDirect).toHaveBeenCalledWith(
        { organizationId: 'test-org' },
        expect.objectContaining({
          $set: expect.objectContaining({
            creditBalance: 150,
            totalCreditsEarned: 550,
          }),
          $push: expect.objectContaining({
            creditHistory: expect.any(Object),
          }),
        })
      );
    });

    it('should reject negative credit amounts', async () => {
      await expect(
        service.addCredits('test-org', -10, 'purchase', 'Invalid purchase')
      ).rejects.toThrow('Credit amount must be positive');
    });
  });

  describe('getCreditBalance', () => {
    it('should return credit balance information', async () => {
      subscriptionRepository.findByOrganizationId.mockResolvedValue(mockSubscription as any);

      const result = await service.getCreditBalance('test-org');

      expect(result.balance).toBe(100);
      expect(result.monthlyAllocation).toBe(500);
      expect(result.totalEarned).toBe(500);
      expect(result.totalSpent).toBe(400);
      expect(result.lastAllocation).toBeDefined();
    });
  });

  describe('getAllFeatureCosts', () => {
    it('should return all feature costs', () => {
      const features = service.getAllFeatureCosts();
      expect(Object.keys(features).length).toBeGreaterThan(0);
      expect(features['advanced_analysis']).toBeDefined();
      expect(features['advanced_analysis'].creditCost).toBe(5);
    });
  });

  describe('getFeaturesByCategory', () => {
    it('should return features filtered by category', () => {
      const advancedFeatures = service.getFeaturesByCategory('advanced');
      expect(advancedFeatures.length).toBeGreaterThan(0);
      expect(advancedFeatures.every(f => f.category === 'advanced')).toBe(true);
    });

    it('should return empty array for non-existing category', () => {
      const features = service.getFeaturesByCategory('non_existing');
      expect(features).toEqual([]);
    });
  });
});

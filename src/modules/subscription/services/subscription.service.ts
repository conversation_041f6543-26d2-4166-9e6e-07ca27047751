import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SubscriptionRepository } from '../repositories/subscription.repository';
import { StripeConfig } from '../config/stripe.config';
import { Subscription } from '../interfaces/subscription.interface';
import { SubscriptionTier, TierLimits } from '../enums/subscription-tier.enum';
import Stripe from 'stripe';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { UserService } from '../../auth/services/user.service';
import { CreditManagementService } from './credit-management.service';

@Injectable()
export class SubscriptionService {
  private readonly logger = new Logger(SubscriptionService.name);

  constructor(
    private readonly subscriptionRepository: SubscriptionRepository,
    private readonly stripeConfig: StripeConfig,
    private readonly userService: UserService,
    private readonly tenantContextService: TenantContextService,
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => CreditManagementService))
    private readonly creditManagementService: CreditManagementService,
  ) {}

  async createSubscription(
    organizationId: string,
    email: string,
    tier: SubscriptionTier,
  ): Promise<Subscription> {
    try {
      // For free tier, create a database record with a 14-day trial of Pro features
      if (tier === SubscriptionTier.LAW_STUDENT) {
        const trialEndDate = new Date();
        trialEndDate.setDate(trialEndDate.getDate() + 14); // 2 weeks trial

        const tierLimits = TierLimits[SubscriptionTier.LAW_STUDENT];
        const initialCredits = tierLimits.monthlyCredits || 0;

        const subscription = await this.subscriptionRepository.create({
          organizationId,
          tier: SubscriptionTier.LAW_STUDENT,
          status: 'active',
          stripeCustomerId: 'free_tier_' + organizationId, // Default value for free tier
          stripeSubscriptionId: 'free_tier_' + organizationId, // Default value for free tier
          currentPeriodStart: new Date(),
          currentPeriodEnd: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
          cancelAtPeriodEnd: false,
          features: [...tierLimits.features], // Base features
          trialTier: SubscriptionTier.LAWYER, // Trial of Pro tier
          trialEndDate: trialEndDate, // Trial ends in 2 weeks
          usageStats: {
            documentsProcessed: 0,
            analysisCount: 0,
            lastUpdated: new Date(),
          },
          // Initialize credit system
          creditBalance: initialCredits,
          monthlyCreditsAllocation: initialCredits,
          totalCreditsEarned: initialCredits,
          totalCreditsSpent: 0,
          lastCreditAllocation: new Date(),
          creditHistory:
            initialCredits > 0
              ? [
                  {
                    type: 'allocation',
                    amount: initialCredits,
                    balance: initialCredits,
                    timestamp: new Date(),
                    description: `Initial credit allocation for ${SubscriptionTier.LAW_STUDENT} tier`,
                  },
                ]
              : [],
          autoRecharge: {
            enabled: false,
            threshold: 10,
            amount: 100,
          },
        });
        return subscription;
      }

      // Create Stripe customer
      const customer = await this.stripeConfig.createCustomer(
        email,
        organizationId,
      );

      // Get price ID for the selected tier
      const priceId = this.stripeConfig.getPriceIdForTier(tier);

      // Create Stripe subscription
      const stripeSubscription = await this.stripeConfig.createSubscription(
        customer.id,
        priceId,
      );

      // Create subscription record in our database
      const tierLimits = TierLimits[tier];
      const initialCredits = tierLimits.monthlyCredits || 0;

      const subscription = await this.subscriptionRepository.create({
        organizationId,
        tier,
        status: 'inactive', // Will be updated when payment is confirmed
        stripeCustomerId: customer.id,
        stripeSubscriptionId: stripeSubscription.id,
        currentPeriodStart: new Date(
          stripeSubscription.current_period_start * 1000,
        ),
        currentPeriodEnd: new Date(
          stripeSubscription.current_period_end * 1000,
        ),
        cancelAtPeriodEnd: false,
        features: tierLimits.features,
        usageStats: {
          documentsProcessed: 0,
          analysisCount: 0,
          lastUpdated: new Date(),
        },
        // Initialize credit system
        creditBalance: initialCredits,
        monthlyCreditsAllocation: initialCredits,
        totalCreditsEarned: initialCredits,
        totalCreditsSpent: 0,
        lastCreditAllocation: new Date(),
        creditHistory:
          initialCredits > 0
            ? [
                {
                  type: 'allocation',
                  amount: initialCredits,
                  balance: initialCredits,
                  timestamp: new Date(),
                  description: `Initial credit allocation for ${tier} tier`,
                },
              ]
            : [],
        autoRecharge: {
          enabled: false,
          threshold: 10,
          amount: 100,
        },
      });

      return subscription;
    } catch (error) {
      throw new BadRequestException(
        `Failed to create subscription: ${error.message}`,
      );
    }
  }

  async getSubscription(organizationId: string): Promise<Subscription> {
    const subscription = await this.subscriptionRepository.findByOrganizationId(
      organizationId,
    );
    if (!subscription) {
      // Instead of throwing an error, create a default free subscription
      this.logger.log(
        `No subscription found for organization ${organizationId}. Creating a default free subscription.`,
      );

      // Try to get the user's email from the current context
      let email = '<EMAIL>'; // Default fallback

      try {
        // Get the current user ID from the tenant context
        const userId = this.tenantContextService.getCurrentUserId();

        if (userId) {
          // Find the user by ID and organization ID
          const user = await this.userService.findById(userId, organizationId);

          if (user && user.email) {
            email = user.email;
            this.logger.log(
              `Using email ${email} for the default subscription.`,
            );
          }
        } else {
          // If no user ID in context, try to find any user in the organization
          const users = await this.userService.findByOrganization(
            organizationId,
          );

          if (users && users.length > 0 && users[0].email) {
            email = users[0].email;
            this.logger.log(
              `Using organization member email ${email} for the default subscription.`,
            );
          }
        }
      } catch (error) {
        this.logger.warn(
          `Could not retrieve user email: ${error.message}. Using default email.`,
        );
      }

      return this.createSubscription(
        organizationId,
        email,
        SubscriptionTier.LAW_STUDENT,
      );
    }
    return subscription;
  }

  /**
   * Get subscription by organization ID without throwing an exception if not found
   * Used by rate limiting and other non-critical services
   */
  async getSubscriptionByOrganizationId(
    organizationId: string,
  ): Promise<Subscription | null> {
    return this.subscriptionRepository.findByOrganizationId(organizationId);
  }

  async updateSubscriptionStatus(
    organizationId: string,
    status: string,
  ): Promise<Subscription> {
    const subscription =
      await this.subscriptionRepository.updateSubscriptionStatus(
        organizationId,
        status,
      );
    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }
    return subscription;
  }

  async cancelSubscription(organizationId: string): Promise<Subscription> {
    const subscription = await this.getSubscription(organizationId);

    // If it's a free tier subscription, just update the status
    if (subscription.tier === SubscriptionTier.LAW_STUDENT) {
      return this.updateSubscriptionStatus(organizationId, 'canceled');
    }

    // For paid subscriptions, cancel in Stripe
    if (subscription.stripeSubscriptionId) {
      await this.stripeConfig
        .getStripeInstance()
        .subscriptions.update(subscription.stripeSubscriptionId, {
          cancel_at_period_end: true,
        });
    }

    // Update our database
    subscription.cancelAtPeriodEnd = true;
    await subscription.save();

    return subscription;
  }

  async handleStripeWebhook(event: Stripe.Event): Promise<void> {
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session;
        const organizationId = session.client_reference_id;
        const subscriptionId = session.subscription as string;

        if (!organizationId) {
          throw new BadRequestException(
            'Missing client_reference_id in checkout session',
          );
        }

        // Check if this is a credit purchase (one-time payment)
        if (session.mode === 'payment' && session.metadata?.type === 'credit_purchase') {
          const packageName = session.metadata.packageName;
          if (!packageName) {
            throw new BadRequestException(
              'Missing packageName in credit purchase session metadata',
            );
          }

          try {
            await this.creditManagementService.processCreditPurchase(
              organizationId,
              packageName,
              session.id,
            );
            this.logger.log(
              `Credit purchase processed successfully for organization ${organizationId}, package: ${packageName}`,
            );
          } catch (error) {
            this.logger.error(
              `Failed to process credit purchase: ${error.message}`,
            );
            throw error;
          }
          break;
        }

        if (!subscriptionId) {
          throw new BadRequestException(
            'Missing subscription ID in checkout session',
          );
        }

        // Get the subscription from Stripe
        const subscription = await this.stripeConfig.retrieveSubscription(
          subscriptionId,
        );

        // Update our database with the subscription details
        const existingSubscription =
          await this.subscriptionRepository.findByOrganizationIdDirect(
            organizationId,
          );
        const tier = this.getTierFromPriceId(
          subscription.items.data[0]?.price?.id,
        );

        if (existingSubscription) {
          const oldTier = existingSubscription.tier;
          const newTier = tier || SubscriptionTier.LAWYER;

          // Update existing subscription
          await this.subscriptionRepository.updateDirect(
            { organizationId },
            {
              $set: {
                tier: newTier,
                stripeCustomerId: subscription.customer,
                stripeSubscriptionId: subscriptionId,
                status: this.mapStripeStatusToAppStatus(subscription.status),
                currentPeriodStart: new Date(
                  subscription.current_period_start * 1000,
                ),
                currentPeriodEnd: new Date(
                  subscription.current_period_end * 1000,
                ),
                cancelAtPeriodEnd: subscription.cancel_at_period_end,
                features: TierLimits[newTier].features,
                monthlyCreditsAllocation: TierLimits[newTier].monthlyCredits || 0,
              },
              $unset: {
                trialTier: '',
                trialEndDate: '',
              },
            },
          );

          // If tier changed, allocate monthly credits for the new tier
          if (oldTier !== newTier) {
            const monthlyCredits = TierLimits[newTier].monthlyCredits || 0;
            if (monthlyCredits > 0) {
              await this.creditManagementService.addCreditsWebhook(
                organizationId,
                monthlyCredits,
                'allocation',
                `Monthly credit allocation for ${newTier} tier upgrade`,
              );

              this.logger.log(
                `Allocated ${monthlyCredits} credits for tier upgrade from ${oldTier} to ${newTier} for organization ${organizationId}`,
              );
            }
          }
        } else {
          // Create new subscription record
          const tier = this.getTierFromPriceId(
            subscription.items.data[0]?.price?.id,
          );

          const finalTier = tier || SubscriptionTier.LAWYER;
          const tierLimits = TierLimits[finalTier];
          const initialCredits = tierLimits.monthlyCredits || 0;

          await this.subscriptionRepository.createDirect({
            organizationId,
            tier: finalTier, // Default to PRO if tier can't be determined
            status: this.mapStripeStatusToAppStatus(subscription.status),
            stripeCustomerId: subscription.customer as string,
            stripeSubscriptionId: subscription.id,
            currentPeriodStart: new Date(
              subscription.current_period_start * 1000,
            ),
            currentPeriodEnd: new Date(subscription.current_period_end * 1000),
            cancelAtPeriodEnd: subscription.cancel_at_period_end,
            features: tierLimits.features,
            usageStats: {
              documentsProcessed: 0,
              analysisCount: 0,
              lastUpdated: new Date(),
            },
            // Initialize credit system
            creditBalance: initialCredits,
            monthlyCreditsAllocation: initialCredits,
            totalCreditsEarned: initialCredits,
            totalCreditsSpent: 0,
            lastCreditAllocation: new Date(),
            creditHistory:
              initialCredits > 0
                ? [
                    {
                      type: 'allocation',
                      amount: initialCredits,
                      balance: initialCredits,
                      timestamp: new Date(),
                      description: `Initial credit allocation for ${finalTier} tier`,
                    },
                  ]
                : [],
            autoRecharge: {
              enabled: false,
              threshold: 10,
              amount: 100,
            },
            // No trial fields for paid subscriptions
          });
        }

        break;
      }
      case 'customer.subscription.created':
      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription;
        const metadata = subscription.metadata || {};
        const organizationId = metadata.organizationId;

        if (!organizationId) {
          throw new BadRequestException(
            'Missing organizationId in subscription metadata',
          );
        }

        // Update subscription status in our database using direct method
        const status = this.mapStripeStatusToAppStatus(subscription.status);
        console.log('status', status);
        // If the subscription is canceled or past_due, check if we need to downgrade to FREE
        if (
          status === 'canceled' ||
          (status === 'past_due' && subscription.cancel_at_period_end)
        ) {
          await this.subscriptionRepository.updateDirect(
            { organizationId },
            {
              $set: {
                status,
                tier: SubscriptionTier.LAW_STUDENT,
                features: TierLimits[SubscriptionTier.LAW_STUDENT].features,
                cancelAtPeriodEnd: subscription.cancel_at_period_end,
              },
            },
          );
        } else {
          // For active subscriptions, just update the status
          await this.subscriptionRepository.updateDirect(
            { organizationId },
            {
              $set: {
                status,
                cancelAtPeriodEnd: subscription.cancel_at_period_end,
              },
            },
          );
        }
        break;
      }
      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription;
        const metadata = subscription.metadata || {};
        const organizationId = metadata.organizationId;

        if (!organizationId) {
          throw new BadRequestException(
            'Missing organizationId in subscription metadata',
          );
        }

        // Update subscription status in our database using direct method
        // When a subscription is deleted, downgrade to FREE tier
        await this.subscriptionRepository.updateDirect(
          { organizationId },
          {
            $set: {
              status: 'canceled',
              tier: SubscriptionTier.LAW_STUDENT,
              features: TierLimits[SubscriptionTier.LAW_STUDENT].features,
            },
          },
        );
        break;
      }
      default:
        // Ignore other event types
        break;
    }
  }

  // Method to manually update a subscription tier
  async updateSubscriptionTier(
    organizationId: string,
    tier: SubscriptionTier,
  ): Promise<Subscription> {
    const subscription = await this.getSubscriptionByOrganizationId(
      organizationId,
    );

    if (!subscription) {
      throw new NotFoundException(
        `Subscription not found for organization: ${organizationId}`,
      );
    }

    // If downgrading to FREE tier and there's an active Stripe subscription, cancel it
    if (
      tier === SubscriptionTier.LAW_STUDENT &&
      subscription.tier !== SubscriptionTier.LAW_STUDENT &&
      subscription.stripeSubscriptionId &&
      !subscription.stripeSubscriptionId.startsWith('free_tier_')
    ) {
      this.logger.log(
        `Canceling Stripe subscription ${subscription.stripeSubscriptionId} for organization ${organizationId}`,
      );

      try {
        // Cancel the subscription in Stripe
        await this.stripeConfig
          .getStripeInstance()
          .subscriptions.update(subscription.stripeSubscriptionId, {
            cancel_at_period_end: true,
          });

        // Mark as canceled at period end
        subscription.cancelAtPeriodEnd = true;
      } catch (error) {
        this.logger.error(
          `Failed to cancel Stripe subscription: ${error.message}`,
        );
        // Continue with the tier update even if Stripe cancellation fails
      }
    }

    // Update the tier and features
    subscription.tier = tier;
    subscription.features = TierLimits[tier].features;

    // If upgrading from FREE to a paid tier, remove trial fields
    if (tier !== SubscriptionTier.LAW_STUDENT) {
      subscription.trialTier = undefined;
      subscription.trialEndDate = undefined;
    }

    // Save the updated subscription
    await this.subscriptionRepository.update(
      subscription._id.toString(),
      subscription,
    );

    return subscription;
  }

  // Method to manually update a subscription's features based on its tier
  async updateSubscriptionFeatures(
    organizationId: string,
  ): Promise<Subscription> {
    const subscription = await this.getSubscriptionByOrganizationId(
      organizationId,
    );

    if (!subscription) {
      throw new NotFoundException(
        `Subscription not found for organization: ${organizationId}`,
      );
    }

    // Update the features based on the current tier
    subscription.features = TierLimits[subscription.tier].features;

    // If it's a paid subscription, remove trial fields
    if (subscription.tier !== SubscriptionTier.LAW_STUDENT) {
      subscription.trialTier = undefined;
      subscription.trialEndDate = undefined;
    }

    // Save the updated subscription
    await this.subscriptionRepository.update(
      subscription._id.toString(),
      subscription,
    );

    return subscription;
  }

  // Helper method to determine the subscription tier from a price ID
  private getTierFromPriceId(priceId: string): SubscriptionTier | null {
    if (!priceId) return null;

    // Check which tier this price ID belongs to
    const proTierPriceId = this.stripeConfig.getPriceIdForTier(
      SubscriptionTier.LAWYER,
    );
    const adminTierPriceId = this.stripeConfig.getPriceIdForTier(
      SubscriptionTier.LAW_FIRM,
    );

    if (priceId === proTierPriceId) {
      return SubscriptionTier.LAWYER;
    } else if (priceId === adminTierPriceId) {
      return SubscriptionTier.LAW_FIRM;
    }

    // If we can't determine the tier, default to PRO
    this.logger.warn(
      `Could not determine tier for price ID: ${priceId}. Defaulting to PRO.`,
    );
    return SubscriptionTier.LAWYER;
  }

  async updateUsageStats(
    organizationId: string,
    documentsProcessed: number,
    analysisCount: number,
  ): Promise<Subscription> {
    const subscription = await this.subscriptionRepository.updateUsageStats(
      organizationId,
      documentsProcessed,
      analysisCount,
    );
    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }
    return subscription;
  }

  async checkUsageLimits(organizationId: string): Promise<boolean> {
    const subscription = await this.getSubscription(organizationId);
    const limits = TierLimits[subscription.tier];

    if (limits.documentLimit === -1) {
      return true; // Unlimited
    }

    return subscription.usageStats.documentsProcessed < limits.documentLimit;
  }

  async createCheckoutSession(
    organizationId: string,
    tier: SubscriptionTier,
    successUrl: string,
    cancelUrl: string,
  ): Promise<{ sessionUrl: string }> {
    try {
      // Handle free tier downgrade without Stripe
      if (tier === SubscriptionTier.LAW_STUDENT) {
        // Directly downgrade to free tier
        await this.downgradeToFreeTier(organizationId);

        // Return success URL since no payment is needed
        return { sessionUrl: successUrl };
      }

      // Get price ID for the selected tier
      const priceId = this.stripeConfig.getPriceIdForTier(tier);

      if (!priceId) {
        throw new BadRequestException(`No price ID found for tier: ${tier}`);
      }

      // Create checkout session
      const session = await this.stripeConfig.createCheckoutSession(
        priceId,
        organizationId,
        successUrl,
        cancelUrl,
      );

      return { sessionUrl: session.url };
    } catch (error) {
      throw new BadRequestException(
        `Failed to create checkout session: ${error.message}`,
      );
    }
  }

  /**
   * Downgrade subscription to free tier
   */
  async downgradeToFreeTier(organizationId: string): Promise<void> {
    const subscription = await this.subscriptionRepository.findByOrganizationIdDirect(organizationId);
    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    // Cancel Stripe subscription if it exists
    if (subscription.stripeSubscriptionId && !subscription.stripeSubscriptionId.startsWith('free_tier_')) {
      try {
        await this.stripeConfig.getStripeInstance().subscriptions.update(
          subscription.stripeSubscriptionId,
          { cancel_at_period_end: true }
        );
        this.logger.log(`Stripe subscription ${subscription.stripeSubscriptionId} marked for cancellation`);
      } catch (error) {
        this.logger.error(`Failed to cancel Stripe subscription: ${error.message}`);
        // Continue with downgrade even if Stripe cancellation fails
      }
    }

    // Update subscription to free tier
    await this.subscriptionRepository.updateDirect(
      { organizationId },
      {
        $set: {
          tier: SubscriptionTier.LAW_STUDENT,
          status: 'active',
          features: TierLimits[SubscriptionTier.LAW_STUDENT].features,
          monthlyCreditsAllocation: TierLimits[SubscriptionTier.LAW_STUDENT].monthlyCredits || 0,
          cancelAtPeriodEnd: false,
        },
      },
    );

    this.logger.log(`Subscription downgraded to free tier for organization ${organizationId}`);
  }

  // Usage tracking methods
  async trackDocumentUpload(organizationId: string): Promise<void> {
    await this.subscriptionRepository.incrementUsage(
      organizationId,
      'documentsProcessed',
      1,
    );
  }

  async trackAnalysis(organizationId: string): Promise<void> {
    await this.subscriptionRepository.incrementUsage(
      organizationId,
      'analysisCount',
      1,
    );
  }

  async getFeatureAvailability(
    organizationId: string,
    feature: string,
  ): Promise<boolean> {
    try {
      const subscription = await this.getSubscriptionByOrganizationId(
        organizationId,
      );

      // If no subscription is found, create a default FREE tier subscription
      if (!subscription) {
        // Auto-create a free subscription for the organization
        await this.createSubscription(
          organizationId,
          '<EMAIL>', // Placeholder email
          SubscriptionTier.LAW_STUDENT,
        );

        // Check if the requested feature is available in the FREE tier
        return TierLimits[SubscriptionTier.LAW_STUDENT].features.includes(feature);
      }

      // Check if the user is in a trial period
      const now = new Date();
      if (
        subscription.trialTier &&
        subscription.trialEndDate &&
        now < subscription.trialEndDate
      ) {
        // User is in trial period, check if feature is available in trial tier
        return TierLimits[subscription.trialTier].features.includes(feature);
      }

      // Check if the feature is available for the subscription tier
      return subscription.features.includes(feature);
    } catch (error) {
      // Log the error but don't fail the request
      console.error(`Error checking feature availability: ${error.message}`);

      // Default to FREE tier features if there's an error
      return TierLimits[SubscriptionTier.LAW_STUDENT].features.includes(feature);
    }
  }

  async resetMonthlyUsageCounts(organizationId: string): Promise<void> {
    await this.subscriptionRepository.resetUsageStats(organizationId, {
      analysisCount: 0,
      lastUpdated: new Date(),
    });
  }

  /**
   * Initialize credit system for existing subscriptions
   * This is a one-time migration method
   */
  async initializeCreditSystemForExistingSubscriptions(): Promise<void> {
    this.logger.log('Starting credit system initialization for existing subscriptions...');

    try {
      // Get all subscriptions that don't have credit fields initialized
      const subscriptions = await this.subscriptionRepository.findAll();

      for (const subscription of subscriptions) {
        // Skip if already has credit balance (already migrated)
        if (subscription.creditBalance !== undefined && subscription.creditBalance > 0) {
          continue;
        }

        // Determine monthly credit allocation based on tier
        let monthlyCredits = 0;
        switch (subscription.tier) {
          case SubscriptionTier.LAW_STUDENT:
            monthlyCredits = 50;
            break;
          case SubscriptionTier.LAWYER:
            monthlyCredits = 500;
            break;
          case SubscriptionTier.LAW_FIRM:
            monthlyCredits = 2000;
            break;
          default:
            monthlyCredits = 50; // Default to free tier
        }

        // Initialize credit fields
        await this.subscriptionRepository.updateDirect(
          { organizationId: subscription.organizationId },
          {
            $set: {
              creditBalance: monthlyCredits, // Start with full monthly allocation
              monthlyCreditsAllocation: monthlyCredits,
              totalCreditsEarned: monthlyCredits,
              totalCreditsSpent: 0,
              lastCreditAllocation: new Date(),
              autoRecharge: {
                enabled: false,
                threshold: 10,
                amount: 100,
                stripePaymentMethodId: null,
              },
            },
            $push: {
              creditHistory: {
                type: 'allocation',
                amount: monthlyCredits,
                balance: monthlyCredits,
                timestamp: new Date(),
                description: `Initial credit allocation for ${subscription.tier} tier`,
              },
            },
          },
        );

        this.logger.log(
          `Initialized credits for organization ${subscription.organizationId}: ${monthlyCredits} credits (${subscription.tier} tier)`,
        );
      }

      this.logger.log('Credit system initialization completed successfully');
    } catch (error) {
      this.logger.error('Failed to initialize credit system:', error);
      throw error;
    }
  }

  async getCurrentUsage(
    organizationId: string,
    resourceType: 'documentLimit' | 'analysisPerMonth' | 'features',
  ): Promise<number> {
    const subscription = await this.getSubscription(organizationId);

    if (resourceType === 'documentLimit') {
      return subscription.usageStats.documentsProcessed;
    } else if (resourceType === 'analysisPerMonth') {
      return subscription.usageStats.analysisCount;
    }

    return 0;
  }

  async incrementUsage(
    organizationId: string,
    resourceType: 'documentLimit' | 'analysisPerMonth' | 'features',
  ): Promise<void> {
    if (resourceType === 'documentLimit') {
      await this.trackDocumentUpload(organizationId);
    } else if (resourceType === 'analysisPerMonth') {
      await this.trackAnalysis(organizationId);
    }
  }

  async canPerformOperation(
    organizationId: string,
    operation: string,
  ): Promise<boolean> {
    try {
      const subscription = await this.getSubscriptionByOrganizationId(
        organizationId,
      );

      // If no subscription exists, create a default free tier subscription
      if (!subscription) {
        await this.createSubscription(
          organizationId,
          '<EMAIL>', // Placeholder email
          SubscriptionTier.LAW_STUDENT,
        );

        // Check if operation is allowed in free tier
        return TierLimits[SubscriptionTier.LAW_STUDENT].features.includes(operation);
      }

      // Check if the user is in a trial period
      const now = new Date();
      if (
        subscription.trialTier &&
        subscription.trialEndDate &&
        now < subscription.trialEndDate
      ) {
        // User is in trial period, check if operation is available in trial tier
        if (TierLimits[subscription.trialTier].features.includes(operation)) {
          return true;
        }
      }

      // Check subscription status
      if (!['active', 'trialing'].includes(subscription.status)) {
        return false;
      }

      // Check if operation is allowed for current tier
      const allowedFeatures = TierLimits[subscription.tier].features;
      if (!allowedFeatures.includes(operation)) {
        return false;
      }

      // Check usage limits if applicable
      return this.checkUsageLimits(organizationId);
    } catch (error) {
      return false; // If subscription not found or other error, deny operation
    }
  }

  /**
   * Manually allocate monthly credits for a subscription (for fixing upgrades)
   */
  async allocateMonthlyCreditsManual(organizationId: string): Promise<any> {
    const subscription = await this.subscriptionRepository.findByOrganizationIdDirect(organizationId);
    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    const monthlyCredits = TierLimits[subscription.tier].monthlyCredits || 0;
    if (monthlyCredits === 0) {
      return {
        success: true,
        creditsAllocated: 0,
        newBalance: subscription.creditBalance,
        message: 'No monthly credits for this tier',
      };
    }

    // Use the credit management service to add credits
    return await this.creditManagementService.addCreditsWebhook(
      organizationId,
      monthlyCredits,
      'allocation',
      `Manual monthly credit allocation for ${subscription.tier} tier`,
    );
  }

  private mapStripeStatusToAppStatus(
    status: string,
  ): 'active' | 'inactive' | 'canceled' | 'past_due' | 'trialing' {
    switch (status) {
      case 'active':
        return 'active';
      case 'trialing':
        return 'trialing';
      case 'past_due':
        return 'past_due';
      case 'canceled':
        return 'canceled';
      case 'unpaid':
        return 'past_due'; // Map unpaid to past_due
      case 'incomplete':
        return 'inactive'; // Map incomplete to inactive
      case 'incomplete_expired':
        return 'inactive'; // Map incomplete_expired to inactive
      default:
        return 'inactive'; // Default to inactive for unknown statuses
    }
  }
}

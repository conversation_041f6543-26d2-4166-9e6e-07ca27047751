import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  Subscription,
  SubscriptionDocument,
  SUBSCRIPTION_MODEL,
} from '../schemas/subscription.schema';
import {
  SubscriptionTier,
  LegacyTierMapping,
  getTierDisplayName,
  getTierPrice,
} from '../enums/subscription-tier.enum';

@Injectable()
export class TierMigrationService {
  private readonly logger = new Logger(TierMigrationService.name);

  constructor(
    @InjectModel(SUBSCRIPTION_MODEL)
    private readonly subscriptionModel: Model<SubscriptionDocument>,
  ) {}

  /**
   * Migrate all existing subscriptions from legacy tier names to new legal profession tiers
   */
  async migrateLegacyTiers(): Promise<{
    success: boolean;
    migratedCount: number;
    errors: string[];
  }> {
    this.logger.log('Starting migration from legacy tier names to legal profession tiers');

    const errors: string[] = [];
    let migratedCount = 0;

    try {
      // Find all subscriptions with legacy tier names
      const legacySubscriptions = await this.subscriptionModel.find({
        tier: { $in: ['law_student', 'lawyer', 'law_firm'] },
      }).exec();

      this.logger.log(`Found ${legacySubscriptions.length} subscriptions with legacy tier names`);

      for (const subscription of legacySubscriptions) {
        try {
          const oldTier = subscription.tier;
          const newTier = LegacyTierMapping[oldTier];

          if (!newTier) {
            errors.push(`Unknown legacy tier: ${oldTier} for subscription ${subscription._id}`);
            continue;
          }

          // Update the subscription
          await this.subscriptionModel.updateOne(
            { _id: subscription._id },
            {
              $set: {
                tier: newTier,
                // Update metadata to reflect the migration
                'metadata.tierMigration': {
                  migratedAt: new Date(),
                  fromTier: oldTier,
                  toTier: newTier,
                  displayName: getTierDisplayName(newTier),
                },
              },
            },
          );

          migratedCount++;
          this.logger.log(`Migrated subscription ${subscription._id}: ${oldTier} → ${newTier}`);

        } catch (error) {
          const errorMsg = `Failed to migrate subscription ${subscription._id}: ${error.message}`;
          errors.push(errorMsg);
          this.logger.error(errorMsg);
        }
      }

      this.logger.log(`Migration completed. Migrated ${migratedCount} subscriptions`);

      return {
        success: errors.length === 0,
        migratedCount,
        errors,
      };

    } catch (error) {
      this.logger.error(`Migration failed: ${error.message}`);
      return {
        success: false,
        migratedCount,
        errors: [error.message],
      };
    }
  }

  /**
   * Update subscription pricing to reflect new tier structure
   */
  async updateSubscriptionPricing(): Promise<{
    success: boolean;
    updatedCount: number;
    errors: string[];
  }> {
    this.logger.log('Updating subscription pricing for new tier structure');

    const errors: string[] = [];
    let updatedCount = 0;

    try {
      // Get all active subscriptions
      const activeSubscriptions = await this.subscriptionModel.find({
        status: 'active',
      }).exec();

      for (const subscription of activeSubscriptions) {
        try {
          const tier = subscription.tier as SubscriptionTier;
          const newPrice = getTierPrice(tier);

          // Only update if price has changed
          if (subscription.pricing?.amount !== newPrice) {
            await this.subscriptionModel.updateOne(
              { _id: subscription._id },
              {
                $set: {
                  'pricing.amount': newPrice,
                  'pricing.currency': 'USD',
                  'pricing.interval': 'month',
                  'pricing.updatedAt': new Date(),
                },
              },
            );

            updatedCount++;
            this.logger.log(`Updated pricing for subscription ${subscription._id}: $${newPrice / 100}`);
          }

        } catch (error) {
          const errorMsg = `Failed to update pricing for subscription ${subscription._id}: ${error.message}`;
          errors.push(errorMsg);
          this.logger.error(errorMsg);
        }
      }

      this.logger.log(`Pricing update completed. Updated ${updatedCount} subscriptions`);

      return {
        success: errors.length === 0,
        updatedCount,
        errors,
      };

    } catch (error) {
      this.logger.error(`Pricing update failed: ${error.message}`);
      return {
        success: false,
        updatedCount,
        errors: [error.message],
      };
    }
  }

  /**
   * Validate all subscriptions have valid tier names
   */
  async validateTierNames(): Promise<{
    valid: boolean;
    invalidSubscriptions: Array<{
      id: string;
      tier: string;
      organizationId: string;
    }>;
  }> {
    this.logger.log('Validating subscription tier names');

    const validTiers = Object.values(SubscriptionTier);
    const invalidSubscriptions: Array<{
      id: string;
      tier: string;
      organizationId: string;
    }> = [];

    try {
      const allSubscriptions = await this.subscriptionModel.find({}).exec();

      for (const subscription of allSubscriptions) {
        if (!validTiers.includes(subscription.tier as SubscriptionTier)) {
          invalidSubscriptions.push({
            id: subscription._id.toString(),
            tier: subscription.tier,
            organizationId: subscription.organizationId,
          });
        }
      }

      const isValid = invalidSubscriptions.length === 0;
      
      if (isValid) {
        this.logger.log('All subscription tier names are valid');
      } else {
        this.logger.warn(`Found ${invalidSubscriptions.length} subscriptions with invalid tier names`);
      }

      return {
        valid: isValid,
        invalidSubscriptions,
      };

    } catch (error) {
      this.logger.error(`Validation failed: ${error.message}`);
      return {
        valid: false,
        invalidSubscriptions: [],
      };
    }
  }

  /**
   * Get migration statistics
   */
  async getMigrationStats(): Promise<{
    totalSubscriptions: number;
    tierDistribution: Record<string, number>;
    migratedSubscriptions: number;
    pendingMigration: number;
  }> {
    try {
      const totalSubscriptions = await this.subscriptionModel.countDocuments({});
      
      // Get tier distribution
      const tierAggregation = await this.subscriptionModel.aggregate([
        {
          $group: {
            _id: '$tier',
            count: { $sum: 1 },
          },
        },
      ]);

      const tierDistribution: Record<string, number> = {};
      tierAggregation.forEach(item => {
        tierDistribution[item._id] = item.count;
      });

      // Count migrated subscriptions (those with migration metadata)
      const migratedSubscriptions = await this.subscriptionModel.countDocuments({
        'metadata.tierMigration': { $exists: true },
      });

      // Count subscriptions that still need migration
      const pendingMigration = await this.subscriptionModel.countDocuments({
        tier: { $in: ['law_student', 'lawyer', 'law_firm'] },
      });

      return {
        totalSubscriptions,
        tierDistribution,
        migratedSubscriptions,
        pendingMigration,
      };

    } catch (error) {
      this.logger.error(`Failed to get migration stats: ${error.message}`);
      return {
        totalSubscriptions: 0,
        tierDistribution: {},
        migratedSubscriptions: 0,
        pendingMigration: 0,
      };
    }
  }

  /**
   * Rollback tier migration (for testing purposes)
   */
  async rollbackTierMigration(): Promise<{
    success: boolean;
    rolledBackCount: number;
    errors: string[];
  }> {
    this.logger.log('Rolling back tier migration');

    const errors: string[] = [];
    let rolledBackCount = 0;

    try {
      // Find all subscriptions that were migrated
      const migratedSubscriptions = await this.subscriptionModel.find({
        'metadata.tierMigration': { $exists: true },
      }).exec();

      for (const subscription of migratedSubscriptions) {
        try {
          const originalTier = subscription.metadata?.tierMigration?.fromTier;
          
          if (!originalTier) {
            errors.push(`No original tier found for subscription ${subscription._id}`);
            continue;
          }

          await this.subscriptionModel.updateOne(
            { _id: subscription._id },
            {
              $set: {
                tier: originalTier,
              },
              $unset: {
                'metadata.tierMigration': '',
              },
            },
          );

          rolledBackCount++;
          this.logger.log(`Rolled back subscription ${subscription._id}: ${subscription.tier} → ${originalTier}`);

        } catch (error) {
          const errorMsg = `Failed to rollback subscription ${subscription._id}: ${error.message}`;
          errors.push(errorMsg);
          this.logger.error(errorMsg);
        }
      }

      this.logger.log(`Rollback completed. Rolled back ${rolledBackCount} subscriptions`);

      return {
        success: errors.length === 0,
        rolledBackCount,
        errors,
      };

    } catch (error) {
      this.logger.error(`Rollback failed: ${error.message}`);
      return {
        success: false,
        rolledBackCount,
        errors: [error.message],
      };
    }
  }
}

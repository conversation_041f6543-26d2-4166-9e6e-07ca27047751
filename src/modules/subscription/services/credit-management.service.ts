import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SubscriptionRepository } from '../repositories/subscription.repository';
import { StripeConfig } from '../config/stripe.config';
import {
  FEATURE_COSTS,
  FeatureCost,
} from '../../../config/feature-costs.config';
import { TierLimits } from '../enums/subscription-tier.enum';

export interface CreditTransaction {
  type: 'allocation' | 'purchase' | 'usage' | 'refund' | 'expiration';
  amount: number;
  balance: number;
  featureName?: string;
  transactionId?: string;
  timestamp: Date;
  description: string;
}

export interface CreditUsageResult {
  success: boolean;
  newBalance: number;
  transaction: CreditTransaction;
  message: string;
}

export interface CreditAllocationResult {
  success: boolean;
  creditsAllocated: number;
  newBalance: number;
  transaction: CreditTransaction;
}

@Injectable()
export class CreditManagementService {
  private readonly logger = new Logger(CreditManagementService.name);

  constructor(
    private readonly subscriptionRepository: SubscriptionRepository,
    private readonly configService: ConfigService,
    private readonly stripeConfig: StripeConfig,
  ) {}

  /**
   * Get feature cost by feature name
   */
  getFeatureCost(featureName: string): FeatureCost | null {
    return FEATURE_COSTS[featureName] || null;
  }

  /**
   * Check if organization has sufficient credits for a feature
   */
  async hasCreditsForFeature(
    organizationId: string,
    featureName: string,
  ): Promise<{
    hasCredits: boolean;
    currentBalance: number;
    requiredCredits: number;
  }> {
    const subscription = await this.subscriptionRepository.findByOrganizationId(
      organizationId,
    );
    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    const featureCost = this.getFeatureCost(featureName);
    if (!featureCost) {
      // If feature cost is not defined, assume it's free
      return {
        hasCredits: true,
        currentBalance: subscription.creditBalance,
        requiredCredits: 0,
      };
    }

    return {
      hasCredits: subscription.creditBalance >= featureCost.creditCost,
      currentBalance: subscription.creditBalance,
      requiredCredits: featureCost.creditCost,
    };
  }

  /**
   * Deduct credits for feature usage
   */
  async deductCreditsForFeature(
    organizationId: string,
    featureName: string,
    transactionId?: string,
  ): Promise<CreditUsageResult> {
    const subscription = await this.subscriptionRepository.findByOrganizationId(
      organizationId,
    );
    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    const featureCost = this.getFeatureCost(featureName);
    if (!featureCost) {
      // If feature cost is not defined, assume it's free
      return {
        success: true,
        newBalance: subscription.creditBalance,
        transaction: {
          type: 'usage',
          amount: 0,
          balance: subscription.creditBalance,
          featureName,
          transactionId,
          timestamp: new Date(),
          description: `Free usage of ${featureName}`,
        },
        message: 'Feature used successfully (free)',
      };
    }

    if (subscription.creditBalance < featureCost.creditCost) {
      return {
        success: false,
        newBalance: subscription.creditBalance,
        transaction: {
          type: 'usage',
          amount: featureCost.creditCost,
          balance: subscription.creditBalance,
          featureName,
          transactionId,
          timestamp: new Date(),
          description: `Insufficient credits for ${featureName}`,
        },
        message: `Insufficient credits. Required: ${featureCost.creditCost}, Available: ${subscription.creditBalance}`,
      };
    }

    // Deduct credits
    const newBalance = subscription.creditBalance - featureCost.creditCost;
    const transaction: CreditTransaction = {
      type: 'usage',
      amount: -featureCost.creditCost,
      balance: newBalance,
      featureName,
      transactionId,
      timestamp: new Date(),
      description: `Used ${featureCost.creditCost} credits for ${featureCost.name}`,
    };

    // Update subscription
    await this.subscriptionRepository.updateDirect(
      { organizationId },
      {
        $set: {
          creditBalance: newBalance,
          totalCreditsSpent:
            subscription.totalCreditsSpent + featureCost.creditCost,
        },
        $push: {
          creditHistory: transaction,
        },
      },
    );

    this.logger.log(
      `Credits deducted: ${featureCost.creditCost} for ${featureName} (Organization: ${organizationId})`,
    );

    return {
      success: true,
      newBalance,
      transaction,
      message: `Successfully used ${featureCost.creditCost} credits for ${featureCost.name}`,
    };
  }

  /**
   * Deduct custom amount of credits
   */
  async deductCustomCredits(
    organizationId: string,
    amount: number,
    featureName: string,
    description?: string,
    transactionId?: string,
  ): Promise<CreditUsageResult> {
    if (amount <= 0) {
      throw new BadRequestException('Credit amount must be positive');
    }

    const subscription = await this.subscriptionRepository.findByOrganizationId(
      organizationId,
    );
    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    if (subscription.creditBalance < amount) {
      return {
        success: false,
        newBalance: subscription.creditBalance,
        transaction: {
          type: 'usage',
          amount: -amount,
          balance: subscription.creditBalance,
          featureName,
          transactionId,
          timestamp: new Date(),
          description: description || `Insufficient credits for ${featureName}`,
        },
        message: `Insufficient credits. Required: ${amount}, Available: ${subscription.creditBalance}`,
      };
    }

    // Deduct credits
    const newBalance = subscription.creditBalance - amount;
    const transaction: CreditTransaction = {
      type: 'usage',
      amount: -amount,
      balance: newBalance,
      featureName,
      transactionId,
      timestamp: new Date(),
      description: description || `Used ${amount} credits for ${featureName}`,
    };

    // Update subscription
    await this.subscriptionRepository.updateDirect(
      { organizationId },
      {
        $set: {
          creditBalance: newBalance,
          totalCreditsSpent: subscription.totalCreditsSpent + amount,
        },
        $push: {
          creditHistory: transaction,
        },
      },
    );

    this.logger.log(
      `Custom credits deducted: ${amount} for ${featureName} (Organization: ${organizationId})`,
    );

    return {
      success: true,
      newBalance,
      transaction,
      message: `Successfully used ${amount} credits for ${featureName}`,
    };
  }

  /**
   * Add credits to organization (purchase or allocation)
   */
  async addCredits(
    organizationId: string,
    amount: number,
    type: 'allocation' | 'purchase' | 'refund',
    description: string,
    transactionId?: string,
  ): Promise<CreditAllocationResult> {
    if (amount <= 0) {
      throw new BadRequestException('Credit amount must be positive');
    }

    const subscription = await this.subscriptionRepository.findByOrganizationId(
      organizationId,
    );
    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    const newBalance = subscription.creditBalance + amount;
    const transaction: CreditTransaction = {
      type,
      amount,
      balance: newBalance,
      transactionId,
      timestamp: new Date(),
      description,
    };

    // Update subscription
    await this.subscriptionRepository.updateDirect(
      { organizationId },
      {
        $set: {
          creditBalance: newBalance,
          totalCreditsEarned: subscription.totalCreditsEarned + amount,
        },
        $push: {
          creditHistory: transaction,
        },
      },
    );

    this.logger.log(
      `Credits added: ${amount} (${type}) for organization ${organizationId}. New balance: ${newBalance}`,
    );

    return {
      success: true,
      creditsAllocated: amount,
      newBalance,
      transaction,
    };
  }

  /**
   * Add credits to organization (webhook version - bypasses tenant context)
   */
  async addCreditsWebhook(
    organizationId: string,
    amount: number,
    type: 'allocation' | 'purchase' | 'refund',
    description: string,
    transactionId?: string,
  ): Promise<CreditAllocationResult> {
    if (amount <= 0) {
      throw new BadRequestException('Credit amount must be positive');
    }

    // Use direct database query to bypass tenant context
    const subscription = await this.subscriptionRepository.findByOrganizationIdDirect(
      organizationId,
    );
    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    const newBalance = subscription.creditBalance + amount;
    const transaction: CreditTransaction = {
      type,
      amount,
      balance: newBalance,
      transactionId,
      timestamp: new Date(),
      description,
    };

    // Update subscription directly
    await this.subscriptionRepository.updateDirect(
      { organizationId },
      {
        $set: {
          creditBalance: newBalance,
          totalCreditsEarned: subscription.totalCreditsEarned + amount,
        },
        $push: {
          creditHistory: transaction,
        },
      },
    );

    this.logger.log(
      `Credits added via webhook: ${amount} (${type}) for organization ${organizationId}. New balance: ${newBalance}`,
    );

    return {
      success: true,
      creditsAllocated: amount,
      newBalance,
      transaction,
    };
  }

  /**
   * Allocate monthly credits based on subscription tier
   */
  async allocateMonthlyCredits(
    organizationId: string,
  ): Promise<CreditAllocationResult> {
    const subscription = await this.subscriptionRepository.findByOrganizationId(
      organizationId,
    );
    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    const tierLimits = TierLimits[subscription.tier];
    const monthlyCredits = tierLimits.monthlyCredits || 0;

    if (monthlyCredits === 0) {
      return {
        success: true,
        creditsAllocated: 0,
        newBalance: subscription.creditBalance,
        transaction: {
          type: 'allocation',
          amount: 0,
          balance: subscription.creditBalance,
          timestamp: new Date(),
          description: 'No monthly credits for this tier',
        },
      };
    }

    // Check if credits were already allocated this month
    const now = new Date();
    const lastAllocation = subscription.lastCreditAllocation;
    const isSameMonth =
      lastAllocation &&
      lastAllocation.getMonth() === now.getMonth() &&
      lastAllocation.getFullYear() === now.getFullYear();

    if (isSameMonth) {
      return {
        success: false,
        creditsAllocated: 0,
        newBalance: subscription.creditBalance,
        transaction: {
          type: 'allocation',
          amount: 0,
          balance: subscription.creditBalance,
          timestamp: new Date(),
          description: 'Monthly credits already allocated for this month',
        },
      };
    }

    return this.addCredits(
      organizationId,
      monthlyCredits,
      'allocation',
      `Monthly credit allocation for ${subscription.tier} tier`,
    );
  }

  /**
   * Get credit balance for organization
   */
  async getCreditBalance(organizationId: string): Promise<{
    balance: number;
    monthlyAllocation: number;
    totalEarned: number;
    totalSpent: number;
    lastAllocation: Date;
  }> {
    const subscription = await this.subscriptionRepository.findByOrganizationId(
      organizationId,
    );
    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    const tierLimits = TierLimits[subscription.tier];

    return {
      balance: subscription.creditBalance,
      monthlyAllocation: tierLimits.monthlyCredits || 0,
      totalEarned: subscription.totalCreditsEarned,
      totalSpent: subscription.totalCreditsSpent,
      lastAllocation: subscription.lastCreditAllocation,
    };
  }

  /**
   * Get credit history for organization
   */
  async getCreditHistory(
    organizationId: string,
    limit: number = 50,
    offset: number = 0,
  ): Promise<{
    transactions: CreditTransaction[];
    total: number;
  }> {
    const subscription = await this.subscriptionRepository.findByOrganizationId(
      organizationId,
    );
    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    const history = subscription.creditHistory || [];
    const sortedHistory = history
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(offset, offset + limit);

    return {
      transactions: sortedHistory,
      total: history.length,
    };
  }

  /**
   * Check if auto-recharge should be triggered
   */
  async checkAutoRecharge(organizationId: string): Promise<boolean> {
    const subscription = await this.subscriptionRepository.findByOrganizationId(
      organizationId,
    );
    if (!subscription) {
      return false;
    }

    const { autoRecharge, creditBalance } = subscription;
    return autoRecharge.enabled && creditBalance <= autoRecharge.threshold;
  }

  /**
   * Get all available features with their costs
   */
  getAllFeatureCosts(): Record<string, FeatureCost> {
    return FEATURE_COSTS;
  }

  /**
   * Get features by category
   */
  getFeaturesByCategory(category: string): FeatureCost[] {
    return Object.values(FEATURE_COSTS).filter(
      (feature) => feature.category === category,
    );
  }

  /**
   * Create checkout session for credit purchase
   */
  async createCreditPurchaseCheckout(
    organizationId: string,
    packageName: string,
    successUrl?: string,
    cancelUrl?: string,
  ): Promise<{ sessionUrl: string; packageInfo: any }> {
    // Get credit package configuration
    const creditPackages = this.configService.get('featureCosts.creditPackages');
    const packageInfo = creditPackages[packageName];

    if (!packageInfo) {
      throw new BadRequestException(`Invalid credit package: ${packageName}`);
    }

    // Get Stripe price ID for the package
    const priceId = this.stripeConfig.getCreditPriceId(packageName);

    // Set default URLs if not provided
    const baseUrl = this.configService.get<string>('APP_URL') || 'http://localhost:3000';
    const defaultSuccessUrl = successUrl || `${baseUrl}/credits/success?session_id={CHECKOUT_SESSION_ID}`;
    const defaultCancelUrl = cancelUrl || `${baseUrl}/credits/cancel`;

    // Create Stripe checkout session
    const session = await this.stripeConfig.createCreditCheckoutSession(
      priceId,
      organizationId,
      packageName,
      defaultSuccessUrl,
      defaultCancelUrl,
    );

    this.logger.log(
      `Credit purchase checkout created for organization ${organizationId}, package: ${packageName}`,
    );

    return {
      sessionUrl: session.url,
      packageInfo: {
        name: packageName,
        credits: packageInfo.credits,
        bonus: packageInfo.bonus,
        totalCredits: packageInfo.credits + packageInfo.bonus,
        price: packageInfo.price / 100, // Convert cents to dollars
        description: packageInfo.description,
      },
    };
  }

  /**
   * Process successful credit purchase (called from webhook)
   */
  async processCreditPurchase(
    organizationId: string,
    packageName: string,
    sessionId: string,
  ): Promise<CreditAllocationResult> {
    // Get credit package configuration
    const creditPackages = this.configService.get('featureCosts.creditPackages');
    const packageInfo = creditPackages[packageName];

    if (!packageInfo) {
      throw new BadRequestException(`Invalid credit package: ${packageName}`);
    }

    const totalCredits = packageInfo.credits + packageInfo.bonus;
    const description = `Credit purchase: ${packageInfo.description} (${packageInfo.credits} + ${packageInfo.bonus} bonus)`;

    // Add credits to the organization (bypass tenant context for webhook)
    const result = await this.addCreditsWebhook(
      organizationId,
      totalCredits,
      'purchase',
      description,
      sessionId,
    );

    this.logger.log(
      `Credit purchase processed: ${totalCredits} credits added to organization ${organizationId}`,
    );

    return result;
  }

  /**
   * Get available credit packages
   */
  getCreditPackages(): any {
    const creditPackages = this.configService.get('featureCosts.creditPackages');

    // Transform packages for API response
    return Object.entries(creditPackages).map(([key, packageInfo]: [string, any]) => ({
      id: key,
      name: packageInfo.description,
      credits: packageInfo.credits,
      bonus: packageInfo.bonus,
      totalCredits: packageInfo.credits + packageInfo.bonus,
      price: packageInfo.price / 100, // Convert cents to dollars
      targetTier: packageInfo.targetTier,
    }));
  }
}

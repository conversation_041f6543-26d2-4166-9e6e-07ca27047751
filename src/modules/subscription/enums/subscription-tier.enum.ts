export enum SubscriptionTier {
  LAW_STUDENT = 'law_student', // Free tier for law students
  LAWYER = 'lawyer',           // Pro tier for individual lawyers
  LAW_FIRM = 'law_firm',       // Admin tier for law firms
}

// Legacy enum values for backward compatibility
export const LegacyTierMapping = {
  'law_student': SubscriptionTier.LAW_STUDENT,
  'lawyer': SubscriptionTier.LAWYER,
  'law_firm': SubscriptionTier.LAW_FIRM,
};

export const TierLimits = {
  [SubscriptionTier.LAW_STUDENT]: {
    displayName: 'Law Student',
    description: 'Perfect for law students learning legal research and document analysis',
    documentLimit: 10,
    analysisPerMonth: 50,
    monthlyCredits: 50, // 50 credits per month
    price: 0, // Free for students
    features: [
      // Basic Features
      'basic_analysis',
      'document_upload',
      'chat',
      'basic_comparison',
      'basic_citation_analysis',
      'document_organization',
      'user_feedback',

      // Limited Lawyer Features (for learning/trial)
      'precedent_analysis',
      'deposition_preparation',
      'deposition_analysis',
      'ai_question_generation',
      'threaded_discussions', // Students can participate in discussions

      // Legal Research Assistant (Full Access)
      'legal_research_assistant',
      'legal_research_synthesis',
      'legal_research_followup',
    ],
  },
  [SubscriptionTier.LAWYER]: {
    displayName: 'Lawyer',
    description: 'Comprehensive tools for practicing attorneys and legal professionals',
    documentLimit: 200,
    analysisPerMonth: -1, // unlimited
    monthlyCredits: 500, // 500 credits per month
    price: 4999, // $49.99/month
    features: [
      // Basic Features
      'basic_analysis',
      'document_upload',
      'chat',
      'advanced_analysis',
      'bulk_upload',
      'priority_processing',
      'basic_comparison',
      'enhanced_comparison',
      'basic_citation_analysis',
      'enhanced_citation_analysis',
      'document_organization',
      'advanced_document_organization',
      'user_feedback',
      'advanced_analytics',
      'document_comparison',

      // AI & Automation
      'precedent_analysis',
      'clause_library',
      'template_generation',
      'document_automation',
      'ai_assisted_drafting',
      'clause_intelligence',
      'related_document_generation',

      // Legal Features
      'privilege_log_automation',
      'privilege_detection',
      'redaction_automation',
      'bulk_redactions',
      'deposition_preparation',
      'deposition_analysis',
      'ai_question_generation',
      'deposition_insights',
      'litigation_support',

      // Contract Management
      'contract_playbooks',
      'playbook_analysis',
      'deviation_detection',
      'contract_risk_scoring',
      'negotiation_playbook',

      // Training & Analytics
      'negotiation_simulator',
      'negotiation_training',
      'scenario_management',
      'performance_analytics',

      // Collaboration
      'real_time_collaboration',
      'workflow_management',
      'threaded_discussions',
      'advanced_sharing',

      // Legal Research Assistant (Full Access)
      'legal_research_assistant',
      'legal_research_synthesis',
      'legal_research_followup',
    ],
  },
  [SubscriptionTier.LAW_FIRM]: {
    displayName: 'Law Firm',
    description: 'Enterprise-grade platform for law firms with team management and advanced analytics',
    documentLimit: -1, // unlimited
    analysisPerMonth: -1, // unlimited
    monthlyCredits: 2000, // 2000 credits per month
    price: 19999, // $199.99/month
    features: [
      // All Lawyer Features (inherits all LAWYER features)
      'basic_analysis',
      'document_upload',
      'chat',
      'advanced_analysis',
      'bulk_upload',
      'priority_processing',
      'basic_comparison',
      'enhanced_comparison',
      'basic_citation_analysis',
      'enhanced_citation_analysis',
      'document_organization',
      'advanced_document_organization',
      'user_feedback',
      'advanced_analytics',
      'document_comparison',
      'precedent_analysis',
      'clause_library',
      'template_generation',
      'document_automation',
      'ai_assisted_drafting',
      'clause_intelligence',
      'related_document_generation',
      'privilege_log_automation',
      'privilege_detection',
      'redaction_automation',
      'bulk_redactions',
      'deposition_preparation',
      'deposition_analysis',
      'ai_question_generation',
      'deposition_insights',
      'litigation_support',
      'contract_playbooks',
      'playbook_analysis',
      'deviation_detection',
      'contract_risk_scoring',
      'negotiation_playbook',
      'negotiation_simulator',
      'negotiation_training',
      'scenario_management',
      'performance_analytics',
      'real_time_collaboration',
      'workflow_management',
      'threaded_discussions',
      'team_analytics',
      'advanced_sharing',

      // Legal Research Assistant (Full Access + Enterprise Features)
      'legal_research_assistant',
      'legal_research_synthesis',
      'legal_research_followup',

      // Law Firm-Only Features
      'api_access',
      'team_collaboration',
      'admin_features',
      'user_management',
      'organization_management',
      'billing_management',
      'system_analytics',
      'audit_logs',
      'data_export',
      'custom_integrations',
      'predictive_analytics',
      'risk_forecasting',
      'trend_analysis',
      'custom_ai_models',
      'white_label_options',
      'dedicated_support',
      'sla_guarantees',
    ],
  },
};

// Helper functions for tier management
export function getTierDisplayName(tier: SubscriptionTier): string {
  return TierLimits[tier]?.displayName || tier;
}

export function getTierDescription(tier: SubscriptionTier): string {
  return TierLimits[tier]?.description || '';
}

export function getTierPrice(tier: SubscriptionTier): number {
  return TierLimits[tier]?.price || 0;
}

export function getAllTiers(): SubscriptionTier[] {
  return [SubscriptionTier.LAW_STUDENT, SubscriptionTier.LAWYER, SubscriptionTier.LAW_FIRM];
}

export function getTierByLegacyValue(legacyValue: string): SubscriptionTier {
  return LegacyTierMapping[legacyValue] || SubscriptionTier.LAW_STUDENT;
}

export function isFeatureAvailable(tier: SubscriptionTier, feature: string): boolean {
  return TierLimits[tier]?.features.includes(feature) || false;
}

// Tier comparison helpers
export function isTierHigherOrEqual(userTier: SubscriptionTier, requiredTier: SubscriptionTier): boolean {
  const tierOrder = {
    [SubscriptionTier.LAW_STUDENT]: 0,
    [SubscriptionTier.LAWYER]: 1,
    [SubscriptionTier.LAW_FIRM]: 2,
  };

  return tierOrder[userTier] >= tierOrder[requiredTier];
}

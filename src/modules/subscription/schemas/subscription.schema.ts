import { Schema, Document } from 'mongoose';
import { SubscriptionTier } from '../enums/subscription-tier.enum';

export interface Subscription {
  organizationId: string;
  tier: SubscriptionTier;
  status: 'active' | 'inactive' | 'canceled' | 'past_due';
  stripeCustomerId: string;
  stripeSubscriptionId: string;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
  features: string[];
  lastFeatureUpdate: Date;
  featureHistory: Array<{
    action: 'added' | 'removed' | 'migrated';
    featureName: string;
    timestamp: Date;
    reason: string;
  }>;
  trialTier?: SubscriptionTier;
  trialEndDate?: Date;
  usageStats: {
    documentsProcessed: number;
    analysisCount: number;
    lastUpdated: Date;
  };
  // Credit System Fields
  creditBalance: number;
  monthlyCreditsAllocation: number;
  totalCreditsEarned: number;
  totalCreditsSpent: number;
  lastCreditAllocation: Date;
  creditHistory: Array<{
    type: 'allocation' | 'purchase' | 'usage' | 'refund' | 'expiration';
    amount: number;
    balance: number;
    featureName?: string;
    transactionId?: string;
    timestamp: Date;
    description: string;
  }>;
  autoRecharge: {
    enabled: boolean;
    threshold: number;
    amount: number;
    stripePaymentMethodId?: string;
  };
  pricing?: {
    amount: number;
    currency: string;
    interval: string;
    updatedAt: Date;
  };
  metadata?: {
    tierMigration?: {
      migratedAt: Date;
      fromTier: string;
      toTier: string;
      displayName: string;
    };
  };
  createdAt: Date;
  updatedAt: Date;
}

export type SubscriptionDocument = Subscription & Document;

export const SubscriptionSchema = new Schema(
  {
    organizationId: {
      type: String,
      required: true,
      index: true,
    },
    tier: {
      type: String,
      enum: Object.values(SubscriptionTier),
      required: true,
    },
    status: {
      type: String,
      enum: ['active', 'inactive', 'canceled', 'past_due'],
      required: true,
      default: 'inactive',
    },
    stripeCustomerId: {
      type: String,
      required: true,
    },
    stripeSubscriptionId: {
      type: String,
      required: true,
    },
    currentPeriodStart: {
      type: Date,
      required: true,
    },
    currentPeriodEnd: {
      type: Date,
      required: true,
    },
    cancelAtPeriodEnd: {
      type: Boolean,
      default: false,
    },
    features: [
      {
        type: String,
      },
    ],
    lastFeatureUpdate: {
      type: Date,
      default: Date.now,
    },
    featureHistory: [
      {
        action: {
          type: String,
          enum: ['added', 'removed', 'migrated'],
          required: true,
        },
        featureName: {
          type: String,
          required: true,
        },
        timestamp: {
          type: Date,
          default: Date.now,
        },
        reason: {
          type: String,
          default: 'automatic_migration',
        },
      },
    ],
    trialTier: {
      type: String,
      enum: Object.values(SubscriptionTier),
      required: false,
    },
    trialEndDate: {
      type: Date,
      required: false,
    },
    usageStats: {
      documentsProcessed: {
        type: Number,
        default: 0,
      },
      analysisCount: {
        type: Number,
        default: 0,
      },
      lastUpdated: {
        type: Date,
        default: Date.now,
      },
    },
    // Credit System Schema Fields
    creditBalance: {
      type: Number,
      default: 0,
      min: 0,
    },
    monthlyCreditsAllocation: {
      type: Number,
      default: 0,
      min: 0,
    },
    totalCreditsEarned: {
      type: Number,
      default: 0,
      min: 0,
    },
    totalCreditsSpent: {
      type: Number,
      default: 0,
      min: 0,
    },
    lastCreditAllocation: {
      type: Date,
      default: Date.now,
    },
    creditHistory: [
      {
        type: {
          type: String,
          enum: ['allocation', 'purchase', 'usage', 'refund', 'expiration'],
          required: true,
        },
        amount: {
          type: Number,
          required: true,
        },
        balance: {
          type: Number,
          required: true,
          min: 0,
        },
        featureName: {
          type: String,
          required: false,
        },
        transactionId: {
          type: String,
          required: false,
        },
        timestamp: {
          type: Date,
          default: Date.now,
        },
        description: {
          type: String,
          required: true,
        },
      },
    ],
    autoRecharge: {
      enabled: {
        type: Boolean,
        default: false,
      },
      threshold: {
        type: Number,
        default: 10,
        min: 0,
      },
      amount: {
        type: Number,
        default: 100,
        min: 1,
      },
      stripePaymentMethodId: {
        type: String,
        required: false,
      },
    },
    pricing: {
      amount: {
        type: Number,
        required: false,
      },
      currency: {
        type: String,
        required: false,
        default: 'USD',
      },
      interval: {
        type: String,
        required: false,
        default: 'month',
      },
      updatedAt: {
        type: Date,
        required: false,
      },
    },
    metadata: {
      tierMigration: {
        migratedAt: {
          type: Date,
          required: false,
        },
        fromTier: {
          type: String,
          required: false,
        },
        toTier: {
          type: String,
          required: false,
        },
        displayName: {
          type: String,
          required: false,
        },
      },
    },
  },
  {
    timestamps: true,
    collection: 'subscriptions',
  },
);

export const SUBSCRIPTION_MODEL = 'Subscription';

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Stripe from 'stripe';
import { SubscriptionTier } from '../enums/subscription-tier.enum';

@Injectable()
export class StripeConfig {
  private readonly stripe: Stripe;
  private readonly priceIds: Record<SubscriptionTier, string>;
  private readonly creditPriceIds: Record<string, string>;
  private readonly logger = new Logger(StripeConfig.name);

  constructor(private readonly configService: ConfigService) {
    const stripeKey = this.configService.get<string>('STRIPE_SECRET_KEY');
    if (!stripeKey) {
      this.logger.warn(
        'STRIPE_SECRET_KEY not found in configuration. Stripe functionality will be limited.',
      );
      return;
    }

    try {
      this.stripe = new Stripe(stripeKey, {
        apiVersion: '2025-02-24.acacia',
      });

      this.priceIds = {
        [SubscriptionTier.LAW_STUDENT]:
          this.configService.get<string>('STRIPE_PLAN_FREE'),
        [SubscriptionTier.LAWYER]:
          this.configService.get<string>('STRIPE_PLAN_PRO'),
        [SubscriptionTier.LAW_FIRM]:
          this.configService.get<string>('STRIPE_PLAN_ADMIN'),
      };

      // Credit package price IDs
      this.creditPriceIds = {
        student: this.configService.get<string>('STRIPE_CREDIT_STUDENT'),
        lawyer_small: this.configService.get<string>('STRIPE_CREDIT_LAWYER_SMALL'),
        lawyer_large: this.configService.get<string>('STRIPE_CREDIT_LAWYER_LARGE'),
        firm_standard: this.configService.get<string>('STRIPE_CREDIT_FIRM_STANDARD'),
        firm_enterprise: this.configService.get<string>('STRIPE_CREDIT_FIRM_ENTERPRISE'),
      };
    } catch (error) {
      this.logger.error('Failed to initialize Stripe:', error);
    }
  }

  getStripeInstance(): Stripe {
    if (!this.stripe) {
      throw new Error('Stripe is not properly initialized');
    }
    return this.stripe;
  }

  getPriceIdForTier(tier: SubscriptionTier): string {
    // Free tier doesn't need a price ID
    if (tier === SubscriptionTier.LAW_STUDENT) {
      return null;
    }

    const priceId = this.priceIds?.[tier];
    return priceId;
  }

  getCreditPriceId(packageName: string): string {
    const priceId = this.creditPriceIds?.[packageName];
    if (!priceId) {
      throw new Error(`No price ID found for credit package: ${packageName}`);
    }
    return priceId;
  }

  getWebhookSecret(): string {
    return this.configService.get<string>('STRIPE_WEBHOOK_SECRET');
  }

  async constructEvent(
    payload: string | Buffer,
    signature: string,
  ): Promise<Stripe.Event> {
    const webhookSecret = this.getWebhookSecret();
    if (!webhookSecret || !this.stripe) {
      throw new Error('Stripe webhook configuration is missing');
    }
    return this.stripe.webhooks.constructEvent(
      payload,
      signature,
      webhookSecret,
    );
  }

  async createCustomer(
    email: string,
    organizationId: string,
  ): Promise<Stripe.Customer> {
    if (!this.stripe) {
      throw new Error('Stripe is not properly initialized');
    }
    return this.stripe.customers.create({
      email,
      metadata: {
        organizationId,
      },
    });
  }

  async createSubscription(
    customerId: string,
    priceId: string,
  ): Promise<Stripe.Subscription> {
    if (!this.stripe) {
      throw new Error('Stripe is not properly initialized');
    }
    return this.stripe.subscriptions.create({
      customer: customerId,
      items: [{ price: priceId }],
      payment_behavior: 'default_incomplete',
      payment_settings: { save_default_payment_method: 'on_subscription' },
      expand: ['latest_invoice.payment_intent'],
    });
  }

  async createCheckoutSession(
    priceId: string,
    organizationId: string,
    successUrl: string,
    cancelUrl: string,
  ): Promise<Stripe.Checkout.Session> {
    if (!this.stripe) {
      throw new Error('Stripe is not properly initialized');
    }

    return this.stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: successUrl,
      cancel_url: cancelUrl,
      client_reference_id: organizationId,
      subscription_data: {
        metadata: {
          organizationId: organizationId,
        },
      },
    });
  }

  async createCreditCheckoutSession(
    priceId: string,
    organizationId: string,
    packageName: string,
    successUrl: string,
    cancelUrl: string,
  ): Promise<Stripe.Checkout.Session> {
    if (!this.stripe) {
      throw new Error('Stripe is not properly initialized');
    }

    return this.stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'payment', // One-time payment for credits
      success_url: successUrl,
      cancel_url: cancelUrl,
      client_reference_id: organizationId,
      metadata: {
        organizationId: organizationId,
        packageName: packageName,
        type: 'credit_purchase',
      },
    });
  }

  async cancelSubscription(
    subscriptionId: string,
  ): Promise<Stripe.Subscription> {
    if (!this.stripe) {
      throw new Error('Stripe is not properly initialized');
    }
    return this.stripe.subscriptions.cancel(subscriptionId);
  }

  async retrieveSubscription(
    subscriptionId: string,
  ): Promise<Stripe.Subscription> {
    if (!this.stripe) {
      throw new Error('Stripe is not properly initialized');
    }
    return this.stripe.subscriptions.retrieve(subscriptionId);
  }
}

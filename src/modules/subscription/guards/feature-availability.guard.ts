import {
  Injectable,
  CanActivate,
  ExecutionContext,
  ForbiddenException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { SubscriptionService } from '../services/subscription.service';
import { CreditManagementService } from '../services/credit-management.service';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { SubscriptionTier, TierLimits } from '../enums/subscription-tier.enum';
import { FEATURES_KEY } from '../decorators/require-features.decorator';
import { SKIP_SUBSCRIPTION_CHECK } from '../decorators/skip-subscription-check.decorator';

@Injectable()
export class FeatureAvailabilityGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private subscriptionService: SubscriptionService,
    private creditService: CreditManagementService,
    private tenantContext: TenantContextService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if the handler has the skip subscription check metadata
    const skipCheck = this.reflector.getAllAndOverride<boolean>(
      SKIP_SUBSCRIPTION_CHECK,
      [context.getHandler(), context.getClass()],
    );

    if (skipCheck) {
      return true; // Skip subscription check
    }

    const requiredFeatures = this.reflector.getAllAndOverride<string[]>(
      FEATURES_KEY,
      [context.getHandler(), context.getClass()],
    );

    if (!requiredFeatures) {
      return true; // No features required for this route
    }

    // Try to get organization ID from tenant context first
    let organizationId = this.tenantContext.getCurrentOrganization();

    // If not available from tenant context, try to get from request object as fallback
    if (!organizationId) {
      const request = context.switchToHttp().getRequest();
      organizationId = request.tenant?.organizationId;
    }

    if (!organizationId) {
      throw new ForbiddenException('Organization context is required');
    }

    try {
      const subscription = await this.subscriptionService.getSubscription(
        organizationId,
      );

      // Check if subscription is active
      if (!['active', 'trialing'].includes(subscription.status)) {
        throw new ForbiddenException('Subscription is not active');
      }

      // Check if the subscription tier allows the required features
      for (const feature of requiredFeatures) {
        if (!this.hasFeature(subscription.tier, feature)) {
          throw new ForbiddenException(
            `Feature '${feature}' is not available in your current subscription plan`,
          );
        }
      }

      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }

      // If subscription not found, assume free tier
      if (error.message === 'Subscription not found') {
        for (const feature of requiredFeatures) {
          if (!this.hasFeature(SubscriptionTier.LAW_STUDENT, feature)) {
            throw new ForbiddenException(
              `Feature '${feature}' is not available in your current subscription plan`,
            );
          }
        }
        return true;
      }

      throw new ForbiddenException('Unable to verify subscription features');
    }
  }

  private hasFeature(tier: SubscriptionTier, feature: string): boolean {
    const tierLimits = TierLimits[tier];
    return tierLimits?.features.includes(feature) || false;
  }
}

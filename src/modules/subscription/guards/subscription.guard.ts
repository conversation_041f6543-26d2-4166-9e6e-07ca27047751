import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { SubscriptionService } from '../services/subscription.service';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { SKIP_SUBSCRIPTION_CHECK } from '../decorators/skip-subscription-check.decorator';
import { SUBSCRIPTION_CHECK_KEY } from '../decorators/subscription-check.decorator';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class SubscriptionGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly subscriptionService: SubscriptionService,
    private readonly tenantContext: TenantContextService,
    private readonly configService: ConfigService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if the handler is marked to skip subscription check
    const skipCheck = this.reflector.get<boolean>(
      SKIP_SUBSCRIPTION_CHECK,
      context.getHandler(),
    );

    // Check if subscription limit bypass is enabled (takes precedence over development mode)
    const bypassLimit = this.configService.get<boolean>('subscription.bypassSubscriptionLimit');
    if (bypassLimit) {
      return true;
    }

    // Check if we're in development mode (only bypass if bypassSubscriptionLimit is not explicitly set to false)
    const isDevelopment = this.configService.get<boolean>('app.isDevelopment');
    if (isDevelopment && bypassLimit !== false) {
      return true;
    }

    if (skipCheck) {
      return true;
    }

    // Get the required operation from metadata
    const operation = this.reflector.get<string>(
      SUBSCRIPTION_CHECK_KEY,
      context.getHandler(),
    );

    if (!operation) {
      return true; // No operation specified means no check required
    }

    const organizationId = this.tenantContext.getCurrentOrganization();
    if (!organizationId) {
      throw new ForbiddenException('Organization ID not found in request');
    }

    const isAllowed = await this.subscriptionService.canPerformOperation(organizationId, operation);

    if (!isAllowed) {
      throw new ForbiddenException('You have reached the limit for your subscription tier. Please upgrade your plan.');
    }

    return true;
  }
}

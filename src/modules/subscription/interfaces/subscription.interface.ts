import { Document } from 'mongoose';
import { SubscriptionTier } from '../enums/subscription-tier.enum';

export interface Subscription extends Document {
  organizationId: string;
  tier: SubscriptionTier;
  status: 'active' | 'inactive' | 'canceled' | 'past_due' | 'trialing';
  stripeCustomerId: string;
  stripeSubscriptionId: string;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
  features: string[];
  trialTier?: SubscriptionTier; // Optional trial tier
  trialEndDate?: Date; // Optional trial end date
  usageStats: {
    documentsProcessed: number;
    analysisCount: number;
    lastUpdated: Date;
  };
  // Credit System Fields
  creditBalance: number;
  monthlyCreditsAllocation: number;
  totalCreditsEarned: number;
  totalCreditsSpent: number;
  lastCreditAllocation: Date;
  creditHistory: Array<{
    type: 'allocation' | 'purchase' | 'usage' | 'refund' | 'expiration';
    amount: number;
    balance: number;
    featureName?: string;
    transactionId?: string;
    timestamp: Date;
    description: string;
  }>;
  autoRecharge: {
    enabled: boolean;
    threshold: number;
    amount: number;
    stripePaymentMethodId?: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

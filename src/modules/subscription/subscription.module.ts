import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { SubscriptionService } from './services/subscription.service';
import { SubscriptionRepository } from './repositories/subscription.repository';
import { SubscriptionSchema } from './schemas/subscription.schema';
import { StripeConfig } from './config/stripe.config';
import { SubscriptionGuard } from './guards/subscription.guard';
import { FeatureAvailabilityGuard } from './guards/feature-availability.guard';
import { SubscriptionController } from './controllers/subscription.controller';
import { FeatureManagementController } from './controllers/feature-management.controller';
import { CreditController } from './controllers/credit.controller';
import { WebhookController } from './controllers/webhook.controller';
import { FeatureManagementService } from './services/feature-management.service';
import { CreditManagementService } from './services/credit-management.service';
import { TierMigrationService } from './services/tier-migration.service';
import { AuthModule } from '../auth/auth.module';
// import { AnalyticsModule } from '../analytics/analytics.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'Subscription', schema: SubscriptionSchema },
    ]),
    forwardRef(() => AuthModule), // AuthModule provides JwtService and ConfigService
    ConfigModule,
    // forwardRef(() => AnalyticsModule),
  ],
  providers: [
    SubscriptionService,
    SubscriptionRepository,
    SubscriptionGuard,
    FeatureAvailabilityGuard,
    StripeConfig,
    FeatureManagementService,
    CreditManagementService,
    TierMigrationService,
  ],
  exports: [
    SubscriptionService,
    SubscriptionGuard,
    FeatureAvailabilityGuard,
    FeatureManagementService,
    CreditManagementService,
    TierMigrationService,
  ],
  controllers: [
    SubscriptionController,
    FeatureManagementController,
    CreditController,
    WebhookController,
  ],
})
export class SubscriptionModule {}

import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { TenantAwareRepository } from '../../common/repositories/base.repository';
import { TenantContextService } from '../../auth/services/tenant-context.service';
import { Subscription } from '../interfaces/subscription.interface';
import { SubscriptionTier } from '../enums/subscription-tier.enum';

@Injectable()
export class SubscriptionRepository extends TenantAwareRepository<Subscription> {
  constructor(
    @InjectModel('Subscription') subscriptionModel: Model<Subscription>,
    tenantContext: TenantContextService,
  ) {
    super(subscriptionModel, tenantContext);
  }

  async findByOrganizationId(
    organizationId: string,
  ): Promise<Subscription | null> {
    return this.findOne({ organizationId });
  }

  async updateSubscriptionTier(
    organizationId: string,
    tier: SubscriptionTier,
  ): Promise<Subscription | null> {
    return this.update(organizationId, { tier } as any);
  }

  async updateUsageStats(
    organizationId: string,
    documentsProcessed: number,
    analysisCount: number,
  ): Promise<Subscription | null> {
    return this.update(organizationId, {
      $set: {
        'usageStats.documentsProcessed': documentsProcessed,
        'usageStats.analysisCount': analysisCount,
        'usageStats.lastUpdated': new Date(),
      },
    } as any);
  }

  async updateSubscriptionStatus(
    organizationId: string,
    status: string,
  ): Promise<Subscription | null> {
    return this.update(organizationId, { status } as any);
  }

  async incrementUsage(
    organizationId: string,
    field: 'documentsProcessed' | 'analysisCount',
    amount: number,
  ): Promise<Subscription | null> {
    const filter = { organizationId };
    const update = {
      $inc: { [`usageStats.${field}`]: amount },
      $set: { 'usageStats.lastUpdated': new Date() },
    };

    // We'll use update method with organizationId as filter instead of using ID
    return this.model
      .findOneAndUpdate(this.addTenantContext(filter), update, { new: true })
      .exec();
  }

  async resetUsageStats(
    organizationId: string,
    statsToReset: Partial<{
      documentsProcessed: number;
      analysisCount: number;
      lastUpdated: Date;
    }>,
  ): Promise<Subscription | null> {
    const updateFields = {};

    Object.entries(statsToReset).forEach(([key, value]) => {
      updateFields[`usageStats.${key}`] = value;
    });

    const filter = { organizationId };
    const update = { $set: updateFields };

    // We'll use update method with organizationId as filter instead of using ID
    return this.model
      .findOneAndUpdate(this.addTenantContext(filter), update, { new: true })
      .exec();
  }

  // Special methods that bypass tenant context for webhook operations
  async findByOrganizationIdDirect(
    organizationId: string,
  ): Promise<Subscription | null> {
    return this.model.findOne({ organizationId }).exec();
  }

  async updateDirect(filter: any, update: any): Promise<Subscription | null> {
    return this.model.findOneAndUpdate(filter, update, { new: true }).exec();
  }

  async createDirect(data: Partial<Subscription>): Promise<Subscription> {
    return this.model.create(data);
  }

  /**
   * Find all subscriptions (for migration purposes)
   */
  async findAll(): Promise<Subscription[]> {
    return this.model.find({}).exec();
  }

  // Credit-related methods
  async updateCreditBalance(
    organizationId: string,
    newBalance: number,
  ): Promise<Subscription | null> {
    const filter = { organizationId };
    const update = {
      $set: {
        creditBalance: newBalance,
        'usageStats.lastUpdated': new Date(),
      },
    };

    return this.model
      .findOneAndUpdate(this.addTenantContext(filter), update, { new: true })
      .exec();
  }

  async addCreditTransaction(
    organizationId: string,
    transaction: {
      type: 'allocation' | 'purchase' | 'usage' | 'refund' | 'expiration';
      amount: number;
      balance: number;
      featureName?: string;
      transactionId?: string;
      timestamp: Date;
      description: string;
    },
  ): Promise<Subscription | null> {
    const filter = { organizationId };
    const update = {
      $push: { creditHistory: transaction },
      $set: {
        creditBalance: transaction.balance,
        'usageStats.lastUpdated': new Date(),
      },
    };

    return this.model
      .findOneAndUpdate(this.addTenantContext(filter), update, { new: true })
      .exec();
  }

  async updateAutoRechargeSettings(
    organizationId: string,
    settings: {
      enabled: boolean;
      threshold?: number;
      amount?: number;
      stripePaymentMethodId?: string;
    },
  ): Promise<Subscription | null> {
    const filter = { organizationId };
    const update = {
      $set: {
        'autoRecharge.enabled': settings.enabled,
        ...(settings.threshold !== undefined && {
          'autoRecharge.threshold': settings.threshold,
        }),
        ...(settings.amount !== undefined && {
          'autoRecharge.amount': settings.amount,
        }),
        ...(settings.stripePaymentMethodId !== undefined && {
          'autoRecharge.stripePaymentMethodId': settings.stripePaymentMethodId,
        }),
      },
    };

    return this.model
      .findOneAndUpdate(this.addTenantContext(filter), update, { new: true })
      .exec();
  }
}

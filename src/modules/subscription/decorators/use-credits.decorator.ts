import { SetMetadata, applyDecorators } from '@nestjs/common';
import { ApiHeader, ApiResponse } from '@nestjs/swagger';

export const CREDITS_FEATURE_KEY = 'credits_feature';
export const CREDITS_COST_KEY = 'credits_cost';

/**
 * Decorator to mark a method as requiring credits for a specific feature
 * This decorator should be used on controller methods that consume credits
 * 
 * @param featureName - The name of the feature that consumes credits
 * @param customCost - Optional custom cost override (if different from config)
 * 
 * @example
 * ```typescript
 * @UseCredits('advanced_analysis')
 * @Post('analyze')
 * async analyzeDocument(@Body() dto: AnalyzeDocumentDto) {
 *   return this.documentsService.analyzeDocument(dto);
 * }
 * 
 * @UseCredits('custom_feature', 10) // Custom cost of 10 credits
 * @Post('custom-operation')
 * async customOperation() {
 *   return this.service.performCustomOperation();
 * }
 * ```
 */
export const UseCredits = (featureName: string, customCost?: number) => {
  return applyDecorators(
    SetMetadata(CREDITS_FEATURE_KEY, featureName),
    ...(customCost !== undefined ? [SetMetadata(CREDITS_COST_KEY, customCost)] : []),
    ApiHeader({
      name: 'X-Credits-Used',
      description: 'Number of credits deducted for this operation',
      required: false,
    }),
    ApiHeader({
      name: 'X-Credits-Remaining',
      description: 'Remaining credit balance after this operation',
      required: false,
    }),
    ApiHeader({
      name: 'X-Feature-Used',
      description: 'Name of the feature that consumed credits',
      required: false,
    }),
    ApiResponse({
      status: 402,
      description: 'Insufficient credits for this operation',
      schema: {
        type: 'object',
        properties: {
          statusCode: { type: 'number', example: 402 },
          message: { 
            type: 'string', 
            example: 'Insufficient credits for Advanced Analysis. Required: 5, Available: 2. Please purchase more credits or upgrade your plan.' 
          },
          error: { type: 'string', example: 'Payment Required' },
          creditInfo: {
            type: 'object',
            properties: {
              required: { type: 'number', example: 5 },
              available: { type: 'number', example: 2 },
              feature: { type: 'string', example: 'advanced_analysis' },
            },
          },
        },
      },
    }),
  );
};

/**
 * Decorator to mark a method as free (no credits required)
 * This is useful for explicitly marking methods that should not consume credits
 * even if they match patterns that would normally consume credits
 * 
 * @example
 * ```typescript
 * @FreeFeature()
 * @Post('basic-operation')
 * async basicOperation() {
 *   return this.service.performBasicOperation();
 * }
 * ```
 */
export const FreeFeature = () => {
  return applyDecorators(
    SetMetadata(CREDITS_FEATURE_KEY, null),
    SetMetadata(CREDITS_COST_KEY, 0),
  );
};

/**
 * Decorator to mark a method as requiring multiple features
 * This is useful for complex operations that use multiple features
 * 
 * @param features - Array of feature names and their costs
 * 
 * @example
 * ```typescript
 * @UseMultipleCredits([
 *   { feature: 'advanced_analysis', cost: 5 },
 *   { feature: 'precedent_analysis', cost: 10 }
 * ])
 * @Post('comprehensive-analysis')
 * async comprehensiveAnalysis() {
 *   return this.service.performComprehensiveAnalysis();
 * }
 * ```
 */
export const UseMultipleCredits = (features: Array<{ feature: string; cost?: number }>) => {
  return applyDecorators(
    SetMetadata(CREDITS_FEATURE_KEY, features.map(f => f.feature)),
    SetMetadata(CREDITS_COST_KEY, features.map(f => f.cost)),
    ApiHeader({
      name: 'X-Credits-Used',
      description: 'Total number of credits deducted for this operation',
      required: false,
    }),
    ApiHeader({
      name: 'X-Credits-Remaining',
      description: 'Remaining credit balance after this operation',
      required: false,
    }),
    ApiHeader({
      name: 'X-Features-Used',
      description: 'Comma-separated list of features that consumed credits',
      required: false,
    }),
    ApiResponse({
      status: 402,
      description: 'Insufficient credits for one or more operations',
    }),
  );
};

/**
 * Decorator to mark a method as requiring credits only for premium tiers
 * Free tier users get unlimited access, paid tiers consume credits
 * 
 * @param featureName - The name of the feature
 * @param customCost - Optional custom cost override
 * 
 * @example
 * ```typescript
 * @UseCreditsPremiumOnly('advanced_analysis')
 * @Post('analyze')
 * async analyzeDocument() {
 *   // Free tier: unlimited access
 *   // Pro/Admin tier: consumes credits
 *   return this.service.analyzeDocument();
 * }
 * ```
 */
export const UseCreditsPremiumOnly = (featureName: string, customCost?: number) => {
  return applyDecorators(
    SetMetadata(CREDITS_FEATURE_KEY, featureName),
    SetMetadata(CREDITS_COST_KEY, customCost),
    SetMetadata('premium_only_credits', true),
    ApiHeader({
      name: 'X-Credits-Used',
      description: 'Number of credits deducted (0 for free tier)',
      required: false,
    }),
    ApiHeader({
      name: 'X-Credits-Remaining',
      description: 'Remaining credit balance',
      required: false,
    }),
  );
};

/**
 * Decorator to mark a method as having conditional credit usage
 * Credits are only consumed if certain conditions are met
 * 
 * @param featureName - The name of the feature
 * @param condition - Function that determines if credits should be consumed
 * 
 * @example
 * ```typescript
 * @UseCreditsConditional('advanced_analysis', (req) => req.body.useAdvancedFeatures)
 * @Post('analyze')
 * async analyzeDocument(@Body() dto: AnalyzeDocumentDto) {
 *   // Credits only consumed if dto.useAdvancedFeatures is true
 *   return this.service.analyzeDocument(dto);
 * }
 * ```
 */
export const UseCreditsConditional = (
  featureName: string, 
  condition: (request: any) => boolean,
  customCost?: number
) => {
  return applyDecorators(
    SetMetadata(CREDITS_FEATURE_KEY, featureName),
    SetMetadata(CREDITS_COST_KEY, customCost),
    SetMetadata('conditional_credits', condition),
  );
};

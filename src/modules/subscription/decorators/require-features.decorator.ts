import { SetMetadata } from '@nestjs/common';

export const FEATURES_KEY = 'required_features';

/**
 * Decorator to specify required subscription features for a route
 * @param features List of features required to access the route
 * 
 * Available features:
 * - basic_analysis: Basic document analysis (all tiers)
 * - document_upload: Upload documents (all tiers) 
 * - chat: Chat about documents (all tiers)
 * - basic_comparison: Basic document comparison (all tiers)
 * - basic_citation_analysis: Basic citation analysis with simple relationship mapping (all tiers)
 * - advanced_analysis: Advanced document analysis (Professional & Enterprise)
 * - bulk_upload: Bulk document upload (Professional & Enterprise)
 * - priority_processing: Priority document processing (Professional & Enterprise)
 * - enhanced_comparison: Enhanced document comparison with visual diff and section analysis (Professional & Enterprise)
 * - enhanced_citation_analysis: Enhanced citation analysis with relationship mapping, precedent tracking, and impact scoring (Professional & Enterprise)
 * - custom_training: Custom model training (Enterprise only)
 * - api_access: API access (Enterprise only)
 * - team_collaboration: Team collaboration features (Enterprise only)
 */
export const RequireFeatures = (...features: string[]) => 
  SetMetadata(FEATURES_KEY, features);

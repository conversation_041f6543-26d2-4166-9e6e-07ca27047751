#!/usr/bin/env node

/**
 * Database Migration Script for Subscription Features
 * 
 * This script helps migrate existing subscriptions when new features are added.
 * It can be run manually or as part of deployment automation.
 * 
 * Usage:
 * npm run migrate:features -- --dry-run
 * npm run migrate:features -- --feature "new_feature_name" --tier "PRO"
 * npm run migrate:features -- --sync-all
 */

import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { FeatureManagementService, FeatureDefinition } from '../modules/subscription/services/feature-management.service';
import { SubscriptionTier } from '../modules/subscription/enums/subscription-tier.enum';
import { Logger } from '@nestjs/common';

interface MigrationOptions {
  dryRun?: boolean;
  syncAll?: boolean;
  feature?: string;
  tier?: SubscriptionTier;
  category?: string;
  description?: string;
  subscriptionId?: string;
}

class FeatureMigrationScript {
  private readonly logger = new Logger(FeatureMigrationScript.name);
  private featureManagementService: FeatureManagementService;

  async run() {
    const app = await NestFactory.createApplicationContext(AppModule);
    this.featureManagementService = app.get(FeatureManagementService);

    try {
      const options = this.parseArguments();
      await this.executeMigration(options);
    } catch (error) {
      this.logger.error(`Migration failed: ${error.message}`, error.stack);
      process.exit(1);
    } finally {
      await app.close();
    }
  }

  private parseArguments(): MigrationOptions {
    const args = process.argv.slice(2);
    const options: MigrationOptions = {};

    for (let i = 0; i < args.length; i++) {
      switch (args[i]) {
        case '--dry-run':
          options.dryRun = true;
          break;
        case '--sync-all':
          options.syncAll = true;
          break;
        case '--feature':
          options.feature = args[++i];
          break;
        case '--tier':
          options.tier = args[++i] as SubscriptionTier;
          break;
        case '--category':
          options.category = args[++i];
          break;
        case '--description':
          options.description = args[++i];
          break;
        case '--subscription-id':
          options.subscriptionId = args[++i];
          break;
      }
    }

    return options;
  }

  private async executeMigration(options: MigrationOptions) {
    this.logger.log('Starting feature migration...');
    this.logger.log(`Options: ${JSON.stringify(options, null, 2)}`);

    if (options.syncAll) {
      await this.syncAllSubscriptions(options.dryRun);
    } else if (options.subscriptionId) {
      await this.syncSingleSubscription(options.subscriptionId);
    } else if (options.feature && options.tier) {
      await this.addNewFeature(options);
    } else {
      await this.showUsage();
    }
  }

  private async syncAllSubscriptions(dryRun: boolean = false) {
    this.logger.log(`Syncing all subscriptions (dryRun: ${dryRun})`);

    // Get all current features and update subscriptions
    const allFeatures = this.featureManagementService.getAllFeatures();
    const newFeatures: FeatureDefinition[] = [];

    // Collect all features as "new" for migration purposes
    Object.entries(allFeatures).forEach(([tier, features]) => {
      newFeatures.push(...features.map(f => ({
        ...f,
        isNew: true,
        addedDate: new Date(),
      })));
    });

    const result = await this.featureManagementService.updateAllSubscriptionsWithNewFeatures(
      newFeatures,
      dryRun,
    );

    this.logger.log('Migration Results:');
    this.logger.log(`Total Subscriptions: ${result.totalSubscriptions}`);
    this.logger.log(`Updated: ${result.updatedSubscriptions}`);
    this.logger.log(`Skipped: ${result.skippedSubscriptions}`);
    this.logger.log(`Errors: ${result.errors.length}`);

    if (result.errors.length > 0) {
      this.logger.error('Errors encountered:');
      result.errors.forEach(error => {
        this.logger.error(`  ${error.subscriptionId}: ${error.error}`);
      });
    }

    if (dryRun) {
      this.logger.log('DRY RUN COMPLETED - No actual changes were made');
    } else {
      this.logger.log('Migration completed successfully');
    }
  }

  private async syncSingleSubscription(subscriptionId: string) {
    this.logger.log(`Syncing single subscription: ${subscriptionId}`);

    try {
      const result = await this.featureManagementService.syncSubscriptionFeatures(subscriptionId);

      this.logger.log('Sync Results:');
      this.logger.log(`Added Features: ${result.added.join(', ')}`);
      this.logger.log(`Removed Features: ${result.removed.join(', ')}`);
      this.logger.log(`Unchanged Features: ${result.unchanged.length}`);
    } catch (error) {
      this.logger.error(`Failed to sync subscription ${subscriptionId}: ${error.message}`);
      throw error;
    }
  }

  private async addNewFeature(options: MigrationOptions) {
    this.logger.log(`Adding new feature: ${options.feature} to tier: ${options.tier}`);

    const newFeature: FeatureDefinition = {
      name: options.feature!,
      description: options.description || `New feature: ${options.feature}`,
      category: options.category || 'general',
      requiredTier: options.tier!,
      isNew: true,
      addedDate: new Date(),
    };

    const result = await this.featureManagementService.updateAllSubscriptionsWithNewFeatures(
      [newFeature],
      options.dryRun || false,
    );

    this.logger.log('Feature Addition Results:');
    this.logger.log(`Feature: ${newFeature.name}`);
    this.logger.log(`Target Tier: ${newFeature.requiredTier}`);
    this.logger.log(`Subscriptions Updated: ${result.updatedSubscriptions}`);
    this.logger.log(`Total Subscriptions: ${result.totalSubscriptions}`);

    if (options.dryRun) {
      this.logger.log('DRY RUN COMPLETED - No actual changes were made');
    }
  }

  private async showUsage() {
    this.logger.log('Feature Migration Script Usage:');
    this.logger.log('');
    this.logger.log('Sync all subscriptions with current feature definitions:');
    this.logger.log('  npm run migrate:features -- --sync-all [--dry-run]');
    this.logger.log('');
    this.logger.log('Add a new feature to all eligible subscriptions:');
    this.logger.log('  npm run migrate:features -- --feature "feature_name" --tier "PRO" [--category "category"] [--description "desc"] [--dry-run]');
    this.logger.log('');
    this.logger.log('Sync a single subscription:');
    this.logger.log('  npm run migrate:features -- --subscription-id "subscription_id"');
    this.logger.log('');
    this.logger.log('Options:');
    this.logger.log('  --dry-run: Preview changes without making them');
    this.logger.log('  --sync-all: Sync all subscriptions with current features');
    this.logger.log('  --feature: Name of the new feature to add');
    this.logger.log('  --tier: Required tier for the feature (FREE, PRO, ADMIN)');
    this.logger.log('  --category: Feature category (optional)');
    this.logger.log('  --description: Feature description (optional)');
    this.logger.log('  --subscription-id: ID of specific subscription to sync');
    this.logger.log('');
    this.logger.log('Examples:');
    this.logger.log('  npm run migrate:features -- --sync-all --dry-run');
    this.logger.log('  npm run migrate:features -- --feature "new_ai_feature" --tier "PRO" --category "ai_analysis"');
    this.logger.log('  npm run migrate:features -- --subscription-id "507f1f77bcf86cd799439011"');
  }
}

// Run the migration script
if (require.main === module) {
  const migration = new FeatureMigrationScript();
  migration.run().catch(console.error);
}

export { FeatureMigrationScript };

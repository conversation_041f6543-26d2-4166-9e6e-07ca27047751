#!/bin/bash

# Simple test to demonstrate the conversation improvements
# Using the session that was working earlier

BASE_URL="http://localhost:4000"
TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************.w84c2lUccYNBsvBw0Tv2caIOdCghSkJo-BdLY61IrXQ"

# Use the working session from earlier
SESSION_ID="685193186b3ee0bcf9ef5502"

echo "=== Testing Final Offer Question ==="
echo "Question: What's your final offer?"

response=$(curl -s -X POST "$BASE_URL/api/chat-negotiation/sessions/$SESSION_ID/moves" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    --data-raw '{"content":"whats your final offer","extractedData":{"strategy":"competitive","sentiment":"neutral"}}')

if echo "$response" | grep -q "statusCode"; then
    echo "❌ Error: $(echo "$response" | jq -r '.message // "Auth error"')"
else
    ai_response=$(echo "$response" | jq -r '.aiResponse.content')
    echo "✅ AI Response: $ai_response"
fi

echo ""
echo "=== Testing Collaborative Question ==="
echo "Question: What can you offer for the data protection clause?"

response2=$(curl -s -X POST "$BASE_URL/api/chat-negotiation/sessions/$SESSION_ID/moves" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json" \
    --data-raw '{"content":"what can you offer for the data protection clause","extractedData":{"strategy":"collaborative","sentiment":"positive"}}')

if echo "$response2" | grep -q "statusCode"; then
    echo "❌ Error: $(echo "$response2" | jq -r '.message // "Auth error"')"
else
    ai_response2=$(echo "$response2" | jq -r '.aiResponse.content')
    echo "✅ AI Response: $ai_response2"
fi

echo ""
echo "=== Testing Document Context ==="

context_response=$(curl -s -X GET "$BASE_URL/api/chat-negotiation/sessions/$SESSION_ID/document-context" \
    -H "Authorization: Bearer $TOKEN")

if echo "$context_response" | grep -q "statusCode"; then
    echo "❌ Error getting context: $(echo "$context_response" | jq -r '.message // "Auth error"')"
else
    has_context=$(echo "$context_response" | jq -r '.hasDocumentContext')
    echo "✅ Document Context Available: $has_context"
    echo "Context Preview: $(echo "$context_response" | jq -r '.context' | head -c 100)..."
fi

echo ""
echo "=== Test Complete ==="

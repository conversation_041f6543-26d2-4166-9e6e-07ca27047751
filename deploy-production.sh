#!/bin/bash
# Production deployment script for Legal Document Analyzer API
# This script implements security best practices for production deployment

set -e

echo "Starting production deployment process..."

# 1. Validate environment
if [ ! -f .env.production ]; then
  echo "Error: .env.production file not found!"
  echo "Please create a production environment file with secure settings."
  exit 1
fi

# 2. Install dependencies
echo "Installing dependencies..."
npm ci --production

# 3. Build the application
echo "Building application for production..."
npm run build

# 4. Run security checks
echo "Running security audit..."
npm audit --production

# 5. Create uploads directory with proper permissions
echo "Setting up secure upload directory..."
mkdir -p uploads
chmod 750 uploads

# 6. Set NODE_ENV to production
export NODE_ENV=production

# 7. Start application with PM2
echo "Starting application with PM2..."
if command -v pm2 &> /dev/null; then
  # Check if app is already running
  if pm2 list | grep -q "legal-document-analyzer"; then
    echo "Restarting existing PM2 process..."
    pm2 reload legal-document-analyzer
  else
    echo "Creating new PM2 process..."
    pm2 start dist/main.js --name legal-document-analyzer --env production
  fi
else
  echo "PM2 not found. Installing..."
  npm install -g pm2
  pm2 start dist/main.js --name legal-document-analyzer --env production
fi

# 8. Save PM2 configuration
pm2 save

echo "Production deployment completed successfully!"
echo "Security enhancements implemented:"
echo "✓ JWT security improvements"
echo "✓ Tenant isolation hardening"
echo "✓ Rate limiting configuration"
echo "✓ Document access controls"

echo "Additional security steps to consider:"
echo "- Set up a reverse proxy (Nginx/Apache) with SSL"
echo "- Configure firewall rules"
echo "- Set up database backups"
echo "- Implement monitoring and alerting"
